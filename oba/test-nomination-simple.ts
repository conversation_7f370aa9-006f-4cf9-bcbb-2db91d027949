#!/usr/bin/env bun

/**
 * Simple Badge Nomination System Test
 */

import { db } from "./db/index";
import { 
  BadgeNominationSchema, 
  BadgeTypeSchema, 
  UserSchema,
} from "./db/schema";
import { BadgeNominationService } from "./services/badge-nomination.service";
import { BadgeService } from "./services/badge.service";
import { WebSocketManager } from "./manager/websocket.manager";

async function testNominationSystem() {
  console.log("🚀 Testing Badge Nomination System");

  try {
    // Clean up
    await db.delete(BadgeNominationSchema);
    await db.delete(BadgeTypeSchema);
    await db.delete(UserSchema);

    // Create test users
    const users = await db.insert(UserSchema).values([
      {
        id: "01234567-89ab-cdef-0123-456789abcde1",
        username: "alice",
        email: "<EMAIL>",
        password: "hashedpassword",
      },
      {
        id: "01234567-89ab-cdef-0123-456789abcde2",
        username: "bob",
        email: "<EMAIL>",
        password: "hashedpassword",
      },
    ]).returning();

    console.log("✓ Created test users");

    // Create test badge type
    const [badgeType] = await db.insert(BadgeTypeSchema).values({
      id: "01234567-89ab-cdef-0123-456789abcdf1",
      badgeId: "helper",
      name: "Helper",
      description: "Community helper badge",
      icon: "🤝",
      design: { shape: "circle", background: "blue", colors: ["#0066cc"] },
      criteria: { requirement: "Peer nominations", tracked: "nominations", threshold: 3 },
      unlockType: "peer_voted",
      category: "community",
      displayOrder: 1,
      isActive: true,
    }).returning();

    console.log("✓ Created test badge type");

    // Initialize services
    const badgeService = new BadgeService();
    const wsManager = WebSocketManager.getInstance();
    const nominationService = new BadgeNominationService(badgeService, wsManager);

    // Test nomination submission
    const nomination = await nominationService.submitNomination(
      users[0].id, // Alice nominates Bob
      {
        badgeTypeId: badgeType.id,
        nomineeUserId: users[1].id,
        nominationReason: "Great helper!",
      }
    );

    console.log("✓ Successfully submitted nomination");
    console.log(`  Nomination ID: ${nomination.id}`);
    console.log(`  Status: ${nomination.status}`);

    // Test getting nominations
    const nominations = await nominationService.getNominationsForUser(users[1].id);
    console.log(`✓ Found ${nominations.length} nominations for user`);

    // Test nomination stats
    const stats = await nominationService.getNominationStats(badgeType.id);
    console.log("✓ Nomination statistics:");
    console.log(`  Total: ${stats.totalNominations}`);
    console.log(`  Pending: ${stats.pendingNominations}`);

    console.log("\n✅ Basic nomination system test completed successfully!");

  } catch (error) {
    console.error("❌ Test failed:", error);
    throw error;
  } finally {
    // Cleanup
    await db.delete(BadgeNominationSchema);
    await db.delete(BadgeTypeSchema);
    await db.delete(UserSchema);
    console.log("🧹 Cleaned up test data");
  }
}

if (import.meta.main) {
  testNominationSystem().catch(console.error);
}