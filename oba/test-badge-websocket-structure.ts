#!/usr/bin/env bun

/**
 * Test script for badge WebSocket event structure
 * This script tests the badge WebSocket event types and utilities without requiring database connection
 */

import { EventTypes } from "./constants/eventTypes";
import { WebSocketUtils } from "./utils/websocket-utils";
import type {
  BadgeAssignedEvent,
  BadgeRemovedEvent,
  BadgeProgressUpdateEvent,
  BadgeAssignedPayload,
  BadgeRemovedPayload,
  BadgeProgressUpdatePayload
} from "./types/badge-websocket.types";
import type { BadgeType, UserBadge } from "./types/badge.types";

function testBadgeWebSocketStructure() {
  console.log("🧪 Testing Badge WebSocket Event Structure\n");

  // Mock badge type
  const mockBadgeType: BadgeType = {
    id: "badge-type-1",
    name: "Test Achievement",
    description: "A test achievement badge",
    iconUrl: "https://example.com/badge.png",
    color: "#FF6B6B",
    category: "achievement",
    isActive: true,
    assignmentType: "manual",
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // Mock user badge
  const mockUserBadge: UserBadge = {
    id: "user-badge-1",
    userId: "user-123",
    badgeTypeId: "badge-type-1",
    badgeType: mockBadgeType,
    assignedBy: "admin-user",
    assignedAt: new Date(),
    isVisible: true
  };

  console.log("1️⃣ Testing Badge Event Types");
  console.log("============================");

  // Test BADGE_ASSIGNED event structure
  const badgeAssignedEvent: BadgeAssignedEvent = {
    type: "BADGE_ASSIGNED",
    userId: "user-123",
    badge: mockUserBadge,
    isAutomatic: false,
    assignedBy: "admin-user",
    timestamp: new Date()
  };

  console.log("✅ BADGE_ASSIGNED event structure:", {
    type: badgeAssignedEvent.type,
    userId: badgeAssignedEvent.userId,
    badgeId: badgeAssignedEvent.badge.id,
    isAutomatic: badgeAssignedEvent.isAutomatic,
    assignedBy: badgeAssignedEvent.assignedBy
  });

  // Test BADGE_REMOVED event structure
  const badgeRemovedEvent: BadgeRemovedEvent = {
    type: "BADGE_REMOVED",
    userId: "user-123",
    badgeTypeId: "badge-type-1",
    badgeType: mockBadgeType,
    removedBy: "admin-user",
    timestamp: new Date()
  };

  console.log("✅ BADGE_REMOVED event structure:", {
    type: badgeRemovedEvent.type,
    userId: badgeRemovedEvent.userId,
    badgeTypeId: badgeRemovedEvent.badgeTypeId,
    removedBy: badgeRemovedEvent.removedBy
  });

  // Test BADGE_PROGRESS_UPDATE event structure
  const badgeProgressEvent: BadgeProgressUpdateEvent = {
    type: "BADGE_PROGRESS_UPDATE",
    userId: "user-123",
    badgeTypeId: "badge-type-1",
    badgeType: mockBadgeType,
    progress: 7,
    total: 10,
    progressPercentage: 70,
    timestamp: new Date()
  };

  console.log("✅ BADGE_PROGRESS_UPDATE event structure:", {
    type: badgeProgressEvent.type,
    userId: badgeProgressEvent.userId,
    badgeTypeId: badgeProgressEvent.badgeTypeId,
    progress: badgeProgressEvent.progress,
    total: badgeProgressEvent.total,
    progressPercentage: badgeProgressEvent.progressPercentage
  });

  console.log("\n2️⃣ Testing WebSocket Message Creation");
  console.log("=====================================");

  // Test creating standardized WebSocket messages
  const assignedMessage = WebSocketUtils.event(EventTypes.BADGE_ASSIGNED, badgeAssignedEvent);
  console.log("✅ BADGE_ASSIGNED WebSocket message:", {
    type: assignedMessage.type,
    event: assignedMessage.event,
    hasData: !!assignedMessage.data,
    hasTimestamp: !!assignedMessage.timestamp
  });

  const removedMessage = WebSocketUtils.event(EventTypes.BADGE_REMOVED, badgeRemovedEvent);
  console.log("✅ BADGE_REMOVED WebSocket message:", {
    type: removedMessage.type,
    event: removedMessage.event,
    hasData: !!removedMessage.data,
    hasTimestamp: !!removedMessage.timestamp
  });

  const progressMessage = WebSocketUtils.event(EventTypes.BADGE_PROGRESS_UPDATE, badgeProgressEvent);
  console.log("✅ BADGE_PROGRESS_UPDATE WebSocket message:", {
    type: progressMessage.type,
    event: progressMessage.event,
    hasData: !!progressMessage.data,
    hasTimestamp: !!progressMessage.timestamp
  });

  console.log("\n3️⃣ Testing Event Constants");
  console.log("===========================");

  // Verify badge event constants are properly defined
  console.log("✅ Badge event constants:");
  console.log("- BADGE_ASSIGNED:", EventTypes.BADGE_ASSIGNED);
  console.log("- BADGE_REMOVED:", EventTypes.BADGE_REMOVED);
  console.log("- BADGE_PROGRESS_UPDATE:", EventTypes.BADGE_PROGRESS_UPDATE);

  console.log("\n4️⃣ Testing Payload Types");
  console.log("=========================");

  // Test payload type structures
  const assignedPayload: BadgeAssignedPayload = {
    userId: "user-123",
    badge: mockUserBadge,
    isAutomatic: false,
    assignedBy: "admin-user"
  };

  const removedPayload: BadgeRemovedPayload = {
    userId: "user-123",
    badgeTypeId: "badge-type-1",
    badgeType: mockBadgeType,
    removedBy: "admin-user"
  };

  const progressPayload: BadgeProgressUpdatePayload = {
    userId: "user-123",
    badgeTypeId: "badge-type-1",
    badgeType: mockBadgeType,
    progress: 7,
    total: 10,
    progressPercentage: 70
  };

  console.log("✅ BadgeAssignedPayload structure valid");
  console.log("✅ BadgeRemovedPayload structure valid");
  console.log("✅ BadgeProgressUpdatePayload structure valid");

  console.log("\n5️⃣ Testing Message Serialization");
  console.log("=================================");

  // Test message serialization
  const serializedAssigned = WebSocketUtils.serialize(assignedMessage);
  const serializedRemoved = WebSocketUtils.serialize(removedMessage);
  const serializedProgress = WebSocketUtils.serialize(progressMessage);

  console.log("✅ BADGE_ASSIGNED message serialized:", typeof serializedAssigned === 'string');
  console.log("✅ BADGE_REMOVED message serialized:", typeof serializedRemoved === 'string');
  console.log("✅ BADGE_PROGRESS_UPDATE message serialized:", typeof serializedProgress === 'string');

  // Test deserialization
  try {
    const parsedAssigned = JSON.parse(serializedAssigned);
    const parsedRemoved = JSON.parse(serializedRemoved);
    const parsedProgress = JSON.parse(serializedProgress);

    console.log("✅ All messages can be parsed back from JSON");
    console.log("✅ Parsed BADGE_ASSIGNED event type:", parsedAssigned.event);
    console.log("✅ Parsed BADGE_REMOVED event type:", parsedRemoved.event);
    console.log("✅ Parsed BADGE_PROGRESS_UPDATE event type:", parsedProgress.event);
  } catch (error) {
    console.error("❌ Message parsing failed:", error);
    throw error;
  }

  console.log("\n6️⃣ Testing Event Validation");
  console.log("============================");

  // Test WebSocket message validation
  const validationResults = [
    WebSocketUtils.validate(assignedMessage),
    WebSocketUtils.validate(removedMessage),
    WebSocketUtils.validate(progressMessage)
  ];

  console.log("✅ BADGE_ASSIGNED message validation:", validationResults[0]);
  console.log("✅ BADGE_REMOVED message validation:", validationResults[1]);
  console.log("✅ BADGE_PROGRESS_UPDATE message validation:", validationResults[2]);

  if (validationResults.every(result => result)) {
    console.log("✅ All badge WebSocket messages pass validation");
  } else {
    console.error("❌ Some badge WebSocket messages failed validation");
    throw new Error("Message validation failed");
  }

  console.log("\n🎉 All badge WebSocket structure tests passed!");
  console.log("\nSummary:");
  console.log("- ✅ Badge event type definitions");
  console.log("- ✅ WebSocket message creation");
  console.log("- ✅ Event constants");
  console.log("- ✅ Payload type structures");
  console.log("- ✅ Message serialization/deserialization");
  console.log("- ✅ Message validation");

  return true;
}

// Run the test
if (import.meta.main) {
  try {
    testBadgeWebSocketStructure();
    console.log("\n✅ Badge WebSocket structure tests completed successfully!");
    process.exit(0);
  } catch (error) {
    console.error("\n❌ Badge WebSocket structure tests failed:", error);
    process.exit(1);
  }
}

export { testBadgeWebSocketStructure };