/**
 * Voice Chat Debugging Script
 *
 * This script patches the necessary files to enable enhanced debugging for voice chat.
 * Run this script with: bun run debug-voice.ts
 */

import fs from "fs";
import path from "path";
import { execSync } from "child_process";

console.log("Voice Chat Debugging Script");
console.log("==========================");

// Paths to the files we need to modify
const indexPath = path.join(process.cwd(), "index.ts");
const sfuImportPath = 'import { SFUService } from "./services/sfu.service";';
const sfuDebugImportPath =
  'import { SFUServiceDebug } from "./services/sfu.service.debug";';
const voiceWsImportPath =
  'import { VoiceWebSocketManager } from "./manager/websocket";';
const voiceWsDebugImportPath =
  'import { VoiceWebSocketManager } from "./manager/websocket.debug";';

// Create backup of original files
console.log("Creating backups of original files...");
if (!fs.existsSync("backups")) {
  fs.mkdirSync("backups");
}

if (!fs.existsSync("backups/index.ts")) {
  fs.copyFileSync(indexPath, "backups/index.ts");
}

if (!fs.existsSync("backups/websocket.ts")) {
  fs.copyFileSync("manager/websocket.ts", "backups/websocket.ts");
}

// Read the index.ts file
console.log("Reading index.ts...");
let indexContent = fs.readFileSync(indexPath, "utf8");

// Replace the SFU import
console.log("Replacing SFU import...");
indexContent = indexContent.replace(sfuImportPath, sfuDebugImportPath);

// Replace the VoiceWebSocketManager import
console.log("Replacing VoiceWebSocketManager import...");
indexContent = indexContent.replace(voiceWsImportPath, voiceWsDebugImportPath);

// Replace SFUService with SFUServiceDebug
console.log("Replacing SFUService with SFUServiceDebug...");
indexContent = indexContent.replace(/new SFUService/g, "new SFUServiceDebug");
indexContent = indexContent.replace(/: SFUService/g, ": SFUServiceDebug");

// Add enhanced binary data handling
console.log("Adding enhanced binary data handling...");
const binaryHandlingRegex =
  /else if \(rawMessage instanceof Blob \|\| rawMessage instanceof ArrayBuffer\) \{[\s\S]*?sfuService\.handleVoiceData\(ws, arrayBuffer\);/;
const enhancedBinaryHandling = `else if (rawMessage instanceof Blob || rawMessage instanceof ArrayBuffer) {
        console.log(\`Received binary data from user \${ws.data.userId}\`);

        if (!ws.data.serverId || !ws.data.channelId) {
          console.error(\`Missing serverId or channelId for user \${ws.data.userId}\`);
          return;
        }

        try {
          // Convert Blob to ArrayBuffer if needed
          const arrayBuffer = rawMessage instanceof Blob ? await rawMessage.arrayBuffer() : rawMessage;
          console.log(\`Binary data size: \${arrayBuffer.byteLength} bytes\`);

          // Set the WebSocket type to 'voice' if not already set
          if (!ws.data.type) {
            ws.data.type = 'voice';
            console.log(\`Set WebSocket type to 'voice' for user \${ws.data.userId}\`);
          }

          // Handle voice data through SFU service
          sfuService.handleVoiceData(ws, arrayBuffer);
        } catch (error) {
          console.error(\`Error processing binary data from user \${ws.data.userId}:\`, error);
        }`;

indexContent = indexContent.replace(
  binaryHandlingRegex,
  enhancedBinaryHandling,
);

// Write the modified content back to index.ts
console.log("Writing modified index.ts...");
fs.writeFileSync(indexPath, indexContent);

// Create a script to restore original files
console.log("Creating restore script...");
const restoreScript = `#!/usr/bin/env bun

import fs from 'fs';

console.log('Restoring original files...');

if (fs.existsSync('backups/index.ts')) {
  fs.copyFileSync('backups/index.ts', 'index.ts');
  console.log('Restored index.ts');
}

if (fs.existsSync('backups/websocket.ts')) {
  fs.copyFileSync('backups/websocket.ts', 'manager/websocket.ts');
  console.log('Restored websocket.ts');
}

console.log('Done!');
`;

fs.writeFileSync("restore-voice-debug.ts", restoreScript);
execSync("chmod +x restore-voice-debug.ts");

console.log("Done!");
console.log("");
console.log("Voice chat debugging is now enabled!");
console.log("Restart your server to apply the changes.");
console.log("");
console.log("To restore the original files, run:");
console.log("  bun run restore-voice-debug.ts");
console.log("");
console.log(
  "Check the console logs for detailed voice chat debugging information.",
);
console.log(
  'Look for logs starting with "SFU:" for voice data handling details.',
);
