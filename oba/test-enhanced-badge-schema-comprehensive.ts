import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { eq, and } from "drizzle-orm";
import { 
  BadgeCollectionSchema, 
  BadgeTypeSchema, 
  UserBadgeSchema, 
  UserCollectionProgressSchema,
  BadgeNominationSchema,
  UserSchema
} from "./db/schema";

// Database connection using environment variables
const DATABASE_URL = `${process.env.POSTGRES_SCHEMA}://${process.env.POSTGRES_USERNAME}:${process.env.POSTGRES_PASSWORD}@${process.env.POSTGRES_HOST}:${process.env.POSTGRES_PORT}/${process.env.POSTGRES_DB}`;
const sql = postgres(DATABASE_URL);
const db = drizzle(sql);

async function testEnhancedBadgeSchemaComprehensive() {
  try {
    console.log("🧪 Running comprehensive enhanced badge schema tests...\n");

    // Test 1: Create a badge collection
    console.log("1. Testing badge collection creation...");
    const testCollection = await db.insert(BadgeCollectionSchema).values({
      collectionId: "test-pioneer-journey",
      name: "Pioneer Journey",
      description: "A progressive journey for early adopters",
      type: "progressive",
      totalBadges: 3,
      unlockedBy: "activity_and_time",
      completionReward: JSON.stringify({
        badge: "pioneer-master",
        title: "Pioneer Master",
        perks: ["special_role", "early_access"],
        visual: "golden_crown",
        animation: "sparkle"
      }),
      isActive: true,
    }).returning();
    console.log("✅ Badge collection created:", testCollection[0].name);

    // Test 2: Create badge types in the collection
    console.log("\n2. Testing badge type creation with collection...");
    const badgeTypes = await db.insert(BadgeTypeSchema).values([
      {
        collectionId: testCollection[0].id,
        badgeId: "first-steps",
        name: "First Steps",
        title: "Early Explorer",
        description: "Took the first steps in the platform",
        icon: "👶",
        tooltip: "Awarded for joining and completing initial setup",
        design: JSON.stringify({
          shape: "circle",
          background: "gradient",
          colors: ["#4F46E5", "#7C3AED"],
          gradient: "linear-gradient(45deg, #4F46E5, #7C3AED)"
        }),
        criteria: JSON.stringify({
          requirement: "Complete account setup",
          tracked: "account_setup_completed",
          type: "custom",
          threshold: 1
        }),
        perks: JSON.stringify(["welcome_badge"]),
        unlockType: "automatic",
        visualDescription: "A purple gradient circle with baby emoji",
        animation: "fade-in",
        displayOrder: 1,
        category: "milestone",
      },
      {
        collectionId: testCollection[0].id,
        badgeId: "active-participant",
        name: "Active Participant",
        title: "Community Contributor",
        description: "Actively participating in community discussions",
        icon: "💬",
        tooltip: "Awarded for sending 50+ messages",
        design: JSON.stringify({
          shape: "hexagon",
          background: "solid",
          colors: ["#10B981"],
          pattern: "dots"
        }),
        criteria: JSON.stringify({
          requirement: "Send 50 messages",
          tracked: "message_count",
          type: "message_count",
          threshold: 50
        }),
        perks: JSON.stringify(["chat_perks", "emoji_access"]),
        unlockType: "automatic",
        visualDescription: "A green hexagon with chat bubble emoji",
        animation: "bounce",
        displayOrder: 2,
        category: "achievement",
      },
      {
        collectionId: testCollection[0].id,
        badgeId: "pioneer-leader",
        name: "Pioneer Leader",
        title: "Trailblazer",
        description: "Leading the way for other pioneers",
        icon: "🚀",
        tooltip: "Awarded for exceptional community leadership",
        design: JSON.stringify({
          shape: "star",
          background: "gradient",
          colors: ["#F59E0B", "#EF4444"],
          gradient: "radial-gradient(circle, #F59E0B, #EF4444)",
          elements: ["sparkles", "glow"]
        }),
        criteria: JSON.stringify({
          requirement: "Demonstrate leadership qualities",
          tracked: "leadership_score",
          type: "complex",
          conditions: {
            message_count: 100,
            server_count: 2,
            friend_count: 10
          }
        }),
        perks: JSON.stringify(["leadership_role", "special_permissions", "priority_support"]),
        unlockType: "peer_voted",
        visualDescription: "A golden star with rocket emoji and sparkle effects",
        animation: "pulse-glow",
        displayOrder: 3,
        category: "special",
      }
    ]).returning();
    console.log("✅ Created", badgeTypes.length, "badge types in collection");

    // Test 3: Test unique constraints
    console.log("\n3. Testing unique constraints...");
    try {
      await db.insert(BadgeTypeSchema).values({
        collectionId: testCollection[0].id,
        badgeId: "first-steps", // Duplicate badge_id in same collection
        name: "Duplicate Test",
        description: "This should fail",
        design: JSON.stringify({ shape: "circle" }),
        criteria: JSON.stringify({ requirement: "test" }),
        displayOrder: 1, // Duplicate display_order in same collection
      });
      console.log("❌ Unique constraint test failed - duplicate was allowed");
    } catch (error) {
      console.log("✅ Unique constraints working - duplicate rejected");
    }

    // Test 4: Create user collection progress
    console.log("\n4. Testing user collection progress...");
    
    // First, get a test user (create one if needed)
    let testUser = await db.select().from(UserSchema).limit(1);
    if (testUser.length === 0) {
      testUser = await db.insert(UserSchema).values({
        username: "test-pioneer-user",
        email: "<EMAIL>",
        password: "hashed_password_here"
      }).returning();
    }

    const collectionProgress = await db.insert(UserCollectionProgressSchema).values({
      userId: testUser[0].id,
      collectionId: testCollection[0].id,
      badgesEarned: 1,
      totalBadges: 3,
      isCompleted: false,
    }).returning();
    console.log("✅ User collection progress created");

    // Test 5: Create user badge with collection reference
    console.log("\n5. Testing user badge assignment with collection...");
    const userBadge = await db.insert(UserBadgeSchema).values({
      userId: testUser[0].id,
      badgeTypeId: badgeTypes[0].id,
      collectionId: testCollection[0].id,
      progressData: JSON.stringify({
        earned_at: new Date().toISOString(),
        criteria_met: ["account_setup_completed"]
      }),
      perksGranted: JSON.stringify(["welcome_badge"]),
      isVisible: true,
    }).returning();
    console.log("✅ User badge assigned with collection reference");

    // Test 6: Create badge nomination
    console.log("\n6. Testing badge nomination system...");
    
    // Create another test user as nominator
    const nominatorUser = await db.insert(UserSchema).values({
      username: "test-nominator-user",
      email: "<EMAIL>",
      password: "hashed_password_here"
    }).returning();

    const nomination = await db.insert(BadgeNominationSchema).values({
      badgeTypeId: badgeTypes[2].id, // Pioneer Leader badge (peer_voted)
      nomineeUserId: testUser[0].id,
      nominatorUserId: nominatorUser[0].id,
      nominationReason: "This user has shown exceptional leadership in our community",
      status: "pending",
    }).returning();
    console.log("✅ Badge nomination created");

    // Test 7: Test complex queries with joins
    console.log("\n7. Testing complex queries with joins...");
    
    // Query user badges with badge type and collection info
    const userBadgesWithDetails = await db
      .select({
        userBadge: UserBadgeSchema,
        badgeType: BadgeTypeSchema,
        collection: BadgeCollectionSchema,
      })
      .from(UserBadgeSchema)
      .leftJoin(BadgeTypeSchema, eq(UserBadgeSchema.badgeTypeId, BadgeTypeSchema.id))
      .leftJoin(BadgeCollectionSchema, eq(UserBadgeSchema.collectionId, BadgeCollectionSchema.id))
      .where(eq(UserBadgeSchema.userId, testUser[0].id));
    
    console.log("✅ Complex join query successful, found", userBadgesWithDetails.length, "badges");

    // Test 8: Test collection progress queries
    console.log("\n8. Testing collection progress queries...");
    const progressWithCollection = await db
      .select({
        progress: UserCollectionProgressSchema,
        collection: BadgeCollectionSchema,
      })
      .from(UserCollectionProgressSchema)
      .leftJoin(BadgeCollectionSchema, eq(UserCollectionProgressSchema.collectionId, BadgeCollectionSchema.id))
      .where(eq(UserCollectionProgressSchema.userId, testUser[0].id));
    
    console.log("✅ Collection progress query successful");

    // Test 9: Test nomination queries
    console.log("\n9. Testing nomination queries...");
    const nominationsWithDetails = await db
      .select({
        nomination: BadgeNominationSchema,
        badgeType: BadgeTypeSchema,
        nominee: {
          id: UserSchema.id,
          username: UserSchema.username,
        },
      })
      .from(BadgeNominationSchema)
      .leftJoin(BadgeTypeSchema, eq(BadgeNominationSchema.badgeTypeId, BadgeTypeSchema.id))
      .leftJoin(UserSchema, eq(BadgeNominationSchema.nomineeUserId, UserSchema.id))
      .where(eq(BadgeNominationSchema.status, "pending"));
    
    console.log("✅ Nomination queries successful, found", nominationsWithDetails.length, "pending nominations");

    // Test 10: Test enum values
    console.log("\n10. Testing enum values...");
    const enumTests = [
      { unlockType: "automatic" as const },
      { unlockType: "manual" as const },
      { unlockType: "peer_voted" as const },
      { unlockType: "manual_invitation" as const },
    ];

    for (const test of enumTests) {
      const result = await db.select().from(BadgeTypeSchema).where(eq(BadgeTypeSchema.unlockType, test.unlockType));
      console.log(`✅ Enum value '${test.unlockType}' query successful`);
    }

    console.log("\n🎉 All comprehensive tests passed successfully!");
    console.log("\nTest Summary:");
    console.log("- ✅ Badge collection creation and management");
    console.log("- ✅ Enhanced badge types with rich design data");
    console.log("- ✅ Collection-aware badge assignments");
    console.log("- ✅ User collection progress tracking");
    console.log("- ✅ Peer nomination system");
    console.log("- ✅ Complex queries with joins");
    console.log("- ✅ Unique constraints and data integrity");
    console.log("- ✅ Enhanced enum types");
    console.log("- ✅ JSONB data storage and retrieval");

    // Cleanup test data
    console.log("\n🧹 Cleaning up test data...");
    await db.delete(BadgeNominationSchema).where(eq(BadgeNominationSchema.id, nomination[0].id));
    await db.delete(UserBadgeSchema).where(eq(UserBadgeSchema.id, userBadge[0].id));
    await db.delete(UserCollectionProgressSchema).where(eq(UserCollectionProgressSchema.id, collectionProgress[0].id));
    await db.delete(BadgeTypeSchema).where(eq(BadgeTypeSchema.collectionId, testCollection[0].id));
    await db.delete(BadgeCollectionSchema).where(eq(BadgeCollectionSchema.id, testCollection[0].id));
    await db.delete(UserSchema).where(eq(UserSchema.id, testUser[0].id));
    await db.delete(UserSchema).where(eq(UserSchema.id, nominatorUser[0].id));
    console.log("✅ Test data cleaned up");

  } catch (error) {
    console.error("❌ Comprehensive test failed:", error);
    process.exit(1);
  } finally {
    await sql.end();
  }
}

testEnhancedBadgeSchemaComprehensive();