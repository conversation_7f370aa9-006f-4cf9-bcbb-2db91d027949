import { z } from "zod";

// WebSocket configuration schema
export const WebSocketConfigSchema = z.object({
  // Message format settings
  messageVersion: z.string().default("1.0.0"),
  enableStandardization: z.boolean().default(true),
  enableLegacySupport: z.boolean().default(true),

  // Connection settings
  heartbeatInterval: z.number().min(1000).default(30000), // 30 seconds
  connectionTimeout: z.number().min(1000).default(60000), // 60 seconds
  maxConnections: z.number().min(1).default(10000),

  // Message settings
  maxMessageSize: z.number().min(1024).default(1048576), // 1MB
  enableCompression: z.boolean().default(true),
  enableBinaryProtocol: z.boolean().default(true),

  // Rate limiting settings
  rateLimits: z
    .object({
      messaging: z.object({
        maxRequests: z.number().default(60),
        windowMs: z.number().default(60000), // 1 minute
      }),
      auth: z.object({
        maxRequests: z.number().default(10),
        windowMs: z.number().default(60000),
      }),
      voice: z.object({
        maxRequests: z.number().default(30),
        windowMs: z.number().default(60000),
      }),
      system: z.object({
        maxRequests: z.number().default(100),
        windowMs: z.number().default(60000),
      }),
      badge_events: z.object({
        maxRequests: z.number().default(50),
        windowMs: z.number().default(60000),
      }),
    })
    .default({
      messaging: { maxRequests: 60, windowMs: 60000 },
      auth: { maxRequests: 10, windowMs: 60000 },
      voice: { maxRequests: 30, windowMs: 60000 },
      system: { maxRequests: 100, windowMs: 60000 },
      badge_events: { maxRequests: 50, windowMs: 60000 },
    }),

  // Correlation tracking settings
  enableCorrelation: z.boolean().default(true),
  correlationTimeout: z.number().min(1000).default(30000), // 30 seconds
  maxCorrelations: z.number().min(100).default(10000),

  // Validation settings
  enableValidation: z.boolean().default(true),
  strictValidation: z.boolean().default(false),
  validateIncomingMessages: z.boolean().default(true),
  validateOutgoingMessages: z.boolean().default(false),

  // Logging and debugging
  logLevel: z.enum(["debug", "info", "warn", "error"]).default("info"),
  enableMessageLogging: z.boolean().default(false),
  enablePerformanceMetrics: z.boolean().default(true),
  enableTracing: z.boolean().default(false),

  // Subscription management
  maxSubscriptionsPerConnection: z.number().min(1).default(100),
  enableSubscriptionCleanup: z.boolean().default(true),
  subscriptionCleanupInterval: z.number().min(1000).default(300000), // 5 minutes

  // Migration settings
  migrationMode: z.enum(["disabled", "gradual", "forced"]).default("gradual"),
  legacyFormatDetection: z.boolean().default(true),
  migrationWarnings: z.boolean().default(true),
});

export type WebSocketConfig = z.infer<typeof WebSocketConfigSchema>;

// Default configuration
export const DEFAULT_WEBSOCKET_CONFIG: WebSocketConfig = {
  messageVersion: "1.0.0",
  enableStandardization: true,
  enableLegacySupport: true,

  heartbeatInterval: 30000,
  connectionTimeout: 60000,
  maxConnections: 10000,

  maxMessageSize: 1048576,
  enableCompression: true,
  enableBinaryProtocol: true,

  rateLimits: {
    messaging: {
      maxRequests: 60,
      windowMs: 60000,
    },
    auth: {
      maxRequests: 10,
      windowMs: 60000,
    },
    voice: {
      maxRequests: 30,
      windowMs: 60000,
    },
    system: {
      maxRequests: 100,
      windowMs: 60000,
    },
    badge_events: {
      maxRequests: 50,
      windowMs: 60000,
    },
  },

  enableCorrelation: true,
  correlationTimeout: 30000,
  maxCorrelations: 10000,

  enableValidation: true,
  strictValidation: false,
  validateIncomingMessages: true,
  validateOutgoingMessages: false,

  logLevel: "info",
  enableMessageLogging: false,
  enablePerformanceMetrics: true,
  enableTracing: false,

  maxSubscriptionsPerConnection: 100,
  enableSubscriptionCleanup: true,
  subscriptionCleanupInterval: 300000,

  migrationMode: "gradual",
  legacyFormatDetection: true,
  migrationWarnings: true,
};

// Environment-based configuration loader
export function loadWebSocketConfig(): WebSocketConfig {
  const config = {
    ...DEFAULT_WEBSOCKET_CONFIG,

    // Override with environment variables
    messageVersion:
      process.env.WS_MESSAGE_VERSION || DEFAULT_WEBSOCKET_CONFIG.messageVersion,
    enableStandardization: process.env.WS_ENABLE_STANDARDIZATION === "true",
    enableLegacySupport: process.env.WS_ENABLE_LEGACY_SUPPORT !== "false",

    heartbeatInterval: parseInt(process.env.WS_HEARTBEAT_INTERVAL || "30000"),
    connectionTimeout: parseInt(process.env.WS_CONNECTION_TIMEOUT || "60000"),
    maxConnections: parseInt(process.env.WS_MAX_CONNECTIONS || "10000"),

    maxMessageSize: parseInt(process.env.WS_MAX_MESSAGE_SIZE || "1048576"),
    enableCompression: process.env.WS_ENABLE_COMPRESSION !== "false",
    enableBinaryProtocol: process.env.WS_ENABLE_BINARY_PROTOCOL !== "false",

    enableCorrelation: process.env.WS_ENABLE_CORRELATION !== "false",
    correlationTimeout: parseInt(process.env.WS_CORRELATION_TIMEOUT || "30000"),
    maxCorrelations: parseInt(process.env.WS_MAX_CORRELATIONS || "10000"),

    enableValidation: process.env.WS_ENABLE_VALIDATION !== "false",
    strictValidation: process.env.WS_STRICT_VALIDATION === "true",
    validateIncomingMessages: process.env.WS_VALIDATE_INCOMING !== "false",
    validateOutgoingMessages: process.env.WS_VALIDATE_OUTGOING === "true",

    logLevel:
      (process.env.WS_LOG_LEVEL as "debug" | "info" | "warn" | "error") ||
      "info",
    enableMessageLogging: process.env.WS_ENABLE_MESSAGE_LOGGING === "true",
    enablePerformanceMetrics:
      process.env.WS_ENABLE_PERFORMANCE_METRICS !== "false",
    enableTracing: process.env.WS_ENABLE_TRACING === "true",

    maxSubscriptionsPerConnection: parseInt(
      process.env.WS_MAX_SUBSCRIPTIONS_PER_CONNECTION || "100",
    ),
    enableSubscriptionCleanup:
      process.env.WS_ENABLE_SUBSCRIPTION_CLEANUP !== "false",
    subscriptionCleanupInterval: parseInt(
      process.env.WS_SUBSCRIPTION_CLEANUP_INTERVAL || "300000",
    ),

    migrationMode:
      (process.env.WS_MIGRATION_MODE as "disabled" | "gradual" | "forced") ||
      "gradual",
    legacyFormatDetection: process.env.WS_LEGACY_FORMAT_DETECTION !== "false",
    migrationWarnings: process.env.WS_MIGRATION_WARNINGS !== "false",
  };

  // Validate configuration
  return WebSocketConfigSchema.parse(config);
}

// Configuration validation helper
export function validateWebSocketConfig(config: Partial<WebSocketConfig>): {
  isValid: boolean;
  errors?: string[];
  validatedConfig?: WebSocketConfig;
} {
  try {
    const validatedConfig = WebSocketConfigSchema.parse({
      ...DEFAULT_WEBSOCKET_CONFIG,
      ...config,
    });

    return {
      isValid: true,
      validatedConfig,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(
          (err) => `${err.path.join(".")}: ${err.message}`,
        ),
      };
    }

    return {
      isValid: false,
      errors: ["Unknown validation error"],
    };
  }
}
