# Redis Cache Migration Plan

## Overview

This document outlines the migration plan to introduce <PERSON><PERSON> caching into the Bun + Svelte real-time chat project while maintaining single-instance compatibility and preparing for multi-instance deployment.

## Step 1: Identify In-Memory Caching Patterns

### Current In-Memory Caching Occurrences

#### 1. WebSocketManager (manager/websocket.manager.ts)

- **State Decision:**
  - `topicSubscriptions`, `rateLimits`, `sessions`, `deviceSessions`, `connectionContext` → **SHARED** (Redis)
  - `clients`, `privateConnections`, `connectionMetadata` → **LOCAL** (in-memory per process)

#### 2. VoiceWebSocketManager (manager/websocket.ts)

- **State Decision:**
  - `topicSubscriptions` → **SHARED** (Redis)

#### 3. SFUService (services/sfu.service.ts)

- **State Decision:**
  - `channelClients` → **SHARED** (Redis)
  - `clientConnections` → **LOCAL** (in-memory per process)

#### Key Functions to Cache

- **User Data:** `getUserById`, `getUserByUserId`, `getUserByUsername` → **SHARED** (Redis)
- **Server Details:** `getServerDetails` → **SHARED** (Redis)
- **Members Data:** `getServerMembers`, `getUserServers` → **SHARED** (Redis)
- **Messages:** `retrieveLastNMessages`, `getDirectMessagesBetweenUsers` → **SHARED** (Redis)
- **Permissions:** `hasServerPermission`, `hasChannelPermission` → **SHARED** (Redis, possibly long-term using TTL)

### State Classification Rules

- **LOCAL (in-memory per process):** Temporary states, WebSocket objects, individual connections.
- **SHARED (Redis):** Persistent or frequently accessed data, user profiles, chat logs, permissions, user/server relationships.

## Step 2: Code Changes

### Code Modifications

#### WebSocketManager Changes

1. **Centralize State Logic**
   - Use `cache.get` and `cache.set` in place of in-memory storage for SHARED objects like `topicSubscriptions`.

```typescript
import { cache } from "../src/cache";

// Example for topicSubscriptions
const serverChannelCombo = `${serverId}:${channelId}`;
await cache.set(`topic:${serverChannelCombo}`, ws);
```

#### SFUService Changes

1. **Replace Channel Management**
   - Cache `channelClients`.

```typescript
const channelKey = `${serverId}:${channelId}`;
await cache.set(`sfu:channel:${channelKey}`, userId);
```

#### Cache User Data

1. **Cache Common Queries**

```typescript
async function getUserInfo(db: any, userId: string) {
  const cacheKey = `user:${userId}:info`;
  let user = await cache.get(cacheKey);
  if (!user) {
    user = await db.getUserById(userId);
    await cache.set(cacheKey, user, 600); // Cache for 10 mins
  }
  return user;
}
```

### New Files

#### src/cache.ts

```typescript
import Redis from "ioredis";

const redis = new Redis();

export const cache = {
  async set(key: string, value: any) {
    await redis.set(key, JSON.stringify(value));
  },
  async get(key: string) {
    const data = await redis.get(key);
    return data ? JSON.parse(data) : null;
  },
  async del(key: string) {
    await redis.del(key);
  },
  async keys(pattern: string) {
    return await redis.keys(pattern);
  },
};
```

#### src/events.ts

```typescript
import { EventEmitter } from "events";

export const events = new EventEmitter();
```

### Package.json Dependency Update

```json
"dependencies": {
    "ioredis": "^5.4.2",
    "@types/ioredis": "^4.28.10"
}
```

## Step 3: Test Plan

### Single Instance Testing

- Verify individual cache operations.
- Test cache hit-miss by intentionally flushing Redis.

### Multi-Instance Scenarios

- Deploy multiple instances.
- Test cross-instance data consistency using Redis.

### Integration with Redis

- Confirm Redis integration within CI/CD using Docker.

### Performance Testing

- Track impact on performance with caching.
- Validate improvements in response times and load handling.

## Step 4: Deployment Strategy

### Configuration

- Add Redis connection details in environment configuration.
- Setup production Redis scaling with clustering and replication.

This plan ensures a seamless transition to Redis caching while preparing for scale and performance improvements across multiple instances.
