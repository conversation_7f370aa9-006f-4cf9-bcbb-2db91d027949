# Oba Backend

Oba is the backend service for a real-time communication platform, similar to Discord or Slack. It provides a RESTful API for core functionalities and uses WebSockets for real-time features like messaging and user status updates. The project is built with Bun, TypeScript, and Drizzle ORM.

## Features

- Real-time messaging in channels and direct messages
- Voice chat with WebRTC
- User authentication and authorization
- Server and channel management
- Roles and permissions system

## Getting Started

### Prerequisites

- [Bun](https://bun.sh/) (v1.1.0 or higher)
- [Docker](https://www.docker.com/) and [Docker Compose](https://docs.docker.com/compose/)
- A PostgreSQL database

### Installation

1.  **Clone the repository:**

    ```bash
    git clone https://github.com/your-username/oba-backend.git
    cd oba-backend
    ```

2.  **Install dependencies:**

    ```bash
    bun install
    ```

3.  **Set up environment variables:**

    Copy the example environment file and update it with your local configuration:

    ```bash
    cp .env.example .env.dev
    ```

4.  **Run database migrations:**

    ```bash
    bun run drizzle-kit migrate
    ```

### Running the Application

- **For local development:**

  ```bash
  bun run dev
  ```

- **Using Docker:**

  ```bash
  docker-compose up --build
  ```

## Contributing

We welcome contributions from the community! Please read our [Contributing Guide](CONTRIBUTING.md) to learn how you can get involved.

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.
