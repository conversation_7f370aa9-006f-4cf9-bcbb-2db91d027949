# Badge Evaluation Service API Documentation

The `BadgeEvaluationService` is a specialized service for handling automatic badge assignment and progress tracking in the OBA platform. It provides comprehensive functionality for evaluating users against badge criteria and managing automatic badge assignments based on user activity and achievements.

## Table of Contents

- [Overview](#overview)
- [Core Evaluation Methods](#core-evaluation-methods)
- [Criteria Evaluation](#criteria-evaluation)
- [User Statistics](#user-statistics)
- [Progress Tracking](#progress-tracking)
- [Batch Processing](#batch-processing)
- [Usage Examples](#usage-examples)
- [Error Handling](#error-handling)
- [Performance Considerations](#performance-considerations)

## Overview

The BadgeEvaluationService works in conjunction with the BadgeService to provide automatic badge assignment capabilities. While BadgeService handles manual badge operations and CRUD operations, BadgeEvaluationService focuses specifically on:

- Evaluating users against automatic badge criteria
- Tracking badge progress for users
- Batch processing for large-scale evaluations
- User statistics aggregation for badge evaluation

## Core Evaluation Methods

### `evaluateUser(userId: string): Promise<EvaluationResult>`

Evaluates a single user for all automatic badges.

```typescript
const evaluationService = new BadgeEvaluationService(db);
const result = await evaluationService.evaluateUser("user-123");

console.log(`New badges: ${result.newBadges.length}`);
console.log(`Evaluated badges: ${result.evaluatedBadges.length}`);
console.log(`Errors: ${result.errors.length}`);
```

**Returns:** `EvaluationResult`
- `userId`: The evaluated user ID
- `newBadges`: Array of newly assigned badges
- `evaluatedBadges`: Array of badge type IDs that were evaluated
- `errors`: Array of error messages encountered during evaluation

### `evaluateUsers(userIds: string[]): Promise<EvaluationResult[]>`

Evaluates multiple users in batch for automatic badges.

```typescript
const results = await evaluationService.evaluateUsers([
  "user-1", "user-2", "user-3"
]);

const totalNewBadges = results.reduce((sum, r) => sum + r.newBadges.length, 0);
console.log(`Total new badges assigned: ${totalNewBadges}`);
```

### `evaluateAllUsers(batchSize?: number): Promise<EvaluationSummary>`

Evaluates all users in the system for automatic badges. Use with caution on large user bases.

```typescript
const summary = await evaluationService.evaluateAllUsers(50);
console.log(`Processed ${summary.processedUsers}/${summary.totalUsers} users`);
console.log(`Assigned ${summary.totalNewBadges} new badges`);
```

### `reevaluateBadgeType(badgeTypeId: string): Promise<ReevaluationSummary>`

Re-evaluates a specific badge type for all users. Useful when badge criteria are updated.

```typescript
const summary = await evaluationService.reevaluateBadgeType("badge-123");
console.log(`Evaluated ${summary.evaluatedUsers} users`);
console.log(`Made ${summary.newAssignments} new assignments`);
```

## Criteria Evaluation

### `checkCriteria(userId: string, criteria: BadgeCriteria): Promise<boolean>`

Checks if a user meets the criteria for a specific badge.

```typescript
const criteria = {
  type: "message_count",
  threshold: 100
};

const meetsCriteria = await evaluationService.checkCriteria("user-123", criteria);
console.log(`User meets criteria: ${meetsCriteria}`);
```

### `checkCriteriaForUsers(userIds: string[], criteria: BadgeCriteria): Promise<Map<string, boolean>>`

Evaluates criteria for multiple users at once.

```typescript
const results = await evaluationService.checkCriteriaForUsers(
  ["user-1", "user-2", "user-3"],
  { type: "server_count", threshold: 5 }
);

results.forEach((meetsCriteria, userId) => {
  console.log(`${userId}: ${meetsCriteria}`);
});
```

## User Statistics

### `getUserStats(userId: string): Promise<UserStats>`

Gets comprehensive user statistics for badge evaluation.

```typescript
const stats = await evaluationService.getUserStats("user-123");
console.log(`Messages: ${stats.messageCount}`);
console.log(`Servers: ${stats.serverCount}`);
console.log(`Friends: ${stats.friendCount}`);
console.log(`Days active: ${stats.daysActive}`);
console.log(`Account age: ${stats.accountAge}`);
```

### `getUserStatsForUsers(userIds: string[]): Promise<Map<string, UserStats>>`

Gets user statistics for multiple users efficiently.

```typescript
const statsMap = await evaluationService.getUserStatsForUsers([
  "user-1", "user-2", "user-3"
]);

statsMap.forEach((stats, userId) => {
  console.log(`${userId}: ${stats.messageCount} messages`);
});
```

### `aggregateUserStats(userId: string): Promise<UserStats>`

Aggregates user statistics from raw database queries for better performance.

```typescript
// More efficient for large-scale operations
const stats = await evaluationService.aggregateUserStats("user-123");
```

## Progress Tracking

### `getBadgeProgress(userId: string, badgeTypeId?: string): Promise<BadgeProgress[]>`

Gets badge progress for a user's automatic badges.

```typescript
// Get progress for all automatic badges
const progress = await evaluationService.getBadgeProgress("user-123");

progress.forEach(p => {
  const percentage = (p.progress / p.total) * 100;
  console.log(`${p.badgeType.name}: ${percentage.toFixed(1)}% complete`);
  console.log(`Progress: ${p.progress}/${p.total}`);
  console.log(`Earned: ${p.isEarned}`);
});

// Get progress for specific badge
const specificProgress = await evaluationService.getBadgeProgress(
  "user-123", 
  "badge-456"
);
```

### `getUsersNearCompletion(badgeTypeId: string, thresholdPercentage?: number): Promise<NearCompletionUser[]>`

Gets users who are close to earning a specific badge.

```typescript
// Get users who are 80% or more complete
const nearCompletion = await evaluationService.getUsersNearCompletion(
  "badge-123", 
  0.8
);

nearCompletion.forEach(user => {
  console.log(`${user.username}: ${user.progressPercentage * 100}% complete`);
});
```

## Batch Processing

### `evaluateRecentlyActiveUsers(daysSinceActive?: number): Promise<RecentEvaluationSummary>`

Processes badge evaluation for users who have been active recently.

```typescript
// Evaluate users active in the last 7 days
const summary = await evaluationService.evaluateRecentlyActiveUsers(7);
console.log(`Evaluated ${summary.evaluatedUsers} recently active users`);
console.log(`Assigned ${summary.totalNewBadges} new badges`);
```

### `evaluateUsersForBadgeType(userIds: string[], badgeTypeId: string): Promise<BadgeTypeEvaluationSummary>`

Evaluates users for a specific badge type only.

```typescript
const summary = await evaluationService.evaluateUsersForBadgeType(
  ["user-1", "user-2", "user-3"],
  "message-badge-123"
);

console.log(`Evaluated ${summary.evaluatedUsers} users`);
console.log(`Made ${summary.newAssignments} new assignments`);
```

## Usage Examples

### Basic Badge Evaluation

```typescript
import { BadgeEvaluationService } from "./services/badge-evaluation.service";

const evaluationService = new BadgeEvaluationService(db);

// Evaluate a user after they send a message
async function onMessageSent(userId: string) {
  const result = await evaluationService.evaluateUser(userId);
  
  if (result.newBadges.length > 0) {
    console.log(`🎉 User earned ${result.newBadges.length} new badges!`);
    // Notify user about new badges
  }
}
```

### Scheduled Badge Evaluation

```typescript
// Daily job to evaluate recently active users
async function dailyBadgeEvaluation() {
  const summary = await evaluationService.evaluateRecentlyActiveUsers(1);
  
  console.log(`Daily badge evaluation complete:`);
  console.log(`- Users evaluated: ${summary.evaluatedUsers}`);
  console.log(`- New badges: ${summary.totalNewBadges}`);
  console.log(`- Errors: ${summary.errors.length}`);
}
```

### Badge Progress Dashboard

```typescript
async function getBadgeProgressForUser(userId: string) {
  const progress = await evaluationService.getBadgeProgress(userId);
  
  return progress.map(p => ({
    badgeName: p.badgeType.name,
    description: p.badgeType.description,
    progress: p.progress,
    total: p.total,
    percentage: Math.round((p.progress / p.total) * 100),
    isEarned: p.isEarned,
    category: p.badgeType.category
  }));
}
```

### Custom Criteria Evaluation

```typescript
// Check if users meet custom criteria
const customCriteria = {
  type: "custom" as const,
  threshold: 1,
  conditions: {
    combinedRequirements: {
      messages: 50,
      servers: 3,
      friends: 10
    }
  }
};

const results = await evaluationService.checkCriteriaForUsers(
  userIds,
  customCriteria
);
```

## Error Handling

The service provides comprehensive error handling:

```typescript
try {
  const result = await evaluationService.evaluateUser("user-123");
  
  // Check for evaluation errors
  if (result.errors.length > 0) {
    console.warn("Evaluation completed with errors:", result.errors);
  }
  
} catch (error) {
  console.error("Evaluation failed:", error.message);
  // Handle critical errors
}
```

Common error scenarios:
- User not found
- Invalid badge criteria
- Database connection issues
- Permission errors

## Performance Considerations

### Batch Processing

- Use batch methods when evaluating multiple users
- Process users in smaller batches (20-50 users) for better performance
- Consider using `evaluateRecentlyActiveUsers()` for scheduled jobs

### Database Optimization

- The service uses efficient database queries with proper indexing
- Statistics aggregation is optimized for large datasets
- Batch operations minimize database round trips

### Memory Usage

- Large batch operations are processed in chunks
- User statistics are cached during evaluation sessions
- Progress calculations are done on-demand

### Recommended Usage Patterns

```typescript
// ✅ Good: Batch processing
const results = await evaluationService.evaluateUsers(userIds);

// ❌ Avoid: Individual processing in loops
for (const userId of userIds) {
  await evaluationService.evaluateUser(userId); // Inefficient
}

// ✅ Good: Scheduled evaluation of active users
await evaluationService.evaluateRecentlyActiveUsers(7);

// ❌ Avoid: Evaluating all users frequently
await evaluationService.evaluateAllUsers(); // Use sparingly
```

## Integration with BadgeService

The BadgeEvaluationService works alongside the BadgeService:

```typescript
const badgeService = new BadgeService(db);
const evaluationService = new BadgeEvaluationService(db);

// Create automatic badge
const badgeType = await badgeService.createBadgeType({
  name: "Active User",
  description: "Send 100 messages",
  assignmentType: "automatic",
  criteria: { type: "message_count", threshold: 100 }
}, adminUserId);

// Evaluate users for the new badge
await evaluationService.reevaluateBadgeType(badgeType.id);
```

This service provides a robust foundation for automatic badge systems, enabling engaging user progression and achievement tracking in the OBA platform.