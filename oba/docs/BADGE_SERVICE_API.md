# Badge Service API Documentation

The `BadgeService` class provides comprehensive business logic for managing the user badge system in OBA. It handles badge type management, user badge operations, automatic badge evaluation, and permission checking.

## Table of Contents

- [Overview](#overview)
- [Badge Type Management](#badge-type-management)
- [User Badge Operations](#user-badge-operations)
- [Automatic Badge Evaluation](#automatic-badge-evaluation)
- [Badge Statistics](#badge-statistics)
- [Bulk Operations](#bulk-operations)
- [Error Handling](#error-handling)
- [Permission System](#permission-system)

## Overview

The BadgeService implements the following key features:

- **Badge Type Management**: Create, update, delete, and retrieve badge definitions
- **User Badge Operations**: Assign, remove, and query user badges
- **Automatic Evaluation**: Evaluate users against automatic badge criteria
- **Statistics & Leaderboards**: Generate badge statistics and leaderboards
- **Permission Checking**: Validate user permissions for badge operations
- **Bulk Operations**: Perform batch operations on multiple badges/users

## Badge Type Management

### createBadgeType(badgeData, createdBy)

Creates a new badge type with validation and permission checking.

```typescript
const badgeData: CreateBadgeTypeRequest = {
  name: "First Message",
  description: "Sent your first message",
  category: "milestone",
  assignmentType: "automatic",
  criteria: {
    type: "message_count",
    threshold: 1
  }
};

const badge = await badgeService.createBadgeType(badgeData, userId);
```

**Parameters:**
- `badgeData`: Badge creation data (name, description, category, etc.)
- `createdBy`: ID of the user creating the badge (requires admin permission)

**Returns:** `Promise<BadgeType>`

**Throws:**
- `BadgeValidationError`: Invalid input data or duplicate name
- `InsufficientPermissionsError`: User lacks admin permission

### updateBadgeType(badgeTypeId, updates, updatedBy)

Updates an existing badge type.

```typescript
const updates = {
  description: "Updated description",
  isActive: false
};

const updatedBadge = await badgeService.updateBadgeType(badgeId, updates, userId);
```

**Parameters:**
- `badgeTypeId`: ID of the badge to update
- `updates`: Partial badge data to update
- `updatedBy`: ID of the user updating the badge

**Returns:** `Promise<BadgeType>`

### deleteBadgeType(badgeTypeId, deletedBy)

Deletes a badge type and all associated user badges.

```typescript
await badgeService.deleteBadgeType(badgeId, userId);
```

**Parameters:**
- `badgeTypeId`: ID of the badge to delete
- `deletedBy`: ID of the user deleting the badge

**Returns:** `Promise<void>`

### getBadgeTypes(filters?, limit?, offset?)

Retrieves badge types with optional filtering and pagination.

```typescript
const filters = {
  category: "achievement",
  isActive: true,
  search: "message"
};

const badges = await badgeService.getBadgeTypes(filters, 10, 0);
```

**Parameters:**
- `filters`: Optional filtering criteria
- `limit`: Maximum number of results
- `offset`: Number of results to skip

**Returns:** `Promise<BadgeType[]>`

### getBadgeTypeById(badgeTypeId)

Gets a specific badge type by ID.

```typescript
const badge = await badgeService.getBadgeTypeById(badgeId);
```

**Returns:** `Promise<BadgeType>`

## User Badge Operations

### assignBadge(userId, badgeTypeId, assignedBy, serverId?)

Assigns a badge to a user with validation and permission checking.

```typescript
const userBadge = await badgeService.assignBadge(
  userId, 
  badgeTypeId, 
  adminUserId, 
  serverId
);
```

**Parameters:**
- `userId`: ID of the user receiving the badge
- `badgeTypeId`: ID of the badge to assign
- `assignedBy`: ID of the user assigning the badge
- `serverId`: Optional server ID for permission checking

**Returns:** `Promise<UserBadge>`

**Throws:**
- `BadgeNotFoundError`: Badge doesn't exist or is inactive
- `BadgeAlreadyAssignedError`: User already has this badge
- `InsufficientPermissionsError`: User lacks assignment permission

### removeBadge(userId, badgeTypeId, removedBy, serverId?)

Removes a badge from a user.

```typescript
await badgeService.removeBadge(userId, badgeTypeId, adminUserId, serverId);
```

**Parameters:**
- `userId`: ID of the user losing the badge
- `badgeTypeId`: ID of the badge to remove
- `removedBy`: ID of the user removing the badge
- `serverId`: Optional server ID for permission checking

**Returns:** `Promise<void>`

### getUserBadges(userId, visibleOnly?)

Gets all badges for a user.

```typescript
const badges = await badgeService.getUserBadges(userId, true); // visible only
const allBadges = await badgeService.getUserBadges(userId, false); // all badges
```

**Parameters:**
- `userId`: ID of the user
- `visibleOnly`: Whether to return only visible badges (default: false)

**Returns:** `Promise<UserBadge[]>`

### getAvailableBadgesForUser(userId)

Gets badges that a user doesn't have yet.

```typescript
const availableBadges = await badgeService.getAvailableBadgesForUser(userId);
```

**Returns:** `Promise<BadgeType[]>`

## Automatic Badge Evaluation

### evaluateUserBadges(userId)

Evaluates automatic badges for a single user.

```typescript
const result = await badgeService.evaluateUserBadges(userId);
console.log(`Assigned ${result.newBadges.length} new badges`);
```

**Returns:** `Promise<EvaluationResult>`

```typescript
interface EvaluationResult {
  userId: string;
  newBadges: UserBadge[];
  evaluatedBadges: string[];
  errors: string[];
}
```

### batchEvaluateUsers(userIds)

Evaluates automatic badges for multiple users.

```typescript
const userIds = ["user1", "user2", "user3"];
const results = await badgeService.batchEvaluateUsers(userIds);
```

**Returns:** `Promise<EvaluationResult[]>`

### evaluateAllUsers(requestedBy, batchSize?)

Evaluates all users for automatic badges (admin only).

```typescript
const summary = await badgeService.evaluateAllUsers(adminUserId, 50);
console.log(`Processed ${summary.processedUsers} users`);
```

**Parameters:**
- `requestedBy`: ID of the admin user requesting evaluation
- `batchSize`: Number of users to process per batch (default: 50)

**Returns:** `Promise<{ totalUsers: number; processedUsers: number; totalNewBadges: number; errors: string[]; }>`

### reevaluateBadgeType(badgeTypeId, requestedBy)

Re-evaluates a specific badge type for all users (admin only).

```typescript
const result = await badgeService.reevaluateBadgeType(badgeTypeId, adminUserId);
```

**Returns:** `Promise<{ evaluatedUsers: number; newAssignments: number; errors: string[]; }>`

## Badge Statistics

### getBadgeStats()

Gets comprehensive badge system statistics.

```typescript
const stats = await badgeService.getBadgeStats();
```

**Returns:** `Promise<BadgeStats>`

```typescript
interface BadgeStats {
  totalBadges: number;
  totalAssignments: number;
  categoryBreakdown: Record<BadgeCategory, number>;
  mostPopularBadges: Array<{
    badgeType: BadgeType;
    assignmentCount: number;
  }>;
}
```

### getBadgeLeaderboard(limit?)

Gets badge leaderboard showing users with the most badges.

```typescript
const leaderboard = await badgeService.getBadgeLeaderboard(10);
```

**Returns:** `Promise<BadgeLeaderboard[]>`

### getBadgeProgress(userId, badgeTypeId?)

Gets badge progress for automatic badges.

```typescript
// Get progress for all automatic badges
const allProgress = await badgeService.getBadgeProgress(userId);

// Get progress for specific badge
const specificProgress = await badgeService.getBadgeProgress(userId, badgeTypeId);
```

**Returns:** `Promise<BadgeProgress[]>`

### getUserStats(userId)

Gets user statistics used for badge evaluation.

```typescript
const stats = await badgeService.getUserStats(userId);
```

**Returns:** `Promise<UserStats>`

## Bulk Operations

### bulkAssignBadges(assignments, assignedBy, serverId?)

Assigns multiple badges to multiple users in a single operation.

```typescript
const assignments = [
  { userId: "user1", badgeTypeId: "badge1" },
  { userId: "user2", badgeTypeId: "badge2" }
];

const results = await badgeService.bulkAssignBadges(
  assignments, 
  adminUserId, 
  serverId
);
```

**Parameters:**
- `assignments`: Array of user-badge assignments
- `assignedBy`: ID of the user performing assignments
- `serverId`: Optional server ID for permission checking

**Returns:** `Promise<UserBadge[]>`

## Error Handling

The BadgeService uses custom error classes for different error scenarios:

### BadgeNotFoundError
Thrown when a badge type doesn't exist.

```typescript
try {
  await badgeService.getBadgeTypeById("invalid-id");
} catch (error) {
  if (error instanceof BadgeNotFoundError) {
    console.log("Badge not found:", error.message);
  }
}
```

### BadgeAlreadyAssignedError
Thrown when trying to assign a badge the user already has.

```typescript
try {
  await badgeService.assignBadge(userId, badgeTypeId, adminId);
} catch (error) {
  if (error instanceof BadgeAlreadyAssignedError) {
    console.log("User already has this badge");
  }
}
```

### BadgeValidationError
Thrown for validation failures or general badge operation errors.

```typescript
try {
  await badgeService.createBadgeType(invalidData, userId);
} catch (error) {
  if (error instanceof BadgeValidationError) {
    console.log("Validation failed:", error.message);
    console.log("Details:", error.details);
  }
}
```

### InsufficientPermissionsError
Thrown when a user lacks required permissions.

```typescript
try {
  await badgeService.createBadgeType(badgeData, regularUserId);
} catch (error) {
  if (error instanceof InsufficientPermissionsError) {
    console.log("User lacks admin permission");
  }
}
```

## Permission System

The BadgeService implements a comprehensive permission system:

### Admin Operations
The following operations require admin permissions:
- Creating badge types
- Updating badge types
- Deleting badge types
- Evaluating all users
- Re-evaluating badge types

### Badge Assignment Operations
Badge assignment/removal requires either:
- Admin permissions (for global operations)
- `MANAGE_ROLES` permission in the specified server

### Permission Validation
Permissions are validated using the existing OBA permission system:

```typescript
// Server-specific permission check
const hasPermission = await hasServerPermission(
  db, 
  userId, 
  serverId, 
  MANAGE_ROLES
);

// Admin permission check (placeholder implementation)
await this.validateAdminPermission(userId);
```

## Usage Examples

### Complete Badge Workflow

```typescript
import { BadgeService } from "./services/badge.service";
import { db } from "./db";

const badgeService = new BadgeService(db);

// 1. Create an automatic badge
const messagesBadge = await badgeService.createBadgeType({
  name: "Chatterbox",
  description: "Sent 1000 messages",
  category: "achievement",
  assignmentType: "automatic",
  criteria: {
    type: "message_count",
    threshold: 1000
  }
}, adminUserId);

// 2. Create a manual badge
const specialBadge = await badgeService.createBadgeType({
  name: "Community Helper",
  description: "Recognized for helping community members",
  category: "special",
  assignmentType: "manual"
}, adminUserId);

// 3. Manually assign the special badge
await badgeService.assignBadge(
  userId, 
  specialBadge.id, 
  adminUserId, 
  serverId
);

// 4. Evaluate user for automatic badges
const evaluation = await badgeService.evaluateUserBadges(userId);

// 5. Get user's badges
const userBadges = await badgeService.getUserBadges(userId, true);

// 6. Get badge statistics
const stats = await badgeService.getBadgeStats();

// 7. Get leaderboard
const leaderboard = await badgeService.getBadgeLeaderboard(10);
```

This documentation covers all the major functionality of the BadgeService. The service provides a complete solution for badge management with proper validation, permission checking, and error handling.