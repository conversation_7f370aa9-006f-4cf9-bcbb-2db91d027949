# WebSocketUtils API Documentation

The WebSocketUtils class provides standardized methods for creating, sending, and managing WebSocket messages in the OBA platform. This API ensures consistent message formats, proper error handling, and type safety across all WebSocket communications.

## Table of Contents

- [Installation](#installation)
- [Basic Usage](#basic-usage)
- [Message Creation Methods](#message-creation-methods)
- [Message Sending Methods](#message-sending-methods)
- [Error Response Methods](#error-response-methods)
- [Type Definitions](#type-definitions)
- [Examples](#examples)
- [Best Practices](#best-practices)

## Installation

```typescript
import { WebSocketUtils } from '../utils/websocket-utils';
import type { 
  IWebSocketMessage, 
  IWebSocketSuccessMessage, 
  IWebSocketErrorMessage,
  IWebSocketEventMessage 
} from '../types/websocket-standardization.types';
```

## Basic Usage

```typescript
import { WebSocketUtils } from '../utils/websocket-utils';

// Create a success message
const successMessage = WebSocketUtils.success('USER_CREATED', {
  userId: '123',
  username: 'john_doe'
});

// Send the message
WebSocketUtils.send(websocket, successMessage);
```

## Message Creation Methods

### `WebSocketUtils.success<T>(type: string, data: T, options?: SuccessOptions): IWebSocketSuccessMessage<T>`

Creates a standardized success message.

**Parameters:**
- `type` (string): The message type identifier
- `data` (T): The payload data
- `options` (optional): Additional options

**Options:**
```typescript
interface SuccessOptions {
  correlationId?: string;
  target?: IWebSocketTarget;
}
```

**Returns:** `IWebSocketSuccessMessage<T>`

**Example:**
```typescript
const message = WebSocketUtils.success('MESSAGE_SENT', {
  messageId: '456',
  content: 'Hello World',
  timestamp: new Date().toISOString()
}, {
  correlationId: 'req-123',
  target: { channelId: 'channel-789' }
});
```

### `WebSocketUtils.error(code: string, message: string, options?: ErrorOptions): IWebSocketErrorMessage`

Creates a standardized error message.

**Parameters:**
- `code` (string): Error code identifier
- `message` (string): Human-readable error message
- `options` (optional): Additional error options

**Options:**
```typescript
interface ErrorOptions {
  correlationId?: string;
  details?: unknown;
  field?: string;
}
```

**Returns:** `IWebSocketErrorMessage`

**Example:**
```typescript
const errorMessage = WebSocketUtils.error('VALIDATION_FAILED', 'Invalid message format', {
  correlationId: 'req-123',
  field: 'content',
  details: { expectedType: 'string', receivedType: 'number' }
});
```

### `WebSocketUtils.event<T>(event: string, data: T, options?: EventOptions): IWebSocketEventMessage<T>`

Creates a standardized event message for broadcasts.

**Parameters:**
- `event` (string): Event name identifier
- `data` (T): Event payload data
- `options` (optional): Event options

**Options:**
```typescript
interface EventOptions {
  target?: IWebSocketTarget;
}
```

**Returns:** `IWebSocketEventMessage<T>`

**Example:**
```typescript
const eventMessage = WebSocketUtils.event('USER_JOINED_CHANNEL', {
  userId: '123',
  channelId: '456',
  username: 'john_doe'
}, {
  target: { channelId: '456' }
});
```

## Message Sending Methods

### `WebSocketUtils.send(ws: ServerWebSocket<CustomWebSocketData>, message: IWebSocketMessage): void`

Sends a message to a specific WebSocket connection.

**Parameters:**
- `ws`: The WebSocket connection
- `message`: The standardized message to send

**Example:**
```typescript
const message = WebSocketUtils.success('PONG', { timestamp: Date.now() });
WebSocketUtils.send(websocket, message);
```

### `WebSocketUtils.broadcast(sockets: Set<ServerWebSocket<CustomWebSocketData>>, message: IWebSocketMessage, options?: BroadcastOptions): void`

Broadcasts a message to multiple WebSocket connections.

**Parameters:**
- `sockets`: Set of WebSocket connections
- `message`: The message to broadcast
- `options` (optional): Broadcast options

**Options:**
```typescript
interface BroadcastOptions {
  excludeUserId?: string;
  filter?: (ws: ServerWebSocket<CustomWebSocketData>) => boolean;
}
```

**Example:**
```typescript
const eventMessage = WebSocketUtils.event('SERVER_ANNOUNCEMENT', {
  title: 'Maintenance Notice',
  message: 'Server will be down for maintenance in 10 minutes'
});

WebSocketUtils.broadcast(allConnectedSockets, eventMessage, {
  excludeUserId: 'admin-123'
});
```

### `WebSocketUtils.sendToUser(userId: string, message: IWebSocketMessage, manager: WebSocketManager): boolean`

Sends a message to a specific user by their ID.

**Parameters:**
- `userId`: Target user's ID
- `message`: The message to send
- `manager`: WebSocket manager instance

**Returns:** `boolean` - True if user was found and message sent

**Example:**
```typescript
const directMessage = WebSocketUtils.success('DIRECT_MESSAGE', {
  fromUserId: '123',
  content: 'Hello there!',
  timestamp: new Date().toISOString()
});

const sent = WebSocketUtils.sendToUser('456', directMessage, websocketManager);
if (!sent) {
  console.log('User not connected');
}
```

### `WebSocketUtils.sendToChannel(channelId: string, message: IWebSocketMessage, manager: WebSocketManager, options?: ChannelOptions): void`

Sends a message to all users subscribed to a channel.

**Parameters:**
- `channelId`: Target channel ID
- `message`: The message to send
- `manager`: WebSocket manager instance
- `options` (optional): Channel-specific options

**Options:**
```typescript
interface ChannelOptions {
  excludeUserId?: string;
}
```

**Example:**
```typescript
const channelMessage = WebSocketUtils.event('NEW_MESSAGE', {
  messageId: '789',
  authorId: '123',
  content: 'Hello channel!',
  timestamp: new Date().toISOString()
});

WebSocketUtils.sendToChannel('channel-456', channelMessage, websocketManager, {
  excludeUserId: '123' // Don't send back to the author
});
```

## Error Response Methods

### `WebSocketUtils.authenticationRequired(): IWebSocketErrorMessage`

Creates a standardized authentication required error.

**Example:**
```typescript
const authError = WebSocketUtils.authenticationRequired();
WebSocketUtils.send(websocket, authError);
```

### `WebSocketUtils.permissionDenied(permission?: string): IWebSocketErrorMessage`

Creates a standardized permission denied error.

**Parameters:**
- `permission` (optional): The specific permission that was denied

**Example:**
```typescript
const permError = WebSocketUtils.permissionDenied('SEND_MESSAGES');
WebSocketUtils.send(websocket, permError);
```

### `WebSocketUtils.notFound(resource: string): IWebSocketErrorMessage`

Creates a standardized resource not found error.

**Parameters:**
- `resource`: The type of resource that wasn't found

**Example:**
```typescript
const notFoundError = WebSocketUtils.notFound('channel');
WebSocketUtils.send(websocket, notFoundError);
```

### `WebSocketUtils.validationError(errors: ValidationError[]): IWebSocketErrorMessage`

Creates a standardized validation error with detailed error information.

**Parameters:**
- `errors`: Array of validation errors

**Example:**
```typescript
const validationErrors = [
  { field: 'content', message: 'Content is required' },
  { field: 'channelId', message: 'Invalid channel ID format' }
];

const validationError = WebSocketUtils.validationError(validationErrors);
WebSocketUtils.send(websocket, validationError);
```

### `WebSocketUtils.rateLimited(retryAfter?: number): IWebSocketErrorMessage`

Creates a standardized rate limiting error.

**Parameters:**
- `retryAfter` (optional): Seconds until the client can retry

**Example:**
```typescript
const rateLimitError = WebSocketUtils.rateLimited(60); // Retry after 60 seconds
WebSocketUtils.send(websocket, rateLimitError);
```

### `WebSocketUtils.internalError(message?: string): IWebSocketErrorMessage`

Creates a standardized internal server error.

**Parameters:**
- `message` (optional): Custom error message

**Example:**
```typescript
const internalError = WebSocketUtils.internalError('Database connection failed');
WebSocketUtils.send(websocket, internalError);
```

## Type Definitions

### Core Message Interface

```typescript
interface IWebSocketMessage<T = unknown> {
  type: string;
  data?: T;
  meta: IWebSocketMeta;
  target?: IWebSocketTarget;
}
```

### Message Metadata

```typescript
interface IWebSocketMeta {
  timestamp: number;
  messageId: string;
  correlationId?: string;
  version: string;
  source: 'server' | 'client';
}
```

### Message Target

```typescript
interface IWebSocketTarget {
  userId?: string;
  channelId?: string;
  serverId?: string;
  topic?: string;
}
```

### Success Message

```typescript
interface IWebSocketSuccessMessage<T = unknown> extends IWebSocketMessage<T> {
  success: true;
  data: T;
}
```

### Error Message

```typescript
interface IWebSocketErrorMessage extends IWebSocketMessage {
  success: false;
  error: {
    code: string;
    message: string;
    details?: unknown;
    field?: string;
  };
}
```

### Event Message

```typescript
interface IWebSocketEventMessage<T = unknown> extends IWebSocketMessage<T> {
  event: string;
  data: T;
}
```

### Validation Error

```typescript
interface ValidationError {
  field: string;
  message: string;
  code?: string;
}
```

## Examples

### Authentication Flow

```typescript
// Client sends authentication request
websocket.message = async (ws, message) => {
  const data = JSON.parse(message.toString());
  
  if (data.type === 'AUTH_REQUEST') {
    try {
      const user = await authenticateUser(data.token);
      
      // Send success response
      const successMessage = WebSocketUtils.success('AUTH_SUCCESS', {
        userId: user.id,
        username: user.username,
        permissions: user.permissions
      }, {
        correlationId: data.meta?.correlationId
      });
      
      WebSocketUtils.send(ws, successMessage);
      
    } catch (error) {
      // Send error response
      const errorMessage = WebSocketUtils.error('AUTH_FAILED', 'Invalid credentials', {
        correlationId: data.meta?.correlationId
      });
      
      WebSocketUtils.send(ws, errorMessage);
    }
  }
};
```

### Message Broadcasting

```typescript
// Handle new message and broadcast to channel
async function handleNewMessage(ws: ServerWebSocket<CustomWebSocketData>, data: any) {
  try {
    // Validate user permissions
    if (!hasPermission(ws.data.userId, 'SEND_MESSAGES', data.channelId)) {
      const permError = WebSocketUtils.permissionDenied('SEND_MESSAGES');
      WebSocketUtils.send(ws, permError);
      return;
    }
    
    // Create message in database
    const message = await createMessage({
      authorId: ws.data.userId,
      channelId: data.channelId,
      content: data.content
    });
    
    // Broadcast to channel subscribers
    const eventMessage = WebSocketUtils.event('MESSAGE_CREATED', {
      messageId: message.id,
      authorId: message.authorId,
      authorUsername: message.author.username,
      channelId: message.channelId,
      content: message.content,
      timestamp: message.createdAt
    });
    
    WebSocketUtils.sendToChannel(data.channelId, eventMessage, websocketManager, {
      excludeUserId: ws.data.userId
    });
    
    // Send confirmation to sender
    const confirmMessage = WebSocketUtils.success('MESSAGE_SENT', {
      messageId: message.id,
      timestamp: message.createdAt
    }, {
      correlationId: data.meta?.correlationId
    });
    
    WebSocketUtils.send(ws, confirmMessage);
    
  } catch (error) {
    const errorMessage = WebSocketUtils.internalError('Failed to send message');
    WebSocketUtils.send(ws, errorMessage);
  }
}
```

### Error Handling

```typescript
// Comprehensive error handling example
websocket.message = async (ws, message) => {
  try {
    // Parse and validate message
    const data = JSON.parse(message.toString());
    
    // Check authentication
    if (!ws.data.userId) {
      const authError = WebSocketUtils.authenticationRequired();
      WebSocketUtils.send(ws, authError);
      return;
    }
    
    // Validate message structure
    const validation = WebSocketValidator.validate(data);
    if (!validation.isValid) {
      const validationError = WebSocketUtils.validationError(validation.errors!);
      WebSocketUtils.send(ws, validationError);
      return;
    }
    
    // Check rate limiting
    if (!checkRateLimit(ws.data.userId)) {
      const rateLimitError = WebSocketUtils.rateLimited(60);
      WebSocketUtils.send(ws, rateLimitError);
      return;
    }
    
    // Process message...
    await handleMessage(ws, validation.sanitizedData);
    
  } catch (error) {
    console.error('WebSocket message handling error:', error);
    const internalError = WebSocketUtils.internalError();
    WebSocketUtils.send(ws, internalError);
  }
};
```

## Best Practices

### 1. Always Use Correlation IDs for Request-Response

```typescript
// Good
const message = WebSocketUtils.success('RESPONSE', data, {
  correlationId: originalMessage.meta.correlationId
});

// Bad - No correlation tracking
const message = WebSocketUtils.success('RESPONSE', data);
```

### 2. Use Appropriate Error Methods

```typescript
// Good - Specific error methods
const authError = WebSocketUtils.authenticationRequired();
const permError = WebSocketUtils.permissionDenied('ADMIN');
const notFoundError = WebSocketUtils.notFound('user');

// Bad - Generic error for everything
const error = WebSocketUtils.error('ERROR', 'Something failed');
```

### 3. Include Relevant Target Information

```typescript
// Good - Specify target for better routing
const message = WebSocketUtils.event('USER_STATUS_CHANGED', userData, {
  target: { serverId: 'server-123' }
});

// Acceptable - No target for global events
const message = WebSocketUtils.event('SYSTEM_MAINTENANCE', maintenanceInfo);
```

### 4. Handle Sending Failures

```typescript
// Good - Check if user exists before sending
const sent = WebSocketUtils.sendToUser(userId, message, manager);
if (!sent) {
  // Handle offline user case
  await queueMessageForLater(userId, message);
}

// Bad - Assume user is always online
WebSocketUtils.sendToUser(userId, message, manager);
```

### 5. Use Type Safety

```typescript
// Good - Typed data
interface MessageData {
  content: string;
  channelId: string;
}

const message = WebSocketUtils.success<MessageData>('MESSAGE_SENT', {
  content: 'Hello',
  channelId: 'channel-123'
});

// Bad - Untyped data
const message = WebSocketUtils.success('MESSAGE_SENT', {
  content: 'Hello',
  channelId: 'channel-123'
});
```

### 6. Consistent Error Handling

```typescript
// Good - Consistent error structure
try {
  await processMessage(data);
} catch (error) {
  if (error instanceof ValidationError) {
    const validationError = WebSocketUtils.validationError(error.errors);
    WebSocketUtils.send(ws, validationError);
  } else if (error instanceof PermissionError) {
    const permError = WebSocketUtils.permissionDenied(error.permission);
    WebSocketUtils.send(ws, permError);
  } else {
    const internalError = WebSocketUtils.internalError();
    WebSocketUtils.send(ws, internalError);
  }
}
```

### 7. Efficient Broadcasting

```typescript
// Good - Use built-in broadcast methods
WebSocketUtils.sendToChannel(channelId, message, manager);

// Bad - Manual iteration
channelSockets.forEach(socket => {
  WebSocketUtils.send(socket, message);
});
```

This API documentation provides comprehensive coverage of the WebSocketUtils class and its methods. For additional examples and advanced usage patterns, refer to the examples directory and migration guide.