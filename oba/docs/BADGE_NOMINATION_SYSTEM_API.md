# Badge Nomination System API Documentation

## Overview

The Badge Nomination System allows community members to nominate each other for peer-voted badges. This system supports threshold-based auto-approval, manual admin approval/rejection, and comprehensive analytics.

## Features

- **Peer Nominations**: Users can nominate others for special badges
- **Threshold Management**: Auto-approval when nomination threshold is reached
- **Admin Workflows**: Manual approval/rejection by administrators
- **Real-time Notifications**: WebSocket events for all nomination activities
- **Analytics & Reporting**: Comprehensive nomination statistics
- **Validation**: Prevents self-nominations, duplicates, and invalid nominations

## API Endpoints

### Submit Nomination

Submit a nomination for a peer-voted badge.

```http
POST /api/badges/nominations
```

**Request Body:**
```json
{
  "badgeTypeId": "uuid",
  "nomineeUserId": "uuid",
  "nominationReason": "string (optional, max 500 chars)"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "nomination": {
      "id": "uuid",
      "badgeTypeId": "uuid",
      "nomineeUserId": "uuid",
      "nominatorUserId": "uuid",
      "nominationReason": "string",
      "status": "pending",
      "createdAt": "datetime",
      "badgeType": {
        "id": "uuid",
        "name": "string",
        "title": "string",
        "description": "string",
        "icon": "string",
        "category": "string"
      }
    },
    "message": "Nomination submitted successfully"
  }
}
```

**Validation Rules:**
- Badge type must exist and be active
- Badge type must have `unlockType: "peer_voted"`
- Nominee user must exist
- Cannot nominate yourself
- Cannot submit duplicate nominations
- Nominee cannot already have the badge

### Get Received Nominations

Get nominations received by the current user.

```http
GET /api/badges/nominations/received?status=pending&limit=20&offset=0
```

**Query Parameters:**
- `status` (optional): Filter by status (`pending`, `approved`, `rejected`)
- `limit` (optional): Number of results (1-100, default: 20)
- `offset` (optional): Pagination offset (default: 0)

**Response:**
```json
{
  "success": true,
  "data": {
    "nominations": [
      {
        "id": "uuid",
        "badgeTypeId": "uuid",
        "nomineeUserId": "uuid",
        "nominatorUserId": "uuid",
        "nominationReason": "string",
        "status": "pending",
        "createdAt": "datetime",
        "processedAt": "datetime",
        "badgeType": {
          "id": "uuid",
          "name": "string",
          "title": "string",
          "description": "string",
          "icon": "string",
          "category": "string"
        }
      }
    ],
    "count": 1
  }
}
```

### Get Submitted Nominations

Get nominations submitted by the current user.

```http
GET /api/badges/nominations/submitted?status=pending
```

**Query Parameters:** Same as received nominations

**Response:** Same structure as received nominations

### Get Nomination Statistics

Get statistics for a specific badge type.

```http
GET /api/badges/nominations/stats/:badgeTypeId
```

**Response:**
```json
{
  "success": true,
  "data": {
    "stats": {
      "totalNominations": 15,
      "pendingNominations": 3,
      "approvedNominations": 10,
      "rejectedNominations": 2,
      "uniqueNominees": 8,
      "uniqueNominators": 12
    }
  }
}
```

### Get Nomination Count

Get the current nomination count for a specific user and badge type.

```http
GET /api/badges/nominations/count/:badgeTypeId/:userId
```

**Response:**
```json
{
  "success": true,
  "data": {
    "badgeTypeId": "uuid",
    "userId": "uuid",
    "nominationCount": 2
  }
}
```

### Approve Nomination (Admin Only)

Approve a pending nomination and assign the badge.

```http
POST /api/badges/nominations/:nominationId/approve
```

**Response:**
```json
{
  "success": true,
  "data": {
    "userBadge": {
      "id": "uuid",
      "userId": "uuid",
      "badgeTypeId": "uuid",
      "assignedBy": "uuid",
      "assignedAt": "datetime",
      "isVisible": true
    },
    "message": "Nomination approved and badge assigned"
  }
}
```

### Reject Nomination (Admin Only)

Reject a pending nomination.

```http
POST /api/badges/nominations/:nominationId/reject
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Nomination rejected"
  }
}
```

### Get User Nominations (Admin Only)

Get all nominations for a specific user.

```http
GET /api/badges/nominations/user/:userId?status=pending
```

**Response:** Same structure as received nominations

### Get Nomination Analytics (Admin Only)

Get comprehensive nomination analytics.

```http
GET /api/badges/nominations/analytics
```

**Response:**
```json
{
  "success": true,
  "data": {
    "analytics": {
      "totalNominations": 150,
      "pendingNominations": 25,
      "approvedNominations": 100,
      "rejectedNominations": 25,
      "topNominatedBadges": [
        {
          "badgeType": { "id": "uuid", "name": "Helper" },
          "nominationCount": 45
        }
      ],
      "mostActiveNominators": [
        {
          "userId": "uuid",
          "username": "alice",
          "nominationCount": 12
        }
      ],
      "recentActivity": [
        {
          "type": "nomination_submitted",
          "timestamp": "datetime",
          "details": {}
        }
      ],
      "conversionRate": 0.75
    }
  }
}
```

## WebSocket Events

The nomination system sends real-time WebSocket events for various activities:

### BADGE_NOMINATED

Sent to the nominee when they receive a nomination.

```json
{
  "type": "BADGE_NOMINATED",
  "payload": {
    "nominationId": "uuid",
    "nomination": { /* nomination object */ },
    "nomineeUserId": "uuid",
    "nominatorUserId": "uuid",
    "badgeType": { /* badge type object */ }
  }
}
```

### NOMINATION_APPROVED

Sent to the nominee when their nomination is approved.

```json
{
  "type": "NOMINATION_APPROVED",
  "payload": {
    "nominationId": "uuid",
    "nomination": { /* nomination object */ },
    "badgeAssigned": { /* user badge object */ },
    "approvedBy": "uuid"
  }
}
```

### NOMINATION_REJECTED

Sent to the nominee when their nomination is rejected.

```json
{
  "type": "NOMINATION_REJECTED",
  "payload": {
    "nominationId": "uuid",
    "nomination": { /* nomination object */ },
    "rejectedBy": "uuid",
    "reason": "string (optional)"
  }
}
```

### BADGE_ASSIGNED

Sent when a badge is auto-assigned due to reaching the nomination threshold.

```json
{
  "type": "BADGE_ASSIGNED",
  "payload": {
    "userId": "uuid",
    "badge": { /* user badge object */ },
    "badgeType": { /* badge type object */ },
    "isNew": true,
    "perksGranted": ["perk1", "perk2"]
  }
}
```

## Auto-Approval System

The nomination system supports automatic badge assignment when the nomination threshold is reached:

1. **Threshold Configuration**: Set in the badge type's `criteria.threshold` field
2. **Unique Nominators**: Each nominator can only nominate a user once per badge
3. **Auto-Assignment**: When threshold is reached, the badge is automatically assigned
4. **Status Update**: All pending nominations are marked as "approved"
5. **Notifications**: WebSocket events are sent to notify the recipient

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- `400 Bad Request`: Validation errors, business rule violations
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions (admin-only endpoints)
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server errors

**Error Response Format:**
```json
{
  "success": false,
  "error": "Error message describing what went wrong"
}
```

## Common Error Scenarios

- **Self-Nomination**: "Cannot nominate yourself"
- **Duplicate Nomination**: "Nomination already exists"
- **Badge Not Found**: "Badge type not found"
- **Invalid Badge Type**: "Badge does not support peer voting"
- **Inactive Badge**: "Badge is not active"
- **User Not Found**: "Nominee user not found"
- **Badge Already Assigned**: "User already has this badge"
- **Nomination Not Pending**: "Nomination is not pending"

## Usage Examples

### Submit a Nomination

```javascript
const response = await fetch('/api/badges/nominations', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    badgeTypeId: 'badge-uuid',
    nomineeUserId: 'user-uuid',
    nominationReason: 'This user has been incredibly helpful to new members'
  })
});

const result = await response.json();
if (result.success) {
  console.log('Nomination submitted:', result.data.nomination);
}
```

### Check Nomination Status

```javascript
const response = await fetch('/api/badges/nominations/received', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});

const result = await response.json();
if (result.success) {
  console.log('Received nominations:', result.data.nominations);
}
```

### Admin Approval

```javascript
const response = await fetch(`/api/badges/nominations/${nominationId}/approve`, {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + adminToken
  }
});

const result = await response.json();
if (result.success) {
  console.log('Badge assigned:', result.data.userBadge);
}
```

## Integration with Badge System

The nomination system integrates seamlessly with the existing badge system:

- **Badge Types**: Only badges with `unlockType: "peer_voted"` support nominations
- **Badge Assignment**: Uses the existing `BadgeService.assignBadge()` method
- **Perks**: Automatically grants perks when badges are assigned
- **Collections**: Supports badges that are part of collections
- **WebSocket Events**: Uses the existing badge WebSocket event system

## Security Considerations

- **Authentication**: All endpoints require valid authentication
- **Authorization**: Admin endpoints check for appropriate permissions
- **Rate Limiting**: Prevent spam nominations (implement as needed)
- **Input Validation**: All inputs are validated using Zod schemas
- **SQL Injection**: Protected by using parameterized queries with Drizzle ORM
- **Business Rules**: Enforced at the service layer to prevent invalid operations

## Performance Considerations

- **Database Indexes**: Indexes on nomination queries for optimal performance
- **Batch Operations**: Efficient handling of threshold checks and bulk updates
- **Caching**: Consider caching nomination counts for frequently checked badges
- **Pagination**: All list endpoints support pagination to handle large datasets
- **WebSocket Efficiency**: Events are only sent to relevant users

## Testing

The nomination system includes comprehensive tests:

- **Unit Tests**: Service layer logic and validation
- **Integration Tests**: API endpoints and database operations
- **End-to-End Tests**: Complete nomination workflows
- **Performance Tests**: Load testing for high-volume scenarios

Run tests with:
```bash
bun test tests/unit/badge-nomination.service.test.ts
bun test tests/integration/badge-nominations.test.ts
```