import { drizzle } from "drizzle-orm/postgres-js";
import { and, eq, isNull, or } from "drizzle-orm";
import {
  ChannelSchema,
  ChannelCategorySchema,
  ChannelPrivacySchema,
  CategoryPermissionSchema,
  ServerSchema,
  UserRoles,
  ServerRoleSchema,
  ChannelAllowedRolesSchema,
  ChannelAllowedUsersSchema,
  ChannelVisibleRolesSchema,
  ChannelVisibleUsersSchema,
  CategoryAllowedRolesSchema,
  CategoryAllowedUsersSchema,
  CategoryVisibleRolesSchema,
  CategoryVisibleUsersSchema,
} from "../db/schema";
import { ADMINISTRATOR } from "../constants/permissions";
import {
  hasChannelAccess,
  isChannelVisibleToUser,
  hasCategoryAccess,
  isCategoryVisibleToUser,
} from "./enhanced-permissions";

/**
 * Get all categories and channels for a user in a server, including visibility and access information
 *
 * @param db - The database connection to use
 * @param userId - The ID of the user
 * @param serverId - The ID of the server
 * @returns An object containing categories and channels with visibility and access information
 */
export async function getServerStructureForUser(
  db: ReturnType<typeof drizzle>,
  userId: string,
  serverId: string,
): Promise<
  Array<{
    id: string;
    name: string;
    description: string | null;
    type: "category" | "channel";
    position: number;
    locked: boolean;
    visible: boolean;
    parentId?: string | null;
    channelType?: string;
  }>
> {
  try {
    // Check if the user is the server owner (owners have access to everything)
    const server = await db
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    const isServerOwner = server.length > 0 && server[0].ownerId === userId;

    // Check if the user has the ADMINISTRATOR permission
    let isAdmin = false;
    if (!isServerOwner) {
      // Get user's roles
      const userRoles = await db
        .select({ roleId: UserRoles.roleId })
        .from(UserRoles)
        .where(
          and(eq(UserRoles.userId, userId), eq(UserRoles.serverId, serverId)),
        );

      // Check if any role has ADMINISTRATOR permission
      for (const role of userRoles) {
        const roleData = await db
          .select({ permissions: ServerRoleSchema.permissions })
          .from(ServerRoleSchema)
          .where(eq(ServerRoleSchema.id, role.roleId as string));

        if (
          roleData.length > 0 &&
          (roleData[0].permissions & ADMINISTRATOR) === ADMINISTRATOR
        ) {
          isAdmin = true;
          break;
        }
      }
    }

    // Get all categories in the server
    const categories = await db
      .select()
      .from(ChannelCategorySchema)
      .where(eq(ChannelCategorySchema.serverId, serverId))
      .orderBy(ChannelCategorySchema.position);

    // Get all channels in the server
    const channels = await db
      .select()
      .from(ChannelSchema)
      .where(eq(ChannelSchema.serverId, serverId))
      .orderBy(ChannelSchema.position);

    // Create a unified array for server structure
    const serverStructure: Array<{
      id: string;
      name: string;
      description: string | null;
      type: "category" | "channel";
      position: number;
      locked: boolean;
      visible: boolean;
      parentId?: string | null;
      channelType?: string;
    }> = [];

    // Process categories and their channels
    for (const category of categories) {
      // Check if the user can see the category
      const isCategoryVisible =
        isServerOwner ||
        isAdmin ||
        (await isCategoryVisibleToUser(db, userId, category.id, serverId));

      // If the category is not visible, skip it
      if (!isCategoryVisible) {
        continue;
      }

      // Check if the user has access to the category
      const hasAccess =
        isServerOwner ||
        isAdmin ||
        (await hasCategoryAccess(db, userId, category.id, serverId));

      // Add the category to the server structure
      serverStructure.push({
        id: category.id,
        name: category.name,
        description: category.description,
        type: "category" as const,
        position: category.position || 0, // Default to 0 if null
        locked: !hasAccess, // Mark as locked if the user doesn't have access
        visible: true, // It's visible since we're including it
      });

      // Get channels in this category
      const categoryChannels = channels.filter(
        (channel) => channel.categoryId === category.id,
      );

      // Process channels in this category
      for (const channel of categoryChannels) {
        // Check if the user can see the channel
        const isChannelVisible =
          isServerOwner ||
          isAdmin ||
          (await isChannelVisibleToUser(db, userId, channel.id, serverId));

        // If the channel is not visible, skip it
        if (!isChannelVisible) {
          continue;
        }

        // Check if the user has access to the channel
        const userHasChannelAccess =
          isServerOwner ||
          isAdmin ||
          (await hasChannelAccess(db, userId, channel.id, serverId));

        // Add the channel to the server structure
        serverStructure.push({
          id: channel.id,
          name: channel.name,
          description: channel.description,
          type: "channel" as const,
          channelType: channel.type,
          position: channel.position || 0, // Default to 0 if null
          parentId: category.id, // Reference to the parent category
          locked: !userHasChannelAccess, // Mark as locked if the user doesn't have access
          visible: true, // It's visible since we're including it
        });
      }
    }

    // Process uncategorized channels
    for (const channel of channels.filter(
      (channel) => channel.categoryId === null,
    )) {
      // Check if the user can see the channel
      const isChannelVisible =
        isServerOwner ||
        isAdmin ||
        (await isChannelVisibleToUser(db, userId, channel.id, serverId));

      // If the channel is not visible, skip it
      if (!isChannelVisible) {
        continue;
      }

      // Check if the user has access to the channel
      const userHasChannelAccess =
        isServerOwner ||
        isAdmin ||
        (await hasChannelAccess(db, userId, channel.id, serverId));

      // Add the channel to the server structure
      serverStructure.push({
        id: channel.id,
        name: channel.name,
        description: channel.description,
        type: "channel" as const,
        channelType: channel.type,
        position: channel.position || 0, // Default to 0 if null
        parentId: null, // No parent category
        locked: !userHasChannelAccess, // Mark as locked if the user doesn't have access
        visible: true, // It's visible since we're including it
      });
    }

    // Sort the server structure purely by position
    // This allows for complete flexibility in ordering channels and categories
    serverStructure.sort((a, b) => {
      // Sort everything by position, regardless of type
      return a.position - b.position;
    });

    return serverStructure;
  } catch (error) {
    console.error("Error getting server structure for user:", error);
    return []; // Return empty array on error
  }
}
