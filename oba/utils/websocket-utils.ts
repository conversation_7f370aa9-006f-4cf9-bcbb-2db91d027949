import type { ServerWebSocket } from "bun";
import { nanoid } from "nanoid";
import {
    type IWebSocketMessage,
    type IWebSocketSuccessMessage,
    type IWebSocketErrorMessage,
    type IWebSocketEventMessage,
    type IWebSocketMeta,
    type IWebSocketTarget,
    WebSocketErrorCode,
} from "../types/websocket-standardization.types";
import type { CustomWebSocketData } from "../types/websocket.types";
import type { WebSocketManager } from "../manager/websocket.manager";
import { logger } from "../services/logger.service";

/**
 * WebSocketUtils - Standardized utility class for WebSocket message creation and sending
 *
 * This class provides standardized methods for creating and sending WebSocket messages,
 * similar to how ResponseUtils standardizes HTTP responses. It ensures consistent
 * message formats, proper error handling, and correlation tracking.
 */
export class WebSocketUtils {
    private static readonly MESSAGE_VERSION = "1.0.0";
    private static readonly logger = logger.createLogger("WebSocketUtils");

    /**
     * Create a standardized success message
     * @param type Message type identifier
     * @param data Success response data
     * @param options Additional options for the message
     * @returns Standardized success message
     */
    public static success<T>(
        type: string | number,
        data: T,
        options?: {
            correlationId?: string;
            target?: IWebSocketTarget;
            message?: string;
        },
    ): IWebSocketSuccessMessage<T> {
        const meta: IWebSocketMeta = {
            timestamp: new Date(),
            id: nanoid(),
            correlationId: options?.correlationId,
            version: this.MESSAGE_VERSION,
            source: "server",
        };

        return {
            type,
            success: true,
            data,
            message: options?.message,
            meta,
            target: options?.target,
        };
    }

    /**
     * Create a standardized error message
     * @param code Error code from WebSocketErrorCode enum
     * @param message Human-readable error message
     * @param options Additional error options
     * @returns Standardized error message
     */
    public static error(
        code: WebSocketErrorCode,
        message: string,
        options?: {
            correlationId?: string;
            details?: {
                field?: string;
                expectedFormat?: string;
                receivedValue?: unknown;
                retryAfter?: number;
                maxSize?: number;
                currentSize?: number;
                validationErrors?: Array<{
                    field: string;
                    message: string;
                    code: string;
                }>;
                stack?: string;
                permission?: string;
                resource?: string;
                channelId?: string;
                serverId?: string;
                userId?: string;
            };
            field?: string;
        },
    ): IWebSocketErrorMessage {
        const meta: IWebSocketMeta = {
            timestamp: new Date(),
            id: nanoid(),
            correlationId: options?.correlationId,
            version: this.MESSAGE_VERSION,
            source: "server",
        };

        return {
            type: "ERROR",
            success: false,
            error: {
                code,
                message,
                details: options?.details,
            },
            meta,
        };
    }

    /**
     * Create a standardized event message
     * @param event Event name/type
     * @param data Event payload data
     * @param options Additional event options
     * @returns Standardized event message
     */
    public static event<T>(
        event: string | number,
        data: T,
        options?: {
            target?: IWebSocketTarget;
            category?: string;
            severity?: "info" | "warning" | "error" | "critical";
        },
    ): IWebSocketEventMessage<T> {
        const meta: IWebSocketMeta = {
            timestamp: new Date(),
            id: nanoid(),
            version: this.MESSAGE_VERSION,
            source: "server",
        };

        return {
            type: "EVENT",
            event,
            data,
            category: options?.category,
            severity: options?.severity || "info",
            meta,
            target: options?.target,
        };
    }

    /**
     * Send a message to a specific WebSocket connection
     * @param ws WebSocket connection to send to
     * @param message Message to send
     */
    public static send(
        ws: ServerWebSocket<CustomWebSocketData>,
        message: IWebSocketMessage,
    ): void {
        try {
            if (ws.readyState !== WebSocket.OPEN) {
                this.logger.warn(
                    "Attempted to send message to closed WebSocket",
                    undefined,
                    {
                        messageId: message.meta.id,
                        userId: ws.data.userId,
                    },
                );
                return;
            }

            const serializedMessage = this.serialize(message);
            ws.send(serializedMessage);

            this.logger.debug("Message sent successfully", undefined, {
                messageId: message.meta.id,
                type: message.type,
                userId: ws.data.userId,
            });
        } catch (error) {
            this.logger.error("Failed to send WebSocket message", undefined, {
                error,
                messageId: message.meta.id,
                userId: ws.data.userId,
            });
        }
    }

    /**
     * Broadcast a message to multiple WebSocket connections
     * @param sockets Set of WebSocket connections to broadcast to
     * @param message Message to broadcast
     * @param options Broadcast options
     */
    public static broadcast(
        sockets: Set<ServerWebSocket<CustomWebSocketData>>,
        message: IWebSocketMessage,
        options?: {
            excludeUserId?: string;
            filter?: (ws: ServerWebSocket<CustomWebSocketData>) => boolean;
        },
    ): void {
        try {
            const serializedMessage = this.serialize(message);
            let sentCount = 0;

            sockets.forEach((ws) => {
                // Skip if WebSocket is not open
                if (ws.readyState !== WebSocket.OPEN) {
                    return;
                }

                // Skip if user should be excluded
                if (
                    options?.excludeUserId &&
                    ws.data.userId === options.excludeUserId
                ) {
                    return;
                }

                // Apply custom filter if provided
                if (options?.filter && !options.filter(ws)) {
                    return;
                }

                try {
                    ws.send(serializedMessage);
                    sentCount++;
                } catch (error) {
                    this.logger.warn(
                        "Failed to send message to individual WebSocket",
                        undefined,
                        {
                            error,
                            userId: ws.data.userId,
                            messageId: message.meta.id,
                        },
                    );
                }
            });

            this.logger.debug("Broadcast completed", undefined, {
                messageId: message.meta.id,
                type: message.type,
                totalSockets: sockets.size,
                sentCount,
            });
        } catch (error) {
            this.logger.error("Failed to broadcast WebSocket message", undefined, {
                error,
                messageId: message.meta.id,
            });
        }
    }

    /**
     * Send a message to a specific user
     * @param userId User ID to send to
     * @param message Message to send
     * @param manager WebSocket manager instance
     * @returns Whether the message was sent successfully
     */
    public static sendToUser(
        userId: string,
        message: IWebSocketMessage,
        manager: WebSocketManager,
    ): boolean {
        try {
            if (!userId) {
                this.logger.error("Cannot send to user: userId is undefined");
                return false;
            }

            const serializedMessage = this.serialize(message);
            manager.broadcastToUser(userId, serializedMessage);

            this.logger.debug("Message sent to user", undefined, {
                messageId: message.meta.id,
                type: message.type,
                userId,
            });

            return true;
        } catch (error) {
            this.logger.error("Failed to send message to user", undefined, {
                error,
                messageId: message.meta.id,
                userId,
            });
            return false;
        }
    }

    /**
     * Send a message to all subscribers of a channel
     * @param channelId Channel ID to send to
     * @param message Message to send
     * @param manager WebSocket manager instance
     * @param options Channel broadcast options
     */
    public static sendToChannel(
        channelId: string,
        message: IWebSocketMessage,
        manager: WebSocketManager,
        options?: {
            serverId?: string;
            excludeUserId?: string;
        },
    ): void {
        try {
            if (!channelId) {
                this.logger.error("Cannot send to channel: channelId is undefined");
                return;
            }

            const serializedMessage = this.serialize(message);

            // Use the existing broadcast method from WebSocketManager
            manager.broadcast(
                serializedMessage,
                options?.serverId,
                channelId,
                options?.excludeUserId,
            );

            this.logger.debug("Message sent to channel", undefined, {
                messageId: message.meta.id,
                type: message.type,
                channelId,
                serverId: options?.serverId,
            });
        } catch (error) {
            this.logger.error("Failed to send message to channel", undefined, {
                error,
                messageId: message.meta.id,
                channelId,
            });
        }
    }

    /**
     * Serialize a message to JSON string with validation
     * @param message Message to serialize
     * @returns Serialized message string
     */
    public static serialize(message: IWebSocketMessage): string {
        try {
            // Basic validation
            if (!message.meta || !message.meta.id) {
                throw new Error("Message missing required metadata");
            }

            return JSON.stringify(message);
        } catch (error) {
            this.logger.error("Failed to serialize WebSocket message", undefined, {
                error,
                messageType: message.type,
            });
            throw error;
        }
    }

    /**
     * Validate a message structure
     * @param message Message to validate
     * @returns Whether the message is valid
     */
    public static validate(message: unknown): boolean {
        try {
            if (!message || typeof message !== "object") {
                return false;
            }

            const msg = message as Record<string, unknown>;

            // Check required fields
            if (!msg.type || !msg.meta) {
                return false;
            }

            // Check meta fields
            const meta = msg.meta as any;
            if (!meta?.id || !meta?.timestamp || !meta?.version) {
                return false;
            }

            return true;
        } catch (error) {
            this.logger.error("Error validating message", undefined, error);
            return false;
        }
    }

    // Common error response factory methods

    /**
     * Create authentication request message (not an error)
     * @param correlationId Optional correlation ID
     * @returns Authentication request message
     */
    public static authenticationRequired(
        correlationId?: string,
    ): IWebSocketMessage {
        const meta: IWebSocketMeta = {
            timestamp: new Date(),
            id: nanoid(),
            correlationId,
            version: this.MESSAGE_VERSION,
            source: "server",
        };

        return {
            type: "AUTH_REQUEST",
            data: {
                message: "Please authenticate to continue",
                supportedMethods: ["token", "jwt"],
                timeout: 30000, // 30 seconds to authenticate
            },
            meta,
        };
    }

    /**
     * Create permission denied error
     * @param permission Optional permission name
     * @param correlationId Optional correlation ID
     * @returns Permission denied error message
     */
    public static permissionDenied(
        permission?: string,
        correlationId?: string,
    ): IWebSocketErrorMessage {
        const message = permission
            ? `Permission denied: ${permission} required`
            : "Permission denied";

        return this.error(WebSocketErrorCode.PERMISSION_DENIED, message, {
            correlationId,
            details: { permission },
        });
    }

    /**
     * Create resource not found error
     * @param resource Resource type that was not found
     * @param correlationId Optional correlation ID
     * @returns Not found error message
     */
    public static notFound(
        resource: string,
        correlationId?: string,
    ): IWebSocketErrorMessage {
        return this.error(
            WebSocketErrorCode.RESOURCE_NOT_FOUND,
            `${resource} not found`,
            { correlationId, details: { resource } },
        );
    }

    /**
     * Create validation error
     * @param errors Array of validation errors
     * @param correlationId Optional correlation ID
     * @returns Validation error message
     */
    public static validationError(
        errors: Array<{ field: string; message: string; code: string }>,
        correlationId?: string,
    ): IWebSocketErrorMessage {
        return this.error(
            WebSocketErrorCode.SCHEMA_VALIDATION_FAILED,
            "Validation failed",
            {
                correlationId,
                details: { validationErrors: errors },
            },
        );
    }

    /**
     * Create rate limited error
     * @param retryAfter Optional retry after seconds
     * @param correlationId Optional correlation ID
     * @returns Rate limited error message
     */
    public static rateLimited(
        retryAfter?: number,
        correlationId?: string,
    ): IWebSocketErrorMessage {
        return this.error(WebSocketErrorCode.RATE_LIMITED, "Rate limit exceeded", {
            correlationId,
            details: { retryAfter },
        });
    }

    /**
     * Create internal server error
     * @param message Optional custom error message
     * @param correlationId Optional correlation ID
     * @returns Internal error message
     */
    public static internalError(
        message?: string,
        correlationId?: string,
    ): IWebSocketErrorMessage {
        return this.error(
            WebSocketErrorCode.INTERNAL_ERROR,
            message || "Internal server error",
            { correlationId },
        );
    }

    /**
     * Create message too large error
     * @param currentSize Current message size
     * @param maxSize Maximum allowed size
     * @param correlationId Optional correlation ID
     * @returns Message too large error
     */
    public static messageTooLarge(
        currentSize: number,
        maxSize: number,
        correlationId?: string,
    ): IWebSocketErrorMessage {
        return this.error(
            WebSocketErrorCode.MESSAGE_TOO_LARGE,
            "Message size exceeds maximum allowed",
            {
                correlationId,
                details: { currentSize, maxSize },
            },
        );
    }

    /**
     * Create channel not found error
     * @param channelId Channel ID that was not found
     * @param correlationId Optional correlation ID
     * @returns Channel not found error
     */
    public static channelNotFound(
        channelId: string,
        correlationId?: string,
    ): IWebSocketErrorMessage {
        return this.error(
            WebSocketErrorCode.CHANNEL_NOT_FOUND,
            "Channel not found",
            {
                correlationId,
                details: { channelId },
            },
        );
    }

    /**
     * Create server not found error
     * @param serverId Server ID that was not found
     * @param correlationId Optional correlation ID
     * @returns Server not found error
     */
    public static serverNotFound(
        serverId: string,
        correlationId?: string,
    ): IWebSocketErrorMessage {
        return this.error(WebSocketErrorCode.SERVER_NOT_FOUND, "Server not found", {
            correlationId,
            details: { serverId },
        });
    }

    /**
     * Create user not found error
     * @param userId User ID that was not found
     * @param correlationId Optional correlation ID
     * @returns User not found error
     */
    public static userNotFound(
        userId: string,
        correlationId?: string,
    ): IWebSocketErrorMessage {
        return this.error(WebSocketErrorCode.USER_NOT_FOUND, "User not found", {
            correlationId,
            details: { userId },
        });
    }
}
