import type { ServerWebSocket } from "bun";
import { nanoid } from "nanoid";
import {
  type IWebSocketMessage,
  type IWebSocketSuccessMessage,
  type IWebSocketErrorMessage,
  type IWebSocketEventMessage,
  type IWebSocketMeta,
  WebSocketErrorCode,
} from "../types/websocket-standardization.types";
import type { CustomWebSocketData } from "../types/websocket.types";
import { logger } from "../services/logger.service";

/**
 * Legacy WebSocket message format interface
 * Represents the old message format used before standardization
 */
export interface ILegacyWebSocketMessage {
  type: string | number;
  sender?: string;
  data?: unknown;
  timestamp?: string;
  [key: string]: unknown; // Allow additional properties
}

/**
 * Legacy error message format
 */
export interface ILegacyErrorMessage {
  type: "ERROR" | "error";
  error?: string;
  message?: string;
  code?: string;
  details?: unknown;
  [key: string]: unknown;
}

/**
 * Legacy event message format
 */
export interface ILegacyEventMessage {
  type: string | number;
  event?: string;
  data?: unknown;
  userId?: string;
  channelId?: string;
  serverId?: string;
  timestamp?: string;
  [key: string]: unknown;
}

/**
 * WebSocketCompatibility - Backward compatibility layer for WebSocket messages
 *
 * This class provides utilities to detect, convert, and handle both legacy and
 * standardized WebSocket message formats during the transition period.
 */
export class WebSocketCompatibility {
  private static readonly MESSAGE_VERSION = "1.0.0";
  private static readonly logger = logger.createLogger(
    "WebSocketCompatibility",
  );

  /**
   * Detect if a message is in legacy format
   * @param message Message to check
   * @returns True if message is in legacy format
   */
  public static isLegacyMessage(message: unknown): boolean {
    try {
      if (!message || typeof message !== "object") {
        return false;
      }

      const msg = message as Record<string, unknown>;

      // Check for standardized format markers
      if (msg.meta && typeof msg.meta === "object") {
        const meta = msg.meta as Record<string, unknown>;
        if (meta.id && meta.timestamp && meta.version) {
          return false; // This is a standardized message
        }
      }

      // Check for legacy format markers
      if (msg.type && typeof msg.type === "string") {
        // Legacy messages typically have simple structure without meta
        return !msg.meta;
      }

      // If it has a type but no meta, it's likely legacy
      return !!msg.type && !msg.meta;
    } catch (error) {
      this.logger.error(
        "Error detecting legacy message format",
        undefined,
        error,
      );
      return false;
    }
  }

  /**
   * Convert a legacy message to standardized format
   * @param message Legacy message to convert
   * @param options Conversion options
   * @returns Standardized message
   */
  public static convertLegacyMessage(
    message: ILegacyWebSocketMessage,
    options?: {
      userId?: string;
      correlationId?: string;
      source?: "client" | "server";
    },
  ): IWebSocketMessage {
    try {
      // Create standardized metadata
      const meta: IWebSocketMeta = {
        timestamp: message.timestamp ? new Date(message.timestamp) : new Date(),
        id: nanoid(),
        correlationId: options?.correlationId,
        version: this.MESSAGE_VERSION,
        source: options?.source || "client",
      };

      // Handle different legacy message types
      if (this.isLegacyErrorMessage(message)) {
        return this.convertLegacyError(message as ILegacyErrorMessage, meta);
      }

      if (this.isLegacyEventMessage(message)) {
        return this.convertLegacyEvent(message as ILegacyEventMessage, meta);
      }

      // Default conversion for generic messages
      const standardizedMessage: IWebSocketMessage = {
        type: message.type,
        data: message.data,
        meta,
      };

      // Add target information if available in legacy format
      const messageWithExtras = message as Record<string, unknown>;
      if (
        message.sender ||
        messageWithExtras.userId ||
        messageWithExtras.channelId ||
        messageWithExtras.serverId
      ) {
        standardizedMessage.target = {
          userId: (messageWithExtras.userId as string) || message.sender,
          channelId: messageWithExtras.channelId as string,
          serverId: messageWithExtras.serverId as string,
        };
      }

      this.logger.debug(
        "Converted legacy message to standardized format",
        undefined,
        {
          originalType: message.type,
          messageId: meta.id,
        },
      );

      return standardizedMessage;
    } catch (error) {
      this.logger.error("Failed to convert legacy message", undefined, {
        error,
        messageType: message.type,
      });

      // Return a basic standardized message as fallback
      return {
        type: message.type,
        data: message.data,
        meta: {
          timestamp: new Date(),
          messageId: nanoid(),
          version: this.MESSAGE_VERSION,
          source: options?.source || "client",
        },
      };
    }
  }

  /**
   * Convert a standardized message to legacy format
   * @param message Standardized message to convert
   * @returns Legacy message format
   */
  public static convertToLegacyFormat(
    message: IWebSocketMessage,
  ): ILegacyWebSocketMessage {
    try {
      const legacyMessage: ILegacyWebSocketMessage = {
        type: message.type,
        data: message.data,
        timestamp: message.meta.timestamp,
      };

      // Handle different standardized message types
      if (this.isStandardizedSuccessMessage(message)) {
        const successMsg = message as IWebSocketSuccessMessage;
        legacyMessage.success = true;
        legacyMessage.data = successMsg.data;
        if (successMsg.message) {
          legacyMessage.message = successMsg.message;
        }
      } else if (this.isStandardizedErrorMessage(message)) {
        const errorMsg = message as IWebSocketErrorMessage;
        legacyMessage.type = "ERROR";
        legacyMessage.error = errorMsg.error.message;
        legacyMessage.code = errorMsg.error.code;
        legacyMessage.details = errorMsg.error.details;
      } else if (this.isStandardizedEventMessage(message)) {
        const eventMsg = message as IWebSocketEventMessage;
        legacyMessage.event = eventMsg.event;
        legacyMessage.data = eventMsg.data;
      }

      // Add target information as separate fields for legacy compatibility
      if (message.target) {
        if (message.target.userId) {
          legacyMessage.userId = message.target.userId;
        }
        if (message.target.channelId) {
          legacyMessage.channelId = message.target.channelId;
        }
        if (message.target.serverId) {
          legacyMessage.serverId = message.target.serverId;
        }
      }

      this.logger.debug(
        "Converted standardized message to legacy format",
        undefined,
        {
          originalType: message.type,
          messageId: message.meta.id,
        },
      );

      return legacyMessage;
    } catch (error) {
      this.logger.error(
        "Failed to convert standardized message to legacy format",
        undefined,
        {
          error,
          messageType: message.type,
          messageId: message.meta?.messageId,
        },
      );

      // Return basic legacy format as fallback
      return {
        type: message.type,
        data: message.data,
        timestamp: message.meta?.timestamp || new Date().toISOString(),
      };
    }
  }

  /**
   * Determine if a WebSocket connection should use legacy format
   * @param ws WebSocket connection to check
   * @returns True if legacy format should be used
   */
  public static shouldUseLegacyFormat(
    ws: ServerWebSocket<CustomWebSocketData>,
  ): boolean {
    try {
      // Check if the connection has been marked as supporting standardized format
      const userData = ws.data as CustomWebSocketData & Record<string, unknown>;

      // If explicitly marked as supporting standardized format
      if (userData.supportsStandardizedFormat === true) {
        return false;
      }

      // If explicitly marked as legacy only
      if (userData.legacyOnly === true) {
        return true;
      }

      // Check client capabilities if available
      const capabilities = userData.capabilities as { protocolVersion?: string } | undefined;
      if (capabilities?.protocolVersion) {
        const version = capabilities.protocolVersion;
        // Version 1.0.0 and above support standardized format
        return this.compareVersions(version, "1.0.0") < 0;
      }

      // Check user agent or other client indicators
      const session = userData.session as { userAgent?: string } | undefined;
      if (session?.userAgent) {
        const userAgent = session.userAgent.toLowerCase();
        // Check for known legacy clients
        if (userAgent.includes("legacy") || userAgent.includes("old")) {
          return true;
        }
      }

      // Default to legacy format during transition period
      // This can be changed to false once migration is complete
      const defaultToLegacy = process.env.WEBSOCKET_DEFAULT_LEGACY === "true";
      return defaultToLegacy;
    } catch (error) {
      this.logger.error(
        "Error determining message format preference",
        undefined,
        {
          error,
          userId: ws.data?.userId,
        },
      );

      // Default to legacy format on error for safety
      return true;
    }
  }

  /**
   * Send a message using the appropriate format for the connection
   * @param ws WebSocket connection
   * @param message Standardized message to send
   */
  public static sendCompatibleMessage(
    ws: ServerWebSocket<CustomWebSocketData>,
    message: IWebSocketMessage,
  ): void {
    try {
      if (ws.readyState !== WebSocket.OPEN) {
        this.logger.warn(
          "Attempted to send message to closed WebSocket",
          undefined,
          {
            messageId: message.meta.id,
            userId: ws.data.userId,
          },
        );
        return;
      }

      let messageToSend: string;

      if (this.shouldUseLegacyFormat(ws)) {
        // Convert to legacy format and send
        const legacyMessage = this.convertToLegacyFormat(message);
        messageToSend = JSON.stringify(legacyMessage);

        this.logger.debug("Sent message in legacy format", undefined, {
          messageId: message.meta.id,
          type: message.type,
          userId: ws.data.userId,
        });
      } else {
        // Send in standardized format
        messageToSend = JSON.stringify(message);

        this.logger.debug("Sent message in standardized format", undefined, {
          messageId: message.meta.id,
          type: message.type,
          userId: ws.data.userId,
        });
      }

      ws.send(messageToSend);
    } catch (error) {
      this.logger.error("Failed to send compatible message", undefined, {
        error,
        messageId: message.meta.id,
        userId: ws.data.userId,
      });
    }
  }

  /**
   * Process incoming message and convert to standardized format if needed
   * @param rawMessage Raw message from WebSocket
   * @param ws WebSocket connection
   * @returns Standardized message
   */
  public static processIncomingMessage(
    rawMessage: string,
    ws: ServerWebSocket<CustomWebSocketData>,
  ): IWebSocketMessage | null {
    try {
      const parsedMessage = JSON.parse(rawMessage);

      if (this.isLegacyMessage(parsedMessage)) {
        this.logger.debug("Processing legacy message", undefined, {
          type: parsedMessage.type,
          userId: ws.data.userId,
        });

        return this.convertLegacyMessage(parsedMessage, {
          userId: ws.data.userId,
          source: "client",
        });
      } else {
        this.logger.debug("Processing standardized message", undefined, {
          type: parsedMessage.type,
          messageId: parsedMessage.meta?.messageId,
          userId: ws.data.userId,
        });

        return parsedMessage as IWebSocketMessage;
      }
    } catch (error) {
      this.logger.error("Failed to process incoming message", undefined, {
        error,
        userId: ws.data.userId,
        rawMessage: rawMessage.substring(0, 200), // Log first 200 chars for debugging
      });

      return null;
    }
  }

  /**
   * Create migration utilities for existing handlers
   */
  public static createMigrationUtilities(): {
    wrapHandler: <T>(
      legacyHandler: (
        ws: ServerWebSocket<CustomWebSocketData>,
        sender: string,
        data: T,
      ) => void | Promise<void>,
    ) => (
      ws: ServerWebSocket<CustomWebSocketData>,
      message: IWebSocketMessage<T>,
    ) => Promise<void>;
    broadcastLegacyMessage: (
      manager: unknown,
      legacyMessage: Record<string, unknown>,
      serverId?: string,
      channelId?: string,
      excludeUserId?: string,
    ) => void;
  } {
    return {
      /**
       * Wrap an existing handler to support both formats
       * @param legacyHandler Original handler function
       * @returns Wrapped handler that supports both formats
       */
      wrapHandler: <T>(
        legacyHandler: (
          ws: ServerWebSocket<CustomWebSocketData>,
          sender: string,
          data: T,
        ) => void | Promise<void>,
      ) => {
        return async (
          ws: ServerWebSocket<CustomWebSocketData>,
          message: IWebSocketMessage<T>,
        ) => {
          try {
            // Extract sender and data for legacy handler compatibility
            const sender = message.target?.userId || ws.data.userId || "";
            const data = message.data as T;

            // Call the legacy handler
            await legacyHandler(ws, sender, data);
          } catch (error) {
            this.logger.error("Error in wrapped legacy handler", undefined, {
              error,
              messageType: message.type,
              messageId: message.meta.id,
            });
          }
        };
      },

      /**
       * Convert legacy broadcast calls to use standardized format
       * @param manager WebSocket manager instance
       * @param legacyMessage Legacy message object
       * @param serverId Server ID for broadcast
       * @param channelId Channel ID for broadcast
       * @param excludeUserId User ID to exclude from broadcast
       */
      broadcastLegacyMessage: (
        manager: unknown,
        legacyMessage: Record<string, unknown>,
        serverId?: string,
        channelId?: string,
        excludeUserId?: string,
      ): void => {
        try {
          // Convert legacy message to standardized format
          const standardizedMessage = this.convertLegacyMessage(
            legacyMessage as ILegacyWebSocketMessage,
            {
              source: "server",
            },
          );

          // Use the manager's broadcast method
          const serializedMessage = JSON.stringify(standardizedMessage);
          (manager as { broadcast: (message: string, serverId?: string, channelId?: string, excludeUserId?: string) => void }).broadcast(
            serializedMessage,
            serverId,
            channelId,
            excludeUserId,
          );

          this.logger.debug("Broadcasted converted legacy message", undefined, {
            originalType: legacyMessage.type,
            messageId: standardizedMessage.meta.id,
            serverId,
            channelId,
          });
        } catch (error) {
          this.logger.error("Failed to broadcast legacy message", undefined, {
            error,
            messageType: legacyMessage.type,
          });
        }
      },
    };
  }

  // Private helper methods

  private static isLegacyErrorMessage(
    message: ILegacyWebSocketMessage,
  ): boolean {
    const messageWithExtras = message as Record<string, unknown>;
    return (
      message.type === "ERROR" ||
      message.type === "error" ||
      !!messageWithExtras.error ||
      !!messageWithExtras.code
    );
  }

  private static isLegacyEventMessage(
    message: ILegacyWebSocketMessage,
  ): boolean {
    const messageWithExtras = message as Record<string, unknown>;
    return !!messageWithExtras.event;
  }

  private static isStandardizedSuccessMessage(
    message: IWebSocketMessage,
  ): boolean {
    const messageWithExtras = message as unknown as Record<string, unknown>;
    return messageWithExtras.success === true;
  }

  private static isStandardizedErrorMessage(
    message: IWebSocketMessage,
  ): boolean {
    const messageWithExtras = message as unknown as Record<string, unknown>;
    return messageWithExtras.success === false || !!messageWithExtras.error;
  }

  private static isStandardizedEventMessage(
    message: IWebSocketMessage,
  ): boolean {
    const messageWithExtras = message as unknown as Record<string, unknown>;
    return !!messageWithExtras.event;
  }

  private static convertLegacyError(
    message: ILegacyErrorMessage,
    meta: IWebSocketMeta,
  ): IWebSocketErrorMessage {
    const errorCode = this.mapLegacyErrorCode(message.code);
    const errorMessage = message.error || message.message || "Unknown error";

    return {
      type: "ERROR",
      success: false,
      error: {
        code: errorCode,
        message: errorMessage,
        details: message.details && typeof message.details === 'object' 
          ? { ...(message.details as Record<string, unknown>) } 
          : message.details,
      },
      meta,
    };
  }

  private static convertLegacyEvent(
    message: ILegacyEventMessage,
    meta: IWebSocketMeta,
  ): IWebSocketEventMessage {
    return {
      type: "EVENT",
      event: message.event || String(message.type),
      data: message.data,
      meta,
      target: {
        userId: message.userId,
        channelId: message.channelId,
        serverId: message.serverId,
      },
    };
  }

  private static mapLegacyErrorCode(legacyCode?: string): WebSocketErrorCode {
    if (!legacyCode) {
      return WebSocketErrorCode.INTERNAL_ERROR;
    }

    // Map common legacy error codes to standardized ones
    const codeMap: Record<string, WebSocketErrorCode> = {
      AUTH_ERROR: WebSocketErrorCode.AUTH_FAILED,
      PERMISSION_ERROR: WebSocketErrorCode.PERMISSION_DENIED,
      NOT_FOUND: WebSocketErrorCode.RESOURCE_NOT_FOUND,
      VALIDATION_ERROR: WebSocketErrorCode.SCHEMA_VALIDATION_FAILED,
      RATE_LIMIT: WebSocketErrorCode.RATE_LIMITED,
      INVALID_DATA: WebSocketErrorCode.INVALID_DATA,
      TIMEOUT: WebSocketErrorCode.TIMEOUT,
    };

    return codeMap[legacyCode] || WebSocketErrorCode.INTERNAL_ERROR;
  }

  private static compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.split(".").map(Number);
    const v2Parts = version2.split(".").map(Number);

    for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;

      if (v1Part < v2Part) return -1;
      if (v1Part > v2Part) return 1;
    }

    return 0;
  }
}
