import { BadgeDesign, BadgeType } from "../types/badge.types";

/**
 * Badge Visual Renderer - Generates SVG representations of badges with rich designs
 */

export interface BadgeRenderOptions {
  size?: 'small' | 'medium' | 'large' | 'xl';
  animation?: boolean;
  preview?: boolean;
  accessibility?: boolean;
}

export interface BadgeAnimationConfig {
  type: 'pulse' | 'glow' | 'bounce' | 'rotate' | 'shake' | 'fade' | 'none';
  duration?: number;
  delay?: number;
  iterations?: number | 'infinite';
}

export interface BadgeSVGResult {
  svg: string;
  css?: string;
  accessibility: {
    alt: string;
    description: string;
    role: string;
  };
}

/**
 * Size configurations for different badge display contexts
 */
const BADGE_SIZES = {
  small: { width: 24, height: 24, iconSize: 12, fontSize: 8 },
  medium: { width: 32, height: 32, iconSize: 16, fontSize: 10 },
  large: { width: 48, height: 48, iconSize: 24, fontSize: 12 },
  xl: { width: 64, height: 64, iconSize: 32, fontSize: 14 }
} as const;

/**
 * Animation configurations for different badge effects
 */
const ANIMATION_CONFIGS: Record<string, BadgeAnimationConfig> = {
  pulse: { type: 'pulse', duration: 2000, iterations: 'infinite' },
  glow: { type: 'glow', duration: 3000, iterations: 'infinite' },
  bounce: { type: 'bounce', duration: 1000, iterations: 3 },
  rotate: { type: 'rotate', duration: 4000, iterations: 'infinite' },
  shake: { type: 'shake', duration: 500, iterations: 2 },
  fade: { type: 'fade', duration: 1500, iterations: 'infinite' },
  none: { type: 'none' }
};

/**
 * Badge Visual Renderer Class
 */
export class BadgeVisualRenderer {
  /**
   * Render a badge as SVG with full visual design support
   */
  static renderBadgeSVG(
    badge: BadgeType,
    options: BadgeRenderOptions = {}
  ): BadgeSVGResult {
    const {
      size = 'medium',
      animation = false,
      preview = false,
      accessibility = true
    } = options;

    const sizeConfig = BADGE_SIZES[size];
    const design = typeof badge.design === 'string' 
      ? JSON.parse(badge.design) as BadgeDesign 
      : badge.design;

    // Generate unique ID for this badge instance
    const badgeId = `badge-${badge.id}-${Date.now()}`;
    const animationId = `anim-${badgeId}`;

    // Build SVG content
    const svg = this.buildSVGContent(badge, design, sizeConfig, badgeId, animation);
    
    // Generate CSS for animations if needed
    const css = animation && badge.animation && badge.animation !== 'none'
      ? this.generateAnimationCSS(badge.animation, animationId)
      : undefined;

    // Generate accessibility information
    const accessibilityInfo = accessibility 
      ? this.generateAccessibilityInfo(badge)
      : { alt: badge.name, description: badge.description, role: 'img' };

    return {
      svg,
      css,
      accessibility: accessibilityInfo
    };
  }

  /**
   * Build the main SVG content for a badge
   */
  private static buildSVGContent(
    badge: BadgeType,
    design: BadgeDesign,
    sizeConfig: typeof BADGE_SIZES[keyof typeof BADGE_SIZES],
    badgeId: string,
    withAnimation: boolean
  ): string {
    const { width, height, iconSize } = sizeConfig;
    
    // Create gradient definitions if needed
    const gradientDefs = design.gradient 
      ? this.createGradientDefinitions(design, badgeId)
      : '';

    // Create pattern definitions if needed
    const patternDefs = design.pattern 
      ? this.createPatternDefinitions(design, badgeId)
      : '';

    // Build the main shape
    const mainShape = this.createMainShape(design, width, height, badgeId);

    // Add decorative elements
    const decorativeElements = design.elements 
      ? this.createDecorativeElements(design.elements, width, height)
      : '';

    // Add icon/emoji
    const iconElement = this.createIconElement(badge.icon, iconSize, width, height);

    // Add animation class if needed
    const animationClass = withAnimation && badge.animation && badge.animation !== 'none'
      ? `class="badge-animation-${badge.animation}"`
      : '';

    return `
      <svg 
        width="${width}" 
        height="${height}" 
        viewBox="0 0 ${width} ${height}" 
        xmlns="http://www.w3.org/2000/svg"
        ${animationClass}
        role="img"
        aria-label="${badge.name}"
      >
        <defs>
          ${gradientDefs}
          ${patternDefs}
        </defs>
        ${mainShape}
        ${decorativeElements}
        ${iconElement}
      </svg>
    `.trim();
  }

  /**
   * Create gradient definitions for SVG
   */
  private static createGradientDefinitions(design: BadgeDesign, badgeId: string): string {
    if (!design.gradient || design.colors.length < 2) return '';

    const gradientId = `gradient-${badgeId}`;
    const colors = design.colors;
    
    // Create color stops
    const colorStops = colors.map((color, index) => {
      const offset = (index / (colors.length - 1)) * 100;
      return `<stop offset="${offset}%" stop-color="${color}" />`;
    }).join('\n');

    // Determine gradient type and direction
    const gradientType = design.gradient.includes('radial') ? 'radialGradient' : 'linearGradient';
    const gradientProps = gradientType === 'radialGradient' 
      ? 'cx="50%" cy="50%" r="50%"'
      : 'x1="0%" y1="0%" x2="100%" y2="100%"';

    return `
      <${gradientType} id="${gradientId}" ${gradientProps}>
        ${colorStops}
      </${gradientType}>
    `;
  }

  /**
   * Create pattern definitions for SVG
   */
  private static createPatternDefinitions(design: BadgeDesign, badgeId: string): string {
    if (!design.pattern) return '';

    const patternId = `pattern-${badgeId}`;
    
    // Simple pattern implementations
    switch (design.pattern) {
      case 'dots':
        return `
          <pattern id="${patternId}" patternUnits="userSpaceOnUse" width="8" height="8">
            <circle cx="4" cy="4" r="1" fill="${design.colors[0]}" opacity="0.3"/>
          </pattern>
        `;
      case 'stripes':
        return `
          <pattern id="${patternId}" patternUnits="userSpaceOnUse" width="8" height="8">
            <rect width="4" height="8" fill="${design.colors[0]}" opacity="0.3"/>
          </pattern>
        `;
      case 'stars':
        return `
          <pattern id="${patternId}" patternUnits="userSpaceOnUse" width="12" height="12">
            <polygon points="6,2 7,5 10,5 8,7 9,10 6,8 3,10 4,7 2,5 5,5" fill="${design.colors[0]}" opacity="0.3"/>
          </pattern>
        `;
      default:
        return '';
    }
  }

  /**
   * Create the main badge shape
   */
  private static createMainShape(
    design: BadgeDesign,
    width: number,
    height: number,
    badgeId: string
  ): string {
    const fill = design.gradient 
      ? `url(#gradient-${badgeId})`
      : design.background || design.colors[0];
    
    const patternOverlay = design.pattern 
      ? `<use href="#pattern-${badgeId}" opacity="0.2"/>`
      : '';

    switch (design.shape) {
      case 'circle':
        return `
          <circle 
            cx="${width/2}" 
            cy="${height/2}" 
            r="${Math.min(width, height)/2 - 2}" 
            fill="${fill}"
            stroke="${design.colors[1] || design.colors[0]}"
            stroke-width="1"
          />
          ${patternOverlay}
        `;
      
      case 'shield':
        const shieldPath = `M${width/2},2 L${width-2},${height/3} L${width-2},${height*2/3} L${width/2},${height-2} L2,${height*2/3} L2,${height/3} Z`;
        return `
          <path 
            d="${shieldPath}" 
            fill="${fill}"
            stroke="${design.colors[1] || design.colors[0]}"
            stroke-width="1"
          />
          ${patternOverlay}
        `;
      
      case 'star':
        const starPath = this.createStarPath(width/2, height/2, Math.min(width, height)/2 - 2, 5);
        return `
          <path 
            d="${starPath}" 
            fill="${fill}"
            stroke="${design.colors[1] || design.colors[0]}"
            stroke-width="1"
          />
          ${patternOverlay}
        `;
      
      case 'hexagon':
        const hexPath = this.createHexagonPath(width/2, height/2, Math.min(width, height)/2 - 2);
        return `
          <path 
            d="${hexPath}" 
            fill="${fill}"
            stroke="${design.colors[1] || design.colors[0]}"
            stroke-width="1"
          />
          ${patternOverlay}
        `;
      
      default: // rectangle
        return `
          <rect 
            x="2" 
            y="2" 
            width="${width-4}" 
            height="${height-4}" 
            rx="4" 
            fill="${fill}"
            stroke="${design.colors[1] || design.colors[0]}"
            stroke-width="1"
          />
          ${patternOverlay}
        `;
    }
  }

  /**
   * Create decorative elements
   */
  private static createDecorativeElements(elements: string[], width: number, height: number): string {
    return elements.map(element => {
      switch (element) {
        case 'border':
          return `<rect x="1" y="1" width="${width-2}" height="${height-2}" rx="4" fill="none" stroke="#fff" stroke-width="1" opacity="0.5"/>`;
        case 'glow':
          return `<circle cx="${width/2}" cy="${height/2}" r="${Math.min(width, height)/2}" fill="none" stroke="#fff" stroke-width="2" opacity="0.3"/>`;
        case 'sparkles':
          return this.createSparkles(width, height);
        default:
          return '';
      }
    }).join('\n');
  }

  /**
   * Create sparkle effects
   */
  private static createSparkles(width: number, height: number): string {
    const sparkles = [];
    const sparkleCount = 3;
    
    for (let i = 0; i < sparkleCount; i++) {
      const x = (width * 0.2) + (Math.random() * width * 0.6);
      const y = (height * 0.2) + (Math.random() * height * 0.6);
      const size = 1 + Math.random() * 2;
      
      sparkles.push(`
        <circle cx="${x}" cy="${y}" r="${size}" fill="#fff" opacity="0.8">
          <animate attributeName="opacity" values="0.8;0.2;0.8" dur="2s" repeatCount="indefinite"/>
        </circle>
      `);
    }
    
    return sparkles.join('\n');
  }

  /**
   * Create icon element (emoji or symbol)
   */
  private static createIconElement(icon: string, iconSize: number, width: number, height: number): string {
    // For emoji, use text element
    if (this.isEmoji(icon)) {
      return `
        <text 
          x="${width/2}" 
          y="${height/2 + iconSize/3}" 
          font-size="${iconSize}" 
          text-anchor="middle" 
          dominant-baseline="middle"
        >${icon}</text>
      `;
    }
    
    // For other icons, could be extended to support icon fonts or SVG symbols
    return `
      <text 
        x="${width/2}" 
        y="${height/2 + iconSize/3}" 
        font-size="${iconSize}" 
        text-anchor="middle" 
        dominant-baseline="middle"
        font-family="Arial, sans-serif"
      >${icon}</text>
    `;
  }

  /**
   * Generate CSS animations for badges
   */
  private static generateAnimationCSS(animationType: string, animationId: string): string {
    const config = ANIMATION_CONFIGS[animationType] || ANIMATION_CONFIGS.none;
    
    if (config.type === 'none') return '';

    const keyframes = this.getAnimationKeyframes(config.type);
    const duration = config.duration || 2000;
    const iterations = config.iterations || 'infinite';
    const delay = config.delay || 0;

    return `
      @keyframes ${animationId} {
        ${keyframes}
      }
      
      .badge-animation-${animationType} {
        animation: ${animationId} ${duration}ms ease-in-out ${delay}ms ${iterations};
      }
    `;
  }

  /**
   * Get keyframes for different animation types
   */
  private static getAnimationKeyframes(type: string): string {
    switch (type) {
      case 'pulse':
        return `
          0%, 100% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.1); opacity: 0.8; }
        `;
      case 'glow':
        return `
          0%, 100% { filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.3)); }
          50% { filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8)); }
        `;
      case 'bounce':
        return `
          0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
          40% { transform: translateY(-10px); }
          60% { transform: translateY(-5px); }
        `;
      case 'rotate':
        return `
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        `;
      case 'shake':
        return `
          0%, 100% { transform: translateX(0); }
          10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
          20%, 40%, 60%, 80% { transform: translateX(2px); }
        `;
      case 'fade':
        return `
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        `;
      default:
        return '';
    }
  }

  /**
   * Generate accessibility information for badges
   */
  private static generateAccessibilityInfo(badge: BadgeType): {
    alt: string;
    description: string;
    role: string;
  } {
    const title = badge.title || badge.name;
    const description = badge.tooltip || badge.description;
    
    return {
      alt: `${title} badge`,
      description: `Badge: ${title}. ${description}`,
      role: 'img'
    };
  }

  /**
   * Helper method to create star path
   */
  private static createStarPath(cx: number, cy: number, radius: number, points: number): string {
    const angle = Math.PI / points;
    const innerRadius = radius * 0.4;
    let path = '';

    for (let i = 0; i < points * 2; i++) {
      const r = i % 2 === 0 ? radius : innerRadius;
      const x = cx + Math.cos(i * angle - Math.PI / 2) * r;
      const y = cy + Math.sin(i * angle - Math.PI / 2) * r;
      path += i === 0 ? `M${x},${y}` : `L${x},${y}`;
    }
    
    return path + 'Z';
  }

  /**
   * Helper method to create hexagon path
   */
  private static createHexagonPath(cx: number, cy: number, radius: number): string {
    const points = [];
    for (let i = 0; i < 6; i++) {
      const angle = (i * Math.PI) / 3;
      const x = cx + radius * Math.cos(angle);
      const y = cy + radius * Math.sin(angle);
      points.push(`${x},${y}`);
    }
    return `M${points.join('L')}Z`;
  }

  /**
   * Check if a string is an emoji
   */
  private static isEmoji(str: string): boolean {
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
    return emojiRegex.test(str);
  }

  /**
   * Validate badge design for rendering
   */
  static validateBadgeDesign(design: BadgeDesign): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!design.shape) {
      errors.push('Badge shape is required');
    }

    if (!design.background && (!design.colors || design.colors.length === 0)) {
      errors.push('Badge must have either background color or color palette');
    }

    if (design.colors) {
      design.colors.forEach((color, index) => {
        if (!/^#[0-9A-Fa-f]{6}$/.test(color)) {
          errors.push(`Invalid color at index ${index}: ${color}`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate badge preview for design validation
   */
  static generateBadgePreview(design: BadgeDesign, options: Partial<BadgeRenderOptions> = {}): BadgeSVGResult {
    const previewBadge: BadgeType = {
      id: 'preview',
      collectionId: undefined,
      badgeId: 'preview',
      name: 'Preview Badge',
      title: 'Preview',
      description: 'Badge design preview',
      icon: '🏆',
      tooltip: 'This is a preview of your badge design',
      design,
      criteria: { requirement: 'Preview', tracked: 'none' },
      perks: [],
      unlockType: 'manual',
      visualDescription: 'Preview badge',
      animation: 'none',
      displayOrder: 0,
      category: 'achievement',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return this.renderBadgeSVG(previewBadge, { ...options, preview: true });
  }
}