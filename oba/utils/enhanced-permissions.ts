import { db } from "../db";
import { drizzle } from "drizzle-orm/postgres-js";
import {
  UserRoles,
  ServerRoleSchema,
  ChannelAllowedRolesSchema,
  ChannelAllowedUsersSchema,
  ChannelVisibleRolesSchema,
  ChannelVisibleUsersSchema,
  ChannelPrivacySchema,
  ServerSchema,
  ChannelPermissionOverridesSchema,
  ChannelSchema,
  CategoryPermissionSchema,
  ChannelCategorySchema,
  CategoryAllowedRolesSchema,
  CategoryAllowedUsersSchema,
  CategoryVisibleRolesSchema,
  CategoryVisibleUsersSchema,
  CategoryPermissionOverridesSchema,
} from "../db/schema";
import { eq, and, or, isNull } from "drizzle-orm";
import { ADMINISTRATOR } from "../constants/permissions";

/**
 * Check if a user has a specific permission in a server
 *
 * @param userId - The ID of the user
 * @param serverId - The ID of the server
 * @param requiredPermission - The permission to check for
 * @returns True if the user has the permission, false otherwise
 */
export async function hasServerPermission(
  dbConnection: ReturnType<typeof drizzle>,
  userId: string,
  serverId: string,
  requiredPermission: bigint,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;
    // Check if the user is the server owner (owners have all permissions)
    const server = await db
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    if (server.length > 0 && server[0].ownerId === userId) {
      return true;
    }

    // Fetch user's roles in the given server
    const userRoles = await db
      .select({ roleId: UserRoles.roleId })
      .from(UserRoles)
      .where(
        and(eq(UserRoles.userId, userId), eq(UserRoles.serverId, serverId)),
      );

    if (userRoles.length === 0) {
      return false; // User has no roles in this server
    }

    let userPermissions = BigInt(0);

    // Combine permissions from all roles
    for (const role of userRoles) {
      const roleData = await db
        .select({ permissions: ServerRoleSchema.permissions })
        .from(ServerRoleSchema)
        .where(eq(ServerRoleSchema.id, role.roleId as string));

      if (roleData.length > 0) {
        userPermissions |= roleData[0].permissions;
      }
    }

    // Check if the user has the ADMINISTRATOR permission
    if ((userPermissions & ADMINISTRATOR) === ADMINISTRATOR) {
      return true; // Administrators have all permissions
    }

    // Check if the user has the required permission
    return (userPermissions & requiredPermission) === requiredPermission;
  } catch (error) {
    console.error("Error checking server permission:", error);
    return false;
  }
}

/**
 * Check if a user has access to a channel
 *
 * @param dbConnection - The database connection to use
 * @param userId - The ID of the user
 * @param channelId - The ID of the channel
 * @returns True if the user has access to the channel, false otherwise
 */
export async function hasChannelAccess(
  dbConnection: ReturnType<typeof drizzle>,
  userId: string,
  channelId: string,
  serverId: string,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if the user is the server owner (owners have access to all channels)
    const server = await db
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    if (server.length > 0 && server[0].ownerId === userId) {
      return true;
    }

    // Check if the user has the ADMINISTRATOR permission
    const isAdmin = await hasServerPermission(
      db,
      userId,
      serverId,
      ADMINISTRATOR,
    );
    if (isAdmin) {
      return true;
    }

    // Get channel details
    const channel = await db
      .select({
        id: ChannelSchema.id,
        categoryId: ChannelSchema.categoryId,
      })
      .from(ChannelSchema)
      .where(eq(ChannelSchema.id, channelId))
      .limit(1);

    if (channel.length === 0) {
      return false; // Channel not found
    }

    // Check category privacy if the channel belongs to a category
    if (channel[0].categoryId) {
      const categoryPermission = await db
        .select({ isPrivate: CategoryPermissionSchema.isPrivate })
        .from(CategoryPermissionSchema)
        .where(eq(CategoryPermissionSchema.categoryId, channel[0].categoryId))
        .limit(1);

      // If category is private, check if user has access to the category
      if (categoryPermission.length > 0 && categoryPermission[0].isPrivate) {
        // Category-level access check would go here
        // For now, we'll continue to the channel-specific checks
      }
    }

    // Check channel privacy settings
    const privacySettings = await db
      .select({ isPublic: ChannelPrivacySchema.isPublic })
      .from(ChannelPrivacySchema)
      .where(eq(ChannelPrivacySchema.channelId, channelId))
      .limit(1);

    // If the channel is public, the user has access
    if (privacySettings.length > 0 && privacySettings[0].isPublic) {
      return true;
    }

    // Check if the user is specifically allowed
    const userAllowed = await db
      .select()
      .from(ChannelAllowedUsersSchema)
      .where(
        and(
          eq(ChannelAllowedUsersSchema.channelId, channelId),
          eq(ChannelAllowedUsersSchema.userId, userId),
        ),
      )
      .limit(1);

    if (userAllowed.length > 0) {
      return true; // User is specifically allowed
    }

    // Get user's roles
    const userRoles = await db
      .select({ roleId: UserRoles.roleId })
      .from(UserRoles)
      .where(
        and(eq(UserRoles.userId, userId), eq(UserRoles.serverId, serverId)),
      );

    if (userRoles.length === 0) {
      return false; // User has no roles in this server
    }

    const roleIds = userRoles.map((role) => role.roleId);

    // Check if any of the user's roles are allowed in this channel
    const allowedRoles = await db
      .select({ roleId: ChannelAllowedRolesSchema.roleId })
      .from(ChannelAllowedRolesSchema)
      .where(
        and(
          eq(ChannelAllowedRolesSchema.channelId, channelId),
          roleIds.length > 0
            ? or(
                ...roleIds.map((roleId) =>
                  eq(ChannelAllowedRolesSchema.roleId, roleId as string),
                ),
              )
            : eq(ChannelAllowedRolesSchema.roleId, ""),
        ),
      );

    return allowedRoles.length > 0;
  } catch (error) {
    console.error("Error checking channel access:", error);
    return false;
  }
}

/**
 * Check if a user has a specific permission in a channel
 *
 * @param dbConnection - The database connection to use
 * @param userId - The ID of the user
 * @param channelId - The ID of the channel
 * @param serverId - The ID of the server
 * @param requiredPermission - The permission to check for
 * @returns True if the user has the permission, false otherwise
 */
export async function hasChannelPermission(
  dbConnection: ReturnType<typeof drizzle>,
  userId: string,
  channelId: string,
  serverId: string,
  requiredPermission: bigint,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // First check if the user has access to the channel
    const hasAccess = await hasChannelAccess(db, userId, channelId, serverId);
    if (!hasAccess) {
      return false;
    }

    // Check if the user is the server owner (owners have all permissions)
    const server = await db
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    if (server.length > 0 && server[0].ownerId === userId) {
      return true;
    }

    // Check for user-specific permission overrides
    const userOverrides = await db
      .select({
        allowed: ChannelPermissionOverridesSchema.allowedPermissions,
        denied: ChannelPermissionOverridesSchema.deniedPermissions,
      })
      .from(ChannelPermissionOverridesSchema)
      .where(
        and(
          eq(ChannelPermissionOverridesSchema.channelId, channelId),
          eq(ChannelPermissionOverridesSchema.userId, userId),
          isNull(ChannelPermissionOverridesSchema.roleId),
        ),
      )
      .limit(1);

    if (userOverrides.length > 0) {
      // Check if the permission is explicitly denied
      if (
        (userOverrides[0].denied & requiredPermission) ===
        requiredPermission
      ) {
        return false;
      }

      // Check if the permission is explicitly allowed
      if (
        (userOverrides[0].allowed & requiredPermission) ===
        requiredPermission
      ) {
        return true;
      }
    }

    // Get user's roles
    const userRoles = await db
      .select({ roleId: UserRoles.roleId })
      .from(UserRoles)
      .where(
        and(eq(UserRoles.userId, userId), eq(UserRoles.serverId, serverId)),
      );

    if (userRoles.length === 0) {
      return false; // User has no roles in this server
    }

    // Check for role-specific permission overrides
    let hasPermissionFromRoles = false;
    let isPermissionDeniedByAnyRole = false;

    for (const role of userRoles) {
      const roleOverrides = await db
        .select({
          allowed: ChannelPermissionOverridesSchema.allowedPermissions,
          denied: ChannelPermissionOverridesSchema.deniedPermissions,
        })
        .from(ChannelPermissionOverridesSchema)
        .where(
          and(
            eq(ChannelPermissionOverridesSchema.channelId, channelId),
            eq(ChannelPermissionOverridesSchema.roleId, role.roleId as string),
            isNull(ChannelPermissionOverridesSchema.userId),
          ),
        )
        .limit(1);

      if (roleOverrides.length > 0) {
        // Check if the permission is explicitly denied by this role
        if (
          (roleOverrides[0].denied & requiredPermission) ===
          requiredPermission
        ) {
          isPermissionDeniedByAnyRole = true;
        }

        // Check if the permission is explicitly allowed by this role
        if (
          (roleOverrides[0].allowed & requiredPermission) ===
          requiredPermission
        ) {
          hasPermissionFromRoles = true;
        }
      }
    }

    // If any role explicitly denies the permission, deny access
    if (isPermissionDeniedByAnyRole) {
      return false;
    }

    // If any role explicitly allows the permission, allow access
    if (hasPermissionFromRoles) {
      return true;
    }

    // If no overrides are found, fall back to server-level permissions
    return hasServerPermission(db, userId, serverId, requiredPermission);
  } catch (error) {
    console.error("Error checking channel permission:", error);
    return false;
  }
}

/**
 * Add a user to the allowed users list for a channel
 *
 * @param dbConnection - The database connection to use
 * @param channelId - The ID of the channel
 * @param userId - The ID of the user to allow
 * @returns True if successful, false otherwise
 */
export async function addAllowedUserToChannel(
  dbConnection: ReturnType<typeof drizzle>,
  channelId: string,
  userId: string,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if the user is already allowed
    const existingAllowedUser = await db
      .select()
      .from(ChannelAllowedUsersSchema)
      .where(
        and(
          eq(ChannelAllowedUsersSchema.channelId, channelId),
          eq(ChannelAllowedUsersSchema.userId, userId),
        ),
      )
      .limit(1);

    if (existingAllowedUser.length > 0) {
      return true; // User is already allowed
    }

    // Add the user to the allowed users list
    await db.insert(ChannelAllowedUsersSchema).values({
      channelId,
      userId,
    });

    return true;
  } catch (error) {
    console.error("Error adding allowed user to channel:", error);
    return false;
  }
}

/**
 * Remove a user from the allowed users list for a channel
 *
 * @param dbConnection - The database connection to use
 * @param channelId - The ID of the channel
 * @param userId - The ID of the user to remove
 * @returns True if successful, false otherwise
 */
export async function removeAllowedUserFromChannel(
  dbConnection: ReturnType<typeof drizzle>,
  channelId: string,
  userId: string,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Remove the user from the allowed users list
    await db
      .delete(ChannelAllowedUsersSchema)
      .where(
        and(
          eq(ChannelAllowedUsersSchema.channelId, channelId),
          eq(ChannelAllowedUsersSchema.userId, userId),
        ),
      );

    return true;
  } catch (error) {
    console.error("Error removing allowed user from channel:", error);
    return false;
  }
}

/**
 * Set permission overrides for a user in a channel
 *
 * @param dbConnection - The database connection to use
 * @param channelId - The ID of the channel
 * @param userId - The ID of the user
 * @param allowedPermissions - Bitmask of permissions to explicitly allow
 * @param deniedPermissions - Bitmask of permissions to explicitly deny
 * @returns True if successful, false otherwise
 */
export async function setUserChannelPermissionOverrides(
  dbConnection: ReturnType<typeof drizzle>,
  channelId: string,
  userId: string,
  allowedPermissions: bigint,
  deniedPermissions: bigint,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if overrides already exist for this user
    const existingOverrides = await db
      .select()
      .from(ChannelPermissionOverridesSchema)
      .where(
        and(
          eq(ChannelPermissionOverridesSchema.channelId, channelId),
          eq(ChannelPermissionOverridesSchema.userId, userId),
          isNull(ChannelPermissionOverridesSchema.roleId),
        ),
      )
      .limit(1);

    if (existingOverrides.length > 0) {
      // Update existing overrides
      await db
        .update(ChannelPermissionOverridesSchema)
        .set({
          allowedPermissions,
          deniedPermissions,
        })
        .where(
          eq(ChannelPermissionOverridesSchema.id, existingOverrides[0].id),
        );
    } else {
      // Create new overrides
      await db.insert(ChannelPermissionOverridesSchema).values({
        channelId,
        userId,
        allowedPermissions,
        deniedPermissions,
      });
    }

    return true;
  } catch (error) {
    console.error("Error setting user channel permission overrides:", error);
    return false;
  }
}

/**
 * Set permission overrides for a role in a channel
 *
 * @param dbConnection - The database connection to use
 * @param channelId - The ID of the channel
 * @param roleId - The ID of the role
 * @param allowedPermissions - Bitmask of permissions to explicitly allow
 * @param deniedPermissions - Bitmask of permissions to explicitly deny
 * @returns True if successful, false otherwise
 */
export async function setRoleChannelPermissionOverrides(
  dbConnection: ReturnType<typeof drizzle>,
  channelId: string,
  roleId: string,
  allowedPermissions: bigint,
  deniedPermissions: bigint,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if overrides already exist for this role
    const existingOverrides = await db
      .select()
      .from(ChannelPermissionOverridesSchema)
      .where(
        and(
          eq(ChannelPermissionOverridesSchema.channelId, channelId),
          eq(ChannelPermissionOverridesSchema.roleId, roleId),
          isNull(ChannelPermissionOverridesSchema.userId),
        ),
      )
      .limit(1);

    if (existingOverrides.length > 0) {
      // Update existing overrides
      await db
        .update(ChannelPermissionOverridesSchema)
        .set({
          allowedPermissions,
          deniedPermissions,
        })
        .where(
          eq(ChannelPermissionOverridesSchema.id, existingOverrides[0].id),
        );
    } else {
      // Create new overrides
      await db.insert(ChannelPermissionOverridesSchema).values({
        channelId,
        roleId,
        allowedPermissions,
        deniedPermissions,
      });
    }

    return true;
  } catch (error) {
    console.error("Error setting role channel permission overrides:", error);
    return false;
  }
}

/**
 * Set privacy settings for a category
 *
 * @param dbConnection - The database connection to use
 * @param categoryId - The ID of the category
 * @param isPrivate - Whether the category is private
 * @returns True if successful, false otherwise
 */
export async function setCategoryPrivacy(
  dbConnection: ReturnType<typeof drizzle>,
  categoryId: string,
  isPrivate: boolean,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if settings already exist for this category
    const existingSettings = await db
      .select()
      .from(CategoryPermissionSchema)
      .where(eq(CategoryPermissionSchema.categoryId, categoryId))
      .limit(1);

    if (existingSettings.length > 0) {
      // Update existing settings
      await db
        .update(CategoryPermissionSchema)
        .set({ isPrivate })
        .where(eq(CategoryPermissionSchema.id, existingSettings[0].id));
    } else {
      // Create new settings
      await db.insert(CategoryPermissionSchema).values({
        categoryId,
        isPrivate,
      });
    }

    return true;
  } catch (error) {
    console.error("Error setting category privacy:", error);
    return false;
  }
}

/**
 * Check if a user has access to a category
 *
 * @param dbConnection - The database connection to use
 * @param userId - The ID of the user
 * @param categoryId - The ID of the category
 * @param serverId - The ID of the server
 * @returns True if the user has access to the category, false otherwise
 */
export async function hasCategoryAccess(
  dbConnection: ReturnType<typeof drizzle>,
  userId: string,
  categoryId: string,
  serverId: string,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if the user is the server owner (owners have access to all categories)
    const server = await db
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    if (server.length > 0 && server[0].ownerId === userId) {
      return true;
    }

    // Check if the user has the ADMINISTRATOR permission
    const isAdmin = await hasServerPermission(
      db,
      userId,
      serverId,
      ADMINISTRATOR,
    );
    if (isAdmin) {
      return true;
    }

    // Check category privacy settings
    const categoryPermission = await db
      .select({ isPrivate: CategoryPermissionSchema.isPrivate })
      .from(CategoryPermissionSchema)
      .where(eq(CategoryPermissionSchema.categoryId, categoryId))
      .limit(1);

    // If the category is public or no privacy settings exist, the user has access
    if (categoryPermission.length === 0 || !categoryPermission[0].isPrivate) {
      return true;
    }

    // Check if the user is specifically allowed
    const userAllowed = await db
      .select()
      .from(CategoryAllowedUsersSchema)
      .where(
        and(
          eq(CategoryAllowedUsersSchema.categoryId, categoryId),
          eq(CategoryAllowedUsersSchema.userId, userId),
        ),
      )
      .limit(1);

    if (userAllowed.length > 0) {
      return true; // User is specifically allowed
    }

    // Get user's roles
    const userRoles = await db
      .select({ roleId: UserRoles.roleId })
      .from(UserRoles)
      .where(
        and(eq(UserRoles.userId, userId), eq(UserRoles.serverId, serverId)),
      );

    if (userRoles.length === 0) {
      return false; // User has no roles in this server
    }

    const roleIds = userRoles.map((role) => role.roleId);

    // Check if any of the user's roles are allowed in this category
    const allowedRoles = await db
      .select({ roleId: CategoryAllowedRolesSchema.roleId })
      .from(CategoryAllowedRolesSchema)
      .where(
        and(
          eq(CategoryAllowedRolesSchema.categoryId, categoryId),
          roleIds.length > 0
            ? or(
                ...roleIds.map((roleId) =>
                  eq(CategoryAllowedRolesSchema.roleId, roleId as string),
                ),
              )
            : eq(CategoryAllowedRolesSchema.roleId, ""),
        ),
      );

    return allowedRoles.length > 0;
  } catch (error) {
    console.error("Error checking category access:", error);
    return false;
  }
}

/**
 * Check if a user has a specific permission in a category
 *
 * @param dbConnection - The database connection to use
 * @param userId - The ID of the user
 * @param categoryId - The ID of the category
 * @param serverId - The ID of the server
 * @param requiredPermission - The permission to check for
 * @returns True if the user has the permission, false otherwise
 */
export async function hasCategoryPermission(
  dbConnection: ReturnType<typeof drizzle>,
  userId: string,
  categoryId: string,
  serverId: string,
  requiredPermission: bigint,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // First check if the user has access to the category
    const hasAccess = await hasCategoryAccess(db, userId, categoryId, serverId);
    if (!hasAccess) {
      return false;
    }

    // Check if the user is the server owner (owners have all permissions)
    const server = await db
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    if (server.length > 0 && server[0].ownerId === userId) {
      return true;
    }

    // Check for user-specific permission overrides
    const userOverrides = await db
      .select({
        allowed: CategoryPermissionOverridesSchema.allowedPermissions,
        denied: CategoryPermissionOverridesSchema.deniedPermissions,
      })
      .from(CategoryPermissionOverridesSchema)
      .where(
        and(
          eq(CategoryPermissionOverridesSchema.categoryId, categoryId),
          eq(CategoryPermissionOverridesSchema.userId, userId),
          isNull(CategoryPermissionOverridesSchema.roleId),
        ),
      )
      .limit(1);

    if (userOverrides.length > 0) {
      // Check if the permission is explicitly denied
      if (
        (userOverrides[0].denied & requiredPermission) ===
        requiredPermission
      ) {
        return false;
      }

      // Check if the permission is explicitly allowed
      if (
        (userOverrides[0].allowed & requiredPermission) ===
        requiredPermission
      ) {
        return true;
      }
    }

    // Get user's roles
    const userRoles = await db
      .select({ roleId: UserRoles.roleId })
      .from(UserRoles)
      .where(
        and(eq(UserRoles.userId, userId), eq(UserRoles.serverId, serverId)),
      );

    if (userRoles.length === 0) {
      return false; // User has no roles in this server
    }

    // Check for role-specific permission overrides
    let hasPermissionFromRoles = false;
    let isPermissionDeniedByAnyRole = false;

    for (const role of userRoles) {
      const roleOverrides = await db
        .select({
          allowed: CategoryPermissionOverridesSchema.allowedPermissions,
          denied: CategoryPermissionOverridesSchema.deniedPermissions,
        })
        .from(CategoryPermissionOverridesSchema)
        .where(
          and(
            eq(CategoryPermissionOverridesSchema.categoryId, categoryId),
            eq(CategoryPermissionOverridesSchema.roleId, role.roleId as string),
            isNull(CategoryPermissionOverridesSchema.userId),
          ),
        )
        .limit(1);

      if (roleOverrides.length > 0) {
        // Check if the permission is explicitly denied by this role
        if (
          (roleOverrides[0].denied & requiredPermission) ===
          requiredPermission
        ) {
          isPermissionDeniedByAnyRole = true;
        }

        // Check if the permission is explicitly allowed by this role
        if (
          (roleOverrides[0].allowed & requiredPermission) ===
          requiredPermission
        ) {
          hasPermissionFromRoles = true;
        }
      }
    }

    // If any role explicitly denies the permission, deny access
    if (isPermissionDeniedByAnyRole) {
      return false;
    }

    // If any role explicitly allows the permission, allow access
    if (hasPermissionFromRoles) {
      return true;
    }

    // If no overrides are found, fall back to server-level permissions
    return hasServerPermission(db, userId, serverId, requiredPermission);
  } catch (error) {
    console.error("Error checking category permission:", error);
    return false;
  }
}

/**
 * Add a user to the allowed users list for a category
 *
 * @param dbConnection - The database connection to use
 * @param categoryId - The ID of the category
 * @param userId - The ID of the user to allow
 * @returns True if successful, false otherwise
 */
export async function addAllowedUserToCategory(
  dbConnection: ReturnType<typeof drizzle>,
  categoryId: string,
  userId: string,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if the user is already allowed
    const existingAllowedUser = await db
      .select()
      .from(CategoryAllowedUsersSchema)
      .where(
        and(
          eq(CategoryAllowedUsersSchema.categoryId, categoryId),
          eq(CategoryAllowedUsersSchema.userId, userId),
        ),
      )
      .limit(1);

    if (existingAllowedUser.length > 0) {
      return true; // User is already allowed
    }

    // Add the user to the allowed users list
    await db.insert(CategoryAllowedUsersSchema).values({
      categoryId,
      userId,
    });

    return true;
  } catch (error) {
    console.error("Error adding allowed user to category:", error);
    return false;
  }
}

/**
 * Remove a user from the allowed users list for a category
 *
 * @param dbConnection - The database connection to use
 * @param categoryId - The ID of the category
 * @param userId - The ID of the user to remove
 * @returns True if successful, false otherwise
 */
export async function removeAllowedUserFromCategory(
  dbConnection: ReturnType<typeof drizzle>,
  categoryId: string,
  userId: string,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Remove the user from the allowed users list
    await db
      .delete(CategoryAllowedUsersSchema)
      .where(
        and(
          eq(CategoryAllowedUsersSchema.categoryId, categoryId),
          eq(CategoryAllowedUsersSchema.userId, userId),
        ),
      );

    return true;
  } catch (error) {
    console.error("Error removing allowed user from category:", error);
    return false;
  }
}

/**
 * Set permission overrides for a user in a category
 *
 * @param dbConnection - The database connection to use
 * @param categoryId - The ID of the category
 * @param userId - The ID of the user
 * @param allowedPermissions - Bitmask of permissions to explicitly allow
 * @param deniedPermissions - Bitmask of permissions to explicitly deny
 * @returns True if successful, false otherwise
 */
export async function setUserCategoryPermissionOverrides(
  dbConnection: ReturnType<typeof drizzle>,
  categoryId: string,
  userId: string,
  allowedPermissions: bigint,
  deniedPermissions: bigint,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if overrides already exist for this user
    const existingOverrides = await db
      .select()
      .from(CategoryPermissionOverridesSchema)
      .where(
        and(
          eq(CategoryPermissionOverridesSchema.categoryId, categoryId),
          eq(CategoryPermissionOverridesSchema.userId, userId),
          isNull(CategoryPermissionOverridesSchema.roleId),
        ),
      )
      .limit(1);

    if (existingOverrides.length > 0) {
      // Update existing overrides
      await db
        .update(CategoryPermissionOverridesSchema)
        .set({
          allowedPermissions,
          deniedPermissions,
        })
        .where(
          eq(CategoryPermissionOverridesSchema.id, existingOverrides[0].id),
        );
    } else {
      // Create new overrides
      await db.insert(CategoryPermissionOverridesSchema).values({
        categoryId,
        userId,
        allowedPermissions,
        deniedPermissions,
      });
    }

    return true;
  } catch (error) {
    console.error("Error setting user category permission overrides:", error);
    return false;
  }
}

/**
 * Set permission overrides for a role in a category
 *
 * @param dbConnection - The database connection to use
 * @param categoryId - The ID of the category
 * @param roleId - The ID of the role
 * @param allowedPermissions - Bitmask of permissions to explicitly allow
 * @param deniedPermissions - Bitmask of permissions to explicitly deny
 * @returns True if successful, false otherwise
 */
export async function setRoleCategoryPermissionOverrides(
  dbConnection: ReturnType<typeof drizzle>,
  categoryId: string,
  roleId: string,
  allowedPermissions: bigint,
  deniedPermissions: bigint,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if overrides already exist for this role
    const existingOverrides = await db
      .select()
      .from(CategoryPermissionOverridesSchema)
      .where(
        and(
          eq(CategoryPermissionOverridesSchema.categoryId, categoryId),
          eq(CategoryPermissionOverridesSchema.roleId, roleId),
          isNull(CategoryPermissionOverridesSchema.userId),
        ),
      )
      .limit(1);

    if (existingOverrides.length > 0) {
      // Update existing overrides
      await db
        .update(CategoryPermissionOverridesSchema)
        .set({
          allowedPermissions,
          deniedPermissions,
        })
        .where(
          eq(CategoryPermissionOverridesSchema.id, existingOverrides[0].id),
        );
    } else {
      // Create new overrides
      await db.insert(CategoryPermissionOverridesSchema).values({
        categoryId,
        roleId,
        allowedPermissions,
        deniedPermissions,
      });
    }

    return true;
  } catch (error) {
    console.error("Error setting role category permission overrides:", error);
    return false;
  }
}

/**
 * Check if a channel is visible to a user
 *
 * @param dbConnection - The database connection to use
 * @param userId - The ID of the user
 * @param channelId - The ID of the channel
 * @param serverId - The ID of the server
 * @returns True if the channel is visible to the user, false otherwise
 */
export async function isChannelVisibleToUser(
  dbConnection: ReturnType<typeof drizzle>,
  userId: string,
  channelId: string,
  serverId: string,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if the user is the server owner (owners can see all channels)
    const server = await db
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    if (server.length > 0 && server[0].ownerId === userId) {
      return true;
    }

    // Check if the user has the ADMINISTRATOR permission
    const isAdmin = await hasServerPermission(
      db,
      userId,
      serverId,
      ADMINISTRATOR,
    );
    if (isAdmin) {
      return true;
    }

    // Get channel privacy settings
    const privacySettings = await db
      .select({ isVisible: ChannelPrivacySchema.isVisible })
      .from(ChannelPrivacySchema)
      .where(eq(ChannelPrivacySchema.channelId, channelId))
      .limit(1);

    // If no privacy settings exist or the channel is set to visible for everyone, the user can see it
    if (privacySettings.length === 0 || privacySettings[0].isVisible) {
      return true;
    }

    // Check if the user is specifically allowed to see the channel
    const userVisible = await db
      .select()
      .from(ChannelVisibleUsersSchema)
      .where(
        and(
          eq(ChannelVisibleUsersSchema.channelId, channelId),
          eq(ChannelVisibleUsersSchema.userId, userId),
        ),
      )
      .limit(1);

    if (userVisible.length > 0) {
      return true; // User is specifically allowed to see the channel
    }

    // Get user's roles
    const userRoles = await db
      .select({ roleId: UserRoles.roleId })
      .from(UserRoles)
      .where(
        and(eq(UserRoles.userId, userId), eq(UserRoles.serverId, serverId)),
      );

    if (userRoles.length === 0) {
      return false; // User has no roles in this server
    }

    const roleIds = userRoles.map((role) => role.roleId);

    // Check if any of the user's roles are allowed to see the channel
    const visibleRoles = await db
      .select({ roleId: ChannelVisibleRolesSchema.roleId })
      .from(ChannelVisibleRolesSchema)
      .where(
        and(
          eq(ChannelVisibleRolesSchema.channelId, channelId),
          roleIds.length > 0
            ? or(
                ...roleIds.map((roleId) =>
                  eq(ChannelVisibleRolesSchema.roleId, roleId as string),
                ),
              )
            : eq(ChannelVisibleRolesSchema.roleId, ""),
        ),
      );

    return visibleRoles.length > 0;
  } catch (error) {
    console.error("Error checking channel visibility:", error);
    return false;
  }
}

/**
 * Check if a category is visible to a user
 *
 * @param dbConnection - The database connection to use
 * @param userId - The ID of the user
 * @param categoryId - The ID of the category
 * @param serverId - The ID of the server
 * @returns True if the category is visible to the user, false otherwise
 */
export async function isCategoryVisibleToUser(
  dbConnection: ReturnType<typeof drizzle>,
  userId: string,
  categoryId: string,
  serverId: string,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if the user is the server owner (owners can see all categories)
    const server = await db
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    if (server.length > 0 && server[0].ownerId === userId) {
      return true;
    }

    // Check if the user has the ADMINISTRATOR permission
    const isAdmin = await hasServerPermission(
      db,
      userId,
      serverId,
      ADMINISTRATOR,
    );
    if (isAdmin) {
      return true;
    }

    // Get category privacy settings
    const privacySettings = await db
      .select({ isVisible: CategoryPermissionSchema.isVisible })
      .from(CategoryPermissionSchema)
      .where(eq(CategoryPermissionSchema.categoryId, categoryId))
      .limit(1);

    // If no privacy settings exist or the category is set to visible for everyone, the user can see it
    if (privacySettings.length === 0 || privacySettings[0].isVisible) {
      return true;
    }

    // Check if the user is specifically allowed to see the category
    const userVisible = await db
      .select()
      .from(CategoryVisibleUsersSchema)
      .where(
        and(
          eq(CategoryVisibleUsersSchema.categoryId, categoryId),
          eq(CategoryVisibleUsersSchema.userId, userId),
        ),
      )
      .limit(1);

    if (userVisible.length > 0) {
      return true; // User is specifically allowed to see the category
    }

    // Get user's roles
    const userRoles = await db
      .select({ roleId: UserRoles.roleId })
      .from(UserRoles)
      .where(
        and(eq(UserRoles.userId, userId), eq(UserRoles.serverId, serverId)),
      );

    if (userRoles.length === 0) {
      return false; // User has no roles in this server
    }

    const roleIds = userRoles.map((role) => role.roleId);

    // Check if any of the user's roles are allowed to see the category
    const visibleRoles = await db
      .select({ roleId: CategoryVisibleRolesSchema.roleId })
      .from(CategoryVisibleRolesSchema)
      .where(
        and(
          eq(CategoryVisibleRolesSchema.categoryId, categoryId),
          roleIds.length > 0
            ? or(
                ...roleIds.map((roleId) =>
                  eq(CategoryVisibleRolesSchema.roleId, roleId as string),
                ),
              )
            : eq(CategoryVisibleRolesSchema.roleId, ""),
        ),
      );

    return visibleRoles.length > 0;
  } catch (error) {
    console.error("Error checking category visibility:", error);
    return false;
  }
}

/**
 * Add a user to the visible users list for a channel
 *
 * @param dbConnection - The database connection to use
 * @param channelId - The ID of the channel
 * @param userId - The ID of the user to make visible
 * @returns True if successful, false otherwise
 */
export async function addVisibleUserToChannel(
  dbConnection: ReturnType<typeof drizzle>,
  channelId: string,
  userId: string,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if the user is already in the visible list
    const existingVisibleUser = await db
      .select()
      .from(ChannelVisibleUsersSchema)
      .where(
        and(
          eq(ChannelVisibleUsersSchema.channelId, channelId),
          eq(ChannelVisibleUsersSchema.userId, userId),
        ),
      )
      .limit(1);

    if (existingVisibleUser.length > 0) {
      return true; // User is already in the visible list
    }

    // Add the user to the visible users list
    await db.insert(ChannelVisibleUsersSchema).values({
      channelId,
      userId,
    });

    return true;
  } catch (error) {
    console.error("Error adding visible user to channel:", error);
    return false;
  }
}

/**
 * Add a user to the visible users list for a category
 *
 * @param dbConnection - The database connection to use
 * @param categoryId - The ID of the category
 * @param userId - The ID of the user to make visible
 * @returns True if successful, false otherwise
 */
export async function addVisibleUserToCategory(
  dbConnection: ReturnType<typeof drizzle>,
  categoryId: string,
  userId: string,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if the user is already in the visible list
    const existingVisibleUser = await db
      .select()
      .from(CategoryVisibleUsersSchema)
      .where(
        and(
          eq(CategoryVisibleUsersSchema.categoryId, categoryId),
          eq(CategoryVisibleUsersSchema.userId, userId),
        ),
      )
      .limit(1);

    if (existingVisibleUser.length > 0) {
      return true; // User is already in the visible list
    }

    // Add the user to the visible users list
    await db.insert(CategoryVisibleUsersSchema).values({
      categoryId,
      userId,
    });

    return true;
  } catch (error) {
    console.error("Error adding visible user to category:", error);
    return false;
  }
}

/**
 * Add a role to the visible roles list for a channel
 *
 * @param dbConnection - The database connection to use
 * @param channelId - The ID of the channel
 * @param roleId - The ID of the role to make visible
 * @returns True if successful, false otherwise
 */
export async function addVisibleRoleToChannel(
  dbConnection: ReturnType<typeof drizzle>,
  channelId: string,
  roleId: string,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if the role is already in the visible list
    const existingVisibleRole = await db
      .select()
      .from(ChannelVisibleRolesSchema)
      .where(
        and(
          eq(ChannelVisibleRolesSchema.channelId, channelId),
          eq(ChannelVisibleRolesSchema.roleId, roleId),
        ),
      )
      .limit(1);

    if (existingVisibleRole.length > 0) {
      return true; // Role is already in the visible list
    }

    // Add the role to the visible roles list
    await db.insert(ChannelVisibleRolesSchema).values({
      channelId,
      roleId,
    });

    return true;
  } catch (error) {
    console.error("Error adding visible role to channel:", error);
    return false;
  }
}

/**
 * Add a role to the visible roles list for a category
 *
 * @param dbConnection - The database connection to use
 * @param categoryId - The ID of the category
 * @param roleId - The ID of the role to make visible
 * @returns True if successful, false otherwise
 */
export async function addVisibleRoleToCategory(
  dbConnection: ReturnType<typeof drizzle>,
  categoryId: string,
  roleId: string,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;

    // Check if the role is already in the visible list
    const existingVisibleRole = await db
      .select()
      .from(CategoryVisibleRolesSchema)
      .where(
        and(
          eq(CategoryVisibleRolesSchema.categoryId, categoryId),
          eq(CategoryVisibleRolesSchema.roleId, roleId),
        ),
      )
      .limit(1);

    if (existingVisibleRole.length > 0) {
      return true; // Role is already in the visible list
    }

    // Add the role to the visible roles list
    await db.insert(CategoryVisibleRolesSchema).values({
      categoryId,
      roleId,
    });

    return true;
  } catch (error) {
    console.error("Error adding visible role to category:", error);
    return false;
  }
}
