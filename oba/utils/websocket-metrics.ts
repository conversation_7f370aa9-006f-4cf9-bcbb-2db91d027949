import { nanoid } from "nanoid";
import { logger } from "../services/logger.service";
import type { ServerWebSocket } from "bun";
import type { CustomWebSocketData } from "../types/websocket.types";
import type { IWebSocketMessage } from "../types/websocket-standardization.types";

/**
 * Interface for detailed performance metrics
 */
interface IDetailedMetrics {
  // Message metrics
  messageCount: number;
  messageCountByType: Record<string, number>;
  messageCountByUser: Record<string, number>;
  messageCountByDirection: { inbound: number; outbound: number };
  
  // Performance metrics
  averageLatency: number;
  latencyPercentiles: { p50: number; p90: number; p95: number; p99: number };
  maxLatency: number;
  minLatency: number;
  
  // Error metrics
  errorCount: number;
  errorCountByType: Record<string, number>;
  errorCountByUser: Record<string, number>;
  errorRate: number;
  
  // Connection metrics
  activeConnections: number;
  connectionsByType: Record<string, number>;
  connectionsByServer: Record<string, number>;
  totalConnectionsCreated: number;
  totalConnectionsClosed: number;
  
  // Throughput metrics
  messagesPerSecond: number;
  bytesPerSecond: number;
  totalBytesProcessed: number;
  
  // Rate limiting metrics
  rateLimitHits: number;
  rateLimitHitsByUser: Record<string, number>;
  
  // Validation metrics
  validationFailures: number;
  validationFailuresByType: Record<string, number>;
  
  // Correlation metrics
  correlationTimeouts: number;
  averageCorrelationTime: number;
  activeCorrelations: number;
  
  // System metrics
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage?: NodeJS.CpuUsage;
  uptime: number;
  
  // Timestamps
  startTime: number;
  lastUpdated: number;
  measurementWindow: number; // in milliseconds
}

/**
 * Interface for metric data point
 */
interface IMetricDataPoint {
  timestamp: number;
  value: number;
  metadata?: Record<string, any>;
}

/**
 * Interface for time series data
 */
interface ITimeSeries {
  name: string;
  dataPoints: IMetricDataPoint[];
  maxDataPoints: number;
}

/**
 * Interface for alert configuration
 */
interface IMetricAlert {
  id: string;
  name: string;
  metric: string;
  threshold: number;
  operator: ">" | "<" | ">=" | "<=" | "==" | "!=";
  windowMs: number;
  enabled: boolean;
  callback?: (alert: IMetricAlert, value: number) => void;
}

/**
 * Interface for metric aggregation
 */
interface IMetricAggregation {
  sum: number;
  average: number;
  min: number;
  max: number;
  count: number;
  standardDeviation: number;
}

/**
 * WebSocket Metrics Collector - Advanced performance monitoring and metrics collection
 */
export class WebSocketMetricsCollector {
  private logger = logger.createLogger("WebSocketMetricsCollector");
  private metrics: IDetailedMetrics;
  private timeSeries: Map<string, ITimeSeries> = new Map();
  private latencyBuffer: number[] = [];
  private correlationTimes: number[] = [];
  private alerts: Map<string, IMetricAlert> = new Map();
  private metricsHistory: IDetailedMetrics[] = [];
  
  private readonly MAX_LATENCY_BUFFER_SIZE = 10000;
  private readonly MAX_CORRELATION_BUFFER_SIZE = 5000;
  private readonly MAX_HISTORY_SIZE = 1440; // 24 hours of minute-by-minute data
  private readonly DEFAULT_TIME_SERIES_SIZE = 1000;
  
  private metricsInterval?: Timer;
  private alertCheckInterval?: Timer;
  private cpuUsageStart?: NodeJS.CpuUsage;

  constructor() {
    this.metrics = this.initializeMetrics();
    this.startMetricsCollection();
    this.startAlertChecking();
  }

  /**
   * Record a message being processed
   */
  public recordMessage(
    messageType: string | number,
    userId: string,
    direction: "inbound" | "outbound",
    size: number,
    latency?: number,
  ): void {
    // Update basic counters
    this.metrics.messageCount++;
    this.metrics.messageCountByDirection[direction]++;
    this.metrics.totalBytesProcessed += size;

    // Update by-type counters
    const typeKey = String(messageType);
    this.metrics.messageCountByType[typeKey] = 
      (this.metrics.messageCountByType[typeKey] || 0) + 1;

    // Update by-user counters
    this.metrics.messageCountByUser[userId] = 
      (this.metrics.messageCountByUser[userId] || 0) + 1;

    // Record latency if provided
    if (latency !== undefined) {
      this.recordLatency(latency);
    }

    // Update time series
    this.addTimeSeriesPoint("messages_per_second", 1);
    this.addTimeSeriesPoint("bytes_per_second", size);
  }

  /**
   * Record an error occurrence
   */
  public recordError(
    errorType: string,
    userId: string,
    messageType?: string | number,
  ): void {
    this.metrics.errorCount++;
    
    // Update by-type counters
    this.metrics.errorCountByType[errorType] = 
      (this.metrics.errorCountByType[errorType] || 0) + 1;

    // Update by-user counters
    this.metrics.errorCountByUser[userId] = 
      (this.metrics.errorCountByUser[userId] || 0) + 1;

    // Update error rate
    this.metrics.errorRate = this.metrics.errorCount / Math.max(this.metrics.messageCount, 1);

    // Update time series
    this.addTimeSeriesPoint("errors_per_second", 1);
    this.addTimeSeriesPoint("error_rate", this.metrics.errorRate);
  }

  /**
   * Record connection event
   */
  public recordConnection(
    event: "connect" | "disconnect",
    connectionType: string,
    serverId?: string,
  ): void {
    if (event === "connect") {
      this.metrics.activeConnections++;
      this.metrics.totalConnectionsCreated++;
    } else {
      this.metrics.activeConnections = Math.max(0, this.metrics.activeConnections - 1);
      this.metrics.totalConnectionsClosed++;
    }

    // Update by-type counters
    if (event === "connect") {
      this.metrics.connectionsByType[connectionType] = 
        (this.metrics.connectionsByType[connectionType] || 0) + 1;
    } else {
      this.metrics.connectionsByType[connectionType] = 
        Math.max(0, (this.metrics.connectionsByType[connectionType] || 0) - 1);
    }

    // Update by-server counters
    if (serverId) {
      if (event === "connect") {
        this.metrics.connectionsByServer[serverId] = 
          (this.metrics.connectionsByServer[serverId] || 0) + 1;
      } else {
        this.metrics.connectionsByServer[serverId] = 
          Math.max(0, (this.metrics.connectionsByServer[serverId] || 0) - 1);
      }
    }

    // Update time series
    this.addTimeSeriesPoint("active_connections", this.metrics.activeConnections);
  }

  /**
   * Record rate limit hit
   */
  public recordRateLimit(userId: string): void {
    this.metrics.rateLimitHits++;
    this.metrics.rateLimitHitsByUser[userId] = 
      (this.metrics.rateLimitHitsByUser[userId] || 0) + 1;

    // Update time series
    this.addTimeSeriesPoint("rate_limit_hits", 1);
  }

  /**
   * Record validation failure
   */
  public recordValidationFailure(messageType: string | number): void {
    this.metrics.validationFailures++;
    const typeKey = String(messageType);
    this.metrics.validationFailuresByType[typeKey] = 
      (this.metrics.validationFailuresByType[typeKey] || 0) + 1;

    // Update time series
    this.addTimeSeriesPoint("validation_failures", 1);
  }

  /**
   * Record correlation timeout
   */
  public recordCorrelationTimeout(): void {
    this.metrics.correlationTimeouts++;
    
    // Update time series
    this.addTimeSeriesPoint("correlation_timeouts", 1);
  }

  /**
   * Record correlation completion
   */
  public recordCorrelationCompletion(duration: number): void {
    this.correlationTimes.push(duration);

    // Limit buffer size
    if (this.correlationTimes.length > this.MAX_CORRELATION_BUFFER_SIZE) {
      this.correlationTimes.shift();
    }

    // Update average
    this.metrics.averageCorrelationTime = 
      this.correlationTimes.reduce((sum, time) => sum + time, 0) / this.correlationTimes.length;
  }

  /**
   * Update active correlations count
   */
  public updateActiveCorrelations(count: number): void {
    this.metrics.activeCorrelations = count;
    
    // Update time series
    this.addTimeSeriesPoint("active_correlations", count);
  }

  /**
   * Get current metrics snapshot
   */
  public getMetrics(): IDetailedMetrics {
    // Update system metrics
    this.updateSystemMetrics();
    
    return { ...this.metrics };
  }

  /**
   * Get time series data for a specific metric
   */
  public getTimeSeries(metricName: string): ITimeSeries | undefined {
    return this.timeSeries.get(metricName);
  }

  /**
   * Get all available time series
   */
  public getAllTimeSeries(): Map<string, ITimeSeries> {
    return new Map(this.timeSeries);
  }

  /**
   * Get metric aggregation for a time window
   */
  public getMetricAggregation(
    metricName: string,
    windowMs: number,
  ): IMetricAggregation | null {
    const timeSeries = this.timeSeries.get(metricName);
    if (!timeSeries) {
      return null;
    }

    const cutoff = Date.now() - windowMs;
    const relevantPoints = timeSeries.dataPoints.filter(
      (point) => point.timestamp >= cutoff,
    );

    if (relevantPoints.length === 0) {
      return null;
    }

    const values = relevantPoints.map((point) => point.value);
    const sum = values.reduce((a, b) => a + b, 0);
    const average = sum / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    // Calculate standard deviation
    const variance = values.reduce((acc, val) => acc + Math.pow(val - average, 2), 0) / values.length;
    const standardDeviation = Math.sqrt(variance);

    return {
      sum,
      average,
      min,
      max,
      count: values.length,
      standardDeviation,
    };
  }

  /**
   * Get metrics history
   */
  public getMetricsHistory(limit?: number): IDetailedMetrics[] {
    const history = [...this.metricsHistory];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Add metric alert
   */
  public addAlert(alert: IMetricAlert): void {
    this.alerts.set(alert.id, alert);
    this.logger.info("Metric alert added", undefined, {
      alertId: alert.id,
      metric: alert.metric,
      threshold: alert.threshold,
    });
  }

  /**
   * Remove metric alert
   */
  public removeAlert(alertId: string): boolean {
    const removed = this.alerts.delete(alertId);
    if (removed) {
      this.logger.info("Metric alert removed", undefined, { alertId });
    }
    return removed;
  }

  /**
   * Get all alerts
   */
  public getAlerts(): IMetricAlert[] {
    return Array.from(this.alerts.values());
  }

  /**
   * Reset metrics (useful for testing or periodic resets)
   */
  public resetMetrics(): void {
    this.metrics = this.initializeMetrics();
    this.latencyBuffer = [];
    this.correlationTimes = [];
    this.timeSeries.clear();
    this.metricsHistory = [];

    this.logger.info("Metrics reset");
  }

  /**
   * Export metrics data
   */
  public exportMetrics(format: "json" | "csv" = "json"): string {
    const data = {
      currentMetrics: this.metrics,
      timeSeries: Object.fromEntries(this.timeSeries),
      history: this.metricsHistory,
      alerts: Array.from(this.alerts.values()),
      exportedAt: new Date().toISOString(),
    };

    if (format === "json") {
      return JSON.stringify(data, null, 2);
    } else {
      // Simple CSV export of current metrics
      const headers = [
        "metric",
        "value",
        "timestamp",
      ];

      const rows = [
        ["messageCount", this.metrics.messageCount, this.metrics.lastUpdated],
        ["errorCount", this.metrics.errorCount, this.metrics.lastUpdated],
        ["activeConnections", this.metrics.activeConnections, this.metrics.lastUpdated],
        ["averageLatency", this.metrics.averageLatency, this.metrics.lastUpdated],
        ["errorRate", this.metrics.errorRate, this.metrics.lastUpdated],
        ["rateLimitHits", this.metrics.rateLimitHits, this.metrics.lastUpdated],
      ];

      const csvRows = [
        headers.join(","),
        ...rows.map((row) => row.join(",")),
      ];

      return csvRows.join("\n");
    }
  }

  /**
   * Cleanup old data
   */
  public cleanup(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAge;

    // Clean up time series data
    for (const [name, series] of this.timeSeries.entries()) {
      const filteredPoints = series.dataPoints.filter(
        (point) => point.timestamp > cutoff,
      );
      
      if (filteredPoints.length !== series.dataPoints.length) {
        series.dataPoints = filteredPoints;
        this.timeSeries.set(name, series);
      }
    }

    // Clean up metrics history
    this.metricsHistory = this.metricsHistory.filter(
      (metrics) => metrics.lastUpdated > cutoff,
    );

    this.logger.info("Metrics cleanup completed");
  }

  /**
   * Destroy metrics collector
   */
  public destroy(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }
    
    if (this.alertCheckInterval) {
      clearInterval(this.alertCheckInterval);
    }

    this.timeSeries.clear();
    this.alerts.clear();
    this.metricsHistory = [];

    this.logger.info("Metrics collector destroyed");
  }

  /**
   * Initialize metrics structure
   */
  private initializeMetrics(): IDetailedMetrics {
    return {
      messageCount: 0,
      messageCountByType: {},
      messageCountByUser: {},
      messageCountByDirection: { inbound: 0, outbound: 0 },
      
      averageLatency: 0,
      latencyPercentiles: { p50: 0, p90: 0, p95: 0, p99: 0 },
      maxLatency: 0,
      minLatency: Number.MAX_SAFE_INTEGER,
      
      errorCount: 0,
      errorCountByType: {},
      errorCountByUser: {},
      errorRate: 0,
      
      activeConnections: 0,
      connectionsByType: {},
      connectionsByServer: {},
      totalConnectionsCreated: 0,
      totalConnectionsClosed: 0,
      
      messagesPerSecond: 0,
      bytesPerSecond: 0,
      totalBytesProcessed: 0,
      
      rateLimitHits: 0,
      rateLimitHitsByUser: {},
      
      validationFailures: 0,
      validationFailuresByType: {},
      
      correlationTimeouts: 0,
      averageCorrelationTime: 0,
      activeCorrelations: 0,
      
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime(),
      
      startTime: Date.now(),
      lastUpdated: Date.now(),
      measurementWindow: 60000, // 1 minute
    };
  }

  /**
   * Record latency measurement
   */
  private recordLatency(latency: number): void {
    this.latencyBuffer.push(latency);

    // Limit buffer size
    if (this.latencyBuffer.length > this.MAX_LATENCY_BUFFER_SIZE) {
      this.latencyBuffer.shift();
    }

    // Update min/max
    this.metrics.maxLatency = Math.max(this.metrics.maxLatency, latency);
    this.metrics.minLatency = Math.min(this.metrics.minLatency, latency);

    // Update average
    this.metrics.averageLatency = 
      this.latencyBuffer.reduce((sum, lat) => sum + lat, 0) / this.latencyBuffer.length;

    // Update percentiles
    this.updateLatencyPercentiles();

    // Update time series
    this.addTimeSeriesPoint("average_latency", this.metrics.averageLatency);
  }

  /**
   * Update latency percentiles
   */
  private updateLatencyPercentiles(): void {
    if (this.latencyBuffer.length === 0) {
      return;
    }

    const sorted = [...this.latencyBuffer].sort((a, b) => a - b);
    const len = sorted.length;

    this.metrics.latencyPercentiles = {
      p50: sorted[Math.floor(len * 0.5)],
      p90: sorted[Math.floor(len * 0.9)],
      p95: sorted[Math.floor(len * 0.95)],
      p99: sorted[Math.floor(len * 0.99)],
    };
  }

  /**
   * Add data point to time series
   */
  private addTimeSeriesPoint(metricName: string, value: number): void {
    let series = this.timeSeries.get(metricName);
    
    if (!series) {
      series = {
        name: metricName,
        dataPoints: [],
        maxDataPoints: this.DEFAULT_TIME_SERIES_SIZE,
      };
      this.timeSeries.set(metricName, series);
    }

    series.dataPoints.push({
      timestamp: Date.now(),
      value,
    });

    // Limit data points
    if (series.dataPoints.length > series.maxDataPoints) {
      series.dataPoints.shift();
    }
  }

  /**
   * Update system metrics
   */
  private updateSystemMetrics(): void {
    this.metrics.memoryUsage = process.memoryUsage();
    this.metrics.uptime = process.uptime();
    this.metrics.lastUpdated = Date.now();

    // Update CPU usage if available
    if (this.cpuUsageStart) {
      this.metrics.cpuUsage = process.cpuUsage(this.cpuUsageStart);
    }
    this.cpuUsageStart = process.cpuUsage();
  }

  /**
   * Start metrics collection interval
   */
  private startMetricsCollection(): void {
    this.metricsInterval = setInterval(() => {
      // Calculate rates
      const now = Date.now();
      const windowMs = now - this.metrics.lastUpdated;
      
      if (windowMs > 0) {
        // Get recent message counts from time series
        const messagesTimeSeries = this.timeSeries.get("messages_per_second");
        const bytesTimeSeries = this.timeSeries.get("bytes_per_second");
        
        if (messagesTimeSeries) {
          const recentMessages = messagesTimeSeries.dataPoints
            .filter((point) => point.timestamp > now - 60000) // Last minute
            .reduce((sum, point) => sum + point.value, 0);
          this.metrics.messagesPerSecond = recentMessages / 60;
        }
        
        if (bytesTimeSeries) {
          const recentBytes = bytesTimeSeries.dataPoints
            .filter((point) => point.timestamp > now - 60000) // Last minute
            .reduce((sum, point) => sum + point.value, 0);
          this.metrics.bytesPerSecond = recentBytes / 60;
        }
      }

      // Update system metrics
      this.updateSystemMetrics();

      // Save to history
      this.metricsHistory.push({ ...this.metrics });
      
      // Limit history size
      if (this.metricsHistory.length > this.MAX_HISTORY_SIZE) {
        this.metricsHistory.shift();
      }

      // Log periodic summary
      this.logger.debug("Metrics updated", undefined, {
        messageCount: this.metrics.messageCount,
        errorCount: this.metrics.errorCount,
        activeConnections: this.metrics.activeConnections,
        averageLatency: Math.round(this.metrics.averageLatency),
        messagesPerSecond: Math.round(this.metrics.messagesPerSecond),
      });
    }, 60000); // Update every minute
  }

  /**
   * Start alert checking interval
   */
  private startAlertChecking(): void {
    this.alertCheckInterval = setInterval(() => {
      this.checkAlerts();
    }, 10000); // Check alerts every 10 seconds
  }

  /**
   * Check all alerts against current metrics
   */
  private checkAlerts(): void {
    for (const alert of this.alerts.values()) {
      if (!alert.enabled) {
        continue;
      }

      const metricValue = this.getMetricValue(alert.metric);
      if (metricValue === null) {
        continue;
      }

      const triggered = this.evaluateAlertCondition(
        metricValue,
        alert.threshold,
        alert.operator,
      );

      if (triggered) {
        this.logger.warn("Metric alert triggered", undefined, {
          alertId: alert.id,
          alertName: alert.name,
          metric: alert.metric,
          value: metricValue,
          threshold: alert.threshold,
          operator: alert.operator,
        });

        if (alert.callback) {
          try {
            alert.callback(alert, metricValue);
          } catch (error) {
            this.logger.error("Error in alert callback", undefined, {
              alertId: alert.id,
              error,
            });
          }
        }
      }
    }
  }

  /**
   * Get metric value by name
   */
  private getMetricValue(metricName: string): number | null {
    switch (metricName) {
      case "messageCount":
        return this.metrics.messageCount;
      case "errorCount":
        return this.metrics.errorCount;
      case "errorRate":
        return this.metrics.errorRate;
      case "activeConnections":
        return this.metrics.activeConnections;
      case "averageLatency":
        return this.metrics.averageLatency;
      case "messagesPerSecond":
        return this.metrics.messagesPerSecond;
      case "rateLimitHits":
        return this.metrics.rateLimitHits;
      case "validationFailures":
        return this.metrics.validationFailures;
      case "correlationTimeouts":
        return this.metrics.correlationTimeouts;
      case "memoryUsageHeap":
        return this.metrics.memoryUsage.heapUsed;
      case "memoryUsageRSS":
        return this.metrics.memoryUsage.rss;
      default:
        return null;
    }
  }

  /**
   * Evaluate alert condition
   */
  private evaluateAlertCondition(
    value: number,
    threshold: number,
    operator: string,
  ): boolean {
    switch (operator) {
      case ">":
        return value > threshold;
      case "<":
        return value < threshold;
      case ">=":
        return value >= threshold;
      case "<=":
        return value <= threshold;
      case "==":
        return value === threshold;
      case "!=":
        return value !== threshold;
      default:
        return false;
    }
  }
}

// Export singleton instance
export const webSocketMetrics = new WebSocketMetricsCollector();

// Export types for external use
export type {
  IDetailedMetrics,
  IMetricDataPoint,
  ITimeSeries,
  IMetricAlert,
  IMetricAggregation,
};