import type { ServerWebSocket } from "bun";
import { nanoid } from "nanoid";
import { logger } from "../services/logger.service";
import type { CustomWebSocketData } from "../types/websocket.types";
import type { IWebSocketMessage } from "../types/websocket-standardization.types";
import { webSocketLogger } from "./websocket-logger";

/**
 * Interface for debug session configuration
 */
interface IDebugSessionConfig {
  userId?: string;
  messageTypes?: (string | number)[];
  includePayload?: boolean;
  includeMetadata?: boolean;
  maxMessages?: number;
  duration?: number; // in milliseconds
  logLevel?: "debug" | "info" | "warn" | "error";
}

/**
 * Interface for debug message capture
 */
interface IDebugMessage {
  id: string;
  timestamp: number;
  correlationId?: string;
  userId?: string;
  direction: "inbound" | "outbound";
  messageType: string | number;
  messageId?: string;
  payload?: any;
  metadata?: Record<string, any>;
  size: number;
  processingTime?: number;
}

/**
 * Interface for connection debug info
 */
interface IConnectionDebugInfo {
  userId?: string;
  sessionId?: string;
  serverId?: string;
  channelId?: string;
  type?: string;
  readyState: number;
  connectedAt: number;
  lastActivity: number;
  messageCount: number;
  errorCount: number;
  subscriptions: string[];
  rateLimitStatus: {
    tokens: number;
    lastRefill: number;
    blocked: boolean;
  };
}

/**
 * Interface for system debug snapshot
 */
interface ISystemDebugSnapshot {
  timestamp: number;
  connections: {
    total: number;
    byType: Record<string, number>;
    byServer: Record<string, number>;
  };
  messages: {
    totalProcessed: number;
    byType: Record<string, number>;
    errorRate: number;
    averageLatency: number;
  };
  performance: {
    memoryUsage: NodeJS.MemoryUsage;
    uptime: number;
    cpuUsage?: NodeJS.CpuUsage;
  };
  subscriptions: {
    totalTopics: number;
    totalSubscriptions: number;
    topTopics: Array<{ topic: string; subscribers: number }>;
  };
}

/**
 * WebSocket Debug Utilities - Advanced debugging and monitoring tools
 */
export class WebSocketDebugger {
  private logger = logger.createLogger("WebSocketDebugger");
  private debugSessions: Map<string, IDebugSessionConfig> = new Map();
  private capturedMessages: Map<string, IDebugMessage[]> = new Map();
  private connectionDebugInfo: WeakMap<
    ServerWebSocket<CustomWebSocketData>,
    IConnectionDebugInfo
  > = new WeakMap();
  private messageInterceptors: Map<
    string,
    (message: IDebugMessage) => void
  > = new Map();
  private performanceBaseline?: ISystemDebugSnapshot;

  /**
   * Start a debug session for specific criteria
   */
  public startDebugSession(
    sessionId: string,
    config: IDebugSessionConfig,
  ): void {
    this.debugSessions.set(sessionId, config);
    this.capturedMessages.set(sessionId, []);

    this.logger.info("Debug session started", undefined, {
      sessionId,
      config,
    });

    // Auto-stop session after duration if specified
    if (config.duration) {
      setTimeout(() => {
        this.stopDebugSession(sessionId);
      }, config.duration);
    }
  }

  /**
   * Stop a debug session
   */
  public stopDebugSession(sessionId: string): IDebugMessage[] {
    const messages = this.capturedMessages.get(sessionId) || [];
    
    this.debugSessions.delete(sessionId);
    this.capturedMessages.delete(sessionId);

    this.logger.info("Debug session stopped", undefined, {
      sessionId,
      capturedMessages: messages.length,
    });

    return messages;
  }

  /**
   * Capture a message for active debug sessions
   */
  public captureMessage(
    ws: ServerWebSocket<CustomWebSocketData>,
    message: any,
    direction: "inbound" | "outbound",
    correlationId?: string,
    processingTime?: number,
  ): void {
    const debugMessage: IDebugMessage = {
      id: nanoid(),
      timestamp: Date.now(),
      correlationId,
      userId: ws.data.userId,
      direction,
      messageType: message.type,
      messageId: message.meta?.messageId,
      size: JSON.stringify(message).length,
      processingTime,
    };

    // Check each active debug session
    for (const [sessionId, config] of this.debugSessions.entries()) {
      if (this.shouldCaptureMessage(debugMessage, config)) {
        const sessionMessages = this.capturedMessages.get(sessionId) || [];

        // Add payload and metadata if configured
        if (config.includePayload) {
          debugMessage.payload = this.sanitizeDebugData(message);
        }

        if (config.includeMetadata) {
          debugMessage.metadata = {
            serverId: ws.data.serverId,
            channelId: ws.data.channelId,
            sessionId: ws.data.sessionId,
            readyState: ws.readyState,
          };
        }

        sessionMessages.push(debugMessage);

        // Limit message count per session
        if (config.maxMessages && sessionMessages.length > config.maxMessages) {
          sessionMessages.shift(); // Remove oldest message
        }

        this.capturedMessages.set(sessionId, sessionMessages);

        // Trigger interceptors
        const interceptor = this.messageInterceptors.get(sessionId);
        if (interceptor) {
          try {
            interceptor(debugMessage);
          } catch (error) {
            this.logger.error("Error in message interceptor", undefined, {
              sessionId,
              error,
            });
          }
        }
      }
    }
  }

  /**
   * Get connection debug information
   */
  public getConnectionDebugInfo(
    ws: ServerWebSocket<CustomWebSocketData>,
  ): IConnectionDebugInfo {
    let debugInfo = this.connectionDebugInfo.get(ws);

    if (!debugInfo) {
      debugInfo = {
        userId: ws.data.userId,
        sessionId: ws.data.sessionId,
        serverId: ws.data.serverId,
        channelId: ws.data.channelId,
        type: ws.data.type,
        readyState: ws.readyState,
        connectedAt: Date.now(),
        lastActivity: Date.now(),
        messageCount: 0,
        errorCount: 0,
        subscriptions: [],
        rateLimitStatus: {
          tokens: 0,
          lastRefill: Date.now(),
          blocked: false,
        },
      };

      this.connectionDebugInfo.set(ws, debugInfo);
    }

    return debugInfo;
  }

  /**
   * Update connection debug info
   */
  public updateConnectionDebugInfo(
    ws: ServerWebSocket<CustomWebSocketData>,
    updates: Partial<IConnectionDebugInfo>,
  ): void {
    const debugInfo = this.getConnectionDebugInfo(ws);
    Object.assign(debugInfo, updates);
    this.connectionDebugInfo.set(ws, debugInfo);
  }

  /**
   * Get system debug snapshot
   */
  public getSystemSnapshot(
    connections: Set<ServerWebSocket<CustomWebSocketData>>,
    topicSubscriptions: Map<string, Set<ServerWebSocket<CustomWebSocketData>>>,
  ): ISystemDebugSnapshot {
    const snapshot: ISystemDebugSnapshot = {
      timestamp: Date.now(),
      connections: {
        total: connections.size,
        byType: {},
        byServer: {},
      },
      messages: {
        totalProcessed: 0,
        byType: {},
        errorRate: 0,
        averageLatency: 0,
      },
      performance: {
        memoryUsage: process.memoryUsage(),
        uptime: process.uptime(),
      },
      subscriptions: {
        totalTopics: topicSubscriptions.size,
        totalSubscriptions: 0,
        topTopics: [],
      },
    };

    // Analyze connections
    for (const ws of connections) {
      const type = ws.data.type || "unknown";
      const serverId = ws.data.serverId || "unknown";

      snapshot.connections.byType[type] =
        (snapshot.connections.byType[type] || 0) + 1;
      snapshot.connections.byServer[serverId] =
        (snapshot.connections.byServer[serverId] || 0) + 1;
    }

    // Analyze subscriptions
    const topicCounts: Array<{ topic: string; subscribers: number }> = [];
    for (const [topic, subscribers] of topicSubscriptions.entries()) {
      snapshot.subscriptions.totalSubscriptions += subscribers.size;
      topicCounts.push({ topic, subscribers: subscribers.size });
    }

    // Sort and get top topics
    snapshot.subscriptions.topTopics = topicCounts
      .sort((a, b) => b.subscribers - a.subscribers)
      .slice(0, 10);

    // Get metrics from WebSocket logger
    const metrics = webSocketLogger.getMetrics();
    snapshot.messages.totalProcessed = metrics.messageCount;
    snapshot.messages.errorRate = metrics.errorCount / Math.max(metrics.messageCount, 1);
    snapshot.messages.averageLatency = metrics.averageLatency;

    return snapshot;
  }

  /**
   * Compare current system state with baseline
   */
  public compareWithBaseline(
    connections: Set<ServerWebSocket<CustomWebSocketData>>,
    topicSubscriptions: Map<string, Set<ServerWebSocket<CustomWebSocketData>>>,
  ): {
    current: ISystemDebugSnapshot;
    baseline?: ISystemDebugSnapshot;
    differences: Record<string, any>;
  } {
    const current = this.getSystemSnapshot(connections, topicSubscriptions);
    const differences: Record<string, any> = {};

    if (this.performanceBaseline) {
      const baseline = this.performanceBaseline;

      differences.connections = {
        totalChange: current.connections.total - baseline.connections.total,
        typeChanges: this.calculateObjectDifferences(
          current.connections.byType,
          baseline.connections.byType,
        ),
      };

      differences.messages = {
        totalProcessedChange:
          current.messages.totalProcessed - baseline.messages.totalProcessed,
        errorRateChange: current.messages.errorRate - baseline.messages.errorRate,
        latencyChange:
          current.messages.averageLatency - baseline.messages.averageLatency,
      };

      differences.memory = {
        heapUsedChange:
          current.performance.memoryUsage.heapUsed -
          baseline.performance.memoryUsage.heapUsed,
        rssChange:
          current.performance.memoryUsage.rss -
          baseline.performance.memoryUsage.rss,
      };

      differences.subscriptions = {
        topicsChange:
          current.subscriptions.totalTopics - baseline.subscriptions.totalTopics,
        subscriptionsChange:
          current.subscriptions.totalSubscriptions -
          baseline.subscriptions.totalSubscriptions,
      };
    }

    return {
      current,
      baseline: this.performanceBaseline,
      differences,
    };
  }

  /**
   * Set performance baseline for comparisons
   */
  public setPerformanceBaseline(
    connections: Set<ServerWebSocket<CustomWebSocketData>>,
    topicSubscriptions: Map<string, Set<ServerWebSocket<CustomWebSocketData>>>,
  ): void {
    this.performanceBaseline = this.getSystemSnapshot(
      connections,
      topicSubscriptions,
    );

    this.logger.info("Performance baseline set", undefined, {
      timestamp: this.performanceBaseline.timestamp,
      connections: this.performanceBaseline.connections.total,
      topics: this.performanceBaseline.subscriptions.totalTopics,
    });
  }

  /**
   * Add message interceptor for real-time debugging
   */
  public addMessageInterceptor(
    sessionId: string,
    interceptor: (message: IDebugMessage) => void,
  ): void {
    this.messageInterceptors.set(sessionId, interceptor);
    this.logger.debug("Message interceptor added", undefined, { sessionId });
  }

  /**
   * Remove message interceptor
   */
  public removeMessageInterceptor(sessionId: string): void {
    this.messageInterceptors.delete(sessionId);
    this.logger.debug("Message interceptor removed", undefined, { sessionId });
  }

  /**
   * Generate debug report for a specific user
   */
  public generateUserDebugReport(
    userId: string,
    connections: Set<ServerWebSocket<CustomWebSocketData>>,
  ): {
    userId: string;
    connections: IConnectionDebugInfo[];
    recentMessages: any[];
    metrics: {
      totalMessages: number;
      errorCount: number;
      averageLatency: number;
    };
  } {
    const userConnections: IConnectionDebugInfo[] = [];
    
    // Find all connections for the user
    for (const ws of connections) {
      if (ws.data.userId === userId) {
        userConnections.push(this.getConnectionDebugInfo(ws));
      }
    }

    // Get recent messages for the user
    const recentMessages = webSocketLogger.getLogs({
      userId,
      limit: 50,
    });

    // Calculate user-specific metrics
    const userMessages = recentMessages.filter((log) => log.userId === userId);
    const errorMessages = userMessages.filter((log) => log.error);
    const messagesWithDuration = userMessages.filter((log) => log.duration);
    const averageLatency = messagesWithDuration.length > 0
      ? messagesWithDuration.reduce((sum, log) => sum + (log.duration || 0), 0) / messagesWithDuration.length
      : 0;

    return {
      userId,
      connections: userConnections,
      recentMessages: recentMessages.slice(0, 20), // Limit for readability
      metrics: {
        totalMessages: userMessages.length,
        errorCount: errorMessages.length,
        averageLatency,
      },
    };
  }

  /**
   * Export debug data for external analysis
   */
  public exportDebugData(format: "json" | "csv" = "json"): string {
    const data = {
      sessions: Object.fromEntries(this.debugSessions),
      capturedMessages: Object.fromEntries(this.capturedMessages),
      systemSnapshot: this.performanceBaseline,
      exportedAt: new Date().toISOString(),
    };

    if (format === "json") {
      return JSON.stringify(data, null, 2);
    } else {
      // Simple CSV export of captured messages
      const allMessages = Array.from(this.capturedMessages.values()).flat();
      const headers = [
        "id",
        "timestamp",
        "userId",
        "direction",
        "messageType",
        "size",
        "processingTime",
      ];

      const csvRows = [
        headers.join(","),
        ...allMessages.map((msg) =>
          [
            msg.id,
            msg.timestamp,
            msg.userId || "",
            msg.direction,
            msg.messageType,
            msg.size,
            msg.processingTime || "",
          ].join(","),
        ),
      ];

      return csvRows.join("\n");
    }
  }

  /**
   * Clean up debug data
   */
  public cleanup(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAge;

    // Clean up old captured messages
    for (const [sessionId, messages] of this.capturedMessages.entries()) {
      const filteredMessages = messages.filter(
        (msg) => msg.timestamp > cutoff,
      );
      
      if (filteredMessages.length !== messages.length) {
        this.capturedMessages.set(sessionId, filteredMessages);
      }
    }

    this.logger.info("Debug data cleanup completed");
  }

  /**
   * Check if a message should be captured for a debug session
   */
  private shouldCaptureMessage(
    message: IDebugMessage,
    config: IDebugSessionConfig,
  ): boolean {
    // Check user filter
    if (config.userId && message.userId !== config.userId) {
      return false;
    }

    // Check message type filter
    if (
      config.messageTypes &&
      config.messageTypes.length > 0 &&
      !config.messageTypes.includes(message.messageType)
    ) {
      return false;
    }

    return true;
  }

  /**
   * Sanitize debug data to remove sensitive information
   */
  private sanitizeDebugData(data: any): any {
    if (!data || typeof data !== "object") {
      return data;
    }

    const sanitized = JSON.parse(JSON.stringify(data));

    const sensitiveFields = [
      "password",
      "token",
      "secret",
      "key",
      "authorization",
      "auth",
      "credential",
    ];

    const sanitizeObject = (obj: any): any => {
      if (!obj || typeof obj !== "object") {
        return obj;
      }

      if (Array.isArray(obj)) {
        return obj.map(sanitizeObject);
      }

      for (const [key, value] of Object.entries(obj)) {
        const lowerKey = key.toLowerCase();
        if (sensitiveFields.some((field) => lowerKey.includes(field))) {
          obj[key] = "[REDACTED]";
        } else if (typeof value === "object") {
          obj[key] = sanitizeObject(value);
        }
      }
      return obj;
    };

    return sanitizeObject(sanitized);
  }

  /**
   * Calculate differences between two objects
   */
  private calculateObjectDifferences(
    current: Record<string, number>,
    baseline: Record<string, number>,
  ): Record<string, number> {
    const differences: Record<string, number> = {};

    // Check all keys from both objects
    const allKeys = new Set([
      ...Object.keys(current),
      ...Object.keys(baseline),
    ]);

    for (const key of allKeys) {
      const currentValue = current[key] || 0;
      const baselineValue = baseline[key] || 0;
      differences[key] = currentValue - baselineValue;
    }

    return differences;
  }
}

// Export singleton instance
export const webSocketDebugger = new WebSocketDebugger();

// Export types for external use
export type {
  IDebugSessionConfig,
  IDebugMessage,
  IConnectionDebugInfo,
  ISystemDebugSnapshot,
};