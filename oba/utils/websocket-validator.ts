import { z } from "zod";
import {
  type IValidationResult,
  type IMessageTypeDefinition,
  type IWebSocketMessage,
  WebSocketErrorCode,
} from "../types/websocket-standardization.types";
import { logger } from "../services/logger.service";

/**
 * WebSocketValidator - Message validation system for WebSocket communications
 *
 * This class provides schema registration, validation, and error handling
 * for all WebSocket message types. It uses Zod for schema validation and
 * provides detailed error reporting for validation failures.
 */
export class WebSocketValidator {
  private static readonly schemas: Map<string | number, IMessageTypeDefinition> = new Map();
  private static readonly logger = logger.createLogger("WebSocketValidator");

  /**
   * Register a message type schema for validation
   * @param type Message type identifier
   * @param definition Message type definition with schema and metadata
   */
  public static registerSchema(
    type: string | number,
    definition: IMessageTypeDefinition,
  ): void {
    try {
      this.schemas.set(type, definition);
      this.logger.debug("Schema registered successfully", undefined, {
        type,
        category: definition.category,
        requiresAuth: definition.requiresAuth,
      });
    } catch (error) {
      this.logger.error("Failed to register schema", undefined, {
        error,
        type,
      });
      throw error;
    }
  }

  /**
   * Validate a complete WebSocket message
   * @param message Message to validate
   * @returns Validation result with errors if any
   */
  public static validate(message: unknown): IValidationResult {
    try {
      // Basic structure validation
      const structureResult = this.validateMessageStructure(message);
      if (!structureResult.isValid) {
        return structureResult;
      }

      const typedMessage = message as IWebSocketMessage;

      // Get schema definition for message type
      const definition = this.schemas.get(typedMessage.type);
      if (!definition) {
        return {
          isValid: false,
          errors: [
            {
              field: "type",
              message: `Unknown message type: ${typedMessage.type}`,
              code: WebSocketErrorCode.INVALID_MESSAGE_TYPE,
              value: typedMessage.type,
            },
          ],
        };
      }

      // Validate message data against schema
      if (definition.dataSchema && typedMessage.data !== undefined) {
        const dataResult = this.validateEventData(
          typedMessage.type,
          typedMessage.data,
        );
        if (!dataResult.isValid) {
          return dataResult;
        }
      }

      // Additional validations based on definition
      const additionalResult = this.validateAdditionalRequirements(
        typedMessage,
        definition,
      );
      if (!additionalResult.isValid) {
        return additionalResult;
      }

      return {
        isValid: true,
        sanitizedData: typedMessage,
      };
    } catch (error) {
      this.logger.error("Validation error", undefined, { error });
      return {
        isValid: false,
        errors: [
          {
            field: "message",
            message: "Internal validation error",
            code: WebSocketErrorCode.INTERNAL_ERROR,
          },
        ],
      };
    }
  }

  /**
   * Validate message structure and required fields
   * @param message Message to validate
   * @returns Validation result
   */
  public static validateMessageStructure(message: unknown): IValidationResult {
    const errors: Array<{
      field: string;
      message: string;
      code: string;
      value?: any;
    }> = [];

    // Check if message is an object
    if (!message || typeof message !== "object") {
      return {
        isValid: false,
        errors: [
          {
            field: "message",
            message: "Message must be an object",
            code: WebSocketErrorCode.INVALID_MESSAGE_FORMAT,
            value: message,
          },
        ],
      };
    }

    const msg = message as Record<string, any>;

    // Validate required top-level fields
    if (!msg.type) {
      errors.push({
        field: "type",
        message: "Message type is required",
        code: WebSocketErrorCode.MISSING_REQUIRED_FIELD,
      });
    }

    if (!msg.meta) {
      errors.push({
        field: "meta",
        message: "Message metadata is required",
        code: WebSocketErrorCode.MISSING_REQUIRED_FIELD,
      });
    } else {
      // Validate meta fields
      const metaErrors = this.validateMetaStructure(msg.meta);
      errors.push(...metaErrors);
    }

    return {
      isValid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }

  /**
   * Validate metadata structure
   * @param meta Metadata object to validate
   * @returns Array of validation errors
   */
  private static validateMetaStructure(meta: any): Array<{
    field: string;
    message: string;
    code: string;
    value?: any;
  }> {
    const errors: Array<{
      field: string;
      message: string;
      code: string;
      value?: any;
    }> = [];

    if (!meta.id) {
      errors.push({
        field: "meta.id",
        message: "Message ID is required",
        code: WebSocketErrorCode.MISSING_REQUIRED_FIELD,
      });
    }

    if (!meta.timestamp) {
      errors.push({
        field: "meta.timestamp",
        message: "Timestamp is required",
        code: WebSocketErrorCode.MISSING_REQUIRED_FIELD,
      });
    } else if (!this.isValidISO8601(meta.timestamp)) {
      errors.push({
        field: "meta.timestamp",
        message: "Timestamp must be a valid ISO 8601 string",
        code: WebSocketErrorCode.INVALID_DATA,
        value: meta.timestamp,
      });
    }

    if (!meta.version) {
      errors.push({
        field: "meta.version",
        message: "Version is required",
        code: WebSocketErrorCode.MISSING_REQUIRED_FIELD,
      });
    }

    if (!meta.source || !["server", "client"].includes(meta.source)) {
      errors.push({
        field: "meta.source",
        message: "Source must be 'server' or 'client'",
        code: WebSocketErrorCode.INVALID_DATA,
        value: meta.source,
      });
    }

    // Validate correlation ID format if present
    if (meta.correlationId !== undefined && !this.isValidCorrelationId(meta.correlationId)) {
      errors.push({
        field: "meta.correlationId",
        message: "Invalid correlation ID format",
        code: WebSocketErrorCode.INVALID_DATA,
        value: meta.correlationId,
      });
    }

    return errors;
  }

  /**
   * Validate event-specific data against registered schema
   * @param type Message type
   * @param data Data to validate
   * @returns Validation result
   */
  public static validateEventData<T>(
    type: string | number,
    data: T,
  ): IValidationResult {
    try {
      const definition = this.schemas.get(type);
      if (!definition || !definition.dataSchema) {
        // No schema defined, consider valid
        return {
          isValid: true,
          sanitizedData: data,
        };
      }

      // Check if dataSchema is valid before using it
      if (typeof definition.dataSchema.safeParse !== "function") {
        this.logger.error("Invalid schema definition", undefined, {
          type,
          schema: definition.dataSchema,
        });
        return {
          isValid: false,
          errors: [
            {
              field: "data",
              message: "Invalid schema definition",
              code: WebSocketErrorCode.INTERNAL_ERROR,
            },
          ],
        };
      }

      const result = definition.dataSchema.safeParse(data);
      if (!result.success) {
        const errors = result.error.errors.map((err: any) => ({
          field: err.path.join("."),
          message: err.message,
          code: WebSocketErrorCode.SCHEMA_VALIDATION_FAILED,
          value: err.input,
        }));

        return {
          isValid: false,
          errors,
        };
      }

      return {
        isValid: true,
        sanitizedData: result.data,
      };
    } catch (error) {
      this.logger.error("Data validation error", undefined, {
        error,
        type,
      });
      return {
        isValid: false,
        errors: [
          {
            field: "data",
            message: "Data validation failed",
            code: WebSocketErrorCode.INTERNAL_ERROR,
          },
        ],
      };
    }
  }

  /**
   * Validate additional requirements based on message definition
   * @param message Message to validate
   * @param definition Message type definition
   * @returns Validation result
   */
  private static validateAdditionalRequirements(
    message: IWebSocketMessage,
    definition: IMessageTypeDefinition,
  ): IValidationResult {
    const errors: Array<{
      field: string;
      message: string;
      code: string;
      value?: any;
    }> = [];

    // Check message size if maxSize is defined
    if (definition.maxSize) {
      const messageSize = JSON.stringify(message).length;
      if (messageSize > definition.maxSize) {
        errors.push({
          field: "message",
          message: `Message size (${messageSize}) exceeds maximum allowed (${definition.maxSize})`,
          code: WebSocketErrorCode.MESSAGE_TOO_LARGE,
          value: messageSize,
        });
      }
    }

    // Validate target structure if present
    if (message.target) {
      const targetErrors = this.validateTargetStructure(message.target);
      errors.push(...targetErrors);
    }

    return {
      isValid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }

  /**
   * Validate target structure
   * @param target Target object to validate
   * @returns Array of validation errors
   */
  private static validateTargetStructure(target: any): Array<{
    field: string;
    message: string;
    code: string;
    value?: any;
  }> {
    const errors: Array<{
      field: string;
      message: string;
      code: string;
      value?: any;
    }> = [];

    // Validate UUID format for ID fields
    const uuidFields = ["userId", "channelId", "serverId", "roleId", "excludeUserId"];
    for (const field of uuidFields) {
      if (target[field] && !this.isValidUUID(target[field])) {
        errors.push({
          field: `target.${field}`,
          message: `${field} must be a valid UUID`,
          code: WebSocketErrorCode.INVALID_DATA,
          value: target[field],
        });
      }
    }

    return errors;
  }

  /**
   * Get registered schema definition for a message type
   * @param type Message type
   * @returns Schema definition or undefined
   */
  public static getSchemaDefinition(
    type: string | number,
  ): IMessageTypeDefinition | undefined {
    return this.schemas.get(type);
  }

  /**
   * Get all registered message types
   * @returns Array of registered message types
   */
  public static getRegisteredTypes(): Array<string | number> {
    return Array.from(this.schemas.keys());
  }

  /**
   * Check if a message type is registered
   * @param type Message type to check
   * @returns Whether the type is registered
   */
  public static isTypeRegistered(type: string | number): boolean {
    return this.schemas.has(type);
  }

  /**
   * Clear all registered schemas (mainly for testing)
   */
  public static clearSchemas(): void {
    this.schemas.clear();
    this.logger.debug("All schemas cleared");
  }

  /**
   * Validate ISO 8601 timestamp format
   * @param timestamp Timestamp string to validate
   * @returns Whether timestamp is valid
   */
  private static isValidISO8601(timestamp: number): boolean {
    try {
      const date = new Date(timestamp);
      return date.toISOString() === timestamp;
    } catch {
      return false;
    }
  }

  /**
   * Validate UUID format
   * @param uuid UUID string to validate
   * @returns Whether UUID is valid
   */
  private static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Validate correlation ID format
   * @param correlationId Correlation ID to validate
   * @returns Whether correlation ID is valid
   */
  private static isValidCorrelationId(correlationId: string): boolean {
    // Correlation IDs should be non-empty strings with reasonable length
    return typeof correlationId === "string" && 
           correlationId.trim().length > 0 && 
           correlationId.length <= 100;
  }
}