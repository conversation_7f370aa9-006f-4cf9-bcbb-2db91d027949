import { db } from "../db";
import { drizzle } from "drizzle-orm/postgres-js";
import {
  UserRoles,
  ServerRoleSchema,
  ChannelAllowedRolesSchema,
  ChannelPrivacySchema,
  ServerSchema,
} from "../db/schema";
import { eq, and, or } from "drizzle-orm";
import { ADMINISTRATOR } from "../constants/permissions";

/**
 * Check if a user has a specific permission in a server
 *
 * @param userId - The ID of the user
 * @param serverId - The ID of the server
 * @param requiredPermission - The permission to check for
 * @returns True if the user has the permission, false otherwise
 */
export async function hasServerPermission(
  dbConnection: ReturnType<typeof drizzle>,
  userId: string,
  serverId: string,
  requiredPermission: bigint,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;
    // Check if the user is the server owner (owners have all permissions)
    const server = await db
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    if (server.length > 0 && server[0].ownerId === userId) {
      return true;
    }

    // Fetch user's roles in the given server
    const userRoles = await db
      .select({ roleId: UserRoles.roleId })
      .from(UserRoles)
      .where(
        and(eq(UserRoles.userId, userId), eq(UserRoles.serverId, serverId)),
      );

    if (userRoles.length === 0) {
      return false; // User has no roles in this server
    }

    let userPermissions = BigInt(0);

    // Combine permissions from all roles
    for (const role of userRoles) {
      const roleData = await db
        .select({ permissions: ServerRoleSchema.permissions })
        .from(ServerRoleSchema)
        .where(eq(ServerRoleSchema.id, role.roleId as string));

      if (roleData.length > 0) {
        userPermissions |= roleData[0].permissions;
      }
    }

    // Check if the user has the ADMINISTRATOR permission
    if ((userPermissions & ADMINISTRATOR) === ADMINISTRATOR) {
      return true; // Administrators have all permissions
    }

    // Check if the user has the required permission
    return (userPermissions & requiredPermission) === requiredPermission;
  } catch (error) {
    console.error("Error checking server permission:", error);
    return false;
  }
}

/**
 * Check if a user has a specific permission in a channel
 *
 * @param dbConnection - The database connection to use
 * @param userId - The ID of the user
 * @param channelId - The ID of the channel
 * @param serverId - The ID of the server
 * @param requiredPermission - The permission to check for
 * @returns True if the user has the permission, false otherwise
 */
export async function hasChannelPermission(
  dbConnection: ReturnType<typeof drizzle>,
  userId: string,
  channelId: string,
  serverId: string,
  requiredPermission: bigint,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;
    // Check if the user is the server owner (owners have all permissions)
    const server = await db
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    if (server.length > 0 && server[0].ownerId === userId) {
      return true;
    }

    // Check channel privacy settings
    const privacySettings = await db
      .select({ isPublic: ChannelPrivacySchema.isPublic })
      .from(ChannelPrivacySchema)
      .where(eq(ChannelPrivacySchema.channelId, channelId))
      .limit(1);

    // If the channel is private, check if the user has access
    if (privacySettings.length > 0 && !privacySettings[0].isPublic) {
      // Get user's roles
      const userRoles = await db
        .select({ roleId: UserRoles.roleId })
        .from(UserRoles)
        .where(
          and(eq(UserRoles.userId, userId), eq(UserRoles.serverId, serverId)),
        );

      if (userRoles.length === 0) {
        return false; // User has no roles in this server
      }

      const roleIds = userRoles.map((role) => role.roleId);

      // Check if any of the user's roles are allowed in this channel
      const allowedRoles = await db
        .select({ roleId: ChannelAllowedRolesSchema.roleId })
        .from(ChannelAllowedRolesSchema)
        .where(
          and(
            eq(ChannelAllowedRolesSchema.channelId, channelId),
            roleIds.length > 0
              ? or(
                  ...roleIds.map((roleId) =>
                    eq(ChannelAllowedRolesSchema.roleId, roleId as string),
                  ),
                )
              : eq(ChannelAllowedRolesSchema.roleId, ""),
          ),
        );

      if (allowedRoles.length === 0) {
        return false; // None of the user's roles are allowed in this channel
      }
    }

    // If we get here, the user has access to the channel
    // Now check if they have the required permission
    return hasServerPermission(db, userId, serverId, requiredPermission);
  } catch (error) {
    console.error("Error checking channel permission:", error);
    return false;
  }
}

/**
 * Create default roles for a server
 *
 * @param dbConnection - The database connection to use
 * @param serverId - The ID of the server
 * @param ownerId - The ID of the server owner
 */
export async function createDefaultRoles(
  dbConnection: ReturnType<typeof drizzle>,
  serverId: string,
  ownerId: string,
): Promise<void> {
  // Use the provided database connection
  const db = dbConnection;
  try {
    // Import default role permissions
    const {
      DEFAULT_ADMIN_PERMISSIONS,
      DEFAULT_MODERATOR_PERMISSIONS,
      DEFAULT_MEMBER_PERMISSIONS,
    } = await import("../constants/permissions");

    // Create admin role
    const adminRole = await db
      .insert(ServerRoleSchema)
      .values({
        serverId,
        name: "Admin",
        permissions: DEFAULT_ADMIN_PERMISSIONS,
      })
      .returning();

    // Create moderator role
    await db
      .insert(ServerRoleSchema)
      .values({
        serverId,
        name: "Moderator",
        permissions: DEFAULT_MODERATOR_PERMISSIONS,
      })
      .returning();

    // Create member role
    await db
      .insert(ServerRoleSchema)
      .values({
        serverId,
        name: "Member",
        permissions: DEFAULT_MEMBER_PERMISSIONS,
      })
      .returning();

    // Assign admin role to server owner
    if (adminRole.length > 0) {
      await db.insert(UserRoles).values({
        userId: ownerId,
        roleId: adminRole[0].id,
        serverId,
      });
    }
  } catch (error) {
    console.error("Error creating default roles:", error);
    throw error;
  }
}

/**
 * Get all roles for a server
 *
 * @param dbConnection - The database connection to use
 * @param serverId - The ID of the server
 * @returns Array of roles
 */
export async function getServerRoles(
  dbConnection: ReturnType<typeof drizzle>,
  serverId: string,
): Promise<any[]> {
  try {
    // Use the provided database connection
    const db = dbConnection;
    const roles = await db
      .select()
      .from(ServerRoleSchema)
      .where(eq(ServerRoleSchema.serverId, serverId));

    return roles;
  } catch (error) {
    console.error("Error getting server roles:", error);
    return [];
  }
}

/**
 * Get all roles for a user in a server
 *
 * @param dbConnection - The database connection to use
 * @param userId - The ID of the user
 * @param serverId - The ID of the server
 * @returns Array of roles
 */
export async function getUserRoles(
  dbConnection: ReturnType<typeof drizzle>,
  userId: string,
  serverId: string,
): Promise<any[]> {
  try {
    // Use the provided database connection
    const db = dbConnection;
    const userRoleIds = await db
      .select({ roleId: UserRoles.roleId })
      .from(UserRoles)
      .where(
        and(eq(UserRoles.userId, userId), eq(UserRoles.serverId, serverId)),
      );

    if (userRoleIds.length === 0) {
      return [];
    }

    const roleIds = userRoleIds.map((role) => role.roleId);

    const roles = await db
      .select()
      .from(ServerRoleSchema)
      .where(
        roleIds.length > 0
          ? or(
              ...roleIds.map((roleId) =>
                eq(ServerRoleSchema.id, roleId as string),
              ),
            )
          : eq(ServerRoleSchema.id, ""),
      );

    return roles;
  } catch (error) {
    console.error("Error getting user roles:", error);
    return [];
  }
}

/**
 * Assign a role to a user
 *
 * @param userId - The ID of the user
 * @param roleId - The ID of the role
 * @param serverId - The ID of the server
 * @returns True if successful, false otherwise
 */
export async function assignRoleToUser(
  userId: string,
  roleId: string,
  serverId: string,
): Promise<boolean> {
  try {
    // Check if the user already has this role
    const existingRole = await db
      .select()
      .from(UserRoles)
      .where(
        and(
          eq(UserRoles.userId, userId),
          eq(UserRoles.roleId, roleId),
          eq(UserRoles.serverId, serverId),
        ),
      )
      .limit(1);

    if (existingRole.length > 0) {
      return true; // User already has this role
    }

    // Assign the role
    await db.insert(UserRoles).values({
      userId,
      roleId,
      serverId,
    });

    return true;
  } catch (error) {
    console.error("Error assigning role to user:", error);
    return false;
  }
}

/**
 * Remove a role from a user
 *
 * @param userId - The ID of the user
 * @param roleId - The ID of the role
 * @param serverId - The ID of the server
 * @returns True if successful, false otherwise
 */
export async function removeRoleFromUser(
  userId: string,
  roleId: string,
  serverId: string,
): Promise<boolean> {
  try {
    await db
      .delete(UserRoles)
      .where(
        and(
          eq(UserRoles.userId, userId),
          eq(UserRoles.roleId, roleId),
          eq(UserRoles.serverId, serverId),
        ),
      );

    return true;
  } catch (error) {
    console.error("Error removing role from user:", error);
    return false;
  }
}

/**
 * Create a new role
 *
 * @param dbConnection - The database connection to use
 * @param serverId - The ID of the server
 * @param name - The name of the role
 * @param permissions - The permissions bitmask
 * @returns The created role, or null if creation failed
 */
export async function createRole(
  dbConnection: ReturnType<typeof drizzle>,
  serverId: string,
  name: string,
  permissions: bigint,
): Promise<any | null> {
  try {
    // Use the provided database connection
    const db = dbConnection;
    const role = await db
      .insert(ServerRoleSchema)
      .values({
        serverId,
        name,
        permissions,
      })
      .returning();

    return role.length > 0 ? role[0] : null;
  } catch (error) {
    console.error("Error creating role:", error);
    return null;
  }
}

/**
 * Update a role
 *
 * @param dbConnection - The database connection to use
 * @param roleId - The ID of the role
 * @param updates - The updates to apply
 * @returns The updated role, or null if update failed
 */
export async function updateRole(
  dbConnection: ReturnType<typeof drizzle>,
  roleId: string,
  updates: {
    name?: string;
    permissions?: bigint;
  },
): Promise<any | null> {
  try {
    // Use the provided database connection
    const db = dbConnection;
    const role = await db
      .update(ServerRoleSchema)
      .set(updates)
      .where(eq(ServerRoleSchema.id, roleId))
      .returning();

    return role.length > 0 ? role[0] : null;
  } catch (error) {
    console.error("Error updating role:", error);
    return null;
  }
}

/**
 * Delete a role
 *
 * @param dbConnection - The database connection to use
 * @param roleId - The ID of the role
 * @returns True if successful, false otherwise
 */
export async function deleteRole(
  dbConnection: ReturnType<typeof drizzle>,
  roleId: string,
): Promise<boolean> {
  try {
    // Use the provided database connection
    const db = dbConnection;
    // First, remove all user assignments for this role
    await db.delete(UserRoles).where(eq(UserRoles.roleId, roleId));

    // Then, remove all channel allowed roles for this role
    await db
      .delete(ChannelAllowedRolesSchema)
      .where(eq(ChannelAllowedRolesSchema.roleId, roleId));

    // Finally, delete the role itself
    await db.delete(ServerRoleSchema).where(eq(ServerRoleSchema.id, roleId));

    return true;
  } catch (error) {
    console.error("Error deleting role:", error);
    return false;
  }
}
