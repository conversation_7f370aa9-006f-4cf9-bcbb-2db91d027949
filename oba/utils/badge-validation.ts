import { z } from "zod";
import { 
  BADGE_CATEGORIES, 
  ASSIGNMENT_TYPES, 
  UNLOCK_TYPES,
  BADGE_CRITERIA_TYPES,
  type BadgeCategory,
  type AssignmentType,
  type UnlockType,
  type BadgeCriteriaType
} from "../types/badge.types";
import { 
  BadgeValidationError,
  InvalidBadgeCriteriaError 
} from "../class/badge-errors";

// Base validation schemas
export const badgeCategorySchema = z.enum(BADGE_CATEGORIES);
export const assignmentTypeSchema = z.enum(ASSIGNMENT_TYPES);
export const unlockTypeSchema = z.enum(UNLOCK_TYPES);
export const badgeCriteriaTypeSchema = z.enum(BADGE_CRITERIA_TYPES);

// Badge criteria validation schema
export const badgeCriteriaSchema = z.object({
  type: badgeCriteriaTypeSchema,
  threshold: z.number().min(0).optional(),
  conditions: z.record(z.any()).optional(),
}).refine((data) => {
  // For automatic badges with numeric thresholds, require threshold
  if (["message_count", "server_count", "friend_count", "days_active"].includes(data.type)) {
    return data.threshold !== undefined && data.threshold > 0;
  }
  // For custom criteria, conditions are required
  if (data.type === 'custom') {
    return data.conditions !== undefined && Object.keys(data.conditions).length > 0;
  }
  return true;
}, {
  message: "Threshold is required for numeric criteria types and must be greater than 0, conditions are required for custom criteria",
});

// Badge type creation validation
export const createBadgeTypeSchema = z.object({
  name: z.string()
    .min(1, "Badge name is required")
    .max(100, "Badge name must be 100 characters or less")
    .trim(),
  description: z.string()
    .min(1, "Badge description is required")
    .max(500, "Badge description must be 500 characters or less")
    .trim(),
  iconUrl: z.string()
    .url("Invalid icon URL format")
    .optional()
    .or(z.literal("")),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, "Color must be a valid hex color (e.g., #FF0000)")
    .default("#000000"),
  category: badgeCategorySchema,
  assignmentType: assignmentTypeSchema,
  criteria: badgeCriteriaSchema.optional(),
}).refine((data) => {
  // Automatic badges must have criteria
  if (data.assignmentType === 'automatic' && !data.criteria) {
    return false;
  }
  return true;
}, {
  message: "Automatic badges must have criteria defined"
});

// Badge type update validation
export const updateBadgeTypeSchema = z.object({
  name: z.string()
    .min(1, "Badge name is required")
    .max(100, "Badge name must be 100 characters or less")
    .trim()
    .optional(),
  description: z.string()
    .min(1, "Badge description is required")
    .max(500, "Badge description must be 500 characters or less")
    .trim()
    .optional(),
  iconUrl: z.string()
    .url("Invalid icon URL format")
    .optional()
    .or(z.literal("")),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, "Color must be a valid hex color (e.g., #FF0000)")
    .optional(),
  category: badgeCategorySchema.optional(),
  isActive: z.boolean().optional(),
  assignmentType: assignmentTypeSchema.optional(),
  criteria: badgeCriteriaSchema.optional(),
}).refine((data) => {
  // If assignmentType is being changed to automatic, criteria must be provided
  if (data.assignmentType === 'automatic' && !data.criteria) {
    return false;
  }
  return true;
}, {
  message: "Automatic badges must have criteria defined"
});

// Badge assignment validation
export const assignBadgeSchema = z.object({
  badgeTypeId: z.string()
    .uuid("Invalid badge type ID format"),
  userId: z.string()
    .uuid("Invalid user ID format"),
});

// Badge filters validation
export const badgeFiltersSchema = z.object({
  category: badgeCategorySchema.optional(),
  assignmentType: assignmentTypeSchema.optional(),
  isActive: z.boolean().optional(),
  search: z.string()
    .min(1)
    .max(100)
    .trim()
    .optional(),
});

// User stats validation
export const userStatsSchema = z.object({
  messageCount: z.number().int().min(0),
  serverCount: z.number().int().min(0),
  friendCount: z.number().int().min(0),
  daysActive: z.number().int().min(0),
  accountAge: z.number().int().min(0),
  lastActive: z.date(),
});

// Badge progress validation
export const badgeProgressSchema = z.object({
  badgeTypeId: z.string().uuid(),
  progress: z.number().min(0),
  total: z.number().min(1),
  isEarned: z.boolean(),
});

// Validation helper functions
export function validateCreateBadgeType(data: unknown) {
  return createBadgeTypeSchema.parse(data);
}

export function validateUpdateBadgeType(data: unknown) {
  return updateBadgeTypeSchema.parse(data);
}

export function validateAssignBadge(data: unknown) {
  return assignBadgeSchema.parse(data);
}

export function validateBadgeFilters(data: unknown) {
  return badgeFiltersSchema.parse(data);
}

export function validateUserStats(data: unknown) {
  return userStatsSchema.parse(data);
}

export function validateBadgeProgress(data: unknown) {
  return badgeProgressSchema.parse(data);
}

// Type exports for use in other files
export type CreateBadgeTypeInput = z.infer<typeof createBadgeTypeSchema>;
export type UpdateBadgeTypeInput = z.infer<typeof updateBadgeTypeSchema>;
export type AssignBadgeInput = z.infer<typeof assignBadgeSchema>;
export type BadgeFiltersInput = z.infer<typeof badgeFiltersSchema>;
export type UserStatsInput = z.infer<typeof userStatsSchema>;
export type BadgeProgressInput = z.infer<typeof badgeProgressSchema>;