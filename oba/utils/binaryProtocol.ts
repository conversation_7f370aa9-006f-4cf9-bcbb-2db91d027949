/**
 * Binary Protocol Utilities
 *
 * This file contains utilities for working with the binary voice data protocol.
 * The protocol format is:
 * [Header Length (4 bytes)][JSON Header][Binary Audio Data]
 */

/**
 * Parse a binary WebSocket message into metadata and audio data
 *
 * @param binaryData The binary WebSocket message
 * @returns Object containing metadata and audio data
 */
export function parseVoiceMessage(binaryData: ArrayBuffer): {
  metadata: any;
  audioData: ArrayBuffer;
} {
  // Create a DataView to read the header length (first 4 bytes)
  const headerLengthView = new DataView(binaryData, 0, 4);
  const headerLength = headerLengthView.getUint32(0, true); // true for little-endian

  // Extract the header as a string
  const headerBytes = new Uint8Array(binaryData, 4, headerLength);
  const headerString = new TextDecoder().decode(headerBytes);

  // Parse the header JSON
  const metadata = JSON.parse(headerString);

  // Extract the audio data
  const audioData = binaryData.slice(4 + headerLength);

  return { metadata, audioData };
}

/**
 * Create a binary WebSocket message from metadata and audio data
 *
 * @param metadata The metadata object
 * @param audioData The binary audio data
 * @returns Binary message ready to send over WebSocket
 */
export function createVoiceMessage(
  metadata: any,
  audioData: ArrayBuffer,
): ArrayBuffer {
  // Convert metadata to JSON string
  const headerString = JSON.stringify(metadata);

  // Convert header string to bytes
  const headerBytes = new TextEncoder().encode(headerString);

  // Create a buffer for the header length (4 bytes) + header + audio data
  const message = new ArrayBuffer(
    4 + headerBytes.byteLength + audioData.byteLength,
  );

  // Write the header length
  const headerLengthView = new DataView(message, 0, 4);
  headerLengthView.setUint32(0, headerBytes.byteLength, true); // true for little-endian

  // Write the header
  new Uint8Array(message, 4, headerBytes.byteLength).set(headerBytes);

  // Write the audio data
  new Uint8Array(message, 4 + headerBytes.byteLength, audioData.byteLength).set(
    new Uint8Array(audioData),
  );

  return message;
}

/**
 * Validate voice metadata
 *
 * @param metadata The metadata object to validate
 * @returns True if valid, false otherwise
 */
export function validateVoiceMetadata(metadata: any): boolean {
  return (
    metadata &&
    typeof metadata.type === "string" &&
    metadata.type === "voice_data" &&
    typeof metadata.userId === "string" &&
    typeof metadata.timestamp === "number" &&
    typeof metadata.sequence === "number"
  );
}

/**
 * Create voice metadata object
 *
 * @param userId The ID of the user sending the audio data
 * @param sequence The sequence number for packet ordering
 * @param format The audio format (e.g., "opus")
 * @param sampleRate The sample rate of the audio in Hz
 * @param channels The number of audio channels
 * @param frameSize The number of samples per frame
 * @returns Voice metadata object
 */
export function createVoiceMetadata(
  userId: string,
  sequence: number,
  format: string = "opus",
  sampleRate: number = 48000,
  channels: number = 1,
  frameSize: number = 960,
): any {
  return {
    type: "voice_data",
    userId,
    timestamp: Date.now(),
    sequence,
    format,
    sampleRate,
    channels,
    frameSize,
  };
}
