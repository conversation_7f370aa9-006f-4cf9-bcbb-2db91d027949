import type { ServerWebSocket } from "bun";
import {
  type IWebSocketMessage,
  type IWebSocketSuccessMessage,
  type IWebSocketErrorMessage,
  type IWebSocketEventMessage,
  WebSocketErrorCode,
} from "../types/websocket-standardization.types";
import type { CustomWebSocketData } from "../types/websocket.types";
import { WebSocketCompatibility } from "./websocket-compatibility";
import { WebSocketUtils } from "./websocket-utils";
import { logger } from "../services/logger.service";

/**
 * Legacy handler function type
 * Represents the signature of existing WebSocket handlers
 */
export type LegacyWebSocketHandler<T = unknown> = (
  ws: ServerWebSocket<CustomWebSocketData>,
  sender: string,
  data: T,
) => void | Promise<void>;

/**
 * Standardized handler function type
 * Represents the new signature for WebSocket handlers
 */
export type StandardizedWebSocketHandler<T = unknown> = (
  ws: ServerWebSocket<CustomWebSocketData>,
  message: IWebSocketMessage<T>,
) => void | Promise<void>;

/**
 * Migration context interface
 * Provides context information during handler migration
 */
export interface IMigrationContext {
  /** Whether the connection supports standardized format */
  supportsStandardizedFormat: boolean;
  /** Whether to log migration activities */
  enableLogging: boolean;
  /** Migration phase identifier */
  phase: "testing" | "gradual" | "complete";
  /** Handler name for logging */
  handlerName?: string;
}

/**
 * WebSocketMigrationUtils - Utilities for migrating existing WebSocket handlers
 *
 * This class provides utilities to help migrate existing WebSocket handlers
 * from legacy format to standardized format while maintaining backward compatibility.
 */
export class WebSocketMigrationUtils {
  private static readonly logger = logger.createLogger("WebSocketMigrationUtils");

  /**
   * Wrap a legacy handler to support both legacy and standardized message formats
   * @param legacyHandler The original legacy handler function
   * @param options Migration options
   * @returns A new handler that supports both formats
   */
  public static wrapLegacyHandler<T>(
    legacyHandler: LegacyWebSocketHandler<T>,
    options?: {
      handlerName?: string;
      enableLogging?: boolean;
      validateInput?: boolean;
    },
  ): StandardizedWebSocketHandler<T> {
    const handlerName = options?.handlerName || "UnknownHandler";
    const enableLogging = options?.enableLogging ?? true;

    return async (ws: ServerWebSocket<CustomWebSocketData>, message: IWebSocketMessage<T>) => {
      try {
        if (enableLogging) {
          this.logger.debug(`Processing message in wrapped legacy handler: ${handlerName}`, undefined, {
            messageType: message.type,
            messageId: message.meta.id,
            userId: ws.data.userId,
          });
        }

        // Extract sender and data for legacy handler compatibility
        const sender = message.target?.userId || ws.data.userId || "";
        const data = message.data as T;

        // Validate input if requested
        if (options?.validateInput && !this.validateLegacyHandlerInput(sender, data)) {
          const errorMessage = WebSocketUtils.validationError([
            { field: "sender", message: "Invalid sender information", code: "INVALID_SENDER" },
          ], message.meta.correlationId);
          
          WebSocketCompatibility.sendCompatibleMessage(ws, errorMessage);
          return;
        }

        // Call the legacy handler with extracted parameters
        await legacyHandler(ws, sender, data);

        if (enableLogging) {
          this.logger.debug(`Successfully processed message in legacy handler: ${handlerName}`, undefined, {
            messageType: message.type,
            messageId: message.meta.id,
          });
        }
      } catch (error) {
        this.logger.error(`Error in wrapped legacy handler: ${handlerName}`, undefined, {
          error,
          messageType: message.type,
          messageId: message.meta.id,
          userId: ws.data.userId,
        });

        // Send standardized error response
        const errorMessage = WebSocketUtils.internalError(
          `Handler error: ${error instanceof Error ? error.message : "Unknown error"}`,
          message.meta.correlationId,
        );
        
        WebSocketCompatibility.sendCompatibleMessage(ws, errorMessage);
      }
    };
  }

  /**
   * Create a dual-format handler that can process both legacy and standardized messages
   * @param legacyHandler Handler for legacy format messages
   * @param standardizedHandler Handler for standardized format messages
   * @param options Migration options
   * @returns A handler that routes to appropriate format handler
   */
  public static createDualFormatHandler<T>(
    legacyHandler: LegacyWebSocketHandler<T>,
    standardizedHandler: StandardizedWebSocketHandler<T>,
    options?: {
      handlerName?: string;
      preferStandardized?: boolean;
    },
  ): (ws: ServerWebSocket<CustomWebSocketData>, rawMessage: string) => Promise<void> {
    const handlerName = options?.handlerName || "DualFormatHandler";

    return async (ws: ServerWebSocket<CustomWebSocketData>, rawMessage: string) => {
      try {
        // Process the incoming message
        const processedMessage = WebSocketCompatibility.processIncomingMessage(rawMessage, ws);
        
        if (!processedMessage) {
          this.logger.error(`Failed to process message in ${handlerName}`, undefined, {
            userId: ws.data.userId,
          });
          return;
        }

        // Determine which handler to use
        const shouldUseLegacy = WebSocketCompatibility.shouldUseLegacyFormat(ws);
        
        if (shouldUseLegacy && !options?.preferStandardized) {
          // Use legacy handler
          const sender = processedMessage.target?.userId || ws.data.userId || "";
          const data = processedMessage.data as T;
          
          this.logger.debug(`Using legacy handler for ${handlerName}`, undefined, {
            messageType: processedMessage.type,
            userId: ws.data.userId,
          });
          
          await legacyHandler(ws, sender, data);
        } else {
          // Use standardized handler
          this.logger.debug(`Using standardized handler for ${handlerName}`, undefined, {
            messageType: processedMessage.type,
            messageId: processedMessage.meta.id,
            userId: ws.data.userId,
          });
          
          await standardizedHandler(ws, processedMessage);
        }
      } catch (error) {
        this.logger.error(`Error in dual format handler: ${handlerName}`, undefined, {
          error,
          userId: ws.data.userId,
        });
      }
    };
  }

  /**
   * Migrate legacy broadcast calls to use WebSocketUtils
   * @param manager WebSocket manager instance
   * @param legacyMessage Legacy message to broadcast
   * @param serverId Server ID for targeting
   * @param channelId Channel ID for targeting
   * @param excludeUserId User ID to exclude from broadcast
   */
  public static migrateLegacyBroadcast(
    manager: any,
    legacyMessage: Record<string, unknown>,
    serverId?: string,
    channelId?: string,
    excludeUserId?: string,
  ): void {
    try {
      // Convert legacy message to standardized format
      const standardizedMessage = WebSocketCompatibility.convertLegacyMessage(
        legacyMessage as any,
        { source: "server" },
      );

      // Add targeting information
      if (serverId || channelId) {
        standardizedMessage.target = {
          serverId,
          channelId,
          excludeUserId,
        };
      }

      // Use WebSocketUtils for broadcasting
      if (channelId) {
        WebSocketUtils.sendToChannel(channelId, standardizedMessage, manager, {
          serverId,
          excludeUserId,
        });
      } else if (serverId) {
        // Broadcast to entire server
        const serializedMessage = WebSocketUtils.serialize(standardizedMessage);
        manager.broadcast(serializedMessage, serverId, undefined, excludeUserId);
      } else {
        this.logger.warn("No targeting information provided for broadcast");
      }

      this.logger.debug("Successfully migrated legacy broadcast", undefined, {
        originalType: legacyMessage.type,
        messageId: standardizedMessage.meta.id,
        serverId,
        channelId,
      });
    } catch (error) {
      this.logger.error("Failed to migrate legacy broadcast", undefined, {
        error,
        messageType: legacyMessage.type,
        serverId,
        channelId,
      });
    }
  }

  /**
   * Create a migration wrapper for WebSocket manager methods
   * @param manager Original WebSocket manager
   * @returns Enhanced manager with migration utilities
   */
  public static createMigrationWrapper(manager: any) {
    return {
      // Original manager methods
      ...manager,

      /**
       * Enhanced broadcast method that supports both formats
       */
      broadcastCompatible: (
        message: IWebSocketMessage | Record<string, unknown>,
        serverId?: string,
        channelId?: string,
        excludeUserId?: string,
      ) => {
        try {
          let standardizedMessage: IWebSocketMessage;

          // Check if message is already standardized
          if (WebSocketUtils.validate(message)) {
            standardizedMessage = message as IWebSocketMessage;
          } else {
            // Convert legacy message
            standardizedMessage = WebSocketCompatibility.convertLegacyMessage(
              message as any,
              { source: "server" },
            );
          }

          // Use the appropriate broadcast method
          if (channelId) {
            WebSocketUtils.sendToChannel(channelId, standardizedMessage, manager, {
              serverId,
              excludeUserId,
            });
          } else {
            const serializedMessage = WebSocketUtils.serialize(standardizedMessage);
            manager.broadcast(serializedMessage, serverId, channelId, excludeUserId);
          }
        } catch (error) {
          this.logger.error("Error in compatible broadcast", undefined, {
            error,
            serverId,
            channelId,
          });
        }
      },

      /**
       * Enhanced sendToUser method that supports both formats
       */
      sendToUserCompatible: (
        userId: string,
        message: IWebSocketMessage | Record<string, unknown>,
      ) => {
        try {
          let standardizedMessage: IWebSocketMessage;

          if (WebSocketUtils.validate(message)) {
            standardizedMessage = message as IWebSocketMessage;
          } else {
            standardizedMessage = WebSocketCompatibility.convertLegacyMessage(
              message as any,
              { source: "server" },
            );
          }

          WebSocketUtils.sendToUser(userId, standardizedMessage, manager);
        } catch (error) {
          this.logger.error("Error in compatible sendToUser", undefined, {
            error,
            userId,
          });
        }
      },
    };
  }

  /**
   * Create migration helpers for common WebSocket operations
   */
  public static createMigrationHelpers() {
    return {
      /**
       * Convert legacy JSON.stringify calls to WebSocketUtils
       * @param message Message to serialize
       * @returns Serialized message string
       */
      serializeMessage: (message: IWebSocketMessage | Record<string, unknown>): string => {
        try {
          if (WebSocketUtils.validate(message)) {
            return WebSocketUtils.serialize(message as IWebSocketMessage);
          } else {
            // Convert and serialize legacy message
            const standardized = WebSocketCompatibility.convertLegacyMessage(
              message as any,
              { source: "server" },
            );
            return WebSocketUtils.serialize(standardized);
          }
        } catch (error) {
          this.logger.error("Error serializing message", undefined, error);
          return JSON.stringify(message);
        }
      },

      /**
       * Create standardized success response from legacy data
       * @param type Message type
       * @param data Response data
       * @param correlationId Optional correlation ID
       * @returns Standardized success message
       */
      createSuccessResponse: <T>(
        type: string | number,
        data: T,
        correlationId?: string,
      ): IWebSocketSuccessMessage<T> => {
        return WebSocketUtils.success(type, data, { correlationId });
      },

      /**
       * Create standardized error response from legacy error
       * @param message Error message
       * @param code Optional error code
       * @param correlationId Optional correlation ID
       * @returns Standardized error message
       */
      createErrorResponse: (
        message: string,
        code?: string,
        correlationId?: string,
      ): IWebSocketErrorMessage => {
        const errorCode = code ? WebSocketErrorCode.INTERNAL_ERROR : WebSocketErrorCode.INTERNAL_ERROR;
        return WebSocketUtils.error(errorCode, message, { correlationId });
      },

      /**
       * Create standardized event message from legacy event
       * @param event Event name
       * @param data Event data
       * @param target Optional targeting information
       * @returns Standardized event message
       */
      createEventMessage: <T>(
        event: string,
        data: T,
        target?: {
          userId?: string;
          channelId?: string;
          serverId?: string;
        },
      ): IWebSocketEventMessage<T> => {
        return WebSocketUtils.event(event, data, { target });
      },
    };
  }

  /**
   * Generate migration report for a handler
   * @param handlerName Name of the handler
   * @param legacyCallsCount Number of legacy format calls
   * @param standardizedCallsCount Number of standardized format calls
   * @returns Migration status report
   */
  public static generateMigrationReport(
    handlerName: string,
    legacyCallsCount: number,
    standardizedCallsCount: number,
  ): {
    handlerName: string;
    totalCalls: number;
    legacyPercentage: number;
    standardizedPercentage: number;
    migrationStatus: "not_started" | "in_progress" | "complete";
    recommendation: string;
  } {
    const totalCalls = legacyCallsCount + standardizedCallsCount;
    const legacyPercentage = totalCalls > 0 ? (legacyCallsCount / totalCalls) * 100 : 0;
    const standardizedPercentage = totalCalls > 0 ? (standardizedCallsCount / totalCalls) * 100 : 0;

    let migrationStatus: "not_started" | "in_progress" | "complete";
    let recommendation: string;

    if (standardizedCallsCount === 0) {
      migrationStatus = "not_started";
      recommendation = "Begin migration by implementing standardized message handling";
    } else if (legacyCallsCount > 0) {
      migrationStatus = "in_progress";
      recommendation = `Continue migration - ${legacyPercentage.toFixed(1)}% of calls still use legacy format`;
    } else {
      migrationStatus = "complete";
      recommendation = "Migration complete - consider removing legacy compatibility layer";
    }

    return {
      handlerName,
      totalCalls,
      legacyPercentage: Math.round(legacyPercentage * 100) / 100,
      standardizedPercentage: Math.round(standardizedPercentage * 100) / 100,
      migrationStatus,
      recommendation,
    };
  }

  // Private helper methods

  private static validateLegacyHandlerInput(sender: string, data: unknown): boolean {
    try {
      // Basic validation for legacy handler inputs
      if (!sender || typeof sender !== "string") {
        return false;
      }

      // Data can be undefined, but if present should be serializable
      if (data !== undefined) {
        JSON.stringify(data);
      }

      return true;
    } catch (error) {
      return false;
    }
  }
}