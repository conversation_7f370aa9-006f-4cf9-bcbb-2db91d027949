import { BadgeType, UserBadge } from "../types/badge.types";

/**
 * Badge Accessibility Utilities - Comprehensive accessibility features for badge visual elements
 */

export interface AccessibilityOptions {
  includeAriaLabels?: boolean;
  includeDescriptions?: boolean;
  includeKeyboardNavigation?: boolean;
  includeScreenReaderSupport?: boolean;
  includeHighContrast?: boolean;
  includeReducedMotion?: boolean;
  language?: string;
}

export interface AccessibilityResult {
  attributes: Record<string, string>;
  css: string;
  javascript?: string;
  screenReaderText: string;
}

export interface ColorContrastInfo {
  ratio: number;
  level: 'AA' | 'AAA' | 'FAIL';
  isAccessible: boolean;
  suggestions?: string[];
}

export interface MotionPreferences {
  respectReducedMotion: boolean;
  alternativeAnimations: string[];
  staticFallback: string;
}

/**
 * WCAG 2.1 compliance levels and requirements
 */
const WCAG_CONTRAST_RATIOS = {
  AA_NORMAL: 4.5,
  AA_LARGE: 3.0,
  AAA_NORMAL: 7.0,
  AAA_LARGE: 4.5
};

/**
 * Badge Accessibility Class
 */
export class BadgeAccessibility {
  /**
   * Generate comprehensive accessibility attributes for a badge
   */
  static generateAccessibilityAttributes(
    badge: BadgeType,
    options: AccessibilityOptions = {}
  ): AccessibilityResult {
    const {
      includeAriaLabels = true,
      includeDescriptions = true,
      includeKeyboardNavigation = true,
      includeScreenReaderSupport = true,
      includeHighContrast = true,
      includeReducedMotion = true,
      language = 'en'
    } = options;

    const attributes: Record<string, string> = {};
    let css = '';
    let javascript = '';

    // Basic accessibility attributes
    if (includeAriaLabels) {
      attributes['role'] = 'img';
      attributes['aria-label'] = this.generateAriaLabel(badge, language);
    }

    if (includeDescriptions) {
      const description = this.generateDetailedDescription(badge, language);
      attributes['aria-describedby'] = `badge-desc-${badge.id}`;
      attributes['title'] = description;
    }

    if (includeKeyboardNavigation) {
      attributes['tabindex'] = '0';
      attributes['role'] = 'button';
      javascript += this.generateKeyboardNavigationJS();
    }

    // High contrast support
    if (includeHighContrast) {
      css += this.generateHighContrastCSS();
    }

    // Reduced motion support
    if (includeReducedMotion) {
      css += this.generateReducedMotionCSS();
    }

    // Screen reader support
    const screenReaderText = includeScreenReaderSupport 
      ? this.generateScreenReaderText(badge, language)
      : '';

    return {
      attributes,
      css,
      javascript,
      screenReaderText
    };
  }

  /**
   * Generate ARIA label for badge
   */
  private static generateAriaLabel(badge: BadgeType, language: string): string {
    const title = badge.title || badge.name;
    const category = badge.category;
    
    // Localization support (basic implementation)
    const labels = this.getLocalizedLabels(language);
    
    return `${title} ${labels.badge}, ${labels.category} ${category}`;
  }

  /**
   * Generate detailed description for screen readers
   */
  private static generateDetailedDescription(badge: BadgeType, language: string): string {
    const labels = this.getLocalizedLabels(language);
    const title = badge.title || badge.name;
    const description = badge.description;
    const category = badge.category;
    const unlockType = badge.unlockType;
    
    let detailedDesc = `${title} ${labels.badge}. ${description}. `;
    detailedDesc += `${labels.category}: ${category}. `;
    detailedDesc += `${labels.unlockType}: ${unlockType}. `;
    
    if (badge.perks && badge.perks.length > 0) {
      detailedDesc += `${labels.perks}: ${badge.perks.join(', ')}. `;
    }
    
    if (badge.animation && badge.animation !== 'none') {
      detailedDesc += `${labels.animation}: ${badge.animation}. `;
    }
    
    return detailedDesc.trim();
  }

  /**
   * Generate screen reader text
   */
  private static generateScreenReaderText(badge: BadgeType, language: string): string {
    const labels = this.getLocalizedLabels(language);
    const title = badge.title || badge.name;
    
    return `${labels.earned} ${title} ${labels.badge}. ${badge.description}`;
  }

  /**
   * Get localized labels (basic implementation)
   */
  private static getLocalizedLabels(language: string): Record<string, string> {
    const labels: Record<string, Record<string, string>> = {
      en: {
        badge: 'badge',
        category: 'Category',
        unlockType: 'Unlock type',
        perks: 'Benefits',
        animation: 'Animation',
        earned: 'Earned',
        collection: 'Collection',
        progress: 'Progress'
      },
      es: {
        badge: 'insignia',
        category: 'Categoría',
        unlockType: 'Tipo de desbloqueo',
        perks: 'Beneficios',
        animation: 'Animación',
        earned: 'Obtenido',
        collection: 'Colección',
        progress: 'Progreso'
      },
      fr: {
        badge: 'badge',
        category: 'Catégorie',
        unlockType: 'Type de déverrouillage',
        perks: 'Avantages',
        animation: 'Animation',
        earned: 'Obtenu',
        collection: 'Collection',
        progress: 'Progrès'
      }
    };
    
    return labels[language] || labels.en;
  }

  /**
   * Generate keyboard navigation JavaScript
   */
  private static generateKeyboardNavigationJS(): string {
    return `
      // Badge keyboard navigation
      document.addEventListener('keydown', function(e) {
        if (e.target.classList.contains('badge-item') || e.target.closest('.badge-item')) {
          const badge = e.target.classList.contains('badge-item') ? e.target : e.target.closest('.badge-item');
          
          switch(e.key) {
            case 'Enter':
            case ' ':
              e.preventDefault();
              // Trigger badge click/activation
              badge.click();
              break;
            case 'ArrowRight':
            case 'ArrowDown':
              e.preventDefault();
              const next = badge.nextElementSibling;
              if (next && next.classList.contains('badge-item')) {
                next.focus();
              }
              break;
            case 'ArrowLeft':
            case 'ArrowUp':
              e.preventDefault();
              const prev = badge.previousElementSibling;
              if (prev && prev.classList.contains('badge-item')) {
                prev.focus();
              }
              break;
            case 'Home':
              e.preventDefault();
              const first = badge.parentElement.querySelector('.badge-item');
              if (first) first.focus();
              break;
            case 'End':
              e.preventDefault();
              const badges = badge.parentElement.querySelectorAll('.badge-item');
              const last = badges[badges.length - 1];
              if (last) last.focus();
              break;
          }
        }
      });
    `;
  }

  /**
   * Generate high contrast CSS
   */
  private static generateHighContrastCSS(): string {
    return `
      @media (prefers-contrast: high) {
        .badge-item {
          border: 2px solid currentColor !important;
          filter: contrast(1.5) !important;
        }
        
        .badge-item svg {
          filter: contrast(1.2) saturate(0.8) !important;
        }
        
        .badge-item:focus {
          outline: 3px solid #000 !important;
          outline-offset: 2px !important;
        }
        
        .badge-tooltip {
          background: #000 !important;
          color: #fff !important;
          border: 1px solid #fff !important;
        }
      }
      
      @media (prefers-contrast: more) {
        .badge-item {
          border: 3px solid #000 !important;
          background: #fff !important;
        }
        
        .badge-item svg {
          filter: contrast(2) saturate(0) !important;
        }
      }
    `;
  }

  /**
   * Generate reduced motion CSS
   */
  private static generateReducedMotionCSS(): string {
    return `
      @media (prefers-reduced-motion: reduce) {
        .badge-item,
        .badge-item *,
        .badge-animation-pulse,
        .badge-animation-glow,
        .badge-animation-bounce,
        .badge-animation-rotate,
        .badge-animation-shake,
        .badge-animation-fade {
          animation: none !important;
          transition: none !important;
        }
        
        .badge-item:hover {
          transform: none !important;
        }
        
        .badge-item:focus {
          transform: scale(1.05) !important;
          transition: transform 0.1s ease !important;
        }
      }
      
      @media (prefers-reduced-motion: no-preference) {
        .badge-item {
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
      }
    `;
  }

  /**
   * Analyze color contrast for accessibility compliance
   */
  static analyzeColorContrast(
    foregroundColor: string,
    backgroundColor: string,
    fontSize: number = 14
  ): ColorContrastInfo {
    const ratio = this.calculateContrastRatio(foregroundColor, backgroundColor);
    const isLargeText = fontSize >= 18 || (fontSize >= 14 && this.isBoldText());
    
    let level: 'AA' | 'AAA' | 'FAIL';
    let isAccessible: boolean;
    
    if (isLargeText) {
      if (ratio >= WCAG_CONTRAST_RATIOS.AAA_LARGE) {
        level = 'AAA';
        isAccessible = true;
      } else if (ratio >= WCAG_CONTRAST_RATIOS.AA_LARGE) {
        level = 'AA';
        isAccessible = true;
      } else {
        level = 'FAIL';
        isAccessible = false;
      }
    } else {
      if (ratio >= WCAG_CONTRAST_RATIOS.AAA_NORMAL) {
        level = 'AAA';
        isAccessible = true;
      } else if (ratio >= WCAG_CONTRAST_RATIOS.AA_NORMAL) {
        level = 'AA';
        isAccessible = true;
      } else {
        level = 'FAIL';
        isAccessible = false;
      }
    }
    
    const suggestions = !isAccessible ? this.generateContrastSuggestions(ratio, isLargeText) : undefined;
    
    return {
      ratio,
      level,
      isAccessible,
      suggestions
    };
  }

  /**
   * Calculate contrast ratio between two colors
   */
  private static calculateContrastRatio(color1: string, color2: string): number {
    const luminance1 = this.getLuminance(color1);
    const luminance2 = this.getLuminance(color2);
    
    const lighter = Math.max(luminance1, luminance2);
    const darker = Math.min(luminance1, luminance2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /**
   * Get relative luminance of a color
   */
  private static getLuminance(color: string): number {
    const rgb = this.hexToRgb(color);
    if (!rgb) return 0;
    
    const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /**
   * Convert hex color to RGB
   */
  private static hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  /**
   * Check if text is bold (simplified implementation)
   */
  private static isBoldText(): boolean {
    // In a real implementation, this would check the actual font weight
    return false;
  }

  /**
   * Generate suggestions for improving contrast
   */
  private static generateContrastSuggestions(currentRatio: number, isLargeText: boolean): string[] {
    const targetRatio = isLargeText ? WCAG_CONTRAST_RATIOS.AA_LARGE : WCAG_CONTRAST_RATIOS.AA_NORMAL;
    const suggestions: string[] = [];
    
    if (currentRatio < targetRatio) {
      suggestions.push(`Current contrast ratio is ${currentRatio.toFixed(2)}:1, but needs to be at least ${targetRatio}:1`);
      suggestions.push('Consider using a darker foreground color or lighter background color');
      suggestions.push('Alternatively, use a lighter foreground color with a darker background');
      
      if (!isLargeText) {
        suggestions.push('Consider increasing font size to 18px or larger to reduce contrast requirements');
      }
    }
    
    return suggestions;
  }

  /**
   * Generate motion preferences configuration
   */
  static getMotionPreferences(badge: BadgeType): MotionPreferences {
    const hasAnimation = badge.animation && badge.animation !== 'none';
    
    return {
      respectReducedMotion: true,
      alternativeAnimations: hasAnimation ? ['focus-scale', 'opacity-change'] : [],
      staticFallback: hasAnimation ? 'static-highlight' : 'none'
    };
  }

  /**
   * Generate accessible badge tooltip
   */
  static generateAccessibleTooltip(badge: BadgeType, language: string = 'en'): {
    html: string;
    css: string;
    javascript: string;
  } {
    const tooltipId = `tooltip-${badge.id}`;
    const labels = this.getLocalizedLabels(language);
    
    const html = `
      <div id="${tooltipId}" 
           class="badge-tooltip" 
           role="tooltip" 
           aria-hidden="true">
        <div class="tooltip-header">
          <strong>${badge.title || badge.name}</strong>
        </div>
        <div class="tooltip-description">
          ${badge.description}
        </div>
        ${badge.perks && badge.perks.length > 0 ? `
          <div class="tooltip-perks">
            <strong>${labels.perks}:</strong>
            <ul>
              ${badge.perks.map(perk => `<li>${perk}</li>`).join('')}
            </ul>
          </div>
        ` : ''}
        <div class="tooltip-meta">
          <small>${labels.category}: ${badge.category}</small>
        </div>
      </div>
    `;

    const css = `
      .badge-tooltip {
        position: absolute;
        z-index: 1000;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 12px;
        border-radius: 8px;
        font-size: 12px;
        line-height: 1.4;
        max-width: 250px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        pointer-events: none;
        opacity: 0;
        transform: translateY(4px);
        transition: opacity 0.2s ease, transform 0.2s ease;
      }
      
      .badge-tooltip[aria-hidden="false"] {
        opacity: 1;
        transform: translateY(0);
      }
      
      .tooltip-header {
        margin-bottom: 4px;
      }
      
      .tooltip-description {
        margin-bottom: 8px;
      }
      
      .tooltip-perks ul {
        margin: 4px 0 0 0;
        padding-left: 16px;
      }
      
      .tooltip-perks li {
        margin-bottom: 2px;
      }
      
      .tooltip-meta {
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        opacity: 0.8;
      }
      
      @media (prefers-reduced-motion: reduce) {
        .badge-tooltip {
          transition: none;
        }
      }
      
      @media (prefers-contrast: high) {
        .badge-tooltip {
          background: #000;
          border: 1px solid #fff;
        }
      }
    `;

    const javascript = `
      (function() {
        const tooltip = document.getElementById('${tooltipId}');
        if (!tooltip) return;
        
        let showTimeout, hideTimeout;
        
        function showTooltip(targetElement) {
          clearTimeout(hideTimeout);
          showTimeout = setTimeout(() => {
            const rect = targetElement.getBoundingClientRect();
            tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
            tooltip.setAttribute('aria-hidden', 'false');
            targetElement.setAttribute('aria-describedby', '${tooltipId}');
          }, 500);
        }
        
        function hideTooltip(targetElement) {
          clearTimeout(showTimeout);
          hideTimeout = setTimeout(() => {
            tooltip.setAttribute('aria-hidden', 'true');
            targetElement.removeAttribute('aria-describedby');
          }, 100);
        }
        
        // Export functions for use by badge components
        window.badgeTooltip = window.badgeTooltip || {};
        window.badgeTooltip['${badge.id}'] = { showTooltip, hideTooltip };
      })();
    `;

    return { html, css, javascript };
  }

  /**
   * Validate badge accessibility compliance
   */
  static validateAccessibilityCompliance(badge: BadgeType): {
    isCompliant: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check if badge has proper text alternatives
    if (!badge.name || badge.name.trim().length === 0) {
      issues.push('Badge must have a name for screen readers');
    }

    if (!badge.description || badge.description.trim().length === 0) {
      issues.push('Badge must have a description for accessibility');
    }

    // Check color contrast if design colors are available
    if (typeof badge.design === 'object' && badge.design.colors && badge.design.colors.length >= 2) {
      const contrastInfo = this.analyzeColorContrast(badge.design.colors[0], badge.design.colors[1]);
      if (!contrastInfo.isAccessible) {
        issues.push(`Color contrast ratio ${contrastInfo.ratio.toFixed(2)}:1 does not meet WCAG ${contrastInfo.level} standards`);
        recommendations.push(...(contrastInfo.suggestions || []));
      }
    }

    // Check animation accessibility
    if (badge.animation && badge.animation !== 'none') {
      recommendations.push('Ensure animations respect prefers-reduced-motion user preference');
      recommendations.push('Provide static alternatives for animated badges');
    }

    // Check icon accessibility
    if (!badge.icon || badge.icon.trim().length === 0) {
      recommendations.push('Consider adding an icon or emoji to improve visual recognition');
    }

    return {
      isCompliant: issues.length === 0,
      issues,
      recommendations
    };
  }
}