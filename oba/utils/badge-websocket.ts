import { WebSocketManager } from "../manager/websocket.manager";
import { WebSocketUtils } from "./websocket-utils";
import { EventTypes } from "../constants/eventTypes";
import type {
  BadgeAssignedPayload,
  BadgeRemovedPayload,
  BadgeProgressUpdatePayload,
  BadgeAssignedEvent,
  BadgeRemovedEvent,
  BadgeProgressUpdateEvent
} from "../types/badge-websocket.types";
import type { UserBadge, BadgeType } from "../types/badge.types";
import type { Perk } from "../services/perk.service";

/**
 * Badge WebSocket Utility Service
 * Handles broadcasting of badge-related events to connected clients
 */
export class BadgeWebSocketService {
  private wsManager: WebSocketManager;

  constructor() {
    this.wsManager = WebSocketManager.getInstance();
  }

  /**
   * Broadcasts a badge assigned event to the user and relevant connections
   */
  async broadcastBadgeAssigned(payload: BadgeAssignedPayload): Promise<void> {
    try {
      const event: BadgeAssignedEvent = {
        type: "BADGE_ASSIGNED",
        userId: payload.userId,
        badge: payload.badge,
        isAutomatic: payload.isAutomatic,
        assignedBy: payload.assignedBy,
        timestamp: new Date()
      };

      // Create standardized WebSocket message
      const message = WebSocketUtils.event(EventTypes.BADGE_ASSIGNED, event);

      // Send to the user who received the badge
      this.wsManager.broadcastToUser(payload.userId, message);

      // If the badge was assigned manually, also notify the assigner
      if (payload.assignedBy && payload.assignedBy !== payload.userId) {
        this.wsManager.broadcastToUser(payload.assignedBy, message);
      }

      // Broadcast to servers where the user is a member (for profile updates)
      await this.broadcastToUserServers(payload.userId, message);

      console.log(`Badge assigned event broadcasted for user ${payload.userId}, badge ${payload.badge.badgeTypeId}`);
    } catch (error) {
      console.error("Error broadcasting badge assigned event:", error);
    }
  }

  /**
   * Broadcasts a badge removed event to the user and relevant connections
   */
  async broadcastBadgeRemoved(payload: BadgeRemovedPayload): Promise<void> {
    try {
      const event: BadgeRemovedEvent = {
        type: "BADGE_REMOVED",
        userId: payload.userId,
        badgeTypeId: payload.badgeTypeId,
        badgeType: payload.badgeType,
        removedBy: payload.removedBy,
        timestamp: new Date()
      };

      // Create standardized WebSocket message
      const message = WebSocketUtils.event(EventTypes.BADGE_REMOVED, event);

      // Send to the user who lost the badge
      this.wsManager.broadcastToUser(payload.userId, message);

      // Notify the user who removed the badge (if different)
      if (payload.removedBy !== payload.userId) {
        this.wsManager.broadcastToUser(payload.removedBy, message);
      }

      // Broadcast to servers where the user is a member (for profile updates)
      await this.broadcastToUserServers(payload.userId, message);

      console.log(`Badge removed event broadcasted for user ${payload.userId}, badge ${payload.badgeTypeId}`);
    } catch (error) {
      console.error("Error broadcasting badge removed event:", error);
    }
  }

  /**
   * Broadcasts a badge progress update event to the user
   */
  async broadcastBadgeProgressUpdate(payload: BadgeProgressUpdatePayload): Promise<void> {
    try {
      const event: BadgeProgressUpdateEvent = {
        type: "BADGE_PROGRESS_UPDATE",
        userId: payload.userId,
        badgeTypeId: payload.badgeTypeId,
        badgeType: payload.badgeType,
        progress: payload.progress,
        total: payload.total,
        progressPercentage: payload.progressPercentage,
        timestamp: new Date()
      };

      // Create standardized WebSocket message
      const message = WebSocketUtils.event(EventTypes.BADGE_PROGRESS_UPDATE, event);

      // Send only to the user whose progress was updated
      this.wsManager.broadcastToUser(payload.userId, message);

      console.log(`Badge progress update event broadcasted for user ${payload.userId}, badge ${payload.badgeTypeId}, progress: ${payload.progress}/${payload.total}`);
    } catch (error) {
      console.error("Error broadcasting badge progress update event:", error);
    }
  }

  /**
   * Broadcasts badge events to all servers where a user is a member
   * This ensures that other users see updated badge information in profiles
   */
  private async broadcastToUserServers(userId: string, message: any): Promise<void> {
    try {
      // Get all servers where the user is a member
      const { getUserServerMemberships } = await import("../db/utils");
      const { db } = await import("../db");
      
      const serverMemberships = await getUserServerMemberships(db, userId);
      
      // Broadcast to each server the user is a member of
      for (const membership of serverMemberships) {
        // Broadcast to all channels in the server (excluding the user themselves)
        this.wsManager.broadcast(message, membership.serverId, undefined, userId);
      }
    } catch (error) {
      console.error("Error broadcasting to user servers:", error);
      // Don't throw - this is a nice-to-have feature, not critical
    }
  }

  /**
   * Broadcasts multiple badge events in batch
   * Useful for bulk badge operations or evaluation results
   */
  async broadcastBatchBadgeAssigned(assignments: BadgeAssignedPayload[]): Promise<void> {
    try {
      // Process assignments in batches to avoid overwhelming the WebSocket system
      const batchSize = 10;
      for (let i = 0; i < assignments.length; i += batchSize) {
        const batch = assignments.slice(i, i + batchSize);
        
        // Process batch concurrently
        await Promise.all(
          batch.map(assignment => this.broadcastBadgeAssigned(assignment))
        );
        
        // Small delay between batches to prevent overwhelming
        if (i + batchSize < assignments.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
    } catch (error) {
      console.error("Error broadcasting batch badge assignments:", error);
    }
  }

  /**
   * Broadcasts badge progress updates for multiple users
   * Useful for periodic progress notifications
   */
  async broadcastBatchProgressUpdates(updates: BadgeProgressUpdatePayload[]): Promise<void> {
    try {
      // Process updates in batches
      const batchSize = 20;
      for (let i = 0; i < updates.length; i += batchSize) {
        const batch = updates.slice(i, i + batchSize);
        
        // Process batch concurrently
        await Promise.all(
          batch.map(update => this.broadcastBadgeProgressUpdate(update))
        );
        
        // Small delay between batches
        if (i + batchSize < updates.length) {
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }
    } catch (error) {
      console.error("Error broadcasting batch progress updates:", error);
    }
  }

  /**
   * Broadcasts a perk assigned event to the user
   */
  async broadcastPerkAssigned(payload: {
    userId: string;
    perk: Perk;
    badgeTypeId: string;
    serverId?: string;
  }): Promise<void> {
    try {
      const event = {
        type: "PERK_ASSIGNED",
        userId: payload.userId,
        perk: payload.perk,
        badgeTypeId: payload.badgeTypeId,
        serverId: payload.serverId,
        timestamp: new Date()
      };

      // Create standardized WebSocket message
      const message = WebSocketUtils.event("PERK_ASSIGNED", event);

      // Send to the user who received the perk
      this.wsManager.broadcastToUser(payload.userId, message);

      // If server-specific, also broadcast to the server
      if (payload.serverId) {
        this.wsManager.broadcast(message, payload.serverId);
      }

      console.log(`Perk assigned event broadcasted for user ${payload.userId}, perk ${payload.perk.name}`);
    } catch (error) {
      console.error("Error broadcasting perk assigned event:", error);
    }
  }

  /**
   * Broadcasts a perk revoked event to the user
   */
  async broadcastPerkRevoked(payload: {
    userId: string;
    perk: Perk;
    badgeTypeId: string;
    serverId?: string;
  }): Promise<void> {
    try {
      const event = {
        type: "PERK_REVOKED",
        userId: payload.userId,
        perk: payload.perk,
        badgeTypeId: payload.badgeTypeId,
        serverId: payload.serverId,
        timestamp: new Date()
      };

      // Create standardized WebSocket message
      const message = WebSocketUtils.event("PERK_REVOKED", event);

      // Send to the user who lost the perk
      this.wsManager.broadcastToUser(payload.userId, message);

      // If server-specific, also broadcast to the server
      if (payload.serverId) {
        this.wsManager.broadcast(message, payload.serverId);
      }

      console.log(`Perk revoked event broadcasted for user ${payload.userId}, perk ${payload.perk.name}`);
    } catch (error) {
      console.error("Error broadcasting perk revoked event:", error);
    }
  }

  /**
   * Sends a badge notification to a specific user
   * Used for personal notifications that don't need to be broadcasted
   */
  async sendBadgeNotification(
    userId: string, 
    type: "achievement" | "milestone" | "progress",
    data: any
  ): Promise<void> {
    try {
      const message = WebSocketUtils.event("BADGE_NOTIFICATION", {
        type,
        userId,
        data,
        timestamp: new Date()
      });

      this.wsManager.broadcastToUser(userId, message);
    } catch (error) {
      console.error("Error sending badge notification:", error);
    }
  }
}

// Export singleton instance
export const badgeWebSocketService = new BadgeWebSocketService();