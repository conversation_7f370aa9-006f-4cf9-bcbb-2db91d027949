import { db } from "../db";
import { drizzle } from "drizzle-orm/postgres-js";
import {
  ServerMembershipSchema,
  ServerBanSchema,
  UserRoles,
  ServerSchema,
  UserSchema,
  ServerRoleSchema,
} from "../db/schema";
import { eq, and, inArray, not } from "drizzle-orm";
import { hasServerPermission } from "./permissions";
import {
  KICK_ACCEPT_DENY_USERS,
  BLOCK_USERS,
  MANAGE_ROLES,
} from "../constants/permissions";

/**
 * Kick a user from a server
 *
 * @param dbConnection - The database connection to use
 * @param serverId - The ID of the server
 * @param userId - The ID of the user to kick
 * @param kickedById - The ID of the user performing the kick
 * @returns True if successful, false otherwise
 */
export async function kickMember(
  dbConnection: ReturnType<typeof drizzle>,
  serverId: string,
  userId: string,
  kickedById: string,
): Promise<boolean> {
  try {
    // Check if the user performing the kick has permission
    const hasPermission = await hasServerPermission(
      dbConnection,
      kickedById,
      serverId,
      KICK_ACCEPT_DENY_USERS,
    );

    if (!hasPermission) {
      return false;
    }

    // Check if the user is the server owner (can't kick the owner)
    const server = await dbConnection
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    if (server.length > 0 && server[0].ownerId === userId) {
      return false; // Can't kick the server owner
    }

    // Delete the user's membership
    const result = await dbConnection
      .delete(ServerMembershipSchema)
      .where(
        and(
          eq(ServerMembershipSchema.serverId, serverId),
          eq(ServerMembershipSchema.userId, userId),
        ),
      );

    // Delete the user's roles in the server
    await dbConnection
      .delete(UserRoles)
      .where(
        and(eq(UserRoles.serverId, serverId), eq(UserRoles.userId, userId)),
      );

    return true;
  } catch (error) {
    console.error("Error kicking member:", error);
    return false;
  }
}

/**
 * Ban a user from a server
 *
 * @param dbConnection - The database connection to use
 * @param serverId - The ID of the server
 * @param userId - The ID of the user to ban
 * @param bannedById - The ID of the user performing the ban
 * @param reason - The reason for the ban (optional)
 * @returns True if successful, false otherwise
 */
export async function banMember(
  dbConnection: ReturnType<typeof drizzle>,
  serverId: string,
  userId: string,
  bannedById: string,
  reason?: string,
): Promise<boolean> {
  try {
    // Check if the user performing the ban has permission
    const hasPermission = await hasServerPermission(
      dbConnection,
      bannedById,
      serverId,
      BLOCK_USERS,
    );

    if (!hasPermission) {
      return false;
    }

    // Check if the user is the server owner (can't ban the owner)
    const server = await dbConnection
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    if (server.length > 0 && server[0].ownerId === userId) {
      return false; // Can't ban the server owner
    }

    // First, kick the user from the server
    await kickMember(dbConnection, serverId, userId, bannedById);

    // Then, add a ban record
    await dbConnection.insert(ServerBanSchema).values({
      serverId,
      userId,
      bannedById,
      reason: reason || "No reason provided",
      bannedAt: new Date(),
    });

    return true;
  } catch (error) {
    console.error("Error banning member:", error);
    return false;
  }
}

/**
 * Unban a user from a server
 *
 * @param dbConnection - The database connection to use
 * @param serverId - The ID of the server
 * @param userId - The ID of the user to unban
 * @param unbannedById - The ID of the user performing the unban
 * @returns True if successful, false otherwise
 */
export async function unbanMember(
  dbConnection: ReturnType<typeof drizzle>,
  serverId: string,
  userId: string,
  unbannedById: string,
): Promise<boolean> {
  try {
    // Check if the user performing the unban has permission
    const hasPermission = await hasServerPermission(
      dbConnection,
      unbannedById,
      serverId,
      BLOCK_USERS,
    );

    if (!hasPermission) {
      return false;
    }

    // Delete the ban record
    const result = await dbConnection
      .delete(ServerBanSchema)
      .where(
        and(
          eq(ServerBanSchema.serverId, serverId),
          eq(ServerBanSchema.userId, userId),
        ),
      );

    return true;
  } catch (error) {
    console.error("Error unbanning member:", error);
    return false;
  }
}

/**
 * Change a user's roles in a server
 *
 * @param dbConnection - The database connection to use
 * @param serverId - The ID of the server
 * @param userId - The ID of the user
 * @param roleIds - The IDs of the roles to assign
 * @param changedById - The ID of the user performing the change
 * @returns True if successful, false otherwise
 */
export async function changeUserRoles(
  dbConnection: ReturnType<typeof drizzle>,
  serverId: string,
  userId: string,
  roleIds: string[],
  changedById: string,
): Promise<boolean> {
  try {
    // Check if the user performing the role change has permission
    const hasPermission = await hasServerPermission(
      dbConnection,
      changedById,
      serverId,
      MANAGE_ROLES,
    );

    if (!hasPermission) {
      return false;
    }

    // Check if the user is the server owner (can't change owner's roles)
    const server = await dbConnection
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    if (server.length > 0 && server[0].ownerId === userId) {
      return false; // Can't change the server owner's roles
    }

    // Start a transaction
    // First, delete all existing roles
    await dbConnection
      .delete(UserRoles)
      .where(
        and(eq(UserRoles.serverId, serverId), eq(UserRoles.userId, userId)),
      );

    // Then, add the new roles
    if (roleIds.length > 0) {
      const values = roleIds.map((roleId) => ({
        userId,
        roleId,
        serverId,
      }));

      await dbConnection.insert(UserRoles).values(values);
    }

    return true;
  } catch (error) {
    console.error("Error changing user roles:", error);
    return false;
  }
}

/**
 * Get banned users for a server
 *
 * @param dbConnection - The database connection to use
 * @param serverId - The ID of the server
 * @param requesterId - The ID of the user making the request
 * @returns Array of banned users with ban details, or null if not authorized
 */
export async function getBannedUsers(
  dbConnection: ReturnType<typeof drizzle>,
  serverId: string,
  requesterId: string,
): Promise<any[] | null> {
  try {
    // Check if the user has permission to view banned users
    const hasPermission = await hasServerPermission(
      dbConnection,
      requesterId,
      serverId,
      BLOCK_USERS,
    );

    if (!hasPermission) {
      return null;
    }

    // Get banned users with user details
    const bannedUsers = await dbConnection
      .select({
        banId: ServerBanSchema.id,
        userId: ServerBanSchema.userId,
        bannedById: ServerBanSchema.bannedById,
        reason: ServerBanSchema.reason,
        bannedAt: ServerBanSchema.bannedAt,
        username: UserSchema.username,
        avatar: UserSchema.avatar,
      })
      .from(ServerBanSchema)
      .innerJoin(UserSchema, eq(ServerBanSchema.userId, UserSchema.id))
      .where(eq(ServerBanSchema.serverId, serverId));

    return bannedUsers;
  } catch (error) {
    console.error("Error getting banned users:", error);
    return null;
  }
}

/**
 * Get all members of a server including their roles
 *
 * @param dbConnection - The database connection to use
 * @param serverId - The ID of the server
 * @returns Array of server members with user details and roles
 */
export async function getServerMembers(
  dbConnection: ReturnType<typeof drizzle>,
  serverId: string,
): Promise<any[]> {
  try {
    // Get server members with user details
    const members = await dbConnection
      .select({
        userId: ServerMembershipSchema.userId,
        joinedAt: ServerMembershipSchema.createdAt,
        username: UserSchema.username,
        avatar: UserSchema.avatar,
        status: UserSchema.status,
        statusMessage: UserSchema.statusMessage,
      })
      .from(ServerMembershipSchema)
      .innerJoin(UserSchema, eq(ServerMembershipSchema.userId, UserSchema.id))
      .where(eq(ServerMembershipSchema.serverId, serverId));

    // Get the server owner
    const server = await dbConnection
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    // Get all roles for the server
    const serverRoles = await dbConnection
      .select()
      .from(ServerRoleSchema)
      .where(eq(ServerRoleSchema.serverId, serverId));

    // Create a map of role IDs to role objects for quick lookup
    const roleMap = new Map();
    serverRoles.forEach((role) => roleMap.set(role.id, role));

    // Process each member to add roles and owner status
    const membersWithDetails = await Promise.all(
      members.map(async (member) => {
        // Get user's roles
        const userRoleIds = await dbConnection
          .select({ roleId: UserRoles.roleId })
          .from(UserRoles)
          .where(
            and(
              eq(UserRoles.userId, member.userId),
              eq(UserRoles.serverId, serverId),
            ),
          );

        // Map role IDs to actual role objects
        const roles = userRoleIds
          .map(({ roleId }) => roleMap.get(roleId))
          .filter((role) => role !== undefined); // Filter out any undefined roles

        // Return member with roles and owner status
        return {
          ...member,
          isOwner: server.length > 0 && server[0].ownerId === member.userId,
          roles: roles,
        };
      }),
    );

    return membersWithDetails;
  } catch (error) {
    console.error("Error getting server members:", error);
    return [];
  }
}

/**
 * Check if a user is banned from a server
 *
 * @param dbConnection - The database connection to use
 * @param serverId - The ID of the server
 * @param userId - The ID of the user
 * @returns True if the user is banned, false otherwise
 */
export async function isUserBanned(
  dbConnection: ReturnType<typeof drizzle>,
  serverId: string,
  userId: string,
): Promise<boolean> {
  try {
    const ban = await dbConnection
      .select()
      .from(ServerBanSchema)
      .where(
        and(
          eq(ServerBanSchema.serverId, serverId),
          eq(ServerBanSchema.userId, userId),
        ),
      )
      .limit(1);

    return ban.length > 0;
  } catch (error) {
    console.error("Error checking if user is banned:", error);
    return false;
  }
}
