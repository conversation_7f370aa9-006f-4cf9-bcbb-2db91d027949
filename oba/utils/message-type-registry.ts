import { z } from "zod";
import { EventTypes } from "../constants/eventTypes";
import type { IMessageTypeDefinition } from "../types/websocket-standardization.types";
import {
  SEND_MESSAGES,
  VIEW_CHANNEL,
  <PERSON><PERSON>GE_MESSAGES,
  <PERSON><PERSON>IN_VOICE,
  SPEAK_VOICE,
  MA<PERSON>GE_CHANNELS,
  MANAGE_SERVER,
  KICK_ACCEPT_DENY_USERS,
  USE_REACTIONS,
  MANAGE_ROLES,
} from "../constants/permissions";

/**
 * Message categories for grouping related message types
 */
export enum MessageCategory {
  AUTH = "auth",
  MESSAGE = "message",
  DIRECT_MESSAGE = "direct_message",
  VOICE = "voice",
  SERVER = "server",
  CHANNEL = "channel",
  CATEGORY = "category",
  ROLE = "role",
  USER = "user",
  FRIEND = "friend",
  MEMBER = "member",
  REACTION = "reaction",
  SYSTEM = "system",
  ERROR = "error",
  ORDERING = "ordering",
}

/**
 * Rate limiting groups for different types of operations
 */
export enum RateLimitGroup {
  MESSAGING = "messaging",
  VOICE = "voice",
  SERVER_MANAGEMENT = "server_management",
  CHANNEL_MANAGEMENT = "channel_management",
  USER_MANAGEMENT = "user_management",
  REACTIONS = "reactions",
  FRIENDS = "friends",
  SYSTEM = "system",
  BULK_OPERATIONS = "bulk_operations",
}

/**
 * Message direction types
 */
export type MessageDirection =
  | "client_to_server"
  | "server_to_client"
  | "bidirectional";

/**
 * Extended message type definition with additional metadata
 */
export interface IExtendedMessageTypeDefinition
  extends Omit<IMessageTypeDefinition, "requiresPermission"> {
  /** Message category for grouping */
  category: MessageCategory;
  /** Message direction */
  direction: MessageDirection;
  /** Whether authentication is required */
  requiresAuth: boolean;
  /** Required permission bitmask */
  requiresPermission?: bigint;
  /** Permission name for easier reference */
  permissionName?: string;
  /** Zod schema for data validation */
  dataSchema?: z.ZodSchema;
  /** Expected response type */
  responseType?: string;
  /** Rate limiting group */
  rateLimitGroup?: RateLimitGroup;
  /** Whether message supports correlation */
  supportsCorrelation?: boolean;
  /** Maximum message size in bytes */
  maxSize?: number;
  /** Message description */
  description?: string;
  /** Whether message can be sent to channels */
  supportsChannelTargeting?: boolean;
  /** Whether message can be sent to users */
  supportsUserTargeting?: boolean;
  /** Whether message supports server-wide broadcasting */
  supportsServerBroadcast?: boolean;
  /** Minimum role level required (if applicable) */
  minimumRoleLevel?: number;
  /** Whether message is deprecated */
  deprecated?: boolean;
  /** Version when message was introduced */
  introducedInVersion?: string;
  /** Version when message was deprecated (if applicable) */
  deprecatedInVersion?: string;
}

/**
 * Zod schemas for common message data structures
 */
export const MessageSchemas = {
  // Message-related schemas
  MessageSend: z.object({
    content: z.string().min(1).max(2000),
    channelId: z.string().uuid(),
    replyToId: z.string().uuid().optional(),
    attachments: z.array(z.string()).optional(),
  }),

  MessageUpdate: z.object({
    messageId: z.string().uuid(),
    content: z.string().min(1).max(2000),
  }),

  MessageDelete: z.object({
    messageId: z.string().uuid(),
  }),

  // Direct message schemas
  DirectMessageSend: z.object({
    content: z.string().min(1).max(2000),
    recipientId: z.string().uuid(),
    replyToId: z.string().uuid().optional(),
    attachments: z.array(z.string()).optional(),
  }),

  // Server-related schemas
  ServerCreate: z.object({
    name: z.string().min(1).max(100),
    description: z.string().max(500).optional(),
    icon: z.string().optional(),
  }),

  ServerUpdate: z.object({
    serverId: z.string().uuid(),
    name: z.string().min(1).max(100).optional(),
    description: z.string().max(500).optional(),
    icon: z.string().optional(),
  }),

  ServerJoin: z.object({
    serverId: z.string().uuid(),
    inviteCode: z.string().optional(),
  }),

  // Channel-related schemas
  ChannelCreate: z.object({
    name: z.string().min(1).max(100),
    type: z.enum(["text", "voice", "category"]),
    serverId: z.string().uuid(),
    categoryId: z.string().uuid().optional(),
    description: z.string().max(500).optional(),
  }),

  ChannelUpdate: z.object({
    channelId: z.string().uuid(),
    name: z.string().min(1).max(100).optional(),
    description: z.string().max(500).optional(),
  }),

  ChannelSubscribe: z.object({
    channelId: z.string().uuid(),
  }),

  // Voice-related schemas
  VoiceJoin: z.object({
    channelId: z.string().uuid(),
  }),

  VoiceData: z.object({
    data: z.instanceof(ArrayBuffer),
    channelId: z.string().uuid(),
  }),

  // User-related schemas
  UserUpdate: z.object({
    name: z.string().min(1).max(50).optional(),
    avatar: z.string().optional(),
    status: z.enum(["online", "away", "busy", "invisible"]).optional(),
  }),

  // Friend-related schemas
  FriendRequest: z.object({
    targetUserId: z.string().uuid(),
  }),

  FriendResponse: z.object({
    requestId: z.string().uuid(),
    accept: z.boolean(),
  }),

  // Reaction schemas
  ReactionAdd: z.object({
    messageId: z.string().uuid(),
    emoji: z.string(),
  }),

  // Role schemas
  RoleCreate: z.object({
    serverId: z.string().uuid(),
    name: z.string().min(1).max(100),
    color: z
      .string()
      .regex(/^#[0-9A-Fa-f]{6}$/)
      .optional(),
    permissions: z.string(), // Bitmask as string
  }),

  // Member management schemas
  MemberKick: z.object({
    serverId: z.string().uuid(),
    userId: z.string().uuid(),
    reason: z.string().max(500).optional(),
  }),

  // Ordering schemas
  ChannelsReorder: z.object({
    serverId: z.string().uuid(),
    channelIds: z.array(z.string().uuid()),
  }),
};

/**
 * Message type registry containing all supported message types
 */
export const MESSAGE_TYPE_REGISTRY = new Map<
  string,
  IExtendedMessageTypeDefinition
>([
  // Message events
  [
    EventTypes.MESSAGE_SEND,
    {
      type: EventTypes.MESSAGE_SEND,
      category: MessageCategory.MESSAGE,
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: SEND_MESSAGES,
      permissionName: "SEND_MESSAGES",
      dataSchema: MessageSchemas.MessageSend,
      responseType: EventTypes.MESSAGE_SENT,
      rateLimitGroup: RateLimitGroup.MESSAGING,
      supportsCorrelation: true,
      maxSize: 4096,
      description: "Send a message to a channel",
      supportsChannelTargeting: true,
      supportsUserTargeting: false,
      supportsServerBroadcast: false,
    },
  ],

  [
    EventTypes.MESSAGE_SENT,
    {
      type: EventTypes.MESSAGE_SENT,
      category: MessageCategory.MESSAGE,
      direction: "server_to_client",
      requiresAuth: false,
      dataSchema: z.object({
        messageId: z.string().uuid(),
        content: z.string(),
        authorId: z.string().uuid(),
        channelId: z.string().uuid(),
        timestamp: z.string(),
      }),
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a message was sent",
      supportsChannelTargeting: true,
      supportsUserTargeting: true,
      supportsServerBroadcast: true,
    },
  ],

  [
    EventTypes.MESSAGE_UPDATED,
    {
      type: EventTypes.MESSAGE_UPDATED,
      category: MessageCategory.MESSAGE,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a message was updated",
      supportsChannelTargeting: true,
      supportsUserTargeting: true,
      supportsServerBroadcast: true,
    },
  ],

  [
    EventTypes.MESSAGE_DELETED,
    {
      type: EventTypes.MESSAGE_DELETED,
      category: MessageCategory.MESSAGE,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a message was deleted",
      supportsChannelTargeting: true,
      supportsUserTargeting: true,
      supportsServerBroadcast: true,
    },
  ],

  [
    EventTypes.MESSAGE_UPDATE,
    {
      type: EventTypes.MESSAGE_UPDATE,
      category: MessageCategory.MESSAGE,
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: SEND_MESSAGES,
      permissionName: "SEND_MESSAGES",
      dataSchema: MessageSchemas.MessageUpdate,
      responseType: EventTypes.MESSAGE_UPDATED,
      rateLimitGroup: RateLimitGroup.MESSAGING,
      supportsCorrelation: true,
      maxSize: 4096,
      description: "Update an existing message",
    },
  ],

  [
    EventTypes.MESSAGE_DELETE,
    {
      type: EventTypes.MESSAGE_DELETE,
      category: MessageCategory.MESSAGE,
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: MANAGE_MESSAGES,
      permissionName: "MANAGE_MESSAGES",
      dataSchema: MessageSchemas.MessageDelete,
      responseType: EventTypes.MESSAGE_DELETED,
      rateLimitGroup: RateLimitGroup.MESSAGING,
      supportsCorrelation: true,
      description: "Delete a message",
    },
  ],

  // Direct message events
  [
    EventTypes.DIRECT_MESSAGE_SEND,
    {
      type: EventTypes.DIRECT_MESSAGE_SEND,
      category: MessageCategory.DIRECT_MESSAGE,
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: MessageSchemas.DirectMessageSend,
      responseType: EventTypes.DIRECT_MESSAGE_SENT,
      rateLimitGroup: RateLimitGroup.MESSAGING,
      supportsCorrelation: true,
      maxSize: 4096,
      description: "Send a direct message to another user",
      supportsUserTargeting: true,
    },
  ],

  [
    EventTypes.DIRECT_MESSAGE_SENT,
    {
      type: EventTypes.DIRECT_MESSAGE_SENT,
      category: MessageCategory.DIRECT_MESSAGE,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a direct message was sent",
      supportsUserTargeting: true,
    },
  ],

  // Server events
  [
    EventTypes.SERVER_CREATE,
    {
      type: EventTypes.SERVER_CREATE,
      category: MessageCategory.SERVER,
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: MessageSchemas.ServerCreate,
      responseType: EventTypes.SERVER_CREATED,
      rateLimitGroup: RateLimitGroup.SERVER_MANAGEMENT,
      supportsCorrelation: true,
      maxSize: 2048,
      description: "Create a new server",
    },
  ],

  [
    EventTypes.SERVER_CREATED,
    {
      type: EventTypes.SERVER_CREATED,
      category: MessageCategory.SERVER,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a server was created",
      supportsUserTargeting: true,
    },
  ],

  [
    EventTypes.SERVER_UPDATE,
    {
      type: EventTypes.SERVER_UPDATE,
      category: MessageCategory.SERVER,
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: MANAGE_SERVER,
      permissionName: "MANAGE_SERVER",
      dataSchema: MessageSchemas.ServerUpdate,
      responseType: EventTypes.SERVER_UPDATED,
      rateLimitGroup: RateLimitGroup.SERVER_MANAGEMENT,
      supportsCorrelation: true,
      description: "Update server settings",
    },
  ],

  [
    EventTypes.SERVER_UPDATED,
    {
      type: EventTypes.SERVER_UPDATED,
      category: MessageCategory.SERVER,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a server was updated",
      supportsServerBroadcast: true,
    },
  ],

  [
    EventTypes.SERVER_JOIN,
    {
      type: EventTypes.SERVER_JOIN,
      category: MessageCategory.SERVER,
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: MessageSchemas.ServerJoin,
      responseType: EventTypes.SERVER_JOINED,
      rateLimitGroup: RateLimitGroup.SERVER_MANAGEMENT,
      supportsCorrelation: true,
      description: "Join a server",
    },
  ],

  [
    EventTypes.SERVER_JOINED,
    {
      type: EventTypes.SERVER_JOINED,
      category: MessageCategory.SERVER,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a user joined a server",
      supportsServerBroadcast: true,
    },
  ],

  // Channel events
  [
    EventTypes.CHANNEL_CREATE,
    {
      type: EventTypes.CHANNEL_CREATE,
      category: MessageCategory.CHANNEL,
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: MANAGE_CHANNELS,
      permissionName: "MANAGE_CHANNELS",
      dataSchema: MessageSchemas.ChannelCreate,
      responseType: EventTypes.CHANNEL_CREATED,
      rateLimitGroup: RateLimitGroup.CHANNEL_MANAGEMENT,
      supportsCorrelation: true,
      description: "Create a new channel",
    },
  ],

  [
    EventTypes.CHANNEL_CREATED,
    {
      type: EventTypes.CHANNEL_CREATED,
      category: MessageCategory.CHANNEL,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a channel was created",
      supportsServerBroadcast: true,
    },
  ],

  [
    EventTypes.CHANNEL_UPDATE,
    {
      type: EventTypes.CHANNEL_UPDATE,
      category: MessageCategory.CHANNEL,
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: MANAGE_CHANNELS,
      permissionName: "MANAGE_CHANNELS",
      dataSchema: MessageSchemas.ChannelUpdate,
      responseType: EventTypes.CHANNEL_UPDATED,
      rateLimitGroup: RateLimitGroup.CHANNEL_MANAGEMENT,
      supportsCorrelation: true,
      description: "Update channel settings",
    },
  ],

  [
    EventTypes.CHANNEL_UPDATED,
    {
      type: EventTypes.CHANNEL_UPDATED,
      category: MessageCategory.CHANNEL,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a channel was updated",
      supportsServerBroadcast: true,
    },
  ],

  [
    EventTypes.CHANNEL_SUBSCRIBE,
    {
      type: EventTypes.CHANNEL_SUBSCRIBE,
      category: MessageCategory.CHANNEL,
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: VIEW_CHANNEL,
      permissionName: "VIEW_CHANNEL",
      dataSchema: MessageSchemas.ChannelSubscribe,
      responseType: EventTypes.CHANNEL_SUBSCRIBED,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Subscribe to channel events",
    },
  ],

  [
    EventTypes.CHANNEL_SUBSCRIBED,
    {
      type: EventTypes.CHANNEL_SUBSCRIBED,
      category: MessageCategory.CHANNEL,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Confirmation that channel subscription was successful",
      supportsUserTargeting: true,
    },
  ],

  // Voice events
  [
    EventTypes.VOICE_JOIN,
    {
      type: EventTypes.VOICE_JOIN,
      category: MessageCategory.VOICE,
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: JOIN_VOICE,
      permissionName: "JOIN_VOICE",
      dataSchema: MessageSchemas.VoiceJoin,
      responseType: EventTypes.VOICE_JOINED,
      rateLimitGroup: RateLimitGroup.VOICE,
      supportsCorrelation: true,
      description: "Join a voice channel",
    },
  ],

  [
    EventTypes.VOICE_JOINED,
    {
      type: EventTypes.VOICE_JOINED,
      category: MessageCategory.VOICE,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Confirmation that voice channel was joined",
      supportsChannelTargeting: true,
      supportsUserTargeting: true,
    },
  ],

  [
    EventTypes.VOICE_DATA_SEND,
    {
      type: EventTypes.VOICE_DATA_SEND,
      category: MessageCategory.VOICE,
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: SPEAK_VOICE,
      permissionName: "SPEAK_VOICE",
      dataSchema: MessageSchemas.VoiceData,
      rateLimitGroup: RateLimitGroup.VOICE,
      maxSize: 65536, // 64KB for voice data
      description: "Send voice data to a voice channel",
      supportsChannelTargeting: true,
    },
  ],

  // User events
  [
    EventTypes.USER_UPDATE,
    {
      type: EventTypes.USER_UPDATE,
      category: MessageCategory.USER,
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: MessageSchemas.UserUpdate,
      responseType: EventTypes.USER_UPDATED,
      rateLimitGroup: RateLimitGroup.USER_MANAGEMENT,
      supportsCorrelation: true,
      description: "Update user profile",
    },
  ],

  [
    EventTypes.USER_UPDATED,
    {
      type: EventTypes.USER_UPDATED,
      category: MessageCategory.USER,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a user profile was updated",
      supportsUserTargeting: true,
      supportsServerBroadcast: true,
    },
  ],

  // Friend events
  [
    EventTypes.FRIEND_REQUEST_SEND,
    {
      type: EventTypes.FRIEND_REQUEST_SEND,
      category: MessageCategory.FRIEND,
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: MessageSchemas.FriendRequest,
      responseType: EventTypes.FRIEND_REQUEST_SENT,
      rateLimitGroup: RateLimitGroup.FRIENDS,
      supportsCorrelation: true,
      description: "Send a friend request",
    },
  ],

  [
    EventTypes.FRIEND_REQUEST_SENT,
    {
      type: EventTypes.FRIEND_REQUEST_SENT,
      category: MessageCategory.FRIEND,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a friend request was sent",
      supportsUserTargeting: true,
    },
  ],

  [
    EventTypes.FRIEND_REQUEST_ACCEPT,
    {
      type: EventTypes.FRIEND_REQUEST_ACCEPT,
      category: MessageCategory.FRIEND,
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: MessageSchemas.FriendResponse,
      responseType: EventTypes.FRIEND_REQUEST_ACCEPTED,
      rateLimitGroup: RateLimitGroup.FRIENDS,
      supportsCorrelation: true,
      description: "Accept a friend request",
    },
  ],

  [
    EventTypes.FRIEND_REQUEST_ACCEPTED,
    {
      type: EventTypes.FRIEND_REQUEST_ACCEPTED,
      category: MessageCategory.FRIEND,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a friend request was accepted",
      supportsUserTargeting: true,
    },
  ],

  // Reaction events
  [
    EventTypes.REACTION_ADD,
    {
      type: EventTypes.REACTION_ADD,
      category: MessageCategory.REACTION,
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: USE_REACTIONS,
      permissionName: "USE_REACTIONS",
      dataSchema: MessageSchemas.ReactionAdd,
      responseType: EventTypes.REACTION_ADDED,
      rateLimitGroup: RateLimitGroup.REACTIONS,
      supportsCorrelation: true,
      description: "Add a reaction to a message",
    },
  ],

  [
    EventTypes.REACTION_ADDED,
    {
      type: EventTypes.REACTION_ADDED,
      category: MessageCategory.REACTION,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a reaction was added",
      supportsChannelTargeting: true,
      supportsUserTargeting: true,
    },
  ],

  // Role events
  [
    EventTypes.ROLE_CREATE,
    {
      type: EventTypes.ROLE_CREATE,
      category: MessageCategory.ROLE,
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: MANAGE_ROLES,
      permissionName: "MANAGE_ROLES",
      dataSchema: MessageSchemas.RoleCreate,
      responseType: EventTypes.ROLE_CREATED,
      rateLimitGroup: RateLimitGroup.SERVER_MANAGEMENT,
      supportsCorrelation: true,
      description: "Create a new role",
    },
  ],

  [
    EventTypes.ROLE_CREATED,
    {
      type: EventTypes.ROLE_CREATED,
      category: MessageCategory.ROLE,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a role was created",
      supportsServerBroadcast: true,
    },
  ],

  // Member events
  [
    EventTypes.MEMBER_KICK,
    {
      type: EventTypes.MEMBER_KICK,
      category: MessageCategory.MEMBER,
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: KICK_ACCEPT_DENY_USERS,
      permissionName: "KICK_ACCEPT_DENY_USERS",
      dataSchema: MessageSchemas.MemberKick,
      responseType: EventTypes.MEMBER_KICKED,
      rateLimitGroup: RateLimitGroup.USER_MANAGEMENT,
      supportsCorrelation: true,
      description: "Kick a member from the server",
    },
  ],

  [
    EventTypes.MEMBER_KICKED,
    {
      type: EventTypes.MEMBER_KICKED,
      category: MessageCategory.MEMBER,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that a member was kicked",
      supportsServerBroadcast: true,
    },
  ],

  // Ordering events
  [
    EventTypes.CHANNELS_REORDER,
    {
      type: EventTypes.CHANNELS_REORDER,
      category: MessageCategory.ORDERING,
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: MANAGE_CHANNELS,
      permissionName: "MANAGE_CHANNELS",
      dataSchema: MessageSchemas.ChannelsReorder,
      responseType: EventTypes.CHANNELS_REORDERED,
      rateLimitGroup: RateLimitGroup.BULK_OPERATIONS,
      supportsCorrelation: true,
      description: "Reorder channels in a server",
    },
  ],

  [
    EventTypes.CHANNELS_REORDERED,
    {
      type: EventTypes.CHANNELS_REORDERED,
      category: MessageCategory.ORDERING,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      supportsCorrelation: true,
      description: "Notification that channels were reordered",
      supportsServerBroadcast: true,
    },
  ],

  // Error event
  [
    EventTypes.ERROR,
    {
      type: EventTypes.ERROR,
      category: MessageCategory.ERROR,
      direction: "server_to_client",
      requiresAuth: false,
      rateLimitGroup: RateLimitGroup.SYSTEM,
      description: "Error notification",
      supportsCorrelation: true,
    },
  ],
]);

/**
 * Rate limiting configuration for different groups
 */
export const RATE_LIMIT_CONFIG = {
  [RateLimitGroup.MESSAGING]: {
    maxRequests: 30,
    windowMs: 60000, // 1 minute
  },
  [RateLimitGroup.VOICE]: {
    maxRequests: 1000,
    windowMs: 60000, // 1 minute (high limit for voice data)
  },
  [RateLimitGroup.SERVER_MANAGEMENT]: {
    maxRequests: 10,
    windowMs: 60000, // 1 minute
  },
  [RateLimitGroup.CHANNEL_MANAGEMENT]: {
    maxRequests: 20,
    windowMs: 60000, // 1 minute
  },
  [RateLimitGroup.USER_MANAGEMENT]: {
    maxRequests: 15,
    windowMs: 60000, // 1 minute
  },
  [RateLimitGroup.REACTIONS]: {
    maxRequests: 50,
    windowMs: 60000, // 1 minute
  },
  [RateLimitGroup.FRIENDS]: {
    maxRequests: 10,
    windowMs: 60000, // 1 minute
  },
  [RateLimitGroup.SYSTEM]: {
    maxRequests: 1000,
    windowMs: 60000, // 1 minute (high limit for system messages)
  },
  [RateLimitGroup.BULK_OPERATIONS]: {
    maxRequests: 5,
    windowMs: 60000, // 1 minute
  },
};

/**
 * Message Type Registry Manager
 * Provides utilities for working with the message type registry
 */
export class MessageTypeRegistry {
  private static registry = MESSAGE_TYPE_REGISTRY;

  /**
   * Get message type definition by type
   */
  public static getMessageType(
    type: string,
  ): IExtendedMessageTypeDefinition | undefined {
    return this.registry.get(type);
  }

  /**
   * Check if a message type exists
   */
  public static hasMessageType(type: string): boolean {
    return this.registry.has(type);
  }

  /**
   * Get all message types in a category
   */
  public static getMessageTypesByCategory(
    category: MessageCategory,
  ): IExtendedMessageTypeDefinition[] {
    return Array.from(this.registry.values()).filter(
      (def) => def.category === category,
    );
  }

  /**
   * Get all message types that require a specific permission
   */
  public static getMessageTypesByPermission(
    permission: bigint,
  ): IExtendedMessageTypeDefinition[] {
    return Array.from(this.registry.values()).filter(
      (def) => def.requiresPermission === permission,
    );
  }

  /**
   * Get all message types in a rate limit group
   */
  public static getMessageTypesByRateLimit(
    group: RateLimitGroup,
  ): IExtendedMessageTypeDefinition[] {
    return Array.from(this.registry.values()).filter(
      (def) => def.rateLimitGroup === group,
    );
  }

  /**
   * Get rate limit configuration for a message type
   */
  public static getRateLimitConfig(
    type: string,
  ): { maxRequests: number; windowMs: number } | undefined {
    const messageType = this.getMessageType(type);
    if (!messageType?.rateLimitGroup) return undefined;

    return RATE_LIMIT_CONFIG[messageType.rateLimitGroup];
  }

  /**
   * Check if a message type requires authentication
   */
  public static requiresAuth(type: string): boolean {
    const messageType = this.getMessageType(type);
    return messageType?.requiresAuth ?? false;
  }

  /**
   * Get required permission for a message type
   */
  public static getRequiredPermission(type: string): bigint | undefined {
    const messageType = this.getMessageType(type);
    return messageType?.requiresPermission;
  }

  /**
   * Get data schema for a message type
   */
  public static getDataSchema(type: string): z.ZodSchema | undefined {
    const messageType = this.getMessageType(type);
    return messageType?.dataSchema;
  }

  /**
   * Check if a message type supports correlation
   */
  public static supportsCorrelation(type: string): boolean {
    const messageType = this.getMessageType(type);
    return messageType?.supportsCorrelation ?? false;
  }

  /**
   * Get maximum message size for a message type
   */
  public static getMaxSize(type: string): number | undefined {
    const messageType = this.getMessageType(type);
    return messageType?.maxSize;
  }

  /**
   * Get expected response type for a message type
   */
  public static getResponseType(type: string): string | undefined {
    const messageType = this.getMessageType(type);
    return messageType?.responseType;
  }

  /**
   * Register a new message type
   */
  public static registerMessageType(
    type: string,
    definition: IExtendedMessageTypeDefinition,
  ): void {
    this.registry.set(type, definition);
  }

  /**
   * Unregister a message type
   */
  public static unregisterMessageType(type: string): boolean {
    return this.registry.delete(type);
  }

  /**
   * Get all registered message types
   */
  public static getAllMessageTypes(): Map<
    string,
    IExtendedMessageTypeDefinition
  > {
    return new Map(this.registry);
  }

  /**
   * Get message types by direction
   */
  public static getMessageTypesByDirection(
    direction: MessageDirection,
  ): IExtendedMessageTypeDefinition[] {
    return Array.from(this.registry.values()).filter(
      (def) => def.direction === direction,
    );
  }

  /**
   * Get client-to-server message types
   */
  public static getClientToServerMessageTypes(): IExtendedMessageTypeDefinition[] {
    return this.getMessageTypesByDirection("client_to_server");
  }

  /**
   * Get server-to-client message types
   */
  public static getServerToClientMessageTypes(): IExtendedMessageTypeDefinition[] {
    return this.getMessageTypesByDirection("server_to_client");
  }

  /**
   * Get bidirectional message types
   */
  public static getBidirectionalMessageTypes(): IExtendedMessageTypeDefinition[] {
    return this.getMessageTypesByDirection("bidirectional");
  }

  /**
   * Validate message type definition
   */
  public static validateMessageTypeDefinition(
    definition: IExtendedMessageTypeDefinition,
  ): boolean {
    // Basic validation
    if (!definition.type || !definition.category || !definition.direction) {
      return false;
    }

    // Check if category is valid
    if (!Object.values(MessageCategory).includes(definition.category)) {
      return false;
    }

    // Check if direction is valid
    const validDirections: MessageDirection[] = [
      "client_to_server",
      "server_to_client",
      "bidirectional",
    ];
    if (!validDirections.includes(definition.direction)) {
      return false;
    }

    // Check if rate limit group is valid (if provided)
    if (
      definition.rateLimitGroup &&
      !Object.values(RateLimitGroup).includes(definition.rateLimitGroup)
    ) {
      return false;
    }

    return true;
  }
}
