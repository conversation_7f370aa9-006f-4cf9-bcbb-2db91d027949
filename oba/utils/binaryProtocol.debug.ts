/**
 * Binary Protocol Utilities with Enhanced Debugging
 *
 * This file contains utilities for working with the binary voice data protocol.
 * The protocol format is:
 * [Header Length (4 bytes)][JSO<PERSON> Header][Binary Audio Data]
 */

/**
 * Parse a binary WebSocket message into metadata and audio data
 *
 * @param binaryData The binary WebSocket message
 * @returns Object containing metadata and audio data
 */
export function parseVoiceMessage(binaryData: ArrayBuffer): {
  metadata: any;
  audioData: ArrayBuffer;
} {
  console.log(
    `VOICE-DEBUG: Parsing binary message of size ${binaryData.byteLength} bytes`,
  );

  try {
    // Create a DataView to read the header length (first 4 bytes)
    const headerLengthView = new DataView(binaryData, 0, 4);
    const headerLength = headerLengthView.getUint32(0, true); // true for little-endian

    console.log(`VOICE-DEBUG: Header length: ${headerLength} bytes`);

    if (headerLength <= 0 || headerLength > binaryData.byteLength - 4) {
      console.error(`VOICE-DEBUG: Invalid header length: ${headerLength}`);
      throw new Error(`Invalid header length: ${headerLength}`);
    }

    // Extract the header as a string
    const headerBytes = new Uint8Array(binaryData, 4, headerLength);
    const headerString = new TextDecoder().decode(headerBytes);

    console.log(`VOICE-DEBUG: Header string: ${headerString}`);

    // Parse the header JSON
    const metadata = JSON.parse(headerString);
    console.log("VOICE-DEBUG: Parsed metadata:", metadata);

    // Extract the audio data
    const audioData = binaryData.slice(4 + headerLength);
    console.log(`VOICE-DEBUG: Audio data size: ${audioData.byteLength} bytes`);

    return { metadata, audioData };
  } catch (error) {
    console.error("VOICE-DEBUG: Error parsing voice message:", error);
    // Return a default object to prevent crashes
    return {
      metadata: {
        type: "voice_data",
        userId: "unknown",
        timestamp: Date.now(),
        sequence: 0,
      },
      audioData: new ArrayBuffer(0),
    };
  }
}

/**
 * Create a binary WebSocket message from metadata and audio data
 *
 * @param metadata The metadata object
 * @param audioData The binary audio data
 * @returns Binary message ready to send over WebSocket
 */
export function createVoiceMessage(
  metadata: any,
  audioData: ArrayBuffer,
): ArrayBuffer {
  console.log("VOICE-DEBUG: Creating voice message with metadata:", metadata);
  console.log(`VOICE-DEBUG: Audio data size: ${audioData.byteLength} bytes`);

  try {
    // Convert metadata to JSON string
    const headerString = JSON.stringify(metadata);
    console.log(`VOICE-DEBUG: Header string: ${headerString}`);

    // Convert header string to bytes
    const headerBytes = new TextEncoder().encode(headerString);
    console.log(`VOICE-DEBUG: Header bytes length: ${headerBytes.byteLength}`);

    // Create a buffer for the header length (4 bytes) + header + audio data
    const message = new ArrayBuffer(
      4 + headerBytes.byteLength + audioData.byteLength,
    );
    console.log(`VOICE-DEBUG: Total message size: ${message.byteLength} bytes`);

    // Write the header length
    const headerLengthView = new DataView(message, 0, 4);
    headerLengthView.setUint32(0, headerBytes.byteLength, true); // true for little-endian

    // Write the header
    new Uint8Array(message, 4, headerBytes.byteLength).set(headerBytes);

    // Write the audio data
    new Uint8Array(
      message,
      4 + headerBytes.byteLength,
      audioData.byteLength,
    ).set(new Uint8Array(audioData));

    return message;
  } catch (error) {
    console.error("VOICE-DEBUG: Error creating voice message:", error);
    // Return an empty buffer to prevent crashes
    return new ArrayBuffer(0);
  }
}

/**
 * Validate voice metadata
 *
 * @param metadata The metadata object to validate
 * @returns True if valid, false otherwise
 */
export function validateVoiceMetadata(metadata: any): boolean {
  console.log("VOICE-DEBUG: Validating voice metadata:", metadata);

  if (!metadata) {
    console.error("VOICE-DEBUG: Metadata is null or undefined");
    return false;
  }

  if (typeof metadata.type !== "string") {
    console.error(`VOICE-DEBUG: Invalid metadata.type: ${metadata.type}`);
    return false;
  }

  if (metadata.type !== "voice_data") {
    console.error(`VOICE-DEBUG: Unexpected metadata.type: ${metadata.type}`);
    return false;
  }

  if (typeof metadata.userId !== "string") {
    console.error(`VOICE-DEBUG: Invalid metadata.userId: ${metadata.userId}`);
    return false;
  }

  if (typeof metadata.timestamp !== "number") {
    console.error(
      `VOICE-DEBUG: Invalid metadata.timestamp: ${metadata.timestamp}`,
    );
    return false;
  }

  if (typeof metadata.sequence !== "number") {
    console.error(
      `VOICE-DEBUG: Invalid metadata.sequence: ${metadata.sequence}`,
    );
    return false;
  }

  console.log("VOICE-DEBUG: Voice metadata is valid");
  return true;
}

/**
 * Create voice metadata object
 *
 * @param userId The ID of the user sending the audio data
 * @param sequence The sequence number for packet ordering
 * @param format The audio format (e.g., "opus")
 * @param sampleRate The sample rate of the audio in Hz
 * @param channels The number of audio channels
 * @param frameSize The number of samples per frame
 * @returns Voice metadata object
 */
export function createVoiceMetadata(
  userId: string,
  sequence: number,
  format: string = "opus",
  sampleRate: number = 48000,
  channels: number = 1,
  frameSize: number = 960,
): any {
  const metadata = {
    type: "voice_data",
    userId,
    timestamp: Date.now(),
    sequence,
    format,
    sampleRate,
    channels,
    frameSize,
  };

  console.log("VOICE-DEBUG: Created voice metadata:", metadata);
  return metadata;
}
