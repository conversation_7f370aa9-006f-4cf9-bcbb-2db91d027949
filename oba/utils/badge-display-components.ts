import { BadgeType, UserBadge, BadgeCollection, UserCollectionProgress } from "../types/badge.types";
import { BadgeVisualRenderer, BadgeRenderOptions } from "./badge-visual-renderer";

/**
 * Badge Display Components - Responsive badge display utilities
 */

export interface BadgeDisplayOptions {
  maxVisible?: number;
  showTooltips?: boolean;
  responsive?: boolean;
  groupByCollection?: boolean;
  showProgress?: boolean;
  accessibility?: boolean;
  theme?: 'light' | 'dark' | 'auto';
}

export interface ResponsiveBreakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
}

export interface BadgeGridConfig {
  columns: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  gap: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  badgeSize: {
    mobile: 'small' | 'medium';
    tablet: 'medium' | 'large';
    desktop: 'medium' | 'large' | 'xl';
  };
}

export interface BadgeDisplayResult {
  html: string;
  css: string;
  javascript?: string;
  accessibility: {
    totalBadges: number;
    visibleBadges: number;
    hiddenBadges: number;
    ariaLabel: string;
  };
}

/**
 * Default responsive configuration
 */
const DEFAULT_BREAKPOINTS: ResponsiveBreakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1200
};

const DEFAULT_GRID_CONFIG: BadgeGridConfig = {
  columns: {
    mobile: 3,
    tablet: 4,
    desktop: 5
  },
  gap: {
    mobile: 8,
    tablet: 12,
    desktop: 16
  },
  badgeSize: {
    mobile: 'small',
    tablet: 'medium',
    desktop: 'large'
  }
};

/**
 * Badge Display Components Class
 */
export class BadgeDisplayComponents {
  /**
   * Create a responsive badge grid display
   */
  static createBadgeGrid(
    userBadges: UserBadge[],
    options: BadgeDisplayOptions = {},
    gridConfig: BadgeGridConfig = DEFAULT_GRID_CONFIG
  ): BadgeDisplayResult {
    const {
      maxVisible = 10,
      showTooltips = true,
      responsive = true,
      groupByCollection = false,
      showProgress = false,
      accessibility = true,
      theme = 'auto'
    } = options;

    // Filter and sort badges
    const visibleBadges = userBadges
      .filter(badge => badge.isVisible)
      .slice(0, maxVisible);

    const hiddenCount = Math.max(0, userBadges.length - maxVisible);

    // Group badges by collection if requested
    const badgeGroups = groupByCollection 
      ? this.groupBadgesByCollection(visibleBadges)
      : [{ collection: null, badges: visibleBadges }];

    // Generate unique container ID
    const containerId = `badge-grid-${Date.now()}`;

    // Build HTML structure
    const html = this.buildBadgeGridHTML(badgeGroups, containerId, {
      showTooltips,
      showProgress,
      hiddenCount,
      theme
    });

    // Generate responsive CSS
    const css = this.generateResponsiveCSS(containerId, gridConfig, theme);

    // Generate JavaScript for interactivity
    const javascript = showTooltips ? this.generateTooltipJS(containerId) : undefined;

    // Generate accessibility information
    const accessibilityInfo = accessibility ? {
      totalBadges: userBadges.length,
      visibleBadges: visibleBadges.length,
      hiddenBadges: hiddenCount,
      ariaLabel: `User has ${userBadges.length} badges, showing ${visibleBadges.length}`
    } : {
      totalBadges: 0,
      visibleBadges: 0,
      hiddenBadges: 0,
      ariaLabel: ''
    };

    return {
      html,
      css,
      javascript,
      accessibility: accessibilityInfo
    };
  }

  /**
   * Create a compact badge list for profile cards
   */
  static createCompactBadgeList(
    userBadges: UserBadge[],
    maxVisible: number = 5
  ): BadgeDisplayResult {
    const visibleBadges = userBadges
      .filter(badge => badge.isVisible)
      .slice(0, maxVisible);

    const hiddenCount = Math.max(0, userBadges.length - maxVisible);
    const containerId = `badge-list-${Date.now()}`;

    // Build compact HTML
    const badgeElements = visibleBadges.map(userBadge => {
      if (!userBadge.badgeType) return '';

      const renderResult = BadgeVisualRenderer.renderBadgeSVG(userBadge.badgeType, {
        size: 'small',
        animation: false,
        accessibility: true
      });

      return `
        <div class="badge-item" 
             data-badge-id="${userBadge.badgeTypeId}"
             title="${renderResult.accessibility.description}">
          ${renderResult.svg}
        </div>
      `;
    }).join('');

    const moreIndicator = hiddenCount > 0 ? `
      <div class="badge-more-indicator" title="And ${hiddenCount} more badges">
        <span class="badge-count">+${hiddenCount}</span>
      </div>
    ` : '';

    const html = `
      <div id="${containerId}" class="badge-list-compact" 
           role="list" 
           aria-label="User badges (${visibleBadges.length} of ${userBadges.length})">
        ${badgeElements}
        ${moreIndicator}
      </div>
    `;

    const css = `
      .badge-list-compact {
        display: flex;
        align-items: center;
        gap: 4px;
        flex-wrap: wrap;
      }
      
      .badge-item {
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.2s ease;
      }
      
      .badge-item:hover {
        transform: scale(1.1);
        z-index: 10;
        position: relative;
      }
      
      .badge-more-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        font-size: 10px;
        font-weight: bold;
        color: #666;
        cursor: pointer;
      }
      
      .badge-more-indicator:hover {
        background: rgba(0, 0, 0, 0.2);
        transform: scale(1.1);
      }
    `;

    return {
      html,
      css,
      accessibility: {
        totalBadges: userBadges.length,
        visibleBadges: visibleBadges.length,
        hiddenBadges: hiddenCount,
        ariaLabel: `User badges: ${visibleBadges.length} visible, ${hiddenCount} hidden`
      }
    };
  }

  /**
   * Create a badge collection progress display
   */
  static createCollectionProgressDisplay(
    collection: BadgeCollection,
    progress: UserCollectionProgress,
    earnedBadges: UserBadge[]
  ): BadgeDisplayResult {
    const containerId = `collection-progress-${Date.now()}`;
    const progressPercentage = Math.round((progress.badgesEarned / progress.totalBadges) * 100);

    // Create badge slots showing earned and unearned badges
    const badgeSlots = Array.from({ length: progress.totalBadges }, (_, index) => {
      const earnedBadge = earnedBadges[index];
      const isEarned = !!earnedBadge;

      if (isEarned && earnedBadge.badgeType) {
        const renderResult = BadgeVisualRenderer.renderBadgeSVG(earnedBadge.badgeType, {
          size: 'medium',
          animation: true,
          accessibility: true
        });

        return `
          <div class="badge-slot earned" 
               data-badge-id="${earnedBadge.badgeTypeId}"
               title="${renderResult.accessibility.description}">
            ${renderResult.svg}
          </div>
        `;
      } else {
        return `
          <div class="badge-slot unearned" 
               title="Badge ${index + 1} - Not yet earned">
            <div class="badge-placeholder">
              <span class="badge-number">${index + 1}</span>
            </div>
          </div>
        `;
      }
    }).join('');

    const html = `
      <div id="${containerId}" class="collection-progress" 
           role="region" 
           aria-label="Badge collection progress">
        <div class="collection-header">
          <h3 class="collection-name">${collection.name}</h3>
          <div class="collection-stats">
            <span class="progress-text">${progress.badgesEarned}/${progress.totalBadges}</span>
            <div class="progress-bar">
              <div class="progress-fill" style="width: ${progressPercentage}%"></div>
            </div>
          </div>
        </div>
        <div class="collection-description">
          ${collection.description}
        </div>
        <div class="badge-slots" role="list" aria-label="Collection badges">
          ${badgeSlots}
        </div>
        ${progress.isCompleted ? `
          <div class="completion-badge">
            <span class="completion-text">Collection Complete! 🎉</span>
          </div>
        ` : ''}
      </div>
    `;

    const css = `
      .collection-progress {
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 16px;
        background: #fafafa;
        margin-bottom: 16px;
      }
      
      .collection-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }
      
      .collection-name {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
      
      .collection-stats {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .progress-text {
        font-size: 14px;
        font-weight: 500;
        color: #666;
      }
      
      .progress-bar {
        width: 100px;
        height: 6px;
        background: #e0e0e0;
        border-radius: 3px;
        overflow: hidden;
      }
      
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #4CAF50, #8BC34A);
        transition: width 0.3s ease;
      }
      
      .collection-description {
        font-size: 14px;
        color: #666;
        margin-bottom: 16px;
        line-height: 1.4;
      }
      
      .badge-slots {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(48px, 1fr));
        gap: 12px;
        margin-bottom: 16px;
      }
      
      .badge-slot {
        display: flex;
        align-items: center;
        justify-content: center;
        aspect-ratio: 1;
        border-radius: 8px;
        transition: transform 0.2s ease;
      }
      
      .badge-slot.earned:hover {
        transform: scale(1.1);
      }
      
      .badge-slot.unearned {
        background: #f0f0f0;
        border: 2px dashed #ccc;
      }
      
      .badge-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        color: #999;
      }
      
      .badge-number {
        font-size: 12px;
        font-weight: 500;
      }
      
      .completion-badge {
        text-align: center;
        padding: 12px;
        background: linear-gradient(135deg, #4CAF50, #8BC34A);
        color: white;
        border-radius: 8px;
        font-weight: 600;
      }
      
      @media (max-width: 768px) {
        .collection-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
        
        .badge-slots {
          grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
          gap: 8px;
        }
      }
    `;

    return {
      html,
      css,
      accessibility: {
        totalBadges: progress.totalBadges,
        visibleBadges: progress.badgesEarned,
        hiddenBadges: progress.totalBadges - progress.badgesEarned,
        ariaLabel: `Collection ${collection.name}: ${progress.badgesEarned} of ${progress.totalBadges} badges earned`
      }
    };
  }

  /**
   * Group badges by collection
   */
  private static groupBadgesByCollection(userBadges: UserBadge[]): Array<{
    collection: BadgeCollection | null;
    badges: UserBadge[];
  }> {
    const groups = new Map<string, UserBadge[]>();
    const standalone: UserBadge[] = [];

    userBadges.forEach(badge => {
      if (badge.collectionId) {
        const key = badge.collectionId;
        if (!groups.has(key)) {
          groups.set(key, []);
        }
        groups.get(key)!.push(badge);
      } else {
        standalone.push(badge);
      }
    });

    const result: Array<{ collection: BadgeCollection | null; badges: UserBadge[] }> = [];

    // Add collection groups
    groups.forEach((badges, collectionId) => {
      // Note: In a real implementation, you'd fetch the collection data
      result.push({
        collection: null, // Would be populated with actual collection data
        badges
      });
    });

    // Add standalone badges
    if (standalone.length > 0) {
      result.push({
        collection: null,
        badges: standalone
      });
    }

    return result;
  }

  /**
   * Build badge grid HTML structure
   */
  private static buildBadgeGridHTML(
    badgeGroups: Array<{ collection: BadgeCollection | null; badges: UserBadge[] }>,
    containerId: string,
    options: {
      showTooltips: boolean;
      showProgress: boolean;
      hiddenCount: number;
      theme: string;
    }
  ): string {
    const { showTooltips, hiddenCount, theme } = options;

    const groupsHTML = badgeGroups.map(group => {
      const badgesHTML = group.badges.map(userBadge => {
        if (!userBadge.badgeType) return '';

        const renderResult = BadgeVisualRenderer.renderBadgeSVG(userBadge.badgeType, {
          size: 'medium',
          animation: true,
          accessibility: true
        });

        const tooltipAttr = showTooltips 
          ? `data-tooltip="${renderResult.accessibility.description}"`
          : '';

        return `
          <div class="badge-grid-item" 
               data-badge-id="${userBadge.badgeTypeId}"
               ${tooltipAttr}
               role="listitem">
            ${renderResult.svg}
            ${renderResult.css ? `<style>${renderResult.css}</style>` : ''}
          </div>
        `;
      }).join('');

      return `
        <div class="badge-group">
          ${group.collection ? `
            <div class="badge-group-header">
              <h4 class="badge-group-title">${group.collection.name}</h4>
            </div>
          ` : ''}
          <div class="badge-group-content">
            ${badgesHTML}
          </div>
        </div>
      `;
    }).join('');

    const moreIndicator = hiddenCount > 0 ? `
      <div class="badge-more-info">
        <span class="more-text">And ${hiddenCount} more badges</span>
      </div>
    ` : '';

    return `
      <div id="${containerId}" 
           class="badge-grid ${theme}" 
           role="list" 
           aria-label="User badge collection">
        ${groupsHTML}
        ${moreIndicator}
      </div>
    `;
  }

  /**
   * Generate responsive CSS for badge grid
   */
  private static generateResponsiveCSS(
    containerId: string,
    gridConfig: BadgeGridConfig,
    theme: string
  ): string {
    const themeColors = theme === 'dark' ? {
      background: '#2a2a2a',
      text: '#ffffff',
      border: '#444444',
      hover: '#3a3a3a'
    } : {
      background: '#ffffff',
      text: '#333333',
      border: '#e0e0e0',
      hover: '#f5f5f5'
    };

    return `
      #${containerId} {
        background: ${themeColors.background};
        color: ${themeColors.text};
        padding: 16px;
        border-radius: 12px;
        border: 1px solid ${themeColors.border};
      }
      
      .badge-group {
        margin-bottom: 24px;
      }
      
      .badge-group:last-child {
        margin-bottom: 0;
      }
      
      .badge-group-header {
        margin-bottom: 12px;
      }
      
      .badge-group-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: ${themeColors.text};
      }
      
      .badge-group-content {
        display: grid;
        gap: ${gridConfig.gap.desktop}px;
        grid-template-columns: repeat(${gridConfig.columns.desktop}, 1fr);
      }
      
      .badge-grid-item {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px;
        border-radius: 8px;
        transition: all 0.2s ease;
        cursor: pointer;
        position: relative;
      }
      
      .badge-grid-item:hover {
        background: ${themeColors.hover};
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      .badge-more-info {
        text-align: center;
        padding: 16px;
        font-size: 14px;
        color: #666;
        font-style: italic;
      }
      
      /* Tablet styles */
      @media (max-width: ${DEFAULT_BREAKPOINTS.desktop}px) {
        .badge-group-content {
          grid-template-columns: repeat(${gridConfig.columns.tablet}, 1fr);
          gap: ${gridConfig.gap.tablet}px;
        }
      }
      
      /* Mobile styles */
      @media (max-width: ${DEFAULT_BREAKPOINTS.mobile}px) {
        #${containerId} {
          padding: 12px;
        }
        
        .badge-group-content {
          grid-template-columns: repeat(${gridConfig.columns.mobile}, 1fr);
          gap: ${gridConfig.gap.mobile}px;
        }
        
        .badge-grid-item {
          padding: 6px;
        }
        
        .badge-group-title {
          font-size: 14px;
        }
      }
      
      /* Tooltip styles */
      .badge-grid-item[data-tooltip]:hover::after {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
        pointer-events: none;
        margin-bottom: 4px;
      }
      
      .badge-grid-item[data-tooltip]:hover::before {
        content: '';
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: rgba(0, 0, 0, 0.9);
        z-index: 1000;
        pointer-events: none;
      }
    `;
  }

  /**
   * Generate JavaScript for tooltip functionality
   */
  private static generateTooltipJS(containerId: string): string {
    return `
      (function() {
        const container = document.getElementById('${containerId}');
        if (!container) return;
        
        const badgeItems = container.querySelectorAll('.badge-grid-item[data-tooltip]');
        
        badgeItems.forEach(item => {
          item.addEventListener('mouseenter', function(e) {
            // Additional tooltip logic can be added here
            this.setAttribute('aria-describedby', 'badge-tooltip-' + this.dataset.badgeId);
          });
          
          item.addEventListener('mouseleave', function(e) {
            this.removeAttribute('aria-describedby');
          });
          
          // Keyboard accessibility
          item.addEventListener('focus', function(e) {
            this.setAttribute('aria-describedby', 'badge-tooltip-' + this.dataset.badgeId);
          });
          
          item.addEventListener('blur', function(e) {
            this.removeAttribute('aria-describedby');
          });
        });
      })();
    `;
  }

  /**
   * Generate badge preview system for design validation
   */
  static createBadgePreviewSystem(): {
    html: string;
    css: string;
    javascript: string;
  } {
    const containerId = `badge-preview-${Date.now()}`;

    const html = `
      <div id="${containerId}" class="badge-preview-system">
        <div class="preview-controls">
          <div class="control-group">
            <label for="shape-select">Shape:</label>
            <select id="shape-select">
              <option value="circle">Circle</option>
              <option value="shield">Shield</option>
              <option value="star">Star</option>
              <option value="hexagon">Hexagon</option>
              <option value="rectangle">Rectangle</option>
            </select>
          </div>
          
          <div class="control-group">
            <label for="color-input">Primary Color:</label>
            <input type="color" id="color-input" value="#4CAF50">
          </div>
          
          <div class="control-group">
            <label for="animation-select">Animation:</label>
            <select id="animation-select">
              <option value="none">None</option>
              <option value="pulse">Pulse</option>
              <option value="glow">Glow</option>
              <option value="bounce">Bounce</option>
              <option value="rotate">Rotate</option>
              <option value="shake">Shake</option>
              <option value="fade">Fade</option>
            </select>
          </div>
          
          <div class="control-group">
            <label for="size-select">Size:</label>
            <select id="size-select">
              <option value="small">Small</option>
              <option value="medium" selected>Medium</option>
              <option value="large">Large</option>
              <option value="xl">Extra Large</option>
            </select>
          </div>
        </div>
        
        <div class="preview-display">
          <div id="badge-preview-container" class="badge-container">
            <!-- Preview badge will be rendered here -->
          </div>
        </div>
        
        <div class="preview-info">
          <div class="accessibility-info">
            <h4>Accessibility Information:</h4>
            <div id="accessibility-details"></div>
          </div>
        </div>
      </div>
    `;

    const css = `
      .badge-preview-system {
        max-width: 600px;
        margin: 0 auto;
        padding: 24px;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        background: #fafafa;
      }
      
      .preview-controls {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
      }
      
      .control-group {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
      
      .control-group label {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }
      
      .control-group select,
      .control-group input {
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 14px;
      }
      
      .preview-display {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 120px;
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        margin-bottom: 24px;
      }
      
      .badge-container {
        padding: 20px;
      }
      
      .preview-info {
        background: white;
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
      }
      
      .accessibility-info h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: #333;
      }
      
      #accessibility-details {
        font-size: 12px;
        color: #666;
        line-height: 1.4;
      }
    `;

    const javascript = `
      (function() {
        const container = document.getElementById('${containerId}');
        if (!container) return;
        
        const shapeSelect = container.querySelector('#shape-select');
        const colorInput = container.querySelector('#color-input');
        const animationSelect = container.querySelector('#animation-select');
        const sizeSelect = container.querySelector('#size-select');
        const previewContainer = container.querySelector('#badge-preview-container');
        const accessibilityDetails = container.querySelector('#accessibility-details');
        
        function updatePreview() {
          const design = {
            shape: shapeSelect.value,
            background: colorInput.value,
            colors: [colorInput.value],
            gradient: null,
            pattern: null,
            elements: []
          };
          
          // This would call the BadgeVisualRenderer.generateBadgePreview method
          // For now, we'll create a simple placeholder
          const size = sizeSelect.value;
          const animation = animationSelect.value;
          
          previewContainer.innerHTML = \`
            <div class="preview-badge \${animation !== 'none' ? 'animated-' + animation : ''}" 
                 style="width: \${size === 'small' ? '24px' : size === 'medium' ? '32px' : size === 'large' ? '48px' : '64px'}; 
                        height: \${size === 'small' ? '24px' : size === 'medium' ? '32px' : size === 'large' ? '48px' : '64px'}; 
                        background: \${colorInput.value}; 
                        border-radius: \${design.shape === 'circle' ? '50%' : '8px'};">
              🏆
            </div>
          \`;
          
          accessibilityDetails.innerHTML = \`
            <div><strong>Alt Text:</strong> Preview Badge</div>
            <div><strong>Description:</strong> Badge with \${design.shape} shape and \${animation} animation</div>
            <div><strong>Role:</strong> img</div>
            <div><strong>Size:</strong> \${size}</div>
          \`;
        }
        
        // Add event listeners
        [shapeSelect, colorInput, animationSelect, sizeSelect].forEach(control => {
          control.addEventListener('change', updatePreview);
        });
        
        // Initial preview
        updatePreview();
      })();
    `;

    return { html, css, javascript };
  }
}