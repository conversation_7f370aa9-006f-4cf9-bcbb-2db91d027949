import { z } from "zod";
import { 
  BADGE_CATEGORIES, 
  UNLOCK_TYPES, 
  COLLECTION_TYPES, 
  BADGE_CRITERIA_TYPES,
  type BadgeCategory,
  type UnlockType,
  type CollectionType,
  type BadgeCriteriaType
} from "../types/badge.types";

// Badge category validation
export const badgeCategorySchema = z.enum(BADGE_CATEGORIES);

// Unlock type validation (replaces assignment type)
export const unlockTypeSchema = z.enum(UNLOCK_TYPES);

// Collection type validation
export const collectionTypeSchema = z.enum(COLLECTION_TYPES);

// Badge criteria type validation
export const badgeCriteriaTypeSchema = z.enum(BADGE_CRITERIA_TYPES);

// Enhanced badge design validation schema
export const badgeDesignSchema = z.object({
  shape: z.string().min(1, "Shape is required"),
  background: z.string().min(1, "Background is required"),
  colors: z.array(z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Invalid hex color")).min(1, "At least one color is required"),
  gradient: z.string().optional(),
  pattern: z.string().optional(),
  elements: z.array(z.string()).optional(),
});

// Enhanced badge criteria validation schema
export const badgeCriteriaSchema = z.object({
  requirement: z.string().min(1, "Requirement description is required"),
  tracked: z.string().min(1, "Tracked metric is required"),
  type: badgeCriteriaTypeSchema.optional(),
  threshold: z.number().min(0).optional(),
  conditions: z.record(z.any()).optional(),
  timeframe: z.string().optional(),
}).refine((data) => {
  // For automatic badges with numeric thresholds, require threshold
  if (data.type && ["message_count", "server_count", "friend_count", "days_active"].includes(data.type)) {
    return data.threshold !== undefined && data.threshold > 0;
  }
  return true;
}, {
  message: "Threshold is required for numeric criteria types and must be greater than 0",
});

// Completion reward validation schema
export const completionRewardSchema = z.object({
  badge: z.string().min(1, "Completion reward badge is required"),
  title: z.string().min(1, "Completion reward title is required"),
  perks: z.array(z.string()).default([]),
  visual: z.string().min(1, "Visual description is required"),
  animation: z.string().min(1, "Animation is required"),
});

// Badge collection validation schemas
export const createBadgeCollectionSchema = z.object({
  collectionId: z.string()
    .min(1, "Collection ID is required")
    .max(100, "Collection ID must be 100 characters or less")
    .regex(/^[a-z0-9_-]+$/, "Collection ID must contain only lowercase letters, numbers, hyphens, and underscores")
    .trim(),
  name: z.string()
    .min(1, "Collection name is required")
    .max(200, "Collection name must be 200 characters or less")
    .trim(),
  description: z.string()
    .min(1, "Collection description is required")
    .trim(),
  type: collectionTypeSchema.default("progressive"),
  unlockedBy: z.string().optional(),
  completionReward: completionRewardSchema.optional(),
});

export const updateBadgeCollectionSchema = createBadgeCollectionSchema.partial();

// Enhanced badge type validation schemas
export const createBadgeTypeSchema = z.object({
  collectionId: z.string().uuid("Invalid collection ID").optional(),
  badgeId: z.string()
    .min(1, "Badge ID is required")
    .max(100, "Badge ID must be 100 characters or less")
    .regex(/^[a-z0-9_-]+$/, "Badge ID must contain only lowercase letters, numbers, hyphens, and underscores")
    .trim(),
  name: z.string()
    .min(1, "Badge name is required")
    .max(100, "Badge name must be 100 characters or less")
    .trim(),
  title: z.string()
    .max(100, "Badge title must be 100 characters or less")
    .trim()
    .optional(),
  description: z.string()
    .min(1, "Badge description is required")
    .trim(),
  icon: z.string()
    .min(1, "Badge icon is required")
    .max(10, "Badge icon must be 10 characters or less")
    .default("🏆"),
  tooltip: z.string().trim().optional(),
  design: badgeDesignSchema,
  criteria: badgeCriteriaSchema,
  perks: z.array(z.string()).default([]),
  unlockType: unlockTypeSchema.default("automatic"),
  visualDescription: z.string().trim().optional(),
  animation: z.string().trim().optional(),
  displayOrder: z.number().min(0).default(0),
  category: badgeCategorySchema.default("achievement"),
}).refine((data) => {
  // For automatic badges, criteria type is required
  if (data.unlockType === "automatic") {
    return data.criteria.type !== undefined;
  }
  return true;
}, {
  message: "Criteria type is required for automatic badge types",
});

export const updateBadgeTypeSchema = z.object({
  collectionId: z.string().uuid("Invalid collection ID").optional(),
  badgeId: z.string()
    .min(1, "Badge ID is required")
    .max(100, "Badge ID must be 100 characters or less")
    .regex(/^[a-z0-9_-]+$/, "Badge ID must contain only lowercase letters, numbers, hyphens, and underscores")
    .trim()
    .optional(),
  name: z.string()
    .min(1, "Badge name is required")
    .max(100, "Badge name must be 100 characters or less")
    .trim()
    .optional(),
  title: z.string()
    .max(100, "Badge title must be 100 characters or less")
    .trim()
    .optional(),
  description: z.string()
    .min(1, "Badge description is required")
    .trim()
    .optional(),
  icon: z.string()
    .min(1, "Badge icon is required")
    .max(10, "Badge icon must be 10 characters or less")
    .optional(),
  tooltip: z.string().trim().optional(),
  design: badgeDesignSchema.optional(),
  criteria: badgeCriteriaSchema.optional(),
  perks: z.array(z.string()).optional(),
  unlockType: unlockTypeSchema.optional(),
  visualDescription: z.string().trim().optional(),
  animation: z.string().trim().optional(),
  displayOrder: z.number().min(0).optional(),
  category: badgeCategorySchema.optional(),
  isActive: z.boolean().optional(),
}).refine((data) => {
  // For automatic badges, criteria type is required
  if (data.unlockType === "automatic" && data.criteria) {
    return data.criteria.type !== undefined;
  }
  return true;
}, {
  message: "Criteria type is required for automatic badge types",
});

// Badge nomination validation schemas
export const createNominationSchema = z.object({
  badgeTypeId: z.string().uuid("Invalid badge type ID"),
  nomineeUserId: z.string().uuid("Invalid nominee user ID"),
  nominationReason: z.string()
    .max(500, "Nomination reason must be 500 characters or less")
    .trim()
    .optional(),
});

export const processNominationSchema = z.object({
  status: z.enum(["approved", "rejected"], {
    errorMap: () => ({ message: "Status must be either 'approved' or 'rejected'" })
  }),
});

// Badge assignment validation
export const assignBadgeSchema = z.object({
  badgeTypeId: z.string().uuid("Invalid badge type ID"),
  userId: z.string().uuid("Invalid user ID"),
});

// Enhanced badge filters validation
export const badgeTypeFiltersSchema = z.object({
  category: badgeCategorySchema.optional(),
  unlockType: unlockTypeSchema.optional(),
  collectionId: z.string().uuid("Invalid collection ID").optional(),
  isActive: z.boolean().optional(),
  search: z.string().trim().optional(),
});

export const badgeCollectionFiltersSchema = z.object({
  type: collectionTypeSchema.optional(),
  isActive: z.boolean().optional(),
  search: z.string().trim().optional(),
});

// Enhanced user stats validation
export const userStatsSchema = z.object({
  messageCount: z.number().min(0),
  serverCount: z.number().min(0),
  friendCount: z.number().min(0),
  daysActive: z.number().min(0),
  accountAge: z.number().min(0),
  lastActive: z.date(),
  invitesSent: z.number().min(0).default(0),
  invitesAccepted: z.number().min(0).default(0),
  feedbackSubmitted: z.number().min(0).default(0),
  moderationActions: z.number().min(0).default(0),
  signupOrder: z.number().min(1).optional(),
  geographicRegion: z.string().trim().optional(),
});

// Badge progress validation
export const badgeProgressSchema = z.object({
  badgeTypeId: z.string().uuid("Invalid badge type ID"),
  progress: z.number().min(0),
  total: z.number().min(1),
  isEarned: z.boolean(),
  collectionProgress: z.object({
    collectionId: z.string().uuid("Invalid collection ID"),
    collectionName: z.string().min(1),
    currentBadge: z.number().min(0),
    totalBadges: z.number().min(1),
    nextBadge: z.any().optional(), // BadgeType interface
  }).optional(),
});

// Collection progress validation
export const collectionProgressSchema = z.object({
  collectionId: z.string().uuid("Invalid collection ID"),
  badgesEarned: z.number().min(0),
  totalBadges: z.number().min(1),
  isCompleted: z.boolean(),
  completionDate: z.date().optional(),
  completionRewardGranted: z.boolean().default(false),
});

// UUID validation helper
export const uuidSchema = z.string().uuid("Invalid UUID format");

// Pagination schema for badge queries
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
});

// Badge visibility validation
export const badgeVisibilitySchema = z.object({
  isVisible: z.boolean(),
});

// Complex criteria validation for advanced badge requirements
export const complexCriteriaSchema = z.object({
  operator: z.enum(["AND", "OR"], {
    errorMap: () => ({ message: "Operator must be either 'AND' or 'OR'" })
  }),
  conditions: z.array(z.object({
    field: z.string().min(1, "Field is required"),
    operator: z.enum([">=", "<=", ">", "<", "=", "!="], {
      errorMap: () => ({ message: "Invalid comparison operator" })
    }),
    value: z.union([z.string(), z.number(), z.boolean()]),
    timeframe: z.string().optional(),
  })).min(1, "At least one condition is required"),
});

// Visual design validation helpers
export const colorPaletteSchema = z.array(
  z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Invalid hex color")
).min(1, "At least one color is required").max(5, "Maximum 5 colors allowed");

export const animationSchema = z.enum([
  "none", "pulse", "glow", "bounce", "rotate", "shake", "fade"
], {
  errorMap: () => ({ message: "Invalid animation type" })
});

// Export type inference helpers
export type CreateBadgeCollectionInput = z.infer<typeof createBadgeCollectionSchema>;
export type UpdateBadgeCollectionInput = z.infer<typeof updateBadgeCollectionSchema>;
export type CreateBadgeTypeInput = z.infer<typeof createBadgeTypeSchema>;
export type UpdateBadgeTypeInput = z.infer<typeof updateBadgeTypeSchema>;
export type CreateNominationInput = z.infer<typeof createNominationSchema>;
export type ProcessNominationInput = z.infer<typeof processNominationSchema>;
export type AssignBadgeInput = z.infer<typeof assignBadgeSchema>;
export type BadgeTypeFiltersInput = z.infer<typeof badgeTypeFiltersSchema>;
export type BadgeCollectionFiltersInput = z.infer<typeof badgeCollectionFiltersSchema>;
export type UserStatsInput = z.infer<typeof userStatsSchema>;
export type BadgeProgressInput = z.infer<typeof badgeProgressSchema>;
export type CollectionProgressInput = z.infer<typeof collectionProgressSchema>;
export type ComplexCriteriaInput = z.infer<typeof complexCriteriaSchema>;
export type PaginationInput = z.infer<typeof paginationSchema>;
export type BadgeVisibilityInput = z.infer<typeof badgeVisibilitySchema>;

// Collection admin schemas
export const reorderBadgesSchema = z.object({
  badgeOrders: z.array(z.object({
    badgeId: z.string().min(1, "Badge ID is required"),
    displayOrder: z.number().min(0, "Display order must be non-negative")
  })).min(1, "At least one badge order is required")
});

export const bulkBadgeAssignmentSchema = z.object({
  userIds: z.array(z.string().uuid("Invalid user ID")).min(1, "At least one user ID is required"),
  badgeIds: z.array(z.string().min(1, "Badge ID is required")).optional()
});

// Export type inference helpers for admin schemas
export type ReorderBadgesInput = z.infer<typeof reorderBadgesSchema>;
export type BulkBadgeAssignmentInput = z.infer<typeof bulkBadgeAssignmentSchema>;