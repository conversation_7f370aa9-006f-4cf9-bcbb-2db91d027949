import { nanoid } from "nanoid";
import { logger } from "../services/logger.service";

/**
 * Interface for correlation tracking data
 */
interface CorrelationData {
  id: string;
  requestType: string;
  userId: string;
  timestamp: number;
  metadata?: Record<string, any>;
  timeoutId?: NodeJS.Timeout;
}

/**
 * Interface for correlation tracking options
 */
interface CorrelationOptions {
  timeoutMs?: number;
  metadata?: Record<string, any>;
  onTimeout?: (correlationId: string, data: CorrelationData) => void;
}

/**
 * Utility class for managing WebSocket request-response correlation tracking
 */
export class CorrelationTracker {
  private correlations: Map<string, CorrelationData> = new Map();
  private readonly logger = logger.createLogger("CorrelationTracker");
  private readonly DEFAULT_TIMEOUT_MS = 30000; // 30 seconds
  private readonly MAX_CORRELATIONS = 10000; // Prevent memory leaks

  /**
   * Generate a new correlation ID
   * @returns A unique correlation ID
   */
  public generateCorrelationId(): string {
    return `corr-${nanoid()}`;
  }

  /**
   * Start tracking a correlation
   * @param requestType The type of request being tracked
   * @param userId The user ID making the request
   * @param options Optional configuration
   * @returns The correlation ID
   */
  public startTracking(
    requestType: string,
    userId: string,
    options: CorrelationOptions = {},
  ): string {
    // Clean up old correlations if we're approaching the limit
    if (this.correlations.size >= this.MAX_CORRELATIONS) {
      this.cleanupOldCorrelations();
    }

    const correlationId = this.generateCorrelationId();
    const timeoutMs = options.timeoutMs || this.DEFAULT_TIMEOUT_MS;

    const correlationData: CorrelationData = {
      id: correlationId,
      requestType,
      userId,
      timestamp: Date.now(),
      metadata: options.metadata,
    };

    // Set up timeout handling
    if (timeoutMs > 0) {
      correlationData.timeoutId = setTimeout(() => {
        this.handleTimeout(correlationId, correlationData, options.onTimeout);
      }, timeoutMs);
    }

    this.correlations.set(correlationId, correlationData);

    this.logger.debug(`Started correlation tracking`, undefined, {
      correlationId,
      requestType,
      userId,
      timeoutMs,
    });

    return correlationId;
  }

  /**
   * Complete a correlation tracking
   * @param correlationId The correlation ID to complete
   * @param responseData Optional response data to log
   * @returns The original correlation data if found
   */
  public completeTracking(
    correlationId: string,
    responseData?: Record<string, any>,
  ): CorrelationData | null {
    const correlationData = this.correlations.get(correlationId);

    if (!correlationData) {
      this.logger.warn(`Correlation not found for completion`, undefined, {
        correlationId,
      });
      return null;
    }

    // Clear timeout if it exists
    if (correlationData.timeoutId) {
      clearTimeout(correlationData.timeoutId);
    }

    // Remove from tracking
    this.correlations.delete(correlationId);

    const duration = Date.now() - correlationData.timestamp;

    this.logger.debug(`Completed correlation tracking`, undefined, {
      correlationId,
      requestType: correlationData.requestType,
      userId: correlationData.userId,
      duration,
      responseData,
    });

    return correlationData;
  }

  /**
   * Get correlation data by ID
   * @param correlationId The correlation ID to look up
   * @returns The correlation data if found
   */
  public getCorrelation(correlationId: string): CorrelationData | null {
    return this.correlations.get(correlationId) || null;
  }

  /**
   * Check if a correlation is being tracked
   * @param correlationId The correlation ID to check
   * @returns True if the correlation is being tracked
   */
  public isTracking(correlationId: string): boolean {
    return this.correlations.has(correlationId);
  }

  /**
   * Get all active correlations for a user
   * @param userId The user ID to filter by
   * @returns Array of correlation data for the user
   */
  public getUserCorrelations(userId: string): CorrelationData[] {
    return Array.from(this.correlations.values()).filter(
      (data) => data.userId === userId,
    );
  }

  /**
   * Get correlation statistics
   * @returns Object with correlation tracking statistics
   */
  public getStats(): {
    activeCorrelations: number;
    totalTracked: number;
    averageAge: number;
    oldestCorrelation?: CorrelationData;
  } {
    const active = this.correlations.size;
    const now = Date.now();
    let totalAge = 0;
    let oldestCorrelation: CorrelationData | undefined;

    for (const data of this.correlations.values()) {
      const age = now - data.timestamp;
      totalAge += age;

      if (!oldestCorrelation || data.timestamp < oldestCorrelation.timestamp) {
        oldestCorrelation = data;
      }
    }

    return {
      activeCorrelations: active,
      totalTracked: active, // This could be enhanced to track total over time
      averageAge: active > 0 ? totalAge / active : 0,
      oldestCorrelation,
    };
  }

  /**
   * Clean up expired correlations
   * @param maxAge Maximum age in milliseconds (default: 5 minutes)
   */
  public cleanupExpired(maxAge: number = 5 * 60 * 1000): number {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [correlationId, data] of this.correlations.entries()) {
      if (now - data.timestamp > maxAge) {
        if (data.timeoutId) {
          clearTimeout(data.timeoutId);
        }
        this.correlations.delete(correlationId);
        cleanedCount++;

        this.logger.debug(`Cleaned up expired correlation`, undefined, {
          correlationId,
          age: now - data.timestamp,
        });
      }
    }

    if (cleanedCount > 0) {
      this.logger.info(`Cleaned up ${cleanedCount} expired correlations`);
    }

    return cleanedCount;
  }

  /**
   * Handle correlation timeout
   * @param correlationId The correlation ID that timed out
   * @param data The correlation data
   * @param onTimeout Optional timeout callback
   */
  private handleTimeout(
    correlationId: string,
    data: CorrelationData,
    onTimeout?: (correlationId: string, data: CorrelationData) => void,
  ): void {
    this.logger.warn(`Correlation timed out`, undefined, {
      correlationId,
      requestType: data.requestType,
      userId: data.userId,
      age: Date.now() - data.timestamp,
    });

    // Remove from tracking
    this.correlations.delete(correlationId);

    // Call timeout callback if provided
    if (onTimeout) {
      try {
        onTimeout(correlationId, data);
      } catch (error) {
        this.logger.error(`Error in correlation timeout callback`, undefined, {
          correlationId,
          error,
        });
      }
    }
  }

  /**
   * Clean up old correlations when approaching memory limit
   */
  private cleanupOldCorrelations(): void {
    const correlationsArray = Array.from(this.correlations.entries());

    // Sort by timestamp (oldest first)
    correlationsArray.sort(([, a], [, b]) => a.timestamp - b.timestamp);

    // Remove oldest 10% of correlations
    const toRemove = Math.floor(correlationsArray.length * 0.1);

    for (let i = 0; i < toRemove; i++) {
      const [correlationId, data] = correlationsArray[i];

      if (data.timeoutId) {
        clearTimeout(data.timeoutId);
      }

      this.correlations.delete(correlationId);
    }

    this.logger.info(
      `Cleaned up ${toRemove} old correlations to prevent memory issues`,
    );
  }

  /**
   * Clear all correlations (useful for testing or shutdown)
   */
  public clearAll(): void {
    for (const data of this.correlations.values()) {
      if (data.timeoutId) {
        clearTimeout(data.timeoutId);
      }
    }

    this.correlations.clear();
    this.logger.info("Cleared all correlation tracking data");
  }
}

// Export a singleton instance
export const correlationTracker = new CorrelationTracker();
