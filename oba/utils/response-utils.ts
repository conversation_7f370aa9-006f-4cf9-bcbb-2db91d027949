import { nanoid } from "nanoid";

// Standard response interfaces
export interface IStandardSuccessResponse<T = unknown> {
  success: true;
  data: T;
  message?: string;
  meta: {
    timestamp: number;
    requestId: string;
    pagination?: IPaginationMeta;
  };
}

export interface IStandardErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: unknown;
    field?: string;
  };
  meta: {
    timestamp: number;
    requestId: string;
  };
}

export interface IPaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Response utility functions
export class ResponseUtils {
  /**
   * Create a standardized success response
   */
  public static success<T>(
    data: T,
    options?: {
      message?: string;
      requestId?: string;
      pagination?: IPaginationMeta;
    },
  ): Response {
    const response: IStandardSuccessResponse<T> = {
      success: true,
      data,
      message: options?.message,
      meta: {
        timestamp: new Date(),
        requestId: options?.requestId || nanoid(),
        pagination: options?.pagination,
      },
    };

    // Remove undefined fields
    if (!response.message) delete response.message;
    if (!response.meta.pagination) delete response.meta.pagination;

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "X-Request-ID": response.meta.requestId,
      },
    });
  }

  /**
   * Create a standardized error response
   */
  public static error(
    code: string,
    message: string,
    options?: {
      status?: number;
      details?: unknown;
      field?: string;
      requestId?: string;
    },
  ): Response {
    const response: IStandardErrorResponse = {
      success: false,
      error: {
        code,
        message,
        details: options?.details,
        field: options?.field,
      },
      meta: {
        timestamp: new Date(),
        requestId: options?.requestId || nanoid(),
      },
    };

    // Remove undefined fields
    if (!response.error.details) delete response.error.details;
    if (!response.error.field) delete response.error.field;

    return new Response(JSON.stringify(response), {
      status: options?.status || 400,
      headers: {
        "Content-Type": "application/json",
        "X-Request-ID": response.meta.requestId,
      },
    });
  }

  /**
   * Create a paginated success response
   */
  public static paginated<T>(
    data: T[],
    pagination: {
      page: number;
      limit: number;
      total: number;
    },
    options?: {
      message?: string;
      requestId?: string;
    },
  ): Response {
    const totalPages = Math.ceil(pagination.total / pagination.limit);
    const paginationMeta: IPaginationMeta = {
      page: pagination.page,
      limit: pagination.limit,
      total: pagination.total,
      totalPages,
      hasNext: pagination.page < totalPages,
      hasPrev: pagination.page > 1,
    };

    return this.success(data, {
      message: options?.message,
      requestId: options?.requestId,
      pagination: paginationMeta,
    });
  }

  /**
   * Create a created (201) response
   */
  public static created<T>(
    data: T,
    options?: {
      message?: string;
      requestId?: string;
    },
  ): Response {
    const response: IStandardSuccessResponse<T> = {
      success: true,
      data,
      message: options?.message || "Resource created successfully",
      meta: {
        timestamp: new Date(),
        requestId: options?.requestId || nanoid(),
      },
    };

    return new Response(JSON.stringify(response), {
      status: 201,
      headers: {
        "Content-Type": "application/json",
        "X-Request-ID": response.meta.requestId,
      },
    });
  }

  /**
   * Create a no content (204) response
   */
  public static noContent(requestId?: string): Response {
    return new Response(null, {
      status: 204,
      headers: {
        "X-Request-ID": requestId || nanoid(),
      },
    });
  }

  /**
   * Common error responses
   */
  public static badRequest(
    message: string,
    details?: unknown,
    field?: string,
  ): Response {
    return this.error("BAD_REQUEST", message, {
      status: 400,
      details,
      field,
    });
  }

  public static unauthorized(
    message: string = "Authentication required",
  ): Response {
    return this.error("UNAUTHORIZED", message, { status: 401 });
  }

  public static forbidden(
    message: string = "Insufficient permissions",
  ): Response {
    return this.error("FORBIDDEN", message, { status: 403 });
  }

  public static notFound(resource: string = "Resource"): Response {
    return this.error("NOT_FOUND", `${resource} not found`, { status: 404 });
  }

  public static conflict(message: string): Response {
    return this.error("CONFLICT", message, { status: 409 });
  }

  public static tooManyRequests(
    message: string = "Rate limit exceeded",
  ): Response {
    return this.error("TOO_MANY_REQUESTS", message, { status: 429 });
  }

  public static internalError(
    message: string = "Internal server error",
  ): Response {
    return this.error("INTERNAL_ERROR", message, { status: 500 });
  }

  /**
   * Validation error response
   */
  public static validationError(
    errors: Array<{ field: string; message: string }>,
  ): Response {
    return this.error("VALIDATION_ERROR", "Validation failed", {
      status: 400,
      details: { errors },
    });
  }

  /**
   * Method not allowed (405) response
   */
  public static methodNotAllowed(
    message: string = "Method not allowed",
  ): Response {
    return this.error("METHOD_NOT_ALLOWED", message, { status: 405 });
  }

  /**
   * Unsupported media type (415) response
   */
  public static unsupportedMediaType(
    message: string = "Unsupported media type",
  ): Response {
    return this.error("UNSUPPORTED_MEDIA_TYPE", message, { status: 415 });
  }

  /**
   * Unprocessable entity (422) response
   */
  public static unprocessableEntity(
    message: string = "Unprocessable entity",
  ): Response {
    return this.error("UNPROCESSABLE_ENTITY", message, { status: 422 });
  }

  /**
   * Service unavailable (503) response
   */
  public static serviceUnavailable(
    message: string = "Service unavailable",
  ): Response {
    return this.error("SERVICE_UNAVAILABLE", message, { status: 503 });
  }

  /**
   * Accepted (202) response
   */
  public static accepted<T>(
    data: T,
    options?: {
      message?: string;
      requestId?: string;
    },
  ): Response {
    const response: IStandardSuccessResponse<T> = {
      success: true,
      data,
      message: options?.message || "Request accepted",
      meta: {
        timestamp: new Date(),
        requestId: options?.requestId || nanoid(),
      },
    };

    return new Response(JSON.stringify(response), {
      status: 202,
      headers: {
        "Content-Type": "application/json",
        "X-Request-ID": response.meta.requestId,
      },
    });
  }
}

// Export commonly used functions
export const {
  success,
  error,
  paginated,
  created,
  noContent,
  badRequest,
  unauthorized,
  forbidden,
  notFound,
  conflict,
  tooManyRequests,
  internalError,
  validationError,
  methodNotAllowed,
  unsupportedMediaType,
  unprocessableEntity,
  serviceUnavailable,
  accepted,
} = ResponseUtils;
