import type { UserBadge, BadgeType } from "../types/badge.types";
import { getUserBadges } from "../db/utils/badge-utils";
import type { drizzle } from "drizzle-orm/postgres-js";

/**
 * Badge display configuration
 */
export const BADGE_DISPLAY_CONFIG = {
  MAX_VISIBLE_BADGES: 5,
  DEFAULT_BADGE_COLOR: '#000000'
} as const;

/**
 * Formatted badge data for API responses
 */
export interface FormattedBadgeData {
  id: string;
  name: string;
  description: string;
  iconUrl?: string;
  color: string;
  category: string;
  assignedAt: Date;
}

/**
 * Badge summary for user profiles
 */
export interface BadgeSummary {
  badges: FormattedBadgeData[];
  totalCount: number;
  visibleCount: number;
  hasMore: boolean;
}

/**
 * Formats a single badge for API response
 */
export function formatBadgeForResponse(userBadge: UserBadge): FormattedBadgeData {
  const badgeType = userBadge.badgeType;
  
  if (!badgeType) {
    throw new Error(`Badge type not found for user badge ${userBadge.id}`);
  }

  return {
    id: userBadge.id,
    name: badgeType.name,
    description: badgeType.description,
    iconUrl: badgeType.iconUrl,
    color: badgeType.color || BADGE_DISPLAY_CONFIG.DEFAULT_BADGE_COLOR,
    category: badgeType.category,
    assignedAt: userBadge.assignedAt
  };
}

/**
 * Formats multiple badges for API response with display limits
 */
export function formatBadgesForResponse(userBadges: UserBadge[]): BadgeSummary {
  // Filter only visible badges
  const visibleBadges = userBadges.filter(badge => badge.isVisible);
  
  // Sort badges by assignment date (newest first)
  const sortedBadges = visibleBadges.sort((a, b) => 
    new Date(b.assignedAt).getTime() - new Date(a.assignedAt).getTime()
  );

  // Take only the first N badges for display
  const displayBadges = sortedBadges.slice(0, BADGE_DISPLAY_CONFIG.MAX_VISIBLE_BADGES);
  
  // Format badges for response
  const formattedBadges = displayBadges.map(formatBadgeForResponse);

  return {
    badges: formattedBadges,
    totalCount: visibleBadges.length,
    visibleCount: displayBadges.length,
    hasMore: visibleBadges.length > BADGE_DISPLAY_CONFIG.MAX_VISIBLE_BADGES
  };
}

/**
 * Gets and formats user badges for API responses
 */
export async function getUserBadgesForResponse(
  db: ReturnType<typeof drizzle>,
  userId: string
): Promise<BadgeSummary> {
  try {
    const userBadges = await getUserBadges(db, userId, true); // Only visible badges
    return formatBadgesForResponse(userBadges);
  } catch (error) {
    console.error(`Error getting badges for user ${userId}:`, error);
    // Return empty badge summary on error
    return {
      badges: [],
      totalCount: 0,
      visibleCount: 0,
      hasMore: false
    };
  }
}

/**
 * Lightweight badge data for WebSocket events and minimal responses
 */
export interface LightweightBadgeData {
  id: string;
  name: string;
  iconUrl?: string;
  color: string;
  category: string;
}

/**
 * Formats badges for WebSocket events (lightweight version)
 */
export function formatBadgesForWebSocket(userBadges: UserBadge[]): LightweightBadgeData[] {
  return userBadges
    .filter(badge => badge.isVisible && badge.badgeType)
    .slice(0, BADGE_DISPLAY_CONFIG.MAX_VISIBLE_BADGES)
    .map(badge => ({
      id: badge.id,
      name: badge.badgeType!.name,
      iconUrl: badge.badgeType!.iconUrl,
      color: badge.badgeType!.color || BADGE_DISPLAY_CONFIG.DEFAULT_BADGE_COLOR,
      category: badge.badgeType!.category
    }));
}

/**
 * Gets lightweight badge data for WebSocket events
 */
export async function getUserBadgesForWebSocket(
  db: ReturnType<typeof drizzle>,
  userId: string
): Promise<LightweightBadgeData[]> {
  try {
    const userBadges = await getUserBadges(db, userId, true);
    return formatBadgesForWebSocket(userBadges);
  } catch (error) {
    console.error(`Error getting badges for WebSocket for user ${userId}:`, error);
    return [];
  }
}