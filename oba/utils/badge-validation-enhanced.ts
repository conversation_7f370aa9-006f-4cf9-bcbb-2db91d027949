import { z } from "zod";
import { 
  BADGE_CATEGORIES, 
  UNLOCK_TYPES,
  COLLECTION_TYPES,
  BADGE_CRITERIA_TYPES,
  type BadgeCategory,
  type UnlockType,
  type CollectionType,
  type BadgeCriteriaType
} from "../types/badge.types";
import { 
  BadgeValidationError,
  InvalidBadgeCriteriaError 
} from "../class/badge-errors";

// Base validation schemas
export const badgeCategorySchema = z.enum(BADGE_CATEGORIES);
export const unlockTypeSchema = z.enum(UNLOCK_TYPES);
export const collectionTypeSchema = z.enum(COLLECTION_TYPES);
export const badgeCriteriaTypeSchema = z.enum(BADGE_CRITERIA_TYPES);

// Badge design validation schema
export const badgeDesignSchema = z.object({
  shape: z.string().min(1, "Shape is required"),
  background: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Background must be a valid hex color"),
  colors: z.array(z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Each color must be a valid hex color")).min(1),
  gradient: z.string().optional(),
  pattern: z.string().optional(),
  elements: z.array(z.string()).optional(),
});

// Enhanced badge criteria validation schema
export const enhancedBadgeCriteriaSchema = z.object({
  requirement: z.string().min(1, "Requirement description is required"),
  tracked: z.string().min(1, "Tracked metric is required"),
  type: badgeCriteriaTypeSchema.optional(),
  threshold: z.number().min(0).optional(),
  conditions: z.record(z.any()).optional(),
  timeframe: z.string().optional(),
}).refine((data) => {
  // For automatic badges with numeric thresholds, require threshold
  if (data.type && ["message_count", "server_count", "friend_count", "days_active"].includes(data.type)) {
    return data.threshold !== undefined && data.threshold > 0;
  }
  // For custom criteria, conditions are required
  if (data.type === 'custom') {
    return data.conditions !== undefined && Object.keys(data.conditions).length > 0;
  }
  // For complex criteria, conditions are required
  if (data.type === 'complex') {
    return data.conditions !== undefined && Object.keys(data.conditions).length > 0;
  }
  return true;
}, {
  message: "Threshold is required for numeric criteria types, conditions are required for custom/complex criteria",
});

// Enhanced badge type creation validation
export const createEnhancedBadgeTypeSchema = z.object({
  collectionId: z.string().uuid().optional(),
  badgeId: z.string()
    .min(1, "Badge ID is required")
    .max(100, "Badge ID must be 100 characters or less")
    .regex(/^[a-z0-9-_]+$/, "Badge ID must contain only lowercase letters, numbers, hyphens, and underscores"),
  name: z.string()
    .min(1, "Badge name is required")
    .max(100, "Badge name must be 100 characters or less")
    .trim(),
  title: z.string()
    .max(100, "Badge title must be 100 characters or less")
    .trim()
    .optional(),
  description: z.string()
    .min(1, "Badge description is required")
    .max(500, "Badge description must be 500 characters or less")
    .trim(),
  icon: z.string()
    .min(1, "Badge icon is required")
    .default("🏆"),
  tooltip: z.string()
    .max(200, "Tooltip must be 200 characters or less")
    .optional(),
  design: badgeDesignSchema,
  criteria: enhancedBadgeCriteriaSchema,
  perks: z.array(z.string()).optional(),
  unlockType: unlockTypeSchema,
  visualDescription: z.string()
    .max(300, "Visual description must be 300 characters or less")
    .optional(),
  animation: z.string()
    .max(50, "Animation name must be 50 characters or less")
    .optional(),
  displayOrder: z.number().int().min(0).default(0),
  category: badgeCategorySchema,
}).refine((data) => {
  // Automatic badges must have criteria with type
  if (data.unlockType === 'automatic' && !data.criteria.type) {
    return false;
  }
  return true;
}, {
  message: "Automatic badges must have criteria with a defined type"
});

// Enhanced badge type update validation
export const updateEnhancedBadgeTypeSchema = z.object({
  collectionId: z.string().uuid().optional(),
  badgeId: z.string()
    .min(1, "Badge ID is required")
    .max(100, "Badge ID must be 100 characters or less")
    .regex(/^[a-z0-9-_]+$/, "Badge ID must contain only lowercase letters, numbers, hyphens, and underscores")
    .optional(),
  name: z.string()
    .min(1, "Badge name is required")
    .max(100, "Badge name must be 100 characters or less")
    .trim()
    .optional(),
  title: z.string()
    .max(100, "Badge title must be 100 characters or less")
    .trim()
    .optional(),
  description: z.string()
    .min(1, "Badge description is required")
    .max(500, "Badge description must be 500 characters or less")
    .trim()
    .optional(),
  icon: z.string()
    .min(1, "Badge icon is required")
    .optional(),
  tooltip: z.string()
    .max(200, "Tooltip must be 200 characters or less")
    .optional(),
  design: badgeDesignSchema.optional(),
  criteria: enhancedBadgeCriteriaSchema.optional(),
  perks: z.array(z.string()).optional(),
  unlockType: unlockTypeSchema.optional(),
  visualDescription: z.string()
    .max(300, "Visual description must be 300 characters or less")
    .optional(),
  animation: z.string()
    .max(50, "Animation name must be 50 characters or less")
    .optional(),
  displayOrder: z.number().int().min(0).optional(),
  category: badgeCategorySchema.optional(),
  isActive: z.boolean().optional(),
}).refine((data) => {
  // If unlockType is being changed to automatic, criteria must be provided or have type
  if (data.unlockType === 'automatic' && data.criteria && !data.criteria.type) {
    return false;
  }
  return true;
}, {
  message: "Automatic badges must have criteria with a defined type"
});

// Badge collection validation schemas
export const createBadgeCollectionSchema = z.object({
  collectionId: z.string()
    .min(1, "Collection ID is required")
    .max(100, "Collection ID must be 100 characters or less")
    .regex(/^[a-z0-9-_]+$/, "Collection ID must contain only lowercase letters, numbers, hyphens, and underscores"),
  name: z.string()
    .min(1, "Collection name is required")
    .max(200, "Collection name must be 200 characters or less")
    .trim(),
  description: z.string()
    .min(1, "Collection description is required")
    .max(1000, "Collection description must be 1000 characters or less")
    .trim(),
  type: collectionTypeSchema.default("progressive"),
  unlockedBy: z.string()
    .max(100, "Unlocked by description must be 100 characters or less")
    .optional(),
  completionReward: z.object({
    badge: z.string().min(1),
    title: z.string().min(1),
    perks: z.array(z.string()),
    visual: z.string().min(1),
    animation: z.string().min(1),
  }).optional(),
});

// Enhanced badge filters validation
export const enhancedBadgeFiltersSchema = z.object({
  category: badgeCategorySchema.optional(),
  unlockType: unlockTypeSchema.optional(),
  collectionId: z.string().uuid().optional(),
  isActive: z.boolean().optional(),
  search: z.string()
    .min(1)
    .max(100)
    .trim()
    .optional(),
});

// Badge assignment validation (same as before)
export const assignBadgeSchema = z.object({
  badgeTypeId: z.string()
    .uuid("Invalid badge type ID format"),
  userId: z.string()
    .uuid("Invalid user ID format"),
});

// Badge visibility validation
export const badgeVisibilitySchema = z.object({
  badgeTypeId: z.string().uuid("Invalid badge type ID format"),
  isVisible: z.boolean(),
});

// Badge progress validation
export const badgeProgressFiltersSchema = z.object({
  badgeTypeId: z.string().uuid().optional(),
  includeEarned: z.boolean().default(true),
  includeInProgress: z.boolean().default(true),
});

// Badge history filters validation
export const badgeHistoryFiltersSchema = z.object({
  limit: z.number().int().min(1).max(100).default(20),
  offset: z.number().int().min(0).default(0),
  category: badgeCategorySchema.optional(),
  unlockType: unlockTypeSchema.optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
});

// Validation helper functions
export function validateCreateEnhancedBadgeType(data: unknown) {
  return createEnhancedBadgeTypeSchema.parse(data);
}

export function validateUpdateEnhancedBadgeType(data: unknown) {
  return updateEnhancedBadgeTypeSchema.parse(data);
}

export function validateCreateBadgeCollection(data: unknown) {
  return createBadgeCollectionSchema.parse(data);
}

export function validateUpdateBadgeCollection(data: unknown) {
  return createBadgeCollectionSchema.partial().extend({
    isActive: z.boolean().optional()
  }).parse(data);
}

export function validateBadgeCollectionFilters(data: unknown) {
  return z.object({
    type: collectionTypeSchema.optional(),
    isActive: z.boolean().optional(),
    search: z.string().optional()
  }).parse(data);
}

export function validateEnhancedBadgeFilters(data: unknown) {
  return enhancedBadgeFiltersSchema.parse(data);
}

export function validateAssignBadge(data: unknown) {
  return assignBadgeSchema.parse(data);
}

export function validateBadgeVisibility(data: unknown) {
  return badgeVisibilitySchema.parse(data);
}

export function validateBadgeProgressFilters(data: unknown) {
  return badgeProgressFiltersSchema.parse(data);
}

export function validateBadgeHistoryFilters(data: unknown) {
  return badgeHistoryFiltersSchema.parse(data);
}

// Type exports for use in other files
export type CreateEnhancedBadgeTypeInput = z.infer<typeof createEnhancedBadgeTypeSchema>;
export type UpdateEnhancedBadgeTypeInput = z.infer<typeof updateEnhancedBadgeTypeSchema>;
export type CreateBadgeCollectionInput = z.infer<typeof createBadgeCollectionSchema>;
export type EnhancedBadgeFiltersInput = z.infer<typeof enhancedBadgeFiltersSchema>;
export type AssignBadgeInput = z.infer<typeof assignBadgeSchema>;
export type BadgeVisibilityInput = z.infer<typeof badgeVisibilitySchema>;
export type BadgeProgressFiltersInput = z.infer<typeof badgeProgressFiltersSchema>;
export type BadgeHistoryFiltersInput = z.infer<typeof badgeHistoryFiltersSchema>;