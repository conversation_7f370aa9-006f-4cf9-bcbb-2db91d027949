// cors.ts - Create this file in your backend
export interface CORSOptions {
  origin: string | string[];
  credentials: boolean;
  allowedHeaders: string[];
  methods: string[];
}

export function createCORSHandler(options: CORSOptions) {
  return (request: Request): Response | null => {
    const origin = request.headers.get("origin");
    const method = request.method;

    // Handle preflight requests
    if (method === "OPTIONS") {
      const headers = new Headers();

      // Set origin
      if (Array.isArray(options.origin)) {
        if (origin && options.origin.includes(origin)) {
          headers.set("Access-Control-Allow-Origin", origin);
        }
      } else {
        headers.set("Access-Control-Allow-Origin", options.origin);
      }

      headers.set("Access-Control-Allow-Methods", options.methods.join(", "));
      headers.set(
        "Access-Control-Allow-Headers",
        options.allowedHeaders.join(", "),
      );

      if (options.credentials) {
        headers.set("Access-Control-Allow-Credentials", "true");
      }

      headers.set("Access-Control-Max-Age", "86400"); // 24 hours

      return new Response(null, { status: 200, headers });
    }

    return null; // Not a preflight request
  };
}

export function addCORSHeaders(
  response: Response,
  options: CORSOptions,
  origin?: string | null,
): Response {
  const headers = new Headers(response.headers);

  // Set origin
  if (Array.isArray(options.origin)) {
    if (origin && options.origin.includes(origin)) {
      headers.set("Access-Control-Allow-Origin", origin);
    }
  } else {
    headers.set("Access-Control-Allow-Origin", options.origin);
  }

  if (options.credentials) {
    headers.set("Access-Control-Allow-Credentials", "true");
  }

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers,
  });
}
