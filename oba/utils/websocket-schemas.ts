import { z } from "zod";
import { EventTypes } from "../constants/eventTypes";
import {
  type IMessageTypeDefinition,
  WebSocketErrorCode,
} from "../types/websocket-standardization.types";

/**
 * WebSocket Message Schemas
 *
 * This file contains Zod schemas for validating different types of WebSocket messages.
 * Each schema defines the expected structure and validation rules for message data.
 */

// Common validation schemas
const UUIDSchema = z.string().uuid("Must be a valid UUID");
const NonEmptyStringSchema = z.string().min(1, "Cannot be empty");
const MessageContentSchema = z
  .string()
  .min(1)
  .max(2000, "Message content cannot exceed 2000 characters");

// Base message metadata schema
export const WebSocketMetaSchema = z.object({
  timestamp: z.string().datetime("Must be a valid ISO 8601 timestamp"),
  messageId: NonEmptyStringSchema,
  correlationId: z.string().optional(),
  version: NonEmptyStringSchema,
  source: z.enum(["server", "client"]),
  traceId: z.string().optional(),
  priority: z.enum(["low", "normal", "high", "critical"]).optional(),
  ttl: z.number().positive().optional(),
});

// Base message target schema
export const WebSocketTargetSchema = z.object({
  userId: UUIDSchema.optional(),
  channelId: UUIDSchema.optional(),
  serverId: UUIDSchema.optional(),
  topic: z.string().optional(),
  roleId: UUIDSchema.optional(),
  excludeUserId: UUIDSchema.optional(),
  filter: z.string().optional(),
});

// Authentication message schemas
export const AuthLoginSchema = z.object({
  username: NonEmptyStringSchema,
  password: NonEmptyStringSchema,
  rememberMe: z.boolean().optional(),
});

export const AuthTokenRefreshSchema = z.object({
  refreshToken: NonEmptyStringSchema,
});

export const AuthLogoutSchema = z.object({
  everywhere: z.boolean().optional(),
});

// Message-related schemas
export const MessageSendSchema = z.object({
  content: MessageContentSchema,
  channelId: UUIDSchema,
  serverId: UUIDSchema,
  replyToId: UUIDSchema.optional(),
  attachments: z
    .array(
      z.object({
        filename: NonEmptyStringSchema,
        url: z.string().url(),
        size: z.number().positive(),
        mimeType: z.string(),
      }),
    )
    .optional(),
});

export const MessageEditSchema = z.object({
  messageId: UUIDSchema,
  content: MessageContentSchema,
  channelId: UUIDSchema,
  serverId: UUIDSchema,
});

export const MessageDeleteSchema = z.object({
  messageId: UUIDSchema,
  channelId: UUIDSchema,
  serverId: UUIDSchema,
});

export const MessageReactionSchema = z.object({
  messageId: UUIDSchema,
  emoji: NonEmptyStringSchema,
  channelId: UUIDSchema,
  serverId: UUIDSchema,
});

// Channel-related schemas
export const ChannelJoinSchema = z.object({
  channelId: UUIDSchema,
  serverId: UUIDSchema,
});

export const ChannelLeaveSchema = z.object({
  channelId: UUIDSchema,
  serverId: UUIDSchema,
});

export const ChannelCreateSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  type: z.enum(["text", "voice", "category"]),
  serverId: UUIDSchema,
  categoryId: UUIDSchema.optional(),
  isPrivate: z.boolean().optional(),
});

export const ChannelUpdateSchema = z.object({
  channelId: UUIDSchema,
  serverId: UUIDSchema,
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  isPrivate: z.boolean().optional(),
});

// Voice-related schemas
export const VoiceJoinSchema = z.object({
  channelId: UUIDSchema,
  serverId: UUIDSchema,
  muted: z.boolean().optional(),
  deafened: z.boolean().optional(),
});

export const VoiceLeaveSchema = z.object({
  channelId: UUIDSchema,
  serverId: UUIDSchema,
});

export const VoiceStateUpdateSchema = z.object({
  channelId: UUIDSchema,
  serverId: UUIDSchema,
  muted: z.boolean().optional(),
  deafened: z.boolean().optional(),
  speaking: z.boolean().optional(),
});

export const VoiceSignalingSchema = z.object({
  type: z.enum(["offer", "answer", "ice-candidate"]),
  channelId: UUIDSchema,
  serverId: UUIDSchema,
  targetUserId: UUIDSchema.optional(),
  sdp: z.string().optional(),
  candidate: z
    .object({
      candidate: z.string(),
      sdpMLineIndex: z.number(),
      sdpMid: z.string(),
    })
    .optional(),
});

// Server-related schemas
export const ServerJoinSchema = z.object({
  serverId: UUIDSchema,
  inviteCode: z.string().optional(),
});

export const ServerLeaveSchema = z.object({
  serverId: UUIDSchema,
});

export const ServerCreateSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  icon: z.string().url().optional(),
  isPublic: z.boolean().optional(),
});

// User-related schemas
export const UserStatusUpdateSchema = z.object({
  status: z.enum(["online", "away", "busy", "invisible"]),
  customMessage: z.string().max(100).optional(),
});

export const UserProfileUpdateSchema = z.object({
  displayName: z.string().min(1).max(50).optional(),
  bio: z.string().max(200).optional(),
  avatar: z.string().url().optional(),
});

// Direct message schemas
export const DirectMessageSendSchema = z.object({
  content: MessageContentSchema,
  recipientId: UUIDSchema,
  replyToId: UUIDSchema.optional(),
  attachments: z
    .array(
      z.object({
        filename: NonEmptyStringSchema,
        url: z.string().url(),
        size: z.number().positive(),
        mimeType: z.string(),
      }),
    )
    .optional(),
});

export const DirectMessageEditSchema = z.object({
  messageId: UUIDSchema,
  content: MessageContentSchema,
  recipientId: UUIDSchema,
});

export const DirectMessageDeleteSchema = z.object({
  messageId: UUIDSchema,
  recipientId: UUIDSchema,
});

// Friend system schemas
export const FriendRequestSendSchema = z.object({
  recipientId: UUIDSchema,
  message: z.string().max(200).optional(),
});

export const FriendRequestResponseSchema = z.object({
  requestId: UUIDSchema,
  accept: z.boolean(),
});

export const FriendRemoveSchema = z.object({
  friendId: UUIDSchema,
});

// Subscription schemas
export const SubscribeSchema = z.object({
  topic: NonEmptyStringSchema,
  filters: z.record(z.string()).optional(),
});

export const UnsubscribeSchema = z.object({
  topic: NonEmptyStringSchema,
});

// Heartbeat schema
export const HeartbeatSchema = z.object({
  timestamp: z.string().datetime(),
  clientTime: z.number().optional(),
});

// Badge event schemas
export const BadgeAssignedSchema = z.object({
  userId: UUIDSchema,
  badge: z.object({
    id: UUIDSchema,
    badgeTypeId: UUIDSchema,
    userId: UUIDSchema,
    assignedBy: UUIDSchema.optional(),
    assignedAt: z.string().datetime(),
    progress: z.number().min(0).optional(),
    metadata: z.record(z.any()).optional(),
  }),
  isAutomatic: z.boolean(),
  assignedBy: UUIDSchema.optional(),
  timestamp: z.string().datetime(),
});

export const BadgeRemovedSchema = z.object({
  userId: UUIDSchema,
  badgeTypeId: UUIDSchema,
  badgeType: z.object({
    id: UUIDSchema,
    name: NonEmptyStringSchema,
    description: z.string().optional(),
    icon: z.string().optional(),
    category: NonEmptyStringSchema,
    rarity: z.enum(["common", "uncommon", "rare", "epic", "legendary"]),
    isActive: z.boolean(),
  }),
  removedBy: UUIDSchema,
  timestamp: z.string().datetime(),
});

export const BadgeProgressUpdateSchema = z.object({
  userId: UUIDSchema,
  badgeTypeId: UUIDSchema,
  badgeType: z.object({
    id: UUIDSchema,
    name: NonEmptyStringSchema,
    description: z.string().optional(),
    icon: z.string().optional(),
    category: NonEmptyStringSchema,
    rarity: z.enum(["common", "uncommon", "rare", "epic", "legendary"]),
    isActive: z.boolean(),
  }),
  progress: z.number().min(0),
  total: z.number().min(1),
  progressPercentage: z.number().min(0).max(100),
  timestamp: z.string().datetime(),
});

// Error response schema
export const ErrorResponseSchema = z.object({
  code: z.nativeEnum(WebSocketErrorCode),
  message: NonEmptyStringSchema,
  details: z.record(z.any()).optional(),
});

// Success response schema
export const SuccessResponseSchema = z.object({
  message: z.string().optional(),
  data: z.any().optional(),
});

// Event notification schema
export const EventNotificationSchema = z.object({
  event: NonEmptyStringSchema,
  category: z.string().optional(),
  severity: z.enum(["info", "warning", "error", "critical"]).optional(),
  data: z.any(),
});

/**
 * Message Type Definitions Registry
 *
 * This registry contains all message type definitions with their schemas,
 * requirements, and metadata for validation and processing.
 */
export const MESSAGE_TYPE_DEFINITIONS: Map<
  string | number,
  IMessageTypeDefinition
> = new Map([
  // Authentication messages
  [
    EventTypes.AUTHENTICATE,
    {
      type: EventTypes.AUTHENTICATE,
      category: "auth",
      direction: "client_to_server",
      requiresAuth: false,
      dataSchema: AuthLoginSchema,
      responseType: EventTypes.AUTH_SUCCESS,
      rateLimitGroup: "auth",
      supportsCorrelation: true,
      maxSize: 1024,
      description: "User login request",
    },
  ],

  // Message operations
  [
    EventTypes.MESSAGE_SEND,
    {
      type: EventTypes.MESSAGE_SEND,
      category: "message",
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: "SEND_MESSAGES",
      dataSchema: MessageSendSchema,
      responseType: EventTypes.MESSAGE_SENT,
      rateLimitGroup: "messaging",
      supportsCorrelation: true,
      maxSize: 4096,
      description: "Send a message to a channel",
    },
  ],

  [
    EventTypes.MESSAGE_UPDATE,
    {
      type: EventTypes.MESSAGE_UPDATE,
      category: "message",
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: "SEND_MESSAGES",
      dataSchema: MessageEditSchema,
      responseType: EventTypes.MESSAGE_UPDATED,
      rateLimitGroup: "messaging",
      supportsCorrelation: true,
      maxSize: 4096,
      description: "Edit an existing message",
    },
  ],

  [
    EventTypes.MESSAGE_DELETE,
    {
      type: EventTypes.MESSAGE_DELETE,
      category: "message",
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: "MANAGE_MESSAGES",
      dataSchema: MessageDeleteSchema,
      responseType: EventTypes.MESSAGE_DELETED,
      rateLimitGroup: "messaging",
      supportsCorrelation: true,
      maxSize: 512,
      description: "Delete a message",
    },
  ],

  [
    EventTypes.REACTION_ADD,
    {
      type: EventTypes.REACTION_ADD,
      category: "message",
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: "ADD_REACTIONS",
      dataSchema: MessageReactionSchema,
      responseType: EventTypes.REACTION_ADDED,
      rateLimitGroup: "reactions",
      supportsCorrelation: true,
      maxSize: 512,
      description: "Add reaction to a message",
    },
  ],

  // Channel operations
  [
    EventTypes.CHANNEL_SUBSCRIBE,
    {
      type: EventTypes.CHANNEL_SUBSCRIBE,
      category: "channel",
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: "VIEW_CHANNEL",
      dataSchema: ChannelJoinSchema,
      responseType: EventTypes.CHANNEL_SUBSCRIBED,
      rateLimitGroup: "channel_operations",
      supportsCorrelation: true,
      maxSize: 512,
      description: "Subscribe to a channel",
    },
  ],

  [
    EventTypes.CHANNEL_UNSUBSCRIBE,
    {
      type: EventTypes.CHANNEL_UNSUBSCRIBE,
      category: "channel",
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: ChannelLeaveSchema,
      responseType: EventTypes.CHANNEL_UNSUBSCRIBED,
      rateLimitGroup: "channel_operations",
      supportsCorrelation: true,
      maxSize: 512,
      description: "Unsubscribe from a channel",
    },
  ],

  [
    EventTypes.CHANNEL_CREATE,
    {
      type: EventTypes.CHANNEL_CREATE,
      category: "channel",
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: "MANAGE_CHANNELS",
      dataSchema: ChannelCreateSchema,
      responseType: EventTypes.CHANNEL_CREATED,
      rateLimitGroup: "channel_management",
      supportsCorrelation: true,
      maxSize: 1024,
      description: "Create a new channel",
    },
  ],

  // Voice operations
  [
    EventTypes.VOICE_JOIN,
    {
      type: EventTypes.VOICE_JOIN,
      category: "voice",
      direction: "client_to_server",
      requiresAuth: true,
      requiresPermission: "CONNECT",
      dataSchema: VoiceJoinSchema,
      responseType: EventTypes.VOICE_JOINED,
      rateLimitGroup: "voice_operations",
      supportsCorrelation: true,
      maxSize: 512,
      description: "Join a voice channel",
    },
  ],

  [
    EventTypes.VOICE_LEAVE,
    {
      type: EventTypes.VOICE_LEAVE,
      category: "voice",
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: VoiceLeaveSchema,
      responseType: EventTypes.VOICE_LEFT,
      rateLimitGroup: "voice_operations",
      supportsCorrelation: true,
      maxSize: 512,
      description: "Leave a voice channel",
    },
  ],

  [
    EventTypes.VOICE_STATE_UPDATE,
    {
      type: EventTypes.VOICE_STATE_UPDATE,
      category: "voice",
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: VoiceStateUpdateSchema,
      responseType: EventTypes.VOICE_STATE_UPDATED,
      rateLimitGroup: "voice_state",
      supportsCorrelation: true,
      maxSize: 512,
      description: "Update voice state (mute/deafen)",
    },
  ],

  // Direct messages
  [
    EventTypes.DIRECT_MESSAGE_SEND,
    {
      type: EventTypes.DIRECT_MESSAGE_SEND,
      category: "direct_message",
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: DirectMessageSendSchema,
      responseType: EventTypes.DIRECT_MESSAGE_SENT,
      rateLimitGroup: "direct_messaging",
      supportsCorrelation: true,
      maxSize: 4096,
      description: "Send a direct message",
    },
  ],

  // Friend system
  [
    EventTypes.FRIEND_REQUEST_SEND,
    {
      type: EventTypes.FRIEND_REQUEST_SEND,
      category: "friend",
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: FriendRequestSendSchema,
      responseType: EventTypes.FRIEND_REQUEST_SENT,
      rateLimitGroup: "friend_operations",
      supportsCorrelation: true,
      maxSize: 1024,
      description: "Send a friend request",
    },
  ],

  [
    EventTypes.FRIEND_REQUEST_ACCEPT,
    {
      type: EventTypes.FRIEND_REQUEST_ACCEPT,
      category: "friend",
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: FriendRequestResponseSchema,
      responseType: EventTypes.FRIEND_REQUEST_ACCEPTED,
      rateLimitGroup: "friend_operations",
      supportsCorrelation: true,
      maxSize: 512,
      description: "Accept a friend request",
    },
  ],

  [
    EventTypes.FRIEND_REQUEST_DECLINE,
    {
      type: EventTypes.FRIEND_REQUEST_DECLINE,
      category: "friend",
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: FriendRequestResponseSchema,
      responseType: EventTypes.FRIEND_REQUEST_DECLINED,
      rateLimitGroup: "friend_operations",
      supportsCorrelation: true,
      maxSize: 512,
      description: "Decline a friend request",
    },
  ],

  // Server operations
  [
    EventTypes.SERVER_JOIN,
    {
      type: EventTypes.SERVER_JOIN,
      category: "server",
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: ServerJoinSchema,
      responseType: EventTypes.SERVER_JOINED,
      rateLimitGroup: "server_operations",
      supportsCorrelation: true,
      maxSize: 512,
      description: "Join a server",
    },
  ],

  [
    EventTypes.SERVER_LEAVE,
    {
      type: EventTypes.SERVER_LEAVE,
      category: "server",
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: ServerLeaveSchema,
      responseType: EventTypes.SERVER_LEFT,
      rateLimitGroup: "server_operations",
      supportsCorrelation: true,
      maxSize: 512,
      description: "Leave a server",
    },
  ],

  [
    EventTypes.SERVER_CREATE,
    {
      type: EventTypes.SERVER_CREATE,
      category: "server",
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: ServerCreateSchema,
      responseType: EventTypes.SERVER_CREATED,
      rateLimitGroup: "server_management",
      supportsCorrelation: true,
      maxSize: 1024,
      description: "Create a new server",
    },
  ],

  // User operations
  [
    EventTypes.USER_STATUS_UPDATE,
    {
      type: EventTypes.USER_STATUS_UPDATE,
      category: "user",
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: UserStatusUpdateSchema,
      responseType: EventTypes.USER_STATUS_UPDATED,
      rateLimitGroup: "user_operations",
      supportsCorrelation: true,
      maxSize: 512,
      description: "Update user status",
    },
  ],

  [
    EventTypes.USER_UPDATE,
    {
      type: EventTypes.USER_UPDATE,
      category: "user",
      direction: "client_to_server",
      requiresAuth: true,
      dataSchema: UserProfileUpdateSchema,
      responseType: EventTypes.USER_UPDATED,
      rateLimitGroup: "user_operations",
      supportsCorrelation: true,
      maxSize: 1024,
      description: "Update user profile",
    },
  ],

  // System messages
  [
    EventTypes.PING,
    {
      type: EventTypes.PING,
      category: "system",
      direction: "bidirectional",
      requiresAuth: false,
      dataSchema: HeartbeatSchema,
      responseType: EventTypes.PONG,
      rateLimitGroup: "system",
      supportsCorrelation: false,
      maxSize: 256,
      description: "Connection heartbeat",
    },
  ],

  // Badge events (server to client only)
  [
    EventTypes.BADGE_ASSIGNED,
    {
      type: EventTypes.BADGE_ASSIGNED,
      category: "badge",
      direction: "server_to_client",
      requiresAuth: true,
      dataSchema: BadgeAssignedSchema,
      rateLimitGroup: "badge_events",
      supportsCorrelation: false,
      maxSize: 2048,
      description: "Badge assigned to user notification",
    },
  ],

  [
    EventTypes.BADGE_REMOVED,
    {
      type: EventTypes.BADGE_REMOVED,
      category: "badge",
      direction: "server_to_client",
      requiresAuth: true,
      dataSchema: BadgeRemovedSchema,
      rateLimitGroup: "badge_events",
      supportsCorrelation: false,
      maxSize: 2048,
      description: "Badge removed from user notification",
    },
  ],

  [
    EventTypes.BADGE_PROGRESS_UPDATE,
    {
      type: EventTypes.BADGE_PROGRESS_UPDATE,
      category: "badge",
      direction: "server_to_client",
      requiresAuth: true,
      dataSchema: BadgeProgressUpdateSchema,
      rateLimitGroup: "badge_events",
      supportsCorrelation: false,
      maxSize: 2048,
      description: "Badge progress update notification",
    },
  ],
]);

/**
 * Initialize all message type schemas in the validator
 * This function should be called during application startup
 */
export function initializeMessageSchemas(): void {
  // This will be implemented when we integrate with the validator
  // For now, we just export the definitions
}
