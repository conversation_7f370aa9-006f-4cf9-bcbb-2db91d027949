import { nanoid } from "nanoid";
import { logger, type ContextualLogger } from "../services/logger.service";
import type { ServerWebSocket } from "bun";
import type { CustomWebSocketData } from "../types/websocket.types";
import type { IWebSocketMessage } from "../types/websocket-standardization.types";

/**
 * Interface for incoming message structure
 */
interface IIncomingMessage {
  type?: string | number;
  meta?: {
    messageId?: string;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

/**
 * Interface for WebSocket log entry
 */
interface IWebSocketLogEntry {
  id: string;
  timestamp: number;
  correlationId?: string;
  userId?: string;
  sessionId?: string;
  type: WebSocketLogType;
  event: string;
  direction: "inbound" | "outbound";
  messageType?: string | number;
  messageId?: string;
  data?: unknown;
  metadata?: Record<string, unknown>;
  duration?: number;
  error?: {
    code: string;
    message: string;
    stack?: string;
  };
  performance?: {
    messageSize: number;
    processingTime: number;
    queueTime?: number;
  };
}

/**
 * Types of WebSocket log entries
 */
enum WebSocketLogType {
  CONNECTION = "connection",
  MESSAGE = "message",
  ERROR = "error",
  PERFORMANCE = "performance",
  SECURITY = "security",
  VALIDATION = "validation",
  CORRELATION = "correlation",
  HEARTBEAT = "heartbeat",
  SUBSCRIPTION = "subscription",
  RATE_LIMIT = "rate_limit",
}

/**
 * Interface for message tracing context
 */
interface IMessageTraceContext {
  correlationId: string;
  userId?: string;
  sessionId?: string;
  startTime: number;
  messageType: string | number;
  direction: "inbound" | "outbound";
  metadata?: Record<string, unknown>;
}

/**
 * Interface for performance metrics
 */
interface IPerformanceMetrics {
  messageCount: number;
  errorCount: number;
  averageLatency: number;
  totalBytes: number;
  connectionCount: number;
  rateLimitHits: number;
  validationFailures: number;
  correlationTimeouts: number;
  lastUpdated: number;
}

/**
 * Interface for error context
 */
interface IErrorContext {
  correlationId?: string;
  userId?: string;
  sessionId?: string;
  messageType?: string | number;
  messageId?: string;
  operation?: string;
  metadata?: Record<string, unknown>;
  stack?: string;
}

/**
 * WebSocket Logger - Comprehensive logging and debugging utilities for WebSocket communications
 */
export class WebSocketLogger {
  private logger: ContextualLogger;
  private logBuffer: IWebSocketLogEntry[] = [];
  private traceContexts: Map<string, IMessageTraceContext> = new Map();
  private performanceMetrics: IPerformanceMetrics;
  private readonly MAX_LOG_BUFFER_SIZE = 10000;
  private readonly MAX_TRACE_CONTEXTS = 5000;
  private readonly METRICS_UPDATE_INTERVAL = 60000; // 1 minute
  private metricsInterval?: Timer;

  public constructor(context: string = "WebSocketLogger") {
    this.logger = logger.createLogger(context);
    this.performanceMetrics = {
      messageCount: 0,
      errorCount: 0,
      averageLatency: 0,
      totalBytes: 0,
      connectionCount: 0,
      rateLimitHits: 0,
      validationFailures: 0,
      correlationTimeouts: 0,
      lastUpdated: Date.now(),
    };

    // Start metrics collection
    this.startMetricsCollection();
  }

  /**
   * Log a WebSocket connection event
   */
  public logConnection(
    event: "connect" | "disconnect" | "error" | "timeout",
    ws: ServerWebSocket<CustomWebSocketData>,
    metadata?: Record<string, unknown>,
  ): void {
    const entry: IWebSocketLogEntry = {
      id: nanoid(),
      timestamp: Date.now(),
      userId: ws.data.userId,
      sessionId: ws.data.sessionId,
      type: WebSocketLogType.CONNECTION,
      event: `connection_${event}`,
      direction: "inbound",
      metadata: {
        readyState: ws.readyState,
        serverId: ws.data.serverId,
        channelId: ws.data.channelId,
        type: ws.data.type,
        ...metadata,
      },
    };

    this.addLogEntry(entry);

    // Update connection metrics
    if (event === "connect") {
      this.performanceMetrics.connectionCount++;
    } else if (event === "disconnect") {
      this.performanceMetrics.connectionCount = Math.max(
        0,
        this.performanceMetrics.connectionCount - 1,
      );
    }

    this.logger.info(`WebSocket ${event}`, undefined, {
      userId: ws.data.userId,
      sessionId: ws.data.sessionId,
      serverId: ws.data.serverId,
      channelId: ws.data.channelId,
      ...metadata,
    });
  }

  /**
   * Log an inbound WebSocket message
   */
  public logInboundMessage(
    ws: ServerWebSocket<CustomWebSocketData>,
    message: IIncomingMessage,
    correlationId?: string,
  ): string {
    const traceId = correlationId || nanoid();
    const messageSize = JSON.stringify(message).length;

    const entry: IWebSocketLogEntry = {
      id: nanoid(),
      timestamp: Date.now(),
      correlationId: traceId,
      userId: ws.data.userId,
      sessionId: ws.data.sessionId,
      type: WebSocketLogType.MESSAGE,
      event: "message_received",
      direction: "inbound",
      messageType: message.type,
      messageId: message.meta?.messageId,
      data: this.sanitizeLogData(message),
      performance: {
        messageSize,
        processingTime: 0, // Will be updated when processing completes
      },
    };

    this.addLogEntry(entry);

    // Create trace context
    const traceContext: IMessageTraceContext = {
      correlationId: traceId,
      userId: ws.data.userId,
      sessionId: ws.data.sessionId,
      startTime: Date.now(),
      messageType: message.type,
      direction: "inbound",
      metadata: {
        messageId: message.meta?.messageId,
        serverId: ws.data.serverId,
        channelId: ws.data.channelId,
      },
    };

    this.traceContexts.set(traceId, traceContext);

    // Update metrics
    this.performanceMetrics.messageCount++;
    this.performanceMetrics.totalBytes += messageSize;

    this.logger.debug("Inbound message received", undefined, {
      correlationId: traceId,
      userId: ws.data.userId,
      messageType: message.type,
      messageId: message.meta?.messageId,
      messageSize,
    });

    return traceId;
  }

  /**
   * Log an outbound WebSocket message
   */
  public logOutboundMessage(
    ws: ServerWebSocket<CustomWebSocketData>,
    message: IWebSocketMessage,
    correlationId?: string,
  ): void {
    const messageSize = JSON.stringify(message).length;

    const entry: IWebSocketLogEntry = {
      id: nanoid(),
      timestamp: Date.now(),
      correlationId: correlationId || message.meta.correlationId,
      userId: ws.data.userId,
      sessionId: ws.data.sessionId,
      type: WebSocketLogType.MESSAGE,
      event: "message_sent",
      direction: "outbound",
      messageType: message.type,
      messageId: message.meta.id,
      data: this.sanitizeLogData(message),
      performance: {
        messageSize,
        processingTime: 0,
      },
    };

    this.addLogEntry(entry);

    // Update metrics
    this.performanceMetrics.messageCount++;
    this.performanceMetrics.totalBytes += messageSize;

    this.logger.debug("Outbound message sent", undefined, {
      correlationId: correlationId || message.meta.correlationId,
      userId: ws.data.userId,
      messageType: message.type,
      messageId: message.meta.id,
      messageSize,
    });
  }

  /**
   * Complete message tracing and log performance metrics
   */
  public completeMessageTrace(
    correlationId: string,
    success: boolean = true,
    error?: Error,
  ): void {
    const traceContext = this.traceContexts.get(correlationId);
    if (!traceContext) {
      this.logger.warn("Trace context not found for correlation", undefined, {
        correlationId,
      });
      return;
    }

    const duration = Date.now() - traceContext.startTime;

    const entry: IWebSocketLogEntry = {
      id: nanoid(),
      timestamp: Date.now(),
      correlationId,
      userId: traceContext.userId,
      sessionId: traceContext.sessionId,
      type: WebSocketLogType.PERFORMANCE,
      event: success ? "message_processed" : "message_failed",
      direction: traceContext.direction,
      messageType: traceContext.messageType,
      duration,
      metadata: traceContext.metadata,
      error: error
        ? {
            code: "PROCESSING_ERROR",
            message: error.message,
            stack: error.stack,
          }
        : undefined,
    };

    this.addLogEntry(entry);

    // Update performance metrics
    this.updateAverageLatency(duration);
    if (!success) {
      this.performanceMetrics.errorCount++;
    }

    // Clean up trace context
    this.traceContexts.delete(correlationId);

    this.logger.debug("Message trace completed", undefined, {
      correlationId,
      duration,
      success,
      messageType: traceContext.messageType,
      error: error?.message,
    });
  }

  /**
   * Log WebSocket errors with detailed context
   */
  public logError(
    error: Error | string,
    context: IErrorContext,
    severity: "low" | "medium" | "high" | "critical" = "medium",
  ): void {
    const errorMessage = typeof error === "string" ? error : error.message;
    const errorStack = typeof error === "string" ? undefined : error.stack;

    const entry: IWebSocketLogEntry = {
      id: nanoid(),
      timestamp: Date.now(),
      correlationId: context.correlationId,
      userId: context.userId,
      sessionId: context.sessionId,
      type: WebSocketLogType.ERROR,
      event: "error_occurred",
      direction: "inbound", // Errors are typically from processing inbound messages
      messageType: context.messageType,
      messageId: context.messageId,
      error: {
        code: "WEBSOCKET_ERROR",
        message: errorMessage,
        stack: errorStack || context.stack,
      },
      metadata: {
        operation: context.operation,
        severity,
        ...context.metadata,
      },
    };

    this.addLogEntry(entry);

    // Update error metrics
    this.performanceMetrics.errorCount++;

    // Log with appropriate level based on severity
    const logMethod =
      severity === "critical"
        ? "error"
        : severity === "high"
          ? "error"
          : "warn";
    this.logger[logMethod](`WebSocket error [${severity}]`, undefined, {
      correlationId: context.correlationId,
      userId: context.userId,
      messageType: context.messageType,
      operation: context.operation,
      error: errorMessage,
      stack: errorStack,
    });
  }

  /**
   * Log validation failures
   */
  public logValidationFailure(
    correlationId: string,
    userId: string,
    messageType: string | number,
    validationErrors: Array<{ field: string; message: string; code: string }>,
  ): void {
    const entry: IWebSocketLogEntry = {
      id: nanoid(),
      timestamp: Date.now(),
      correlationId,
      userId,
      type: WebSocketLogType.VALIDATION,
      event: "validation_failed",
      direction: "inbound",
      messageType,
      metadata: {
        validationErrors,
        errorCount: validationErrors.length,
      },
    };

    this.addLogEntry(entry);

    // Update validation failure metrics
    this.performanceMetrics.validationFailures++;

    this.logger.warn("Message validation failed", undefined, {
      correlationId,
      userId,
      messageType,
      errorCount: validationErrors.length,
      errors: validationErrors,
    });
  }

  /**
   * Log rate limiting events
   */
  public logRateLimit(
    userId: string,
    messageType: string | number,
    currentRate: number,
    limit: number,
    windowMs: number,
  ): void {
    const entry: IWebSocketLogEntry = {
      id: nanoid(),
      timestamp: Date.now(),
      userId,
      type: WebSocketLogType.RATE_LIMIT,
      event: "rate_limit_exceeded",
      direction: "inbound",
      messageType,
      metadata: {
        currentRate,
        limit,
        windowMs,
        exceedBy: currentRate - limit,
      },
    };

    this.addLogEntry(entry);

    // Update rate limit metrics
    this.performanceMetrics.rateLimitHits++;

    this.logger.warn("Rate limit exceeded", undefined, {
      userId,
      messageType,
      currentRate,
      limit,
      windowMs,
    });
  }

  /**
   * Log correlation timeouts
   */
  public logCorrelationTimeout(
    correlationId: string,
    userId: string,
    messageType: string | number,
    timeoutMs: number,
  ): void {
    const entry: IWebSocketLogEntry = {
      id: nanoid(),
      timestamp: Date.now(),
      correlationId,
      userId,
      type: WebSocketLogType.CORRELATION,
      event: "correlation_timeout",
      direction: "inbound",
      messageType,
      metadata: {
        timeoutMs,
        age: timeoutMs,
      },
    };

    this.addLogEntry(entry);

    // Update correlation timeout metrics
    this.performanceMetrics.correlationTimeouts++;

    this.logger.warn("Correlation timeout", undefined, {
      correlationId,
      userId,
      messageType,
      timeoutMs,
    });
  }

  /**
   * Log subscription events
   */
  public logSubscription(
    event: "subscribe" | "unsubscribe",
    userId: string,
    topic: string,
    metadata?: Record<string, unknown>,
  ): void {
    const entry: IWebSocketLogEntry = {
      id: nanoid(),
      timestamp: Date.now(),
      userId,
      type: WebSocketLogType.SUBSCRIPTION,
      event: `subscription_${event}`,
      direction: "inbound",
      metadata: {
        topic,
        ...metadata,
      },
    };

    this.addLogEntry(entry);

    this.logger.debug(`Subscription ${event}`, undefined, {
      userId,
      topic,
      ...metadata,
    });
  }

  /**
   * Get current performance metrics
   */
  public getMetrics(): IPerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * Get log entries with optional filtering
   */
  public getLogs(filter?: {
    userId?: string;
    correlationId?: string;
    type?: WebSocketLogType;
    startTime?: number;
    endTime?: number;
    limit?: number;
  }): IWebSocketLogEntry[] {
    let logs = [...this.logBuffer];

    if (filter) {
      if (filter.userId) {
        logs = logs.filter((log) => log.userId === filter.userId);
      }
      if (filter.correlationId) {
        logs = logs.filter((log) => log.correlationId === filter.correlationId);
      }
      if (filter.type) {
        logs = logs.filter((log) => log.type === filter.type);
      }
      if (filter.startTime) {
        logs = logs.filter((log) => log.timestamp >= filter.startTime!);
      }
      if (filter.endTime) {
        logs = logs.filter((log) => log.timestamp <= filter.endTime!);
      }
    }

    // Sort by timestamp (newest first)
    logs.sort((a, b) => b.timestamp - a.timestamp);

    // Apply limit
    if (filter?.limit) {
      logs = logs.slice(0, filter.limit);
    }

    return logs;
  }

  /**
   * Get message trace for a specific correlation ID
   */
  public getMessageTrace(correlationId: string): IWebSocketLogEntry[] {
    return this.logBuffer
      .filter((log) => log.correlationId === correlationId)
      .sort((a, b) => a.timestamp - b.timestamp);
  }

  /**
   * Export logs for external analysis
   */
  public exportLogs(format: "json" | "csv" = "json"): string {
    if (format === "json") {
      return JSON.stringify(this.logBuffer, null, 2);
    } else {
      // CSV format
      const headers = [
        "id",
        "timestamp",
        "correlationId",
        "userId",
        "type",
        "event",
        "direction",
        "messageType",
        "duration",
        "error",
      ];

      const csvRows = [
        headers.join(","),
        ...this.logBuffer.map((log) =>
          [
            log.id,
            log.timestamp,
            log.correlationId || "",
            log.userId || "",
            log.type,
            log.event,
            log.direction,
            log.messageType || "",
            log.duration || "",
            log.error?.message || "",
          ].join(","),
        ),
      ];

      return csvRows.join("\n");
    }
  }

  /**
   * Clear old logs to prevent memory issues
   */
  public clearOldLogs(maxAge: number = 24 * 60 * 60 * 1000): number {
    const cutoff = Date.now() - maxAge;
    const initialCount = this.logBuffer.length;

    this.logBuffer = this.logBuffer.filter((log) => log.timestamp > cutoff);

    const removedCount = initialCount - this.logBuffer.length;

    if (removedCount > 0) {
      this.logger.info(`Cleared ${removedCount} old log entries`);
    }

    return removedCount;
  }

  /**
   * Add a log entry to the buffer
   */
  private addLogEntry(entry: IWebSocketLogEntry): void {
    this.logBuffer.push(entry);

    // Prevent memory issues by limiting buffer size
    if (this.logBuffer.length > this.MAX_LOG_BUFFER_SIZE) {
      // Remove oldest 10% of entries
      const toRemove = Math.floor(this.MAX_LOG_BUFFER_SIZE * 0.1);
      this.logBuffer.splice(0, toRemove);
    }

    // Clean up old trace contexts
    if (this.traceContexts.size > this.MAX_TRACE_CONTEXTS) {
      const oldestContexts = Array.from(this.traceContexts.entries())
        .sort(([, a], [, b]) => a.startTime - b.startTime)
        .slice(0, Math.floor(this.MAX_TRACE_CONTEXTS * 0.1));

      oldestContexts.forEach(([correlationId]) => {
        this.traceContexts.delete(correlationId);
      });
    }
  }

  /**
   * Sanitize log data to remove sensitive information
   */
  private sanitizeLogData(data: unknown): unknown {
    if (!data || typeof data !== "object") {
      return data;
    }

    const sanitized = { ...data };

    // Remove sensitive fields
    const sensitiveFields = [
      "password",
      "token",
      "secret",
      "key",
      "authorization",
      "auth",
      "credential",
    ];

    const sanitizeObject = (obj: unknown): unknown => {
      if (!obj || typeof obj !== "object") {
        return obj;
      }

      if (Array.isArray(obj)) {
        return obj.map(sanitizeObject);
      }

      const result: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(obj)) {
        const lowerKey = key.toLowerCase();
        if (sensitiveFields.some((field) => lowerKey.includes(field))) {
          result[key] = "[REDACTED]";
        } else if (typeof value === "object") {
          result[key] = sanitizeObject(value);
        } else {
          result[key] = value;
        }
      }
      return result;
    };

    return sanitizeObject(sanitized);
  }

  /**
   * Update average latency metric
   */
  private updateAverageLatency(newLatency: number): void {
    const currentAvg = this.performanceMetrics.averageLatency;
    const count = this.performanceMetrics.messageCount;

    // Calculate new average using incremental formula
    this.performanceMetrics.averageLatency =
      (currentAvg * (count - 1) + newLatency) / count;
  }

  /**
   * Start metrics collection interval
   */
  private startMetricsCollection(): void {
    this.metricsInterval = setInterval(() => {
      this.performanceMetrics.lastUpdated = Date.now();

      // Log periodic metrics summary
      this.logger.info("WebSocket metrics summary", undefined, {
        messageCount: this.performanceMetrics.messageCount,
        errorCount: this.performanceMetrics.errorCount,
        averageLatency: Math.round(this.performanceMetrics.averageLatency),
        connectionCount: this.performanceMetrics.connectionCount,
        rateLimitHits: this.performanceMetrics.rateLimitHits,
        validationFailures: this.performanceMetrics.validationFailures,
        correlationTimeouts: this.performanceMetrics.correlationTimeouts,
        totalBytes: this.performanceMetrics.totalBytes,
      });
    }, this.METRICS_UPDATE_INTERVAL);
  }

  /**
   * Stop metrics collection and cleanup
   */
  public destroy(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }

    // Clear all data
    this.logBuffer.length = 0;
    this.traceContexts.clear();

    this.logger.info("WebSocket logger destroyed");
  }
}

// Export singleton instance
export const webSocketLogger = new WebSocketLogger();

// Export types for external use
export type {
  IWebSocketLogEntry,
  IMessageTraceContext,
  IPerformanceMetrics,
  IErrorContext,
};
export { WebSocketLogType };
