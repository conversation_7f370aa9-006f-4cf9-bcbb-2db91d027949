{"name": "oba", "module": "index.ts", "scripts": {"dev": "bun run --watch index.ts", "test": "bun run test:all", "test:all": "bun run test:unit && bun run test:integration", "test:unit": "bun run test:user && bun run test:message && bun run test:directMessage && bun run test:auth:unit && bun run test:friends:unit && bun run test:status:unit && bun run test:channels:unit && bun run test:permissions:unit && bun run test:serverMembers:unit && bun run test:binaryProtocol && bun run test:sfu", "test:integration": "bun run test:auth:integration && bun run test:messages && bun run test:directMessages && bun run test:friends:integration && bun run test:status:integration && bun run test:channels:integration && bun run test:roles:integration", "test:user": "bun test tests/unit/user.test.ts", "test:message": "bun test tests/unit/message.test.ts", "test:directMessage": "bun test tests/unit/directMessage.test.ts", "test:auth:unit": "bun test tests/unit/auth.test.ts", "test:friends:unit": "bun test tests/unit/friends.test.ts", "test:status:unit": "bun test tests/unit/status.test.ts", "test:channels:unit": "bun test tests/unit/channels.test.ts", "test:permissions:unit": "bun test tests/unit/permissions.test.ts", "test:serverMembers:unit": "bun test tests/unit/serverMembers.test.ts", "test:binaryProtocol": "bun test tests/unit/binaryProtocol.test.ts", "test:sfu": "bun test tests/unit/sfuService.test.ts", "test:auth:integration": "bun test tests/integration/auth.test.ts", "test:messages": "bun test tests/integration/messages.test.ts", "test:directMessages": "bun test tests/integration/directMessages.test.ts", "test:friends:integration": "bun test tests/integration/friends.test.ts", "test:status:integration": "bun test tests/integration/status.test.ts", "test:channels:integration": "bun test tests/integration/channels.test.ts", "test:roles:integration": "bun test tests/integration/roles.test.ts", "test:watch": "bun test --watch", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write .", "type-check": "tsc --noEmit"}, "devDependencies": {"@eslint/js": "^9.31.0", "@types/bun": "latest", "drizzle-kit": "^0.30.6"}, "peerDependencies": {"typescript": "^5.7.2"}, "type": "module", "dependencies": {"@kurultai/oba-types": "^0.1.4", "@napi-rs/canvas": "^0.1.73", "@types/validator": "^13.12.2", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "argon2": "^0.41.1", "drizzle-orm": "^0.43.1", "eslint": "^9.30.0", "ioredis": "^5.4.2", "jose": "^5.9.6", "minio": "^8.0.5", "nanoid": "^5.0.9", "postgres": "^3.4.5", "prettier": "^3.6.2", "uuid": "^11.0.4", "validator": "^13.12.0", "zod": "^3.24.1"}}