#!/usr/bin/env bun

/**
 * Integration test for badge WebSocket events with WebSocket manager
 * This test demonstrates how badge events integrate with the existing WebSocket infrastructure
 */

import { WebSocketManager } from "./manager/websocket.manager";
import { badgeWebSocketService } from "./utils/badge-websocket";
import { WebSocketUtils } from "./utils/websocket-utils";
import { EventTypes } from "./constants/eventTypes";
import type {
  BadgeAssignedPayload,
  BadgeRemovedPayload,
  BadgeProgressUpdatePayload
} from "./types/badge-websocket.types";
import type { BadgeType, UserBadge } from "./types/badge.types";

// Mock WebSocket connections for testing
function createMockWebSocket(userId: string, serverId?: string, channelId?: string) {
  const messages: string[] = [];
  
  return {
    data: {
      userId,
      serverId,
      channelId,
      isAuthenticated: true,
      isAlive: true,
      token: "mock-token",
      type: serverId && channelId ? "channel" : "private"
    },
    readyState: 1, // WebSocket.OPEN
    send: (message: string) => {
      messages.push(message);
      console.log(`📤 [${userId}] Received:`, JSON.parse(message));
    },
    messages, // Store messages for verification
    close: () => {},
    ping: () => {},
    pong: () => {}
  } as any;
}

async function testBadgeWebSocketIntegration() {
  console.log("🧪 Testing Badge WebSocket Integration\n");

  const wsManager = WebSocketManager.getInstance();

  // Create mock users and WebSocket connections
  const user1WS = createMockWebSocket("user-1", "server-1", "channel-1");
  const user2WS = createMockWebSocket("user-2", "server-1", "channel-2");
  const user3WS = createMockWebSocket("user-3"); // Private connection only
  const adminWS = createMockWebSocket("admin-user");

  // Mock badge type and user badge
  const mockBadgeType: BadgeType = {
    id: "achievement-badge-1",
    name: "First Achievement",
    description: "Your first achievement badge",
    iconUrl: "https://example.com/achievement.png",
    color: "#FFD700",
    category: "achievement",
    isActive: true,
    assignmentType: "manual",
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const mockUserBadge: UserBadge = {
    id: "user-badge-1",
    userId: "user-1",
    badgeTypeId: "achievement-badge-1",
    badgeType: mockBadgeType,
    assignedBy: "admin-user",
    assignedAt: new Date(),
    isVisible: true
  };

  try {
    console.log("1️⃣ Setting up WebSocket connections");
    console.log("===================================");

    // Add connections to WebSocket manager
    wsManager.add(user1WS);
    wsManager.add(user2WS);
    wsManager.addPrivateConnection(user3WS);
    wsManager.addPrivateConnection(adminWS);

    console.log("✅ Added 4 WebSocket connections to manager");
    console.log(`- user-1: channel connection (server-1/channel-1)`);
    console.log(`- user-2: channel connection (server-1/channel-2)`);
    console.log(`- user-3: private connection`);
    console.log(`- admin-user: private connection`);

    console.log("\n2️⃣ Testing Badge Assignment Event");
    console.log("==================================");

    const assignedPayload: BadgeAssignedPayload = {
      userId: "user-1",
      badge: mockUserBadge,
      isAutomatic: false,
      assignedBy: "admin-user"
    };

    console.log("📋 Broadcasting badge assigned event...");
    await badgeWebSocketService.broadcastBadgeAssigned(assignedPayload);

    // Verify messages were sent
    console.log("✅ Badge assigned event broadcasted");
    console.log(`- user-1 received: ${user1WS.messages.length} message(s)`);
    console.log(`- admin-user received: ${adminWS.messages.length} message(s)`);

    console.log("\n3️⃣ Testing Badge Removal Event");
    console.log("===============================");

    const removedPayload: BadgeRemovedPayload = {
      userId: "user-1",
      badgeTypeId: "achievement-badge-1",
      badgeType: mockBadgeType,
      removedBy: "admin-user"
    };

    console.log("🗑️ Broadcasting badge removed event...");
    await badgeWebSocketService.broadcastBadgeRemoved(removedPayload);

    console.log("✅ Badge removed event broadcasted");
    console.log(`- user-1 total messages: ${user1WS.messages.length}`);
    console.log(`- admin-user total messages: ${adminWS.messages.length}`);

    console.log("\n4️⃣ Testing Badge Progress Update Event");
    console.log("======================================");

    const progressPayload: BadgeProgressUpdatePayload = {
      userId: "user-2",
      badgeTypeId: "achievement-badge-1",
      badgeType: mockBadgeType,
      progress: 8,
      total: 10,
      progressPercentage: 80
    };

    console.log("📊 Broadcasting badge progress update...");
    await badgeWebSocketService.broadcastBadgeProgressUpdate(progressPayload);

    console.log("✅ Badge progress update broadcasted");
    console.log(`- user-2 received: ${user2WS.messages.length} message(s)`);

    console.log("\n5️⃣ Testing Batch Badge Operations");
    console.log("==================================");

    // Test batch badge assignments
    const batchAssignments: BadgeAssignedPayload[] = [
      {
        userId: "user-2",
        badge: { ...mockUserBadge, id: "user-badge-2", userId: "user-2" },
        isAutomatic: true,
        assignedBy: undefined
      },
      {
        userId: "user-3",
        badge: { ...mockUserBadge, id: "user-badge-3", userId: "user-3" },
        isAutomatic: true,
        assignedBy: undefined
      }
    ];

    console.log("📦 Broadcasting batch badge assignments...");
    await badgeWebSocketService.broadcastBatchBadgeAssigned(batchAssignments);

    console.log("✅ Batch badge assignments broadcasted");
    console.log(`- user-2 total messages: ${user2WS.messages.length}`);
    console.log(`- user-3 total messages: ${user3WS.messages.length}`);

    // Test batch progress updates
    const batchProgressUpdates: BadgeProgressUpdatePayload[] = [
      {
        userId: "user-1",
        badgeTypeId: "achievement-badge-1",
        badgeType: mockBadgeType,
        progress: 5,
        total: 10,
        progressPercentage: 50
      },
      {
        userId: "user-3",
        badgeTypeId: "achievement-badge-1",
        badgeType: mockBadgeType,
        progress: 9,
        total: 10,
        progressPercentage: 90
      }
    ];

    console.log("📊 Broadcasting batch progress updates...");
    await badgeWebSocketService.broadcastBatchProgressUpdates(batchProgressUpdates);

    console.log("✅ Batch progress updates broadcasted");
    console.log(`- user-1 total messages: ${user1WS.messages.length}`);
    console.log(`- user-3 total messages: ${user3WS.messages.length}`);

    console.log("\n6️⃣ Testing Badge Notifications");
    console.log("===============================");

    console.log("🔔 Sending badge notifications...");
    await badgeWebSocketService.sendBadgeNotification(
      "user-1",
      "achievement",
      { message: "Congratulations on your new achievement!" }
    );

    await badgeWebSocketService.sendBadgeNotification(
      "user-2",
      "milestone",
      { message: "You're almost there! Keep going!" }
    );

    console.log("✅ Badge notifications sent");
    console.log(`- user-1 total messages: ${user1WS.messages.length}`);
    console.log(`- user-2 total messages: ${user2WS.messages.length}`);

    console.log("\n7️⃣ Testing Direct WebSocket Manager Integration");
    console.log("===============================================");

    // Test direct integration with WebSocket manager
    const directMessage = WebSocketUtils.event(EventTypes.BADGE_ASSIGNED, {
      type: "BADGE_ASSIGNED",
      userId: "user-3",
      badge: mockUserBadge,
      isAutomatic: false,
      assignedBy: "admin-user",
      timestamp: new Date()
    });

    console.log("🔗 Sending direct message via WebSocket manager...");
    wsManager.broadcastToUser("user-3", directMessage);

    console.log("✅ Direct message sent");
    console.log(`- user-3 total messages: ${user3WS.messages.length}`);

    console.log("\n8️⃣ Message Analysis");
    console.log("====================");

    // Analyze received messages
    const allUsers = [
      { name: "user-1", ws: user1WS },
      { name: "user-2", ws: user2WS },
      { name: "user-3", ws: user3WS },
      { name: "admin-user", ws: adminWS }
    ];

    allUsers.forEach(({ name, ws }) => {
      console.log(`\n📋 ${name} received ${ws.messages.length} messages:`);
      ws.messages.forEach((msg, index) => {
        try {
          const parsed = JSON.parse(msg);
          console.log(`  ${index + 1}. ${parsed.event || parsed.type} - ${parsed.type}`);
        } catch (error) {
          console.log(`  ${index + 1}. [Invalid JSON]`);
        }
      });
    });

    console.log("\n🎉 Badge WebSocket Integration Test Completed!");
    console.log("\nSummary of tested functionality:");
    console.log("- ✅ Badge assignment event broadcasting");
    console.log("- ✅ Badge removal event broadcasting");
    console.log("- ✅ Badge progress update broadcasting");
    console.log("- ✅ Batch badge operation events");
    console.log("- ✅ Badge notification system");
    console.log("- ✅ Integration with WebSocket manager");
    console.log("- ✅ Message delivery to appropriate users");

    return true;

  } catch (error) {
    console.error("❌ Integration test failed:", error);
    throw error;
  } finally {
    // Cleanup
    wsManager.remove(user1WS);
    wsManager.remove(user2WS);
    wsManager.remove(user3WS);
    wsManager.remove(adminWS);
    console.log("\n🧹 Cleaned up WebSocket connections");
  }
}

// Run the test
if (import.meta.main) {
  testBadgeWebSocketIntegration()
    .then(() => {
      console.log("\n✅ Badge WebSocket integration tests completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n❌ Badge WebSocket integration tests failed:", error);
      process.exit(1);
    });
}

export { testBadgeWebSocketIntegration };