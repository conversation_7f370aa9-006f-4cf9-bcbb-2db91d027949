#!/usr/bin/env bun

/**
 * Test script for Task 7: User Badge Management Endpoints
 * 
 * This script tests all the endpoints required for task 7:
 * - DELETE /api/users/:userId/badges/:badgeId - for badge removal
 * - GET /api/badges/available - for showing available badges
 * - POST /api/badges/evaluate/:userId - for triggering evaluation
 * - GET /api/badges/stats - for badge statistics
 * - GET /api/badges/leaderboard - for badge rankings
 */

import { db } from "./db";
import { BadgeService } from "./services/badge.service";

const BASE_URL = "http://localhost:3005";

// Test user credentials (using proper UUID format)
const TEST_USER_ID = "01234567-89ab-cdef-0123-456789abcdef";
const TEST_BADGE_ID = "fedcba98-7654-3210-fedc-ba9876543210";
const TEST_TOKEN = "test-jwt-token"; // You'll need a valid JWT token for protected endpoints

async function testEndpoints() {
  console.log("🧪 Testing Task 7: User Badge Management Endpoints\n");

  // Test 1: GET /api/badges/available
  console.log("1️⃣ Testing GET /api/badges/available");
  try {
    const response = await fetch(`${BASE_URL}/api/badges/available`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${TEST_TOKEN}`,
        "Content-Type": "application/json"
      }
    });
    
    console.log(`   Status: ${response.status}`);
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ Available badges endpoint working`);
      console.log(`   📊 Response:`, JSON.stringify(data, null, 2));
    } else {
      console.log(`   ❌ Failed: ${response.statusText}`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error}`);
  }
  console.log();

  // Test 2: GET /api/badges/stats
  console.log("2️⃣ Testing GET /api/badges/stats");
  try {
    const response = await fetch(`${BASE_URL}/api/badges/stats`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json"
      }
    });
    
    console.log(`   Status: ${response.status}`);
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ Badge stats endpoint working`);
      console.log(`   📊 Response:`, JSON.stringify(data, null, 2));
    } else {
      console.log(`   ❌ Failed: ${response.statusText}`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error}`);
  }
  console.log();

  // Test 3: GET /api/badges/leaderboard
  console.log("3️⃣ Testing GET /api/badges/leaderboard");
  try {
    const response = await fetch(`${BASE_URL}/api/badges/leaderboard?limit=5`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json"
      }
    });
    
    console.log(`   Status: ${response.status}`);
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ Badge leaderboard endpoint working`);
      console.log(`   📊 Response:`, JSON.stringify(data, null, 2));
    } else {
      console.log(`   ❌ Failed: ${response.statusText}`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error}`);
  }
  console.log();

  // Test 4: POST /api/badges/evaluate/:userId
  console.log("4️⃣ Testing POST /api/badges/evaluate/:userId");
  try {
    const response = await fetch(`${BASE_URL}/api/badges/evaluate/${TEST_USER_ID}`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${TEST_TOKEN}`,
        "Content-Type": "application/json"
      }
    });
    
    console.log(`   Status: ${response.status}`);
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ Badge evaluation endpoint working`);
      console.log(`   📊 Response:`, JSON.stringify(data, null, 2));
    } else {
      console.log(`   ❌ Failed: ${response.statusText}`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error}`);
  }
  console.log();

  // Test 5: DELETE /api/users/:userId/badges/:badgeId
  console.log("5️⃣ Testing DELETE /api/users/:userId/badges/:badgeId");
  try {
    const response = await fetch(`${BASE_URL}/api/users/${TEST_USER_ID}/badges/${TEST_BADGE_ID}`, {
      method: "DELETE",
      headers: {
        "Authorization": `Bearer ${TEST_TOKEN}`,
        "Content-Type": "application/json"
      }
    });
    
    console.log(`   Status: ${response.status}`);
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ Badge removal endpoint working`);
      console.log(`   📊 Response:`, JSON.stringify(data, null, 2));
    } else {
      console.log(`   ❌ Failed: ${response.statusText}`);
      const errorData = await response.text();
      console.log(`   📄 Error response:`, errorData);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error}`);
  }
  console.log();

  console.log("🎯 Task 7 endpoint testing completed!");
}

// Test the badge service directly
async function testBadgeServiceMethods() {
  console.log("🔧 Testing Badge Service Methods Directly\n");

  const badgeService = new BadgeService(db);

  try {
    // Test getAvailableBadgesForUser
    console.log("1️⃣ Testing getAvailableBadgesForUser method");
    const availableBadges = await badgeService.getAvailableBadgesForUser(TEST_USER_ID);
    console.log(`   ✅ Available badges:`, availableBadges);
    console.log();

    // Test getBadgeStats
    console.log("2️⃣ Testing getBadgeStats method");
    const stats = await badgeService.getBadgeStats();
    console.log(`   ✅ Badge stats:`, stats);
    console.log();

    // Test getBadgeLeaderboard
    console.log("3️⃣ Testing getBadgeLeaderboard method");
    const leaderboard = await badgeService.getBadgeLeaderboard(5);
    console.log(`   ✅ Badge leaderboard:`, leaderboard);
    console.log();

    // Test evaluateUserBadges
    console.log("4️⃣ Testing evaluateUserBadges method");
    const evaluationResult = await badgeService.evaluateUserBadges(TEST_USER_ID);
    console.log(`   ✅ Evaluation result:`, evaluationResult);
    console.log();

  } catch (error) {
    console.error("❌ Error testing badge service methods:", error);
  }
}

// Run tests
async function runAllTests() {
  console.log("🚀 Starting Task 7 Badge Management Endpoints Tests\n");
  
  // Test service methods first
  await testBadgeServiceMethods();
  
  console.log("=" .repeat(60));
  console.log();
  
  // Test HTTP endpoints
  await testEndpoints();
}

if (import.meta.main) {
  runAllTests().catch(console.error);
}