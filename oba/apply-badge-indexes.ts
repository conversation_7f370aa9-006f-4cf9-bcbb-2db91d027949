import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { readFileSync } from "fs";
import { join } from "path";

// Database connection
const connectionString = process.env.DATABASE_URL || "postgresql://postgres:password@localhost:5432/oba_db";
const sql = postgres(connectionString);
const db = drizzle(sql);

async function applyBadgeIndexes() {
  try {
    console.log("Applying badge system indexes...");
    
    // Read the SQL file
    const sqlContent = readFileSync(join(__dirname, "drizzle/0007_badge_system_indexes.sql"), "utf-8");
    
    // Split by statements and execute each one
    const statements = sqlContent
      .split("--")
      .map(s => s.trim())
      .filter(s => s.length > 0 && !s.startsWith("Additional indexes"))
      .map(s => s.replace(/\n/g, " ").trim())
      .filter(s => s.length > 0);

    for (const statement of statements) {
      if (statement.startsWith("CREATE INDEX")) {
        console.log(`Executing: ${statement.substring(0, 50)}...`);
        await sql.unsafe(statement);
      }
    }
    
    console.log("✅ Badge system indexes applied successfully!");
  } catch (error) {
    console.error("❌ Error applying badge indexes:", error);
  } finally {
    await sql.end();
  }
}

applyBadgeIndexes();