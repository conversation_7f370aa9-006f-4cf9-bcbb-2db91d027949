#!/usr/bin/env bun

/**
 * Comprehensive test for badge API endpoints
 */

import {
  getBadgeTypesHandler,
  createBadgeType<PERSON><PERSON><PERSON>,
  update<PERSON>adgeT<PERSON><PERSON><PERSON><PERSON>,
  deleteBadgeType<PERSON>and<PERSON>,
  getUserBadgesHand<PERSON>,
  assignBadgeToUser<PERSON>and<PERSON>,
  removeBadgeFromUser<PERSON>andler,
  getAvailableBadgesHandler,
  evaluateUserBadgesHandler,
  getBadgeStatsHandler,
  getBadgeLeaderboardHandler,
} from "./handlers/badges";

// Mock request helper
function createMockRequest(method: string, url: string, body?: any, headers?: Record<string, string>): Request {
  const requestInit: RequestInit = {
    method,
    headers: {
      "Content-Type": "application/json",
      ...headers
    }
  };

  if (body && (method === "POST" || method === "PUT")) {
    requestInit.body = JSON.stringify(body);
  }

  return new Request(url, requestInit);
}

// Mock authenticated request (adds user info)
function createAuthenticatedRequest(method: string, url: string, body?: any): Request {
  const req = createMockRequest(method, url, body, {
    "Authorization": "Bearer mock-token"
  });
  
  // Mock the user property that auth middleware would add
  (req as any).user = {
    userId: "test-user-123",
    email: "<EMAIL>"
  };
  
  return req;
}

async function testBadgeHandlers() {
  console.log("🧪 Testing Badge API Handlers...\n");

  try {
    // Test 1: Get badge types (GET)
    console.log("1. Testing GET badge types handler...");
    const getTypesReq = createMockRequest("GET", "http://localhost:3005/api/badges/types");
    const getTypesResponse = await getBadgeTypesHandler(getTypesReq);
    const getTypesData = await getTypesResponse.json();
    console.log("✅ GET badge types:", getTypesData.success ? "SUCCESS" : "FAILED");

    // Test 2: Create badge type (POST) - authenticated
    console.log("\n2. Testing POST create badge type handler...");
    const createReq = createAuthenticatedRequest("POST", "http://localhost:3005/api/badges/types/create", {
      name: "Test Handler Badge",
      description: "A test badge created via handler",
      category: "achievement",
      assignmentType: "manual",
      color: "#FF5733"
    });
    const createResponse = await createBadgeTypeHandler(createReq);
    const createData = await createResponse.json();
    console.log("✅ POST create badge type:", createData.success ? "SUCCESS" : "FAILED");
    
    let createdBadgeId = null;
    if (createData.success && createData.data) {
      createdBadgeId = createData.data.id;
      console.log("   Created badge ID:", createdBadgeId);
    }

    // Test 3: Update badge type (PUT) - authenticated
    if (createdBadgeId) {
      console.log("\n3. Testing PUT update badge type handler...");
      const updateReq = createAuthenticatedRequest("PUT", `http://localhost:3005/api/badges/types/${createdBadgeId}/update`, {
        description: "Updated test badge description via handler"
      });
      const updateResponse = await updateBadgeTypeHandler(updateReq, { id: createdBadgeId });
      const updateData = await updateResponse.json();
      console.log("✅ PUT update badge type:", updateData.success ? "SUCCESS" : "FAILED");
    }

    // Test 4: Get badge stats (GET)
    console.log("\n4. Testing GET badge stats handler...");
    const statsReq = createMockRequest("GET", "http://localhost:3005/api/badges/stats");
    const statsResponse = await getBadgeStatsHandler(statsReq);
    const statsData = await statsResponse.json();
    console.log("✅ GET badge stats:", statsData.success ? "SUCCESS" : "FAILED");
    if (statsData.success) {
      console.log("   Stats:", {
        totalBadges: statsData.data.totalBadges,
        totalAssignments: statsData.data.totalAssignments
      });
    }

    // Test 5: Get badge leaderboard (GET)
    console.log("\n5. Testing GET badge leaderboard handler...");
    const leaderboardReq = createMockRequest("GET", "http://localhost:3005/api/badges/leaderboard?limit=5");
    const leaderboardResponse = await getBadgeLeaderboardHandler(leaderboardReq);
    const leaderboardData = await leaderboardResponse.json();
    console.log("✅ GET badge leaderboard:", leaderboardData.success ? "SUCCESS" : "FAILED");

    // Test 6: Get user badges (GET)
    console.log("\n6. Testing GET user badges handler...");
    const userBadgesReq = createMockRequest("GET", "http://localhost:3005/api/users/test-user-123/badges");
    const userBadgesResponse = await getUserBadgesHandler(userBadgesReq, { userId: "test-user-123" });
    const userBadgesData = await userBadgesResponse.json();
    console.log("✅ GET user badges:", userBadgesData.success ? "SUCCESS" : "FAILED");

    // Test 7: Get available badges (GET) - authenticated
    console.log("\n7. Testing GET available badges handler...");
    const availableReq = createAuthenticatedRequest("GET", "http://localhost:3005/api/badges/available");
    const availableResponse = await getAvailableBadgesHandler(availableReq);
    const availableData = await availableResponse.json();
    console.log("✅ GET available badges:", availableData.success ? "SUCCESS" : "FAILED");

    // Test 8: Evaluate user badges (POST) - authenticated
    console.log("\n8. Testing POST evaluate user badges handler...");
    const evaluateReq = createAuthenticatedRequest("POST", "http://localhost:3005/api/badges/evaluate/test-user-123");
    const evaluateResponse = await evaluateUserBadgesHandler(evaluateReq, { userId: "test-user-123" });
    const evaluateData = await evaluateResponse.json();
    console.log("✅ POST evaluate user badges:", evaluateData.success ? "SUCCESS" : "FAILED");

    // Test 9: Assign badge to user (POST) - authenticated
    if (createdBadgeId) {
      console.log("\n9. Testing POST assign badge to user handler...");
      const assignReq = createAuthenticatedRequest("POST", "http://localhost:3005/api/users/test-user-123/badges/assign", {
        badgeTypeId: createdBadgeId
      });
      const assignResponse = await assignBadgeToUserHandler(assignReq, { userId: "test-user-123" });
      const assignData = await assignResponse.json();
      console.log("✅ POST assign badge to user:", assignData.success ? "SUCCESS" : "FAILED");

      // Test 10: Remove badge from user (DELETE) - authenticated
      if (assignData.success) {
        console.log("\n10. Testing DELETE remove badge from user handler...");
        const removeReq = createAuthenticatedRequest("DELETE", `http://localhost:3005/api/users/test-user-123/badges/${createdBadgeId}/remove`);
        const removeResponse = await removeBadgeFromUserHandler(removeReq, { userId: "test-user-123", badgeId: createdBadgeId });
        const removeData = await removeResponse.json();
        console.log("✅ DELETE remove badge from user:", removeData.success ? "SUCCESS" : "FAILED");
      }
    }

    // Test 11: Delete badge type (DELETE) - authenticated
    if (createdBadgeId) {
      console.log("\n11. Testing DELETE badge type handler...");
      const deleteReq = createAuthenticatedRequest("DELETE", `http://localhost:3005/api/badges/types/${createdBadgeId}/delete`);
      const deleteResponse = await deleteBadgeTypeHandler(deleteReq, { id: createdBadgeId });
      const deleteData = await deleteResponse.json();
      console.log("✅ DELETE badge type:", deleteData.success ? "SUCCESS" : "FAILED");
    }

    console.log("\n🎉 All badge handler tests completed!");

  } catch (error) {
    console.error("❌ Badge handler test failed:", error);
    process.exit(1);
  }
}

// Test HTTP method validation
async function testMethodValidation() {
  console.log("\n🔒 Testing HTTP method validation...\n");

  try {
    // Test wrong method for GET endpoint
    console.log("1. Testing wrong method (POST) for GET endpoint...");
    const wrongMethodReq = createMockRequest("POST", "http://localhost:3005/api/badges/types");
    const wrongMethodResponse = await getBadgeTypesHandler(wrongMethodReq);
    console.log("✅ Method validation:", wrongMethodResponse.status === 405 ? "WORKING" : "FAILED");

    // Test wrong method for POST endpoint
    console.log("\n2. Testing wrong method (GET) for POST endpoint...");
    const wrongMethodReq2 = createAuthenticatedRequest("GET", "http://localhost:3005/api/badges/types/create");
    const wrongMethodResponse2 = await createBadgeTypeHandler(wrongMethodReq2);
    console.log("✅ Method validation:", wrongMethodResponse2.status === 405 ? "WORKING" : "FAILED");

    console.log("\n🎉 Method validation tests completed!");

  } catch (error) {
    console.error("❌ Method validation test failed:", error);
  }
}

// Run all tests
async function runAllTests() {
  await testBadgeHandlers();
  await testMethodValidation();
  console.log("\n✨ Badge API comprehensive testing complete!");
}

runAllTests().catch((error) => {
  console.error("💥 Test execution failed:", error);
  process.exit(1);
});