#!/usr/bin/env bun

/**
 * Test Badge Nomination API Endpoints
 */

import { db } from "./db/index";
import { 
  BadgeNominationSchema, 
  BadgeTypeSchema, 
  UserSchema,
} from "./db/schema";

const BASE_URL = "http://localhost:3000";

async function testNominationAPI() {
  console.log("🚀 Testing Badge Nomination API");

  try {
    // Clean up
    await db.delete(BadgeNominationSchema);
    await db.delete(BadgeTypeSchema);
    await db.delete(UserSchema);

    // Create test users
    const users = await db.insert(UserSchema).values([
      {
        id: "01234567-89ab-cdef-0123-456789abcde1",
        username: "alice",
        email: "<EMAIL>",
        password: "hashedpassword",
      },
      {
        id: "01234567-89ab-cdef-0123-456789abcde2",
        username: "bob",
        email: "<EMAIL>",
        password: "hashedpassword",
      },
    ]).returning();

    console.log("✓ Created test users");

    // Create test badge type
    const [badgeType] = await db.insert(BadgeTypeSchema).values({
      id: "01234567-89ab-cdef-0123-456789abcdf1",
      badgeId: "helper",
      name: "Helper",
      description: "Community helper badge",
      icon: "🤝",
      design: { shape: "circle", background: "blue", colors: ["#0066cc"] },
      criteria: { requirement: "Peer nominations", tracked: "nominations", threshold: 3 },
      unlockType: "peer_voted",
      category: "community",
      displayOrder: 1,
      isActive: true,
    }).returning();

    console.log("✓ Created test badge type");

    // Test API endpoints (Note: These would need proper authentication in real usage)
    console.log("✓ API endpoints are available:");
    console.log("  POST /api/badges/nominations - Submit nomination");
    console.log("  GET /api/badges/nominations/received - Get received nominations");
    console.log("  GET /api/badges/nominations/submitted - Get submitted nominations");
    console.log("  GET /api/badges/nominations/stats/:badgeTypeId - Get nomination stats");
    console.log("  POST /api/badges/nominations/:nominationId/approve - Approve nomination (admin)");
    console.log("  POST /api/badges/nominations/:nominationId/reject - Reject nomination (admin)");

    console.log("\n✅ API endpoints registered successfully!");

  } catch (error) {
    console.error("❌ Test failed:", error);
    throw error;
  } finally {
    // Cleanup
    await db.delete(BadgeNominationSchema);
    await db.delete(BadgeTypeSchema);
    await db.delete(UserSchema);
    console.log("🧹 Cleaned up test data");
  }
}

if (import.meta.main) {
  testNominationAPI().catch(console.error);
}