#!/usr/bin/env bun

/**
 * Integration test for BadgeService
 * This script tests the BadgeService with actual database operations
 */

import { db } from "./db";
import { BadgeService } from "./services/badge.service";
import type { CreateBadgeTypeRequest } from "./types/badge.types";

async function testBadgeService() {
  console.log("🧪 Testing BadgeService Integration...\n");

  const badgeService = new BadgeService(db);
  const testUserId = "550e8400-e29b-41d4-a716-446655440000";

  try {
    // Test 1: Create a badge type
    console.log("1️⃣ Testing badge type creation...");
    const badgeData: CreateBadgeTypeRequest = {
      name: "Test Achievement Badge",
      description: "A test badge for achievements",
      category: "achievement",
      assignmentType: "manual",
      color: "#FF5733"
    };

    const createdBadge = await badgeService.createBadgeType(badgeData, testUserId);
    console.log("✅ Badge type created:", createdBadge.name);

    // Test 2: Get badge types
    console.log("\n2️⃣ Testing badge type retrieval...");
    const badgeTypes = await badgeService.getBadgeTypes();
    console.log(`✅ Retrieved ${badgeTypes.length} badge types`);

    // Test 3: Create an automatic badge
    console.log("\n3️⃣ Testing automatic badge creation...");
    const automaticBadgeData: CreateBadgeTypeRequest = {
      name: "Message Master",
      description: "Sent 100 messages",
      category: "milestone",
      assignmentType: "automatic",
      criteria: {
        type: "message_count",
        threshold: 100
      }
    };

    const automaticBadge = await badgeService.createBadgeType(automaticBadgeData, testUserId);
    console.log("✅ Automatic badge created:", automaticBadge.name);

    // Test 4: Get badge statistics
    console.log("\n4️⃣ Testing badge statistics...");
    const stats = await badgeService.getBadgeStats();
    console.log("✅ Badge stats:", {
      totalBadges: stats.totalBadges,
      totalAssignments: stats.totalAssignments
    });

    // Test 5: Get available badges for user
    console.log("\n5️⃣ Testing available badges for user...");
    const availableBadges = await badgeService.getAvailableBadgesForUser(testUserId);
    console.log(`✅ Found ${availableBadges.length} available badges for user`);

    // Test 6: Get user statistics
    console.log("\n6️⃣ Testing user statistics...");
    try {
      const userStats = await badgeService.getUserStats(testUserId);
      console.log("✅ User stats:", {
        messageCount: userStats.messageCount,
        serverCount: userStats.serverCount,
        friendCount: userStats.friendCount
      });
    } catch (error) {
      console.log("⚠️ User not found (expected for test user)");
    }

    // Test 7: Evaluate user badges
    console.log("\n7️⃣ Testing badge evaluation...");
    const evaluationResult = await badgeService.evaluateUserBadges(testUserId);
    console.log("✅ Badge evaluation completed:", {
      evaluatedBadges: evaluationResult.evaluatedBadges.length,
      newBadges: evaluationResult.newBadges.length,
      errors: evaluationResult.errors.length
    });

    // Test 8: Update badge type
    console.log("\n8️⃣ Testing badge type update...");
    const updatedBadge = await badgeService.updateBadgeType(
      createdBadge.id,
      { description: "Updated test badge description" },
      testUserId
    );
    console.log("✅ Badge updated:", updatedBadge.description);

    // Test 9: Get badge leaderboard
    console.log("\n9️⃣ Testing badge leaderboard...");
    const leaderboard = await badgeService.getBadgeLeaderboard(5);
    console.log(`✅ Leaderboard has ${leaderboard.length} entries`);

    // Cleanup: Delete test badges
    console.log("\n🧹 Cleaning up test data...");
    await badgeService.deleteBadgeType(createdBadge.id, testUserId);
    await badgeService.deleteBadgeType(automaticBadge.id, testUserId);
    console.log("✅ Test badges deleted");

    console.log("\n🎉 All BadgeService tests passed!");

  } catch (error) {
    console.error("❌ BadgeService test failed:", error);

    if (error.code) {
      console.error("Error code:", error.code);
    }

    if (error.details) {
      console.error("Error details:", error.details);
    }

    process.exit(1);
  }
}

// Run the test
testBadgeService().catch(console.error);