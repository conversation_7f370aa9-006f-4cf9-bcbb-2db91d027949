/**
 * Test script for enhanced badge validation functionality
 */

import {
    validateBadgeCollectionCreation,
    validateBadgeCollectionUpdate,
    validateBadgeTypeCreation,
    validateBadgeTypeUpdate,
    validateBadgeNomination,
    validateNominationProcessing,
    validateBadgeDesign,
    validateBadgeCriteria,
    validateComplexCriteria,
    validateUserStats,
    validateCollectionOrder,
    validateBadgeAssignmentEligibility,
    evaluateBadgeCriteria,
    validateCollectionCompletion,
} from "./utils/badge-validation-enhanced";
import {
    BadgeValidationError,
    BadgeDesignValidationError,
    BadgeNominationError,
    InvalidBadgeCriteriaError,
    SelfNominationError,
} from "./class/badge-errors";
import type {
    BadgeCollection,
    BadgeType,
    BadgeDesign,
    BadgeCriteria,
    UserStats,
} from "./types/badge.types";

console.log("🧪 Testing Enhanced Badge Validation...\n");

// Test data
const validBadgeCollection = {
    collectionId: "early-adopters",
    name: "Early Adopters",
    description: "Badges for early platform users",
    type: "progressive" as const,
    unlockedBy: "signup_date",
    completionReward: {
        badge: "pioneer-legend",
        title: "Pioneer Legend",
        perks: ["special_role", "exclusive_channel"],
        visual: "golden_crown",
        animation: "glow"
    }
};

const validBadgeType = {
    collectionId: "550e8400-e29b-41d4-a716-446655440000",
    badgeId: "first-message",
    name: "First Message",
    title: "Communication Pioneer",
    description: "Sent your first message on the platform",
    icon: "💬",
    tooltip: "This badge is awarded for sending your first message",
    design: {
        shape: "circle",
        background: "gradient",
        colors: ["#FF6B6B", "#4ECDC4"],
        gradient: "linear-gradient(45deg, #FF6B6B, #4ECDC4)",
        pattern: "dots",
        elements: ["border", "shadow"]
    },
    criteria: {
        requirement: "Send at least 1 message",
        tracked: "message_count",
        type: "message_count" as const,
        threshold: 1,
        timeframe: "all_time"
    },
    perks: ["message_highlight"],
    unlockType: "automatic" as const,
    visualDescription: "A circular badge with gradient background",
    animation: "pulse",
    displayOrder: 0,
    category: "milestone" as const
};

const validNomination = {
    badgeTypeId: "550e8400-e29b-41d4-a716-************",
    nomineeUserId: "550e8400-e29b-41d4-a716-************",
    nominationReason: "This user has been extremely helpful to new members"
};

const validUserStats: UserStats = {
    messageCount: 150,
    serverCount: 3,
    friendCount: 25,
    daysActive: 30,
    accountAge: 45,
    lastActive: new Date(),
    invitesSent: 5,
    invitesAccepted: 3,
    feedbackSubmitted: 2,
    moderationActions: 0,
    signupOrder: 42,
    geographicRegion: "US-West"
};

// Test functions
async function testBadgeCollectionValidation() {
    console.log("📁 Testing Badge Collection Validation...");

    try {
        // Test valid collection creation
        const validatedCollection = validateBadgeCollectionCreation(validBadgeCollection);
        console.log("✅ Valid collection creation passed");

        // Test invalid collection ID
        try {
            validateBadgeCollectionCreation({
                ...validBadgeCollection,
                collectionId: "Invalid ID!" // Contains invalid characters
            });
            console.log("❌ Should have failed for invalid collection ID");
        } catch (error) {
            if (error instanceof BadgeValidationError) {
                console.log("✅ Invalid collection ID properly rejected");
            }
        }

        // Test collection update
        const updateData = { name: "Updated Early Adopters" };
        const validatedUpdate = validateBadgeCollectionUpdate(updateData);
        console.log("✅ Collection update validation passed");

    } catch (error) {
        console.error("❌ Badge collection validation failed:", error);
    }
}

async function testBadgeTypeValidation() {
    console.log("\n🏆 Testing Badge Type Validation...");

    try {
        // Test valid badge type creation
        const validatedBadgeType = validateBadgeTypeCreation(validBadgeType);
        console.log("✅ Valid badge type creation passed");

        // Test invalid design colors
        try {
            validateBadgeTypeCreation({
                ...validBadgeType,
                design: {
                    ...validBadgeType.design,
                    colors: ["invalid-color"] // Invalid hex color
                }
            });
            console.log("❌ Should have failed for invalid colors");
        } catch (error) {
            if (error instanceof BadgeValidationError) {
                console.log("✅ Invalid design colors properly rejected");
            }
        }

        // Test automatic badge without criteria type
        try {
            validateBadgeTypeCreation({
                ...validBadgeType,
                criteria: {
                    requirement: "Test requirement",
                    tracked: "messages",
                    // Missing type for automatic badge
                }
            });
            console.log("❌ Should have failed for missing criteria type");
        } catch (error) {
            if (error instanceof BadgeValidationError) {
                console.log("✅ Missing criteria type properly rejected");
            }
        }

    } catch (error) {
        console.error("❌ Badge type validation failed:", error);
    }
}

async function testBadgeDesignValidation() {
    console.log("\n🎨 Testing Badge Design Validation...");

    try {
        // Test valid design
        validateBadgeDesign(validBadgeType.design);
        console.log("✅ Valid badge design passed");

        // Test invalid color format
        try {
            validateBadgeDesign({
                ...validBadgeType.design,
                colors: ["#GGGGGG"] // Invalid hex color
            });
            console.log("❌ Should have failed for invalid color format");
        } catch (error) {
            if (error instanceof BadgeDesignValidationError) {
                console.log("✅ Invalid color format properly rejected");
            }
        }

        // Test empty colors array
        try {
            validateBadgeDesign({
                ...validBadgeType.design,
                colors: [] // Empty colors array
            });
            console.log("❌ Should have failed for empty colors array");
        } catch (error) {
            if (error instanceof BadgeDesignValidationError) {
                console.log("✅ Empty colors array properly rejected");
            }
        }

    } catch (error) {
        console.error("❌ Badge design validation failed:", error);
    }
}

async function testBadgeNominationValidation() {
    console.log("\n🗳️ Testing Badge Nomination Validation...");

    try {
        // Test valid nomination
        const nominatorId = "550e8400-e29b-41d4-a716-446655440003";
        const validatedNomination = validateBadgeNomination(validNomination, nominatorId);
        console.log("✅ Valid nomination passed");

        // Test self-nomination
        try {
            validateBadgeNomination({
                ...validNomination,
                nomineeUserId: nominatorId // Same as nominator
            }, nominatorId);
            console.log("❌ Should have failed for self-nomination");
        } catch (error) {
            if (error instanceof SelfNominationError) {
                console.log("✅ Self-nomination properly rejected");
            }
        }

        // Test nomination processing
        const processData = { status: "approved" as const };
        const validatedProcessing = validateNominationProcessing(processData);
        console.log("✅ Nomination processing validation passed");

    } catch (error) {
        console.error("❌ Badge nomination validation failed:", error);
    }
}

async function testComplexCriteriaValidation() {
    console.log("\n🔧 Testing Complex Criteria Validation...");

    try {
        // Test valid complex criteria
        const complexCriteria = {
            operator: "AND" as const,
            conditions: [
                {
                    field: "messageCount",
                    operator: ">=" as const,
                    value: 100,
                    timeframe: "last_30_days"
                },
                {
                    field: "friendCount",
                    operator: ">=" as const,
                    value: 10
                }
            ]
        };

        const validatedCriteria = validateComplexCriteria(complexCriteria);
        console.log("✅ Valid complex criteria passed");

        // Test invalid operator
        try {
            validateComplexCriteria({
                operator: "INVALID",
                conditions: complexCriteria.conditions
            });
            console.log("❌ Should have failed for invalid operator");
        } catch (error) {
            if (error instanceof InvalidBadgeCriteriaError) {
                console.log("✅ Invalid operator properly rejected");
            }
        }

    } catch (error) {
        console.error("❌ Complex criteria validation failed:", error);
    }
}

async function testUserStatsValidation() {
    console.log("\n📊 Testing User Stats Validation...");

    try {
        // Test valid user stats
        const validatedStats = validateUserStats(validUserStats);
        console.log("✅ Valid user stats passed");

        // Test negative values
        try {
            validateUserStats({
                ...validUserStats,
                messageCount: -5 // Negative value
            });
            console.log("❌ Should have failed for negative message count");
        } catch (error) {
            if (error instanceof BadgeValidationError) {
                console.log("✅ Negative values properly rejected");
            }
        }

    } catch (error) {
        console.error("❌ User stats validation failed:", error);
    }
}

async function testBadgeCriteriaEvaluation() {
    console.log("\n⚖️ Testing Badge Criteria Evaluation...");

    try {
        // Test message count criteria
        const messageCountCriteria: BadgeCriteria = {
            requirement: "Send 100 messages",
            tracked: "message_count",
            type: "message_count",
            threshold: 100
        };

        const meetsRequirement = evaluateBadgeCriteria(messageCountCriteria, validUserStats);
        console.log(`✅ Message count evaluation: ${meetsRequirement ? "PASS" : "FAIL"} (expected: PASS)`);

        // Test friend count criteria (should fail)
        const friendCountCriteria: BadgeCriteria = {
            requirement: "Have 50 friends",
            tracked: "friend_count",
            type: "friend_count",
            threshold: 50
        };

        const meetsFriendRequirement = evaluateBadgeCriteria(friendCountCriteria, validUserStats);
        console.log(`✅ Friend count evaluation: ${meetsFriendRequirement ? "PASS" : "FAIL"} (expected: FAIL)`);

    } catch (error) {
        console.error("❌ Badge criteria evaluation failed:", error);
    }
}

async function testCollectionOrderValidation() {
    console.log("\n📋 Testing Collection Order Validation...");

    try {
        const collection: BadgeCollection = {
            id: "550e8400-e29b-41d4-a716-446655440000",
            collectionId: "test-collection",
            name: "Test Collection",
            description: "Test progressive collection",
            type: "progressive",
            totalBadges: 3,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        const badge1: BadgeType = {
            id: "badge1",
            collectionId: collection.id,
            badgeId: "first-badge",
            name: "First Badge",
            description: "First badge in collection",
            icon: "🥇",
            design: validBadgeType.design,
            criteria: validBadgeType.criteria,
            unlockType: "automatic",
            displayOrder: 0,
            category: "achievement",
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        const badge2: BadgeType = {
            ...badge1,
            id: "badge2",
            badgeId: "second-badge",
            name: "Second Badge",
            displayOrder: 1
        };

        // Test first badge (should pass)
        const canAssignFirst = validateCollectionOrder(badge1, collection, []);
        console.log(`✅ First badge assignment: ${canAssignFirst ? "ALLOWED" : "BLOCKED"} (expected: ALLOWED)`);

        // Test second badge without first (should fail for progressive)
        const canAssignSecond = validateCollectionOrder(badge2, collection, []);
        console.log(`✅ Second badge without first: ${canAssignSecond ? "ALLOWED" : "BLOCKED"} (expected: BLOCKED)`);

        // Test second badge with first (should pass)
        const canAssignSecondWithFirst = validateCollectionOrder(badge2, collection, [badge1]);
        console.log(`✅ Second badge with first: ${canAssignSecondWithFirst ? "ALLOWED" : "BLOCKED"} (expected: ALLOWED)`);

    } catch (error) {
        console.error("❌ Collection order validation failed:", error);
    }
}

async function testCollectionCompletion() {
    console.log("\n🏁 Testing Collection Completion Validation...");

    try {
        const collection: BadgeCollection = {
            id: "550e8400-e29b-41d4-a716-446655440000",
            collectionId: "test-collection",
            name: "Test Collection",
            description: "Test collection",
            type: "progressive",
            totalBadges: 3,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        const badges: BadgeType[] = [
            {
                id: "badge1",
                collectionId: collection.id,
                badgeId: "first",
                name: "First",
                description: "First badge",
                icon: "🥇",
                design: validBadgeType.design,
                criteria: validBadgeType.criteria,
                unlockType: "automatic",
                displayOrder: 0,
                category: "achievement",
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                id: "badge2",
                collectionId: collection.id,
                badgeId: "second",
                name: "Second",
                description: "Second badge",
                icon: "🥈",
                design: validBadgeType.design,
                criteria: validBadgeType.criteria,
                unlockType: "automatic",
                displayOrder: 1,
                category: "achievement",
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date()
            }
        ];

        // Test incomplete collection
        const incompleteResult = validateCollectionCompletion(collection, badges);
        console.log(`✅ Incomplete collection: ${incompleteResult.completed ? "COMPLETE" : "INCOMPLETE"} (expected: INCOMPLETE)`);

        // Test complete collection
        const completeBadges = [...badges, {
            ...badges[0],
            id: "badge3",
            badgeId: "third",
            name: "Third",
            displayOrder: 2
        }];

        const completeResult = validateCollectionCompletion(collection, completeBadges);
        console.log(`✅ Complete collection: ${completeResult.completed ? "COMPLETE" : "INCOMPLETE"} (expected: COMPLETE)`);

    } catch (error) {
        console.error("❌ Collection completion validation failed:", error);
    }
}

// Run all tests
async function runAllTests() {
    try {
        await testBadgeCollectionValidation();
        await testBadgeTypeValidation();
        await testBadgeDesignValidation();
        await testBadgeNominationValidation();
        await testComplexCriteriaValidation();
        await testUserStatsValidation();
        await testBadgeCriteriaEvaluation();
        await testCollectionOrderValidation();
        await testCollectionCompletion();

        console.log("\n🎉 All enhanced badge validation tests completed!");

    } catch (error) {
        console.error("\n💥 Test suite failed:", error);
        process.exit(1);
    }
}

// Run tests if this file is executed directly
if (import.meta.main) {
    runAllTests();
}