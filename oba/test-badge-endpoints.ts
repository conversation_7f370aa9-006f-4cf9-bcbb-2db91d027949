#!/usr/bin/env bun

/**
 * Test script to verify badge HTTP endpoints are working
 */

async function testBadgeEndpoints() {
  console.log("🌐 Testing Badge HTTP Endpoints...\n");

  const baseUrl = "http://localhost:3005";

  try {
    // Test 1: GET /api/badges/types (should work without auth)
    console.log("1. Testing GET /api/badges/types...");
    const typesResponse = await fetch(`${baseUrl}/api/badges/types`);
    
    if (typesResponse.ok) {
      const typesData = await typesResponse.json();
      console.log("✅ Badge types endpoint working:", typesData.success);
    } else {
      console.log("❌ Badge types endpoint failed:", typesResponse.status, typesResponse.statusText);
    }

    // Test 2: GET /api/badges/stats (should work without auth)
    console.log("\n2. Testing GET /api/badges/stats...");
    const statsResponse = await fetch(`${baseUrl}/api/badges/stats`);
    
    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log("✅ Badge stats endpoint working:", statsData.success);
    } else {
      console.log("❌ Badge stats endpoint failed:", statsResponse.status, statsResponse.statusText);
    }

    // Test 3: GET /api/badges/leaderboard (should work without auth)
    console.log("\n3. Testing GET /api/badges/leaderboard...");
    const leaderboardResponse = await fetch(`${baseUrl}/api/badges/leaderboard`);
    
    if (leaderboardResponse.ok) {
      const leaderboardData = await leaderboardResponse.json();
      console.log("✅ Badge leaderboard endpoint working:", leaderboardData.success);
    } else {
      console.log("❌ Badge leaderboard endpoint failed:", leaderboardResponse.status, leaderboardResponse.statusText);
    }

    // Test 4: POST /api/badges/types/create (should require auth)
    console.log("\n4. Testing POST /api/badges/types/create (without auth)...");
    const createResponse = await fetch(`${baseUrl}/api/badges/types/create`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        name: "Test Badge",
        description: "Test badge description",
        category: "achievement",
        assignmentType: "manual"
      })
    });
    
    if (createResponse.status === 401 || createResponse.status === 403) {
      console.log("✅ Badge creation properly requires authentication:", createResponse.status);
    } else {
      console.log("❌ Badge creation should require authentication:", createResponse.status);
    }

    console.log("\n🎉 Badge endpoint tests completed!");

  } catch (error) {
    console.error("❌ Badge endpoint test failed:", error);
    process.exit(1);
  }
}

// Check if server is running first
async function checkServerStatus() {
  try {
    const response = await fetch("http://localhost:3005/");
    if (response.ok) {
      console.log("✅ Server is running on port 3005\n");
      return true;
    }
  } catch (error) {
    console.log("❌ Server is not running on port 3005");
    console.log("Please start the server with: bun run dev");
    return false;
  }
  return false;
}

// Run the test
checkServerStatus().then(async (serverRunning) => {
  if (serverRunning) {
    await testBadgeEndpoints();
    console.log("\n✨ Badge endpoint verification complete!");
  }
}).catch((error) => {
  console.error("💥 Test execution failed:", error);
  process.exit(1);
});