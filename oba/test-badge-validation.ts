#!/usr/bin/env bun

// Test script to verify badge validation schemas work correctly
import { 
  validateCreateBadgeType,
  validateUpdateBadgeType,
  validateAssignBadge,
  validateBadgeFilters,
  validateUserStats,
  validateBadgeProgress
} from "./utils/badge-validation";

import {
  BadgeNotFoundError,
  BadgeAlreadyAssignedError,
  BadgeValidationError,
  InvalidBadgeCriteriaError,
  BadgeEvaluationError
} from "./class/badge-errors";

console.log("Testing badge validation schemas...");

// Test createBadgeType validation
try {
  const validBadgeType = validateCreateBadgeType({
    name: "First Message",
    description: "Sent your first message",
    color: "#FF0000",
    category: "milestone",
    assignmentType: "automatic",
    criteria: {
      type: "message_count",
      threshold: 1
    }
  });
  console.log("✅ Create badge type validation passed");
} catch (error) {
  console.error("❌ Create badge type validation failed:", error);
}

// Test updateBadgeType validation
try {
  const validUpdate = validateUpdateBadgeType({
    name: "Updated Badge Name",
    isActive: false
  });
  console.log("✅ Update badge type validation passed");
} catch (error) {
  console.error("❌ Update badge type validation failed:", error);
}

// Test assignBadge validation
try {
  const validAssignment = validateAssignBadge({
    badgeTypeId: "550e8400-e29b-41d4-a716-************",
    userId: "550e8400-e29b-41d4-a716-446655440001"
  });
  console.log("✅ Assign badge validation passed");
} catch (error) {
  console.error("❌ Assign badge validation failed:", error);
}

// Test badge filters validation
try {
  const validFilters = validateBadgeFilters({
    category: "achievement",
    assignmentType: "manual",
    isActive: true,
    search: "community"
  });
  console.log("✅ Badge filters validation passed");
} catch (error) {
  console.error("❌ Badge filters validation failed:", error);
}

// Test userStats validation
try {
  const validStats = validateUserStats({
    messageCount: 150,
    serverCount: 3,
    friendCount: 25,
    daysActive: 30,
    accountAge: 90,
    lastActive: new Date()
  });
  console.log("✅ User stats validation passed");
} catch (error) {
  console.error("❌ User stats validation failed:", error);
}

// Test badgeProgress validation
try {
  const validProgress = validateBadgeProgress({
    badgeTypeId: "550e8400-e29b-41d4-a716-************",
    progress: 75,
    total: 100,
    isEarned: false
  });
  console.log("✅ Badge progress validation passed");
} catch (error) {
  console.error("❌ Badge progress validation failed:", error);
}

// Test error classes
console.log("\nTesting badge error classes...");

try {
  throw new BadgeNotFoundError("test-badge-id");
} catch (error) {
  if (error instanceof BadgeNotFoundError) {
    console.log("✅ BadgeNotFoundError works correctly:", error.message, error.code);
  }
}

try {
  throw new BadgeAlreadyAssignedError("user-id", "badge-id");
} catch (error) {
  if (error instanceof BadgeAlreadyAssignedError) {
    console.log("✅ BadgeAlreadyAssignedError works correctly:", error.message, error.code);
  }
}

try {
  throw new BadgeValidationError("Validation failed", { name: ["Name is required"], color: ["Invalid color"] });
} catch (error) {
  if (error instanceof BadgeValidationError) {
    console.log("✅ BadgeValidationError works correctly:", error.message, error.details?.validationErrors);
  }
}

try {
  throw new InvalidBadgeCriteriaError("message_count", "Invalid criteria configuration");
} catch (error) {
  if (error instanceof InvalidBadgeCriteriaError) {
    console.log("✅ InvalidBadgeCriteriaError works correctly:", error.message, error.code);
  }
}

try {
  throw new BadgeEvaluationError("user-123", "badge-456", "Failed to evaluate badges");
} catch (error) {
  if (error instanceof BadgeEvaluationError) {
    console.log("✅ BadgeEvaluationError works correctly:", error.message, error.code);
  }
}

console.log("\n🎉 All badge validation and error tests completed!");