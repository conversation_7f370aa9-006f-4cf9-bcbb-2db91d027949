# Server Configuration
PORT=3000
HOST="localhost"
ENVIRONMENT="development" # development, staging, production

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/oba_db"
DATABASE_HOST="localhost"
DATABASE_PORT=5432
DATABASE_NAME="oba_db"
DATABASE_USER="username"
DATABASE_PASSWORD="password"

# Redis Configuration (for WebSocket state management)
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""

# JWT Configuration
JWT_SECRET="your-jwt-secret-key"
JWT_EXPIRATION="1h" # 1 hour
REFRESH_TOKEN_SECRET="your-refresh-token-secret"
REFRESH_TOKEN_EXPIRATION="7d" # 7 days

# MinIO Configuration (for file storage)
MINIO_ENDPOINT="localhost"
MINIO_PORT=9000
MINIO_ACCESS_KEY="your-minio-access-key"
MINIO_SECRET_KEY="your-minio-secret-key"
MINIO_BUCKET_NAME="oba-files"

# Email Configuration (for password reset and verification)
SMTP_HOST="smtp.example.com"
SMTP_PORT=587
SMTP_USER="your-smtp-username"
SMTP_PASSWORD="your-smtp-password"
FROM_EMAIL="<EMAIL>"

# WebRTC Configuration
ICE_SERVERS=[{"urls":"stun:stun.l.google.com:19302"},{"urls":"stun:stun1.l.google.com:19302"}]

# Logging Configuration
LOG_LEVEL="debug" # debug, info, warn, error
LOG_FORMAT="json" # json, text

# CORS Configuration
ALLOWED_ORIGINS="http://localhost:3000,http://localhost:8080"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000 # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100 # Maximum requests per window

# Security
BCRYPT_SALT_ROUNDS=10 # Number of salt rounds for password hashing
SESSION_SECRET="your-session-secret"

# Feature Flags
ENABLE_VOICE_CHAT=true
ENABLE_FILE_SHARING=true
ENABLE_USER_PRESENCE=true