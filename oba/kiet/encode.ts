import { writeFileSync } from "fs";

function textToBmp(text: string) {
  const bytes = new TextEncoder().encode(text);
  const total = bytes.length + 6; // marker + payload

  // find smallest square side
  const side = Math.ceil(Math.sqrt(total / 3));
  const rowSize = side * 3; // bytes per row (must be multiple of 4)
  const paddedRowSize = (rowSize + 3) & ~3;
  const dataSize = paddedRowSize * side;
  const fileSize = 54 + dataSize;

  // BMP header
  const hdr = new ArrayBuffer(54);
  const v = new DataView(hdr);
  v.setUint16(0, 0x4d42, true); // 'BM'
  v.setUint32(2, fileSize, true);
  v.setUint32(10, 54, true); // pixel offset
  v.setUint32(14, 40, true); // DIB size
  v.setInt32(18, side, true); // width
  v.setInt32(22, side, true); // height
  v.setUint16(26, 1, true); // planes
  v.setUint16(28, 24, true); // 24-bit
  v.setUint32(34, dataSize, true); // image size

  // pixel array
  const pixels = new Uint8Array(dataSize);
  // marker (6 bytes)
  pixels[0] = 0x42;
  pixels[1] = 0x4d;
  pixels[2] = 0x01;
  pixels[3] = (bytes.length >> 8) & 0xff;
  pixels[4] = bytes.length & 0xff;
  pixels[5] = 0x00;
  // payload
  pixels.set(bytes, 6);

  const bmp = new Uint8Array(fileSize);
  bmp.set(new Uint8Array(hdr), 0);
  bmp.set(pixels, 54);
  return { bmp, side };
}

const input = process.argv[2] || (await Bun.stdin.text()).trimEnd();
const { bmp, side } = textToBmp(input);
writeFileSync("bot.bmp", bmp);
console.log(`✅ bot.bmp written (${side}×${side})`);
