import { readFileSync } from "fs";

function bmpToText(path: string) {
  const buf = readFileSync(path);
  if (buf.readUInt16LE(0) !== 0x4d42) throw new Error("Not BMP");

  const dataOffset = buf.readUInt32LE(10);
  const width = buf.readInt32LE(18);
  const height = buf.readInt32LE(22);
  const bpp = buf.readUInt16LE(28);
  if (bpp !== 24) throw new Error("Only 24-bit BMP");

  const marker = buf.subarray(dataOffset, dataOffset + 6);
  if (marker[0] !== 0x42 || marker[1] !== 0x4d || marker[2] !== 0x01)
    throw new Error("Bad marker");

  const len = (marker[3] << 8) | marker[4];
  const payload = buf.subarray(dataOffset + 6, dataOffset + 6 + len);
  return new TextDecoder().decode(payload);
}

const path = process.argv[2] || "bot.bmp";
console.log(bmpToText(path));
