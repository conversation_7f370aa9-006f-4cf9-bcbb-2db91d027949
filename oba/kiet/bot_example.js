import { OBAClient } from '../src/client/OBAClient';
import { EventType } from '../src/types/common';

// Create a new OBA client
const client = new OBAClient({
  baseURL: 'https://api.example.com',
  logLevel: 'debug',
});

// Handle connection events
client.on(EventType.CONNECTED, () => {
  console.log('Connected to OBA platform');
});

client.on(EventType.DISCONNECTED, () => {
  console.log('Disconnected from OBA platform');
});

// Handle message events
client.on(EventType.MESSAGE_RECEIVED, (message) => {
  console.log(`Received message: ${message.content}`);
  
  // Echo the message back if it starts with "!echo"
  if (message.content.startsWith('!echo ')) {
    const content = message.content.substring(6);
    client.messages.send(message.channelId, { text: `Echo: ${content}` })
      .then(() => console.log('Echo message sent'))
      .catch((error) => console.error('Failed to send echo message', error));
  }
  
  // Delete the message if it starts with "!delete"
  if (message.content === '!delete') {
    client.messages.delete(message.id)
      .then(() => console.log('Message deleted'))
      .catch((error) => console.error('Failed to delete message', error));
  }
  
  // React to the message if it starts with "!react"
  if (message.content.startsWith('!react ')) {
    const emoji = message.content.substring(7);
    client.messages.addReaction(message.id, emoji)
      .then(() => console.log(`Added reaction: ${emoji}`))
      .catch((error) => console.error('Failed to add reaction', error));
  }
});

// Handle message updates
client.on(EventType.MESSAGE_UPDATED, (message) => {
  console.log(`Message updated: ${message.content}`);
});

// Handle message deletions
client.on(EventType.MESSAGE_DELETED, (data) => {
  console.log(`Message deleted: ${data.messageId}`);
});

// Login and connect
async function start() {
  try {
    // Login with credentials
    await client.auth.login({
      username: 'bot-username',
      password: 'bot-password',
    });
    
    console.log('Logged in successfully');
    
    // Connect to WebSocket
    await client.connect();
    
    // Send a message to a channel
    const channelId = 'your-channel-id';
    const message = await client.messages.send(channelId, {
      text: 'Hello from the message bot!',
    });
    
    console.log(`Sent message with ID: ${message.id}`);
    
    // Edit the message after 5 seconds
    setTimeout(async () => {
      try {
        const updatedMessage = await client.messages.edit(message.id, {
          text: 'Hello from the message bot! (edited)',
        });
        
        console.log(`Edited message: ${updatedMessage.content}`);
      } catch (error) {
        console.error('Failed to edit message', error);
      }
    }, 5000);
    
    // Get message history
    const history = await client.messages.getHistory(channelId, {
      limit: 10,
    });
    
    console.log(`Retrieved ${history.length} messages from history`);
    
    // Start typing indicator
    await client.messages.startTyping(channelId);
    console.log('Started typing indicator');
    
    // Stop typing indicator after 3 seconds
    setTimeout(async () => {
      try {
        await client.messages.stopTyping(channelId);
        console.log('Stopped typing indicator');
      } catch (error) {
        console.error('Failed to stop typing indicator', error);
      }
    }, 3000);
  } catch (error) {
    console.error('Error starting bot', error);
  }
}

// Start the bot
start().catch(console.error);

// Handle process termination
process.on('SIGINT', async () => {
  console.log('Shutting down...');
  await client.disconnect();
  process.exit(0);
});