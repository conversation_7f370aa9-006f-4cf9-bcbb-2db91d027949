#!/usr/bin/env bun

/**
 * Comprehensive Badge Dashboard Test Suite
 * Creates test data and tests badge progress tracking and user dashboard features
 */

import { db } from "./db";
import { BadgeService } from "./services/badge.service";
import { UserSchema } from "./db/schema";
import type {
  CreateBadgeTypeRequest,
  BadgeProgress,
  UserStats
} from "./types/badge.types";

const badgeService = new BadgeService(db);

// Test user data
const TEST_USER_DATA = {
  id: "01234567-89ab-cdef-0123-456789abcdef",
  username: "test_dashboard_user",
  email: "<EMAIL>",
  password: "test_hash_password", // Required field
  createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
  lastActive: new Date(),
};

const TEST_ADMIN_DATA = {
  id: "01234567-89ab-cdef-0123-456789abcde0",
  username: "test_dashboard_admin",
  email: "<EMAIL>",
  password: "test_hash_password", // Required field
  createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // 60 days ago
  lastActive: new Date(),
};

async function createTestUsers(): Promise<void> {
  console.log("👥 Creating test users...");
  
  try {
    // Check if users already exist
    const existingUsers = await db
      .select()
      .from(UserSchema)
      .where(sql`${UserSchema.id} IN (${TEST_USER_DATA.id}, ${TEST_ADMIN_DATA.id})`);
    
    if (existingUsers.length === 0) {
      await db.insert(UserSchema).values([TEST_USER_DATA, TEST_ADMIN_DATA]);
      console.log("✅ Test users created successfully");
    } else {
      console.log("ℹ️  Test users already exist");
    }
  } catch (error) {
    console.error("❌ Failed to create test users:", error.message);
    throw error;
  }
}

async function createTestBadgeTypes(): Promise<string[]> {
  console.log("🏗️  Creating test badge types...");
  
  const badgeTypes: CreateBadgeTypeRequest[] = [
    {
      badgeId: "first-message-dashboard",
      name: "First Message Dashboard",
      title: "Message Pioneer",
      description: "Send your first message in the community",
      icon: "💬",
      tooltip: "Awarded for sending your first message",
      design: {
        shape: "circle",
        background: "#4CAF50",
        colors: ["#4CAF50", "#81C784"]
      },
      criteria: {
        requirement: "Send 1 message",
        tracked: "message_count",
        type: "message_count",
        threshold: 1
      },
      unlockType: "automatic",
      category: "milestone",
      displayOrder: 1
    },
    {
      badgeId: "social-butterfly-dashboard",
      name: "Social Butterfly Dashboard",
      title: "Community Connector",
      description: "Make 5 friends in the community",
      icon: "🦋",
      tooltip: "Awarded for making 5 friends",
      design: {
        shape: "hexagon",
        background: "#E91E63",
        colors: ["#E91E63", "#F48FB1"]
      },
      criteria: {
        requirement: "Make 5 friends",
        tracked: "friend_count",
        type: "friend_count",
        threshold: 5
      },
      unlockType: "automatic",
      category: "community",
      displayOrder: 2
    },
    {
      badgeId: "community-helper-dashboard",
      name: "Community Helper Dashboard",
      title: "Helpful Soul",
      description: "Manually awarded for helping others",
      icon: "🤝",
      tooltip: "Awarded by moderators for helping community members",
      design: {
        shape: "heart",
        background: "#F44336",
        colors: ["#F44336", "#EF5350"]
      },
      criteria: {
        requirement: "Help community members",
        tracked: "manual"
      },
      unlockType: "manual",
      category: "community",
      displayOrder: 3
    }
  ];

  const createdBadgeIds: string[] = [];
  
  for (const badgeType of badgeTypes) {
    try {
      const created = await badgeService.createBadgeType(badgeType, TEST_ADMIN_DATA.id);
      createdBadgeIds.push(created.id);
      console.log(`✅ Created badge: ${created.name}`);
    } catch (error) {
      console.error(`❌ Failed to create badge ${badgeType.name}:`, error.message);
    }
  }
  
  return createdBadgeIds;
}

async function testUserStats(): Promise<void> {
  console.log("\n📊 Testing user statistics calculation...");
  
  try {
    const userStats = await badgeService.getUserStats(TEST_USER_DATA.id);
    
    console.log("📈 User Statistics:");
    console.log(`  Messages: ${userStats.messageCount}`);
    console.log(`  Servers: ${userStats.serverCount}`);
    console.log(`  Friends: ${userStats.friendCount}`);
    console.log(`  Days Active: ${userStats.daysActive}`);
    console.log(`  Account Age: ${userStats.accountAge} days`);
    console.log(`  Invites Sent: ${userStats.invitesSent}`);
    console.log(`  Invites Accepted: ${userStats.invitesAccepted}`);
    console.log(`  Signup Order: ${userStats.signupOrder || 'Unknown'}`);
    console.log(`  Last Active: ${userStats.lastActive.toISOString()}`);
    
    // Validate stats
    if (userStats.accountAge < 25 || userStats.accountAge > 35) {
      console.warn("⚠️  Account age seems incorrect");
    } else {
      console.log("✅ Account age calculation looks correct");
    }
    
  } catch (error) {
    console.error("❌ User stats test failed:", error.message);
  }
}

async function testBadgeProgress(): Promise<void> {
  console.log("\n📊 Testing badge progress calculation...");
  
  try {
    // Get badge progress for test user
    const progress = await badgeService.getBadgeProgress(TEST_USER_DATA.id);
    
    console.log(`📈 Found ${progress.length} badges with progress tracking`);
    
    for (const p of progress) {
      const percentage = Math.round((p.progress / p.total) * 100);
      const status = p.isEarned ? "✅ EARNED" : "⏳ IN PROGRESS";
      
      console.log(`  ${p.badgeType.icon} ${p.badgeType.name}: ${p.progress}/${p.total} (${percentage}%) ${status}`);
      
      if (p.badgeType.criteria.type) {
        console.log(`    Criteria: ${p.badgeType.criteria.requirement}`);
      }
    }
    
    // Test specific badge progress
    if (progress.length > 0) {
      const specificProgress = await badgeService.getBadgeProgress(TEST_USER_DATA.id, progress[0].badgeTypeId);
      console.log(`🎯 Specific badge progress for ${progress[0].badgeType.name}: ${specificProgress.length} result(s)`);
      
      if (specificProgress.length === 1) {
        console.log("✅ Specific badge progress query works correctly");
      }
    }
    
  } catch (error) {
    console.error("❌ Badge progress test failed:", error.message);
  }
}

async function testBadgeDashboard(): Promise<void> {
  console.log("\n🎛️  Testing badge dashboard functionality...");
  
  try {
    const userBadges = await badgeService.getUserBadges(TEST_USER_DATA.id);
    const availableBadges = await badgeService.getAvailableBadgesForUser(TEST_USER_DATA.id);
    const progress = await badgeService.getBadgeProgress(TEST_USER_DATA.id);
    
    console.log("🏆 Dashboard Summary:");
    console.log(`  Earned Badges: ${userBadges.length}`);
    console.log(`  Available Badges: ${availableBadges.length}`);
    console.log(`  Badges with Progress: ${progress.length}`);
    
    // Show available badges
    if (availableBadges.length > 0) {
      console.log("\n🎯 Available Badges:");
      for (const badge of availableBadges.slice(0, 3)) { // Show first 3
        console.log(`  ${badge.icon} ${badge.name}`);
        console.log(`    ${badge.description}`);
        console.log(`    Type: ${badge.unlockType}`);
      }
    }
    
    // Show progress
    const inProgressBadges = progress.filter(p => !p.isEarned && p.progress >= 0);
    if (inProgressBadges.length > 0) {
      console.log("\n⏳ Badges Available for Progress:");
      for (const p of inProgressBadges.slice(0, 3)) { // Show first 3
        const percentage = p.total > 0 ? Math.round((p.progress / p.total) * 100) : 0;
        console.log(`  ${p.badgeType.icon} ${p.badgeType.name}: ${percentage}%`);
        console.log(`    Progress: ${p.progress}/${p.total}`);
      }
    }
    
    // Test dashboard data consistency
    const earnedBadgeIds = new Set(userBadges.map(b => b.badgeTypeId));
    const availableBadgeIds = new Set(availableBadges.map(b => b.id));
    
    // Check for overlap (should be none)
    let hasOverlap = false;
    for (const earnedId of earnedBadgeIds) {
      if (availableBadgeIds.has(earnedId)) {
        hasOverlap = true;
        break;
      }
    }
    
    if (!hasOverlap) {
      console.log("✅ Dashboard data consistency check passed");
    } else {
      console.warn("⚠️  Dashboard data has overlap between earned and available badges");
    }
    
  } catch (error) {
    console.error("❌ Badge dashboard test failed:", error.message);
  }
}

async function testBadgeAssignmentAndVisibility(): Promise<void> {
  console.log("\n🏅 Testing badge assignment and visibility...");
  
  try {
    const availableBadges = await badgeService.getAvailableBadgesForUser(TEST_USER_DATA.id);
    const manualBadges = availableBadges.filter(b => b.unlockType === 'manual');
    
    if (manualBadges.length > 0) {
      const testBadge = manualBadges[0];
      console.log(`🎯 Testing with badge: ${testBadge.name}`);
      
      // Assign the badge
      const assignedBadge = await badgeService.assignBadge(
        TEST_USER_DATA.id,
        testBadge.id,
        TEST_ADMIN_DATA.id
      );
      
      console.log(`✅ Badge assigned successfully: ${assignedBadge.badgeType?.name}`);
      
      // Test visibility toggle
      console.log("👁️  Testing visibility controls...");
      
      await badgeService.updateBadgeVisibility(
        TEST_USER_DATA.id,
        testBadge.id,
        false
      );
      console.log("✅ Badge visibility set to hidden");
      
      // Check visibility
      const hiddenBadges = await badgeService.getUserBadges(TEST_USER_DATA.id, false);
      const visibleBadges = await badgeService.getUserBadges(TEST_USER_DATA.id, true);
      
      console.log(`📊 Total badges: ${hiddenBadges.length}, Visible badges: ${visibleBadges.length}`);
      
      // Restore visibility
      await badgeService.updateBadgeVisibility(
        TEST_USER_DATA.id,
        testBadge.id,
        true
      );
      console.log("✅ Badge visibility restored");
      
    } else {
      console.log("ℹ️  No manual badges available for assignment test");
    }
    
  } catch (error) {
    console.error("❌ Badge assignment and visibility test failed:", error.message);
  }
}

async function testBadgeEvaluation(): Promise<void> {
  console.log("\n🔍 Testing automatic badge evaluation...");
  
  try {
    const evaluationResult = await badgeService.evaluateUserBadges(TEST_USER_DATA.id);
    
    console.log("📊 Evaluation Results:");
    console.log(`  New badges awarded: ${evaluationResult.newBadges.length}`);
    console.log(`  Badges evaluated: ${evaluationResult.evaluatedBadges.length}`);
    console.log(`  Errors: ${evaluationResult.errors.length}`);
    
    if (evaluationResult.newBadges.length > 0) {
      console.log("\n🎉 New badges awarded:");
      for (const badge of evaluationResult.newBadges) {
        console.log(`  ${badge.badgeType?.icon} ${badge.badgeType?.name}`);
      }
    }
    
    if (evaluationResult.errors.length > 0) {
      console.log("\n⚠️  Evaluation errors:");
      for (const error of evaluationResult.errors) {
        console.log(`  ${error}`);
      }
    }
    
    // Test progress after evaluation
    console.log("\n📈 Progress after evaluation:");
    const postEvalProgress = await badgeService.getBadgeProgress(TEST_USER_DATA.id);
    const earnedCount = postEvalProgress.filter(p => p.isEarned).length;
    console.log(`  Earned badges from progress: ${earnedCount}/${postEvalProgress.length}`);
    
  } catch (error) {
    console.error("❌ Badge evaluation test failed:", error.message);
  }
}

async function cleanup(badgeTypeIds: string[]): Promise<void> {
  console.log("\n🧹 Cleaning up test data...");
  
  // Clean up badges
  for (const badgeTypeId of badgeTypeIds) {
    try {
      await badgeService.deleteBadgeType(badgeTypeId, TEST_ADMIN_DATA.id);
      console.log(`✅ Deleted badge type: ${badgeTypeId}`);
    } catch (error) {
      console.error(`❌ Failed to delete badge type ${badgeTypeId}:`, error.message);
    }
  }
  
  // Clean up users
  try {
    await db
      .delete(UserSchema)
      .where(sql`${UserSchema.id} IN (${TEST_USER_DATA.id}, ${TEST_ADMIN_DATA.id})`);
    console.log("✅ Test users cleaned up");
  } catch (error) {
    console.error("❌ Failed to clean up test users:", error.message);
  }
}

async function runTests(): Promise<void> {
  console.log("🚀 Starting Comprehensive Badge Dashboard Test Suite");
  console.log("===================================================");
  
  let createdBadgeIds: string[] = [];
  
  try {
    // Setup
    await createTestUsers();
    createdBadgeIds = await createTestBadgeTypes();
    
    // Run tests
    await testUserStats();
    await testBadgeProgress();
    await testBadgeDashboard();
    await testBadgeAssignmentAndVisibility();
    await testBadgeEvaluation();
    
    console.log("\n✅ All badge dashboard tests completed successfully!");
    
  } catch (error) {
    console.error("\n❌ Test suite failed:", error);
  } finally {
    // Cleanup
    if (createdBadgeIds.length > 0) {
      await cleanup(createdBadgeIds);
    }
  }
  
  console.log("\n🏁 Comprehensive Badge Dashboard Test Suite Complete");
}

// Import sql from drizzle-orm for cleanup queries
import { sql } from "drizzle-orm";

// Run the tests
if (import.meta.main) {
  runTests().catch(console.error);
}