#!/bin/bash

# Script to switch between development and production environments

# Function to display usage information
usage() {
  echo "Usage: $0 [dev|prod]"
  echo "  dev  - Switch to development environment"
  echo "  prod - Switch to production environment"
  exit 1
}

# Check if an argument was provided
if [ $# -ne 1 ]; then
  usage
fi

# Switch based on the argument
case "$1" in
  dev)
    echo "Switching to development environment..."
    cp .env.dev .env
    echo "Done! Now using development environment."
    ;;
  prod)
    echo "Switching to production environment..."
    cp .env.prod .env
    echo "Done! Now using production environment."
    ;;
  *)
    usage
    ;;
esac

# Display current environment
echo ""
echo "Current database settings:"
grep "POSTGRES_DB" .env
grep "POSTGRES_HOST" .env
echo ""
echo "Current environment:"
grep "NODE_ENV" .env
