import type { Middleware, RouteHandlerFunction } from "./types";

type MiddlewareHandler = (req: Request) => Response | Promise<Response>;

export function applyMiddleware(
  middlewares: Middleware[],
  handler: RouteHandlerFunction,
): RouteHandlerFunction {
  return async (req: Request, params?: Record<string, string>) => {
    // Create a context object to pass between middlewares
    const ctx = {
      user: null,
      ...((req as any).ctx || {}),
    };

    // Attach the context to the request
    (req as any).ctx = ctx;

    // Create the final handler that includes params
    const finalHandler: MiddlewareHandler = async (request: Request) =>
      handler(request, params);

    // Chain middlewares in reverse order
    const chain = middlewares.reduceRight(
      (next: MiddlewareHandler, middleware: Middleware) => {
        return async (request: Request) => {
          return middleware(request, next);
        };
      },
      finalHandler,
    );

    return chain(req);
  };
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;

  return function debounced(...args: Parameters<T>) {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      func(...args);
      timeoutId = null;
    }, delay);
  };
}
