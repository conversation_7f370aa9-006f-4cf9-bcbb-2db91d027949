#!/usr/bin/env bun

/**
 * Test script to verify badge API endpoints are working
 */

import { db } from "./db";
import { BadgeService } from "./services/badge.service";
import type { CreateBadgeTypeRequest } from "./types/badge.types";

async function testBadgeAPI() {
  console.log("🧪 Testing Badge API Implementation...\n");

  const badgeService = new BadgeService(db);

  try {
    // Test 1: Create a badge type
    console.log("1. Testing badge type creation...");
    const testBadgeData: CreateBadgeTypeRequest = {
      name: "Test Badge",
      description: "A test badge for API verification",
      category: "achievement",
      assignmentType: "manual",
      color: "#FF5733"
    };

    const createdBadge = await badgeService.createBadgeType(testBadgeData, "test-user-id");
    console.log("✅ Badge type created:", createdBadge.name);

    // Test 2: Get badge types
    console.log("\n2. Testing badge types retrieval...");
    const badgeTypes = await badgeService.getBadgeTypes();
    console.log(`✅ Retrieved ${badgeTypes.length} badge types`);

    // Test 3: Update badge type
    console.log("\n3. Testing badge type update...");
    const updatedBadge = await badgeService.updateBadgeType(
      createdBadge.id,
      { description: "Updated test badge description" },
      "test-user-id"
    );
    console.log("✅ Badge type updated:", updatedBadge.description);

    // Test 4: Get badge stats
    console.log("\n4. Testing badge statistics...");
    const stats = await badgeService.getBadgeStats();
    console.log("✅ Badge stats retrieved:", {
      totalBadges: stats.totalBadges,
      totalAssignments: stats.totalAssignments
    });

    // Test 5: Get badge leaderboard
    console.log("\n5. Testing badge leaderboard...");
    const leaderboard = await badgeService.getBadgeLeaderboard(5);
    console.log(`✅ Leaderboard retrieved with ${leaderboard.length} entries`);

    // Test 6: Clean up - delete test badge
    console.log("\n6. Cleaning up test badge...");
    await badgeService.deleteBadgeType(createdBadge.id, "test-user-id");
    console.log("✅ Test badge deleted");

    console.log("\n🎉 All badge API tests passed!");

  } catch (error) {
    console.error("❌ Badge API test failed:", error);
    process.exit(1);
  }
}

// Run the test
testBadgeAPI().then(() => {
  console.log("\n✨ Badge API verification complete!");
  process.exit(0);
}).catch((error) => {
  console.error("💥 Test execution failed:", error);
  process.exit(1);
});