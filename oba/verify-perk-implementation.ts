#!/usr/bin/env bun

/**
 * Perk Implementation Verification Script
 * 
 * This script verifies the core perk system implementation without requiring database connection.
 * It tests the parsing, validation, and basic functionality of the perk system.
 */

import { PerkService } from "./services/perk.service";
import type { BadgeType } from "./types/badge.types";

// Mock database for testing
const mockDb = {} as any;

// Test configuration
const TEST_CONFIG = {
  verbose: true
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  errors: [] as string[]
};

// Utility functions
function log(message: string, level: 'info' | 'success' | 'error' | 'warn' = 'info') {
  if (!TEST_CONFIG.verbose && level === 'info') return;
  
  const colors = {
    info: '\x1b[36m',    // Cyan
    success: '\x1b[32m', // Green
    error: '\x1b[31m',   // Red
    warn: '\x1b[33m',    // Yellow
    reset: '\x1b[0m'
  };
  
  console.log(`${colors[level]}[${level.toUpperCase()}] ${message}${colors.reset}`);
}

function assert(condition: boolean, message: string) {
  if (condition) {
    testResults.passed++;
    log(`✓ ${message}`, 'success');
  } else {
    testResults.failed++;
    testResults.errors.push(message);
    log(`✗ ${message}`, 'error');
  }
}

function testPerkParsing() {
  log("\n=== Testing Perk Parsing ===");
  
  const perkService = new PerkService(mockDb);
  
  // Test string perk parsing
  log("Testing string perk parsing...");
  
  try {
    // Test role assignment perk
    const rolePerk = perkService['parseStringPerk']("role:Moderator", "test_id_1");
    assert(rolePerk.id === "test_id_1", "Role perk should have correct ID");
    assert(rolePerk.type === "role_assignment", "Role perk should have correct type");
    assert(rolePerk.name === "Role: Moderator", "Role perk should have correct name");
    assert(rolePerk.description === "Assigns the Moderator role", "Role perk should have correct description");
    assert(rolePerk.value === "Moderator", "Role perk should have correct value");
    assert(rolePerk.isActive === true, "Role perk should be active by default");
    
    // Test permission grant perk
    const permissionPerk = perkService['parseStringPerk']("permission:MANAGE_MESSAGES", "test_id_2");
    assert(permissionPerk.type === "permission_grant", "Permission perk should have correct type");
    assert(permissionPerk.name === "Permission: MANAGE_MESSAGES", "Permission perk should have correct name");
    assert(Array.isArray(permissionPerk.permissions), "Permission perk should have permissions array");
    assert(permissionPerk.permissions![0] === "MANAGE_MESSAGES", "Permission perk should have correct permission");
    
    // Test cosmetic feature perk
    const cosmeticPerk = perkService['parseStringPerk']("cosmetic:special_glow", "test_id_3");
    assert(cosmeticPerk.type === "cosmetic_feature", "Cosmetic perk should have correct type");
    assert(cosmeticPerk.name === "Cosmetic: special_glow", "Cosmetic perk should have correct name");
    assert(cosmeticPerk.value === "special_glow", "Cosmetic perk should have correct value");
    
    // Test access privilege perk
    const accessPerk = perkService['parseStringPerk']("access:vip_lounge", "test_id_4");
    assert(accessPerk.type === "access_privilege", "Access perk should have correct type");
    assert(accessPerk.name === "Access: vip_lounge", "Access perk should have correct name");
    assert(accessPerk.value === "vip_lounge", "Access perk should have correct value");
    
    // Test custom privilege perk
    const customPerk = perkService['parseStringPerk']("custom_feature", "test_id_5");
    assert(customPerk.type === "custom_privilege", "Custom perk should have correct type");
    assert(customPerk.name === "Custom: custom_feature", "Custom perk should have correct name");
    assert(customPerk.value === "custom_feature", "Custom perk should have correct value");
    
    log("String perk parsing tests completed", 'success');
  } catch (error) {
    log(`String perk parsing failed: ${error}`, 'error');
    testResults.failed++;
  }
  
  // Test object perk parsing
  log("Testing object perk parsing...");
  
  try {
    const objectPerk = {
      id: "custom_id",
      type: "role_assignment",
      name: "VIP Role",
      description: "Assigns VIP role to user",
      value: "VIP",
      serverId: "server123",
      isActive: true,
      metadata: { priority: 1 }
    };
    
    const parsed = perkService['parseObjectPerk'](objectPerk, "fallback_id");
    assert(parsed.id === "custom_id", "Object perk should preserve custom ID");
    assert(parsed.type === "role_assignment", "Object perk should preserve type");
    assert(parsed.name === "VIP Role", "Object perk should preserve name");
    assert(parsed.description === "Assigns VIP role to user", "Object perk should preserve description");
    assert(parsed.value === "VIP", "Object perk should preserve value");
    assert(parsed.serverId === "server123", "Object perk should preserve serverId");
    assert(parsed.isActive === true, "Object perk should preserve isActive");
    assert(parsed.metadata?.priority === 1, "Object perk should preserve metadata");
    
    // Test object perk with minimal properties
    const minimalPerk = { type: "cosmetic_feature" };
    const parsedMinimal = perkService['parseObjectPerk'](minimalPerk, "fallback_id");
    assert(parsedMinimal.id === "fallback_id", "Minimal perk should use fallback ID");
    assert(parsedMinimal.type === "cosmetic_feature", "Minimal perk should preserve type");
    assert(parsedMinimal.name === "Unnamed Perk", "Minimal perk should have default name");
    assert(parsedMinimal.description === "No description provided", "Minimal perk should have default description");
    assert(parsedMinimal.isActive === true, "Minimal perk should be active by default");
    
    log("Object perk parsing tests completed", 'success');
  } catch (error) {
    log(`Object perk parsing failed: ${error}`, 'error');
    testResults.failed++;
  }
}

function testPerkExtraction() {
  log("\n=== Testing Perk Extraction from Badge Types ===");
  
  const perkService = new PerkService(mockDb);
  
  try {
    // Test badge with string perks
    const badgeWithStringPerks: BadgeType = {
      id: "badge_1",
      badgeId: "test_badge_1",
      name: "Test Badge 1",
      description: "Test badge with string perks",
      icon: "🏆",
      design: { shape: "circle", background: "gold", colors: ["#FFD700"] },
      criteria: { requirement: "manual", tracked: "manual" },
      perks: ["role:Moderator", "cosmetic:glow", "access:lounge"],
      unlockType: "manual",
      displayOrder: 1,
      category: "achievement",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const stringPerks = perkService['extractPerksFromBadge'](badgeWithStringPerks);
    assert(Array.isArray(stringPerks), "Should extract perks as array");
    assert(stringPerks.length === 3, "Should extract correct number of perks");
    assert(stringPerks[0].type === "role_assignment", "First perk should be role assignment");
    assert(stringPerks[1].type === "cosmetic_feature", "Second perk should be cosmetic feature");
    assert(stringPerks[2].type === "access_privilege", "Third perk should be access privilege");
    
    // Test badge with object perks
    const badgeWithObjectPerks: BadgeType = {
      id: "badge_2",
      badgeId: "test_badge_2",
      name: "Test Badge 2",
      description: "Test badge with object perks",
      icon: "💎",
      design: { shape: "diamond", background: "purple", colors: ["#8A2BE2"] },
      criteria: { requirement: "manual", tracked: "manual" },
      perks: [
        {
          type: "role_assignment",
          name: "VIP Role",
          description: "Assigns VIP role",
          value: "VIP"
        },
        {
          type: "cosmetic_feature",
          name: "Sparkle Effect",
          description: "Adds sparkle effect",
          value: "sparkle"
        }
      ],
      unlockType: "manual",
      displayOrder: 2,
      category: "special",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const objectPerks = perkService['extractPerksFromBadge'](badgeWithObjectPerks);
    assert(Array.isArray(objectPerks), "Should extract object perks as array");
    assert(objectPerks.length === 2, "Should extract correct number of object perks");
    assert(objectPerks[0].name === "VIP Role", "First object perk should have correct name");
    assert(objectPerks[1].name === "Sparkle Effect", "Second object perk should have correct name");
    
    // Test badge with no perks
    const badgeWithNoPerks: BadgeType = {
      id: "badge_3",
      badgeId: "test_badge_3",
      name: "Test Badge 3",
      description: "Test badge with no perks",
      icon: "🎖️",
      design: { shape: "star", background: "silver", colors: ["#C0C0C0"] },
      criteria: { requirement: "manual", tracked: "manual" },
      perks: [],
      unlockType: "manual",
      displayOrder: 3,
      category: "milestone",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const noPerks = perkService['extractPerksFromBadge'](badgeWithNoPerks);
    assert(Array.isArray(noPerks), "Should return array even with no perks");
    assert(noPerks.length === 0, "Should return empty array for badge with no perks");
    
    // Test badge with undefined perks
    const badgeWithUndefinedPerks: BadgeType = {
      id: "badge_4",
      badgeId: "test_badge_4",
      name: "Test Badge 4",
      description: "Test badge with undefined perks",
      icon: "🏅",
      design: { shape: "circle", background: "bronze", colors: ["#CD7F32"] },
      criteria: { requirement: "manual", tracked: "manual" },
      unlockType: "manual",
      displayOrder: 4,
      category: "participation",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const undefinedPerks = perkService['extractPerksFromBadge'](badgeWithUndefinedPerks);
    assert(Array.isArray(undefinedPerks), "Should return array even with undefined perks");
    assert(undefinedPerks.length === 0, "Should return empty array for badge with undefined perks");
    
    log("Perk extraction tests completed", 'success');
  } catch (error) {
    log(`Perk extraction failed: ${error}`, 'error');
    testResults.failed++;
  }
}

function testPerkTypes() {
  log("\n=== Testing Perk Type Coverage ===");
  
  const perkService = new PerkService(mockDb);
  
  try {
    // Test all supported perk types
    const perkTypes = [
      { input: "role:TestRole", expectedType: "role_assignment" },
      { input: "permission:TEST_PERMISSION", expectedType: "permission_grant" },
      { input: "cosmetic:test_cosmetic", expectedType: "cosmetic_feature" },
      { input: "access:test_access", expectedType: "access_privilege" },
      { input: "unknown:test", expectedType: "custom_privilege" },
      { input: "just_text", expectedType: "custom_privilege" }
    ];
    
    perkTypes.forEach((testCase, index) => {
      const perk = perkService['parseStringPerk'](testCase.input, `test_${index}`);
      assert(
        perk.type === testCase.expectedType,
        `Perk "${testCase.input}" should have type "${testCase.expectedType}", got "${perk.type}"`
      );
    });
    
    // Test object-based perk types
    const objectPerkTypes = [
      "role_assignment",
      "permission_grant", 
      "cosmetic_feature",
      "access_privilege",
      "status_boost",
      "custom_privilege"
    ];
    
    objectPerkTypes.forEach((type, index) => {
      const objectPerk = {
        type,
        name: `Test ${type}`,
        description: `Test ${type} perk`
      };
      
      const parsed = perkService['parseObjectPerk'](objectPerk, `test_obj_${index}`);
      assert(
        parsed.type === type,
        `Object perk should preserve type "${type}"`
      );
    });
    
    log("Perk type coverage tests completed", 'success');
  } catch (error) {
    log(`Perk type coverage failed: ${error}`, 'error');
    testResults.failed++;
  }
}

function testErrorHandling() {
  log("\n=== Testing Error Handling ===");
  
  const perkService = new PerkService(mockDb);
  
  try {
    // Test invalid perk format in badge
    const badgeWithInvalidPerks: BadgeType = {
      id: "badge_invalid",
      badgeId: "invalid_badge",
      name: "Invalid Badge",
      description: "Badge with invalid perks",
      icon: "❌",
      design: { shape: "circle", background: "red", colors: ["#FF0000"] },
      criteria: { requirement: "manual", tracked: "manual" },
      perks: [123 as any], // Invalid perk format
      unlockType: "manual",
      displayOrder: 1,
      category: "achievement",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    let errorCaught = false;
    try {
      perkService['extractPerksFromBadge'](badgeWithInvalidPerks);
    } catch (error) {
      errorCaught = true;
      assert(
        error.message.includes("Invalid perk format"),
        "Should throw error with appropriate message for invalid perk format"
      );
    }
    
    assert(errorCaught, "Should throw error for invalid perk format");
    
    log("Error handling tests completed", 'success');
  } catch (error) {
    log(`Error handling tests failed: ${error}`, 'error');
    testResults.failed++;
  }
}

function testServiceIntegration() {
  log("\n=== Testing Service Integration Points ===");
  
  try {
    // Test that PerkService can be instantiated
    const perkService = new PerkService(mockDb);
    assert(perkService !== null, "PerkService should be instantiable");
    assert(typeof perkService.getUserPerks === 'function', "PerkService should have getUserPerks method");
    assert(typeof perkService.getAvailablePerks === 'function', "PerkService should have getAvailablePerks method");
    assert(typeof perkService.validatePerk === 'function', "PerkService should have validatePerk method");
    assert(typeof perkService.assignPerksForBadge === 'function', "PerkService should have assignPerksForBadge method");
    assert(typeof perkService.revokePerksForBadge === 'function', "PerkService should have revokePerksForBadge method");
    
    // Test that service methods exist and are callable (without database)
    const methods = [
      'getUserPerks',
      'getAvailablePerks', 
      'validatePerk',
      'assignPerksForBadge',
      'revokePerksForBadge',
      'validatePerkManagementPermission'
    ];
    
    methods.forEach(method => {
      assert(
        typeof perkService[method] === 'function',
        `PerkService should have ${method} method`
      );
    });
    
    log("Service integration tests completed", 'success');
  } catch (error) {
    log(`Service integration tests failed: ${error}`, 'error');
    testResults.failed++;
  }
}

function runVerification() {
  log("🚀 Starting Perk Implementation Verification\n", 'info');
  
  try {
    // Run verification tests
    testPerkParsing();
    testPerkExtraction();
    testPerkTypes();
    testErrorHandling();
    testServiceIntegration();
    
    // Results
    log("\n=== Verification Results ===", 'info');
    log(`✓ Passed: ${testResults.passed}`, 'success');
    log(`✗ Failed: ${testResults.failed}`, testResults.failed > 0 ? 'error' : 'success');
    
    if (testResults.errors.length > 0) {
      log("\nFailed Tests:", 'error');
      testResults.errors.forEach(error => log(`  - ${error}`, 'error'));
    }
    
    const successRate = (testResults.passed / (testResults.passed + testResults.failed)) * 100;
    log(`\nSuccess Rate: ${successRate.toFixed(1)}%`, successRate >= 90 ? 'success' : 'warn');
    
    if (testResults.failed === 0) {
      log("\n🎉 All verification tests passed! Perk system core implementation is working correctly.", 'success');
      log("\n📋 Implementation Summary:", 'info');
      log("  ✓ Perk parsing (string and object formats)", 'info');
      log("  ✓ Perk type coverage (role, permission, cosmetic, access, custom)", 'info');
      log("  ✓ Badge-perk integration", 'info');
      log("  ✓ Error handling", 'info');
      log("  ✓ Service integration points", 'info');
      log("\n🔧 Next Steps:", 'info');
      log("  - Set up database connection to run full integration tests", 'info');
      log("  - Test API endpoints with actual server", 'info');
      log("  - Verify WebSocket event broadcasting", 'info');
      log("  - Test permission validation with real user data", 'info');
    } else {
      log(`\n⚠️  ${testResults.failed} verification test(s) failed. Please review the implementation.`, 'warn');
    }
    
  } catch (error) {
    log(`\n💥 Verification failed with error: ${error}`, 'error');
    process.exit(1);
  }
}

// Run verification if this script is executed directly
if (import.meta.main) {
  runVerification();
}

export { runVerification };