#!/usr/bin/env bun

/**
 * Verification script for Task 7: User Badge Management Endpoints
 * 
 * This script verifies that all required endpoints are properly implemented:
 * - DELETE /api/users/:userId/badges/:badgeId - for badge removal
 * - GET /api/badges/available - for showing available badges  
 * - POST /api/badges/evaluate/:userId - for triggering evaluation
 * - GET /api/badges/stats - for badge statistics
 * - GET /api/badges/leaderboard - for badge rankings
 */

import { readFile } from "fs/promises";
import { join } from "path";

interface EndpointCheck {
  method: string;
  path: string;
  handler: string;
  description: string;
  implemented: boolean;
  routeRegistered: boolean;
}

const REQUIRED_ENDPOINTS: EndpointCheck[] = [
  {
    method: "DELETE",
    path: "/api/users/:userId/badges/:badgeId",
    handler: "removeBadgeFromUserHandler",
    description: "Remove badge from user",
    implemented: false,
    routeRegistered: false
  },
  {
    method: "GET", 
    path: "/api/badges/available",
    handler: "getAvailableBadgesHandler",
    description: "Get available badges for current user",
    implemented: false,
    routeRegistered: false
  },
  {
    method: "POST",
    path: "/api/badges/evaluate/:userId", 
    handler: "evaluateUserBadgesHandler",
    description: "Trigger badge evaluation for user",
    implemented: false,
    routeRegistered: false
  },
  {
    method: "GET",
    path: "/api/badges/stats",
    handler: "getBadgeStatsHandler", 
    description: "Get badge statistics",
    implemented: false,
    routeRegistered: false
  },
  {
    method: "GET",
    path: "/api/badges/leaderboard",
    handler: "getBadgeLeaderboardHandler",
    description: "Get badge leaderboard/rankings", 
    implemented: false,
    routeRegistered: false
  }
];

async function checkHandlerImplementation(): Promise<void> {
  console.log("🔍 Checking handler implementations in badges.ts...\n");
  
  try {
    const handlersContent = await readFile(join(process.cwd(), "handlers/badges.ts"), "utf-8");
    
    for (const endpoint of REQUIRED_ENDPOINTS) {
      // Check if handler function exists
      const handlerPattern = new RegExp(`export const ${endpoint.handler}.*?async.*?Request.*?Response`, "s");
      endpoint.implemented = handlerPattern.test(handlersContent);
      
      if (endpoint.implemented) {
        console.log(`   ✅ ${endpoint.handler} - implemented`);
      } else {
        console.log(`   ❌ ${endpoint.handler} - missing`);
      }
    }
  } catch (error) {
    console.error("❌ Error reading handlers file:", error);
  }
  
  console.log();
}

async function checkRouteRegistration(): Promise<void> {
  console.log("🛣️  Checking route registrations in badgeRoutes.ts...\n");
  
  try {
    const routesContent = await readFile(join(process.cwd(), "routes/badgeRoutes.ts"), "utf-8");
    
    for (const endpoint of REQUIRED_ENDPOINTS) {
      // Check if route is registered
      const routePattern = new RegExp(`router\\.add\\(\\s*["'\`]${endpoint.path.replace(/:/g, "\\:")}["'\`]`, "s");
      endpoint.routeRegistered = routePattern.test(routesContent);
      
      if (endpoint.routeRegistered) {
        console.log(`   ✅ ${endpoint.method} ${endpoint.path} - registered`);
      } else {
        console.log(`   ❌ ${endpoint.method} ${endpoint.path} - not registered`);
      }
    }
  } catch (error) {
    console.error("❌ Error reading routes file:", error);
  }
  
  console.log();
}

async function checkServiceMethods(): Promise<void> {
  console.log("🔧 Checking BadgeService methods...\n");
  
  try {
    const serviceContent = await readFile(join(process.cwd(), "services/badge.service.ts"), "utf-8");
    
    const requiredMethods = [
      "getAvailableBadgesForUser",
      "evaluateUserBadges", 
      "getBadgeStats",
      "getBadgeLeaderboard",
      "removeBadge"
    ];
    
    for (const method of requiredMethods) {
      const methodPattern = new RegExp(`${method}\\s*\\([^)]*\\)\\s*:.*?{`, "s");
      const implemented = methodPattern.test(serviceContent);
      
      if (implemented) {
        console.log(`   ✅ ${method} - implemented`);
      } else {
        console.log(`   ❌ ${method} - missing`);
      }
    }
  } catch (error) {
    console.error("❌ Error reading service file:", error);
  }
  
  console.log();
}

async function generateSummaryReport(): Promise<void> {
  console.log("📊 Task 7 Implementation Summary\n");
  console.log("=" .repeat(60));
  
  let allImplemented = true;
  let allRoutesRegistered = true;
  
  for (const endpoint of REQUIRED_ENDPOINTS) {
    const status = endpoint.implemented && endpoint.routeRegistered ? "✅ COMPLETE" : "❌ INCOMPLETE";
    console.log(`${status} ${endpoint.method.padEnd(6)} ${endpoint.path}`);
    console.log(`         ${endpoint.description}`);
    
    if (!endpoint.implemented) {
      console.log(`         ⚠️  Handler missing: ${endpoint.handler}`);
      allImplemented = false;
    }
    
    if (!endpoint.routeRegistered) {
      console.log(`         ⚠️  Route not registered`);
      allRoutesRegistered = false;
    }
    
    console.log();
  }
  
  console.log("=" .repeat(60));
  
  if (allImplemented && allRoutesRegistered) {
    console.log("🎉 Task 7 is FULLY IMPLEMENTED!");
    console.log("   All required endpoints are implemented and registered.");
  } else {
    console.log("⚠️  Task 7 is PARTIALLY IMPLEMENTED");
    if (!allImplemented) {
      console.log("   Some handlers are missing or incomplete.");
    }
    if (!allRoutesRegistered) {
      console.log("   Some routes are not properly registered.");
    }
  }
  
  console.log();
}

async function checkRequirementsCoverage(): Promise<void> {
  console.log("📋 Requirements Coverage Check\n");
  
  const requirements = [
    "3.2 - Manual badge removal by administrators",
    "5.1 - View own badges and earning criteria", 
    "5.2 - View badge details with earning timestamps",
    "5.3 - View available badges with progress tracking"
  ];
  
  console.log("Task 7 addresses the following requirements:");
  for (const req of requirements) {
    console.log(`   ✅ ${req}`);
  }
  
  console.log();
}

async function main(): Promise<void> {
  console.log("🚀 Task 7: User Badge Management Endpoints Verification\n");
  
  await checkHandlerImplementation();
  await checkRouteRegistration();
  await checkServiceMethods();
  await checkRequirementsCoverage();
  await generateSummaryReport();
}

if (import.meta.main) {
  main().catch(console.error);
}