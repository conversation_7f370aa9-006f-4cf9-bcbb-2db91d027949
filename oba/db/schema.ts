import {
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  primaryKey,
  integer,
  pgSchema,
  uuid,
  pgEnum,
  boolean,
  bigint,
} from "drizzle-orm/pg-core";
import { relations, sql } from "drizzle-orm";

// Define the schema
export const BaseSchema = pgSchema("base_schema"); // Create schema

// User status enum
export const userStatusEnum = pgEnum("user_status", [
  "ONLINE", // User is online and active
  "AWAY", // User is online but inactive/away
  "BUSY", // User is online but busy/do not disturb
  "INVISIBLE", // User appears offline to others but can use the app
  "OFFLINE", // User is offline
]);

// --- Extension (uuid-ossp) ---
// Drizzle does not auto-create extensions, so you'll need a migration for this:
export const enableUuidOssp = sql.raw(
  `CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`,
);

// --- Custom SQL Functions (uuidv7 and helpers) ---

export const createUuidV7Function = `CREATE FUNCTION public.uuidv7(timestamp with time zone DEFAULT clock_timestamp()) RETURNS uuid
    LANGUAGE sql PARALLEL SAFE
    AS $$
  select encode(
    set_bit(
      set_bit(
        overlay(uuid_send(gen_random_uuid()) placing
	  substring(int8send((extract(epoch from $1)*1000)::bigint) from 3)
	  from 1 for 6),
	52, 1),
      53, 1), 'hex')::uuid;
$$;`;

export const createUuidV7FunctionComment = `
COMMENT ON FUNCTION public.uuidv7(timestamp with time zone) IS
'Generate a uuid-v7 value with a 48-bit timestamp (millisecond precision) and 74 bits of randomness';
`;

export const createUuidV7BoundaryFunction = `CREATE FUNCTION public.uuidv7_boundary(timestamp with time zone) RETURNS uuid
    LANGUAGE sql STABLE STRICT PARALLEL SAFE
    AS $$
  select encode(
    overlay('\\x00000000000070008000000000000000'::bytea
      placing substring(int8send(floor(extract(epoch from $1) * 1000)::bigint) from 3)
        from 1 for 6),
    'hex')::uuid;
$$;`;

export const createUuidV7BoundaryFunctionComment = `
COMMENT ON FUNCTION public.uuidv7_boundary(timestamp with time zone) IS
'Generate a non-random uuidv7 with the given timestamp (first 48 bits) and all random bits to 0. As the smallest possible uuidv7 for that timestamp, it may be used as a boundary for partitions.';
`;

export const createUuidV7ExtractTimestampFunction = `CREATE FUNCTION public.uuidv7_extract_timestamp(uuid) RETURNS timestamp with time zone
    LANGUAGE sql IMMUTABLE STRICT PARALLEL SAFE
    AS $$
 select to_timestamp(
   right(substring(uuid_send($1) from 1 for 6)::text, -1)::bit(48)::int8 /1000.0);
$$;`;

export const createUuidV7ExtractTimestampFunctionComment = `
COMMENT ON FUNCTION public.uuidv7_extract_timestamp(uuid) IS
'Return the timestamp stored in the first 48 bits of the UUID v7 value';
`;

export const createUuidV7SubMsFunction = `CREATE FUNCTION public.uuidv7_sub_ms(timestamp with time zone DEFAULT clock_timestamp()) RETURNS uuid
    LANGUAGE sql PARALLEL SAFE
    AS $$
 select encode(
   substring(int8send(floor(t_ms)::int8) from 3) ||
   int2send((7<<12)::int2 | ((t_ms-floor(t_ms))*4096)::int2) ||
   substring(uuid_send(gen_random_uuid()) from 9 for 8)
  , 'hex')::uuid
  from (select extract(epoch from $1)*1000 as t_ms) s
$$;`;

export const createUuidV7SubMsFunctionComment = `
COMMENT ON FUNCTION public.uuidv7_sub_ms(timestamp with time zone) IS
'Generate a uuid-v7 value with a 60-bit timestamp (sub-millisecond precision) and 62 bits of randomness';
`;

// Define tables using BaseSchema
export const UserSchema = BaseSchema.table("users", {
  id: uuid("id")
    .primaryKey()
    .default(sql`uuidv7()`),
  username: text("username").notNull().unique(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  avatar: text("avatar_url").default("placeholder"),
  status: userStatusEnum("status").default("OFFLINE"),
  preferredStatus: userStatusEnum("preferred_status").default("ONLINE"),
  statusMessage: text("status_message"),
  lastActive: timestamp("last_active", { mode: "date" }),
  isEmailVerified: boolean("is_email_verified").default(false),
  emailVerificationToken: text("email_verification_token"),
  emailVerificationExpiry: timestamp("email_verification_expiry", {
    mode: "date",
  }),
  resetToken: text("reset_token"),
  resetTokenExpiry: timestamp("reset_token_expiry", { mode: "date" }),
  refreshToken: text("refresh_token"),
  refreshTokenExpiry: timestamp("refresh_token_expiry", { mode: "date" }),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow(),
});

// Deprecated
// export const RoleSchema = BaseSchema.table('roles', {
//   id: uuid('id').primaryKey().default(sql`uuidv7()`),
//   name: text('name').notNull(),
//   color: text('color'),
//   permissions: text('permissions'),
//   serverId: uuid('server_id').references(() => ServerSchema.id),
//   createdAt: timestamp('created_at', { mode: 'date' }).defaultNow(),
// });

export const ServerRoleSchema = BaseSchema.table("server_roles", {
  id: uuid("id")
    .primaryKey()
    .default(sql`uuidv7()`),
  serverId: uuid("server_id")
    .references(() => ServerSchema.id)
    .notNull(),
  name: text("name").notNull(),
  permissions: bigint("permissions", { mode: "bigint" })
    .notNull()
    .default(0 as unknown as bigint), // Store as 64-bit integer
  color: text("color").default("#000000"), // Default color
  isDefault: boolean("is_default").default(false), // Whether this is the default role for new members
  position: integer("position").default(0), // For ordering roles
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow(),
});

export const UserRoles = BaseSchema.table("user_roles", {
  id: uuid("id")
    .primaryKey()
    .default(sql`uuidv7()`),
  userId: uuid("user_id").references(() => UserSchema.id),
  roleId: uuid("role_id").references(() => ServerRoleSchema.id),
  serverId: uuid("server_id").references(() => ServerSchema.id),
});

export const TokenSchema = BaseSchema.table("tokens", {
  id: uuid("id")
    .primaryKey()
    .default(sql`uuidv7()`),
  accessToken: text("access_token"),
  refreshToken: text("refresh_token"),
  expiresIn: timestamp("expires_in", { mode: "date" }),
});

export const ServerSchema = BaseSchema.table("servers", {
  id: uuid("id")
    .primaryKey()
    .default(sql`uuidv7()`),
  name: text("name"),
  description: text("description"),
  icon: text("icon_url"),
  ownerId: uuid("owner_id").references(() => UserSchema.id),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
});

export const ServerMembershipSchema = BaseSchema.table(
  "server_memberships",
  {
    userId: uuid("user_id")
      .notNull()
      .references(() => UserSchema.id),
    serverId: uuid("server_id")
      .notNull()
      .references(() => ServerSchema.id),
    createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
  },
  (t) => ({
    pk: primaryKey({ columns: [t.userId, t.serverId] }), // Composite primary key
  }),
);

// Define the channel type enum
export const channelTypeEnum = pgEnum("channel_type", [
  "TEXT",
  "VOICE",
  "ANNOUNCEMENT",
]);

export const ChannelCategorySchema = BaseSchema.table("channel_categories", {
  id: uuid("id")
    .primaryKey()
    .default(sql`uuidv7()`),
  name: text("name").notNull(),
  description: text("description"),
  serverId: uuid("server_id")
    .references(() => ServerSchema.id)
    .notNull(),
  position: integer("position").default(0), // For ordering categories
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
});

export const ChannelSchema = BaseSchema.table("channels", {
  id: uuid("id")
    .primaryKey()
    .default(sql`uuidv7()`),
  name: text("name").notNull(),
  description: text("description"),
  serverId: uuid("server_id").references(() => ServerSchema.id),
  categoryId: uuid("category_id").references(() => ChannelCategorySchema.id),
  type: channelTypeEnum("type").default("TEXT").notNull(), // Use the enum here
  position: integer("position").default(0), // For ordering channels within a category
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
});

export const ChannelPrivacySchema = BaseSchema.table("channel_privacy", {
  id: uuid("id")
    .primaryKey()
    .default(sql`uuidv7()`),
  channelId: uuid("channel_id")
    .references(() => ChannelSchema.id)
    .notNull(),
  isPublic: boolean("is_public").default(true).notNull(), // true for public, false for private
  isVisible: boolean("is_visible").default(true).notNull(), // Controls visibility separate from permissions
});

export const ChannelAllowedRolesSchema = BaseSchema.table(
  "channel_allowed_roles",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    channelId: uuid("channel_id")
      .references(() => ChannelSchema.id)
      .notNull(),
    roleId: uuid("role_id")
      .references(() => ServerRoleSchema.id)
      .notNull(),
  },
);

// New table for user-specific channel access
export const ChannelAllowedUsersSchema = BaseSchema.table(
  "channel_allowed_users",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    channelId: uuid("channel_id")
      .references(() => ChannelSchema.id)
      .notNull(),
    userId: uuid("user_id")
      .references(() => UserSchema.id)
      .notNull(),
  },
);

// New table for channel-specific visible roles (who can see the channel)
export const ChannelVisibleRolesSchema = BaseSchema.table(
  "channel_visible_roles",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    channelId: uuid("channel_id")
      .references(() => ChannelSchema.id)
      .notNull(),
    roleId: uuid("role_id")
      .references(() => ServerRoleSchema.id)
      .notNull(),
  },
);

// New table for channel-specific visible users (who can see the channel)
export const ChannelVisibleUsersSchema = BaseSchema.table(
  "channel_visible_users",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    channelId: uuid("channel_id")
      .references(() => ChannelSchema.id)
      .notNull(),
    userId: uuid("user_id")
      .references(() => UserSchema.id)
      .notNull(),
  },
);

// New table for channel-specific permission overrides
export const ChannelPermissionOverridesSchema = BaseSchema.table(
  "channel_permission_overrides",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    channelId: uuid("channel_id")
      .references(() => ChannelSchema.id)
      .notNull(),
    roleId: uuid("role_id").references(() => ServerRoleSchema.id),
    userId: uuid("user_id").references(() => UserSchema.id),
    // Allow either role-based or user-based override (one must be null)
    allowedPermissions: bigint("allowed_permissions", {
      mode: "bigint",
    }).default(0 as unknown as bigint),
    deniedPermissions: bigint("denied_permissions", { mode: "bigint" }).default(
      0 as unknown as bigint,
    ),
  },
);

// New table for category-level permissions
export const CategoryPermissionSchema = BaseSchema.table(
  "category_permissions",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    categoryId: uuid("category_id")
      .references(() => ChannelCategorySchema.id)
      .notNull(),
    isPrivate: boolean("is_private").default(false).notNull(),
    isVisible: boolean("is_visible").default(true).notNull(), // Controls visibility separate from permissions
  },
);

// New table for category-specific allowed roles
export const CategoryAllowedRolesSchema = BaseSchema.table(
  "category_allowed_roles",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    categoryId: uuid("category_id")
      .references(() => ChannelCategorySchema.id)
      .notNull(),
    roleId: uuid("role_id")
      .references(() => ServerRoleSchema.id)
      .notNull(),
  },
);

// New table for category-specific allowed users
export const CategoryAllowedUsersSchema = BaseSchema.table(
  "category_allowed_users",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    categoryId: uuid("category_id")
      .references(() => ChannelCategorySchema.id)
      .notNull(),
    userId: uuid("user_id")
      .references(() => UserSchema.id)
      .notNull(),
  },
);

// New table for category-specific visible roles (who can see the category)
export const CategoryVisibleRolesSchema = BaseSchema.table(
  "category_visible_roles",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    categoryId: uuid("category_id")
      .references(() => ChannelCategorySchema.id)
      .notNull(),
    roleId: uuid("role_id")
      .references(() => ServerRoleSchema.id)
      .notNull(),
  },
);

// New table for category-specific visible users (who can see the category)
export const CategoryVisibleUsersSchema = BaseSchema.table(
  "category_visible_users",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    categoryId: uuid("category_id")
      .references(() => ChannelCategorySchema.id)
      .notNull(),
    userId: uuid("user_id")
      .references(() => UserSchema.id)
      .notNull(),
  },
);

// New table for category-specific permission overrides
export const CategoryPermissionOverridesSchema = BaseSchema.table(
  "category_permission_overrides",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    categoryId: uuid("category_id")
      .references(() => ChannelCategorySchema.id)
      .notNull(),
    roleId: uuid("role_id").references(() => ServerRoleSchema.id),
    userId: uuid("user_id").references(() => UserSchema.id),
    // Allow either role-based or user-based override (one must be null)
    allowedPermissions: bigint("allowed_permissions", {
      mode: "bigint",
    }).default(0 as unknown as bigint),
    deniedPermissions: bigint("denied_permissions", { mode: "bigint" }).default(
      0 as unknown as bigint,
    ),
  },
);

export const MessageSchema = BaseSchema.table("messages", {
  id: uuid("id")
    .primaryKey()
    .default(sql`uuidv7()`),
  content: text("content").notNull(),
  userId: uuid("user_id").references(() => UserSchema.id),
  channelId: uuid("channel_id").references(() => ChannelSchema.id),
  status: text("status").default("sent"),
  type: text("type").default("text"), // For supporting different message types
  attachments: text("attachments"), // Store JSON array of attachment URLs
  editedAt: timestamp("edited_at", { mode: "date" }),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
});

export const DirectMessageSchema = BaseSchema.table("direct_messages", {
  id: uuid("id")
    .primaryKey()
    .default(sql`uuidv7()`),
  senderId: uuid("sender_id").references(() => UserSchema.id),
  receiverId: uuid("receiver_id").references(() => UserSchema.id),
  content: text("content").notNull(),
  status: text("status").default("sent"),
  readAt: timestamp("read_at", { mode: "date" }),
  attachments: text("attachments"),
  editedAt: timestamp("edited_at", { mode: "date" }),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
});

export const ServerInviteSchema = BaseSchema.table("server_invites", {
  id: uuid("id")
    .primaryKey()
    .default(sql`uuidv7()`),
  code: text("code").notNull().unique(),
  serverId: uuid("server_id").references(() => ServerSchema.id),
  createdById: uuid("created_by_id").references(() => UserSchema.id),
  expiresAt: timestamp("expires_at", { mode: "date" }),
  maxUses: integer("max_uses").notNull(),
  uses: integer("uses").notNull().default(0),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
});

export const MessageReactionSchema = BaseSchema.table("message_reactions", {
  id: uuid("id")
    .primaryKey()
    .default(sql`uuidv7()`),
  messageId: uuid("message_id").references(() => MessageSchema.id),
  userId: uuid("user_id").references(() => UserSchema.id),
  emoji: text("emoji").notNull(),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
});

export const MessageReadSchema = BaseSchema.table("message_reads", {
  id: uuid("id")
    .primaryKey()
    .default(sql`uuidv7()`),
  messageId: uuid("message_id").references(() => MessageSchema.id),
  userId: uuid("user_id").references(() => UserSchema.id),
  readAt: timestamp("read_at", { mode: "date" }).defaultNow(),
});

// Define relationships
export const usersRelations = relations(UserSchema, ({ many }) => ({
  serverMemberships: many(ServerMembershipSchema),
  badges: many(UserBadgeSchema),
  collectionProgress: many(UserCollectionProgressSchema),
  nominationsReceived: many(BadgeNominationSchema, {
    relationName: "nominee",
  }),
  nominationsGiven: many(BadgeNominationSchema, {
    relationName: "nominator",
  }),
}));

export const serversRelations = relations(ServerSchema, ({ one, many }) => ({
  owner: one(UserSchema, {
    fields: [ServerSchema.ownerId],
    references: [UserSchema.id],
  }),
  serverMemberships: many(ServerMembershipSchema),
  roles: many(ServerRoleSchema),
  channels: many(ChannelSchema),
  categories: many(ChannelCategorySchema),
  invites: many(ServerInviteSchema),
}));

export const serverMembershipsRelations = relations(
  ServerMembershipSchema,
  ({ one }) => ({
    user: one(UserSchema, {
      fields: [ServerMembershipSchema.userId],
      references: [UserSchema.id],
    }),
    server: one(ServerSchema, {
      fields: [ServerMembershipSchema.serverId],
      references: [ServerSchema.id],
    }),
  }),
);

export const channelCategoryRelations = relations(
  ChannelCategorySchema,
  ({ one, many }) => ({
    server: one(ServerSchema, {
      fields: [ChannelCategorySchema.serverId],
      references: [ServerSchema.id],
    }),
    channels: many(ChannelSchema),
    permissions: one(CategoryPermissionSchema, {
      fields: [ChannelCategorySchema.id],
      references: [CategoryPermissionSchema.categoryId],
    }),
    allowedRoles: many(CategoryAllowedRolesSchema),
    allowedUsers: many(CategoryAllowedUsersSchema),
    visibleRoles: many(CategoryVisibleRolesSchema),
    visibleUsers: many(CategoryVisibleUsersSchema),
    permissionOverrides: many(CategoryPermissionOverridesSchema),
  }),
);

export const channelRelations = relations(ChannelSchema, ({ one, many }) => ({
  server: one(ServerSchema, {
    fields: [ChannelSchema.serverId],
    references: [ServerSchema.id],
  }),
  category: one(ChannelCategorySchema, {
    fields: [ChannelSchema.categoryId],
    references: [ChannelCategorySchema.id],
  }),
  messages: many(MessageSchema),
  privacySettings: one(ChannelPrivacySchema, {
    fields: [ChannelSchema.id],
    references: [ChannelPrivacySchema.channelId],
  }),
  allowedRoles: many(ChannelAllowedRolesSchema),
  allowedUsers: many(ChannelAllowedUsersSchema),
  visibleRoles: many(ChannelVisibleRolesSchema),
  visibleUsers: many(ChannelVisibleUsersSchema),
  permissionOverrides: many(ChannelPermissionOverridesSchema),
}));

export const messageRelations = relations(MessageSchema, ({ one }) => ({
  channel: one(ChannelSchema, {
    fields: [MessageSchema.channelId],
    references: [ChannelSchema.id],
  }),
  user: one(UserSchema, {
    fields: [MessageSchema.userId],
    references: [UserSchema.id],
  }),
}));

export const directMessageRelations = relations(
  DirectMessageSchema,
  ({ one }) => ({
    sender: one(UserSchema, {
      fields: [DirectMessageSchema.senderId],
      references: [UserSchema.id],
    }),
    receiver: one(UserSchema, {
      fields: [DirectMessageSchema.receiverId],
      references: [UserSchema.id],
    }),
  }),
);

export const serverInviteRelations = relations(
  ServerInviteSchema,
  ({ one }) => ({
    server: one(ServerSchema, {
      fields: [ServerInviteSchema.serverId],
      references: [ServerSchema.id],
    }),
    createdBy: one(UserSchema, {
      fields: [ServerInviteSchema.createdById],
      references: [UserSchema.id],
    }),
  }),
);

export const messageReactionRelations = relations(
  MessageReactionSchema,
  ({ one }) => ({
    message: one(MessageSchema, {
      fields: [MessageReactionSchema.messageId],
      references: [MessageSchema.id],
    }),
    user: one(UserSchema, {
      fields: [MessageReactionSchema.userId],
      references: [UserSchema.id],
    }),
  }),
);

export const messageReadRelations = relations(MessageReadSchema, ({ one }) => ({
  message: one(MessageSchema, {
    fields: [MessageReadSchema.messageId],
    references: [MessageSchema.id],
  }),
  user: one(UserSchema, {
    fields: [MessageReadSchema.userId],
    references: [UserSchema.id],
  }),
}));

export const roleRelations = relations(ServerRoleSchema, ({ one }) => ({
  server: one(ServerSchema, {
    fields: [ServerRoleSchema.serverId],
    references: [ServerSchema.id],
  }),
}));

// Friend relationship status enum
export const friendStatusEnum = pgEnum("friend_status", [
  "PENDING", // Friend request sent but not accepted
  "ACCEPTED", // Friend request accepted, users are friends
  "BLOCKED", // One user has blocked the other
]);

// Badge category enum
export const badgeCategoryEnum = pgEnum("badge_category", [
  "achievement", // Achievement-based badges
  "role", // Role-based badges
  "special", // Special status badges
  "community", // Community contribution badges
  "milestone", // Milestone badges
]);

// Enhanced enums for advanced badge system
export const unlockTypeEnum = pgEnum("unlock_type", [
  "automatic", // Automatically assigned based on criteria
  "manual", // Manually assigned by administrators
  "peer_voted", // Assigned through peer nominations
  "manual_invitation", // Invitation-only badges
]);

export const collectionTypeEnum = pgEnum("collection_type", [
  "progressive", // Sequential badge collections
  "standalone", // Individual badge collections
]);

// Friend relationships schema
export const FriendshipSchema = BaseSchema.table(
  "friendships",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    // The user who initiated the friendship/request
    userId: uuid("user_id")
      .notNull()
      .references(() => UserSchema.id, { onDelete: "cascade" }),
    // The target user of the friendship/request
    friendId: uuid("friend_id")
      .notNull()
      .references(() => UserSchema.id, { onDelete: "cascade" }),
    // The status of the friendship
    status: friendStatusEnum("status").notNull().default("PENDING"),
    // Who initiated the current status (for blocking)
    initiatedBy: uuid("initiated_by")
      .notNull()
      .references(() => UserSchema.id),
    createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
    updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow(),
  },
  (table) => {
    return {
      // Ensure a user can't have duplicate relationships with the same friend
      userFriendIndex: uniqueIndex("user_friend_idx").on(
        table.userId,
        table.friendId,
      ),
    };
  },
);

// Friendship relations
export const friendshipRelations = relations(FriendshipSchema, ({ one }) => ({
  user: one(UserSchema, {
    fields: [FriendshipSchema.userId],
    references: [UserSchema.id],
  }),
  friend: one(UserSchema, {
    fields: [FriendshipSchema.friendId],
    references: [UserSchema.id],
  }),
  initiator: one(UserSchema, {
    fields: [FriendshipSchema.initiatedBy],
    references: [UserSchema.id],
  }),
}));

// Server Ban Schema
export const ServerBanSchema = BaseSchema.table(
  "server_bans",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    serverId: uuid("server_id")
      .notNull()
      .references(() => ServerSchema.id, { onDelete: "cascade" }),
    userId: uuid("user_id")
      .notNull()
      .references(() => UserSchema.id, { onDelete: "cascade" }),
    bannedById: uuid("banned_by_id")
      .notNull()
      .references(() => UserSchema.id),
    reason: text("reason"),
    bannedAt: timestamp("banned_at", { mode: "date" }).defaultNow(),
  },
  (table) => {
    return {
      // Ensure a user can't be banned multiple times from the same server
      serverUserIndex: uniqueIndex("server_user_ban_idx").on(
        table.serverId,
        table.userId,
      ),
    };
  },
);

// Server Ban relations
export const serverBanRelations = relations(ServerBanSchema, ({ one }) => ({
  server: one(ServerSchema, {
    fields: [ServerBanSchema.serverId],
    references: [ServerSchema.id],
  }),
  user: one(UserSchema, {
    fields: [ServerBanSchema.userId],
    references: [UserSchema.id],
  }),
  bannedBy: one(UserSchema, {
    fields: [ServerBanSchema.bannedById],
    references: [UserSchema.id],
  }),
}));

// Badge Collections Schema - for progressive badge collections
export const BadgeCollectionSchema = BaseSchema.table("badge_collections", {
  id: uuid("id")
    .primaryKey()
    .default(sql`uuidv7()`),
  collectionId: text("collection_id").notNull().unique(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  type: collectionTypeEnum("type").default("progressive"),
  totalBadges: integer("total_badges").default(0),
  unlockedBy: text("unlocked_by"), // e.g., 'activity_and_time', 'exploration_and_impact'
  completionReward: text("completion_reward"), // JSONB stored as text for completion reward data
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
  updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow(),
});

// Enhanced Badge Types Schema with rich design and collection support
export const BadgeTypeSchema = BaseSchema.table(
  "badge_types",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    collectionId: uuid("collection_id").references(() => BadgeCollectionSchema.id, { onDelete: "set null" }),
    badgeId: text("badge_id").notNull(), // Unique within collection
    name: text("name").notNull(),
    title: text("title"), // Display title (e.g., "Founding Pioneer")
    description: text("description").notNull(),
    icon: text("icon").default("🏆"), // Emoji or icon identifier
    tooltip: text("tooltip"), // Rich tooltip text
    design: text("design").notNull(), // JSONB stored as text for visual design properties
    criteria: text("criteria").notNull(), // JSONB stored as text for earning criteria
    perks: text("perks"), // JSONB stored as text for benefits and privileges
    unlockType: unlockTypeEnum("unlock_type").default("automatic"),
    visualDescription: text("visual_description"), // Description of visual appearance
    animation: text("animation"), // Animation type
    displayOrder: integer("display_order").default(0), // Order within collection
    category: badgeCategoryEnum("category").default("achievement"),
    isActive: boolean("is_active").default(true),
    createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
    updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow(),
  },
  (table) => {
    return {
      // Ensure badge_id is unique within collection
      collectionBadgeIndex: uniqueIndex("collection_badge_idx").on(
        table.collectionId,
        table.badgeId,
      ),
      // Ensure display_order is unique within collection
      collectionOrderIndex: uniqueIndex("collection_order_idx").on(
        table.collectionId,
        table.displayOrder,
      ),
    };
  },
);

// Enhanced User Badges Schema with collection progress tracking
export const UserBadgeSchema = BaseSchema.table(
  "user_badges",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    userId: uuid("user_id")
      .notNull()
      .references(() => UserSchema.id, { onDelete: "cascade" }),
    badgeTypeId: uuid("badge_type_id")
      .notNull()
      .references(() => BadgeTypeSchema.id, { onDelete: "cascade" }),
    collectionId: uuid("collection_id").references(() => BadgeCollectionSchema.id, { onDelete: "set null" }),
    assignedBy: uuid("assigned_by").references(() => UserSchema.id), // NULL for automatic assignments
    assignedAt: timestamp("assigned_at", { mode: "date" }).defaultNow(),
    isVisible: boolean("is_visible").default(true),
    progressData: text("progress_data"), // JSONB stored as text for progress tracking data
    perksGranted: text("perks_granted"), // JSONB stored as text for record of granted perks
  },
  (table) => {
    return {
      // Ensure a user can't have duplicate badges of the same type
      userBadgeIndex: uniqueIndex("user_badge_idx").on(
        table.userId,
        table.badgeTypeId,
      ),
    };
  },
);

// User Collection Progress Schema - tracks progress through badge collections
export const UserCollectionProgressSchema = BaseSchema.table(
  "user_collection_progress",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    userId: uuid("user_id")
      .notNull()
      .references(() => UserSchema.id, { onDelete: "cascade" }),
    collectionId: uuid("collection_id")
      .notNull()
      .references(() => BadgeCollectionSchema.id, { onDelete: "cascade" }),
    badgesEarned: integer("badges_earned").default(0),
    totalBadges: integer("total_badges").default(0),
    isCompleted: boolean("is_completed").default(false),
    completionDate: timestamp("completion_date", { mode: "date" }),
    completionRewardGranted: boolean("completion_reward_granted").default(false),
    createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
    updatedAt: timestamp("updated_at", { mode: "date" }).defaultNow(),
  },
  (table) => {
    return {
      // Ensure a user can't have duplicate progress records for the same collection
      userCollectionIndex: uniqueIndex("user_collection_idx").on(
        table.userId,
        table.collectionId,
      ),
    };
  },
);

// Badge Nominations Schema - tracks peer nominations for special badges
export const BadgeNominationSchema = BaseSchema.table(
  "badge_nominations",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`uuidv7()`),
    badgeTypeId: uuid("badge_type_id")
      .notNull()
      .references(() => BadgeTypeSchema.id, { onDelete: "cascade" }),
    nomineeUserId: uuid("nominee_user_id")
      .notNull()
      .references(() => UserSchema.id, { onDelete: "cascade" }),
    nominatorUserId: uuid("nominator_user_id")
      .notNull()
      .references(() => UserSchema.id, { onDelete: "cascade" }),
    nominationReason: text("nomination_reason"),
    status: text("status").default("pending"), // 'pending', 'approved', 'rejected'
    createdAt: timestamp("created_at", { mode: "date" }).defaultNow(),
    processedAt: timestamp("processed_at", { mode: "date" }),
  },
  (table) => {
    return {
      // Ensure a user can't nominate the same person for the same badge multiple times
      nominationIndex: uniqueIndex("nomination_idx").on(
        table.badgeTypeId,
        table.nomineeUserId,
        table.nominatorUserId,
      ),
    };
  },
);

// Badge Collection relations
export const badgeCollectionRelations = relations(BadgeCollectionSchema, ({ many }) => ({
  badgeTypes: many(BadgeTypeSchema),
  userProgress: many(UserCollectionProgressSchema),
  userBadges: many(UserBadgeSchema),
}));

// Enhanced Badge Type relations
export const badgeTypeRelations = relations(BadgeTypeSchema, ({ one, many }) => ({
  collection: one(BadgeCollectionSchema, {
    fields: [BadgeTypeSchema.collectionId],
    references: [BadgeCollectionSchema.id],
  }),
  userBadges: many(UserBadgeSchema),
  nominations: many(BadgeNominationSchema),
}));

// Enhanced User Badge relations
export const userBadgeRelations = relations(UserBadgeSchema, ({ one }) => ({
  user: one(UserSchema, {
    fields: [UserBadgeSchema.userId],
    references: [UserSchema.id],
  }),
  badgeType: one(BadgeTypeSchema, {
    fields: [UserBadgeSchema.badgeTypeId],
    references: [BadgeTypeSchema.id],
  }),
  collection: one(BadgeCollectionSchema, {
    fields: [UserBadgeSchema.collectionId],
    references: [BadgeCollectionSchema.id],
  }),
  assignedByUser: one(UserSchema, {
    fields: [UserBadgeSchema.assignedBy],
    references: [UserSchema.id],
  }),
}));

// User Collection Progress relations
export const userCollectionProgressRelations = relations(UserCollectionProgressSchema, ({ one }) => ({
  user: one(UserSchema, {
    fields: [UserCollectionProgressSchema.userId],
    references: [UserSchema.id],
  }),
  collection: one(BadgeCollectionSchema, {
    fields: [UserCollectionProgressSchema.collectionId],
    references: [BadgeCollectionSchema.id],
  }),
}));

// Badge Nomination relations
export const badgeNominationRelations = relations(BadgeNominationSchema, ({ one }) => ({
  badgeType: one(BadgeTypeSchema, {
    fields: [BadgeNominationSchema.badgeTypeId],
    references: [BadgeTypeSchema.id],
  }),
  nominee: one(UserSchema, {
    fields: [BadgeNominationSchema.nomineeUserId],
    references: [UserSchema.id],
  }),
  nominator: one(UserSchema, {
    fields: [BadgeNominationSchema.nominatorUserId],
    references: [UserSchema.id],
  }),
}));

// Export all schemas and relations
export const schema = {
  UserSchema,
  //RoleSchema,
  UserRoles,
  TokenSchema,
  ServerSchema,
  ServerMembershipSchema,
  ChannelSchema,
  ChannelCategorySchema,
  MessageSchema,
  DirectMessageSchema,
  ServerInviteSchema,
  MessageReactionSchema,
  MessageReadSchema,
  FriendshipSchema,
  ServerBanSchema,
  BadgeCollectionSchema,
  BadgeTypeSchema,
  UserBadgeSchema,
  UserCollectionProgressSchema,
  BadgeNominationSchema,
  ChannelPrivacySchema,
  ChannelAllowedRolesSchema,
  ChannelAllowedUsersSchema,
  ChannelVisibleRolesSchema,
  ChannelVisibleUsersSchema,
  ChannelPermissionOverridesSchema,
  CategoryPermissionSchema,
  CategoryAllowedRolesSchema,
  CategoryAllowedUsersSchema,
  CategoryVisibleRolesSchema,
  CategoryVisibleUsersSchema,
  CategoryPermissionOverridesSchema,
  usersRelations,
  serversRelations,
  serverMembershipsRelations,
  channelRelations,
  channelCategoryRelations,
  messageRelations,
  directMessageRelations,
  serverInviteRelations,
  messageReactionRelations,
  messageReadRelations,
  roleRelations,
  friendshipRelations,
  serverBanRelations,
  badgeCollectionRelations,
  badgeTypeRelations,
  userBadgeRelations,
  userCollectionProgressRelations,
  badgeNominationRelations,
};
