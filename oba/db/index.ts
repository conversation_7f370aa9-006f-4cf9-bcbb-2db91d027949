import { drizzle } from "drizzle-orm/postgres-js";
import { migrate } from "drizzle-orm/postgres-js/migrator";
import { join } from "path";
import postgres from "postgres";
import * as schema from "./schema";
import {
  createUuidV7BoundaryFunction,
  createUuidV7BoundaryFunctionComment,
  createUuidV7ExtractTimestampFunction,
  createUuidV7ExtractTimestampFunctionComment,
  createUuidV7Function,
  createUuidV7FunctionComment,
  createUuidV7Functions,
  createUuidV7SubMsFunction,
  createUuidV7SubMsFunctionComment,
  enableUuidOssp,
} from "./schema";
import { sql } from "drizzle-orm/sql";

// Log environment variables for debugging
console.log("Database connection parameters:");
console.log("POSTGRES_SCHEMA:", process.env.POSTGRES_SCHEMA);
console.log("POSTGRES_USERNAME:", process.env.POSTGRES_USERNAME);
console.log("POSTGRES_HOST:", process.env.POSTGRES_HOST);
console.log("POSTGRES_PORT:", process.env.POSTGRES_PORT);
console.log("POSTGRES_DB:", process.env.POSTGRES_DB);

// Construct database URL
const DATABASE_URL = `${process.env.POSTGRES_SCHEMA}://${process.env.POSTGRES_USERNAME}:${process.env.POSTGRES_PASSWORD}@${process.env.POSTGRES_HOST}:${process.env.POSTGRES_PORT}/${process.env.POSTGRES_DB}`;
console.log("Database URL:", DATABASE_URL);

// Create postgres client with error handling and retry logic
let client;
let retryCount = 0;
const maxRetries = 5;
const retryDelay = 3000; // 3 seconds

const connectWithRetry = async () => {
  while (retryCount < maxRetries) {
    try {
      client = postgres(DATABASE_URL, {
        onnotice: () => {}, // Suppress notice messages
        debug: process.env.NODE_ENV === "development", // Enable debug in development
        connect_timeout: 10, // 10 seconds connection timeout
        idle_timeout: 30, // Close idle connections after 30 seconds
      });

      // Test the connection
      await client`SELECT 1 as connection_test`;
      console.log(
        "Database client created successfully and connection verified",
      );
      return;
    } catch (error) {
      retryCount++;
      console.error(
        `Failed to connect to database (attempt ${retryCount}/${maxRetries}):`,
        error,
      );

      if (retryCount >= maxRetries) {
        console.error(
          "Maximum retry attempts reached. Could not connect to database.",
        );
        throw new Error(
          "Database connection failed after multiple attempts. Please check your database configuration and ensure the database server is running.",
        );
      }

      console.log(`Retrying in ${retryDelay / 1000} seconds...`);
      await new Promise((resolve) => setTimeout(resolve, retryDelay));
    }
  }
};

// Initialize connection
try {
  // For synchronous initialization, we'll create the client without testing the connection
  client = postgres(DATABASE_URL, {
    onnotice: () => {}, // Suppress notice messages
    debug: process.env.NODE_ENV === "development", // Enable debug in development
  });
  console.log("Database client created successfully");

  // Start the retry process in the background
  connectWithRetry().catch((error) => {
    console.error(
      "Failed to establish database connection after retries:",
      error,
    );
  });
} catch (error) {
  console.error("Failed to create database client:", error);
  throw new Error(
    "Database connection failed. Please check your environment variables.",
  );
}

// Create drizzle ORM instance
export const db = drizzle(client, { schema });

// console.log("Setting up routines before migrating...");
// await db.execute(enableUuidOssp);
// await db.execute(createUuidV7Function);
// await db.execute(createUuidV7BoundaryFunction);
// await db.execute(createUuidV7ExtractTimestampFunction);
// await db.execute(createUuidV7SubMsFunction);
// await db.execute(createUuidV7FunctionComment);
// await db.execute(createUuidV7BoundaryFunctionComment);
// await db.execute(createUuidV7ExtractTimestampFunctionComment);
// await db.execute(createUuidV7SubMsFunctionComment);

// console.log("Routines set up successfully");
// console.log('Migrating any changes to the database...');
// await migrate(db, { migrationsFolder: join(__dirname, '../drizzle') });
// console.log('Migrations completed successfully');

console.log("Drizzle ORM initialized successfully");
