import { and, eq, or, sql, desc, not, inArray } from "drizzle-orm";
import crypto from "crypto";
import * as argon2 from "argon2";
import type { drizzle } from "drizzle-orm/pg-proxy";
import {
  UserSchema,
  ServerSchema,
  ServerMembershipSchema,
  ChannelSchema,
  channelTypeEnum,
  ChannelPrivacySchema,
  ChannelAllowedRolesSchema,
  ServerInviteSchema,
  MessageSchema,
  UserRoles,
  ServerRoleSchema,
  DirectMessageSchema,
  MessageReactionSchema,
  FriendshipSchema,
  friendStatusEnum,
  ChannelCategorySchema,
} from "./schema";
import { db } from "./index";
import validator from "validator";
import { AuthenticationError } from "../class/errors";

import { customAlphabet } from "nanoid";
import { v4 as uuidv4 } from "uuid";

// Type for user insertion
type NewUser = typeof UserSchema.$inferInsert;
type User = typeof UserSchema.$inferSelect;
type NewServer = typeof ServerSchema.$inferInsert;
// Define more specific types based on your schema
type Channel = typeof ChannelSchema.$inferSelect;
type Server = typeof ServerSchema.$inferSelect;
type ServerMembership = typeof ServerMembershipSchema.$inferSelect;
type ChannelCategory = typeof ChannelCategorySchema.$inferSelect;

interface UserWithServers {
  serverMemberships: (ServerMembership & { server: Server })[];
}

interface ServerDetails {
  server: Server;
  owner: User;
  members: any[]; // Members with roles
  roles: any[]; // Server roles
  channels: Channel[];
  categories: ChannelCategory[];
  invites?: any[]; // Optional server invites
}

export function sanitizeTextInput(input: string): string {
  return validator.escape(validator.trim(input));
}

export function sanitizeEmailInput(email: string): string {
  const sanitizedEmail = validator.normalizeEmail(email);
  if (!sanitizedEmail) {
    throw new AuthenticationError("Invalid email format");
  }
  return sanitizedEmail;
}

export function sanitizeURLInput(url: string): string {
  // Use validator to trim and escape the URL
  const sanitizedUrl = validator.trim(url);
  if (!validator.isURL(sanitizedUrl)) {
    throw new AuthenticationError("Invalid URL format");
  }
  return validator.escape(sanitizedUrl);
}

// Generate a random invite code using nanoid
// Customize the length and characters as needed

const generateInviteCode = customAlphabet(
  "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
  8,
); // Customize the length and characters as needed

// Example to make them unique
export function generateUniqueInviteCode() {
  const shortUuid = uuidv4().split("-")[0]; // Get the first part of a UUID
  const randomPart = generateInviteCode();
  return `${shortUuid}-${randomPart}`;
}

export async function registerUser(
  db: ReturnType<typeof drizzle>,
  userData: NewUser,
): Promise<Partial<User>> {
  // 1. Input Validation and Sanitization (Optional but Recommended)
  if (!userData.username || !userData.email || !userData.password) {
    throw new AuthenticationError("Username, email, and password are required");
  }

  const sanitizedUsername = sanitizeTextInput(userData.username);
  const sanitizedEmail = validator.normalizeEmail(userData.email) || ""; // Normalize email or set to empty if invalid

  if (!sanitizedEmail) {
    throw new AuthenticationError("Invalid email format");
  }

  userData.username = sanitizedUsername;

  // 2. Check for Existing User (Optimized)
  const existingUser = await db
    .select()
    .from(UserSchema)
    .where(
      or(
        eq(UserSchema.username, userData.username),
        eq(UserSchema.email, userData.email),
      ),
    )
    .limit(1); // We only need to know if a user exists, not retrieve all fields

  if (existingUser.length > 0) {
    throw new AuthenticationError("Username or email already exists");
  }

  // 3. Hash Password
  const hashedPassword = await argon2.hash(userData.password);

  // 4. Insert New User (Optimized)
  try {
    const newUser = await db
      .insert(UserSchema)
      .values({
        ...userData, // Use spread syntax if userData contains other fields
        password: hashedPassword,
        // Removed unnecessary avatar: 'placeholder' as it is already handled by default value in schema
      })
      .returning({
        id: UserSchema.id,
        username: UserSchema.username,
        email: UserSchema.email,
        createdAt: UserSchema.createdAt,
        updatedAt: UserSchema.updatedAt,
      }); // Specify only necessary fields

    return newUser[0];
  } catch (error) {
    // Handle database errors (e.g., unique constraint violations)
    console.error("Error inserting user:", error);
    throw new Error("Failed to register user"); // Generic error for security
  }
}

export async function validateUser(
  db: ReturnType<typeof drizzle>,
  credentials: { username: string; password: string },
): Promise<Partial<User>> {
  console.log("validateUser called with username:", credentials.username);

  // 1. Input Validation
  if (!credentials.username || !credentials.password) {
    console.log("Validation error: Username and password are required");
    throw new AuthenticationError("Username and password are required");
  }

  const sanitizedUsername = sanitizeTextInput(credentials.username);
  console.log("Sanitized username:", sanitizedUsername);

  try {
    // 2. Find User by Username
    console.log("Querying database for user...");
    const user = await db
      .select()
      .from(UserSchema)
      .where(eq(UserSchema.username, sanitizedUsername))
      .limit(1); // Only fetch one

    console.log("Database query completed, found users:", user.length);

    if (user.length === 0) {
      console.log("User not found in database");
      throw new AuthenticationError("Invalid username or password");
    }

    console.log("User found, verifying password...");

    // 3. Verify Password
    try {
      const isPasswordValid = await argon2.verify(
        user[0].password,
        credentials.password,
      );

      console.log("Password verification result:", isPasswordValid);

      if (!isPasswordValid) {
        console.log("Password verification failed");
        throw new AuthenticationError("Invalid username or password");
      }

      console.log("Password verified successfully");

      // 4. Return Necessary User Data (Security)
      const userData = {
        id: user[0].id,
        username: user[0].username,
        email: user[0].email,
        createdAt: user[0].createdAt,
        updatedAt: user[0].updatedAt,
        avatar: user[0].avatar,
        isEmailVerified: user[0].isEmailVerified,
        password: "",
      };

      console.log("Returning user data for ID:", userData.id);
      return userData;
    } catch (error) {
      console.error("Error during password verification:", error);
      throw error;
    }
  } catch (error) {
    console.error("Error in validateUser:", error);
    throw error;
  }
}

export async function createServer(
  db: ReturnType<typeof drizzle>,
  serverName: string,
  ownerId: string,
) {
  console.log("createServer DB function called with:", { serverName, ownerId });

  try {
    console.log("Inserting new server into database...");
    const newServer = await db
      .insert(ServerSchema)
      .values({
        name: serverName,
        description: "test server",
        ownerId: ownerId,
      })
      .returning({
        id: ServerSchema.id,
        name: ServerSchema.name,
        description: ServerSchema.description,
        ownerId: ServerSchema.ownerId,
        createdAt: ServerSchema.createdAt,
      }); // Specify only necessary fields

    console.log("Server created successfully:", newServer[0]);

    console.log("Adding owner as server member...");
    await addUserToServer(db, ownerId, newServer[0].id);
    console.log("Owner added to server successfully");

    console.log("Server setup completed successfully");
    return newServer[0];
  } catch (error) {
    console.error("Error inserting server:", error);
    if (error.code) {
      console.error("Database error code:", error.code);
    }
    if (error.detail) {
      console.error("Error detail:", error.detail);
    }
    throw new Error(
      "Failed to create server: " + (error.message || "Unknown error"),
    );
  }
}

/**
 * Creates a new channel.
 *
 * @param serverId - The ID of the server to create the channel in.
 * @param name - The name of the new channel.
 * @param description - An optional description for the channel.
 * @param type - The type of the channel (from channelTypeEnum). Defaults to 'TEXT'.
 * @param isPublic - Whether the channel is public or private. Defaults to true.
 * @param allowedRoleIds - An optional array of role IDs allowed to access the channel (for private channels).
 * @returns The newly created channel object, or null if an error occurred.
 */
export async function createChannel(
  db: ReturnType<typeof drizzle>,
  serverId: string,
  name: string,
  details: {
    description?: string;
    type: (typeof channelTypeEnum.enumValues)[number]; // Use enum type
    isPublic?: boolean;
    allowedRoleIds?: string[];
    categoryId?: string; // Added categoryId parameter
    position?: number; // Added position parameter
  },
): Promise<typeof ChannelSchema.$inferSelect | null> {
  const { description, type, isPublic, allowedRoleIds, categoryId, position } =
    details;
  try {
    return await db.transaction(async (trx) => {
      // Determine the position for the new channel
      let channelPosition = position;

      if (channelPosition === undefined) {
        // Import the position manager
        const { getNextServerItemPosition } = await import(
          "./utils/position-manager"
        );

        // Get the next available position for this item in the server
        channelPosition = await getNextServerItemPosition(serverId);
      } else if (position !== undefined && position > 0) {
        // If a specific position is requested, shift existing items to make room
        // This would need to be updated to shift both channels and categories
        // For now, we'll just use the provided position
      }

      // Create the new channel with the determined position
      const newChannel = await trx
        .insert(ChannelSchema)
        .values({
          name,
          description,
          serverId,
          categoryId, // Add categoryId to the channel
          type,
          position: channelPosition,
          createdAt: new Date(),
        })
        .returning();

      // Create privacy settings
      await trx.insert(ChannelPrivacySchema).values({
        channelId: newChannel[0].id,
        isPublic,
      });

      // Add allowed roles if it's a private channel and roles are provided
      if (!isPublic && allowedRoleIds && allowedRoleIds.length > 0) {
        const allowedRolesValues = allowedRoleIds.map((roleId) => ({
          channelId: newChannel[0].id,
          roleId,
        }));
        await trx.insert(ChannelAllowedRolesSchema).values(allowedRolesValues);
      }

      return newChannel[0];
    });
  } catch (error) {
    console.error("Error creating channel:", error);
    return null; // Or throw a custom error
  }
}

/**
 * Fetches the details of a channel, including its privacy settings and allowed roles.
 *
 * @param channelId - The ID of the channel to fetch.
 * @returns The channel details object, or null if the channel is not found or an error occurred.
 */
/**
 * Creates a new channel category.
 *
 * @param db - The database connection.
 * @param serverId - The ID of the server to create the category in.
 * @param name - The name of the new category.
 * @param description - An optional description for the category.
 * @param position - An optional position for ordering categories (default: 0).
 * @returns The newly created category object, or null if an error occurred.
 */
export async function createChannelCategory(
  db: ReturnType<typeof drizzle>,
  serverId: string,
  name: string,
  description?: string,
  position?: number,
): Promise<typeof ChannelCategorySchema.$inferSelect | null> {
  try {
    // Determine the position for the new category
    let categoryPosition = position;

    if (categoryPosition === undefined) {
      // Import the position manager
      const { getNextServerItemPosition } = await import(
        "./utils/position-manager"
      );

      // Get the next available position for this item in the server
      categoryPosition = await getNextServerItemPosition(serverId);
    } else if (position !== undefined && position > 0) {
      // If a specific position is requested, shift existing items to make room
      // This would need to be updated to shift both channels and categories
      // For now, we'll just use the provided position
    }

    const newCategory = await db
      .insert(ChannelCategorySchema)
      .values({
        name,
        description,
        serverId,
        position: categoryPosition,
        createdAt: new Date(),
      })
      .returning();

    return newCategory[0];
  } catch (error) {
    console.error("Error creating channel category:", error);
    return null;
  }
}

/**
 * Updates an existing channel category.
 *
 * @param db - The database connection.
 * @param categoryId - The ID of the category to update.
 * @param updates - An object containing the fields to update.
 * @returns The updated category object, or null if the category is not found or an error occurred.
 */
export async function updateChannelCategory(
  db: ReturnType<typeof drizzle>,
  categoryId: string,
  updates: {
    name?: string;
    description?: string;
    position?: number;
  },
): Promise<typeof ChannelCategorySchema.$inferSelect | null> {
  try {
    // Check if category exists
    const existingCategory = await db
      .select()
      .from(ChannelCategorySchema)
      .where(eq(ChannelCategorySchema.id, categoryId));

    if (existingCategory.length === 0) {
      return null; // Category not found
    }

    // Update category details
    const categoryUpdates: Record<string, any> = {};
    if (updates.name !== undefined) categoryUpdates.name = updates.name;
    if (updates.description !== undefined)
      categoryUpdates.description = updates.description;
    if (updates.position !== undefined)
      categoryUpdates.position = updates.position;

    // Only update if there are changes
    if (Object.keys(categoryUpdates).length > 0) {
      const updatedCategory = await db
        .update(ChannelCategorySchema)
        .set(categoryUpdates)
        .where(eq(ChannelCategorySchema.id, categoryId))
        .returning();

      return updatedCategory[0];
    }

    // If no updates were made, return the existing category
    return existingCategory[0];
  } catch (error) {
    console.error("Error updating channel category:", error);
    return null;
  }
}

/**
 * Deletes a channel category.
 * Note: This does not delete the channels in the category, it just removes the category.
 * Channels in this category will become uncategorized (categoryId = null).
 *
 * @param db - The database connection.
 * @param categoryId - The ID of the category to delete.
 * @returns True if the category was successfully deleted, false otherwise.
 */
export async function deleteChannelCategory(
  db: ReturnType<typeof drizzle>,
  categoryId: string,
): Promise<boolean> {
  try {
    return await db.transaction(async (trx) => {
      // Check if category exists
      const existingCategory = await trx
        .select()
        .from(ChannelCategorySchema)
        .where(eq(ChannelCategorySchema.id, categoryId));

      if (existingCategory.length === 0) {
        return false; // Category not found
      }

      // Update all channels in this category to have no category
      await trx
        .update(ChannelSchema)
        .set({ categoryId: null })
        .where(eq(ChannelSchema.categoryId, categoryId));

      // Delete the category
      await trx
        .delete(ChannelCategorySchema)
        .where(eq(ChannelCategorySchema.id, categoryId));

      return true;
    });
  } catch (error) {
    console.error("Error deleting channel category:", error);
    return false;
  }
}

/**
 * Gets all categories for a server.
 *
 * @param db - The database connection.
 * @param serverId - The ID of the server to get categories for.
 * @returns An array of category objects, or an empty array if none are found or an error occurred.
 */
export async function getServerCategories(
  db: ReturnType<typeof drizzle>,
  serverId: string,
): Promise<(typeof ChannelCategorySchema.$inferSelect)[]> {
  try {
    const categories = await db
      .select()
      .from(ChannelCategorySchema)
      .where(eq(ChannelCategorySchema.serverId, serverId))
      .orderBy(ChannelCategorySchema.position);

    return categories;
  } catch (error) {
    console.error("Error fetching server categories:", error);
    return [];
  }
}

/**
 * Gets a specific category by ID.
 *
 * @param db - The database connection.
 * @param categoryId - The ID of the category to get.
 * @returns The category object, or null if not found or an error occurred.
 */
export async function getCategoryById(
  db: ReturnType<typeof drizzle>,
  categoryId: string,
): Promise<typeof ChannelCategorySchema.$inferSelect | null> {
  try {
    const category = await db
      .select()
      .from(ChannelCategorySchema)
      .where(eq(ChannelCategorySchema.id, categoryId))
      .limit(1);

    if (category.length === 0) {
      return null;
    }

    return category[0];
  } catch (error) {
    console.error("Error fetching category:", error);
    return null;
  }
}

/**
 * Gets all channels in a specific category.
 *
 * @param db - The database connection.
 * @param categoryId - The ID of the category to get channels for.
 * @returns An array of channel objects, or an empty array if none are found or an error occurred.
 */
export async function getCategoryChannels(
  db: ReturnType<typeof drizzle>,
  categoryId: string,
): Promise<(typeof ChannelSchema.$inferSelect)[]> {
  try {
    const channels = await db
      .select()
      .from(ChannelSchema)
      .where(eq(ChannelSchema.categoryId, categoryId))
      .orderBy(ChannelSchema.position);

    return channels;
  } catch (error) {
    console.error("Error fetching category channels:", error);
    return [];
  }
}

export async function getChannelDetails(
  db: ReturnType<typeof drizzle>,
  channelId: string,
): Promise<any | null> {
  try {
    // Get the channel
    const channel = await db
      .select()
      .from(ChannelSchema)
      .where(eq(ChannelSchema.id, channelId))
      .limit(1);

    if (channel.length === 0) {
      return null; // Channel not found
    }

    // Get privacy settings
    const privacySettings = await db
      .select()
      .from(ChannelPrivacySchema)
      .where(eq(ChannelPrivacySchema.channelId, channelId))
      .limit(1);

    // Get allowed roles
    const allowedRoles = await db
      .select({
        id: ChannelAllowedRolesSchema.id,
        channelId: ChannelAllowedRolesSchema.channelId,
        roleId: ChannelAllowedRolesSchema.roleId,
      })
      .from(ChannelAllowedRolesSchema)
      .where(eq(ChannelAllowedRolesSchema.channelId, channelId));

    // Combine the results
    return {
      ...channel[0],
      privacySettings: privacySettings.length > 0 ? privacySettings[0] : null,
      allowedRoles: allowedRoles,
    };
  } catch (error) {
    console.error("Error fetching channel details:", error);
    return null; // Or handle the error as appropriate
  }
}

/**
 * Updates an existing channel's details.
 *
 * @param channelId - The ID of the channel to update.
 * @param updates - An object containing the fields to update.
 * @param allowedRoleIds - Optional array of role IDs allowed to access the channel (for private channels).
 * @returns The updated channel object, or null if the channel is not found or an error occurred.
 */
export async function updateChannel(
  db: ReturnType<typeof drizzle>,
  channelId: string,
  updates: {
    name?: string;
    description?: string;
    type?: (typeof channelTypeEnum.enumValues)[number];
    isPublic?: boolean;
    categoryId?: string;
    position?: number;
  },
  allowedRoleIds?: string[],
): Promise<typeof ChannelSchema.$inferSelect | null> {
  try {
    return await db.transaction(async (trx) => {
      // Check if channel exists
      const existingChannel = await trx
        .select()
        .from(ChannelSchema)
        .where(eq(ChannelSchema.id, channelId));

      if (existingChannel.length === 0) {
        return null; // Channel not found
      }

      // Update channel basic details
      const channelUpdates: Record<string, any> = {};
      if (updates.name !== undefined) channelUpdates.name = updates.name;
      if (updates.description !== undefined)
        channelUpdates.description = updates.description;
      if (updates.type !== undefined) channelUpdates.type = updates.type;
      if (updates.categoryId !== undefined)
        channelUpdates.categoryId = updates.categoryId;
      if (updates.position !== undefined)
        channelUpdates.position = updates.position;

      // Only update if there are changes
      if (Object.keys(channelUpdates).length > 0) {
        await trx
          .update(ChannelSchema)
          .set(channelUpdates)
          .where(eq(ChannelSchema.id, channelId));
      }

      // Update privacy settings if specified
      if (updates.isPublic !== undefined) {
        // Check if privacy settings exist
        const existingPrivacy = await trx
          .select()
          .from(ChannelPrivacySchema)
          .where(eq(ChannelPrivacySchema.channelId, channelId));

        if (existingPrivacy.length > 0) {
          // Update existing privacy settings
          await trx
            .update(ChannelPrivacySchema)
            .set({ isPublic: updates.isPublic })
            .where(eq(ChannelPrivacySchema.channelId, channelId));
        } else {
          // Create new privacy settings
          await trx.insert(ChannelPrivacySchema).values({
            channelId,
            isPublic: updates.isPublic,
          });
        }

        // If channel is now private and role IDs are provided, update allowed roles
        if (!updates.isPublic && allowedRoleIds && allowedRoleIds.length > 0) {
          // Delete existing allowed roles
          await trx
            .delete(ChannelAllowedRolesSchema)
            .where(eq(ChannelAllowedRolesSchema.channelId, channelId));

          // Add new allowed roles
          const allowedRolesValues = allowedRoleIds.map((roleId) => ({
            channelId,
            roleId,
          }));
          await trx
            .insert(ChannelAllowedRolesSchema)
            .values(allowedRolesValues);
        }
      }

      // Return the updated channel
      const updatedChannel = await trx
        .select()
        .from(ChannelSchema)
        .where(eq(ChannelSchema.id, channelId));

      return updatedChannel[0];
    });
  } catch (error) {
    console.error("Error updating channel:", error);
    return null; // Or throw a custom error
  }
}

/**
 * Deletes a channel and all associated data.
 *
 * @param channelId - The ID of the channel to delete.
 * @returns True if the channel was successfully deleted, false otherwise.
 */
export async function deleteChannel(
  db: ReturnType<typeof drizzle>,
  channelId: string,
): Promise<boolean> {
  try {
    return await db.transaction(async (trx) => {
      // Check if channel exists
      const existingChannel = await trx
        .select()
        .from(ChannelSchema)
        .where(eq(ChannelSchema.id, channelId));

      if (existingChannel.length === 0) {
        return false; // Channel not found
      }

      // Delete channel privacy settings
      await trx
        .delete(ChannelPrivacySchema)
        .where(eq(ChannelPrivacySchema.channelId, channelId));

      // Delete channel allowed roles
      await trx
        .delete(ChannelAllowedRolesSchema)
        .where(eq(ChannelAllowedRolesSchema.channelId, channelId));

      // Delete channel messages and reactions
      // First get all message IDs for this channel
      const messages = await trx
        .select({ id: MessageSchema.id })
        .from(MessageSchema)
        .where(eq(MessageSchema.channelId, channelId));

      const messageIds = messages.map((m) => m.id);

      // Delete message reactions if there are any messages
      if (messageIds.length > 0) {
        await trx
          .delete(MessageReactionSchema)
          .where(inArray(MessageReactionSchema.messageId, messageIds));
      }

      // Delete messages
      await trx
        .delete(MessageSchema)
        .where(eq(MessageSchema.channelId, channelId));

      // Finally, delete the channel itself
      await trx.delete(ChannelSchema).where(eq(ChannelSchema.id, channelId));

      return true;
    });
  } catch (error) {
    console.error("Error deleting channel:", error);
    return false; // Or throw a custom error
  }
}

// Define a type for the channel details, including related data
type Role = typeof ServerRoleSchema.$inferSelect;

interface ChannelDetails {
  id: string;
  name: string;
  description: string | null | undefined;
  serverId: string;
  type: (typeof channelTypeEnum.enumValues)[number];
  createdAt: Date;
  privacySettings: {
    isPublic: boolean;
  } | null;
  allowedRoles: {
    role: Role;
  }[];
}

/**
 * Adds a user to a server (creates a server membership).
 */
export async function addUserToServer(
  db: ReturnType<typeof drizzle>,
  userId: string,
  serverId: string,
): Promise<void> {
  console.log("addUserToServer called with:", { userId, serverId });

  try {
    console.log("Inserting server membership...");
    await db.insert(ServerMembershipSchema).values({
      userId,
      serverId,
    });
    console.log("Server membership created successfully");
  } catch (error: any) {
    // Type assertion to 'any' to access properties
    // Handle potential errors, such as unique constraint violations
    if (error.code === "23505") {
      console.log(
        "User is already a member of this server (constraint violation)",
      );
      // 23505 is the error code for unique violation in PostgreSQL
      throw new Error("User is already a member of this server");
    }
    console.error("Error adding user to server:", error);
    if (error.code) {
      console.error("Database error code:", error.code);
    }
    if (error.detail) {
      console.error("Error detail:", error.detail);
    }
    throw new Error(
      "Failed to add user to server: " + (error.message || "Unknown error"),
    );
  }
}

export async function getServerMembers(
  db: ReturnType<typeof drizzle>,
  serverId: string,
): Promise<any[]> {
  // Replace any with a more specific type
  try {
    // Get the server with members
    const serverWithMembers = await db.query.ServerSchema.findFirst({
      where: eq(ServerSchema.id, serverId),
      with: {
        serverMemberships: {
          with: {
            user: true,
          },
        },
      },
    });

    if (!serverWithMembers) {
      return []; // Or throw an error: throw new Error('Server not found');
    }

    // Get all roles for the server
    const serverRoles = await db
      .select()
      .from(ServerRoleSchema)
      .where(eq(ServerRoleSchema.serverId, serverId));

    // Create a map of role IDs to role objects for quick lookup
    const roleMap = new Map();
    serverRoles.forEach((role) => roleMap.set(role.id, role));

    // Process each member to add roles and owner status
    const membersWithDetails = await Promise.all(
      serverWithMembers.serverMemberships.map(async (membership) => {
        // Get user's roles
        const userRoleIds = await db
          .select({ roleId: UserRoles.roleId })
          .from(UserRoles)
          .where(
            and(
              eq(UserRoles.userId, membership.user.id),
              eq(UserRoles.serverId, serverId),
            ),
          );

        // Map role IDs to actual role objects
        const roles = userRoleIds
          .map(({ roleId }) => roleMap.get(roleId))
          .filter((role) => role !== undefined); // Filter out any undefined roles

        // Return member with roles and owner status
        return {
          ...membership.user,
          isOwner: serverWithMembers.ownerId === membership.user.id,
          roles: roles,
          joinedAt: membership.createdAt,
        };
      }),
    );

    return membersWithDetails;
  } catch (error) {
    console.error("Error getting server members with roles:", error);
    return [];
  }
}

export async function getUserServers(
  db: ReturnType<typeof drizzle>,
  userId: string,
): Promise<any[]> {
  // Replace any with a more specific type if possible
  const userWithServers = (await db.query.UserSchema.findFirst({
    where: eq(UserSchema.id, userId), // Make sure you are using the correct schema
    with: {
      serverMemberships: {
        with: {
          server: true,
        },
      },
    },
  })) as UserWithServers | undefined;

  if (!userWithServers) {
    return []; // Or handle the case where the user is not found appropriately
  }

  return userWithServers.serverMemberships.map(
    (membership) => membership.server,
  );
}

export async function isUserInServer(
  db: ReturnType<typeof drizzle>,
  userId: number,
  serverId: number,
): Promise<boolean> {
  const membership = await db
    .select()
    .from(ServerMembershipSchema)
    .where(
      and(
        eq(ServerMembershipSchema.userId, userId),
        eq(ServerMembershipSchema.serverId, serverId),
      ),
    )
    .limit(1);

  return membership.length > 0;
}

export async function getServersByOwner(
  db: ReturnType<typeof drizzle>,
  ownerId: number,
): Promise<any[]> {
  // Replace any with a more specific type
  const servers = await db.query.servers.findMany({
    where: eq(ServerSchema.ownerId, ownerId),
  });

  return servers;
}

export async function getUserData(
  db: ReturnType<typeof drizzle>,
  userId: string,
): Promise<any> {
  // Use select instead of query for better type safety
  const user = await db
    .select()
    .from(UserSchema)
    .where(eq(UserSchema.id, userId))
    .limit(1);

  if (user.length === 0) {
    return null;
  }

  // Get server memberships separately
  const memberships = await db
    .select({
      membership: ServerMembershipSchema,
      server: ServerSchema,
    })
    .from(ServerMembershipSchema)
    .innerJoin(
      ServerSchema,
      eq(ServerMembershipSchema.serverId, ServerSchema.id),
    )
    .where(eq(ServerMembershipSchema.userId, userId));

  return {
    ...user[0],
    password: undefined, // Don't return the password
    serverMemberships: memberships,
  };
}

/**
 * Verify a user's password
 */
export async function verifyUserPassword(
  db: ReturnType<typeof drizzle>,
  userId: string,
  password: string,
): Promise<boolean> {
  const user = await db
    .select({ password: UserSchema.password })
    .from(UserSchema)
    .where(eq(UserSchema.id, userId))
    .limit(1);

  if (user.length === 0) {
    return false;
  }

  return await argon2.verify(user[0].password, password);
}

/**
 * Update a user's profile
 */
export async function updateUserProfile(
  db: ReturnType<typeof drizzle>,
  userId: string,
  updates: {
    username?: string;
    email?: string;
    avatar?: string;
    password?: string;
  },
): Promise<Partial<User>> {
  // Sanitize inputs
  const sanitizedUpdates: Record<string, any> = {};

  if (updates.username) {
    sanitizedUpdates.username = sanitizeTextInput(updates.username);

    // Check if username is already taken
    const existingUser = await db
      .select()
      .from(UserSchema)
      .where(
        and(
          eq(UserSchema.username, sanitizedUpdates.username),
          not(eq(UserSchema.id, userId)),
        ),
      )
      .limit(1);

    if (existingUser.length > 0) {
      throw new Error("Username already taken");
    }
  }

  if (updates.email) {
    const sanitizedEmail = validator.normalizeEmail(updates.email) || "";
    if (!sanitizedEmail) {
      throw new Error("Invalid email format");
    }
    sanitizedUpdates.email = sanitizedEmail;

    // Check if email is already taken
    const existingUser = await db
      .select()
      .from(UserSchema)
      .where(
        and(
          eq(UserSchema.email, sanitizedEmail),
          not(eq(UserSchema.id, userId)),
        ),
      )
      .limit(1);

    if (existingUser.length > 0) {
      throw new Error("Email already taken");
    }
  }

  if (updates.avatar) {
    sanitizedUpdates.avatar = sanitizeTextInput(updates.avatar);
  }

  if (updates.password) {
    sanitizedUpdates.password = updates.password; // Password should already be hashed
  }

  // Add updatedAt timestamp
  sanitizedUpdates.updatedAt = new Date();

  // Update the user
  const updatedUser = await db
    .update(UserSchema)
    .set(sanitizedUpdates)
    .where(eq(UserSchema.id, userId))
    .returning({
      id: UserSchema.id,
      username: UserSchema.username,
      email: UserSchema.email,
      avatar: UserSchema.avatar,
      updatedAt: UserSchema.updatedAt,
      createdAt: UserSchema.createdAt,
    });

  if (updatedUser.length === 0) {
    throw new Error("User not found");
  }

  return updatedUser[0];
}

/**
 * Gets comprehensive details about a server, including members with roles, server roles, channels, and categories.
 *
 * @param db - The database connection.
 * @param serverId - The ID of the server to get details for.
 * @param includeInvites - Whether to include server invites in the response (default: false).
 * @returns A ServerDetails object with all server information, or null if the server is not found or an error occurred.
 */
export async function getServerDetails(
  db: ReturnType<typeof drizzle>,
  serverId: string,
  includeInvites: boolean = false,
): Promise<ServerDetails | null> {
  try {
    // Get basic server information
    const server = await db.query.ServerSchema.findFirst({
      where: eq(ServerSchema.id, serverId),
      with: {
        channels: true,
        categories: true,
      },
    });

    if (!server) {
      return null; // Server not found
    }

    // Get server owner details
    const owner = await db.query.UserSchema.findFirst({
      where: eq(UserSchema.id, server.ownerId),
    });

    if (!owner) {
      console.error(
        `Server owner (${server.ownerId}) not found for server ${serverId}`,
      );
      return null;
    }

    // Get server roles
    const roles = await db
      .select()
      .from(ServerRoleSchema)
      .where(eq(ServerRoleSchema.serverId, serverId));

    // Get server members with roles
    const members = await getServerMembers(db, serverId);

    // Get server invites if requested
    let invites = [];
    if (includeInvites) {
      invites = await db
        .select()
        .from(ServerInviteSchema)
        .where(eq(ServerInviteSchema.serverId, serverId));
    }

    // Return comprehensive server details
    return {
      server,
      owner,
      members,
      roles,
      channels: server.channels,
      categories: server.categories,
      ...(includeInvites && { invites }),
    };
  } catch (error) {
    console.error("Error fetching server details:", error);
    return null;
  }
}

/**
 * Creates a new server invite.
 */
export async function createServerInvite(
  db: ReturnType<typeof drizzle>,
  serverId: string,
  createdById: string,
  expiresAt?: Date,
  maxUses?: number,
) {
  const code = generateUniqueInviteCode();

  const newInvite = await db
    .insert(ServerInviteSchema)
    .values({
      code,
      serverId,
      createdById,
      expiresAt,
      maxUses,
    })
    .returning();

  return newInvite[0];
}

/**
 * Retrieves a server invite by its code.
 */
export async function getServerInviteByCode(
  db: ReturnType<typeof drizzle>,
  code: string,
) {
  return db.query.ServerInviteSchema.findFirst({
    where: eq(ServerInviteSchema.code, code),
  });
}

/**
 * Increments the uses count of a server invite.
 */
export async function incrementInviteUses(
  db: ReturnType<typeof drizzle>,
  inviteId: string,
) {
  return db
    .update(ServerInviteSchema)
    .set({ uses: sql`${ServerInviteSchema.uses} + 1` })
    .where(eq(ServerInviteSchema.id, inviteId));
}

/**
 * Checks if a user is already a member of a server.
 */
export async function isUserServerMember(
  db: ReturnType<typeof drizzle>,
  userId: string,
  serverId: string,
) {
  const existingMembership = await db.query.ServerMembershipSchema.findFirst({
    where: and(
      eq(ServerMembershipSchema.serverId, serverId),
      eq(ServerMembershipSchema.userId, userId),
    ),
  });

  return !!existingMembership;
}

/**
 * Retrieves a server by its ID, including related data.
 */
export async function getServerById(
  db: ReturnType<typeof drizzle>,
  serverId: string,
) {
  return db.query.ServerSchema.findFirst({
    where: eq(ServerSchema.id, serverId),
    with: {
      owner: true,
      serverMemberships: {
        with: {
          user: true,
        },
      },
      roles: true,
      channels: true,
      invites: true,
    },
  });
}

/**
 * Updates server details
 *
 * @param db - The database connection
 * @param serverId - The ID of the server to update
 * @param updates - Object containing the properties to update
 * @param userId - Optional: ID of the user making the update (for permission checking)
 * @param checkPermission - Optional: Whether to check if the user has permission (default: true)
 * @returns The updated server object
 */
export async function updateServerDetails(
  db: ReturnType<typeof drizzle>,
  serverId: string,
  updates: {
    name?: string;
    description?: string;
    icon?: string;
  },
  userId?: string, // Optional: to verify the user has permission
  checkPermission: boolean = true,
): Promise<Partial<Server>> {
  // Sanitize inputs
  const sanitizedUpdates: Record<string, any> = {};

  // Process text fields with sanitization
  if (updates.name) {
    sanitizedUpdates.name = sanitizeTextInput(updates.name);
  }

  if (updates.description) {
    sanitizedUpdates.description = sanitizeTextInput(updates.description);
  }

  sanitizedUpdates.icon = updates.icon;

  // If userId is provided and permission check is enabled, verify the user has permission
  if (userId && checkPermission) {
    // First check if the server exists
    const server = await db
      .select({ ownerId: ServerSchema.ownerId })
      .from(ServerSchema)
      .where(eq(ServerSchema.id, serverId))
      .limit(1);

    console.log("Server found:", server);

    if (server.length === 0) {
      throw new Error("Server not found");
    }

    // If user is the owner, they have permission
    if (server[0].ownerId !== userId) {
      // If not the owner, check if they have MANAGE_SERVER permission
      const { hasServerPermission } = await import("../utils/permissions");
      const { MANAGE_SERVER } = await import("../constants/permissions");

      const hasPermission = await hasServerPermission(
        db,
        userId,
        serverId,
        MANAGE_SERVER,
      );

      if (!hasPermission) {
        throw new Error(
          "Unauthorized: You do not have permission to update server details",
        );
      }
    }
  }

  // Update the server
  const updatedServer = await db
    .update(ServerSchema)
    .set(sanitizedUpdates)
    .where(eq(ServerSchema.id, serverId))
    .returning({
      id: ServerSchema.id,
      name: ServerSchema.name,
      description: ServerSchema.description,
      icon: ServerSchema.icon,
      ownerId: ServerSchema.ownerId,
      createdAt: ServerSchema.createdAt,
    });

  if (updatedServer.length === 0) {
    throw new Error("Server not found");
  }

  return updatedServer[0];
}

/**
 * Retrieves a user by its ID.
 */
export async function getUserById(
  db: ReturnType<typeof drizzle>,
  userId: string,
) {
  return db.query.UserSchema.findFirst({
    where: eq(UserSchema.id, userId),
  });
}

/**
 * Notifies users about a server join event.
 */
// export async function notifyServerJoin(userId: string, serverId: string) {
//   const user = await getUserById(userId);
//   if (!user) return;

//   const server = await getServerById(serverId);
//   if (!server) return;

//   wsManager.broadcast(
//     {
//       type: 'server_join',
//       data: { user, server },
//     },
//     WebSocketType.GLOBAL,
//   );
// }

export async function createNewMessage(
  db: ReturnType<typeof drizzle>,
  userId: string,
  channelId: string,
  content: string,
  type?: string,
  attachments?: string,
) {
  const newMessage = await db
    .insert(MessageSchema)
    .values({
      content,
      userId,
      channelId,
      type,
      attachments,
    })
    .returning();

  return newMessage[0];
}

export async function editExistingMessage(
  db: ReturnType<typeof drizzle>,
  messageId: string,
  newContent: string,
) {
  const result = await db
    .update(MessageSchema)
    .set({ content: newContent, editedAt: sql`now()` })
    .where(eq(MessageSchema.id, messageId))
    .returning({
      id: MessageSchema.id,
      content: MessageSchema.content,
      userId: MessageSchema.userId,
      channelId: MessageSchema.channelId,
      editedAt: MessageSchema.editedAt,
      createdAt: MessageSchema.createdAt,
    });

  if (result.length === 0) {
    throw new Error("Message not found");
  }

  return result[0];
}

/**
 * Delete a message by its ID
 */
export async function deleteMessage(
  db: ReturnType<typeof drizzle>,
  messageId: string,
) {
  const result = await db
    .delete(MessageSchema)
    .where(eq(MessageSchema.id, messageId))
    .returning({
      id: MessageSchema.id,
      channelId: MessageSchema.channelId,
      userId: MessageSchema.userId,
    });

  if (result.length === 0) {
    throw new Error("Message not found");
  }

  return result[0];
}

/**
 * Get a message by its ID
 */
export async function getMessageById(
  db: ReturnType<typeof drizzle>,
  messageId: string,
) {
  const result = await db
    .select()
    .from(MessageSchema)
    .where(eq(MessageSchema.id, messageId))
    .limit(1);

  if (result.length === 0) {
    return null;
  }

  return result[0];
}

/**
 * Retrieve the last N messages from a channel
 *
 * @param db Database connection
 * @param channelId Channel ID to retrieve messages from
 * @param n Number of messages to retrieve (default: 50)
 * @returns Array of messages with author information
 */
export async function retrieveLastNMessages(
  db: ReturnType<typeof drizzle>,
  channelId: string,
  n: number = 50,
) {
  console.log(
    `retrieveLastNMessages called for channelId: ${channelId}, limit: ${n}`,
  );

  if (!channelId) {
    console.error("Cannot retrieve messages: channelId is undefined or null");
    return [];
  }

  try {
    // Query messages with author information
    const messages = await db
      .select({
        message: MessageSchema, // Select all message fields
        author: {
          // Create an 'author' object in the result
          username: UserSchema.username, // Select username from UserSchema
          avatar: UserSchema.avatar, // Select avatar from UserSchema
          userId: UserSchema.id, // Select userId from UserSchema
        },
      })
      .from(MessageSchema)
      .innerJoin(UserSchema, eq(MessageSchema.userId, UserSchema.id)) // Join MessageSchema with UserSchema on userId
      .orderBy(desc(MessageSchema.createdAt)) // Order by message creation time (newest first)
      .limit(n)
      .where(eq(MessageSchema.channelId, channelId));

    console.log(
      `Retrieved ${messages.length} messages for channel ${channelId}`,
    );

    // Return messages in reverse order (oldest first) to match frontend expectations
    return messages.reverse();
  } catch (error) {
    console.error("Error retrieving messages:", error);
    return [];
  }
}

/**
 * Create a new direct message
 */
export async function createDirectMessage(
  db: ReturnType<typeof drizzle>,
  senderId: string,
  receiverId: string,
  content: string,
  attachments?: string,
) {
  const newMessage = await db
    .insert(DirectMessageSchema)
    .values({
      senderId,
      receiverId,
      content,
      attachments,
    })
    .returning();

  return newMessage[0];
}

/**
 * Edit an existing direct message
 */
export async function editDirectMessage(
  db: ReturnType<typeof drizzle>,
  messageId: string,
  newContent: string,
) {
  const result = await db
    .update(DirectMessageSchema)
    .set({ content: newContent, editedAt: sql`now()` })
    .where(eq(DirectMessageSchema.id, messageId))
    .returning({
      id: DirectMessageSchema.id,
      content: DirectMessageSchema.content,
      senderId: DirectMessageSchema.senderId,
      receiverId: DirectMessageSchema.receiverId,
      editedAt: DirectMessageSchema.editedAt,
      createdAt: DirectMessageSchema.createdAt,
    });

  if (result.length === 0) {
    throw new Error("Direct message not found");
  }

  return result[0];
}

/**
 * Delete a direct message
 */
export async function deleteDirectMessage(
  db: ReturnType<typeof drizzle>,
  messageId: string,
) {
  const result = await db
    .delete(DirectMessageSchema)
    .where(eq(DirectMessageSchema.id, messageId))
    .returning({
      id: DirectMessageSchema.id,
      senderId: DirectMessageSchema.senderId,
      receiverId: DirectMessageSchema.receiverId,
    });

  if (result.length === 0) {
    throw new Error("Direct message not found");
  }

  return result[0];
}

/**
 * Get a direct message by ID
 */
export async function getDirectMessageById(
  db: ReturnType<typeof drizzle>,
  messageId: string,
) {
  const result = await db
    .select()
    .from(DirectMessageSchema)
    .where(eq(DirectMessageSchema.id, messageId))
    .limit(1);

  if (result.length === 0) {
    return null;
  }

  return result[0];
}

/**
 * Get direct messages between two users
 */
export async function getDirectMessagesBetweenUsers(
  db: ReturnType<typeof drizzle>,
  userId1: string,
  userId2: string,
  limit: number = 50,
) {
  return await db
    .select({
      message: DirectMessageSchema,
      sender: {
        id: UserSchema.id,
        username: UserSchema.username,
        avatar: UserSchema.avatar,
      },
    })
    .from(DirectMessageSchema)
    .innerJoin(UserSchema, eq(DirectMessageSchema.senderId, UserSchema.id))
    .where(
      or(
        and(
          eq(DirectMessageSchema.senderId, userId1),
          eq(DirectMessageSchema.receiverId, userId2),
        ),
        and(
          eq(DirectMessageSchema.senderId, userId2),
          eq(DirectMessageSchema.receiverId, userId1),
        ),
      ),
    )
    .orderBy(desc(DirectMessageSchema.createdAt))
    .limit(limit);
}

/**
 * Mark a direct message as read
 */
export async function markDirectMessageAsRead(
  db: ReturnType<typeof drizzle>,
  messageId: string,
) {
  return await db
    .update(DirectMessageSchema)
    .set({ readAt: sql`now()` })
    .where(eq(DirectMessageSchema.id, messageId));
}

/**
 * Create new direct message conversation
 */
export async function createDirectMessageConversation(
  db: ReturnType<typeof drizzle>,
  senderId: string,
  receiverId: string,
  content: string,
  attachments?: string,
) {
  const newMessage = await db
    .insert(DirectMessageSchema)
    .values({
      senderId,
      receiverId,
      content,
      attachments,
    })
    .returning();

  return newMessage[0];
}

/**
 * Get all users that have direct message conversations with a user
 */
export async function getUserDirectMessageContacts(
  db: ReturnType<typeof drizzle>,
  userId: string,
) {
  // Get unique users that the current user has exchanged messages with
  const sentToUsers = await db
    .selectDistinct({ contactId: DirectMessageSchema.receiverId })
    .from(DirectMessageSchema)
    .where(eq(DirectMessageSchema.senderId, userId));

  const receivedFromUsers = await db
    .selectDistinct({ contactId: DirectMessageSchema.senderId })
    .from(DirectMessageSchema)
    .where(eq(DirectMessageSchema.receiverId, userId));

  // Combine the unique user IDs
  const contactIds = new Set<string>();
  sentToUsers.forEach((user) => contactIds.add(user.contactId));
  receivedFromUsers.forEach((user) => contactIds.add(user.contactId));

  // Get user details for all contacts
  const contacts = [];
  for (const contactId of contactIds) {
    const user = await db
      .select({
        id: UserSchema.id,
        username: UserSchema.username,
        avatar: UserSchema.avatar,
      })
      .from(UserSchema)
      .where(eq(UserSchema.id, contactId))
      .limit(1);

    if (user.length > 0) {
      // Get the latest message between the users
      const latestMessage = await db
        .select()
        .from(DirectMessageSchema)
        .where(
          or(
            and(
              eq(DirectMessageSchema.senderId, userId),
              eq(DirectMessageSchema.receiverId, contactId),
            ),
            and(
              eq(DirectMessageSchema.senderId, contactId),
              eq(DirectMessageSchema.receiverId, userId),
            ),
          ),
        )
        .orderBy(desc(DirectMessageSchema.createdAt))
        .limit(1);

      contacts.push({
        ...user[0],
        latestMessage: latestMessage.length > 0 ? latestMessage[0] : null,
      });
    }
  }

  return contacts;
}

/**
 * Add a reaction to a message
 */
export async function addMessageReaction(
  db: ReturnType<typeof drizzle>,
  messageId: string,
  userId: string,
  emoji: string,
) {
  // Check if the reaction already exists
  const existingReaction = await db
    .select()
    .from(MessageReactionSchema)
    .where(
      and(
        eq(MessageReactionSchema.messageId, messageId),
        eq(MessageReactionSchema.userId, userId),
        eq(MessageReactionSchema.emoji, emoji),
      ),
    )
    .limit(1);

  // If the reaction already exists, return it
  if (existingReaction.length > 0) {
    return existingReaction[0];
  }

  // Otherwise, create a new reaction
  const newReaction = await db
    .insert(MessageReactionSchema)
    .values({
      messageId,
      userId,
      emoji,
    })
    .returning();

  return newReaction[0];
}

/**
 * Remove a reaction from a message
 */
export async function removeMessageReaction(
  db: ReturnType<typeof drizzle>,
  messageId: string,
  userId: string,
  emoji: string,
) {
  const result = await db
    .delete(MessageReactionSchema)
    .where(
      and(
        eq(MessageReactionSchema.messageId, messageId),
        eq(MessageReactionSchema.userId, userId),
        eq(MessageReactionSchema.emoji, emoji),
      ),
    )
    .returning();

  if (result.length === 0) {
    throw new Error("Reaction not found");
  }

  return result[0];
}

/**
 * Get all reactions for a message
 */
export async function getMessageReactions(
  db: ReturnType<typeof drizzle>,
  messageId: string,
) {
  const reactions = await db
    .select({
      reaction: MessageReactionSchema,
      user: {
        id: UserSchema.id,
        username: UserSchema.username,
        avatar: UserSchema.avatar,
      },
    })
    .from(MessageReactionSchema)
    .innerJoin(UserSchema, eq(MessageReactionSchema.userId, UserSchema.id))
    .where(eq(MessageReactionSchema.messageId, messageId));

  return reactions;
}

/**
 * Get reaction counts grouped by emoji for a message
 */
export async function getMessageReactionCounts(
  db: ReturnType<typeof drizzle>,
  messageId: string,
) {
  const reactions = await db
    .select({
      emoji: MessageReactionSchema.emoji,
      count: sql<number>`count(${MessageReactionSchema.id})`,
    })
    .from(MessageReactionSchema)
    .where(eq(MessageReactionSchema.messageId, messageId))
    .groupBy(MessageReactionSchema.emoji);

  return reactions;
}

/**
 * Check if a user has reacted to a message with a specific emoji
 */
export async function hasUserReacted(
  db: ReturnType<typeof drizzle>,
  messageId: string,
  userId: string,
  emoji: string,
) {
  const reaction = await db
    .select()
    .from(MessageReactionSchema)
    .where(
      and(
        eq(MessageReactionSchema.messageId, messageId),
        eq(MessageReactionSchema.userId, userId),
        eq(MessageReactionSchema.emoji, emoji),
      ),
    )
    .limit(1);

  return reaction.length > 0;
}

/**
 * Generate a password reset token for a user
 */
export async function generatePasswordResetToken(
  db: ReturnType<typeof drizzle>,
  email: string,
) {
  // Find the user by email
  const user = await db
    .select()
    .from(UserSchema)
    .where(eq(UserSchema.email, email))
    .limit(1);

  if (user.length === 0) {
    throw new Error("User not found");
  }

  // Generate a random token
  const resetToken = crypto.randomUUID();

  // Set token expiry to 1 hour from now
  const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour in milliseconds

  // Update the user with the reset token and expiry
  await db
    .update(UserSchema)
    .set({
      resetToken,
      resetTokenExpiry,
    })
    .where(eq(UserSchema.id, user[0].id));

  return {
    userId: user[0].id,
    email: user[0].email,
    resetToken,
  };
}

/**
 * Verify a password reset token
 */
export async function verifyPasswordResetToken(
  db: ReturnType<typeof drizzle>,
  userId: string,
  token: string,
) {
  // Find the user by ID
  const user = await db
    .select()
    .from(UserSchema)
    .where(eq(UserSchema.id, userId))
    .limit(1);

  if (user.length === 0) {
    throw new Error("User not found");
  }

  // Check if the token exists and is valid
  if (!user[0].resetToken || user[0].resetToken !== token) {
    throw new Error("Invalid reset token");
  }

  // Check if the token has expired
  if (!user[0].resetTokenExpiry || new Date() > user[0].resetTokenExpiry) {
    throw new Error("Reset token has expired");
  }

  return user[0];
}

/**
 * Reset a user's password using a valid reset token
 */
export async function resetPassword(
  db: ReturnType<typeof drizzle>,
  userId: string,
  token: string,
  newPassword: string,
) {
  // Verify the token first
  const user = await verifyPasswordResetToken(db, userId, token);

  // Hash the new password
  const hashedPassword = await argon2.hash(newPassword);

  // Update the user's password and clear the reset token
  await db
    .update(UserSchema)
    .set({
      password: hashedPassword,
      resetToken: null,
      resetTokenExpiry: null,
    })
    .where(eq(UserSchema.id, userId));

  return {
    id: user.id,
    username: user.username,
    email: user.email,
  };
}

/**
 * Generate an email verification token for a new user
 */
export async function generateEmailVerificationToken(
  db: ReturnType<typeof drizzle>,
  userId: string,
) {
  // Find the user by ID
  const user = await db
    .select()
    .from(UserSchema)
    .where(eq(UserSchema.id, userId))
    .limit(1);

  if (user.length === 0) {
    throw new Error("User not found");
  }

  // Generate a random token
  const verificationToken = crypto.randomUUID();

  // Set token expiry to 24 hours from now
  const verificationTokenExpiry = new Date(Date.now() + 86400000); // 24 hours in milliseconds

  // Update the user with the verification token and expiry
  await db
    .update(UserSchema)
    .set({
      emailVerificationToken: verificationToken,
      emailVerificationExpiry: verificationTokenExpiry,
    })
    .where(eq(UserSchema.id, userId));

  return {
    userId: user[0].id,
    email: user[0].email,
    verificationToken,
  };
}

/**
 * Verify an email verification token
 */
export async function verifyEmailToken(
  db: ReturnType<typeof drizzle>,
  userId: string,
  token: string,
) {
  // Find the user by ID
  const user = await db
    .select()
    .from(UserSchema)
    .where(eq(UserSchema.id, userId))
    .limit(1);

  if (user.length === 0) {
    throw new Error("User not found");
  }

  // Check if the token exists and is valid
  if (
    !user[0].emailVerificationToken ||
    user[0].emailVerificationToken !== token
  ) {
    throw new Error("Invalid verification token");
  }

  // Check if the token has expired
  if (
    !user[0].emailVerificationExpiry ||
    new Date() > user[0].emailVerificationExpiry
  ) {
    throw new Error("Verification token has expired");
  }

  // Mark the email as verified and clear the token
  await db
    .update(UserSchema)
    .set({
      isEmailVerified: true,
      emailVerificationToken: null,
      emailVerificationExpiry: null,
    })
    .where(eq(UserSchema.id, userId));

  return {
    id: user[0].id,
    username: user[0].username,
    email: user[0].email,
    isEmailVerified: true,
  };
}

/**
 * Generate a refresh token for a user
 */
export async function generateRefreshToken(
  db: ReturnType<typeof drizzle>,
  userId: string,
) {
  // Find the user by ID
  const user = await db
    .select()
    .from(UserSchema)
    .where(eq(UserSchema.id, userId))
    .limit(1);

  if (user.length === 0) {
    throw new Error("User not found");
  }

  // Generate a random token
  const refreshToken = crypto.randomUUID();

  // Set token expiry to 30 days from now
  const refreshTokenExpiry = new Date(Date.now() + 30 * 86400000); // 30 days in milliseconds

  // Update the user with the refresh token and expiry
  await db
    .update(UserSchema)
    .set({
      refreshToken,
      refreshTokenExpiry,
    })
    .where(eq(UserSchema.id, userId));

  return {
    userId: user[0].id,
    refreshToken,
    refreshTokenExpiry,
  };
}

/**
 * Verify a refresh token and generate a new access token
 */
export async function verifyRefreshToken(
  db: ReturnType<typeof drizzle>,
  userId: string,
  token: string,
) {
  // Find the user by ID
  const user = await db
    .select()
    .from(UserSchema)
    .where(eq(UserSchema.id, userId))
    .limit(1);

  if (user.length === 0) {
    throw new Error("User not found");
  }

  // Check if the token exists and is valid
  if (!user[0].refreshToken || user[0].refreshToken !== token) {
    throw new Error("Invalid refresh token");
  }

  // Check if the token has expired
  if (!user[0].refreshTokenExpiry || new Date() > user[0].refreshTokenExpiry) {
    throw new Error("Refresh token has expired");
  }

  return user[0];
}

/**
 * Invalidate a refresh token
 */
export async function invalidateRefreshToken(
  db: ReturnType<typeof drizzle>,
  userId: string,
) {
  // Update the user to clear the refresh token
  await db
    .update(UserSchema)
    .set({
      refreshToken: null,
      refreshTokenExpiry: null,
    })
    .where(eq(UserSchema.id, userId));

  return true;
}

export async function getUserByUserId(
  db: ReturnType<typeof drizzle>,
  userId: string,
) {
  const user = await db
    .select()
    .from(UserSchema)
    .where(eq(UserSchema.id, userId))
    .limit(1);

  if (user.length === 0) {
    throw new Error("User not found");
  }

  return user[0];
}

export async function getUserByUsername(
  db: ReturnType<typeof drizzle>,
  username: string,
) {
  const user = await db
    .select()
    .from(UserSchema)
    .where(eq(UserSchema.username, username))
    .limit(1);

  if (user.length === 0) {
    throw new Error("User not found");
  }

  return user[0];
}

export async function getUserIdByUsername(
  db: ReturnType<typeof drizzle>,
  username: string,
) {
  const user = await db
    .select({ id: UserSchema.id })
    .from(UserSchema)
    .where(eq(UserSchema.username, username))
    .limit(1);

  if (user.length === 0) {
    throw new Error("User not found");
  }

  return user[0].id;
}

/**
 * Friend Management Utility Functions
 */

/**
 * Send a friend request
 */
export async function sendFriendRequest(
  db: ReturnType<typeof drizzle>,
  userId: string,
  friendId: string,
) {
  // Check if users are the same
  if (userId === friendId) {
    throw new Error("Cannot send friend request to yourself");
  }

  // Check if the friend exists
  const friend = await db
    .select()
    .from(UserSchema)
    .where(eq(UserSchema.id, friendId))
    .limit(1);

  if (friend.length === 0) {
    throw new Error("User not found");
  }

  // Check if a friendship already exists (in either direction)
  const existingFriendship = await db
    .select()
    .from(FriendshipSchema)
    .where(
      or(
        and(
          eq(FriendshipSchema.userId, userId),
          eq(FriendshipSchema.friendId, friendId),
        ),
        and(
          eq(FriendshipSchema.userId, friendId),
          eq(FriendshipSchema.friendId, userId),
        ),
      ),
    );

  if (existingFriendship.length > 0) {
    const friendship = existingFriendship[0];

    // If there's already a PENDING request from the other user, auto-accept it
    if (
      friendship.status === "PENDING" &&
      friendship.userId === friendId &&
      friendship.friendId === userId
    ) {
      return acceptFriendRequest(db, userId, friendship.id);
    }

    // If there's already a friendship or pending request
    if (friendship.status === "ACCEPTED") {
      throw new Error("Already friends");
    } else if (
      friendship.status === "PENDING" &&
      friendship.userId === userId &&
      friendship.friendId === friendId
    ) {
      throw new Error("Friend request already sent");
    } else if (friendship.status === "BLOCKED") {
      throw new Error("Cannot send friend request");
    }
  }

  // Create the friend request
  const newFriendship = await db
    .insert(FriendshipSchema)
    .values({
      userId,
      friendId,
      status: "PENDING",
      initiatedBy: userId,
    })
    .returning();

  return newFriendship[0];
}

/**
 * Accept a friend request
 */
export async function acceptFriendRequest(
  db: ReturnType<typeof drizzle>,
  userId: string,
  friendshipId: string,
) {
  // Get the friendship
  const friendship = await db
    .select()
    .from(FriendshipSchema)
    .where(
      and(
        eq(FriendshipSchema.id, friendshipId),
        or(
          eq(FriendshipSchema.friendId, userId),
          eq(FriendshipSchema.userId, userId),
        ),
        eq(FriendshipSchema.status, "PENDING"),
      ),
    )
    .limit(1);

  if (friendship.length === 0) {
    throw new Error("Friend request not found");
  }

  // Update the friendship status
  const updatedFriendship = await db
    .update(FriendshipSchema)
    .set({
      status: "ACCEPTED",
      initiatedBy: userId,
      updatedAt: new Date(),
    })
    .where(eq(FriendshipSchema.id, friendshipId))
    .returning();

  return updatedFriendship[0];
}

/**
 * Reject a friend request
 */
export async function rejectFriendRequest(
  db: ReturnType<typeof drizzle>,
  userId: string,
  friendshipId: string,
) {
  // Get the friendship
  const friendship = await db
    .select()
    .from(FriendshipSchema)
    .where(
      and(
        eq(FriendshipSchema.id, friendshipId),
        eq(FriendshipSchema.friendId, userId),
        eq(FriendshipSchema.status, "PENDING"),
      ),
    )
    .limit(1);

  if (friendship.length === 0) {
    throw new Error("Friend request not found");
  }

  // Delete the friendship
  const deletedFriendship = await db
    .delete(FriendshipSchema)
    .where(eq(FriendshipSchema.id, friendshipId))
    .returning();

  return deletedFriendship[0];
}

/**
 * Cancel a friend request
 */
export async function cancelFriendRequest(
  db: ReturnType<typeof drizzle>,
  userId: string,
  friendshipId: string,
) {
  // Get the friendship
  const friendship = await db
    .select()
    .from(FriendshipSchema)
    .where(
      and(
        eq(FriendshipSchema.id, friendshipId),
        eq(FriendshipSchema.userId, userId),
        eq(FriendshipSchema.status, "PENDING"),
      ),
    )
    .limit(1);

  if (friendship.length === 0) {
    throw new Error("Friend request not found");
  }

  // Delete the friendship
  const deletedFriendship = await db
    .delete(FriendshipSchema)
    .where(eq(FriendshipSchema.id, friendshipId))
    .returning();

  return deletedFriendship[0];
}

/**
 * Remove a friend
 */
export async function removeFriend(
  db: ReturnType<typeof drizzle>,
  userId: string,
  friendshipId: string,
) {
  // Get the friendship
  const friendship = await db
    .select()
    .from(FriendshipSchema)
    .where(
      and(
        eq(FriendshipSchema.id, friendshipId),
        or(
          eq(FriendshipSchema.userId, userId),
          eq(FriendshipSchema.friendId, userId),
        ),
        eq(FriendshipSchema.status, "ACCEPTED"),
      ),
    )
    .limit(1);

  if (friendship.length === 0) {
    throw new Error("Friendship not found");
  }

  // Delete the friendship
  const deletedFriendship = await db
    .delete(FriendshipSchema)
    .where(eq(FriendshipSchema.id, friendshipId))
    .returning();

  return deletedFriendship[0];
}

/**
 * Block a user
 */
export async function blockUser(
  db: ReturnType<typeof drizzle>,
  userId: string,
  targetUserId: string,
) {
  // Check if users are the same
  if (userId === targetUserId) {
    throw new Error("Cannot block yourself");
  }

  // Check if the target user exists
  const targetUser = await db
    .select()
    .from(UserSchema)
    .where(eq(UserSchema.id, targetUserId))
    .limit(1);

  if (targetUser.length === 0) {
    throw new Error("User not found");
  }

  // Check if a friendship already exists (in either direction)
  const existingFriendship = await db
    .select()
    .from(FriendshipSchema)
    .where(
      or(
        and(
          eq(FriendshipSchema.userId, userId),
          eq(FriendshipSchema.friendId, targetUserId),
        ),
        and(
          eq(FriendshipSchema.userId, targetUserId),
          eq(FriendshipSchema.friendId, userId),
        ),
      ),
    );

  if (existingFriendship.length > 0) {
    const friendship = existingFriendship[0];

    // If already blocked by this user, just return it
    if (
      friendship.status === "BLOCKED" &&
      ((friendship.userId === userId && friendship.friendId === targetUserId) ||
        (friendship.userId === targetUserId &&
          friendship.friendId === userId)) &&
      friendship.initiatedBy === userId
    ) {
      return friendship;
    }

    // Update the existing friendship to blocked
    const updatedFriendship = await db
      .update(FriendshipSchema)
      .set({
        status: "BLOCKED",
        initiatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(FriendshipSchema.id, friendship.id))
      .returning();

    return updatedFriendship[0];
  }

  // Create a new blocked relationship
  const newFriendship = await db
    .insert(FriendshipSchema)
    .values({
      userId,
      friendId: targetUserId,
      status: "BLOCKED",
      initiatedBy: userId,
    })
    .returning();

  return newFriendship[0];
}

/**
 * Unblock a user
 */
export async function unblockUser(
  db: ReturnType<typeof drizzle>,
  userId: string,
  friendshipId: string,
) {
  // Get the friendship
  const friendship = await db
    .select()
    .from(FriendshipSchema)
    .where(
      and(
        eq(FriendshipSchema.id, friendshipId),
        or(
          eq(FriendshipSchema.userId, userId),
          eq(FriendshipSchema.friendId, userId),
        ),
        eq(FriendshipSchema.status, "BLOCKED"),
        eq(FriendshipSchema.initiatedBy, userId),
      ),
    )
    .limit(1);

  if (friendship.length === 0) {
    throw new Error("Blocked relationship not found");
  }

  // Delete the blocked relationship
  const deletedFriendship = await db
    .delete(FriendshipSchema)
    .where(eq(FriendshipSchema.id, friendshipId))
    .returning();

  return deletedFriendship[0];
}

/**
 * Get a user's friends
 */
export async function getUserFriends(
  db: ReturnType<typeof drizzle>,
  userId: string,
) {
  // Get all accepted friendships where the user is either the requester or the recipient
  const friendships = await db
    .select({
      friendship: FriendshipSchema,
      friend: {
        id: UserSchema.id,
        username: UserSchema.username,
        avatar: UserSchema.avatar,
      },
    })
    .from(FriendshipSchema)
    .where(
      and(
        or(
          eq(FriendshipSchema.userId, userId),
          eq(FriendshipSchema.friendId, userId),
        ),
        eq(FriendshipSchema.status, "ACCEPTED"),
      ),
    )
    .innerJoin(
      UserSchema,
      or(
        and(
          eq(FriendshipSchema.userId, userId),
          eq(FriendshipSchema.friendId, UserSchema.id),
        ),
        and(
          eq(FriendshipSchema.friendId, userId),
          eq(FriendshipSchema.userId, UserSchema.id),
        ),
      ),
    );

  return friendships;
}

/**
 * Get a user's pending friend requests (received)
 */
export async function getPendingFriendRequests(
  db: ReturnType<typeof drizzle>,
  userId: string,
) {
  // Get all pending friendships where the user is the recipient
  const friendRequests = await db
    .select({
      friendship: FriendshipSchema,
      requester: {
        id: UserSchema.id,
        username: UserSchema.username,
        avatar: UserSchema.avatar,
      },
    })
    .from(FriendshipSchema)
    .where(
      and(
        eq(FriendshipSchema.friendId, userId),
        eq(FriendshipSchema.status, "PENDING"),
      ),
    )
    .innerJoin(UserSchema, eq(FriendshipSchema.userId, UserSchema.id));

  return friendRequests;
}

/**
 * Get a user's sent friend requests
 */
export async function getSentFriendRequests(
  db: ReturnType<typeof drizzle>,
  userId: string,
) {
  // Get all pending friendships where the user is the requester
  const sentRequests = await db
    .select({
      friendship: FriendshipSchema,
      requester: {
        id: UserSchema.id,
        username: UserSchema.username,
        avatar: UserSchema.avatar,
      },
    })
    .from(FriendshipSchema)
    .where(
      and(
        eq(FriendshipSchema.userId, userId),
        eq(FriendshipSchema.status, "PENDING"),
      ),
    )
    .innerJoin(UserSchema, eq(FriendshipSchema.friendId, UserSchema.id));

  return sentRequests;
}

/**
 * Get a user's blocked users
 */
export async function getBlockedUsers(
  db: ReturnType<typeof drizzle>,
  userId: string,
) {
  // Get all blocked relationships where the user is the blocker
  const blockedUsers = await db
    .select({
      friendship: FriendshipSchema,
      blocked: {
        id: UserSchema.id,
        username: UserSchema.username,
        avatar: UserSchema.avatar,
      },
    })
    .from(FriendshipSchema)
    .where(
      and(
        or(
          and(
            eq(FriendshipSchema.userId, userId),
            eq(FriendshipSchema.initiatedBy, userId),
          ),
          and(
            eq(FriendshipSchema.friendId, userId),
            eq(FriendshipSchema.initiatedBy, userId),
          ),
        ),
        eq(FriendshipSchema.status, "BLOCKED"),
      ),
    )
    .innerJoin(
      UserSchema,
      or(
        and(
          eq(FriendshipSchema.userId, userId),
          eq(FriendshipSchema.friendId, UserSchema.id),
        ),
        and(
          eq(FriendshipSchema.friendId, userId),
          eq(FriendshipSchema.userId, UserSchema.id),
        ),
      ),
    );

  return blockedUsers;
}

/**
 * Check if a user is blocked
 */
export async function isUserBlocked(
  db: ReturnType<typeof drizzle>,
  userId: string,
  targetUserId: string,
) {
  // Check if there's a blocked relationship between the users
  const blockedRelationship = await db
    .select()
    .from(FriendshipSchema)
    .where(
      and(
        or(
          and(
            eq(FriendshipSchema.userId, userId),
            eq(FriendshipSchema.friendId, targetUserId),
          ),
          and(
            eq(FriendshipSchema.userId, targetUserId),
            eq(FriendshipSchema.friendId, userId),
          ),
        ),
        eq(FriendshipSchema.status, "BLOCKED"),
      ),
    )
    .limit(1);

  return blockedRelationship.length > 0;
}

/**
 * User Status Management Utility Functions
 */

/**
 * Update a user's status
 */
export async function updateUserStatus(
  db: ReturnType<typeof drizzle>,
  userId: string,
  status: "ONLINE" | "AWAY" | "BUSY" | "INVISIBLE" | "OFFLINE",
  statusMessage?: string,
  updatePreferred: boolean = true,
) {
  console.log(
    `updateUserStatus called with userId: ${userId}, status: ${status}, updatePreferred: ${updatePreferred}`,
  );

  if (!userId) {
    console.error("Cannot update user status: userId is undefined or null");
    throw new Error("userId is required");
  }

  try {
    // Update the user's status
    console.log("Updating user status in database...");

    // Prepare update object
    const updateData: any = {
      status,
      statusMessage: statusMessage || null,
      lastActive: new Date(),
      updatedAt: new Date(),
    };

    // Only update preferredStatus if it's a manual status change and not OFFLINE
    // This prevents overwriting the preferred status when a user disconnects
    if (updatePreferred && status !== "OFFLINE") {
      updateData.preferredStatus = status;
    }

    const updatedUser = await db
      .update(UserSchema)
      .set(updateData)
      .where(eq(UserSchema.id, userId))
      .returning({
        id: UserSchema.id,
        username: UserSchema.username,
        status: UserSchema.status,
        preferredStatus: UserSchema.preferredStatus,
        statusMessage: UserSchema.statusMessage,
        lastActive: UserSchema.lastActive,
        avatar: UserSchema.avatar,
      });

    console.log(`User status update result:`, updatedUser);

    if (!updatedUser || updatedUser.length === 0) {
      console.error(`User not found with ID: ${userId}`);
      throw new Error("User not found");
    }

    console.log(`User ${userId} status updated to ${status} successfully`);
    return updatedUser[0];
  } catch (error) {
    console.error(`Error updating user status:`, error);
    // Return a default user object to prevent undefined errors
    return {
      id: userId,
      username: "Unknown User",
      status: status,
      preferredStatus: status !== "OFFLINE" ? status : "ONLINE",
      statusMessage: null,
      lastActive: new Date(),
      avatar: null,
    };
  }
}

/**
 * Get a user's status
 */
export async function getUserStatus(
  db: ReturnType<typeof drizzle>,
  userId: string,
) {
  // Get the user's status
  const user = await db
    .select({
      id: UserSchema.id,
      username: UserSchema.username,
      status: UserSchema.status,
      statusMessage: UserSchema.statusMessage,
      lastActive: UserSchema.lastActive,
      avatar: UserSchema.avatar,
    })
    .from(UserSchema)
    .where(eq(UserSchema.id, userId))
    .limit(1);

  if (user.length === 0) {
    throw new Error("User not found");
  }

  return user[0];
}

/**
 * Get all friends for a user
 */
export async function getAllFriends(
  db: ReturnType<typeof drizzle>,
  userId: string,
) {
  // Get all accepted friendships where the user is either the requester or the recipient
  const friends = await db
    .select({
      friendship: FriendshipSchema,
      friend: {
        id: UserSchema.id,
        username: UserSchema.username,
        status: UserSchema.status,
        statusMessage: UserSchema.statusMessage,
        lastActive: UserSchema.lastActive,
        avatar: UserSchema.avatar,
      },
    })
    .from(FriendshipSchema)
    .where(
      and(
        or(
          eq(FriendshipSchema.userId, userId),
          eq(FriendshipSchema.friendId, userId),
        ),
        eq(FriendshipSchema.status, "ACCEPTED"),
      ),
    )
    .innerJoin(
      UserSchema,
      or(
        and(
          eq(FriendshipSchema.userId, userId),
          eq(FriendshipSchema.friendId, UserSchema.id),
        ),
        and(
          eq(FriendshipSchema.friendId, userId),
          eq(FriendshipSchema.userId, UserSchema.id),
        ),
      ),
    );

  return friends;
}

/**
 * Get online friends for a user
 */
export async function getOnlineFriends(
  db: ReturnType<typeof drizzle>,
  userId: string,
) {
  console.log(`getOnlineFriends called for userId: ${userId}`);

  if (!userId) {
    console.error("Cannot get online friends: userId is undefined or null");
    return [];
  }

  try {
    // Get all accepted friendships where the user is either the requester or the recipient
    // and the friend is online
    console.log("Querying database for online friends...");
    const onlineFriends = await db
      .select({
        friendship: FriendshipSchema,
        friend: {
          id: UserSchema.id,
          username: UserSchema.username,
          status: UserSchema.status,
          statusMessage: UserSchema.statusMessage,
          lastActive: UserSchema.lastActive,
          avatar: UserSchema.avatar,
        },
      })
      .from(FriendshipSchema)
      .where(
        and(
          or(
            eq(FriendshipSchema.userId, userId),
            eq(FriendshipSchema.friendId, userId),
          ),
          eq(FriendshipSchema.status, "ACCEPTED"),
          not(eq(UserSchema.status, "OFFLINE")),
          not(eq(UserSchema.status, "INVISIBLE")),
        ),
      )
      .innerJoin(
        UserSchema,
        or(
          and(
            eq(FriendshipSchema.userId, userId),
            eq(FriendshipSchema.friendId, UserSchema.id),
          ),
          and(
            eq(FriendshipSchema.friendId, userId),
            eq(FriendshipSchema.userId, UserSchema.id),
          ),
        ),
      );

    console.log(
      `Found ${onlineFriends.length} online friends for user ${userId}`,
    );
    return onlineFriends;
  } catch (error) {
    console.error("Error getting online friends:", error);
    return [];
  }
}

/**
 * Update user's last active timestamp
 */
export async function updateLastActive(
  db: ReturnType<typeof drizzle>,
  userId: string,
) {
  // Update the user's last active timestamp
  const updatedUser = await db
    .update(UserSchema)
    .set({
      lastActive: new Date(),
    })
    .where(eq(UserSchema.id, userId))
    .returning({
      id: UserSchema.id,
      lastActive: UserSchema.lastActive,
    });

  if (updatedUser.length === 0) {
    throw new Error("User not found");
  }

  return updatedUser[0];
}

/**
 * Set users to offline status (for server startup/cleanup)
 */
export async function setAllUsersOffline(db: ReturnType<typeof drizzle>) {
  // Set all users to offline status
  await db.update(UserSchema).set({
    status: "OFFLINE",
    updatedAt: new Date(),
  });

  return true;
}

/**
 * Checks if a user has a specific permission in a server.
 * @param userId - The ID of the user.
 * @param serverId - The ID of the server.
 * @param requiredPermission - The permission bit to check.
 * @returns {Promise<boolean>} - True if the user has the permission, false otherwise.
 */
export const hasPermission = async (
  userId: string,
  serverId: string,
  requiredPermission: bigint,
): Promise<boolean> => {
  // Fetch user's roles in the given server
  const userRoles = await db
    .select({ roleId: UserRoles.roleId })
    .from(UserRoles)
    .where(and(eq(UserRoles.userId, userId), eq(UserRoles.serverId, serverId)));

  let userPermissions = BigInt(0);

  // Combine permissions from all roles
  for (const role of userRoles) {
    const roleId = role.roleId + "";
    const roleData = await db
      .select({ permissions: ServerRoleSchema.permissions })
      .from(ServerRoleSchema)
      .where(eq(ServerRoleSchema.id, roleId));

    if (roleData && roleData[0]) {
      userPermissions |= BigInt(roleData[0].permissions); // Merge permissions
    }
  }

  // Check if the user has the required permission
  return (userPermissions & requiredPermission) === requiredPermission;
};
/**
 * Get all server memberships for a user
 * @param db - Database instance
 * @param userId - The ID of the user
 * @returns Array of server memberships with server details
 */
export async function getUserServerMemberships(
  db: ReturnType<typeof drizzle>,
  userId: string,
): Promise<Array<{ serverId: string; server: Server }>> {
  try {
    const memberships = await db
      .select({
        serverId: ServerMembershipSchema.serverId,
        server: ServerSchema,
      })
      .from(ServerMembershipSchema)
      .innerJoin(
        ServerSchema,
        eq(ServerMembershipSchema.serverId, ServerSchema.id),
      )
      .where(eq(ServerMembershipSchema.userId, userId));

    return memberships;
  } catch (error) {
    console.error("Error getting user server memberships:", error);
    return [];
  }
}

// Re-export badge utilities for convenience
export * from './utils/badge-utils';
export * from './utils/badge-evaluation';