import type { drizzle } from "drizzle-orm/postgres-js";
import { eq, and, or, desc, asc, count, sql, gte, lte, isNull, isNotNull } from "drizzle-orm";
import {
  UserBadgeSchema,
  BadgeTypeSchema,
  BadgeCollectionSchema,
  UserCollectionProgressSchema,
  BadgeNominationSchema,
  UserSchema
} from "../schema";
import type {
  BadgeAuditLogEntry,
  BadgeAuditLogFilters,
  BadgeAdminStats,
  BadgeSystemHealthCheck,
  BadgeCleanupResult,
  UserBadgeSummary
} from "../../types/badge.types";

/**
 * Gets badge assignment audit log with filtering and pagination
 */
export async function getBadgeAuditLog(
  db: ReturnType<typeof drizzle>,
  filters: BadgeAuditLogFilters
): Promise<{
  entries: BadgeAuditLogEntry[];
  total: number;
  hasMore: boolean;
}> {
  try {
    // Build where conditions
    const whereConditions = [];

    if (filters.userId) {
      whereConditions.push(eq(UserBadgeSchema.userId, filters.userId));
    }

    if (filters.badgeTypeId) {
      whereConditions.push(eq(UserBadgeSchema.badgeTypeId, filters.badgeTypeId));
    }

    if (filters.assignedBy) {
      whereConditions.push(eq(UserBadgeSchema.assignedBy, filters.assignedBy));
    }

    if (filters.startDate) {
      whereConditions.push(gte(UserBadgeSchema.assignedAt, new Date(filters.startDate)));
    }

    if (filters.endDate) {
      whereConditions.push(lte(UserBadgeSchema.assignedAt, new Date(filters.endDate)));
    }

    // Get total count
    const totalQuery = db
      .select({ count: count() })
      .from(UserBadgeSchema)
      .leftJoin(BadgeTypeSchema, eq(UserBadgeSchema.badgeTypeId, BadgeTypeSchema.id))
      .leftJoin(UserSchema, eq(UserBadgeSchema.userId, UserSchema.id));

    if (whereConditions.length > 0) {
      totalQuery.where(and(...whereConditions));
    }

    const totalResult = await totalQuery;
    const total = totalResult[0]?.count || 0;

    // Get entries with pagination
    const entriesQuery = db
      .select({
        id: UserBadgeSchema.id,
        userId: UserBadgeSchema.userId,
        username: UserSchema.username,
        badgeTypeId: UserBadgeSchema.badgeTypeId,
        badgeName: BadgeTypeSchema.name,
        assignedBy: UserBadgeSchema.assignedBy,
        assignedAt: UserBadgeSchema.assignedAt,
        action: sql<string>`'assigned'`, // All current records are assignments
        isVisible: UserBadgeSchema.isVisible
      })
      .from(UserBadgeSchema)
      .leftJoin(BadgeTypeSchema, eq(UserBadgeSchema.badgeTypeId, BadgeTypeSchema.id))
      .leftJoin(UserSchema, eq(UserBadgeSchema.userId, UserSchema.id))
      .orderBy(desc(UserBadgeSchema.assignedAt))
      .limit(filters.limit)
      .offset(filters.offset);

    if (whereConditions.length > 0) {
      entriesQuery.where(and(...whereConditions));
    }

    const entries = await entriesQuery;

    // Transform to audit log entries
    const auditEntries: BadgeAuditLogEntry[] = entries.map(entry => ({
      id: entry.id,
      userId: entry.userId,
      username: entry.username || 'Unknown User',
      badgeTypeId: entry.badgeTypeId,
      badgeName: entry.badgeName || 'Unknown Badge',
      assignedBy: entry.assignedBy,
      assignedAt: entry.assignedAt,
      action: entry.action as 'assigned' | 'removed',
      isVisible: entry.isVisible
    }));

    return {
      entries: auditEntries,
      total: Number(total),
      hasMore: filters.offset + filters.limit < Number(total)
    };
  } catch (error) {
    console.error("Error getting badge audit log:", error);
    throw error;
  }
}

/**
 * Gets comprehensive badge statistics for administrators
 */
export async function getAdminBadgeStats(
  db: ReturnType<typeof drizzle>
): Promise<BadgeAdminStats> {
  try {
    // Get basic badge counts
    const [
      totalBadgeTypes,
      activeBadgeTypes,
      totalUserBadges,
      totalCollections,
      activeCollections
    ] = await Promise.all([
      db.select({ count: count() }).from(BadgeTypeSchema),
      db.select({ count: count() }).from(BadgeTypeSchema).where(eq(BadgeTypeSchema.isActive, true)),
      db.select({ count: count() }).from(UserBadgeSchema),
      db.select({ count: count() }).from(BadgeCollectionSchema),
      db.select({ count: count() }).from(BadgeCollectionSchema).where(eq(BadgeCollectionSchema.isActive, true))
    ]);

    // Get badge type distribution by category
    const categoryDistribution = await db
      .select({
        category: BadgeTypeSchema.category,
        count: count()
      })
      .from(BadgeTypeSchema)
      .where(eq(BadgeTypeSchema.isActive, true))
      .groupBy(BadgeTypeSchema.category);

    // Get badge type distribution by unlock type
    const unlockTypeDistribution = await db
      .select({
        unlockType: BadgeTypeSchema.unlockType,
        count: count()
      })
      .from(BadgeTypeSchema)
      .where(eq(BadgeTypeSchema.isActive, true))
      .groupBy(BadgeTypeSchema.unlockType);

    // Get most assigned badges
    const mostAssignedBadges = await db
      .select({
        badgeTypeId: UserBadgeSchema.badgeTypeId,
        badgeName: BadgeTypeSchema.name,
        assignmentCount: count()
      })
      .from(UserBadgeSchema)
      .leftJoin(BadgeTypeSchema, eq(UserBadgeSchema.badgeTypeId, BadgeTypeSchema.id))
      .groupBy(UserBadgeSchema.badgeTypeId, BadgeTypeSchema.name)
      .orderBy(desc(count()))
      .limit(10);

    // Get recent assignment activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentAssignments = await db
      .select({ count: count() })
      .from(UserBadgeSchema)
      .where(gte(UserBadgeSchema.assignedAt, thirtyDaysAgo));

    // Get users with most badges
    const topBadgeHolders = await db
      .select({
        userId: UserBadgeSchema.userId,
        username: UserSchema.username,
        badgeCount: count()
      })
      .from(UserBadgeSchema)
      .leftJoin(UserSchema, eq(UserBadgeSchema.userId, UserSchema.id))
      .groupBy(UserBadgeSchema.userId, UserSchema.username)
      .orderBy(desc(count()))
      .limit(10);

    return {
      totalBadgeTypes: Number(totalBadgeTypes[0]?.count || 0),
      activeBadgeTypes: Number(activeBadgeTypes[0]?.count || 0),
      totalUserBadges: Number(totalUserBadges[0]?.count || 0),
      totalCollections: Number(totalCollections[0]?.count || 0),
      activeCollections: Number(activeCollections[0]?.count || 0),
      recentAssignments: Number(recentAssignments[0]?.count || 0),
      categoryDistribution: categoryDistribution.map(item => ({
        category: item.category,
        count: Number(item.count)
      })),
      unlockTypeDistribution: unlockTypeDistribution.map(item => ({
        unlockType: item.unlockType,
        count: Number(item.count)
      })),
      mostAssignedBadges: mostAssignedBadges.map(item => ({
        badgeTypeId: item.badgeTypeId,
        badgeName: item.badgeName || 'Unknown Badge',
        assignmentCount: Number(item.assignmentCount)
      })),
      topBadgeHolders: topBadgeHolders.map(item => ({
        userId: item.userId,
        username: item.username || 'Unknown User',
        badgeCount: Number(item.badgeCount)
      }))
    };
  } catch (error) {
    console.error("Error getting admin badge stats:", error);
    throw error;
  }
}

/**
 * Performs comprehensive badge system health check
 */
export async function getBadgeSystemHealthCheck(
  db: ReturnType<typeof drizzle>
): Promise<BadgeSystemHealthCheck> {
  try {
    const issues: string[] = [];
    const warnings: string[] = [];

    // Check for orphaned user badges (badges referencing non-existent badge types)
    const orphanedBadges = await db
      .select({ count: count() })
      .from(UserBadgeSchema)
      .leftJoin(BadgeTypeSchema, eq(UserBadgeSchema.badgeTypeId, BadgeTypeSchema.id))
      .where(isNull(BadgeTypeSchema.id));

    const orphanedBadgeCount = Number(orphanedBadges[0]?.count || 0);
    if (orphanedBadgeCount > 0) {
      issues.push(`Found ${orphanedBadgeCount} orphaned user badges referencing deleted badge types`);
    }

    // Check for badge types without any assignments
    const unassignedBadgeTypes = await db
      .select({ count: count() })
      .from(BadgeTypeSchema)
      .leftJoin(UserBadgeSchema, eq(BadgeTypeSchema.id, UserBadgeSchema.badgeTypeId))
      .where(and(
        eq(BadgeTypeSchema.isActive, true),
        isNull(UserBadgeSchema.id)
      ));

    const unassignedBadgeTypeCount = Number(unassignedBadgeTypes[0]?.count || 0);
    if (unassignedBadgeTypeCount > 0) {
      warnings.push(`Found ${unassignedBadgeTypeCount} active badge types with no assignments`);
    }

    // Check for collections with inconsistent progress
    const inconsistentCollections = await db
      .select({
        collectionId: UserCollectionProgressSchema.collectionId,
        userId: UserCollectionProgressSchema.userId,
        recordedBadges: UserCollectionProgressSchema.badgesEarned,
        actualBadges: count(UserBadgeSchema.id)
      })
      .from(UserCollectionProgressSchema)
      .leftJoin(UserBadgeSchema, and(
        eq(UserCollectionProgressSchema.userId, UserBadgeSchema.userId),
        eq(UserCollectionProgressSchema.collectionId, UserBadgeSchema.collectionId)
      ))
      .groupBy(
        UserCollectionProgressSchema.collectionId,
        UserCollectionProgressSchema.userId,
        UserCollectionProgressSchema.badgesEarned
      )
      .having(sql`${UserCollectionProgressSchema.badgesEarned} != COUNT(${UserBadgeSchema.id})`);

    if (inconsistentCollections.length > 0) {
      issues.push(`Found ${inconsistentCollections.length} collection progress records with inconsistent badge counts`);
    }

    // Check for inactive badge types still being assigned
    const inactiveBadgeAssignments = await db
      .select({ count: count() })
      .from(UserBadgeSchema)
      .leftJoin(BadgeTypeSchema, eq(UserBadgeSchema.badgeTypeId, BadgeTypeSchema.id))
      .where(eq(BadgeTypeSchema.isActive, false));

    const inactiveBadgeAssignmentCount = Number(inactiveBadgeAssignments[0]?.count || 0);
    if (inactiveBadgeAssignmentCount > 0) {
      warnings.push(`Found ${inactiveBadgeAssignmentCount} assignments for inactive badge types`);
    }

    // Check for duplicate badge assignments (should not happen due to unique constraint)
    const duplicateAssignments = await db
      .select({
        userId: UserBadgeSchema.userId,
        badgeTypeId: UserBadgeSchema.badgeTypeId,
        count: count()
      })
      .from(UserBadgeSchema)
      .groupBy(UserBadgeSchema.userId, UserBadgeSchema.badgeTypeId)
      .having(sql`COUNT(*) > 1`);

    if (duplicateAssignments.length > 0) {
      issues.push(`Found ${duplicateAssignments.length} duplicate badge assignments`);
    }

    // Calculate overall health score
    const totalChecks = 5;
    const issueCount = issues.length;
    const warningCount = warnings.length;
    const healthScore = Math.max(0, 100 - (issueCount * 20) - (warningCount * 5));

    return {
      healthScore,
      status: healthScore >= 90 ? 'healthy' : healthScore >= 70 ? 'warning' : 'critical',
      issues,
      warnings,
      lastChecked: new Date(),
      metrics: {
        orphanedBadges: orphanedBadgeCount,
        unassignedBadgeTypes: unassignedBadgeTypeCount,
        inconsistentCollections: inconsistentCollections.length,
        inactiveBadgeAssignments: inactiveBadgeAssignmentCount,
        duplicateAssignments: duplicateAssignments.length
      }
    };
  } catch (error) {
    console.error("Error performing badge health check:", error);
    throw error;
  }
}

/**
 * Cleans up orphaned badge data
 */
export async function cleanupOrphanedBadgeData(
  db: ReturnType<typeof drizzle>
): Promise<BadgeCleanupResult> {
  try {
    let cleanedItems = 0;
    const cleanupActions: string[] = [];

    // Clean up orphaned user badges
    const orphanedBadgesResult = await db
      .delete(UserBadgeSchema)
      .where(
        sql`${UserBadgeSchema.badgeTypeId} NOT IN (SELECT id FROM ${BadgeTypeSchema})`
      );

    if (orphanedBadgesResult.rowCount && orphanedBadgesResult.rowCount > 0) {
      cleanedItems += orphanedBadgesResult.rowCount;
      cleanupActions.push(`Removed ${orphanedBadgesResult.rowCount} orphaned user badges`);
    }

    // Clean up orphaned collection progress records
    const orphanedProgressResult = await db
      .delete(UserCollectionProgressSchema)
      .where(
        sql`${UserCollectionProgressSchema.collectionId} NOT IN (SELECT id FROM ${BadgeCollectionSchema})`
      );

    if (orphanedProgressResult.rowCount && orphanedProgressResult.rowCount > 0) {
      cleanedItems += orphanedProgressResult.rowCount;
      cleanupActions.push(`Removed ${orphanedProgressResult.rowCount} orphaned collection progress records`);
    }

    // Clean up orphaned nominations
    const orphanedNominationsResult = await db
      .delete(BadgeNominationSchema)
      .where(
        sql`${BadgeNominationSchema.badgeTypeId} NOT IN (SELECT id FROM ${BadgeTypeSchema})`
      );

    if (orphanedNominationsResult.rowCount && orphanedNominationsResult.rowCount > 0) {
      cleanedItems += orphanedNominationsResult.rowCount;
      cleanupActions.push(`Removed ${orphanedNominationsResult.rowCount} orphaned badge nominations`);
    }

    return {
      cleanedItems,
      cleanupActions,
      cleanupDate: new Date()
    };
  } catch (error) {
    console.error("Error cleaning up badge data:", error);
    throw error;
  }
}

/**
 * Gets comprehensive user badge summary for administrators
 */
export async function getUserBadgeSummary(
  db: ReturnType<typeof drizzle>,
  userId: string
): Promise<UserBadgeSummary> {
  try {
    // Get user basic info
    const user = await db
      .select({
        id: UserSchema.id,
        username: UserSchema.username,
        createdAt: UserSchema.createdAt
      })
      .from(UserSchema)
      .where(eq(UserSchema.id, userId))
      .limit(1);

    if (user.length === 0) {
      throw new Error("User not found");
    }

    // Get user badges with badge type info
    const userBadges = await db
      .select({
        id: UserBadgeSchema.id,
        badgeTypeId: UserBadgeSchema.badgeTypeId,
        badgeName: BadgeTypeSchema.name,
        badgeCategory: BadgeTypeSchema.category,
        unlockType: BadgeTypeSchema.unlockType,
        assignedBy: UserBadgeSchema.assignedBy,
        assignedAt: UserBadgeSchema.assignedAt,
        isVisible: UserBadgeSchema.isVisible
      })
      .from(UserBadgeSchema)
      .leftJoin(BadgeTypeSchema, eq(UserBadgeSchema.badgeTypeId, BadgeTypeSchema.id))
      .where(eq(UserBadgeSchema.userId, userId))
      .orderBy(desc(UserBadgeSchema.assignedAt));

    // Get collection progress
    const collectionProgress = await db
      .select({
        collectionId: UserCollectionProgressSchema.collectionId,
        collectionName: BadgeCollectionSchema.name,
        badgesEarned: UserCollectionProgressSchema.badgesEarned,
        totalBadges: UserCollectionProgressSchema.totalBadges,
        isCompleted: UserCollectionProgressSchema.isCompleted,
        completionDate: UserCollectionProgressSchema.completionDate
      })
      .from(UserCollectionProgressSchema)
      .leftJoin(BadgeCollectionSchema, eq(UserCollectionProgressSchema.collectionId, BadgeCollectionSchema.id))
      .where(eq(UserCollectionProgressSchema.userId, userId));

    // Get nominations received
    const nominationsReceived = await db
      .select({
        id: BadgeNominationSchema.id,
        badgeTypeId: BadgeNominationSchema.badgeTypeId,
        badgeName: BadgeTypeSchema.name,
        nominatorUserId: BadgeNominationSchema.nominatorUserId,
        status: BadgeNominationSchema.status,
        createdAt: BadgeNominationSchema.createdAt
      })
      .from(BadgeNominationSchema)
      .leftJoin(BadgeTypeSchema, eq(BadgeNominationSchema.badgeTypeId, BadgeTypeSchema.id))
      .where(eq(BadgeNominationSchema.nomineeUserId, userId))
      .orderBy(desc(BadgeNominationSchema.createdAt));

    // Calculate statistics
    const totalBadges = userBadges.length;
    const visibleBadges = userBadges.filter(badge => badge.isVisible).length;
    const automaticBadges = userBadges.filter(badge => badge.unlockType === 'automatic').length;
    const manualBadges = userBadges.filter(badge => badge.unlockType === 'manual').length;
    const completedCollections = collectionProgress.filter(progress => progress.isCompleted).length;

    // Get category breakdown
    const categoryBreakdown = userBadges.reduce((acc, badge) => {
      const category = badge.badgeCategory || 'unknown';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      user: {
        id: user[0].id,
        username: user[0].username || 'Unknown User',
        createdAt: user[0].createdAt
      },
      badges: userBadges.map(badge => ({
        id: badge.id,
        badgeTypeId: badge.badgeTypeId,
        badgeName: badge.badgeName || 'Unknown Badge',
        badgeCategory: badge.badgeCategory,
        unlockType: badge.unlockType,
        assignedBy: badge.assignedBy,
        assignedAt: badge.assignedAt,
        isVisible: badge.isVisible
      })),
      collectionProgress: collectionProgress.map(progress => ({
        collectionId: progress.collectionId,
        collectionName: progress.collectionName || 'Unknown Collection',
        badgesEarned: progress.badgesEarned,
        totalBadges: progress.totalBadges,
        isCompleted: progress.isCompleted,
        completionDate: progress.completionDate
      })),
      nominationsReceived: nominationsReceived.map(nomination => ({
        id: nomination.id,
        badgeTypeId: nomination.badgeTypeId,
        badgeName: nomination.badgeName || 'Unknown Badge',
        nominatorUserId: nomination.nominatorUserId,
        status: nomination.status,
        createdAt: nomination.createdAt
      })),
      statistics: {
        totalBadges,
        visibleBadges,
        automaticBadges,
        manualBadges,
        completedCollections,
        categoryBreakdown
      }
    };
  } catch (error) {
    console.error("Error getting user badge summary:", error);
    throw error;
  }
}