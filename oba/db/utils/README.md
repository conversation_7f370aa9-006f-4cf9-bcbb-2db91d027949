# Badge Database Utilities

This directory contains database utilities for the badge system, providing comprehensive CRUD operations, user statistics calculation, and automatic badge evaluation.

## Files

### `badge-utils.ts`
Core badge database operations including:

#### Badge Type Management
- `createBadgeType()` - Create new badge types
- `getBadgeTypeById()` - Retrieve badge type by ID
- `getBadgeTypes()` - Get badge types with filtering and pagination
- `updateBadgeType()` - Update existing badge types
- `deleteBadgeType()` - Delete badge types and associated assignments

#### User Badge Operations
- `assignBadgeToUser()` - Assign badges to users
- `removeBadgeFromUser()` - Remove badges from users
- `getUserBadges()` - Get all badges for a user
- `updateBadgeVisibility()` - Update badge visibility settings
- `getAvailableBadgesForUser()` - Get badges user doesn't have yet

#### Statistics and Analytics
- `getUserStats()` - Calculate user statistics for badge evaluation
- `getBadgeStats()` - Get system-wide badge statistics
- `getBadgeLeaderboard()` - Get badge leaderboard
- `getBadgeProgress()` - Get progress toward automatic badges

#### Bulk Operations
- `bulkAssignBadges()` - Assign multiple badges in a transaction
- `executeBadgeTransaction()` - Execute custom badge operations in transactions

### `badge-evaluation.ts`
Automatic badge evaluation and criteria checking:

#### Evaluation Functions
- `evaluateBadgeCriteria()` - Check if user meets badge criteria
- `evaluateUserForAutomaticBadges()` - Evaluate all automatic badges for a user
- `batchEvaluateUsers()` - Evaluate multiple users in batch
- `evaluateAllUsersForAutomaticBadges()` - Evaluate entire user base
- `reevaluateBadgeTypeForAllUsers()` - Re-evaluate specific badge type
- `getUsersNearBadgeCompletion()` - Find users close to earning badges

#### Criteria Types Supported
- `message_count` - Based on total messages sent
- `server_count` - Based on servers user is member of
- `friend_count` - Based on accepted friendships
- `days_active` - Based on account activity duration
- `custom` - Extensible custom criteria system

## Usage Examples

### Creating a Badge Type
```typescript
import { createBadgeType } from './db/utils/badge-utils';

const badgeData = {
  name: "First Message",
  description: "Sent your first message",
  category: "milestone",
  assignmentType: "automatic",
  criteria: {
    type: "message_count",
    threshold: 1,
  },
};

const badge = await createBadgeType(db, badgeData);
```

### Assigning a Badge
```typescript
import { assignBadgeToUser } from './db/utils/badge-utils';

const userBadge = await assignBadgeToUser(db, userId, badgeTypeId, assignedByUserId);
```

### Evaluating Automatic Badges
```typescript
import { evaluateUserForAutomaticBadges } from './db/utils/badge-evaluation';

const result = await evaluateUserForAutomaticBadges(db, userId);
console.log(`Assigned ${result.newBadges.length} new badges`);
```

### Getting User Statistics
```typescript
import { getUserStats } from './db/utils/badge-utils';

const stats = await getUserStats(db, userId);
console.log(`User has sent ${stats.messageCount} messages`);
```

## Database Schema

The badge system uses two main tables:

### `badge_types`
- Stores badge definitions and criteria
- Supports both automatic and manual assignment types
- Includes metadata like name, description, icon, color, category

### `user_badges`
- Tracks badge assignments to users
- Records assignment timestamp and assigner
- Supports visibility controls

## Error Handling

All functions include comprehensive error handling:
- Input validation
- Database constraint checking
- Transaction rollback on failures
- Descriptive error messages

## Performance Considerations

- Database indexes on `user_id` and `badge_type_id` for efficient queries
- Batch operations for processing multiple users
- Pagination support for large result sets
- Transaction-based operations for data consistency

## Testing

Run the test suite with:
```bash
bun run test-badge-utils.ts
```

The test suite covers all major functions and edge cases.