import type { drizzle } from "drizzle-orm/postgres-js";
import { eq, and } from "drizzle-orm";
import { BadgeTypeSchema, UserSchema } from "../schema";
import {
  getUserStats,
  assignBadgeToUser,
  getUserBadges,
  getBadgeTypes,
} from "./badge-utils";
import type {
  BadgeCriteria,
  UserStats,
  EvaluationResult,
} from "../../types/badge.types";

/**
 * Evaluates if a user meets the criteria for a specific badge
 */
export async function evaluateBadgeCriteria(
  userStats: UserStats,
  criteria: BadgeCriteria,
): Promise<boolean> {
  try {
    switch (criteria.type) {
      case "message_count":
        return criteria.threshold ? userStats.messageCount >= criteria.threshold : false;
      
      case "server_count":
        return criteria.threshold ? userStats.serverCount >= criteria.threshold : false;
      
      case "friend_count":
        return criteria.threshold ? userStats.friendCount >= criteria.threshold : false;
      
      case "days_active":
        return criteria.threshold ? userStats.daysActive >= criteria.threshold : false;
      
      case "custom":
        // For custom criteria, implement specific logic based on conditions
        if (criteria.conditions) {
          return evaluateCustomCriteria(userStats, criteria.conditions);
        }
        return false;
      
      case "complex":
        // Complex criteria support multiple conditions and advanced logic
        return evaluateComplexCriteria(userStats, criteria);
      
      default:
        return false;
    }
  } catch (error) {
    console.error("Error evaluating badge criteria:", error instanceof Error ? error.message : String(error));
    return false;
  }
}

/**
 * Evaluates complex criteria with multiple conditions and advanced logic
 */
function evaluateComplexCriteria(
  userStats: UserStats,
  criteria: BadgeCriteria,
): boolean {
  if (!criteria.conditions) {
    return false;
  }
  
  const conditions = criteria.conditions;
  
  // Time-based criteria with timeframe support
  if (criteria.timeframe && conditions.timeBasedRequirements) {
    // For now, we'll use simplified logic since we don't track time-based stats
    // In a real implementation, you'd query time-windowed data
    const timeReqs = conditions.timeBasedRequirements;
    
    if (timeReqs.messagesInPeriod && userStats.messageCount < timeReqs.messagesInPeriod) {
      return false;
    }
    
    if (timeReqs.serversJoinedInPeriod && userStats.serverCount < timeReqs.serversJoinedInPeriod) {
      return false;
    }
  }
  
  // Multi-threshold criteria (must meet ALL thresholds)
  if (conditions.multiThreshold) {
    const thresholds = conditions.multiThreshold;
    
    if (thresholds.messages && userStats.messageCount < thresholds.messages) return false;
    if (thresholds.servers && userStats.serverCount < thresholds.servers) return false;
    if (thresholds.friends && userStats.friendCount < thresholds.friends) return false;
    if (thresholds.accountAge && userStats.accountAge < thresholds.accountAge) return false;
    if (thresholds.invitesSent && userStats.invitesSent < thresholds.invitesSent) return false;
    if (thresholds.invitesAccepted && userStats.invitesAccepted < thresholds.invitesAccepted) return false;
  }
  
  // Weighted scoring system
  if (conditions.weightedScore) {
    const weights = conditions.weightedScore;
    let totalScore = 0;
    
    if (weights.messageWeight) {
      totalScore += userStats.messageCount * weights.messageWeight;
    }
    if (weights.serverWeight) {
      totalScore += userStats.serverCount * weights.serverWeight;
    }
    if (weights.friendWeight) {
      totalScore += userStats.friendCount * weights.friendWeight;
    }
    if (weights.inviteWeight) {
      totalScore += userStats.invitesSent * weights.inviteWeight;
    }
    
    const requiredScore = weights.minimumScore || criteria.threshold || 0;
    if (totalScore < requiredScore) {
      return false;
    }
  }
  
  // Conditional logic (if-then requirements)
  if (conditions.conditionalRequirements) {
    const condReqs = conditions.conditionalRequirements;
    
    // If user has many servers, they need more friends
    if (condReqs.serverToFriendRatio) {
      if (userStats.serverCount >= condReqs.serverToFriendRatio.serverThreshold) {
        const requiredFriends = userStats.serverCount * condReqs.serverToFriendRatio.friendMultiplier;
        if (userStats.friendCount < requiredFriends) {
          return false;
        }
      }
    }
    
    // If user is old account, they need more activity
    if (condReqs.ageToActivityRatio) {
      if (userStats.accountAge >= condReqs.ageToActivityRatio.ageThreshold) {
        const requiredMessages = userStats.accountAge * condReqs.ageToActivityRatio.messageMultiplier;
        if (userStats.messageCount < requiredMessages) {
          return false;
        }
      }
    }
  }
  
  return true;
}

/**
 * Evaluates custom criteria (extensible for future badge types)
 */
function evaluateCustomCriteria(
  userStats: UserStats,
  conditions: Record<string, any>,
): boolean {
  // Account age requirement
  if (conditions.minAccountAge) {
    if (userStats.accountAge < conditions.minAccountAge) {
      return false;
    }
  }
  
  // Combined requirements (e.g., messages AND servers)
  if (conditions.combinedRequirements) {
    const reqs = conditions.combinedRequirements;
    if (reqs.messages && userStats.messageCount < reqs.messages) return false;
    if (reqs.servers && userStats.serverCount < reqs.servers) return false;
    if (reqs.friends && userStats.friendCount < reqs.friends) return false;
    if (reqs.invitesSent && userStats.invitesSent < reqs.invitesSent) return false;
    if (reqs.invitesAccepted && userStats.invitesAccepted < reqs.invitesAccepted) return false;
    if (reqs.feedbackSubmitted && userStats.feedbackSubmitted < reqs.feedbackSubmitted) return false;
    if (reqs.moderationActions && userStats.moderationActions < reqs.moderationActions) return false;
  }
  
  // Activity ratio (messages per day)
  if (conditions.minActivityRatio) {
    const activityRatio = userStats.messageCount / Math.max(userStats.daysActive, 1);
    if (activityRatio < conditions.minActivityRatio) {
      return false;
    }
  }
  
  // Early adopter criteria (based on signup order)
  if (conditions.maxSignupOrder) {
    if (!userStats.signupOrder || userStats.signupOrder > conditions.maxSignupOrder) {
      return false;
    }
  }
  
  // Geographic region requirement
  if (conditions.requiredRegion) {
    if (!userStats.geographicRegion || userStats.geographicRegion !== conditions.requiredRegion) {
      return false;
    }
  }
  
  // Social engagement criteria (friends to servers ratio)
  if (conditions.minSocialEngagement) {
    const socialRatio = userStats.friendCount / Math.max(userStats.serverCount, 1);
    if (socialRatio < conditions.minSocialEngagement) {
      return false;
    }
  }
  
  // Invitation success rate
  if (conditions.minInviteSuccessRate) {
    const successRate = userStats.invitesSent > 0 
      ? userStats.invitesAccepted / userStats.invitesSent 
      : 0;
    if (successRate < conditions.minInviteSuccessRate) {
      return false;
    }
  }
  
  // Multi-condition OR logic (at least one condition must be met)
  if (conditions.orRequirements) {
    const orReqs = conditions.orRequirements;
    let anyMet = false;
    
    if (orReqs.messages && userStats.messageCount >= orReqs.messages) anyMet = true;
    if (orReqs.servers && userStats.serverCount >= orReqs.servers) anyMet = true;
    if (orReqs.friends && userStats.friendCount >= orReqs.friends) anyMet = true;
    if (orReqs.invitesSent && userStats.invitesSent >= orReqs.invitesSent) anyMet = true;
    if (orReqs.accountAge && userStats.accountAge >= orReqs.accountAge) anyMet = true;
    
    if (!anyMet) {
      return false;
    }
  }
  
  // Percentage-based requirements (e.g., top 10% of users by message count)
  if (conditions.percentileRequirements) {
    // This would require additional database queries to calculate percentiles
    // For now, we'll implement basic thresholds
    const percentReqs = conditions.percentileRequirements;
    
    // These thresholds would ideally be calculated dynamically
    if (percentReqs.messageCountTop10Percent && userStats.messageCount < 1000) return false;
    if (percentReqs.serverCountTop25Percent && userStats.serverCount < 5) return false;
    if (percentReqs.friendCountTop50Percent && userStats.friendCount < 10) return false;
  }
  
  return true;
}

/**
 * Evaluates all automatic badges for a single user
 */
export async function evaluateUserForAutomaticBadges(
  db: ReturnType<typeof drizzle>,
  userId: string,
): Promise<EvaluationResult> {
  try {
    const result: EvaluationResult = {
      userId,
      newBadges: [],
      evaluatedBadges: [],
      errors: [],
    };

    // Get user statistics
    let userStats: UserStats;
    try {
      userStats = await getUserStats(db, userId);
    } catch (error) {
      result.errors.push(`Failed to get user stats: ${error instanceof Error ? error.message : String(error)}`);
      return result;
    }

    // Get all automatic badge types
    const automaticBadges = await getBadgeTypes(db, {
      assignmentType: "automatic",
      isActive: true,
    });

    // Get user's current badges to avoid duplicates
    const currentBadges = await getUserBadges(db, userId);
    const currentBadgeTypeIds = new Set(currentBadges.map(badge => badge.badgeTypeId));

    // Evaluate each automatic badge
    for (const badgeType of automaticBadges) {
      result.evaluatedBadges.push(badgeType.id);

      // Skip if user already has this badge
      if (currentBadgeTypeIds.has(badgeType.id)) {
        continue;
      }

      // Skip if no criteria defined
      if (!badgeType.criteria) {
        continue;
      }

      try {
        // Check if user meets criteria
        const meetsCriteria = await evaluateBadgeCriteria(userStats, badgeType.criteria);

        if (meetsCriteria) {
          // Assign the badge
          const newBadge = await assignBadgeToUser(db, userId, badgeType.id);
          if (newBadge) {
            result.newBadges.push(newBadge);
          }
        }
      } catch (error) {
        result.errors.push(`Failed to evaluate badge ${badgeType.name}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    return result;
  } catch (error) {
    console.error("Error evaluating user for automatic badges:", error);
    return {
      userId,
      newBadges: [],
      evaluatedBadges: [],
      errors: [`Evaluation failed: ${error instanceof Error ? error.message : String(error)}`],
    };
  }
}

/**
 * Evaluates automatic badges for multiple users in batch
 */
export async function batchEvaluateUsers(
  db: ReturnType<typeof drizzle>,
  userIds: string[],
): Promise<EvaluationResult[]> {
  try {
    const results: EvaluationResult[] = [];

    // Process users in smaller batches to avoid overwhelming the database
    const batchSize = 10;
    for (let i = 0; i < userIds.length; i += batchSize) {
      const batch = userIds.slice(i, i + batchSize);
      
      const batchPromises = batch.map(userId => 
        evaluateUserForAutomaticBadges(db, userId)
      );
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return results;
  } catch (error) {
    console.error("Error in batch evaluation:", error);
    throw new Error("Failed to batch evaluate users");
  }
}

/**
 * Evaluates all users in the system for automatic badges
 * Use with caution on large user bases - consider running as a background job
 */
export async function evaluateAllUsersForAutomaticBadges(
  db: ReturnType<typeof drizzle>,
  batchSize: number = 50,
): Promise<{
  totalUsers: number;
  processedUsers: number;
  totalNewBadges: number;
  errors: string[];
}> {
  try {
    const summary = {
      totalUsers: 0,
      processedUsers: 0,
      totalNewBadges: 0,
      errors: [] as string[],
    };

    // Get all user IDs
    const users = await db
      .select({ id: UserSchema.id })
      .from(UserSchema);

    summary.totalUsers = users.length;

    // Process users in batches
    for (let i = 0; i < users.length; i += batchSize) {
      const batch = users.slice(i, i + batchSize);
      const userIds = batch.map(user => user.id);

      try {
        const batchResults = await batchEvaluateUsers(db, userIds);
        
        for (const result of batchResults) {
          summary.processedUsers++;
          summary.totalNewBadges += result.newBadges.length;
          summary.errors.push(...result.errors);
        }
      } catch (error) {
        summary.errors.push(`Batch processing error: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    return summary;
  } catch (error) {
    console.error("Error evaluating all users:", error);
    throw new Error("Failed to evaluate all users for automatic badges");
  }
}

/**
 * Re-evaluates a specific badge type for all users
 * Useful when badge criteria are updated
 */
export async function reevaluateBadgeTypeForAllUsers(
  db: ReturnType<typeof drizzle>,
  badgeTypeId: string,
): Promise<{
  evaluatedUsers: number;
  newAssignments: number;
  errors: string[];
}> {
  try {
    const summary = {
      evaluatedUsers: 0,
      newAssignments: 0,
      errors: [] as string[],
    };

    // Get the badge type
    const badgeType = await db
      .select()
      .from(BadgeTypeSchema)
      .where(and(
        eq(BadgeTypeSchema.id, badgeTypeId),
        eq(BadgeTypeSchema.assignmentType, "automatic"),
        eq(BadgeTypeSchema.isActive, true)
      ))
      .limit(1);

    if (badgeType.length === 0) {
      throw new Error("Badge type not found or not automatic");
    }

    const badge = {
      ...badgeType[0],
      criteria: badgeType[0].criteria ? JSON.parse(badgeType[0].criteria) : undefined,
    };

    if (!badge.criteria) {
      throw new Error("Badge type has no criteria defined");
    }

    // Get all users
    const users = await db
      .select({ id: UserSchema.id })
      .from(UserSchema);

    // Process all users and let the assignment function handle duplicates
    for (const user of users) {
      try {
        summary.evaluatedUsers++;
        
        const userStats = await getUserStats(db, user.id);
        const meetsCriteria = await evaluateBadgeCriteria(userStats, badge.criteria);
        
        if (meetsCriteria) {
          try {
            const newBadge = await assignBadgeToUser(db, user.id, badgeTypeId);
            if (newBadge) {
              summary.newAssignments++;
            }
          } catch (error) {
            // Ignore "already has badge" errors
            const errorMessage = error instanceof Error ? error.message : String(error);
            if (!errorMessage.includes("already has this badge")) {
              summary.errors.push(`Failed to assign badge to user ${user.id}: ${errorMessage}`);
            }
          }
        }
      } catch (error) {
        summary.errors.push(`Failed to evaluate user ${user.id}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    return summary;
  } catch (error) {
    console.error("Error re-evaluating badge type:", error);
    throw new Error("Failed to re-evaluate badge type for all users");
  }
}

/**
 * Gets users who are close to earning a specific automatic badge
 */
export async function getUsersNearBadgeCompletion(
  db: ReturnType<typeof drizzle>,
  badgeTypeId: string,
  thresholdPercentage: number = 0.8,
): Promise<Array<{
  userId: string;
  username: string;
  progress: number;
  total: number;
  progressPercentage: number;
}>> {
  try {
    // Get the badge type
    const badgeType = await db
      .select()
      .from(BadgeTypeSchema)
      .where(and(
        eq(BadgeTypeSchema.id, badgeTypeId),
        eq(BadgeTypeSchema.assignmentType, "automatic"),
        eq(BadgeTypeSchema.isActive, true)
      ))
      .limit(1);

    if (badgeType.length === 0) {
      throw new Error("Badge type not found or not automatic");
    }

    const badge = {
      ...badgeType[0],
      criteria: badgeType[0].criteria ? JSON.parse(badgeType[0].criteria) : undefined,
    };

    if (!badge.criteria || !badge.criteria.threshold) {
      throw new Error("Badge type has no valid criteria defined");
    }

    // Get all users who don't have this badge yet
    const users = await db
      .select({
        id: UserSchema.id,
        username: UserSchema.username,
      })
      .from(UserSchema);

    const results = [];
    const threshold = badge.criteria.threshold * thresholdPercentage;

    for (const user of users) {
      try {
        // Check if user already has this badge
        const userBadges = await getUserBadges(db, user.id);
        const hasBadge = userBadges.some(b => b.badgeTypeId === badgeTypeId);
        
        if (hasBadge) {
          continue; // Skip users who already have the badge
        }

        const userStats = await getUserStats(db, user.id);
        let currentProgress = 0;

        // Get current progress based on criteria type
        switch (badge.criteria.type) {
          case "message_count":
            currentProgress = userStats.messageCount;
            break;
          case "server_count":
            currentProgress = userStats.serverCount;
            break;
          case "friend_count":
            currentProgress = userStats.friendCount;
            break;
          case "days_active":
            currentProgress = userStats.daysActive;
            break;
          default:
            continue;
        }

        const progressPercentage = currentProgress / badge.criteria.threshold;

        // Include users who are above the threshold percentage
        if (currentProgress >= threshold && currentProgress < badge.criteria.threshold) {
          results.push({
            userId: user.id,
            username: user.username,
            progress: currentProgress,
            total: badge.criteria.threshold,
            progressPercentage,
          });
        }
      } catch (error) {
        console.warn(`Failed to check progress for user ${user.id}:`, error instanceof Error ? error.message : String(error));
      }
    }

    // Sort by progress percentage (closest to completion first)
    return results.sort((a, b) => b.progressPercentage - a.progressPercentage);
  } catch (error) {
    console.error("Error getting users near badge completion:", error);
    throw new Error("Failed to get users near badge completion");
  }
}