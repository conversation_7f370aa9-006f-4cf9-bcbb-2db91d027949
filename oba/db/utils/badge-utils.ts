import { and, eq, or, sql, desc, asc, count, inArray, like, isNull, lt } from "drizzle-orm";
import type { drizzle } from "drizzle-orm/postgres-js";
import {
  BadgeTypeSchema,
  UserBadgeSchema,
  UserSchema,
  ServerMembershipSchema,
  MessageSchema,
  FriendshipSchema,
  ServerInviteSchema,
  friendStatusEnum,
} from "../schema";
import type {
  BadgeType,
  UserBadge,
  CreateBadgeTypeRequest,
  UpdateBadgeTypeRequest,
  BadgeTypeFilters,
  UserStats,
  BadgeStats,
  BadgeLeaderboard,
  EvaluationResult,
  BadgeProgress,
} from "../../types/badge.types";

// Badge Type CRUD Operations

/**
 * Creates a new badge type
 */
export async function createBadgeType(
  db: ReturnType<typeof drizzle>,
  badgeData: CreateBadgeTypeRequest,
): Promise<BadgeType> {
  try {
    const newBadgeType = await db
      .insert(BadgeTypeSchema)
      .values({
        collectionId: badgeData.collectionId || null,
        badgeId: badgeData.badgeId || `badge-${Date.now()}`,
        name: badgeData.name,
        title: badgeData.title,
        description: badgeData.description,
        icon: badgeData.icon || "🏆",
        tooltip: badgeData.tooltip,
        design: JSON.stringify(badgeData.design || { shape: "circle", background: "#000000", colors: ["#000000"] }),
        criteria: JSON.stringify(badgeData.criteria || { requirement: "Manual assignment", tracked: "manual" }),
        perks: badgeData.perks ? JSON.stringify(badgeData.perks) : null,
        unlockType: badgeData.unlockType || "manual",
        visualDescription: badgeData.visualDescription,
        animation: badgeData.animation,
        displayOrder: badgeData.displayOrder || 0,
        category: badgeData.category,
        isActive: true,
      })
      .returning();

    const result = newBadgeType[0];
    return {
      ...result,
      design: result.design ? JSON.parse(result.design) : { shape: "circle", background: "#000000", colors: ["#000000"] },
      criteria: result.criteria ? JSON.parse(result.criteria) : { requirement: "Manual assignment", tracked: "manual" },
      perks: result.perks ? JSON.parse(result.perks) : undefined,
    };
  } catch (error) {
    console.error("Error creating badge type:", error);
    throw new Error("Failed to create badge type");
  }
}

/**
 * Gets a badge type by ID
 */
export async function getBadgeTypeById(
  db: ReturnType<typeof drizzle>,
  badgeTypeId: string,
): Promise<BadgeType | null> {
  try {
    const badgeType = await db
      .select()
      .from(BadgeTypeSchema)
      .where(eq(BadgeTypeSchema.id, badgeTypeId))
      .limit(1);

    if (badgeType.length === 0) {
      return null;
    }

    const result = badgeType[0];
    return {
      ...result,
      criteria: result.criteria ? JSON.parse(result.criteria) : undefined,
    };
  } catch (error) {
    console.error("Error getting badge type by ID:", error);
    throw new Error("Failed to get badge type");
  }
}

/**
 * Gets all badge types with optional filtering and pagination
 */
export async function getBadgeTypes(
  db: ReturnType<typeof drizzle>,
  filters?: BadgeTypeFilters,
  limit?: number,
  offset?: number,
): Promise<BadgeType[]> {
  try {
    let query = db.select().from(BadgeTypeSchema);

    // Apply filters
    const conditions = [];
    if (filters?.category) {
      conditions.push(eq(BadgeTypeSchema.category, filters.category));
    }
    if (filters?.unlockType) {
      conditions.push(eq(BadgeTypeSchema.unlockType, filters.unlockType));
    }
    if (filters?.assignmentType) {
      // Legacy compatibility - map assignmentType to unlockType
      conditions.push(eq(BadgeTypeSchema.unlockType, filters.assignmentType));
    }
    if (filters?.isActive !== undefined) {
      conditions.push(eq(BadgeTypeSchema.isActive, filters.isActive));
    }
    if (filters?.search) {
      conditions.push(
        or(
          like(BadgeTypeSchema.name, `%${filters.search}%`),
          like(BadgeTypeSchema.description, `%${filters.search}%`)
        )
      );
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Apply ordering
    query = query.orderBy(asc(BadgeTypeSchema.name));

    // Apply pagination
    if (limit) {
      query = query.limit(limit);
    }
    if (offset) {
      query = query.offset(offset);
    }

    const badgeTypes = await query;

    return badgeTypes.map((badgeType) => ({
      ...badgeType,
      criteria: badgeType.criteria ? JSON.parse(badgeType.criteria) : undefined,
    }));
  } catch (error) {
    console.error("Error getting badge types:", error);
    throw new Error("Failed to get badge types");
  }
}

/**
 * Updates a badge type
 */
export async function updateBadgeType(
  db: ReturnType<typeof drizzle>,
  badgeTypeId: string,
  updates: UpdateBadgeTypeRequest,
): Promise<BadgeType | null> {
  try {
    // Check if badge type exists
    const existingBadgeType = await getBadgeTypeById(db, badgeTypeId);
    if (!existingBadgeType) {
      return null;
    }

    // Prepare update data
    const updateData: Record<string, any> = {
      updatedAt: new Date(),
    };

    if (updates.name !== undefined) updateData.name = updates.name;
    if (updates.description !== undefined) updateData.description = updates.description;
    if (updates.iconUrl !== undefined) updateData.iconUrl = updates.iconUrl;
    if (updates.color !== undefined) updateData.color = updates.color;
    if (updates.category !== undefined) updateData.category = updates.category;
    if (updates.isActive !== undefined) updateData.isActive = updates.isActive;
    if (updates.assignmentType !== undefined) updateData.assignmentType = updates.assignmentType;
    if (updates.criteria !== undefined) {
      updateData.criteria = updates.criteria ? JSON.stringify(updates.criteria) : null;
    }

    const updatedBadgeType = await db
      .update(BadgeTypeSchema)
      .set(updateData)
      .where(eq(BadgeTypeSchema.id, badgeTypeId))
      .returning();

    const result = updatedBadgeType[0];
    return {
      ...result,
      criteria: result.criteria ? JSON.parse(result.criteria) : undefined,
    };
  } catch (error) {
    console.error("Error updating badge type:", error);
    throw new Error("Failed to update badge type");
  }
}

/**
 * Deletes a badge type and all associated user badges
 */
export async function deleteBadgeType(
  db: ReturnType<typeof drizzle>,
  badgeTypeId: string,
): Promise<boolean> {
  try {
    return await db.transaction(async (trx) => {
      // Check if badge type exists
      const existingBadgeType = await trx
        .select()
        .from(BadgeTypeSchema)
        .where(eq(BadgeTypeSchema.id, badgeTypeId))
        .limit(1);

      if (existingBadgeType.length === 0) {
        return false;
      }

      // Delete all user badges of this type
      await trx
        .delete(UserBadgeSchema)
        .where(eq(UserBadgeSchema.badgeTypeId, badgeTypeId));

      // Delete the badge type
      await trx
        .delete(BadgeTypeSchema)
        .where(eq(BadgeTypeSchema.id, badgeTypeId));

      return true;
    });
  } catch (error) {
    console.error("Error deleting badge type:", error);
    throw new Error("Failed to delete badge type");
  }
}

// User Badge Operations

/**
 * Assigns a badge to a user
 */
export async function assignBadgeToUser(
  db: ReturnType<typeof drizzle>,
  userId: string,
  badgeTypeId: string,
  assignedBy?: string,
): Promise<UserBadge | null> {
  try {
    return await db.transaction(async (trx) => {
      // Check if badge type exists and is active
      const badgeType = await trx
        .select()
        .from(BadgeTypeSchema)
        .where(and(
          eq(BadgeTypeSchema.id, badgeTypeId),
          eq(BadgeTypeSchema.isActive, true)
        ))
        .limit(1);

      if (badgeType.length === 0) {
        throw new Error("Badge type not found or inactive");
      }

      // Check if user already has this badge
      const existingBadge = await trx
        .select()
        .from(UserBadgeSchema)
        .where(and(
          eq(UserBadgeSchema.userId, userId),
          eq(UserBadgeSchema.badgeTypeId, badgeTypeId)
        ))
        .limit(1);

      if (existingBadge.length > 0) {
        throw new Error("User already has this badge");
      }

      // Assign the badge
      const newUserBadge = await trx
        .insert(UserBadgeSchema)
        .values({
          userId,
          badgeTypeId,
          assignedBy,
          assignedAt: new Date(),
          isVisible: true,
        })
        .returning();

      return {
        ...newUserBadge[0],
        badgeType: {
          ...badgeType[0],
          criteria: badgeType[0].criteria ? JSON.parse(badgeType[0].criteria) : undefined,
        },
      };
    });
  } catch (error) {
    console.error("Error assigning badge to user:", error);
    if (error.message.includes("already has this badge") || error.message.includes("not found")) {
      throw error;
    }
    throw new Error("Failed to assign badge to user");
  }
}

/**
 * Removes a badge from a user
 */
export async function removeBadgeFromUser(
  db: ReturnType<typeof drizzle>,
  userId: string,
  badgeTypeId: string,
): Promise<boolean> {
  try {
    const result = await db
      .delete(UserBadgeSchema)
      .where(and(
        eq(UserBadgeSchema.userId, userId),
        eq(UserBadgeSchema.badgeTypeId, badgeTypeId)
      ))
      .returning();

    return result.length > 0;
  } catch (error) {
    console.error("Error removing badge from user:", error);
    throw new Error("Failed to remove badge from user");
  }
}

/**
 * Gets all badges for a user
 */
export async function getUserBadges(
  db: ReturnType<typeof drizzle>,
  userId: string,
  visibleOnly: boolean = false,
): Promise<UserBadge[]> {
  try {
    let query = db
      .select({
        id: UserBadgeSchema.id,
        userId: UserBadgeSchema.userId,
        badgeTypeId: UserBadgeSchema.badgeTypeId,
        assignedBy: UserBadgeSchema.assignedBy,
        assignedAt: UserBadgeSchema.assignedAt,
        isVisible: UserBadgeSchema.isVisible,
        badgeType: BadgeTypeSchema,
      })
      .from(UserBadgeSchema)
      .innerJoin(BadgeTypeSchema, eq(UserBadgeSchema.badgeTypeId, BadgeTypeSchema.id))
      .where(eq(UserBadgeSchema.userId, userId));

    if (visibleOnly) {
      query = query.where(and(
        eq(UserBadgeSchema.userId, userId),
        eq(UserBadgeSchema.isVisible, true)
      ));
    }

    query = query.orderBy(desc(UserBadgeSchema.assignedAt));

    const userBadges = await query;

    return userBadges.map((badge) => ({
      id: badge.id,
      userId: badge.userId,
      badgeTypeId: badge.badgeTypeId,
      assignedBy: badge.assignedBy,
      assignedAt: badge.assignedAt,
      isVisible: badge.isVisible,
      badgeType: {
        ...badge.badgeType,
        criteria: badge.badgeType.criteria ? JSON.parse(badge.badgeType.criteria) : undefined,
      },
    }));
  } catch (error) {
    console.error("Error getting user badges:", error);
    throw new Error("Failed to get user badges");
  }
}

/**
 * Updates badge visibility for a user
 */
export async function updateBadgeVisibility(
  db: ReturnType<typeof drizzle>,
  userId: string,
  badgeTypeId: string,
  isVisible: boolean,
): Promise<boolean> {
  try {
    const result = await db
      .update(UserBadgeSchema)
      .set({ isVisible })
      .where(and(
        eq(UserBadgeSchema.userId, userId),
        eq(UserBadgeSchema.badgeTypeId, badgeTypeId)
      ))
      .returning();

    return result.length > 0;
  } catch (error) {
    console.error("Error updating badge visibility:", error);
    throw new Error("Failed to update badge visibility");
  }
}

// User Statistics Calculation

/**
 * Calculates user statistics for badge evaluation
 */
export async function getUserStats(
  db: ReturnType<typeof drizzle>,
  userId: string,
): Promise<UserStats> {
  try {
    // Get user info
    const user = await db
      .select({
        createdAt: UserSchema.createdAt,
        lastActive: UserSchema.lastActive,
      })
      .from(UserSchema)
      .where(eq(UserSchema.id, userId))
      .limit(1);

    if (user.length === 0) {
      throw new Error("User not found");
    }

    // Get message count
    const messageCount = await db
      .select({ count: count() })
      .from(MessageSchema)
      .where(eq(MessageSchema.userId, userId));

    // Get server count (servers user is a member of)
    const serverCount = await db
      .select({ count: count() })
      .from(ServerMembershipSchema)
      .where(eq(ServerMembershipSchema.userId, userId));

    // Get friend count (accepted friendships)
    const friendCount = await db
      .select({ count: count() })
      .from(FriendshipSchema)
      .where(and(
        or(
          eq(FriendshipSchema.userId, userId),
          eq(FriendshipSchema.friendId, userId)
        ),
        eq(FriendshipSchema.status, "ACCEPTED")
      ));

    // Get invites sent (server invites created by user)
    const invitesSent = await db
      .select({ count: count() })
      .from(ServerInviteSchema)
      .where(eq(ServerInviteSchema.createdById, userId));

    // Get invites accepted (simplified - just use 0 for now)
    // In a real system you'd track invite usage properly
    const invitesAccepted = [{ count: 0 }]; // Placeholder since we don't track invite usage yet

    // Calculate account age in days
    const accountAge = Math.floor(
      (Date.now() - user[0].createdAt.getTime()) / (1000 * 60 * 60 * 24)
    );

    // Calculate days active (simplified - using account age for now)
    // In a real implementation, you might track daily activity
    const daysActive = user[0].lastActive 
      ? Math.floor((user[0].lastActive.getTime() - user[0].createdAt.getTime()) / (1000 * 60 * 60 * 24))
      : accountAge;

    // Get signup order (approximate based on creation date)
    const signupOrder = await db
      .select({ count: count() })
      .from(UserSchema)
      .where(lt(UserSchema.createdAt, user[0].createdAt));

    return {
      messageCount: Number(messageCount[0].count),
      serverCount: Number(serverCount[0].count),
      friendCount: Number(friendCount[0].count),
      daysActive: Math.max(daysActive, 1), // At least 1 day
      accountAge,
      lastActive: user[0].lastActive || user[0].createdAt,
      invitesSent: Number(invitesSent[0].count),
      invitesAccepted: Number(invitesAccepted[0].count),
      feedbackSubmitted: 0, // Placeholder - no feedback table exists yet
      moderationActions: 0, // Placeholder - no moderation table exists yet
      signupOrder: Number(signupOrder[0].count) + 1, // +1 because we want 1-based indexing
      geographicRegion: undefined, // Placeholder - no geographic data tracked yet
    };
  } catch (error) {
    console.error("Error calculating user stats:", error);
    throw new Error("Failed to calculate user statistics");
  }
}

// Badge Statistics and Leaderboard

/**
 * Gets badge system statistics
 */
export async function getBadgeStats(
  db: ReturnType<typeof drizzle>,
): Promise<BadgeStats> {
  try {
    // Get total badge types
    const totalBadges = await db
      .select({ count: count() })
      .from(BadgeTypeSchema)
      .where(eq(BadgeTypeSchema.isActive, true));

    // Get total badge assignments
    const totalAssignments = await db
      .select({ count: count() })
      .from(UserBadgeSchema);

    // Get category breakdown
    const categoryBreakdown = await db
      .select({
        category: BadgeTypeSchema.category,
        count: count(),
      })
      .from(BadgeTypeSchema)
      .where(eq(BadgeTypeSchema.isActive, true))
      .groupBy(BadgeTypeSchema.category);

    // Get most popular badges
    const mostPopularBadges = await db
      .select({
        badgeType: BadgeTypeSchema,
        assignmentCount: count(UserBadgeSchema.id),
      })
      .from(BadgeTypeSchema)
      .leftJoin(UserBadgeSchema, eq(BadgeTypeSchema.id, UserBadgeSchema.badgeTypeId))
      .where(eq(BadgeTypeSchema.isActive, true))
      .groupBy(BadgeTypeSchema.id)
      .orderBy(desc(count(UserBadgeSchema.id)))
      .limit(10);

    const categoryBreakdownMap: Record<string, number> = {};
    categoryBreakdown.forEach((item) => {
      categoryBreakdownMap[item.category] = item.count;
    });

    return {
      totalBadges: totalBadges[0].count,
      totalAssignments: totalAssignments[0].count,
      categoryBreakdown: categoryBreakdownMap as any,
      mostPopularBadges: mostPopularBadges.map((item) => ({
        badgeType: {
          ...item.badgeType,
          criteria: item.badgeType.criteria ? JSON.parse(item.badgeType.criteria) : undefined,
        },
        assignmentCount: item.assignmentCount,
      })),
    };
  } catch (error) {
    console.error("Error getting badge stats:", error);
    throw new Error("Failed to get badge statistics");
  }
}

/**
 * Gets badge leaderboard
 */
export async function getBadgeLeaderboard(
  db: ReturnType<typeof drizzle>,
  limit: number = 10,
): Promise<BadgeLeaderboard[]> {
  try {
    const leaderboard = await db
      .select({
        userId: UserBadgeSchema.userId,
        username: UserSchema.username,
        badgeCount: count(UserBadgeSchema.id),
      })
      .from(UserBadgeSchema)
      .innerJoin(UserSchema, eq(UserBadgeSchema.userId, UserSchema.id))
      .where(eq(UserBadgeSchema.isVisible, true))
      .groupBy(UserBadgeSchema.userId, UserSchema.username)
      .orderBy(desc(count(UserBadgeSchema.id)))
      .limit(limit);

    // Get badges for each user in the leaderboard
    const leaderboardWithBadges = await Promise.all(
      leaderboard.map(async (entry) => {
        const badges = await getUserBadges(db, entry.userId, true);
        return {
          userId: entry.userId,
          username: entry.username,
          badgeCount: entry.badgeCount,
          badges,
        };
      })
    );

    return leaderboardWithBadges;
  } catch (error) {
    console.error("Error getting badge leaderboard:", error);
    throw new Error("Failed to get badge leaderboard");
  }
}

// Transaction Helpers

/**
 * Executes multiple badge operations in a transaction
 */
export async function executeBadgeTransaction<T>(
  db: ReturnType<typeof drizzle>,
  operations: (trx: any) => Promise<T>,
): Promise<T> {
  try {
    return await db.transaction(operations);
  } catch (error) {
    console.error("Error executing badge transaction:", error);
    throw new Error("Badge transaction failed");
  }
}

/**
 * Bulk assign badges to multiple users
 */
export async function bulkAssignBadges(
  db: ReturnType<typeof drizzle>,
  assignments: Array<{
    userId: string;
    badgeTypeId: string;
    assignedBy?: string;
  }>,
): Promise<UserBadge[]> {
  try {
    return await db.transaction(async (trx) => {
      const results: UserBadge[] = [];

      for (const assignment of assignments) {
        try {
          const result = await assignBadgeToUser(
            trx as any,
            assignment.userId,
            assignment.badgeTypeId,
            assignment.assignedBy
          );
          if (result) {
            results.push(result);
          }
        } catch (error) {
          // Log error but continue with other assignments
          console.warn(`Failed to assign badge ${assignment.badgeTypeId} to user ${assignment.userId}:`, error.message);
        }
      }

      return results;
    });
  } catch (error) {
    console.error("Error bulk assigning badges:", error);
    throw new Error("Failed to bulk assign badges");
  }
}

/**
 * Gets available badges for a user (badges they don't have yet)
 */
export async function getAvailableBadgesForUser(
  db: ReturnType<typeof drizzle>,
  userId: string,
): Promise<BadgeType[]> {
  try {
    // Get all active badge types
    const allBadges = await getBadgeTypes(db, { isActive: true });
    
    // Get user's current badges
    const userBadges = await getUserBadges(db, userId);
    const userBadgeTypeIds = new Set(userBadges.map(badge => badge.badgeTypeId));
    
    // Filter out badges the user already has
    return allBadges.filter(badge => !userBadgeTypeIds.has(badge.id));
  } catch (error) {
    console.error("Error getting available badges for user:", error);
    throw new Error("Failed to get available badges");
  }
}

/**
 * Gets badge progress for automatic badges
 */
export async function getBadgeProgress(
  db: ReturnType<typeof drizzle>,
  userId: string,
  badgeTypeId?: string,
): Promise<BadgeProgress[]> {
  try {
    // Get user stats
    const userStats = await getUserStats(db, userId);
    
    // Get automatic badges (either specific one or all)
    const filters: BadgeTypeFilters = { 
      unlockType: "automatic", 
      isActive: true 
    };
    
    let badgeTypes: BadgeType[];
    if (badgeTypeId) {
      const badgeType = await getBadgeTypeById(db, badgeTypeId);
      badgeTypes = badgeType ? [badgeType] : [];
    } else {
      badgeTypes = await getBadgeTypes(db, filters);
    }
    
    // Get user's current badges
    const userBadges = await getUserBadges(db, userId);
    const userBadgeTypeIds = new Set(userBadges.map(badge => badge.badgeTypeId));
    
    const progress: BadgeProgress[] = [];
    
    for (const badgeType of badgeTypes) {
      if (!badgeType.criteria) continue;
      
      const isEarned = userBadgeTypeIds.has(badgeType.id);
      let currentProgress = 0;
      let total = badgeType.criteria.threshold || 1;
      
      // Calculate progress based on criteria type
      switch (badgeType.criteria.type) {
        case "message_count":
          currentProgress = userStats.messageCount;
          break;
        case "server_count":
          currentProgress = userStats.serverCount;
          break;
        case "friend_count":
          currentProgress = userStats.friendCount;
          break;
        case "days_active":
          currentProgress = userStats.daysActive;
          break;
        case "custom":
          // For custom criteria, check conditions
          if (badgeType.criteria.conditions) {
            currentProgress = calculateCustomProgress(userStats, badgeType.criteria.conditions);
          }
          break;
        case "complex":
          // For complex criteria, evaluate multiple conditions
          currentProgress = calculateComplexProgress(userStats, badgeType.criteria);
          break;
        default:
          currentProgress = 0;
      }
      
      // Add collection context if badge is part of a collection
      let collectionProgress;
      if (badgeType.collectionId) {
        // This would be implemented when collections are added
        // For now, we'll leave it undefined
        collectionProgress = undefined;
      }
      
      progress.push({
        badgeTypeId: badgeType.id,
        badgeType,
        progress: Math.min(currentProgress, total),
        total,
        isEarned,
        collectionProgress,
      });
    }
    
    return progress;
  } catch (error) {
    console.error("Error getting badge progress:", error);
    throw new Error("Failed to get badge progress");
  }
}

/**
 * Calculates progress for custom criteria
 */
function calculateCustomProgress(userStats: UserStats, conditions: Record<string, any>): number {
  let progress = 0;
  
  // Handle different custom conditions
  if (conditions.signup_order && userStats.signupOrder) {
    // Early adopter badges based on signup order
    if (userStats.signupOrder <= conditions.signup_order) {
      progress = 1;
    }
  }
  
  if (conditions.invites_ratio) {
    // Invitation success rate
    const ratio = userStats.invitesAccepted / Math.max(userStats.invitesSent, 1);
    if (ratio >= conditions.invites_ratio) {
      progress = 1;
    }
  }
  
  if (conditions.account_age_days) {
    // Account age milestones
    progress = Math.min(userStats.accountAge / conditions.account_age_days, 1);
  }
  
  return progress;
}

/**
 * Calculates progress for complex criteria with multiple conditions
 */
function calculateComplexProgress(userStats: UserStats, criteria: BadgeCriteria): number {
  if (!criteria.conditions) return 0;
  
  let totalConditions = 0;
  let metConditions = 0;
  
  // Check each condition in the complex criteria
  for (const [key, value] of Object.entries(criteria.conditions)) {
    totalConditions++;
    
    switch (key) {
      case "min_messages":
        if (userStats.messageCount >= value) metConditions++;
        break;
      case "min_servers":
        if (userStats.serverCount >= value) metConditions++;
        break;
      case "min_friends":
        if (userStats.friendCount >= value) metConditions++;
        break;
      case "min_days_active":
        if (userStats.daysActive >= value) metConditions++;
        break;
      case "min_invites_sent":
        if (userStats.invitesSent >= value) metConditions++;
        break;
      case "min_account_age":
        if (userStats.accountAge >= value) metConditions++;
        break;
    }
  }
  
  // Return percentage of conditions met
  return totalConditions > 0 ? metConditions / totalConditions : 0;
}