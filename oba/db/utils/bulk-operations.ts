import { db } from "../index";
import { ChannelSchema, ChannelCategorySchema } from "../schema";
import { eq, and, sql } from "drizzle-orm";

/**
 * Updates the positions of multiple channels at once.
 * Useful when reordering channels within a category.
 *
 * @param channelPositions - Array of objects with channelId and new position
 * @returns True if successful, false otherwise
 */
export async function updateChannelPositions(
  channelPositions: Array<{ channelId: string; position: number }>,
): Promise<boolean> {
  try {
    await db.transaction(async (trx) => {
      for (const { channelId, position } of channelPositions) {
        await trx
          .update(ChannelSchema)
          .set({ position })
          .where(eq(ChannelSchema.id, channelId));
      }
    });
    return true;
  } catch (error) {
    console.error("Error updating channel positions:", error);
    return false;
  }
}

/**
 * Updates the positions of multiple categories at once.
 * Useful when reordering categories within a server.
 *
 * @param categoryPositions - Array of objects with categoryId and new position
 * @returns True if successful, false otherwise
 */
export async function updateCategoryPositions(
  categoryPositions: Array<{ categoryId: string; position: number }>,
): Promise<boolean> {
  try {
    await db.transaction(async (trx) => {
      for (const { categoryId, position } of categoryPositions) {
        await trx
          .update(ChannelCategorySchema)
          .set({ position })
          .where(eq(ChannelCategorySchema.id, categoryId));
      }
    });
    return true;
  } catch (error) {
    console.error("Error updating category positions:", error);
    return false;
  }
}

/**
 * Moves multiple channels to a category at once.
 *
 * @param channelIds - Array of channel IDs to move
 * @param categoryId - Target category ID (or null to uncategorize)
 * @returns True if successful, false otherwise
 */
export async function moveChannelsToCategory(
  channelIds: string[],
  categoryId: string | null,
): Promise<boolean> {
  try {
    await db.transaction(async (trx) => {
      for (const channelId of channelIds) {
        await trx
          .update(ChannelSchema)
          .set({ categoryId })
          .where(eq(ChannelSchema.id, channelId));
      }
    });
    return true;
  } catch (error) {
    console.error("Error moving channels to category:", error);
    return false;
  }
}

/**
 * Update the server structure with new positions and parent-child relationships
 *
 * @param serverId - The ID of the server
 * @param structure - Array of items (channels and categories) with their positions and relationships
 * @returns True if successful, false otherwise
 */
export async function updateServerStructure(
  serverId: string,
  structure: Array<{
    id: string;
    type: "channel" | "category";
    position: number;
    parentId?: string | null;
  }>,
): Promise<boolean> {
  try {
    await db.transaction(async (trx) => {
      for (const item of structure) {
        if (item.type === "channel") {
          // Update channel position and parent category
          await trx
            .update(ChannelSchema)
            .set({
              position: item.position,
              categoryId: item.parentId || null,
            })
            .where(
              and(
                eq(ChannelSchema.id, item.id),
                eq(ChannelSchema.serverId, serverId),
              ),
            );
        } else if (item.type === "category") {
          // Update category position
          await trx
            .update(ChannelCategorySchema)
            .set({ position: item.position })
            .where(
              and(
                eq(ChannelCategorySchema.id, item.id),
                eq(ChannelCategorySchema.serverId, serverId),
              ),
            );
        }
      }
    });
    return true;
  } catch (error) {
    console.error("Error updating server structure:", error);
    return false;
  }
}
