import { db } from "../index";
import { ChannelSchema, ChannelCategorySchema } from "../schema";
import { eq, and, sql } from "drizzle-orm";

/**
 * Get the next available position for any item (channel or category) in a server
 *
 * @param serverId - The ID of the server
 * @returns The next available position (max + 1)
 */
export async function getNextServerItemPosition(
  serverId: string,
): Promise<number> {
  try {
    // First, get the maximum position from channels
    const channelsResult = await db
      .select({
        maxPosition: sql<number>`COALESCE(MAX(${ChannelSchema.position}), -1)`,
      })
      .from(ChannelSchema)
      .where(eq(ChannelSchema.serverId, serverId));

    // Then, get the maximum position from categories
    const categoriesResult = await db
      .select({
        maxPosition: sql<number>`COALESCE(MAX(${ChannelCategorySchema.position}), -1)`,
      })
      .from(ChannelCategorySchema)
      .where(eq(ChannelCategorySchema.serverId, serverId));

    // Find the overall maximum position
    const maxChannelPosition = channelsResult[0]?.maxPosition ?? -1;
    const maxCategoryPosition = categoriesResult[0]?.maxPosition ?? -1;
    const maxPosition = Math.max(maxChannelPosition, maxCategoryPosition);

    // Return max + 1 (or 0 if no items exist)
    return maxPosition + 1;
  } catch (error) {
    console.error("Error getting next server item position:", error);
    return 0; // Default to 0 if there's an error
  }
}

/**
 * Get the next available position for a channel in a specific category
 * This is used when you want to maintain separate position sequences within categories
 *
 * @param serverId - The ID of the server
 * @param categoryId - The ID of the category (or null for uncategorized channels)
 * @returns The next available position (max + 1)
 */
export async function getNextChannelPositionInCategory(
  serverId: string,
  categoryId: string | null,
): Promise<number> {
  try {
    // Query to find the maximum position of channels in this category
    const result = await db
      .select({
        maxPosition: sql<number>`COALESCE(MAX(${ChannelSchema.position}), -1)`,
      })
      .from(ChannelSchema)
      .where(
        and(
          eq(ChannelSchema.serverId, serverId),
          categoryId
            ? eq(ChannelSchema.categoryId, categoryId)
            : sql`${ChannelSchema.categoryId} IS NULL`,
        ),
      );

    // Return max + 1 (or 0 if no channels exist)
    return (result[0]?.maxPosition ?? -1) + 1;
  } catch (error) {
    console.error("Error getting next channel position in category:", error);
    return 0; // Default to 0 if there's an error
  }
}

export async function getTotalChannelsAndCategories(
  serverId: string,
): Promise<number> {
  try {
    // Query to count all channels and categories in this server
    const result = await db
      .select({
        totalCount: sql<number>`COUNT(*)`.mapWith(Number),
      })
      .from(ChannelSchema)
      .where(eq(ChannelSchema.serverId, serverId));

    const channelCount = result[0]?.totalCount ?? 0;

    const categoryResult = await db
      .select({
        totalCount: sql<number>`COUNT(*)`.mapWith(Number),
      })
      .from(ChannelCategorySchema)
      .where(eq(ChannelCategorySchema.serverId, serverId));

    const categoryCount = categoryResult[0]?.totalCount ?? 0;
    console.log(
      "counts:",
      channelCount,
      categoryCount,
      channelCount + categoryCount,
    );
    return channelCount + categoryCount;
  } catch (error) {
    console.error("Error getting total channels and categories:", error);
    return 0; // Default to 0 if there's an error
  }
}

export async function getTotalChannelsInCategory(
  serverId: string,
  categoryId: string | null,
): Promise<number> {
  try {
    // Query to count all channels in this category
    const result = await db
      .select({
        totalCount: sql<number>`COUNT(*)`,
      })
      .from(ChannelSchema)
      .where(
        and(
          eq(ChannelSchema.serverId, serverId),
          categoryId
            ? eq(ChannelSchema.categoryId, categoryId)
            : sql`${ChannelSchema.categoryId} IS NULL`,
        ),
      );

    return result[0]?.totalCount ?? 0;
  } catch (error) {
    console.error("Error getting total channels in category:", error);
    return 0; // Default to 0 if there's an error
  }
}

export async function getTotalCategoriesInServer(
  serverId: string,
): Promise<number> {
  try {
    // Query to count all categories in this server
    const result = await db
      .select({
        totalCount: sql<number>`COUNT(*)`,
      })
      .from(ChannelCategorySchema)
      .where(eq(ChannelCategorySchema.serverId, serverId));

    return result[0]?.totalCount ?? 0;
  } catch (error) {
    console.error("Error getting total categories in server:", error);
    return 0; // Default to 0 if there's an error
  }
}

/**
 * Shift positions of channels to make room for a new channel at a specific position
 *
 * @param serverId - The ID of the server
 * @param categoryId - The ID of the category (or null for uncategorized channels)
 * @param startPosition - The position where the new channel will be inserted
 * @returns True if successful, false otherwise
 */
export async function shiftChannelPositions(
  serverId: string,
  categoryId: string | null,
  startPosition: number,
): Promise<boolean> {
  try {
    await db.transaction(async (trx) => {
      // Increment positions of all channels at or after the start position
      await trx
        .update(ChannelSchema)
        .set({ position: sql`${ChannelSchema.position} + 1` })
        .where(
          and(
            eq(ChannelSchema.serverId, serverId),
            categoryId
              ? eq(ChannelSchema.categoryId, categoryId)
              : sql`${ChannelSchema.categoryId} IS NULL`,
            sql`${ChannelSchema.position} >= ${startPosition}`,
          ),
        );
    });
    return true;
  } catch (error) {
    console.error("Error shifting channel positions:", error);
    return false;
  }
}

/**
 * Shift positions of categories to make room for a new category at a specific position
 *
 * @param serverId - The ID of the server
 * @param startPosition - The position where the new category will be inserted
 * @returns True if successful, false otherwise
 */
export async function shiftCategoryPositions(
  serverId: string,
  startPosition: number,
): Promise<boolean> {
  try {
    await db.transaction(async (trx) => {
      // Increment positions of all categories at or after the start position
      await trx
        .update(ChannelCategorySchema)
        .set({ position: sql`${ChannelCategorySchema.position} + 1` })
        .where(
          and(
            eq(ChannelCategorySchema.serverId, serverId),
            sql`${ChannelCategorySchema.position} >= ${startPosition}`,
          ),
        );
    });
    return true;
  } catch (error) {
    console.error("Error shifting category positions:", error);
    return false;
  }
}
