import { and, eq, or, inArray, isNull } from "drizzle-orm";
import type { drizzle } from "drizzle-orm/pg-proxy";
import {
  UserSchema,
  ServerSchema,
  ChannelSchema,
  channelTypeEnum,
  ChannelPrivacySchema,
  ChannelAllowedRolesSchema,
  ChannelAllowedUsersSchema,
  ChannelVisibleRolesSchema,
  ChannelVisibleUsersSchema,
  ChannelPermissionOverridesSchema,
  MessageSchema,
  MessageReactionSchema,
  CategoryPermissionSchema,
  ChannelCategorySchema,
  CategoryAllowedRolesSchema,
  CategoryAllowedUsersSchema,
  CategoryVisibleRolesSchema,
  CategoryVisibleUsersSchema,
  CategoryPermissionOverridesSchema,
} from "./schema";
import { db } from "./index";

/**
 * Creates a new channel with enhanced permission settings.
 *
 * @param db - The database connection to use
 * @param serverId - The ID of the server to create the channel in
 * @param name - The name of the new channel
 * @param details - Object containing channel details
 * @returns The newly created channel object, or null if an error occurred
 */
export async function createEnhancedChannel(
  db: ReturnType<typeof drizzle>,
  serverId: string,
  name: string,
  details: {
    description?: string;
    type: (typeof channelTypeEnum.enumValues)[number];
    isPublic: boolean;
    isVisible: boolean;
    allowedRoleIds?: string[];
    allowedUserIds?: string[];
    visibleRoleIds?: string[];
    visibleUserIds?: string[];
    categoryId?: string;
    position?: number;
    rolePermissionOverrides?: Array<{
      roleId: string;
      allowedPermissions: bigint;
      deniedPermissions: bigint;
    }>;
    userPermissionOverrides?: Array<{
      userId: string;
      allowedPermissions: bigint;
      deniedPermissions: bigint;
    }>;
  },
): Promise<typeof ChannelSchema.$inferSelect | null> {
  const {
    description,
    type,
    isPublic,
    isVisible,
    allowedRoleIds,
    allowedUserIds,
    visibleRoleIds,
    visibleUserIds,
    categoryId,
    position,
    rolePermissionOverrides,
    userPermissionOverrides,
  } = details;

  try {
    return await db.transaction(async (trx) => {
      // Create the channel
      const newChannel = await trx
        .insert(ChannelSchema)
        .values({
          name,
          description,
          serverId,
          categoryId,
          type,
          position: position || 0,
          createdAt: new Date(),
        })
        .returning();

      // Create privacy settings
      await trx.insert(ChannelPrivacySchema).values({
        channelId: newChannel[0].id,
        isPublic,
        isVisible,
      });

      // Add allowed roles if it's a private channel and roles are provided
      if (!isPublic && allowedRoleIds && allowedRoleIds.length > 0) {
        const allowedRolesValues = allowedRoleIds.map((roleId) => ({
          channelId: newChannel[0].id,
          roleId,
        }));
        await trx.insert(ChannelAllowedRolesSchema).values(allowedRolesValues);
      }

      // Add allowed users if it's a private channel and users are provided
      if (!isPublic && allowedUserIds && allowedUserIds.length > 0) {
        const allowedUsersValues = allowedUserIds.map((userId) => ({
          channelId: newChannel[0].id,
          userId,
        }));
        await trx.insert(ChannelAllowedUsersSchema).values(allowedUsersValues);
      }

      // Add visible roles if the channel has restricted visibility and roles are provided
      if (!isVisible && visibleRoleIds && visibleRoleIds.length > 0) {
        const visibleRolesValues = visibleRoleIds.map((roleId) => ({
          channelId: newChannel[0].id,
          roleId,
        }));
        await trx.insert(ChannelVisibleRolesSchema).values(visibleRolesValues);
      }

      // Add visible users if the channel has restricted visibility and users are provided
      if (!isVisible && visibleUserIds && visibleUserIds.length > 0) {
        const visibleUsersValues = visibleUserIds.map((userId) => ({
          channelId: newChannel[0].id,
          userId,
        }));
        await trx.insert(ChannelVisibleUsersSchema).values(visibleUsersValues);
      }

      // Add role permission overrides if provided
      if (rolePermissionOverrides && rolePermissionOverrides.length > 0) {
        const roleOverridesValues = rolePermissionOverrides.map((override) => ({
          channelId: newChannel[0].id,
          roleId: override.roleId,
          allowedPermissions: override.allowedPermissions,
          deniedPermissions: override.deniedPermissions,
        }));
        await trx
          .insert(ChannelPermissionOverridesSchema)
          .values(roleOverridesValues);
      }

      // Add user permission overrides if provided
      if (userPermissionOverrides && userPermissionOverrides.length > 0) {
        const userOverridesValues = userPermissionOverrides.map((override) => ({
          channelId: newChannel[0].id,
          userId: override.userId,
          allowedPermissions: override.allowedPermissions,
          deniedPermissions: override.deniedPermissions,
        }));
        await trx
          .insert(ChannelPermissionOverridesSchema)
          .values(userOverridesValues);
      }

      return newChannel[0];
    });
  } catch (error) {
    console.error("Error creating enhanced channel:", error);
    return null;
  }
}

/**
 * Updates an existing channel with enhanced permission settings.
 *
 * @param db - The database connection to use
 * @param channelId - The ID of the channel to update
 * @param updates - Object containing the fields to update
 * @returns The updated channel object, or null if the channel is not found or an error occurred
 */
export async function updateEnhancedChannel(
  db: ReturnType<typeof drizzle>,
  channelId: string,
  updates: {
    name?: string;
    description?: string;
    type?: (typeof channelTypeEnum.enumValues)[number];
    isPublic?: boolean;
    isVisible?: boolean;
    allowedRoleIds?: string[];
    allowedUserIds?: string[];
    visibleRoleIds?: string[];
    visibleUserIds?: string[];
    categoryId?: string;
    position?: number;
    rolePermissionOverrides?: Array<{
      roleId: string;
      allowedPermissions: bigint;
      deniedPermissions: bigint;
    }>;
    userPermissionOverrides?: Array<{
      userId: string;
      allowedPermissions: bigint;
      deniedPermissions: bigint;
    }>;
  },
): Promise<typeof ChannelSchema.$inferSelect | null> {
  try {
    return await db.transaction(async (trx) => {
      // Check if channel exists
      const existingChannel = await trx
        .select()
        .from(ChannelSchema)
        .where(eq(ChannelSchema.id, channelId));

      if (existingChannel.length === 0) {
        return null; // Channel not found
      }

      // Create updates object with only the fields that are provided
      const channelUpdates: {
        name?: string;
        description?: string;
        type?: (typeof channelTypeEnum.enumValues)[number];
        categoryId?: string;
        position?: number;
      } = {};

      if (updates.name !== undefined) channelUpdates.name = updates.name;
      if (updates.description !== undefined)
        channelUpdates.description = updates.description;
      if (updates.type !== undefined) channelUpdates.type = updates.type;
      if (updates.categoryId !== undefined)
        channelUpdates.categoryId = updates.categoryId;
      if (updates.position !== undefined)
        channelUpdates.position = updates.position;

      // Update the channel if there are any updates
      if (Object.keys(channelUpdates).length > 0) {
        await trx
          .update(ChannelSchema)
          .set(channelUpdates)
          .where(eq(ChannelSchema.id, channelId));
      }

      // Update privacy settings if specified
      if (updates.isPublic !== undefined || updates.isVisible !== undefined) {
        // Check if privacy settings exist
        const existingPrivacy = await trx
          .select()
          .from(ChannelPrivacySchema)
          .where(eq(ChannelPrivacySchema.channelId, channelId));

        if (existingPrivacy.length > 0) {
          // Update existing privacy settings
          const privacyUpdates: {
            isPublic?: boolean;
            isVisible?: boolean;
          } = {};

          if (updates.isPublic !== undefined)
            privacyUpdates.isPublic = updates.isPublic;
          if (updates.isVisible !== undefined)
            privacyUpdates.isVisible = updates.isVisible;

          await trx
            .update(ChannelPrivacySchema)
            .set(privacyUpdates)
            .where(eq(ChannelPrivacySchema.channelId, channelId));
        } else {
          // Create new privacy settings
          await trx.insert(ChannelPrivacySchema).values({
            channelId,
            isPublic: updates.isPublic ?? true,
            isVisible: updates.isVisible ?? true,
          });
        }
      }

      // Update allowed roles if specified
      if (updates.allowedRoleIds !== undefined) {
        // Delete existing allowed roles
        await trx
          .delete(ChannelAllowedRolesSchema)
          .where(eq(ChannelAllowedRolesSchema.channelId, channelId));

        // Add new allowed roles if any are provided
        if (updates.allowedRoleIds.length > 0) {
          const allowedRolesValues = updates.allowedRoleIds.map((roleId) => ({
            channelId,
            roleId,
          }));
          await trx
            .insert(ChannelAllowedRolesSchema)
            .values(allowedRolesValues);
        }
      }

      // Update allowed users if specified
      if (updates.allowedUserIds !== undefined) {
        // Delete existing allowed users
        await trx
          .delete(ChannelAllowedUsersSchema)
          .where(eq(ChannelAllowedUsersSchema.channelId, channelId));

        // Add new allowed users if any are provided
        if (updates.allowedUserIds.length > 0) {
          const allowedUsersValues = updates.allowedUserIds.map((userId) => ({
            channelId,
            userId,
          }));
          await trx
            .insert(ChannelAllowedUsersSchema)
            .values(allowedUsersValues);
        }
      }

      // Update visible roles if specified
      if (updates.visibleRoleIds !== undefined) {
        // Delete existing visible roles
        await trx
          .delete(ChannelVisibleRolesSchema)
          .where(eq(ChannelVisibleRolesSchema.channelId, channelId));

        // Add new visible roles if any are provided
        if (updates.visibleRoleIds.length > 0) {
          const visibleRolesValues = updates.visibleRoleIds.map((roleId) => ({
            channelId,
            roleId,
          }));
          await trx
            .insert(ChannelVisibleRolesSchema)
            .values(visibleRolesValues);
        }
      }

      // Update visible users if specified
      if (updates.visibleUserIds !== undefined) {
        // Delete existing visible users
        await trx
          .delete(ChannelVisibleUsersSchema)
          .where(eq(ChannelVisibleUsersSchema.channelId, channelId));

        // Add new visible users if any are provided
        if (updates.visibleUserIds.length > 0) {
          const visibleUsersValues = updates.visibleUserIds.map((userId) => ({
            channelId,
            userId,
          }));
          await trx
            .insert(ChannelVisibleUsersSchema)
            .values(visibleUsersValues);
        }
      }

      // Update role permission overrides if specified
      if (updates.rolePermissionOverrides !== undefined) {
        // Delete existing role permission overrides
        await trx
          .delete(ChannelPermissionOverridesSchema)
          .where(
            and(
              eq(ChannelPermissionOverridesSchema.channelId, channelId),
              isNull(ChannelPermissionOverridesSchema.userId),
            ),
          );

        // Add new role permission overrides if any are provided
        if (updates.rolePermissionOverrides.length > 0) {
          const roleOverridesValues = updates.rolePermissionOverrides.map(
            (override) => ({
              channelId,
              roleId: override.roleId,
              allowedPermissions: override.allowedPermissions,
              deniedPermissions: override.deniedPermissions,
            }),
          );
          await trx
            .insert(ChannelPermissionOverridesSchema)
            .values(roleOverridesValues);
        }
      }

      // Update user permission overrides if specified
      if (updates.userPermissionOverrides !== undefined) {
        // Delete existing user permission overrides
        await trx
          .delete(ChannelPermissionOverridesSchema)
          .where(
            and(
              eq(ChannelPermissionOverridesSchema.channelId, channelId),
              isNull(ChannelPermissionOverridesSchema.roleId),
            ),
          );

        // Add new user permission overrides if any are provided
        if (updates.userPermissionOverrides.length > 0) {
          const userOverridesValues = updates.userPermissionOverrides.map(
            (override) => ({
              channelId,
              userId: override.userId,
              allowedPermissions: override.allowedPermissions,
              deniedPermissions: override.deniedPermissions,
            }),
          );
          await trx
            .insert(ChannelPermissionOverridesSchema)
            .values(userOverridesValues);
        }
      }

      // Return the updated channel
      const updatedChannel = await trx
        .select()
        .from(ChannelSchema)
        .where(eq(ChannelSchema.id, channelId));

      return updatedChannel[0];
    });
  } catch (error) {
    console.error("Error updating enhanced channel:", error);
    return null;
  }
}

/**
 * Gets detailed information about a channel, including privacy settings, allowed roles, allowed users, and permission overrides.
 *
 * @param db - The database connection to use
 * @param channelId - The ID of the channel to get details for
 * @returns Detailed channel information, or null if the channel is not found or an error occurred
 */
export async function getEnhancedChannelDetails(
  db: ReturnType<typeof drizzle>,
  channelId: string,
): Promise<any | null> {
  try {
    // Get the channel
    const channel = await db
      .select()
      .from(ChannelSchema)
      .where(eq(ChannelSchema.id, channelId))
      .limit(1);

    if (channel.length === 0) {
      return null; // Channel not found
    }

    // Get privacy settings
    const privacySettings = await db
      .select()
      .from(ChannelPrivacySchema)
      .where(eq(ChannelPrivacySchema.channelId, channelId))
      .limit(1);

    // Get allowed roles
    const allowedRoles = await db
      .select({
        id: ChannelAllowedRolesSchema.id,
        channelId: ChannelAllowedRolesSchema.channelId,
        roleId: ChannelAllowedRolesSchema.roleId,
      })
      .from(ChannelAllowedRolesSchema)
      .where(eq(ChannelAllowedRolesSchema.channelId, channelId));

    // Get allowed users
    const allowedUsers = await db
      .select({
        id: ChannelAllowedUsersSchema.id,
        channelId: ChannelAllowedUsersSchema.channelId,
        userId: ChannelAllowedUsersSchema.userId,
      })
      .from(ChannelAllowedUsersSchema)
      .where(eq(ChannelAllowedUsersSchema.channelId, channelId));

    // Get visible roles
    const visibleRoles = await db
      .select({
        id: ChannelVisibleRolesSchema.id,
        channelId: ChannelVisibleRolesSchema.channelId,
        roleId: ChannelVisibleRolesSchema.roleId,
      })
      .from(ChannelVisibleRolesSchema)
      .where(eq(ChannelVisibleRolesSchema.channelId, channelId));

    // Get visible users
    const visibleUsers = await db
      .select({
        id: ChannelVisibleUsersSchema.id,
        channelId: ChannelVisibleUsersSchema.channelId,
        userId: ChannelVisibleUsersSchema.userId,
      })
      .from(ChannelVisibleUsersSchema)
      .where(eq(ChannelVisibleUsersSchema.channelId, channelId));

    // Get role permission overrides
    const rolePermissionOverrides = await db
      .select({
        id: ChannelPermissionOverridesSchema.id,
        channelId: ChannelPermissionOverridesSchema.channelId,
        roleId: ChannelPermissionOverridesSchema.roleId,
        allowedPermissions: ChannelPermissionOverridesSchema.allowedPermissions,
        deniedPermissions: ChannelPermissionOverridesSchema.deniedPermissions,
      })
      .from(ChannelPermissionOverridesSchema)
      .where(
        and(
          eq(ChannelPermissionOverridesSchema.channelId, channelId),
          isNull(ChannelPermissionOverridesSchema.userId),
        ),
      );

    // Get user permission overrides
    const userPermissionOverrides = await db
      .select({
        id: ChannelPermissionOverridesSchema.id,
        channelId: ChannelPermissionOverridesSchema.channelId,
        userId: ChannelPermissionOverridesSchema.userId,
        allowedPermissions: ChannelPermissionOverridesSchema.allowedPermissions,
        deniedPermissions: ChannelPermissionOverridesSchema.deniedPermissions,
      })
      .from(ChannelPermissionOverridesSchema)
      .where(
        and(
          eq(ChannelPermissionOverridesSchema.channelId, channelId),
          isNull(ChannelPermissionOverridesSchema.roleId),
        ),
      );

    // Get category information if the channel belongs to a category
    let categoryPermissions = null;
    if (channel[0].categoryId) {
      const categoryPermission = await db
        .select()
        .from(CategoryPermissionSchema)
        .where(eq(CategoryPermissionSchema.categoryId, channel[0].categoryId))
        .limit(1);

      if (categoryPermission.length > 0) {
        categoryPermissions = categoryPermission[0];
      }
    }

    // Combine the results
    return {
      ...channel[0],
      privacySettings: privacySettings.length > 0 ? privacySettings[0] : null,
      allowedRoles,
      allowedUsers,
      visibleRoles,
      visibleUsers,
      rolePermissionOverrides,
      userPermissionOverrides,
      categoryPermissions,
    };
  } catch (error) {
    console.error("Error fetching enhanced channel details:", error);
    return null;
  }
}

/**
 * Creates a new category with enhanced permission settings.
 *
 * @param db - The database connection to use
 * @param serverId - The ID of the server to create the category in
 * @param name - The name of the new category
 * @param details - Object containing category details
 * @returns The newly created category object, or null if an error occurred
 */
export async function createEnhancedCategory(
  db: ReturnType<typeof drizzle>,
  serverId: string,
  name: string,
  details: {
    description?: string;
    position?: number;
    isPrivate: boolean;
    isVisible: boolean;
    allowedRoleIds?: string[];
    allowedUserIds?: string[];
    visibleRoleIds?: string[];
    visibleUserIds?: string[];
    rolePermissionOverrides?: Array<{
      roleId: string;
      allowedPermissions: bigint;
      deniedPermissions: bigint;
    }>;
    userPermissionOverrides?: Array<{
      userId: string;
      allowedPermissions: bigint;
      deniedPermissions: bigint;
    }>;
  },
): Promise<typeof ChannelCategorySchema.$inferSelect | null> {
  const {
    description,
    position,
    isPrivate,
    isVisible,
    allowedRoleIds,
    allowedUserIds,
    visibleRoleIds,
    visibleUserIds,
    rolePermissionOverrides,
    userPermissionOverrides,
  } = details;

  try {
    return await db.transaction(async (trx) => {
      // Create the category
      const newCategory = await trx
        .insert(ChannelCategorySchema)
        .values({
          name,
          description,
          serverId,
          position: position || 0,
          createdAt: new Date(),
        })
        .returning();

      // Create privacy settings
      await trx.insert(CategoryPermissionSchema).values({
        categoryId: newCategory[0].id,
        isPrivate,
        isVisible,
      });

      // Add allowed roles if it's a private category and roles are provided
      if (isPrivate && allowedRoleIds && allowedRoleIds.length > 0) {
        const allowedRolesValues = allowedRoleIds.map((roleId) => ({
          categoryId: newCategory[0].id,
          roleId,
        }));
        await trx.insert(CategoryAllowedRolesSchema).values(allowedRolesValues);
      }

      // Add allowed users if it's a private category and users are provided
      if (isPrivate && allowedUserIds && allowedUserIds.length > 0) {
        const allowedUsersValues = allowedUserIds.map((userId) => ({
          categoryId: newCategory[0].id,
          userId,
        }));
        await trx.insert(CategoryAllowedUsersSchema).values(allowedUsersValues);
      }

      // Add visible roles if the category has restricted visibility and roles are provided
      if (!isVisible && visibleRoleIds && visibleRoleIds.length > 0) {
        const visibleRolesValues = visibleRoleIds.map((roleId) => ({
          categoryId: newCategory[0].id,
          roleId,
        }));
        await trx.insert(CategoryVisibleRolesSchema).values(visibleRolesValues);
      }

      // Add visible users if the category has restricted visibility and users are provided
      if (!isVisible && visibleUserIds && visibleUserIds.length > 0) {
        const visibleUsersValues = visibleUserIds.map((userId) => ({
          categoryId: newCategory[0].id,
          userId,
        }));
        await trx.insert(CategoryVisibleUsersSchema).values(visibleUsersValues);
      }

      // Add role permission overrides if provided
      if (rolePermissionOverrides && rolePermissionOverrides.length > 0) {
        const roleOverridesValues = rolePermissionOverrides.map((override) => ({
          categoryId: newCategory[0].id,
          roleId: override.roleId,
          allowedPermissions: override.allowedPermissions,
          deniedPermissions: override.deniedPermissions,
        }));
        await trx
          .insert(CategoryPermissionOverridesSchema)
          .values(roleOverridesValues);
      }

      // Add user permission overrides if provided
      if (userPermissionOverrides && userPermissionOverrides.length > 0) {
        const userOverridesValues = userPermissionOverrides.map((override) => ({
          categoryId: newCategory[0].id,
          userId: override.userId,
          allowedPermissions: override.allowedPermissions,
          deniedPermissions: override.deniedPermissions,
        }));
        await trx
          .insert(CategoryPermissionOverridesSchema)
          .values(userOverridesValues);
      }

      return newCategory[0];
    });
  } catch (error) {
    console.error("Error creating enhanced category:", error);
    return null;
  }
}

/**
 * Updates an existing category with enhanced permission settings.
 *
 * @param db - The database connection to use
 * @param categoryId - The ID of the category to update
 * @param updates - Object containing the fields to update
 * @returns The updated category object, or null if the category is not found or an error occurred
 */
export async function updateEnhancedCategory(
  db: ReturnType<typeof drizzle>,
  categoryId: string,
  updates: {
    name?: string;
    description?: string;
    position?: number;
    isPrivate?: boolean;
    isVisible?: boolean;
    allowedRoleIds?: string[];
    allowedUserIds?: string[];
    visibleRoleIds?: string[];
    visibleUserIds?: string[];
    rolePermissionOverrides?: Array<{
      roleId: string;
      allowedPermissions: bigint;
      deniedPermissions: bigint;
    }>;
    userPermissionOverrides?: Array<{
      userId: string;
      allowedPermissions: bigint;
      deniedPermissions: bigint;
    }>;
  },
): Promise<typeof ChannelCategorySchema.$inferSelect | null> {
  try {
    return await db.transaction(async (trx) => {
      // Check if category exists
      const existingCategory = await trx
        .select()
        .from(ChannelCategorySchema)
        .where(eq(ChannelCategorySchema.id, categoryId));

      if (existingCategory.length === 0) {
        return null; // Category not found
      }

      // Create updates object with only the fields that are provided
      const categoryUpdates: {
        name?: string;
        description?: string;
        position?: number;
      } = {};

      if (updates.name !== undefined) categoryUpdates.name = updates.name;
      if (updates.description !== undefined)
        categoryUpdates.description = updates.description;
      if (updates.position !== undefined)
        categoryUpdates.position = updates.position;

      // Update the category if there are any updates
      if (Object.keys(categoryUpdates).length > 0) {
        await trx
          .update(ChannelCategorySchema)
          .set(categoryUpdates)
          .where(eq(ChannelCategorySchema.id, categoryId));
      }

      // Update privacy settings if specified
      if (updates.isPrivate !== undefined || updates.isVisible !== undefined) {
        // Check if privacy settings exist
        const existingPrivacy = await trx
          .select()
          .from(CategoryPermissionSchema)
          .where(eq(CategoryPermissionSchema.categoryId, categoryId));

        if (existingPrivacy.length > 0) {
          // Update existing privacy settings
          const privacyUpdates: {
            isPrivate?: boolean;
            isVisible?: boolean;
          } = {};

          if (updates.isPrivate !== undefined)
            privacyUpdates.isPrivate = updates.isPrivate;
          if (updates.isVisible !== undefined)
            privacyUpdates.isVisible = updates.isVisible;

          await trx
            .update(CategoryPermissionSchema)
            .set(privacyUpdates)
            .where(eq(CategoryPermissionSchema.categoryId, categoryId));
        } else {
          // Create new privacy settings
          await trx.insert(CategoryPermissionSchema).values({
            categoryId,
            isPrivate: updates.isPrivate ?? false,
            isVisible: updates.isVisible ?? true,
          });
        }
      }

      // Update allowed roles if specified
      if (updates.allowedRoleIds !== undefined) {
        // Delete existing allowed roles
        await trx
          .delete(CategoryAllowedRolesSchema)
          .where(eq(CategoryAllowedRolesSchema.categoryId, categoryId));

        // Add new allowed roles if any are provided
        if (updates.allowedRoleIds.length > 0) {
          const allowedRolesValues = updates.allowedRoleIds.map((roleId) => ({
            categoryId,
            roleId,
          }));
          await trx
            .insert(CategoryAllowedRolesSchema)
            .values(allowedRolesValues);
        }
      }

      // Update allowed users if specified
      if (updates.allowedUserIds !== undefined) {
        // Delete existing allowed users
        await trx
          .delete(CategoryAllowedUsersSchema)
          .where(eq(CategoryAllowedUsersSchema.categoryId, categoryId));

        // Add new allowed users if any are provided
        if (updates.allowedUserIds.length > 0) {
          const allowedUsersValues = updates.allowedUserIds.map((userId) => ({
            categoryId,
            userId,
          }));
          await trx
            .insert(CategoryAllowedUsersSchema)
            .values(allowedUsersValues);
        }
      }

      // Update visible roles if specified
      if (updates.visibleRoleIds !== undefined) {
        // Delete existing visible roles
        await trx
          .delete(CategoryVisibleRolesSchema)
          .where(eq(CategoryVisibleRolesSchema.categoryId, categoryId));

        // Add new visible roles if any are provided
        if (updates.visibleRoleIds.length > 0) {
          const visibleRolesValues = updates.visibleRoleIds.map((roleId) => ({
            categoryId,
            roleId,
          }));
          await trx
            .insert(CategoryVisibleRolesSchema)
            .values(visibleRolesValues);
        }
      }

      // Update visible users if specified
      if (updates.visibleUserIds !== undefined) {
        // Delete existing visible users
        await trx
          .delete(CategoryVisibleUsersSchema)
          .where(eq(CategoryVisibleUsersSchema.categoryId, categoryId));

        // Add new visible users if any are provided
        if (updates.visibleUserIds.length > 0) {
          const visibleUsersValues = updates.visibleUserIds.map((userId) => ({
            categoryId,
            userId,
          }));
          await trx
            .insert(CategoryVisibleUsersSchema)
            .values(visibleUsersValues);
        }
      }

      // Update role permission overrides if specified
      if (updates.rolePermissionOverrides !== undefined) {
        // Delete existing role permission overrides
        await trx
          .delete(CategoryPermissionOverridesSchema)
          .where(
            and(
              eq(CategoryPermissionOverridesSchema.categoryId, categoryId),
              isNull(CategoryPermissionOverridesSchema.userId),
            ),
          );

        // Add new role permission overrides if any are provided
        if (updates.rolePermissionOverrides.length > 0) {
          const roleOverridesValues = updates.rolePermissionOverrides.map(
            (override) => ({
              categoryId,
              roleId: override.roleId,
              allowedPermissions: override.allowedPermissions,
              deniedPermissions: override.deniedPermissions,
            }),
          );
          await trx
            .insert(CategoryPermissionOverridesSchema)
            .values(roleOverridesValues);
        }
      }

      // Update user permission overrides if specified
      if (updates.userPermissionOverrides !== undefined) {
        // Delete existing user permission overrides
        await trx
          .delete(CategoryPermissionOverridesSchema)
          .where(
            and(
              eq(CategoryPermissionOverridesSchema.categoryId, categoryId),
              isNull(CategoryPermissionOverridesSchema.roleId),
            ),
          );

        // Add new user permission overrides if any are provided
        if (updates.userPermissionOverrides.length > 0) {
          const userOverridesValues = updates.userPermissionOverrides.map(
            (override) => ({
              categoryId,
              userId: override.userId,
              allowedPermissions: override.allowedPermissions,
              deniedPermissions: override.deniedPermissions,
            }),
          );
          await trx
            .insert(CategoryPermissionOverridesSchema)
            .values(userOverridesValues);
        }
      }

      // Return the updated category
      const updatedCategory = await trx
        .select()
        .from(ChannelCategorySchema)
        .where(eq(ChannelCategorySchema.id, categoryId));

      return updatedCategory[0];
    });
  } catch (error) {
    console.error("Error updating enhanced category:", error);
    return null;
  }
}

/**
 * Gets detailed information about a category, including privacy settings, allowed roles, allowed users, and permission overrides.
 *
 * @param db - The database connection to use
 * @param categoryId - The ID of the category to get details for
 * @returns Detailed category information, or null if the category is not found or an error occurred
 */
export async function getEnhancedCategoryDetails(
  db: ReturnType<typeof drizzle>,
  categoryId: string,
): Promise<any | null> {
  try {
    // Get the category
    const category = await db
      .select()
      .from(ChannelCategorySchema)
      .where(eq(ChannelCategorySchema.id, categoryId))
      .limit(1);

    if (category.length === 0) {
      return null; // Category not found
    }

    // Get privacy settings
    const privacySettings = await db
      .select()
      .from(CategoryPermissionSchema)
      .where(eq(CategoryPermissionSchema.categoryId, categoryId))
      .limit(1);

    // Get allowed roles
    const allowedRoles = await db
      .select({
        id: CategoryAllowedRolesSchema.id,
        categoryId: CategoryAllowedRolesSchema.categoryId,
        roleId: CategoryAllowedRolesSchema.roleId,
      })
      .from(CategoryAllowedRolesSchema)
      .where(eq(CategoryAllowedRolesSchema.categoryId, categoryId));

    // Get allowed users
    const allowedUsers = await db
      .select({
        id: CategoryAllowedUsersSchema.id,
        categoryId: CategoryAllowedUsersSchema.categoryId,
        userId: CategoryAllowedUsersSchema.userId,
      })
      .from(CategoryAllowedUsersSchema)
      .where(eq(CategoryAllowedUsersSchema.categoryId, categoryId));

    // Get visible roles
    const visibleRoles = await db
      .select({
        id: CategoryVisibleRolesSchema.id,
        categoryId: CategoryVisibleRolesSchema.categoryId,
        roleId: CategoryVisibleRolesSchema.roleId,
      })
      .from(CategoryVisibleRolesSchema)
      .where(eq(CategoryVisibleRolesSchema.categoryId, categoryId));

    // Get visible users
    const visibleUsers = await db
      .select({
        id: CategoryVisibleUsersSchema.id,
        categoryId: CategoryVisibleUsersSchema.categoryId,
        userId: CategoryVisibleUsersSchema.userId,
      })
      .from(CategoryVisibleUsersSchema)
      .where(eq(CategoryVisibleUsersSchema.categoryId, categoryId));

    // Get role permission overrides
    const rolePermissionOverrides = await db
      .select({
        id: CategoryPermissionOverridesSchema.id,
        categoryId: CategoryPermissionOverridesSchema.categoryId,
        roleId: CategoryPermissionOverridesSchema.roleId,
        allowedPermissions:
          CategoryPermissionOverridesSchema.allowedPermissions,
        deniedPermissions: CategoryPermissionOverridesSchema.deniedPermissions,
      })
      .from(CategoryPermissionOverridesSchema)
      .where(
        and(
          eq(CategoryPermissionOverridesSchema.categoryId, categoryId),
          isNull(CategoryPermissionOverridesSchema.userId),
        ),
      );

    // Get user permission overrides
    const userPermissionOverrides = await db
      .select({
        id: CategoryPermissionOverridesSchema.id,
        categoryId: CategoryPermissionOverridesSchema.categoryId,
        userId: CategoryPermissionOverridesSchema.userId,
        allowedPermissions:
          CategoryPermissionOverridesSchema.allowedPermissions,
        deniedPermissions: CategoryPermissionOverridesSchema.deniedPermissions,
      })
      .from(CategoryPermissionOverridesSchema)
      .where(
        and(
          eq(CategoryPermissionOverridesSchema.categoryId, categoryId),
          isNull(CategoryPermissionOverridesSchema.roleId),
        ),
      );

    // Get channels in this category
    const channels = await db
      .select()
      .from(ChannelSchema)
      .where(eq(ChannelSchema.categoryId, categoryId))
      .orderBy(ChannelSchema.position);

    // Combine the results
    return {
      ...category[0],
      privacySettings: privacySettings.length > 0 ? privacySettings[0] : null,
      allowedRoles,
      allowedUsers,
      visibleRoles,
      visibleUsers,
      rolePermissionOverrides,
      userPermissionOverrides,
      channels,
    };
  } catch (error) {
    console.error("Error fetching enhanced category details:", error);
    return null;
  }
}
