import { eq, and, desc, count, sql } from "drizzle-orm";
import { db } from "../db/index";
import { 
  BadgeNominationSchema, 
  BadgeTypeSchema, 
  UserSchema,
  UserBadgeSchema 
} from "../db/schema";
import { 
  BadgeNomination, 
  CreateNominationRequest,
  BadgeType,
  UserBadge
} from "../types/badge.types";
import { BadgeError } from "../class/badge-errors";
import { BadgeService } from "./badge.service";
import { WebSocketManager } from "../manager/websocket.manager";

export class BadgeNominationService {
  private badgeService: BadgeService;
  private wsManager: WebSocketManager;

  constructor(badgeService: BadgeService, wsManager: WebSocketManager) {
    this.badgeService = badgeService;
    this.wsManager = wsManager;
  }

  /**
   * Submit a nomination for a peer-voted badge
   */
  async submitNomination(
    nominatorUserId: string,
    request: CreateNominationRequest
  ): Promise<BadgeNomination> {
    // Validate badge type exists and supports peer voting
    const badgeType = await this.validateBadgeForNomination(request.badgeTypeId);
    
    // Check if nominee exists
    await this.validateNominee(request.nomineeUserId);
    
    // Check if nominator can nominate (not self-nomination)
    if (nominatorUserId === request.nomineeUserId) {
      throw new BadgeError("Cannot nominate yourself", "SELF_NOMINATION_NOT_ALLOWED");
    }
    
    // Check if nomination already exists
    await this.checkExistingNomination(
      request.badgeTypeId, 
      request.nomineeUserId, 
      nominatorUserId
    );
    
    // Check if nominee already has this badge
    await this.checkExistingBadge(request.badgeTypeId, request.nomineeUserId);
    
    // Create nomination
    const [nomination] = await db
      .insert(BadgeNominationSchema)
      .values({
        badgeTypeId: request.badgeTypeId,
        nomineeUserId: request.nomineeUserId,
        nominatorUserId: nominatorUserId,
        nominationReason: request.nominationReason,
        status: "pending",
      })
      .returning();

    // Get full nomination with badge type info
    const fullNomination = await this.getNominationById(nomination.id);
    
    // Check if nomination threshold is met and auto-approve if needed
    await this.checkNominationThreshold(request.badgeTypeId, request.nomineeUserId);
    
    // Send WebSocket notification to nominee
    this.wsManager.sendToUser(request.nomineeUserId, {
      type: "BADGE_NOMINATED",
      payload: {
        nominationId: fullNomination.id,
        nomination: fullNomination,
        nomineeUserId: request.nomineeUserId,
        nominatorUserId: nominatorUserId,
        badgeType: badgeType
      }
    });
    
    return fullNomination;
  }

  /**
   * Get nominations for a specific user (as nominee)
   */
  async getNominationsForUser(
    userId: string,
    status?: 'pending' | 'approved' | 'rejected'
  ): Promise<BadgeNomination[]> {
    let whereCondition = eq(BadgeNominationSchema.nomineeUserId, userId);
    
    if (status) {
      whereCondition = and(
        eq(BadgeNominationSchema.nomineeUserId, userId),
        eq(BadgeNominationSchema.status, status)
      );
    }

    const results = await db
      .select({
        id: BadgeNominationSchema.id,
        badgeTypeId: BadgeNominationSchema.badgeTypeId,
        nomineeUserId: BadgeNominationSchema.nomineeUserId,
        nominatorUserId: BadgeNominationSchema.nominatorUserId,
        nominationReason: BadgeNominationSchema.nominationReason,
        status: BadgeNominationSchema.status,
        createdAt: BadgeNominationSchema.createdAt,
        processedAt: BadgeNominationSchema.processedAt,
        badgeType: {
          id: BadgeTypeSchema.id,
          name: BadgeTypeSchema.name,
          title: BadgeTypeSchema.title,
          description: BadgeTypeSchema.description,
          icon: BadgeTypeSchema.icon,
          category: BadgeTypeSchema.category,
        }
      })
      .from(BadgeNominationSchema)
      .leftJoin(BadgeTypeSchema, eq(BadgeNominationSchema.badgeTypeId, BadgeTypeSchema.id))
      .where(whereCondition)
      .orderBy(desc(BadgeNominationSchema.createdAt));
    
    return results.map(row => ({
      id: row.id,
      badgeTypeId: row.badgeTypeId,
      nomineeUserId: row.nomineeUserId,
      nominatorUserId: row.nominatorUserId,
      nominationReason: row.nominationReason,
      status: row.status as 'pending' | 'approved' | 'rejected',
      createdAt: row.createdAt,
      processedAt: row.processedAt,
      badgeType: row.badgeType ? {
        id: row.badgeType.id,
        name: row.badgeType.name,
        title: row.badgeType.title,
        description: row.badgeType.description,
        icon: row.badgeType.icon,
        category: row.badgeType.category,
      } : undefined
    }));
  }

  /**
   * Get nominations submitted by a user (as nominator)
   */
  async getNominationsByUser(
    nominatorUserId: string,
    status?: 'pending' | 'approved' | 'rejected'
  ): Promise<BadgeNomination[]> {
    let whereCondition = eq(BadgeNominationSchema.nominatorUserId, nominatorUserId);
    
    if (status) {
      whereCondition = and(
        eq(BadgeNominationSchema.nominatorUserId, nominatorUserId),
        eq(BadgeNominationSchema.status, status)
      );
    }

    const results = await db
      .select({
        id: BadgeNominationSchema.id,
        badgeTypeId: BadgeNominationSchema.badgeTypeId,
        nomineeUserId: BadgeNominationSchema.nomineeUserId,
        nominatorUserId: BadgeNominationSchema.nominatorUserId,
        nominationReason: BadgeNominationSchema.nominationReason,
        status: BadgeNominationSchema.status,
        createdAt: BadgeNominationSchema.createdAt,
        processedAt: BadgeNominationSchema.processedAt,
        badgeType: {
          id: BadgeTypeSchema.id,
          name: BadgeTypeSchema.name,
          title: BadgeTypeSchema.title,
          description: BadgeTypeSchema.description,
          icon: BadgeTypeSchema.icon,
          category: BadgeTypeSchema.category,
        }
      })
      .from(BadgeNominationSchema)
      .leftJoin(BadgeTypeSchema, eq(BadgeNominationSchema.badgeTypeId, BadgeTypeSchema.id))
      .where(whereCondition)
      .orderBy(desc(BadgeNominationSchema.createdAt));
    
    return results.map(row => ({
      id: row.id,
      badgeTypeId: row.badgeTypeId,
      nomineeUserId: row.nomineeUserId,
      nominatorUserId: row.nominatorUserId,
      nominationReason: row.nominationReason,
      status: row.status as 'pending' | 'approved' | 'rejected',
      createdAt: row.createdAt,
      processedAt: row.processedAt,
      badgeType: row.badgeType ? {
        id: row.badgeType.id,
        name: row.badgeType.name,
        title: row.badgeType.title,
        description: row.badgeType.description,
        icon: row.badgeType.icon,
        category: row.badgeType.category,
      } : undefined
    }));
  }

  /**
   * Get nomination statistics for a badge type
   */
  async getNominationStats(badgeTypeId: string): Promise<{
    totalNominations: number;
    pendingNominations: number;
    approvedNominations: number;
    rejectedNominations: number;
    uniqueNominees: number;
    uniqueNominators: number;
  }> {
    const [stats] = await db
      .select({
        totalNominations: count(),
        pendingNominations: count(sql`CASE WHEN status = 'pending' THEN 1 END`),
        approvedNominations: count(sql`CASE WHEN status = 'approved' THEN 1 END`),
        rejectedNominations: count(sql`CASE WHEN status = 'rejected' THEN 1 END`),
        uniqueNominees: sql<number>`COUNT(DISTINCT nominee_user_id)`,
        uniqueNominators: sql<number>`COUNT(DISTINCT nominator_user_id)`,
      })
      .from(BadgeNominationSchema)
      .where(eq(BadgeNominationSchema.badgeTypeId, badgeTypeId));

    return {
      totalNominations: stats.totalNominations,
      pendingNominations: stats.pendingNominations,
      approvedNominations: stats.approvedNominations,
      rejectedNominations: stats.rejectedNominations,
      uniqueNominees: stats.uniqueNominees,
      uniqueNominators: stats.uniqueNominators,
    };
  }

  /**
   * Get nomination count for a specific nominee and badge type
   */
  async getNominationCount(badgeTypeId: string, nomineeUserId: string): Promise<number> {
    const [result] = await db
      .select({ count: count() })
      .from(BadgeNominationSchema)
      .where(
        and(
          eq(BadgeNominationSchema.badgeTypeId, badgeTypeId),
          eq(BadgeNominationSchema.nomineeUserId, nomineeUserId),
          eq(BadgeNominationSchema.status, "pending")
        )
      );

    return result.count;
  }

  /**
   * Process nomination approval (admin function)
   */
  async approveNomination(nominationId: string, adminUserId: string): Promise<UserBadge> {
    const nomination = await this.getNominationById(nominationId);
    
    if (nomination.status !== "pending") {
      throw new BadgeError("Nomination is not pending", "NOMINATION_NOT_PENDING");
    }

    // Update nomination status
    await db
      .update(BadgeNominationSchema)
      .set({
        status: "approved",
        processedAt: new Date(),
      })
      .where(eq(BadgeNominationSchema.id, nominationId));

    // Assign the badge
    const userBadge = await this.badgeService.assignBadge(
      nomination.nomineeUserId,
      nomination.badgeTypeId,
      adminUserId
    );

    // Send notification to nominee
    this.wsManager.sendToUser(nomination.nomineeUserId, {
      type: "NOMINATION_APPROVED",
      payload: {
        nominationId: nominationId,
        nomination: { ...nomination, status: "approved" },
        badgeAssigned: userBadge,
        approvedBy: adminUserId
      }
    });

    return userBadge;
  }

  /**
   * Process nomination rejection (admin function)
   */
  async rejectNomination(nominationId: string, adminUserId: string): Promise<void> {
    const nomination = await this.getNominationById(nominationId);
    
    if (nomination.status !== "pending") {
      throw new BadgeError("Nomination is not pending", "NOMINATION_NOT_PENDING");
    }

    // Update nomination status
    await db
      .update(BadgeNominationSchema)
      .set({
        status: "rejected",
        processedAt: new Date(),
      })
      .where(eq(BadgeNominationSchema.id, nominationId));

    // Send notification to nominee
    this.wsManager.sendToUser(nomination.nomineeUserId, {
      type: "NOMINATION_REJECTED",
      payload: {
        nominationId: nominationId,
        nomination: { ...nomination, status: "rejected" },
        rejectedBy: adminUserId
      }
    });
  }

  /**
   * Check if nomination threshold is met and auto-approve
   */
  private async checkNominationThreshold(badgeTypeId: string, nomineeUserId: string): Promise<void> {
    const badgeType = await this.getBadgeTypeById(badgeTypeId);
    
    // Get threshold from badge criteria (default to 3 if not specified)
    const threshold = badgeType.criteria.threshold || 3;
    
    const nominationCount = await this.getNominationCount(badgeTypeId, nomineeUserId);
    
    if (nominationCount >= threshold) {
      // Auto-approve by assigning the badge
      const userBadge = await this.badgeService.assignBadge(
        nomineeUserId,
        badgeTypeId,
        "system" // System assignment for peer-voted badges
      );

      // Update all pending nominations for this user/badge to approved
      await db
        .update(BadgeNominationSchema)
        .set({
          status: "approved",
          processedAt: new Date(),
        })
        .where(
          and(
            eq(BadgeNominationSchema.badgeTypeId, badgeTypeId),
            eq(BadgeNominationSchema.nomineeUserId, nomineeUserId),
            eq(BadgeNominationSchema.status, "pending")
          )
        );

      // Send notification to nominee
      this.wsManager.sendToUser(nomineeUserId, {
        type: "BADGE_ASSIGNED",
        payload: {
          userId: nomineeUserId,
          badge: userBadge,
          badgeType: badgeType,
          isNew: true,
          perksGranted: badgeType.perks
        }
      });
    }
  }

  /**
   * Get nomination by ID with full details
   */
  private async getNominationById(nominationId: string): Promise<BadgeNomination> {
    const [result] = await db
      .select({
        id: BadgeNominationSchema.id,
        badgeTypeId: BadgeNominationSchema.badgeTypeId,
        nomineeUserId: BadgeNominationSchema.nomineeUserId,
        nominatorUserId: BadgeNominationSchema.nominatorUserId,
        nominationReason: BadgeNominationSchema.nominationReason,
        status: BadgeNominationSchema.status,
        createdAt: BadgeNominationSchema.createdAt,
        processedAt: BadgeNominationSchema.processedAt,
        badgeType: {
          id: BadgeTypeSchema.id,
          name: BadgeTypeSchema.name,
          title: BadgeTypeSchema.title,
          description: BadgeTypeSchema.description,
          icon: BadgeTypeSchema.icon,
          category: BadgeTypeSchema.category,
        }
      })
      .from(BadgeNominationSchema)
      .leftJoin(BadgeTypeSchema, eq(BadgeNominationSchema.badgeTypeId, BadgeTypeSchema.id))
      .where(eq(BadgeNominationSchema.id, nominationId));

    if (!result) {
      throw new BadgeError("Nomination not found", "NOMINATION_NOT_FOUND");
    }

    return {
      id: result.id,
      badgeTypeId: result.badgeTypeId,
      nomineeUserId: result.nomineeUserId,
      nominatorUserId: result.nominatorUserId,
      nominationReason: result.nominationReason,
      status: result.status as 'pending' | 'approved' | 'rejected',
      createdAt: result.createdAt,
      processedAt: result.processedAt,
      badgeType: result.badgeType ? {
        id: result.badgeType.id,
        name: result.badgeType.name,
        title: result.badgeType.title,
        description: result.badgeType.description,
        icon: result.badgeType.icon,
        category: result.badgeType.category,
      } : undefined
    };
  }

  /**
   * Validate badge type supports peer voting
   */
  private async validateBadgeForNomination(badgeTypeId: string): Promise<BadgeType> {
    const badgeType = await this.getBadgeTypeById(badgeTypeId);
    
    if (badgeType.unlockType !== "peer_voted") {
      throw new BadgeError("Badge does not support peer voting", "BADGE_NOT_PEER_VOTED");
    }
    
    if (!badgeType.isActive) {
      throw new BadgeError("Badge is not active", "BADGE_NOT_ACTIVE");
    }
    
    return badgeType;
  }

  /**
   * Get badge type by ID
   */
  private async getBadgeTypeById(badgeTypeId: string): Promise<BadgeType> {
    const [result] = await db
      .select()
      .from(BadgeTypeSchema)
      .where(eq(BadgeTypeSchema.id, badgeTypeId));

    if (!result) {
      throw new BadgeError("Badge type not found", "BADGE_TYPE_NOT_FOUND");
    }

    return {
      id: result.id,
      collectionId: result.collectionId || undefined,
      badgeId: result.badgeId,
      name: result.name,
      title: result.title || undefined,
      description: result.description,
      icon: result.icon,
      tooltip: result.tooltip || undefined,
      design: result.design as any,
      criteria: result.criteria as any,
      perks: result.perks as string[] || undefined,
      unlockType: result.unlockType as any,
      visualDescription: result.visualDescription || undefined,
      animation: result.animation || undefined,
      displayOrder: result.displayOrder,
      category: result.category as any,
      isActive: result.isActive,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    };
  }

  /**
   * Validate nominee exists
   */
  private async validateNominee(nomineeUserId: string): Promise<void> {
    const [user] = await db
      .select({ id: UserSchema.id })
      .from(UserSchema)
      .where(eq(UserSchema.id, nomineeUserId));

    if (!user) {
      throw new BadgeError("Nominee user not found", "USER_NOT_FOUND");
    }
  }

  /**
   * Check if nomination already exists
   */
  private async checkExistingNomination(
    badgeTypeId: string,
    nomineeUserId: string,
    nominatorUserId: string
  ): Promise<void> {
    const [existing] = await db
      .select({ id: BadgeNominationSchema.id })
      .from(BadgeNominationSchema)
      .where(
        and(
          eq(BadgeNominationSchema.badgeTypeId, badgeTypeId),
          eq(BadgeNominationSchema.nomineeUserId, nomineeUserId),
          eq(BadgeNominationSchema.nominatorUserId, nominatorUserId)
        )
      );

    if (existing) {
      throw new BadgeError("Nomination already exists", "NOMINATION_ALREADY_EXISTS");
    }
  }

  /**
   * Check if nominee already has the badge
   */
  private async checkExistingBadge(badgeTypeId: string, nomineeUserId: string): Promise<void> {
    const [existing] = await db
      .select({ id: UserBadgeSchema.id })
      .from(UserBadgeSchema)
      .where(
        and(
          eq(UserBadgeSchema.badgeTypeId, badgeTypeId),
          eq(UserBadgeSchema.userId, nomineeUserId)
        )
      );

    if (existing) {
      throw new BadgeError("User already has this badge", "BADGE_ALREADY_ASSIGNED");
    }
  }
}