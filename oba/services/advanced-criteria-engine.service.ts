import type { drizzle } from "drizzle-orm/postgres-js";
import { eq, and, or, count, sql, gte, lte, between, desc, asc } from "drizzle-orm";
import { 
  UserSchema, 
  MessageSchema, 
  ServerMembershipSchema,
  FriendshipSchema,
  ServerInviteSchema,
  DirectMessageSchema,
  UserBadgeSchema,
  BadgeNominationSchema
} from "../db/schema";
import type {
  BadgeCriteria,
  UserStats,
} from "../types/badge.types";

/**
 * Advanced criteria evaluation engine supporting complex multi-condition requirements
 * This service handles sophisticated badge criteria that go beyond simple thresholds
 */
export class AdvancedCriteriaEngine {
  constructor(private db: ReturnType<typeof drizzle>) {}

  /**
   * Evaluates complex criteria with multiple conditions and logical operators
   */
  async evaluateComplexCriteria(
    userId: string, 
    criteria: ComplexBadgeCriteria
  ): Promise<CriteriaEvaluationResult> {
    try {
      const userStats = await this.getEnhancedUserStats(userId);
      const result: CriteriaEvaluationResult = {
        userId,
        meetsCriteria: false,
        evaluatedConditions: [],
        progress: 0,
        details: {}
      };

      // Handle different types of complex criteria
      switch (criteria.type) {
        case 'multi_condition':
          return await this.evaluateMultiConditionCriteria(userStats, criteria, result);
        case 'time_based':
          return await this.evaluateTimeBasedCriteria(userId, criteria, result);
        case 'engagement_pattern':
          return await this.evaluateEngagementPatternCriteria(userId, criteria, result);
        case 'social_network':
          return await this.evaluateSocialNetworkCriteria(userId, criteria, result);
        case 'achievement_chain':
          return await this.evaluateAchievementChainCriteria(userId, criteria, result);
        case 'conditional_threshold':
          return await this.evaluateConditionalThresholdCriteria(userStats, criteria, result);
        default:
          throw new Error(`Unsupported criteria type: ${criteria.type}`);
      }
    } catch (error) {
      console.error("Error evaluating complex criteria:", error);
      return {
        userId,
        meetsCriteria: false,
        evaluatedConditions: [],
        progress: 0,
        details: { error: error instanceof Error ? error.message : String(error) }
      };
    }
  }

  /**
   * Evaluates multi-condition criteria with AND/OR logic
   */
  private async evaluateMultiConditionCriteria(
    userStats: EnhancedUserStats,
    criteria: MultiConditionCriteria,
    result: CriteriaEvaluationResult
  ): Promise<CriteriaEvaluationResult> {
    const conditions = criteria.conditions;
    const evaluatedConditions: ConditionResult[] = [];
    
    for (const condition of conditions) {
      const conditionResult = await this.evaluateSingleCondition(userStats, condition);
      evaluatedConditions.push(conditionResult);
    }

    result.evaluatedConditions = evaluatedConditions;

    // Apply logical operator
    if (criteria.operator === 'AND') {
      result.meetsCriteria = evaluatedConditions.every(c => c.met);
      result.progress = evaluatedConditions.reduce((sum, c) => sum + c.progress, 0) / evaluatedConditions.length;
    } else if (criteria.operator === 'OR') {
      result.meetsCriteria = evaluatedConditions.some(c => c.met);
      result.progress = Math.max(...evaluatedConditions.map(c => c.progress));
    } else if (criteria.operator === 'WEIGHTED') {
      const totalWeight = conditions.reduce((sum, c) => sum + (c.weight || 1), 0);
      const weightedScore = evaluatedConditions.reduce((sum, c, i) => {
        const weight = conditions[i].weight || 1;
        return sum + (c.met ? weight : 0);
      }, 0);
      
      result.meetsCriteria = weightedScore >= (criteria.threshold || totalWeight);
      result.progress = weightedScore / totalWeight;
    }

    result.details = {
      operator: criteria.operator,
      conditionsEvaluated: evaluatedConditions.length,
      conditionsMet: evaluatedConditions.filter(c => c.met).length
    };

    return result;
  }

  /**
   * Evaluates time-based criteria with specific time windows and patterns
   */
  private async evaluateTimeBasedCriteria(
    userId: string,
    criteria: TimeBasedCriteria,
    result: CriteriaEvaluationResult
  ): Promise<CriteriaEvaluationResult> {
    const timeWindow = this.parseTimeWindow(criteria.timeWindow);
    const startDate = new Date(Date.now() - timeWindow);
    
    const timeBasedStats = await this.getTimeBasedStats(userId, startDate);
    
    const conditions = criteria.conditions;
    let metConditions = 0;
    const evaluatedConditions: ConditionResult[] = [];

    for (const condition of conditions) {
      let currentValue = 0;
      let met = false;

      switch (condition.metric) {
        case 'messages_per_day':
          currentValue = timeBasedStats.messagesInPeriod / (timeWindow / (1000 * 60 * 60 * 24));
          met = currentValue >= condition.threshold;
          break;
        case 'active_days':
          currentValue = timeBasedStats.activeDays;
          met = currentValue >= condition.threshold;
          break;
        case 'consecutive_days':
          currentValue = timeBasedStats.consecutiveActiveDays;
          met = currentValue >= condition.threshold;
          break;
        case 'peak_activity_hours':
          currentValue = timeBasedStats.peakActivityHours.length;
          met = currentValue >= condition.threshold;
          break;
        case 'engagement_consistency':
          currentValue = timeBasedStats.engagementConsistency;
          met = currentValue >= condition.threshold;
          break;
      }

      const conditionResult: ConditionResult = {
        condition: condition.metric,
        threshold: condition.threshold,
        currentValue,
        met,
        progress: Math.min(currentValue / condition.threshold, 1)
      };

      evaluatedConditions.push(conditionResult);
      if (met) metConditions++;
    }

    result.evaluatedConditions = evaluatedConditions;
    result.meetsCriteria = metConditions >= (criteria.minimumConditions || conditions.length);
    result.progress = metConditions / conditions.length;
    result.details = {
      timeWindow: criteria.timeWindow,
      periodStart: startDate.toISOString(),
      timeBasedStats
    };

    return result;
  }

  /**
   * Evaluates engagement pattern criteria (activity patterns, interaction quality)
   */
  private async evaluateEngagementPatternCriteria(
    userId: string,
    criteria: EngagementPatternCriteria,
    result: CriteriaEvaluationResult
  ): Promise<CriteriaEvaluationResult> {
    const engagementStats = await this.getEngagementPatternStats(userId);
    
    const patterns = criteria.patterns;
    let metPatterns = 0;
    const evaluatedConditions: ConditionResult[] = [];

    for (const pattern of patterns) {
      let currentValue = 0;
      let met = false;

      switch (pattern.type) {
        case 'interaction_diversity':
          currentValue = engagementStats.interactionTypes.length;
          met = currentValue >= pattern.threshold;
          break;
        case 'response_rate':
          currentValue = engagementStats.responseRate;
          met = currentValue >= pattern.threshold;
          break;
        case 'conversation_starter':
          currentValue = engagementStats.conversationsStarted;
          met = currentValue >= pattern.threshold;
          break;
        case 'community_helper':
          currentValue = engagementStats.helpfulInteractions;
          met = currentValue >= pattern.threshold;
          break;
        case 'content_quality':
          currentValue = engagementStats.averageMessageLength;
          met = currentValue >= pattern.threshold;
          break;
      }

      const conditionResult: ConditionResult = {
        condition: pattern.type,
        threshold: pattern.threshold,
        currentValue,
        met,
        progress: Math.min(currentValue / pattern.threshold, 1)
      };

      evaluatedConditions.push(conditionResult);
      if (met) metPatterns++;
    }

    result.evaluatedConditions = evaluatedConditions;
    result.meetsCriteria = metPatterns >= (criteria.minimumPatterns || patterns.length);
    result.progress = metPatterns / patterns.length;
    result.details = { engagementStats };

    return result;
  }

  /**
   * Evaluates social network criteria (friend connections, influence)
   */
  private async evaluateSocialNetworkCriteria(
    userId: string,
    criteria: SocialNetworkCriteria,
    result: CriteriaEvaluationResult
  ): Promise<CriteriaEvaluationResult> {
    const socialStats = await this.getSocialNetworkStats(userId);
    
    const metrics = criteria.metrics;
    let metMetrics = 0;
    const evaluatedConditions: ConditionResult[] = [];

    for (const metric of metrics) {
      let currentValue = 0;
      let met = false;

      switch (metric.type) {
        case 'friend_network_size':
          currentValue = socialStats.totalFriends;
          met = currentValue >= metric.threshold;
          break;
        case 'mutual_connections':
          currentValue = socialStats.mutualConnections;
          met = currentValue >= metric.threshold;
          break;
        case 'network_influence':
          currentValue = socialStats.networkInfluence;
          met = currentValue >= metric.threshold;
          break;
        case 'community_bridges':
          currentValue = socialStats.communityBridges;
          met = currentValue >= metric.threshold;
          break;
        case 'referral_success':
          currentValue = socialStats.successfulReferrals;
          met = currentValue >= metric.threshold;
          break;
      }

      const conditionResult: ConditionResult = {
        condition: metric.type,
        threshold: metric.threshold,
        currentValue,
        met,
        progress: Math.min(currentValue / metric.threshold, 1)
      };

      evaluatedConditions.push(conditionResult);
      if (met) metMetrics++;
    }

    result.evaluatedConditions = evaluatedConditions;
    result.meetsCriteria = metMetrics >= (criteria.minimumMetrics || metrics.length);
    result.progress = metMetrics / metrics.length;
    result.details = { socialStats };

    return result;
  }

  /**
   * Evaluates achievement chain criteria (prerequisite badges, progression)
   */
  private async evaluateAchievementChainCriteria(
    userId: string,
    criteria: AchievementChainCriteria,
    result: CriteriaEvaluationResult
  ): Promise<CriteriaEvaluationResult> {
    const userBadges = await this.db
      .select({
        badgeTypeId: UserBadgeSchema.badgeTypeId,
        assignedAt: UserBadgeSchema.assignedAt
      })
      .from(UserBadgeSchema)
      .where(eq(UserBadgeSchema.userId, userId));

    const userBadgeIds = new Set(userBadges.map(b => b.badgeTypeId));
    
    const chain = criteria.chain;
    let metRequirements = 0;
    const evaluatedConditions: ConditionResult[] = [];

    for (const requirement of chain) {
      let met = false;
      let currentValue = 0;

      switch (requirement.type) {
        case 'prerequisite_badges':
          const requiredBadges = requirement.badgeIds || [];
          const hasBadges = requiredBadges.filter(id => userBadgeIds.has(id));
          currentValue = hasBadges.length;
          met = currentValue >= requiredBadges.length;
          break;
        case 'badge_category_count':
          // Count badges in specific category
          const categoryBadges = userBadges.filter(b => 
            requirement.categories?.includes(this.getBadgeCategory(b.badgeTypeId))
          );
          currentValue = categoryBadges.length;
          met = currentValue >= (requirement.threshold || 1);
          break;
        case 'time_between_badges':
          if (userBadges.length >= 2) {
            const sortedBadges = userBadges.sort((a, b) => 
              a.assignedAt.getTime() - b.assignedAt.getTime()
            );
            const timeDiffs = [];
            for (let i = 1; i < sortedBadges.length; i++) {
              const diff = sortedBadges[i].assignedAt.getTime() - sortedBadges[i-1].assignedAt.getTime();
              timeDiffs.push(diff / (1000 * 60 * 60 * 24)); // Convert to days
            }
            currentValue = Math.min(...timeDiffs);
            met = currentValue <= (requirement.threshold || 30); // Max 30 days between badges
          }
          break;
      }

      const conditionResult: ConditionResult = {
        condition: requirement.type,
        threshold: requirement.threshold || 1,
        currentValue,
        met,
        progress: requirement.type === 'time_between_badges' 
          ? (met ? 1 : 0) 
          : Math.min(currentValue / (requirement.threshold || 1), 1)
      };

      evaluatedConditions.push(conditionResult);
      if (met) metRequirements++;
    }

    result.evaluatedConditions = evaluatedConditions;
    result.meetsCriteria = metRequirements >= (criteria.minimumRequirements || chain.length);
    result.progress = metRequirements / chain.length;
    result.details = { 
      userBadgeCount: userBadges.length,
      badgeIds: Array.from(userBadgeIds)
    };

    return result;
  }

  /**
   * Evaluates conditional threshold criteria (dynamic thresholds based on conditions)
   */
  private async evaluateConditionalThresholdCriteria(
    userStats: EnhancedUserStats,
    criteria: ConditionalThresholdCriteria,
    result: CriteriaEvaluationResult
  ): Promise<CriteriaEvaluationResult> {
    const conditions = criteria.conditions;
    let activeThreshold = criteria.baseThreshold;
    const evaluatedConditions: ConditionResult[] = [];

    // Evaluate conditions that modify the threshold
    for (const condition of conditions) {
      let conditionMet = false;
      let modifier = 0;

      switch (condition.type) {
        case 'account_age_modifier':
          if (userStats.accountAge >= condition.accountAgeThreshold) {
            conditionMet = true;
            modifier = condition.thresholdModifier;
          }
          break;
        case 'activity_level_modifier':
          const activityScore = userStats.messageCount + (userStats.serverCount * 10);
          if (activityScore >= condition.activityThreshold) {
            conditionMet = true;
            modifier = condition.thresholdModifier;
          }
          break;
        case 'social_modifier':
          if (userStats.friendCount >= condition.socialThreshold) {
            conditionMet = true;
            modifier = condition.thresholdModifier;
          }
          break;
      }

      if (conditionMet) {
        activeThreshold += modifier;
      }

      evaluatedConditions.push({
        condition: condition.type,
        threshold: condition.accountAgeThreshold || condition.activityThreshold || condition.socialThreshold || 0,
        currentValue: condition.type === 'account_age_modifier' ? userStats.accountAge :
                     condition.type === 'activity_level_modifier' ? userStats.messageCount + (userStats.serverCount * 10) :
                     userStats.friendCount,
        met: conditionMet,
        progress: conditionMet ? 1 : 0
      });
    }

    // Evaluate main metric against the modified threshold
    let mainMetricValue = 0;
    switch (criteria.metric) {
      case 'message_count':
        mainMetricValue = userStats.messageCount;
        break;
      case 'server_count':
        mainMetricValue = userStats.serverCount;
        break;
      case 'friend_count':
        mainMetricValue = userStats.friendCount;
        break;
    }

    result.evaluatedConditions = evaluatedConditions;
    result.meetsCriteria = mainMetricValue >= activeThreshold;
    result.progress = Math.min(mainMetricValue / activeThreshold, 1);
    result.details = {
      baseThreshold: criteria.baseThreshold,
      activeThreshold,
      mainMetric: criteria.metric,
      mainMetricValue
    };

    return result;
  }

  /**
   * Evaluates a single condition within multi-condition criteria
   */
  private async evaluateSingleCondition(
    userStats: EnhancedUserStats,
    condition: SingleCondition
  ): Promise<ConditionResult> {
    let currentValue = 0;
    let met = false;

    switch (condition.type) {
      case 'message_count':
        currentValue = userStats.messageCount;
        break;
      case 'server_count':
        currentValue = userStats.serverCount;
        break;
      case 'friend_count':
        currentValue = userStats.friendCount;
        break;
      case 'account_age':
        currentValue = userStats.accountAge;
        break;
      case 'days_active':
        currentValue = userStats.daysActive;
        break;
      case 'invites_sent':
        currentValue = userStats.invitesSent;
        break;
      case 'invites_accepted':
        currentValue = userStats.invitesAccepted;
        break;
      case 'feedback_submitted':
        currentValue = userStats.feedbackSubmitted;
        break;
      case 'moderation_actions':
        currentValue = userStats.moderationActions;
        break;
    }

    // Apply comparison operator
    switch (condition.operator || 'gte') {
      case 'gte':
        met = currentValue >= condition.threshold;
        break;
      case 'lte':
        met = currentValue <= condition.threshold;
        break;
      case 'eq':
        met = currentValue === condition.threshold;
        break;
      case 'gt':
        met = currentValue > condition.threshold;
        break;
      case 'lt':
        met = currentValue < condition.threshold;
        break;
    }

    return {
      condition: condition.type,
      threshold: condition.threshold,
      currentValue,
      met,
      progress: condition.operator === 'lte' || condition.operator === 'lt' 
        ? (met ? 1 : 0)
        : Math.min(currentValue / condition.threshold, 1)
    };
  }

  /**
   * Gets enhanced user statistics including additional metrics
   */
  private async getEnhancedUserStats(userId: string): Promise<EnhancedUserStats> {
    // Get basic user info
    const user = await this.db
      .select({
        createdAt: UserSchema.createdAt,
        lastActive: UserSchema.lastActive,
      })
      .from(UserSchema)
      .where(eq(UserSchema.id, userId))
      .limit(1);

    if (user.length === 0) {
      throw new Error("User not found");
    }

    const userData = user[0];
    const now = new Date();
    const accountAge = Math.floor((now.getTime() - userData.createdAt.getTime()) / (1000 * 60 * 60 * 24));
    
    // Calculate days active
    const lastActiveDate = userData.lastActive || userData.createdAt;
    const daysActive = Math.floor((now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60 * 24));

    // Get message count
    const messageCountResult = await this.db
      .select({ count: count() })
      .from(MessageSchema)
      .where(eq(MessageSchema.userId, userId));
    
    const messageCount = Number(messageCountResult[0]?.count || 0);

    // Get server count
    const serverCountResult = await this.db
      .select({ count: count() })
      .from(ServerMembershipSchema)
      .where(eq(ServerMembershipSchema.userId, userId));
    
    const serverCount = Number(serverCountResult[0]?.count || 0);

    // Get friend count
    const friendCountResult = await this.db
      .select({ count: count() })
      .from(FriendshipSchema)
      .where(
        and(
          eq(FriendshipSchema.userId, userId),
          eq(FriendshipSchema.status, "ACCEPTED")
        )
      );
    
    const friendCount = Number(friendCountResult[0]?.count || 0);

    // Get invite statistics
    const invitesSentResult = await this.db
      .select({ count: count() })
      .from(ServerInviteSchema)
      .where(eq(ServerInviteSchema.createdById, userId));
    
    const invitesSent = Number(invitesSentResult[0]?.count || 0);

    // Get invites accepted (approximate by checking invite uses)
    const invitesAcceptedResult = await this.db
      .select({ totalUses: sql<number>`SUM(${ServerInviteSchema.uses})` })
      .from(ServerInviteSchema)
      .where(eq(ServerInviteSchema.createdById, userId));
    
    const invitesAccepted = Number(invitesAcceptedResult[0]?.totalUses || 0);

    return {
      messageCount,
      serverCount,
      friendCount,
      daysActive: Math.max(0, daysActive),
      accountAge,
      lastActive: lastActiveDate,
      invitesSent,
      invitesAccepted,
      feedbackSubmitted: 0, // Would need feedback table
      moderationActions: 0, // Would need moderation table
      signupOrder: undefined,
      geographicRegion: undefined
    };
  }

  /**
   * Gets time-based statistics for a user within a specific time window
   */
  private async getTimeBasedStats(userId: string, startDate: Date): Promise<TimeBasedStats> {
    // Get messages in time period
    const messagesResult = await this.db
      .select({
        count: count(),
        createdAt: MessageSchema.createdAt
      })
      .from(MessageSchema)
      .where(
        and(
          eq(MessageSchema.userId, userId),
          gte(MessageSchema.createdAt, startDate)
        )
      );

    const messagesInPeriod = Number(messagesResult[0]?.count || 0);

    // Calculate active days (days with at least one message)
    const dailyActivity = await this.db
      .select({
        date: sql<string>`DATE(${MessageSchema.createdAt})`,
        count: count()
      })
      .from(MessageSchema)
      .where(
        and(
          eq(MessageSchema.userId, userId),
          gte(MessageSchema.createdAt, startDate)
        )
      )
      .groupBy(sql`DATE(${MessageSchema.createdAt})`);

    const activeDays = dailyActivity.length;

    // Calculate consecutive active days (simplified)
    let consecutiveActiveDays = 0;
    if (dailyActivity.length > 0) {
      const sortedDays = dailyActivity.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      consecutiveActiveDays = 1;
      
      for (let i = 1; i < sortedDays.length; i++) {
        const currentDate = new Date(sortedDays[i].date);
        const previousDate = new Date(sortedDays[i-1].date);
        const dayDiff = (previousDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24);
        
        if (dayDiff === 1) {
          consecutiveActiveDays++;
        } else {
          break;
        }
      }
    }

    // Calculate peak activity hours (simplified - hours with most messages)
    const hourlyActivity = await this.db
      .select({
        hour: sql<number>`EXTRACT(HOUR FROM ${MessageSchema.createdAt})`,
        count: count()
      })
      .from(MessageSchema)
      .where(
        and(
          eq(MessageSchema.userId, userId),
          gte(MessageSchema.createdAt, startDate)
        )
      )
      .groupBy(sql`EXTRACT(HOUR FROM ${MessageSchema.createdAt})`)
      .orderBy(desc(count()));

    const peakActivityHours = hourlyActivity.slice(0, 3).map(h => Number(h.hour));

    // Calculate engagement consistency (messages per active day)
    const engagementConsistency = activeDays > 0 ? messagesInPeriod / activeDays : 0;

    return {
      messagesInPeriod,
      activeDays,
      consecutiveActiveDays,
      peakActivityHours,
      engagementConsistency
    };
  }

  /**
   * Gets engagement pattern statistics for a user
   */
  private async getEngagementPatternStats(userId: string): Promise<EngagementPatternStats> {
    // Get message statistics
    const messageStats = await this.db
      .select({
        count: count(),
        avgLength: sql<number>`AVG(LENGTH(${MessageSchema.content}))`
      })
      .from(MessageSchema)
      .where(eq(MessageSchema.userId, userId));

    const totalMessages = Number(messageStats[0]?.count || 0);
    const averageMessageLength = Number(messageStats[0]?.avgLength || 0);

    // Get direct message statistics (for response rate calculation)
    const dmStats = await this.db
      .select({ count: count() })
      .from(DirectMessageSchema)
      .where(eq(DirectMessageSchema.senderId, userId));

    const directMessagesSent = Number(dmStats[0]?.count || 0);

    // Simplified engagement metrics
    const interactionTypes = ['messages', 'direct_messages']; // Would expand with reactions, etc.
    const responseRate = totalMessages > 0 ? Math.min(directMessagesSent / totalMessages, 1) : 0;
    const conversationsStarted = Math.floor(totalMessages * 0.1); // Simplified estimate
    const helpfulInteractions = Math.floor(totalMessages * 0.05); // Simplified estimate

    return {
      interactionTypes,
      responseRate,
      conversationsStarted,
      helpfulInteractions,
      averageMessageLength
    };
  }

  /**
   * Gets social network statistics for a user
   */
  private async getSocialNetworkStats(userId: string): Promise<SocialNetworkStats> {
    // Get total friends
    const friendsResult = await this.db
      .select({ count: count() })
      .from(FriendshipSchema)
      .where(
        and(
          eq(FriendshipSchema.userId, userId),
          eq(FriendshipSchema.status, "ACCEPTED")
        )
      );

    const totalFriends = Number(friendsResult[0]?.count || 0);

    // Simplified social network metrics
    const mutualConnections = Math.floor(totalFriends * 0.3); // Estimate
    const networkInfluence = Math.floor(totalFriends * 0.2); // Estimate
    const communityBridges = Math.floor(totalFriends * 0.1); // Estimate

    // Get successful referrals (invites that were used)
    const referralResult = await this.db
      .select({ totalUses: sql<number>`SUM(${ServerInviteSchema.uses})` })
      .from(ServerInviteSchema)
      .where(eq(ServerInviteSchema.createdById, userId));

    const successfulReferrals = Number(referralResult[0]?.totalUses || 0);

    return {
      totalFriends,
      mutualConnections,
      networkInfluence,
      communityBridges,
      successfulReferrals
    };
  }

  /**
   * Parses time window string to milliseconds
   */
  private parseTimeWindow(timeWindow: string): number {
    const match = timeWindow.match(/^(\d+)([hdwmy])$/);
    if (!match) {
      throw new Error(`Invalid time window format: ${timeWindow}`);
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 'h': return value * 60 * 60 * 1000;
      case 'd': return value * 24 * 60 * 60 * 1000;
      case 'w': return value * 7 * 24 * 60 * 60 * 1000;
      case 'm': return value * 30 * 24 * 60 * 60 * 1000;
      case 'y': return value * 365 * 24 * 60 * 60 * 1000;
      default: throw new Error(`Unsupported time unit: ${unit}`);
    }
  }

  /**
   * Gets badge category for a badge type ID (simplified)
   */
  private getBadgeCategory(badgeTypeId: string): string {
    // In a real implementation, this would query the badge type
    return 'achievement'; // Simplified
  }
}

// Type definitions for advanced criteria

export interface ComplexBadgeCriteria {
  type: 'multi_condition' | 'time_based' | 'engagement_pattern' | 'social_network' | 'achievement_chain' | 'conditional_threshold';
  description: string;
}

export interface MultiConditionCriteria extends ComplexBadgeCriteria {
  type: 'multi_condition';
  operator: 'AND' | 'OR' | 'WEIGHTED';
  conditions: SingleCondition[];
  threshold?: number; // For weighted operator
}

export interface TimeBasedCriteria extends ComplexBadgeCriteria {
  type: 'time_based';
  timeWindow: string; // e.g., '7d', '30d', '1y'
  conditions: TimeBasedCondition[];
  minimumConditions?: number;
}

export interface EngagementPatternCriteria extends ComplexBadgeCriteria {
  type: 'engagement_pattern';
  patterns: EngagementPattern[];
  minimumPatterns?: number;
}

export interface SocialNetworkCriteria extends ComplexBadgeCriteria {
  type: 'social_network';
  metrics: SocialNetworkMetric[];
  minimumMetrics?: number;
}

export interface AchievementChainCriteria extends ComplexBadgeCriteria {
  type: 'achievement_chain';
  chain: AchievementRequirement[];
  minimumRequirements?: number;
}

export interface ConditionalThresholdCriteria extends ComplexBadgeCriteria {
  type: 'conditional_threshold';
  metric: 'message_count' | 'server_count' | 'friend_count';
  baseThreshold: number;
  conditions: ThresholdModifier[];
}

export interface SingleCondition {
  type: 'message_count' | 'server_count' | 'friend_count' | 'account_age' | 'days_active' | 'invites_sent' | 'invites_accepted' | 'feedback_submitted' | 'moderation_actions';
  threshold: number;
  operator?: 'gte' | 'lte' | 'eq' | 'gt' | 'lt';
  weight?: number; // For weighted conditions
}

export interface TimeBasedCondition {
  metric: 'messages_per_day' | 'active_days' | 'consecutive_days' | 'peak_activity_hours' | 'engagement_consistency';
  threshold: number;
}

export interface EngagementPattern {
  type: 'interaction_diversity' | 'response_rate' | 'conversation_starter' | 'community_helper' | 'content_quality';
  threshold: number;
}

export interface SocialNetworkMetric {
  type: 'friend_network_size' | 'mutual_connections' | 'network_influence' | 'community_bridges' | 'referral_success';
  threshold: number;
}

export interface AchievementRequirement {
  type: 'prerequisite_badges' | 'badge_category_count' | 'time_between_badges';
  badgeIds?: string[];
  categories?: string[];
  threshold?: number;
}

export interface ThresholdModifier {
  type: 'account_age_modifier' | 'activity_level_modifier' | 'social_modifier';
  accountAgeThreshold?: number;
  activityThreshold?: number;
  socialThreshold?: number;
  thresholdModifier: number; // Can be positive or negative
}

export interface CriteriaEvaluationResult {
  userId: string;
  meetsCriteria: boolean;
  evaluatedConditions: ConditionResult[];
  progress: number; // 0-1
  details: Record<string, any>;
}

export interface ConditionResult {
  condition: string;
  threshold: number;
  currentValue: number;
  met: boolean;
  progress: number; // 0-1
}

export interface EnhancedUserStats extends UserStats {
  // Additional stats for advanced criteria
}

export interface TimeBasedStats {
  messagesInPeriod: number;
  activeDays: number;
  consecutiveActiveDays: number;
  peakActivityHours: number[];
  engagementConsistency: number;
}

export interface EngagementPatternStats {
  interactionTypes: string[];
  responseRate: number;
  conversationsStarted: number;
  helpfulInteractions: number;
  averageMessageLength: number;
}

export interface SocialNetworkStats {
  totalFriends: number;
  mutualConnections: number;
  networkInfluence: number;
  communityBridges: number;
  successfulReferrals: number;
}