import type { drizzle } from "drizzle-orm/postgres-js";
import { eq, and, or } from "drizzle-orm";
import { 
  BadgeValidationError,
  InsufficientPermissionsError 
} from "../class/badge-errors";
import { 
  UserBadgeSchema, 
  BadgeTypeSchema, 
  ServerRoleSchema,
  UserRoles,
  UserSchema
} from "../db/schema";
import { 
  hasServerPermission,
  assignRoleToUser,
  removeRoleFrom<PERSON>ser,
  getUserRoles
} from "../utils/permissions";
import { ADMINISTRATOR, MANAGE_ROLES } from "../constants/permissions";
import { badgeWebSocketService } from "../utils/badge-websocket";
import type {
  BadgeType,
  UserBadge
} from "../types/badge.types";

// Perk types and interfaces
export interface Perk {
  id: string;
  type: PerkType;
  name: string;
  description: string;
  value?: string | number | boolean;
  serverId?: string; // For server-specific perks
  roleId?: string; // For role-based perks
  permissions?: string[]; // For permission-based perks
  isActive: boolean;
  metadata?: Record<string, any>;
}

export type PerkType = 
  | 'role_assignment'      // Assigns a specific role
  | 'permission_grant'     // Grants specific permissions
  | 'cosmetic_feature'     // Unlocks cosmetic features
  | 'access_privilege'     // Grants access to special areas/features
  | 'status_boost'         // Provides status enhancements
  | 'custom_privilege';    // Custom application-specific privileges

export interface PerkAssignment {
  id: string;
  userId: string;
  perkId: string;
  badgeTypeId: string;
  serverId?: string;
  assignedAt: Date;
  isActive: boolean;
  metadata?: Record<string, any>;
}

export interface PerkValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface PerkAssignmentResult {
  success: boolean;
  perk?: Perk;
  assignment?: PerkAssignment;
  error?: string;
  rollbackActions?: Array<() => Promise<void>>;
}

/**
 * Perk Service - Manages badge perks and benefits system
 * Handles automatic perk assignment, validation, and integration with permissions/roles
 */
export class PerkService {
  constructor(private db: ReturnType<typeof drizzle>) {}

  /**
   * Extracts perks from badge type definition
   */
  private extractPerksFromBadge(badgeType: BadgeType): Perk[] {
    if (!badgeType.perks || badgeType.perks.length === 0) {
      return [];
    }

    return badgeType.perks.map((perkData, index) => {
      // Handle both string and object perk definitions
      if (typeof perkData === 'string') {
        return this.parseStringPerk(perkData, `${badgeType.id}_${index}`);
      } else if (typeof perkData === 'object') {
        return this.parseObjectPerk(perkData, `${badgeType.id}_${index}`);
      }
      
      throw new BadgeValidationError(`Invalid perk format in badge ${badgeType.name}`);
    });
  }

  /**
   * Parses string-based perk definitions
   */
  private parseStringPerk(perkString: string, id: string): Perk {
    // Parse common perk string formats:
    // "role:RoleName" - assigns a role
    // "permission:PERMISSION_NAME" - grants a permission
    // "cosmetic:feature_name" - unlocks cosmetic feature
    // "access:area_name" - grants access privilege
    
    const colonIndex = perkString.indexOf(':');
    const type = colonIndex !== -1 ? perkString.substring(0, colonIndex) : '';
    const value = colonIndex !== -1 ? perkString.substring(colonIndex + 1) : perkString;
    
    switch (type.toLowerCase()) {
      case 'role':
        return {
          id,
          type: 'role_assignment',
          name: `Role: ${value}`,
          description: `Assigns the ${value} role`,
          value,
          isActive: true
        };
      
      case 'permission':
        return {
          id,
          type: 'permission_grant',
          name: `Permission: ${value}`,
          description: `Grants ${value} permission`,
          permissions: [value],
          isActive: true
        };
      
      case 'cosmetic':
        return {
          id,
          type: 'cosmetic_feature',
          name: `Cosmetic: ${value}`,
          description: `Unlocks ${value} cosmetic feature`,
          value,
          isActive: true
        };
      
      case 'access':
        return {
          id,
          type: 'access_privilege',
          name: `Access: ${value}`,
          description: `Grants access to ${value}`,
          value,
          isActive: true
        };
      
      default:
        return {
          id,
          type: 'custom_privilege',
          name: `Custom: ${perkString}`,
          description: `Custom privilege: ${perkString}`,
          value: perkString,
          isActive: true
        };
    }
  }

  /**
   * Parses object-based perk definitions
   */
  private parseObjectPerk(perkObject: any, id: string): Perk {
    return {
      id: perkObject.id || id,
      type: perkObject.type || 'custom_privilege',
      name: perkObject.name || 'Unnamed Perk',
      description: perkObject.description || 'No description provided',
      value: perkObject.value,
      serverId: perkObject.serverId,
      roleId: perkObject.roleId,
      permissions: perkObject.permissions,
      isActive: perkObject.isActive !== false,
      metadata: perkObject.metadata
    };
  }

  /**
   * Validates perk configuration and requirements
   */
  async validatePerk(perk: Perk, userId: string, serverId?: string): Promise<PerkValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      switch (perk.type) {
        case 'role_assignment':
          if (!perk.value && !perk.roleId) {
            errors.push('Role assignment perk requires role name or roleId');
          }
          
          if (serverId && perk.value) {
            // Check if role exists in the server
            const roles = await this.db
              .select()
              .from(ServerRoleSchema)
              .where(and(
                eq(ServerRoleSchema.serverId, serverId),
                eq(ServerRoleSchema.name, perk.value as string)
              ));
            
            if (roles.length === 0) {
              errors.push(`Role "${perk.value}" not found in server`);
            }
          }
          break;

        case 'permission_grant':
          if (!perk.permissions || perk.permissions.length === 0) {
            errors.push('Permission grant perk requires permissions array');
          }
          break;

        case 'cosmetic_feature':
        case 'access_privilege':
        case 'status_boost':
          if (!perk.value) {
            warnings.push(`${perk.type} perk should specify a value`);
          }
          break;

        case 'custom_privilege':
          // Custom privileges are always valid but may need application-specific validation
          if (!perk.value && !perk.metadata) {
            warnings.push('Custom privilege should specify value or metadata');
          }
          break;
      }

      // Check server-specific constraints
      if (perk.serverId && serverId && perk.serverId !== serverId) {
        errors.push('Perk is not valid for this server');
      }

    } catch (error) {
      console.error('Error validating perk:', error);
      errors.push('Failed to validate perk configuration');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Assigns perks when a badge is earned
   */
  async assignPerksForBadge(
    userId: string, 
    badgeType: BadgeType, 
    serverId?: string
  ): Promise<PerkAssignmentResult[]> {
    const perks = this.extractPerksFromBadge(badgeType);
    const results: PerkAssignmentResult[] = [];
    const rollbackActions: Array<() => Promise<void>> = [];

    for (const perk of perks) {
      try {
        // Validate perk before assignment
        const validation = await this.validatePerk(perk, userId, serverId);
        if (!validation.isValid) {
          results.push({
            success: false,
            perk,
            error: `Validation failed: ${validation.errors.join(', ')}`
          });
          continue;
        }

        // Assign the perk
        const result = await this.assignPerk(userId, perk, badgeType.id, serverId);
        results.push(result);

        // Collect rollback actions for successful assignments
        if (result.success && result.rollbackActions) {
          rollbackActions.push(...result.rollbackActions);
        }

      } catch (error) {
        console.error(`Error assigning perk ${perk.id}:`, error);
        results.push({
          success: false,
          perk,
          error: `Assignment failed: ${error.message}`
        });
      }
    }

    // If any critical assignments failed, consider rolling back
    const criticalFailures = results.filter(r => !r.success && r.perk?.type === 'role_assignment');
    if (criticalFailures.length > 0) {
      console.warn(`Critical perk assignments failed for badge ${badgeType.name}:`, criticalFailures);
    }

    return results;
  }

  /**
   * Assigns a specific perk to a user
   */
  private async assignPerk(
    userId: string, 
    perk: Perk, 
    badgeTypeId: string, 
    serverId?: string
  ): Promise<PerkAssignmentResult> {
    const rollbackActions: Array<() => Promise<void>> = [];

    try {
      switch (perk.type) {
        case 'role_assignment':
          return await this.assignRolePerk(userId, perk, badgeTypeId, serverId, rollbackActions);
        
        case 'permission_grant':
          return await this.assignPermissionPerk(userId, perk, badgeTypeId, serverId, rollbackActions);
        
        case 'cosmetic_feature':
        case 'access_privilege':
        case 'status_boost':
        case 'custom_privilege':
          return await this.assignCustomPerk(userId, perk, badgeTypeId, serverId, rollbackActions);
        
        default:
          return {
            success: false,
            perk,
            error: `Unsupported perk type: ${perk.type}`
          };
      }
    } catch (error) {
      console.error(`Error in assignPerk for ${perk.type}:`, error);
      return {
        success: false,
        perk,
        error: `Failed to assign perk: ${error.message}`,
        rollbackActions
      };
    }
  }

  /**
   * Assigns role-based perks
   */
  private async assignRolePerk(
    userId: string,
    perk: Perk,
    badgeTypeId: string,
    serverId?: string,
    rollbackActions: Array<() => Promise<void>>
  ): Promise<PerkAssignmentResult> {
    if (!serverId) {
      return {
        success: false,
        perk,
        error: 'Server ID required for role assignment'
      };
    }

    try {
      // Find the role to assign
      let roleId = perk.roleId;
      
      if (!roleId && perk.value) {
        const roles = await this.db
          .select()
          .from(ServerRoleSchema)
          .where(and(
            eq(ServerRoleSchema.serverId, serverId),
            eq(ServerRoleSchema.name, perk.value as string)
          ));
        
        if (roles.length === 0) {
          return {
            success: false,
            perk,
            error: `Role "${perk.value}" not found in server`
          };
        }
        
        roleId = roles[0].id;
      }

      if (!roleId) {
        return {
          success: false,
          perk,
          error: 'No role ID specified for role assignment'
        };
      }

      // Check if user already has this role
      const existingRoles = await getUserRoles(this.db, userId, serverId);
      const hasRole = existingRoles.some(role => role.id === roleId);
      
      if (hasRole) {
        // User already has the role, consider this a success
        return {
          success: true,
          perk,
          assignment: {
            id: `${userId}_${roleId}_${badgeTypeId}`,
            userId,
            perkId: perk.id,
            badgeTypeId,
            serverId,
            assignedAt: new Date(),
            isActive: true
          }
        };
      }

      // Assign the role
      const success = await assignRoleToUser(userId, roleId, serverId);
      
      if (!success) {
        return {
          success: false,
          perk,
          error: 'Failed to assign role to user'
        };
      }

      // Add rollback action
      rollbackActions.push(async () => {
        await removeRoleFromUser(userId, roleId!, serverId);
      });

      // Broadcast perk assignment event
      await badgeWebSocketService.broadcastPerkAssigned({
        userId,
        perk,
        badgeTypeId,
        serverId
      });

      return {
        success: true,
        perk,
        assignment: {
          id: `${userId}_${roleId}_${badgeTypeId}`,
          userId,
          perkId: perk.id,
          badgeTypeId,
          serverId,
          assignedAt: new Date(),
          isActive: true
        },
        rollbackActions
      };

    } catch (error) {
      console.error('Error assigning role perk:', error);
      return {
        success: false,
        perk,
        error: `Role assignment failed: ${error.message}`
      };
    }
  }

  /**
   * Assigns permission-based perks
   */
  private async assignPermissionPerk(
    userId: string,
    perk: Perk,
    badgeTypeId: string,
    serverId?: string,
    rollbackActions: Array<() => Promise<void>>
  ): Promise<PerkAssignmentResult> {
    // For permission perks, we would typically create a special role with the required permissions
    // or use a permission override system. For now, we'll log the assignment and return success.
    
    console.log(`Permission perk assigned to user ${userId}:`, {
      permissions: perk.permissions,
      badgeTypeId,
      serverId
    });

    // In a full implementation, you might:
    // 1. Create a temporary role with the required permissions
    // 2. Use a permission override system
    // 3. Store permission grants in a separate table

    return {
      success: true,
      perk,
      assignment: {
        id: `${userId}_${perk.id}_${badgeTypeId}`,
        userId,
        perkId: perk.id,
        badgeTypeId,
        serverId,
        assignedAt: new Date(),
        isActive: true,
        metadata: { permissions: perk.permissions }
      }
    };
  }

  /**
   * Assigns custom perks (cosmetic, access, status, custom)
   */
  private async assignCustomPerk(
    userId: string,
    perk: Perk,
    badgeTypeId: string,
    serverId?: string,
    rollbackActions: Array<() => Promise<void>>
  ): Promise<PerkAssignmentResult> {
    // Custom perks are application-specific and would be handled by the client application
    // We'll store the assignment and let the application handle the implementation
    
    console.log(`Custom perk assigned to user ${userId}:`, {
      type: perk.type,
      value: perk.value,
      metadata: perk.metadata,
      badgeTypeId,
      serverId
    });

    // Broadcast perk assignment event
    await badgeWebSocketService.broadcastPerkAssigned({
      userId,
      perk,
      badgeTypeId,
      serverId
    });

    return {
      success: true,
      perk,
      assignment: {
        id: `${userId}_${perk.id}_${badgeTypeId}`,
        userId,
        perkId: perk.id,
        badgeTypeId,
        serverId,
        assignedAt: new Date(),
        isActive: true,
        metadata: perk.metadata
      }
    };
  }

  /**
   * Revokes perks when a badge is removed
   */
  async revokePerksForBadge(
    userId: string, 
    badgeType: BadgeType, 
    serverId?: string
  ): Promise<PerkAssignmentResult[]> {
    const perks = this.extractPerksFromBadge(badgeType);
    const results: PerkAssignmentResult[] = [];

    for (const perk of perks) {
      try {
        const result = await this.revokePerk(userId, perk, badgeType.id, serverId);
        results.push(result);
      } catch (error) {
        console.error(`Error revoking perk ${perk.id}:`, error);
        results.push({
          success: false,
          perk,
          error: `Revocation failed: ${error.message}`
        });
      }
    }

    return results;
  }

  /**
   * Revokes a specific perk from a user
   */
  private async revokePerk(
    userId: string, 
    perk: Perk, 
    badgeTypeId: string, 
    serverId?: string
  ): Promise<PerkAssignmentResult> {
    try {
      switch (perk.type) {
        case 'role_assignment':
          return await this.revokeRolePerk(userId, perk, badgeTypeId, serverId);
        
        case 'permission_grant':
          return await this.revokePermissionPerk(userId, perk, badgeTypeId, serverId);
        
        case 'cosmetic_feature':
        case 'access_privilege':
        case 'status_boost':
        case 'custom_privilege':
          return await this.revokeCustomPerk(userId, perk, badgeTypeId, serverId);
        
        default:
          return {
            success: false,
            perk,
            error: `Unsupported perk type for revocation: ${perk.type}`
          };
      }
    } catch (error) {
      console.error(`Error in revokePerk for ${perk.type}:`, error);
      return {
        success: false,
        perk,
        error: `Failed to revoke perk: ${error.message}`
      };
    }
  }

  /**
   * Revokes role-based perks
   */
  private async revokeRolePerk(
    userId: string,
    perk: Perk,
    badgeTypeId: string,
    serverId?: string
  ): Promise<PerkAssignmentResult> {
    if (!serverId) {
      return {
        success: false,
        perk,
        error: 'Server ID required for role revocation'
      };
    }

    try {
      // Find the role to revoke
      let roleId = perk.roleId;
      
      if (!roleId && perk.value) {
        const roles = await this.db
          .select()
          .from(ServerRoleSchema)
          .where(and(
            eq(ServerRoleSchema.serverId, serverId),
            eq(ServerRoleSchema.name, perk.value as string)
          ));
        
        if (roles.length === 0) {
          return {
            success: false,
            perk,
            error: `Role "${perk.value}" not found in server`
          };
        }
        
        roleId = roles[0].id;
      }

      if (!roleId) {
        return {
          success: false,
          perk,
          error: 'No role ID specified for role revocation'
        };
      }

      // Check if user has other badges that grant the same role
      const otherBadgesWithSameRole = await this.db
        .select({
          badgeType: BadgeTypeSchema,
          userBadge: UserBadgeSchema
        })
        .from(UserBadgeSchema)
        .innerJoin(BadgeTypeSchema, eq(UserBadgeSchema.badgeTypeId, BadgeTypeSchema.id))
        .where(and(
          eq(UserBadgeSchema.userId, userId),
          eq(UserBadgeSchema.badgeTypeId, badgeTypeId)
        ));

      // Check if any other badges grant the same role
      const hasOtherBadgeWithSameRole = otherBadgesWithSameRole.some(({ badgeType }) => {
        if (badgeType.id === badgeTypeId) return false; // Skip the badge being removed
        
        const otherPerks = this.extractPerksFromBadge(badgeType);
        return otherPerks.some(otherPerk => 
          otherPerk.type === 'role_assignment' && 
          (otherPerk.roleId === roleId || otherPerk.value === perk.value)
        );
      });

      if (hasOtherBadgeWithSameRole) {
        // Don't revoke the role if user has other badges that grant it
        return {
          success: true,
          perk,
          assignment: {
            id: `${userId}_${roleId}_${badgeTypeId}`,
            userId,
            perkId: perk.id,
            badgeTypeId,
            serverId,
            assignedAt: new Date(),
            isActive: false
          }
        };
      }

      // Revoke the role
      const success = await removeRoleFromUser(userId, roleId, serverId);
      
      if (!success) {
        return {
          success: false,
          perk,
          error: 'Failed to remove role from user'
        };
      }

      // Broadcast perk revocation event
      await badgeWebSocketService.broadcastPerkRevoked({
        userId,
        perk,
        badgeTypeId,
        serverId
      });

      return {
        success: true,
        perk,
        assignment: {
          id: `${userId}_${roleId}_${badgeTypeId}`,
          userId,
          perkId: perk.id,
          badgeTypeId,
          serverId,
          assignedAt: new Date(),
          isActive: false
        }
      };

    } catch (error) {
      console.error('Error revoking role perk:', error);
      return {
        success: false,
        perk,
        error: `Role revocation failed: ${error.message}`
      };
    }
  }

  /**
   * Revokes permission-based perks
   */
  private async revokePermissionPerk(
    userId: string,
    perk: Perk,
    badgeTypeId: string,
    serverId?: string
  ): Promise<PerkAssignmentResult> {
    console.log(`Permission perk revoked from user ${userId}:`, {
      permissions: perk.permissions,
      badgeTypeId,
      serverId
    });

    return {
      success: true,
      perk,
      assignment: {
        id: `${userId}_${perk.id}_${badgeTypeId}`,
        userId,
        perkId: perk.id,
        badgeTypeId,
        serverId,
        assignedAt: new Date(),
        isActive: false,
        metadata: { permissions: perk.permissions }
      }
    };
  }

  /**
   * Revokes custom perks
   */
  private async revokeCustomPerk(
    userId: string,
    perk: Perk,
    badgeTypeId: string,
    serverId?: string
  ): Promise<PerkAssignmentResult> {
    console.log(`Custom perk revoked from user ${userId}:`, {
      type: perk.type,
      value: perk.value,
      metadata: perk.metadata,
      badgeTypeId,
      serverId
    });

    // Broadcast perk revocation event
    await badgeWebSocketService.broadcastPerkRevoked({
      userId,
      perk,
      badgeTypeId,
      serverId
    });

    return {
      success: true,
      perk,
      assignment: {
        id: `${userId}_${perk.id}_${badgeTypeId}`,
        userId,
        perkId: perk.id,
        badgeTypeId,
        serverId,
        assignedAt: new Date(),
        isActive: false,
        metadata: perk.metadata
      }
    };
  }

  /**
   * Gets all active perks for a user
   */
  async getUserPerks(userId: string, serverId?: string): Promise<Perk[]> {
    try {
      // Get all user badges
      const userBadges = await this.db
        .select({
          userBadge: UserBadgeSchema,
          badgeType: BadgeTypeSchema
        })
        .from(UserBadgeSchema)
        .innerJoin(BadgeTypeSchema, eq(UserBadgeSchema.badgeTypeId, BadgeTypeSchema.id))
        .where(eq(UserBadgeSchema.userId, userId));

      const allPerks: Perk[] = [];

      for (const { badgeType } of userBadges) {
        const badgePerks = this.extractPerksFromBadge(badgeType);
        
        // Filter perks by server if specified
        const filteredPerks = serverId 
          ? badgePerks.filter(perk => !perk.serverId || perk.serverId === serverId)
          : badgePerks;
        
        allPerks.push(...filteredPerks);
      }

      return allPerks;
    } catch (error) {
      console.error('Error getting user perks:', error);
      throw new BadgeValidationError('Failed to get user perks');
    }
  }

  /**
   * Gets available perks for display (what benefits are available)
   */
  async getAvailablePerks(serverId?: string): Promise<Perk[]> {
    try {
      // Get all active badge types
      const badgeTypes = await this.db
        .select()
        .from(BadgeTypeSchema)
        .where(eq(BadgeTypeSchema.isActive, true));

      const allPerks: Perk[] = [];

      for (const badgeType of badgeTypes) {
        const badgePerks = this.extractPerksFromBadge(badgeType);
        
        // Filter perks by server if specified
        const filteredPerks = serverId 
          ? badgePerks.filter(perk => !perk.serverId || perk.serverId === serverId)
          : badgePerks;
        
        allPerks.push(...filteredPerks);
      }

      // Remove duplicates based on perk type and value
      const uniquePerks = allPerks.filter((perk, index, array) => 
        array.findIndex(p => 
          p.type === perk.type && 
          p.value === perk.value && 
          p.serverId === perk.serverId
        ) === index
      );

      return uniquePerks;
    } catch (error) {
      console.error('Error getting available perks:', error);
      throw new BadgeValidationError('Failed to get available perks');
    }
  }

  /**
   * Validates user permission to manage perks
   */
  async validatePerkManagementPermission(userId: string, serverId?: string): Promise<void> {
    if (serverId) {
      const hasPermission = await hasServerPermission(
        this.db,
        userId,
        serverId,
        MANAGE_ROLES
      );
      
      if (!hasPermission) {
        throw new InsufficientPermissionsError();
      }
    } else {
      // For global perk operations, require admin permission
      // This is a placeholder - implement actual admin checking
      const isAdmin = await this.checkGlobalAdminPermission(userId);
      if (!isAdmin) {
        throw new InsufficientPermissionsError();
      }
    }
  }

  /**
   * Placeholder for global admin permission checking
   */
  private async checkGlobalAdminPermission(userId: string): Promise<boolean> {
    // TODO: Implement proper global admin checking
    // For now, allow all operations
    return true;
  }
}