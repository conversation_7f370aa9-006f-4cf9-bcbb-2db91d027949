// oba/services/logger.service.ts
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4,
}

export interface LoggerOptions {
  minLevel?: LogLevel;
  includeTimestamp?: boolean;
  includeContext?: boolean;
}

export class LoggerService {
  private minLevel: LogLevel;
  private includeTimestamp: boolean;
  private includeContext: boolean;

  // Singleton instance
  private static instance: LoggerService;

  // Private constructor to prevent direct instantiation
  private constructor(options: LoggerOptions = {}) {
    this.minLevel = options.minLevel ?? LogLevel.DEBUG;
    this.includeTimestamp = options.includeTimestamp ?? true;
    this.includeContext = options.includeContext ?? true;
  }

  // Static method to get the singleton instance
  public static getInstance(options: LoggerOptions = {}): LoggerService {
    if (!LoggerService.instance) {
      LoggerService.instance = new LoggerService(options);
    }
    return LoggerService.instance;
  }

  debug(message: string, context?: string, ...args: any[]) {
    this.log(LogLevel.DEBUG, message, context, ...args);
  }

  info(message: string, context?: string, ...args: any[]) {
    this.log(LogLevel.INFO, message, context, ...args);
  }

  warn(message: string, context?: string, ...args: any[]) {
    this.log(LogLevel.WARN, message, context, ...args);
  }

  error(message: string, context?: string, ...args: any[]) {
    this.log(LogLevel.ERROR, message, context, ...args);
  }

  private log(
    level: LogLevel,
    message: string,
    context?: string,
    ...args: any[]
  ) {
    if (level < this.minLevel) return;

    const timestamp = this.includeTimestamp ? new Date().toISOString() : "";
    const contextStr = this.includeContext && context ? `[${context}]` : "";
    const prefix = `${timestamp} ${this.getLevelString(level)} ${contextStr}`;

    switch (level) {
      case LogLevel.DEBUG:
        console.debug(`${prefix} ${message}`, ...args);
        break;
      case LogLevel.INFO:
        console.info(`${prefix} ${message}`, ...args);
        break;
      case LogLevel.WARN:
        console.warn(`${prefix} ${message}`, ...args);
        break;
      case LogLevel.ERROR:
        console.error(`${prefix} ${message}`, ...args);
        break;
    }
  }

  private getLevelString(level: LogLevel): string {
    switch (level) {
      case LogLevel.DEBUG:
        return "[DEBUG]";
      case LogLevel.INFO:
        return "[INFO]";
      case LogLevel.WARN:
        return "[WARN]";
      case LogLevel.ERROR:
        return "[ERROR]";
      default:
        return "";
    }
  }

  // Configure logger at runtime
  setMinLevel(level: LogLevel) {
    this.minLevel = level;
  }

  // Create a contextual logger that uses the singleton instance
  createLogger(context: string): ContextualLogger {
    return new ContextualLogger(this, context);
  }
}

// ContextualLogger class for creating loggers with specific contexts
export class ContextualLogger {
  private parentLogger: LoggerService;
  private context: string;

  constructor(parentLogger: LoggerService, context: string) {
    this.parentLogger = parentLogger;
    this.context = context;
  }

  debug(message: string, childContext?: string, ...args: any[]) {
    this.parentLogger.debug(message, childContext || this.context, ...args);
  }

  info(message: string, childContext?: string, ...args: any[]) {
    this.parentLogger.info(message, childContext || this.context, ...args);
  }

  warn(message: string, childContext?: string, ...args: any[]) {
    this.parentLogger.warn(message, childContext || this.context, ...args);
  }

  error(message: string, childContext?: string, ...args: any[]) {
    this.parentLogger.error(message, childContext || this.context, ...args);
  }

  // Create a nested contextual logger
  createLogger(nestedContext: string): ContextualLogger {
    return new ContextualLogger(this.parentLogger, nestedContext);
  }
}

// Create a default instance with DEBUG level
export const logger = LoggerService.getInstance({
  minLevel: LogLevel.DEBUG,
  includeTimestamp: true,
  includeContext: true,
});
