import { db } from '../db/index';
import { badgeCollections, badgeTypes, userBadges, userCollectionProgress } from '../db/schema';
import { eq, and, desc, asc, sql, count, avg } from 'drizzle-orm';
import type { 
  BadgeCollection, 
  BadgeType, 
  CreateBadgeCollectionRequest,
  UpdateBadgeCollectionRequest,
  CollectionAnalytics,
  CollectionProgressReport,
  BulkBadgeAssignmentRequest,
  CollectionTestResult
} from '../types/badge.types';
import { BadgeError } from '../class/badge-errors';

export class BadgeCollectionAdminService {
  /**
   * Create a new badge collection
   */
  async createCollection(data: CreateBadgeCollectionRequest): Promise<BadgeCollection> {
    try {
      const [collection] = await db.insert(badgeCollections).values({
        collectionId: data.collectionId,
        name: data.name,
        description: data.description,
        type: data.type || 'progressive',
        totalBadges: data.totalBadges || 0,
        unlockedBy: data.unlockedBy,
        completionReward: data.completionReward,
        isActive: data.isActive ?? true
      }).returning();

      return collection;
    } catch (error) {
      if (error instanceof Error && error.message.includes('unique constraint')) {
        throw new BadgeError(`Collection with ID '${data.collectionId}' already exists`, 'COLLECTION_ALREADY_EXISTS');
      }
      throw new BadgeError('Failed to create badge collection', 'COLLECTION_CREATE_FAILED');
    }
  }

  /**
   * Update an existing badge collection
   */
  async updateCollection(id: string, updates: UpdateBadgeCollectionRequest): Promise<BadgeCollection> {
    const [collection] = await db.update(badgeCollections)
      .set({
        ...updates,
        updatedAt: new Date()
      })
      .where(eq(badgeCollections.id, id))
      .returning();

    if (!collection) {
      throw new BadgeError(`Collection not found: ${id}`, 'COLLECTION_NOT_FOUND');
    }

    return collection;
  }

  /**
   * Delete a badge collection and handle dependencies
   */
  async deleteCollection(id: string): Promise<void> {
    const collection = await this.getCollectionById(id);
    
    // Check if collection has badges
    const badges = await db.select()
      .from(badgeTypes)
      .where(eq(badgeTypes.collectionId, id));

    if (badges.length > 0) {
      throw new BadgeError('Cannot delete collection with existing badges', 'COLLECTION_HAS_BADGES');
    }

    await db.delete(badgeCollections).where(eq(badgeCollections.id, id));
  }

  /**
   * Get collection by ID with full details
   */
  async getCollectionById(id: string): Promise<BadgeCollection> {
    const [collection] = await db.select()
      .from(badgeCollections)
      .where(eq(badgeCollections.id, id));

    if (!collection) {
      throw new BadgeError(`Collection not found: ${id}`, 'COLLECTION_NOT_FOUND');
    }

    return collection;
  }

  /**
   * Get all collections with optional filtering
   */
  async getCollections(filters?: {
    isActive?: boolean;
    type?: string;
    limit?: number;
    offset?: number;
  }): Promise<BadgeCollection[]> {
    let query = db.select().from(badgeCollections);

    if (filters?.isActive !== undefined) {
      query = query.where(eq(badgeCollections.isActive, filters.isActive));
    }

    if (filters?.type) {
      query = query.where(eq(badgeCollections.type, filters.type));
    }

    query = query.orderBy(asc(badgeCollections.name));

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    if (filters?.offset) {
      query = query.offset(filters.offset);
    }

    return await query;
  }

  /**
   * Reorder badges within a collection
   */
  async reorderCollectionBadges(collectionId: string, badgeOrders: Array<{ badgeId: string; displayOrder: number }>): Promise<void> {
    const collection = await this.getCollectionById(collectionId);
    
    // Validate all badges belong to this collection
    const badges = await db.select()
      .from(badgeTypes)
      .where(eq(badgeTypes.collectionId, collectionId));

    const badgeIds = badges.map(b => b.badgeId);
    const invalidBadges = badgeOrders.filter(order => !badgeIds.includes(order.badgeId));
    
    if (invalidBadges.length > 0) {
      throw new BadgeError('Some badges do not belong to this collection', 'INVALID_BADGE_COLLECTION');
    }

    // Update display orders in a transaction
    await db.transaction(async (tx) => {
      for (const { badgeId, displayOrder } of badgeOrders) {
        await tx.update(badgeTypes)
          .set({ displayOrder, updatedAt: new Date() })
          .where(and(
            eq(badgeTypes.collectionId, collectionId),
            eq(badgeTypes.badgeId, badgeId)
          ));
      }
    });
  }

  /**
   * Bulk assign badges from a collection to users
   */
  async bulkAssignCollectionBadges(request: BulkBadgeAssignmentRequest): Promise<{
    successful: number;
    failed: Array<{ userId: string; error: string }>;
  }> {
    const { collectionId, userIds, badgeIds, assignedBy } = request;
    
    const collection = await this.getCollectionById(collectionId);
    
    // Get badges to assign
    let badgesToAssign = await db.select()
      .from(badgeTypes)
      .where(eq(badgeTypes.collectionId, collectionId));

    if (badgeIds && badgeIds.length > 0) {
      badgesToAssign = badgesToAssign.filter(badge => badgeIds.includes(badge.badgeId));
    }

    const results = {
      successful: 0,
      failed: [] as Array<{ userId: string; error: string }>
    };

    for (const userId of userIds) {
      try {
        await db.transaction(async (tx) => {
          for (const badge of badgesToAssign) {
            // Check if user already has this badge
            const existing = await tx.select()
              .from(userBadges)
              .where(and(
                eq(userBadges.userId, userId),
                eq(userBadges.badgeTypeId, badge.id)
              ));

            if (existing.length === 0) {
              await tx.insert(userBadges).values({
                userId,
                badgeTypeId: badge.id,
                collectionId,
                assignedBy,
                assignedAt: new Date(),
                isVisible: true
              });
            }
          }
        });
        
        results.successful++;
      } catch (error) {
        results.failed.push({
          userId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }

  /**
   * Get collection analytics and statistics
   */
  async getCollectionAnalytics(collectionId: string): Promise<CollectionAnalytics> {
    const collection = await this.getCollectionById(collectionId);
    
    // Get badge count
    const [badgeCount] = await db.select({ count: count() })
      .from(badgeTypes)
      .where(eq(badgeTypes.collectionId, collectionId));

    // Get user progress statistics
    const progressStats = await db.select({
      totalUsers: count(),
      avgProgress: avg(userCollectionProgress.badgesEarned),
      completedUsers: sql<number>`COUNT(CASE WHEN ${userCollectionProgress.isCompleted} = true THEN 1 END)`
    })
    .from(userCollectionProgress)
    .where(eq(userCollectionProgress.collectionId, collectionId));

    // Get badge distribution
    const badgeDistribution = await db.select({
      badgeId: badgeTypes.badgeId,
      badgeName: badgeTypes.name,
      assignmentCount: count(userBadges.id)
    })
    .from(badgeTypes)
    .leftJoin(userBadges, eq(badgeTypes.id, userBadges.badgeTypeId))
    .where(eq(badgeTypes.collectionId, collectionId))
    .groupBy(badgeTypes.id, badgeTypes.badgeId, badgeTypes.name)
    .orderBy(asc(badgeTypes.displayOrder));

    const stats = progressStats[0] || { totalUsers: 0, avgProgress: 0, completedUsers: 0 };

    return {
      collection,
      totalBadges: badgeCount.count,
      totalUsers: stats.totalUsers,
      averageProgress: Number(stats.avgProgress) || 0,
      completionRate: stats.totalUsers > 0 ? (stats.completedUsers / stats.totalUsers) * 100 : 0,
      badgeDistribution: badgeDistribution.map(bd => ({
        badgeId: bd.badgeId,
        badgeName: bd.badgeName,
        assignmentCount: bd.assignmentCount
      }))
    };
  }

  /**
   * Get detailed progress report for a collection
   */
  async getCollectionProgressReport(collectionId: string, options?: {
    limit?: number;
    offset?: number;
    includeCompleted?: boolean;
  }): Promise<CollectionProgressReport> {
    const collection = await this.getCollectionById(collectionId);
    
    let query = db.select({
      userId: userCollectionProgress.userId,
      badgesEarned: userCollectionProgress.badgesEarned,
      totalBadges: userCollectionProgress.totalBadges,
      isCompleted: userCollectionProgress.isCompleted,
      completionDate: userCollectionProgress.completionDate,
      progressPercentage: sql<number>`(${userCollectionProgress.badgesEarned}::float / ${userCollectionProgress.totalBadges}::float) * 100`
    })
    .from(userCollectionProgress)
    .where(eq(userCollectionProgress.collectionId, collectionId));

    if (options?.includeCompleted === false) {
      query = query.where(eq(userCollectionProgress.isCompleted, false));
    }

    query = query.orderBy(desc(userCollectionProgress.badgesEarned));

    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.offset(options.offset);
    }

    const userProgress = await query;

    return {
      collection,
      userProgress: userProgress.map(up => ({
        userId: up.userId,
        badgesEarned: up.badgesEarned,
        totalBadges: up.totalBadges,
        progressPercentage: Number(up.progressPercentage) || 0,
        isCompleted: up.isCompleted,
        completionDate: up.completionDate
      }))
    };
  }

  /**
   * Test collection configuration and dependencies
   */
  async testCollection(collectionId: string): Promise<CollectionTestResult> {
    const collection = await this.getCollectionById(collectionId);
    const issues: string[] = [];
    const warnings: string[] = [];

    // Get all badges in collection
    const badges = await db.select()
      .from(badgeTypes)
      .where(eq(badgeTypes.collectionId, collectionId))
      .orderBy(asc(badgeTypes.displayOrder));

    // Test 1: Check if collection has badges
    if (badges.length === 0) {
      issues.push('Collection has no badges defined');
    }

    // Test 2: Check display order sequence
    const expectedOrders = Array.from({ length: badges.length }, (_, i) => i);
    const actualOrders = badges.map(b => b.displayOrder).sort((a, b) => a - b);
    
    if (JSON.stringify(expectedOrders) !== JSON.stringify(actualOrders)) {
      warnings.push('Badge display orders are not sequential (0, 1, 2, ...)');
    }

    // Test 3: Check for duplicate display orders
    const orderCounts = badges.reduce((acc, badge) => {
      acc[badge.displayOrder] = (acc[badge.displayOrder] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const duplicateOrders = Object.entries(orderCounts)
      .filter(([_, count]) => count > 1)
      .map(([order, _]) => order);

    if (duplicateOrders.length > 0) {
      issues.push(`Duplicate display orders found: ${duplicateOrders.join(', ')}`);
    }

    // Test 4: Validate badge criteria
    for (const badge of badges) {
      if (!badge.criteria || Object.keys(badge.criteria).length === 0) {
        warnings.push(`Badge '${badge.name}' has no criteria defined`);
      }
    }

    // Test 5: Check completion reward
    if (collection.type === 'progressive' && !collection.completionReward) {
      warnings.push('Progressive collection has no completion reward defined');
    }

    // Test 6: Validate total badges count
    if (collection.totalBadges !== badges.length) {
      issues.push(`Collection totalBadges (${collection.totalBadges}) doesn't match actual badge count (${badges.length})`);
    }

    return {
      collectionId,
      collectionName: collection.name,
      isValid: issues.length === 0,
      issues,
      warnings,
      badgeCount: badges.length,
      testedAt: new Date()
    };
  }

  /**
   * Preview collection for testing purposes
   */
  async previewCollection(collectionId: string): Promise<{
    collection: BadgeCollection;
    badges: BadgeType[];
    sampleUserProgress: Array<{
      userId: string;
      progress: number;
      nextBadge?: string;
    }>;
  }> {
    const collection = await this.getCollectionById(collectionId);
    
    const badges = await db.select()
      .from(badgeTypes)
      .where(eq(badgeTypes.collectionId, collectionId))
      .orderBy(asc(badgeTypes.displayOrder));

    // Get sample user progress (first 10 users)
    const sampleProgress = await db.select({
      userId: userCollectionProgress.userId,
      badgesEarned: userCollectionProgress.badgesEarned,
      totalBadges: userCollectionProgress.totalBadges
    })
    .from(userCollectionProgress)
    .where(eq(userCollectionProgress.collectionId, collectionId))
    .limit(10);

    const sampleUserProgress = sampleProgress.map(progress => {
      const nextBadgeIndex = progress.badgesEarned;
      const nextBadge = badges[nextBadgeIndex];
      
      return {
        userId: progress.userId,
        progress: (progress.badgesEarned / progress.totalBadges) * 100,
        nextBadge: nextBadge?.name
      };
    });

    return {
      collection,
      badges,
      sampleUserProgress
    };
  }
}