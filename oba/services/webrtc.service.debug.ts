import type { ServerWebSocket } from "bun";
import { VoiceWebSocketManager } from "../manager/websocket.debug";
import type { CustomWebSocketData } from "../types/websocket.types";

// WebRTC event types
export const WebRTCEventTypes = {
  WEBRTC_JOIN: "webrtc_join",
  WEBRTC_LEAVE: "webrtc_leave",
  WEBRTC_OFFER: "webrtc_offer",
  WEBRTC_ANSWER: "webrtc_answer",
  WEBRTC_ICE_CANDIDATE: "webrtc_ice_candidate",
  WEBRTC_STREAM_ADDED: "webrtc_stream_added",
  WEBRTC_STREAM_REMOVED: "webrtc_stream_removed",
  WEBRTC_MUTE_TOGGLE: "webrtc_mute_toggle",
  WEBRTC_DEAFEN_TOGGLE: "webrtc_deafen_toggle",
  WEBRTC_SPEAKING_STATE: "webrtc_speaking_state",
  WE<PERSON><PERSON>_CONNECTION_STATE: "webrtc_connection_state",
  WEBRTC_ERROR: "webrtc_error",
};

// STUN/TURN server configuration
export const stunTurnConfig = {
  iceServers: [
    {
      urls: [
        "stun:stun.l.google.com:19302", // Public STUN server (Google)
        "stun:stun1.l.google.com:19302",
        "stun:stun2.l.google.com:19302",
      ],
    },
    {
      urls: [
        "turn:coolify.berkormanli.dev:3478", // Your TURN server
      ],
      credential: "mypassword",
      username: "myuser",
    },
  ],
  iceCandidatePoolSize: 10,
};

// Using CustomWebSocketData from shared types

/**
 * WebRTC Signaling Service with Debug Logging
 *
 * Handles WebRTC signaling for peer-to-peer connections:
 * 1. Manages WebRTC signaling (offers, answers, ICE candidates)
 * 2. Tracks peer connections and their states
 * 3. Handles mute/deafen state changes
 */
export class WebRTCSignalingServiceDebug {
  private voiceWsManager: VoiceWebSocketManager;

  constructor(voiceWsManager: VoiceWebSocketManager) {
    console.log("VOICE-DEBUG: WebRTC Signaling Service initialized");
    this.voiceWsManager = voiceWsManager;
  }

  /**
   * Handle a user joining a voice channel
   */
  handleJoin(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(`VOICE-DEBUG: User ${ws.data.userId} joining voice channel`);
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(`${ws.data.serverId}:${ws.data.channelId}`, {
      type: WebRTCEventTypes.WEBRTC_JOIN,
      data: {
        userId: ws.data.userId,
        success: true,
      },
    });
  }

  /**
   * Handle a user leaving a voice channel
   */
  handleLeave(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(`VOICE-DEBUG: User ${ws.data.userId} leaving voice channel`);
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(`${ws.data.serverId}:${ws.data.channelId}`, {
      type: WebRTCEventTypes.WEBRTC_LEAVE,
      data: {
        userId: ws.data.userId,
      },
    });
  }

  /**
   * Handle WebRTC offer from a client
   */
  handleOffer(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(`VOICE-DEBUG: Received offer from user ${ws.data.userId}`);
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(
      `${ws.data.serverId}:${ws.data.channelId}`,
      {
        type: WebRTCEventTypes.WEBRTC_OFFER,
        data: {
          userId: ws.data.userId,
          offer: message.data.offer,
        },
      },
      ws.data.userId,
    );
  }

  /**
   * Handle WebRTC answer from a client
   */
  handleAnswer(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(`VOICE-DEBUG: Received answer from user ${ws.data.userId}`);
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(
      `${ws.data.serverId}:${ws.data.channelId}`,
      {
        type: WebRTCEventTypes.WEBRTC_ANSWER,
        data: {
          userId: ws.data.userId,
          answer: message.data.answer,
        },
      },
      ws.data.userId,
    );
  }

  /**
   * Handle ICE candidate from a client
   */
  handleIceCandidate(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(
      `VOICE-DEBUG: Received ICE candidate from user ${ws.data.userId}`,
    );
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(
      `${ws.data.serverId}:${ws.data.channelId}`,
      {
        type: WebRTCEventTypes.WEBRTC_ICE_CANDIDATE,
        data: {
          userId: ws.data.userId,
          candidate: message.data.candidate,
        },
      },
      ws.data.userId,
    );
  }

  /**
   * Handle mute toggle from a client
   */
  handleMuteToggle(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(
      `VOICE-DEBUG: User ${ws.data.userId} toggled mute: ${message.data.isMuted}`,
    );
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(`${ws.data.serverId}:${ws.data.channelId}`, {
      type: WebRTCEventTypes.WEBRTC_MUTE_TOGGLE,
      data: {
        userId: ws.data.userId,
        isMuted: message.data.isMuted,
      },
    });
  }

  /**
   * Handle deafen toggle from a client
   */
  handleDeafenToggle(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(
      `VOICE-DEBUG: User ${ws.data.userId} toggled deafen: ${message.data.isDeafened}`,
    );
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(`${ws.data.serverId}:${ws.data.channelId}`, {
      type: WebRTCEventTypes.WEBRTC_DEAFEN_TOGGLE,
      data: {
        userId: ws.data.userId,
        isDeafened: message.data.isDeafened,
      },
    });
  }

  /**
   * Handle speaking state change from a client
   */
  handleSpeakingState(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(
      `VOICE-DEBUG: User ${ws.data.userId} speaking state: ${message.data.isSpeaking}`,
    );
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(`${ws.data.serverId}:${ws.data.channelId}`, {
      type: WebRTCEventTypes.WEBRTC_SPEAKING_STATE,
      data: {
        userId: ws.data.userId,
        isSpeaking: message.data.isSpeaking,
      },
    });
  }

  /**
   * Broadcast a message to all clients in a channel
   *
   * @param channelKey Channel key (serverId:channelId)
   * @param message Message to broadcast
   * @param excludeUserId User ID to exclude from the broadcast (optional)
   */
  private broadcastToChannel(
    channelKey: string,
    message: any,
    excludeUserId?: string,
  ) {
    console.log(
      `VOICE-DEBUG: Broadcasting message to channel ${channelKey}, type: ${message.type}`,
    );
    const channelSockets = this.voiceWsManager.getSocketsInChannel(channelKey);

    if (!channelSockets) {
      console.warn(`VOICE-DEBUG: No sockets found for channel ${channelKey}`);
      return;
    }
    console.log(
      `VOICE-DEBUG: Found ${channelSockets.size} sockets in channel ${channelKey}`,
    );

    const messageStr = JSON.stringify(message);

    let broadcastCount = 0;
    channelSockets.forEach((socket) => {
      if (
        socket.readyState === WebSocket.OPEN &&
        (!excludeUserId || socket.data.userId !== excludeUserId)
      ) {
        console.log(
          `VOICE-DEBUG: Sending message to user ${socket.data.userId}`,
        );
        socket.send(messageStr);
        broadcastCount++;
      }
    });
    console.log(`VOICE-DEBUG: Broadcast message to ${broadcastCount} users`);
  }
}
