import type { drizzle } from "drizzle-orm/postgres-js";
import { 
  BadgeNotFoundError,
  BadgeAlreadyAssignedError,
  InsufficientPermissionsError,
  BadgeValidationError
} from "../class/badge-errors";
import {
  validateCreateEnhancedBadgeType,
  validateUpdateEnhancedBadgeType,
  validateAssignBadge,
  validateEnhancedBadgeFilters
} from "../utils/badge-validation-enhanced";
import {
  createBadgeType,
  getBadgeTypeById,
  getBadgeTypes,
  updateBadgeType,
  deleteBadgeType,
  assignBadgeToUser,
  removeBadgeFromUser,
  getUserBadges,
  getUserStats,
  getBadgeStats,
  getBadgeLeaderboard,
  getAvailableBadgesForUser,
  getBadgeProgress,
  bulkAssignBadges
} from "../db/utils/badge-utils";
import { badgeWebSocketService } from "../utils/badge-websocket";
import {
  evaluateUserForAutomaticBadges,
  batchEvaluateUsers,
  evaluateAllUsersForAutomaticBadges,
  reevaluateBadgeTypeForAllUsers
} from "../db/utils/badge-evaluation";
import { hasServerPermission } from "../utils/permissions";
import { ADMINISTRATOR, MANAGE_ROLES } from "../constants/permissions";
import { BadgeCollectionService } from "./badge-collection.service";
import { PerkService } from "./perk.service";
import { BadgeCollectionSchema } from "../db/schema";
import { eq } from "drizzle-orm";
import type {
  BadgeType,
  UserBadge,
  CreateBadgeTypeRequest,
  UpdateBadgeTypeRequest,
  BadgeTypeFilters,
  BadgeStats,
  BadgeLeaderboard,
  EvaluationResult,
  BadgeProgress,
  UserStats
} from "../types/badge.types";

/**
 * Badge Service - Core business logic for badge management
 * Handles badge type management, user badge operations, automatic evaluation, and permissions
 */
export class BadgeService {
  private collectionService: BadgeCollectionService;
  private perkService: PerkService;

  constructor(private db: ReturnType<typeof drizzle>) {
    this.collectionService = new BadgeCollectionService(db);
    this.perkService = new PerkService(db);
  }

  // Badge Type Management

  /**
   * Creates a new badge type with validation and permission checking
   */
  async createBadgeType(
    badgeData: CreateBadgeTypeRequest,
    createdBy: string
  ): Promise<BadgeType> {
    try {
      // Validate input data
      const validatedData = validateCreateEnhancedBadgeType(badgeData);

      // Check if user has permission to create badges (admin only)
      await this.validateAdminPermission(createdBy);

      // Check for duplicate badge names
      const existingBadges = await getBadgeTypes(this.db, { search: validatedData.name });
      const duplicateName = existingBadges.find(
        badge => badge.name.toLowerCase() === validatedData.name.toLowerCase()
      );
      
      if (duplicateName) {
        throw new BadgeValidationError(`Badge with name "${validatedData.name}" already exists`);
      }

      const newBadgeType = await createBadgeType(this.db, validatedData);

      // Update collection badge count if badge is part of a collection
      if (newBadgeType.collectionId) {
        try {
          // Get collection by UUID (internal ID)
          const [collection] = await this.db
            .select()
            .from(BadgeCollectionSchema)
            .where(eq(BadgeCollectionSchema.id, newBadgeType.collectionId))
            .limit(1);
          
          if (collection) {
            await this.collectionService.updateCollectionBadgeCount(collection.collectionId);
          }
        } catch (error) {
          console.error("Error updating collection badge count:", error);
          // Don't fail badge creation if collection update fails
        }
      }

      return newBadgeType;
    } catch (error) {
      if (error instanceof BadgeValidationError || error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error creating badge type:", error);
      throw new BadgeValidationError("Failed to create badge type");
    }
  }

  /**
   * Updates an existing badge type with validation and permission checking
   */
  async updateBadgeType(
    badgeTypeId: string,
    updates: UpdateBadgeTypeRequest,
    updatedBy: string
  ): Promise<BadgeType> {
    try {
      // Validate input data
      const validatedUpdates = validateUpdateEnhancedBadgeType(updates);

      // Check if user has permission to update badges (admin only)
      await this.validateAdminPermission(updatedBy);

      // Check if badge exists
      const existingBadge = await getBadgeTypeById(this.db, badgeTypeId);
      if (!existingBadge) {
        throw new BadgeNotFoundError(badgeTypeId);
      }

      // Check for duplicate names if name is being updated
      if (validatedUpdates.name && validatedUpdates.name !== existingBadge.name) {
        const existingBadges = await getBadgeTypes(this.db, { search: validatedUpdates.name });
        const duplicateName = existingBadges.find(
          badge => badge.name.toLowerCase() === validatedUpdates.name!.toLowerCase() && badge.id !== badgeTypeId
        );
        
        if (duplicateName) {
          throw new BadgeValidationError(`Badge with name "${validatedUpdates.name}" already exists`);
        }
      }

      const updatedBadge = await updateBadgeType(this.db, badgeTypeId, validatedUpdates);
      if (!updatedBadge) {
        throw new BadgeNotFoundError(badgeTypeId);
      }

      return updatedBadge;
    } catch (error) {
      if (error instanceof BadgeValidationError || error instanceof BadgeNotFoundError || error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error updating badge type:", error);
      throw new BadgeValidationError("Failed to update badge type");
    }
  }

  /**
   * Deletes a badge type and all associated user badges
   */
  async deleteBadgeType(badgeTypeId: string, deletedBy: string): Promise<void> {
    try {
      // Check if user has permission to delete badges (admin only)
      await this.validateAdminPermission(deletedBy);

      // Check if badge exists
      const existingBadge = await getBadgeTypeById(this.db, badgeTypeId);
      if (!existingBadge) {
        throw new BadgeNotFoundError(badgeTypeId);
      }

      const success = await deleteBadgeType(this.db, badgeTypeId);
      if (!success) {
        throw new BadgeNotFoundError(badgeTypeId);
      }
    } catch (error) {
      if (error instanceof BadgeNotFoundError || error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error deleting badge type:", error);
      throw new BadgeValidationError("Failed to delete badge type");
    }
  }

  /**
   * Gets badge types with optional filtering
   */
  async getBadgeTypes(
    filters?: BadgeTypeFilters,
    limit?: number,
    offset?: number
  ): Promise<BadgeType[]> {
    try {
      const validatedFilters = filters ? validateEnhancedBadgeFilters(filters) : undefined;
      return await getBadgeTypes(this.db, validatedFilters, limit, offset);
    } catch (error) {
      console.error("Error getting badge types:", error);
      throw new BadgeValidationError("Failed to get badge types");
    }
  }

  /**
   * Gets a specific badge type by ID
   */
  async getBadgeTypeById(badgeTypeId: string): Promise<BadgeType> {
    try {
      const badgeType = await getBadgeTypeById(this.db, badgeTypeId);
      if (!badgeType) {
        throw new BadgeNotFoundError(badgeTypeId);
      }
      return badgeType;
    } catch (error) {
      if (error instanceof BadgeNotFoundError) {
        throw error;
      }
      console.error("Error getting badge type by ID:", error);
      throw new BadgeValidationError("Failed to get badge type");
    }
  }

  // User Badge Operations

  /**
   * Assigns a badge to a user with validation and permission checking
   */
  async assignBadge(
    userId: string,
    badgeTypeId: string,
    assignedBy: string,
    serverId?: string
  ): Promise<UserBadge> {
    try {
      // Validate input
      const validatedData = validateAssignBadge({ userId, badgeTypeId });

      // Check if badge exists and is active
      const badgeType = await getBadgeTypeById(this.db, badgeTypeId);
      if (!badgeType) {
        throw new BadgeNotFoundError(badgeTypeId);
      }

      if (!badgeType.isActive) {
        throw new BadgeValidationError("Cannot assign inactive badge");
      }

      // For manual badges, check permissions
      if (badgeType.assignmentType === 'manual') {
        await this.validateBadgeAssignmentPermission(assignedBy, serverId);
      }

      // Check for duplicate assignment
      const existingBadges = await getUserBadges(this.db, userId);
      const hasBadge = existingBadges.some(badge => badge.badgeTypeId === badgeTypeId);
      
      if (hasBadge) {
        throw new BadgeAlreadyAssignedError(userId, badgeTypeId);
      }

      // Check collection dependencies if badge is part of a collection
      if (badgeType.collectionId) {
        const collectionCheck = await this.collectionService.assignBadgeWithCollectionCheck(
          userId,
          badgeTypeId,
          assignedBy
        );

        if (!collectionCheck.success) {
          throw new BadgeValidationError(collectionCheck.error || "Collection dependency check failed");
        }
      }

      const userBadge = await assignBadgeToUser(this.db, userId, badgeTypeId, assignedBy);
      if (!userBadge) {
        throw new BadgeValidationError("Failed to assign badge");
      }

      // Update collection progress if badge is part of a collection
      if (badgeType.collectionId) {
        try {
          const collection = await this.collectionService.getCollectionById(badgeType.collectionId);
          await this.collectionService.updateUserCollectionProgress(
            userId,
            collection.collectionId,
            true
          );
        } catch (error) {
          console.error("Error updating collection progress:", error);
          // Don't fail the badge assignment if collection update fails
        }
      }

      // Assign perks for the badge
      try {
        const perkResults = await this.perkService.assignPerksForBadge(userId, badgeType, serverId);
        const failedPerks = perkResults.filter(result => !result.success);
        
        if (failedPerks.length > 0) {
          console.warn(`Some perks failed to assign for badge ${badgeType.name}:`, failedPerks);
        }
        
        // Update user badge with granted perks information
        const grantedPerks = perkResults
          .filter(result => result.success)
          .map(result => result.perk?.name)
          .filter(Boolean);
        
        if (grantedPerks.length > 0) {
          // Update the user badge record with granted perks
          // This would require updating the database record, but for now we'll just log it
          console.log(`Perks granted for badge ${badgeType.name}:`, grantedPerks);
        }
      } catch (error) {
        console.error(`Error assigning perks for badge ${badgeType.name}:`, error);
        // Don't fail the badge assignment if perk assignment fails
      }

      // Broadcast badge assigned event
      await badgeWebSocketService.broadcastBadgeAssigned({
        userId,
        badge: userBadge,
        isAutomatic: badgeType.assignmentType === 'automatic',
        assignedBy: badgeType.assignmentType === 'manual' ? assignedBy : undefined
      });

      return userBadge;
    } catch (error) {
      if (error instanceof BadgeNotFoundError || 
          error instanceof BadgeAlreadyAssignedError || 
          error instanceof BadgeValidationError ||
          error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error assigning badge:", error);
      throw new BadgeValidationError("Failed to assign badge");
    }
  }

  /**
   * Removes a badge from a user with permission checking
   */
  async removeBadge(
    userId: string,
    badgeTypeId: string,
    removedBy: string,
    serverId?: string
  ): Promise<void> {
    try {
      // Check if badge exists
      const badgeType = await getBadgeTypeById(this.db, badgeTypeId);
      if (!badgeType) {
        throw new BadgeNotFoundError(badgeTypeId);
      }

      // Check permissions for badge removal
      await this.validateBadgeAssignmentPermission(removedBy, serverId);

      // Check if user has the badge
      const userBadges = await getUserBadges(this.db, userId);
      const hasBadge = userBadges.some(badge => badge.badgeTypeId === badgeTypeId);
      
      if (!hasBadge) {
        throw new BadgeValidationError("User does not have this badge");
      }

      const success = await removeBadgeFromUser(this.db, userId, badgeTypeId);
      if (!success) {
        throw new BadgeValidationError("Failed to remove badge");
      }

      // Revoke perks for the badge
      try {
        const perkResults = await this.perkService.revokePerksForBadge(userId, badgeType, serverId);
        const failedRevocations = perkResults.filter(result => !result.success);
        
        if (failedRevocations.length > 0) {
          console.warn(`Some perks failed to revoke for badge ${badgeType.name}:`, failedRevocations);
        }
      } catch (error) {
        console.error(`Error revoking perks for badge ${badgeType.name}:`, error);
        // Don't fail the badge removal if perk revocation fails
      }

      // Broadcast badge removed event
      await badgeWebSocketService.broadcastBadgeRemoved({
        userId,
        badgeTypeId,
        badgeType,
        removedBy
      });
    } catch (error) {
      if (error instanceof BadgeNotFoundError || 
          error instanceof BadgeValidationError ||
          error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error removing badge:", error);
      throw new BadgeValidationError("Failed to remove badge");
    }
  }

  /**
   * Gets all badges for a user
   */
  async getUserBadges(userId: string, visibleOnly: boolean = false): Promise<UserBadge[]> {
    try {
      return await getUserBadges(this.db, userId, visibleOnly);
    } catch (error) {
      console.error("Error getting user badges:", error);
      throw new BadgeValidationError("Failed to get user badges");
    }
  }

  /**
   * Gets available badges for a user (badges they don't have yet)
   */
  async getAvailableBadgesForUser(userId: string): Promise<BadgeType[]> {
    try {
      return await getAvailableBadgesForUser(this.db, userId);
    } catch (error) {
      console.error("Error getting available badges:", error);
      throw new BadgeValidationError("Failed to get available badges");
    }
  }

  // Automatic Badge Evaluation

  /**
   * Evaluates automatic badges for a single user
   */
  async evaluateUserBadges(userId: string): Promise<EvaluationResult> {
    try {
      return await evaluateUserForAutomaticBadges(this.db, userId);
    } catch (error) {
      console.error("Error evaluating user badges:", error);
      return {
        userId,
        newBadges: [],
        evaluatedBadges: [],
        errors: [`Evaluation failed: ${error.message}`]
      };
    }
  }

  /**
   * Evaluates automatic badges for multiple users
   */
  async batchEvaluateUsers(userIds: string[]): Promise<EvaluationResult[]> {
    try {
      return await batchEvaluateUsers(this.db, userIds);
    } catch (error) {
      console.error("Error in batch evaluation:", error);
      throw new BadgeValidationError("Failed to batch evaluate users");
    }
  }

  /**
   * Evaluates all users for automatic badges (admin only)
   */
  async evaluateAllUsers(
    requestedBy: string,
    batchSize: number = 50
  ): Promise<{
    totalUsers: number;
    processedUsers: number;
    totalNewBadges: number;
    errors: string[];
  }> {
    try {
      // Check admin permission
      await this.validateAdminPermission(requestedBy);

      return await evaluateAllUsersForAutomaticBadges(this.db, batchSize);
    } catch (error) {
      if (error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error evaluating all users:", error);
      throw new BadgeValidationError("Failed to evaluate all users");
    }
  }

  /**
   * Re-evaluates a specific badge type for all users (admin only)
   */
  async reevaluateBadgeType(
    badgeTypeId: string,
    requestedBy: string
  ): Promise<{
    evaluatedUsers: number;
    newAssignments: number;
    errors: string[];
  }> {
    try {
      // Check admin permission
      await this.validateAdminPermission(requestedBy);

      // Check if badge exists
      const badgeType = await getBadgeTypeById(this.db, badgeTypeId);
      if (!badgeType) {
        throw new BadgeNotFoundError(badgeTypeId);
      }

      return await reevaluateBadgeTypeForAllUsers(this.db, badgeTypeId);
    } catch (error) {
      if (error instanceof BadgeNotFoundError || error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error re-evaluating badge type:", error);
      throw new BadgeValidationError("Failed to re-evaluate badge type");
    }
  }

  // Badge Statistics and Leaderboard

  /**
   * Gets badge system statistics
   */
  async getBadgeStats(): Promise<BadgeStats> {
    try {
      return await getBadgeStats(this.db);
    } catch (error) {
      console.error("Error getting badge stats:", error);
      throw new BadgeValidationError("Failed to get badge statistics");
    }
  }

  /**
   * Gets badge leaderboard
   */
  async getBadgeLeaderboard(limit: number = 10): Promise<BadgeLeaderboard[]> {
    try {
      return await getBadgeLeaderboard(this.db, limit);
    } catch (error) {
      console.error("Error getting badge leaderboard:", error);
      throw new BadgeValidationError("Failed to get badge leaderboard");
    }
  }

  /**
   * Gets badge progress for automatic badges
   */
  async getBadgeProgress(userId: string, badgeTypeId?: string): Promise<BadgeProgress[]> {
    try {
      return await getBadgeProgress(this.db, userId, badgeTypeId);
    } catch (error) {
      console.error("Error getting badge progress:", error);
      throw new BadgeValidationError("Failed to get badge progress");
    }
  }

  /**
   * Gets detailed badge progress with collection context
   */
  async getDetailedBadgeProgress(userId: string): Promise<BadgeProgress[]> {
    try {
      const progress = await getBadgeProgress(this.db, userId);
      
      // Enhance progress with collection information
      const enhancedProgress = await Promise.all(
        progress.map(async (p) => {
          if (p.badgeType.collectionId) {
            // Get collection progress if badge is part of a collection
            // This would require collection utilities that aren't implemented yet
            // For now, we'll return the basic progress
            return p;
          }
          return p;
        })
      );
      
      return enhancedProgress;
    } catch (error) {
      console.error("Error getting detailed badge progress:", error);
      throw new BadgeValidationError("Failed to get detailed badge progress");
    }
  }

  /**
   * Updates badge visibility for a user
   */
  async updateBadgeVisibility(
    userId: string,
    badgeTypeId: string,
    isVisible: boolean
  ): Promise<void> {
    try {
      const { updateBadgeVisibility } = await import("../db/utils/badge-utils");
      const success = await updateBadgeVisibility(this.db, userId, badgeTypeId, isVisible);
      
      if (!success) {
        throw new BadgeValidationError("Failed to update badge visibility");
      }
    } catch (error) {
      console.error("Error updating badge visibility:", error);
      throw new BadgeValidationError("Failed to update badge visibility");
    }
  }

  /**
   * Gets user statistics for badge evaluation
   */
  async getUserStats(userId: string): Promise<UserStats> {
    try {
      return await getUserStats(this.db, userId);
    } catch (error) {
      console.error("Error getting user stats:", error);
      throw new BadgeValidationError("Failed to get user statistics");
    }
  }

  // Bulk Operations

  /**
   * Bulk assign badges to multiple users (admin only)
   */
  async bulkAssignBadges(
    assignments: Array<{
      userId: string;
      badgeTypeId: string;
    }>,
    assignedBy: string,
    serverId?: string
  ): Promise<UserBadge[]> {
    try {
      // Check permissions
      await this.validateBadgeAssignmentPermission(assignedBy, serverId);

      // Validate all assignments
      const validatedAssignments = assignments.map(assignment => 
        validateAssignBadge(assignment)
      );

      // Add assignedBy to each assignment
      const assignmentsWithAssigner = validatedAssignments.map(assignment => ({
        ...assignment,
        assignedBy
      }));

      const assignedBadges = await bulkAssignBadges(this.db, assignmentsWithAssigner);

      // Broadcast badge assigned events for all successful assignments
      const broadcastPayloads = assignedBadges.map(badge => ({
        userId: badge.userId,
        badge,
        isAutomatic: false, // Bulk assignments are always manual
        assignedBy
      }));

      await badgeWebSocketService.broadcastBatchBadgeAssigned(broadcastPayloads);

      return assignedBadges;
    } catch (error) {
      if (error instanceof InsufficientPermissionsError || error instanceof BadgeValidationError) {
        throw error;
      }
      console.error("Error bulk assigning badges:", error);
      throw new BadgeValidationError("Failed to bulk assign badges");
    }
  }

  // Permission Validation Helpers

  /**
   * Validates admin permission for badge management operations
   */
  private async validateAdminPermission(userId: string): Promise<void> {
    // For now, we'll implement a simple check
    // In a real implementation, you would check user roles/permissions
    // This is a placeholder that should be replaced with actual permission logic
    
    // TODO: Implement proper admin permission checking
    // For now, we'll assume all operations are allowed
    // In production, you would:
    // 1. Check if user has admin role
    // 2. Check if user has specific badge management permissions
    // 3. Validate against server-specific permissions if applicable
    
    try {
      // Placeholder implementation - replace with actual permission checking
      // const isAdmin = await checkUserAdminStatus(this.db, userId);
      // if (!isAdmin) {
      //   throw new InsufficientPermissionsError();
      // }
      
      // For now, allow all operations
      return;
    } catch (error) {
      console.error("Error validating admin permission:", error);
      throw new InsufficientPermissionsError();
    }
  }

  // Perk Management Methods

  /**
   * Gets all active perks for a user
   */
  async getUserPerks(userId: string, serverId?: string): Promise<any[]> {
    try {
      return await this.perkService.getUserPerks(userId, serverId);
    } catch (error) {
      console.error("Error getting user perks:", error);
      throw new BadgeValidationError("Failed to get user perks");
    }
  }

  /**
   * Gets available perks for display
   */
  async getAvailablePerks(serverId?: string): Promise<any[]> {
    try {
      return await this.perkService.getAvailablePerks(serverId);
    } catch (error) {
      console.error("Error getting available perks:", error);
      throw new BadgeValidationError("Failed to get available perks");
    }
  }

  /**
   * Validates perk configuration for a badge type
   */
  async validateBadgePerks(badgeType: BadgeType, serverId?: string): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    try {
      if (!badgeType.perks || badgeType.perks.length === 0) {
        return { isValid: true, errors: [], warnings: [] };
      }

      const allErrors: string[] = [];
      const allWarnings: string[] = [];

      // Extract and validate each perk
      const perks = badgeType.perks.map((perkData, index) => {
        if (typeof perkData === 'string') {
          return this.perkService['parseStringPerk'](perkData, `${badgeType.id}_${index}`);
        } else if (typeof perkData === 'object') {
          return this.perkService['parseObjectPerk'](perkData, `${badgeType.id}_${index}`);
        }
        throw new Error(`Invalid perk format at index ${index}`);
      });

      // Validate each perk
      for (const perk of perks) {
        const validation = await this.perkService.validatePerk(perk, 'validation', serverId);
        allErrors.push(...validation.errors);
        allWarnings.push(...validation.warnings);
      }

      return {
        isValid: allErrors.length === 0,
        errors: allErrors,
        warnings: allWarnings
      };
    } catch (error) {
      console.error("Error validating badge perks:", error);
      return {
        isValid: false,
        errors: [`Perk validation failed: ${error.message}`],
        warnings: []
      };
    }
  }

  /**
   * Manually assigns perks to a user (admin only)
   */
  async manuallyAssignPerks(
    userId: string,
    badgeTypeId: string,
    requestedBy: string,
    serverId?: string
  ): Promise<any[]> {
    try {
      // Validate permissions
      await this.perkService.validatePerkManagementPermission(requestedBy, serverId);

      // Get badge type
      const badgeType = await this.getBadgeTypeById(badgeTypeId);
      
      // Assign perks
      const results = await this.perkService.assignPerksForBadge(userId, badgeType, serverId);
      
      return results;
    } catch (error) {
      if (error instanceof InsufficientPermissionsError || error instanceof BadgeNotFoundError) {
        throw error;
      }
      console.error("Error manually assigning perks:", error);
      throw new BadgeValidationError("Failed to manually assign perks");
    }
  }

  /**
   * Manually revokes perks from a user (admin only)
   */
  async manuallyRevokePerks(
    userId: string,
    badgeTypeId: string,
    requestedBy: string,
    serverId?: string
  ): Promise<any[]> {
    try {
      // Validate permissions
      await this.perkService.validatePerkManagementPermission(requestedBy, serverId);

      // Get badge type
      const badgeType = await this.getBadgeTypeById(badgeTypeId);
      
      // Revoke perks
      const results = await this.perkService.revokePerksForBadge(userId, badgeType, serverId);
      
      return results;
    } catch (error) {
      if (error instanceof InsufficientPermissionsError || error instanceof BadgeNotFoundError) {
        throw error;
      }
      console.error("Error manually revoking perks:", error);
      throw new BadgeValidationError("Failed to manually revoke perks");
    }
  }

  /**
   * Validates permission for badge assignment/removal operations
   */
  private async validateBadgeAssignmentPermission(
    userId: string,
    serverId?: string
  ): Promise<void> {
    try {
      if (serverId) {
        // Check server-specific permissions
        const hasPermission = await hasServerPermission(
          this.db,
          userId,
          serverId,
          MANAGE_ROLES // Use MANAGE_ROLES permission for badge assignments
        );
        
        if (!hasPermission) {
          throw new InsufficientPermissionsError();
        }
      } else {
        // For global badge operations, require admin permission
        await this.validateAdminPermission(userId);
      }
    } catch (error) {
      if (error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error validating badge assignment permission:", error);
      throw new InsufficientPermissionsError();
    }
  }
}