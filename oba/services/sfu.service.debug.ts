import type { ServerWebSocket } from "bun";
import { VoiceWebSocketManager } from "../manager/websocket.debug";
import {
  parseVoiceMessage,
  validateVoiceMetadata,
} from "../utils/binaryProtocol.debug";
import type { CustomWebSocketData, User } from "../types/websocket.types";

// WebRTC event types for SFU
export const SFUEventTypes = {
  SFU_JOIN: "sfu_join",
  SFU_LEAVE: "sfu_leave",
  SFU_OFFER: "sfu_offer",
  SFU_ANSWER: "sfu_answer",
  SFU_ICE_CANDIDATE: "sfu_ice_candidate",
  SFU_STREAM_ADDED: "sfu_stream_added",
  SFU_STREAM_REMOVED: "sfu_stream_removed",
  SFU_MUTE_TOGGLE: "sfu_mute_toggle",
  SFU_DEAFEN_TOGGLE: "sfu_deafen_toggle",
  SFU_SPEAKING_STATE: "sfu_speaking_state",
  SFU_CONNECTION_STATE: "sfu_connection_state",
  SFU_ERROR: "sfu_error",
  SFU_VOICE_DATA: "voice_data",
};

// STUN/TURN server configuration
export const stunTurnConfig = {
  iceServers: [
    {
      urls: [
        "stun:stun.l.google.com:19302", // Public STUN server (Google)
        "stun:stun1.l.google.com:19302",
        "stun:stun2.l.google.com:19302",
      ],
    },
    {
      urls: [
        "turn:coolify.berkormanli.dev:3478", // Your TURN server
      ],
      credential: "mypassword",
      username: "myuser",
    },
  ],
  iceCandidatePoolSize: 10,
};

// Using CustomWebSocketData from shared types

// Interface for SFU client connection
interface SFUClientConnection {
  userId: string;
  peerConnection: any; // In a real implementation, this would be RTCPeerConnection
  audioTransceiver: any; // In a real implementation, this would be RTCRtpTransceiver
  dataChannel: any; // In a real implementation, this would be RTCDataChannel
  serverId: string;
  channelId: string;
  isMuted: boolean;
  isDeafened: boolean;
  isSpeaking: boolean;
  lastActivity: number;
  connectionState: string;
  streams: Map<string, any>; // Map of streams this client is receiving
  speakingTimeout?: any; // Timeout for speaking state
}

/**
 * Enhanced Selective Forwarding Unit (SFU) Service with Debugging
 *
 * Handles WebRTC connections for voice chat in a client-server-client model:
 * 1. Each client connects to the SFU server
 * 2. The SFU server forwards audio streams between clients
 * 3. This reduces client complexity and improves reliability
 */
export class SFUServiceDebug {
  private voiceWsManager: VoiceWebSocketManager;
  private clientConnections: Map<string, SFUClientConnection> = new Map();
  private channelClients: Map<string, Set<string>> = new Map(); // Map of channelId -> Set of userIds

  constructor(voiceWsManager: VoiceWebSocketManager) {
    console.log("VOICE-DEBUG: SFU Service initialized");
    this.voiceWsManager = voiceWsManager;
  }

  /**
   * Handle a user joining a voice channel
   */
  handleJoin(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(`VOICE-DEBUG: User ${ws.data.userId} joining voice channel`);
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(`${ws.data.serverId}:${ws.data.channelId}`, {
      type: SFUEventTypes.SFU_JOIN,
      data: {
        userId: ws.data.userId,
        success: true,
      },
    });
  }

  /**
   * Handle a user leaving a voice channel
   */
  handleLeave(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(`VOICE-DEBUG: User ${ws.data.userId} leaving voice channel`);
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(`${ws.data.serverId}:${ws.data.channelId}`, {
      type: SFUEventTypes.SFU_LEAVE,
      data: {
        userId: ws.data.userId,
      },
    });
  }

  /**
   * Handle WebRTC offer from a client
   */
  handleOffer(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(`VOICE-DEBUG: Received offer from user ${ws.data.userId}`);
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(
      `${ws.data.serverId}:${ws.data.channelId}`,
      {
        type: SFUEventTypes.SFU_OFFER,
        data: {
          userId: ws.data.userId,
          offer: message.data.offer,
        },
      },
      ws.data.userId,
    );
  }

  /**
   * Handle ICE candidate from a client
   */
  handleIceCandidate(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(
      `VOICE-DEBUG: Received ICE candidate from user ${ws.data.userId}`,
    );
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(
      `${ws.data.serverId}:${ws.data.channelId}`,
      {
        type: SFUEventTypes.SFU_ICE_CANDIDATE,
        data: {
          userId: ws.data.userId,
          candidate: message.data.candidate,
        },
      },
      ws.data.userId,
    );
  }

  /**
   * Handle mute toggle from a client
   */
  handleMuteToggle(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(
      `VOICE-DEBUG: User ${ws.data.userId} toggled mute: ${message.data.isMuted}`,
    );
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(`${ws.data.serverId}:${ws.data.channelId}`, {
      type: SFUEventTypes.SFU_MUTE_TOGGLE,
      data: {
        userId: ws.data.userId,
        isMuted: message.data.isMuted,
      },
    });
  }

  /**
   * Handle deafen toggle from a client
   */
  handleDeafenToggle(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(
      `VOICE-DEBUG: User ${ws.data.userId} toggled deafen: ${message.data.isDeafened}`,
    );
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(`${ws.data.serverId}:${ws.data.channelId}`, {
      type: SFUEventTypes.SFU_DEAFEN_TOGGLE,
      data: {
        userId: ws.data.userId,
        isDeafened: message.data.isDeafened,
      },
    });
  }

  /**
   * Handle speaking state change from a client
   */
  handleSpeakingState(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    console.log(
      `VOICE-DEBUG: User ${ws.data.userId} speaking state: ${message.data.isSpeaking}`,
    );
    console.log(`VOICE-DEBUG: Message:`, message);

    // Implementation would go here
    this.broadcastToChannel(`${ws.data.serverId}:${ws.data.channelId}`, {
      type: SFUEventTypes.SFU_SPEAKING_STATE,
      data: {
        userId: ws.data.userId,
        isSpeaking: message.data.isSpeaking,
      },
    });
  }

  /**
   * Handle binary voice data from a client with enhanced debugging
   *
   * @param ws WebSocket connection of the user sending voice data
   * @param binaryData Binary voice data
   */
  handleVoiceData(
    ws: ServerWebSocket<CustomWebSocketData>,
    binaryData: ArrayBuffer,
  ) {
    console.log(
      `VOICE-DEBUG: Handling voice data from user ${ws.data.userId}, data size: ${binaryData.byteLength} bytes`,
    );

    try {
      // Parse the binary message
      console.log("VOICE-DEBUG: Parsing binary message...");
      const { metadata, audioData } = parseVoiceMessage(binaryData);
      console.log(
        `VOICE-DEBUG: Parsed binary message, audio data size: ${audioData.byteLength} bytes`,
      );
      console.log("VOICE-DEBUG: Metadata:", metadata);

      // Validate metadata
      console.log("VOICE-DEBUG: Validating metadata...");
      if (!validateVoiceMetadata(metadata)) {
        console.error("VOICE-DEBUG: Invalid voice data metadata");
        return;
      }
      console.log("VOICE-DEBUG: Metadata is valid");

      const { userId, timestamp, sequence } = metadata;
      console.log(
        `VOICE-DEBUG: Voice data from user ${userId}, sequence: ${sequence}, timestamp: ${timestamp}`,
      );

      // Ensure the user ID matches the WebSocket user ID
      if (userId !== ws.data.userId) {
        console.error(
          `VOICE-DEBUG: User ID mismatch in voice data. Expected: ${ws.data.userId}, Got: ${userId}`,
        );
        return;
      }
      console.log("VOICE-DEBUG: User ID matches WebSocket user ID");

      // Get the channel key
      const serverId = ws.data.serverId;
      const channelId = ws.data.channelId;

      if (!serverId || !channelId) {
        console.error(
          `VOICE-DEBUG: Missing serverId or channelId for voice data. serverId: ${serverId}, channelId: ${channelId}`,
        );
        return;
      }
      console.log(`VOICE-DEBUG: Channel key: ${serverId}:${channelId}`);

      const channelKey = `${serverId}:${channelId}`;

      // Check if the user is muted
      const clientConnection = this.clientConnections.get(userId);
      if (clientConnection?.isMuted) {
        console.log(
          `VOICE-DEBUG: User ${userId} is muted, not forwarding audio`,
        );
        return;
      }
      console.log(
        `VOICE-DEBUG: User ${userId} is not muted, proceeding with forwarding`,
      );

      // Forward the voice data to all other users in the channel
      console.log(`VOICE-DEBUG: Getting sockets in channel ${channelKey}...`);
      const channelSockets =
        this.voiceWsManager.getSocketsInChannel(channelKey);

      if (!channelSockets) {
        console.error(
          `VOICE-DEBUG: No sockets found for channel ${channelKey}`,
        );
        return;
      }
      console.log(
        `VOICE-DEBUG: Found ${channelSockets.size} sockets in channel ${channelKey}`,
      );

      // Forward the original binary message to all other users
      let forwardCount = 0;
      channelSockets.forEach((socket) => {
        if (socket !== ws && socket.readyState === WebSocket.OPEN) {
          console.log(
            `VOICE-DEBUG: Forwarding voice data to user ${socket.data.userId}`,
          );
          socket.send(binaryData);
          forwardCount++;
        }
      });
      console.log(`VOICE-DEBUG: Forwarded voice data to ${forwardCount} users`);

      // Update speaking state if needed
      if (!clientConnection?.isSpeaking) {
        console.log(
          `VOICE-DEBUG: User ${userId} started speaking, updating speaking state`,
        );
        // User started speaking
        if (clientConnection) {
          clientConnection.isSpeaking = true;
          clientConnection.lastActivity = Date.now();

          // Update user data in WebSocket
          if (ws.data.user) {
            ws.data.user.speaking = true;
          }

          // Broadcast speaking state
          this.broadcastToChannel(channelKey, {
            type: SFUEventTypes.SFU_SPEAKING_STATE,
            data: {
              userId,
              isSpeaking: true,
              timestamp: Date.now(),
            },
          });
          console.log(
            `VOICE-DEBUG: Broadcast speaking state (true) for user ${userId}`,
          );
        }
      }

      // Set a timeout to mark the user as not speaking after a period of silence
      if (clientConnection) {
        if (clientConnection.speakingTimeout) {
          clearTimeout(clientConnection.speakingTimeout);
        }

        clientConnection.speakingTimeout = setTimeout(() => {
          if (clientConnection.isSpeaking) {
            console.log(
              `VOICE-DEBUG: User ${userId} stopped speaking (timeout), updating speaking state`,
            );
            clientConnection.isSpeaking = false;

            // Update user data in WebSocket
            const userSocket = this.voiceWsManager.getSocketByUserIdAndTopic(
              userId,
              channelKey,
            );
            if (userSocket?.data.user) {
              userSocket.data.user.speaking = false;
            }

            // Broadcast speaking state
            this.broadcastToChannel(channelKey, {
              type: SFUEventTypes.SFU_SPEAKING_STATE,
              data: {
                userId,
                isSpeaking: false,
                timestamp: Date.now(),
              },
            });
            console.log(
              `VOICE-DEBUG: Broadcast speaking state (false) for user ${userId}`,
            );
          }
        }, 500); // 500ms of silence before marking as not speaking
      }
    } catch (error) {
      console.error("VOICE-DEBUG: Error handling voice data:", error);
    }
  }

  /**
   * Broadcast a message to all clients in a channel
   *
   * @param channelKey Channel key (serverId:channelId)
   * @param message Message to broadcast
   * @param excludeUserId User ID to exclude from the broadcast (optional)
   */
  private broadcastToChannel(
    channelKey: string,
    message: any,
    excludeUserId?: string,
  ) {
    console.log(
      `VOICE-DEBUG: Broadcasting message to channel ${channelKey}, type: ${message.type}`,
    );
    const channelSockets = this.voiceWsManager.getSocketsInChannel(channelKey);

    if (!channelSockets) {
      console.warn(`VOICE-DEBUG: No sockets found for channel ${channelKey}`);
      return;
    }
    console.log(
      `VOICE-DEBUG: Found ${channelSockets.size} sockets in channel ${channelKey}`,
    );

    const messageStr = JSON.stringify(message);

    let broadcastCount = 0;
    channelSockets.forEach((socket) => {
      if (
        socket.readyState === WebSocket.OPEN &&
        (!excludeUserId || socket.data.userId !== excludeUserId)
      ) {
        console.log(
          `VOICE-DEBUG: Sending message to user ${socket.data.userId}`,
        );
        socket.send(messageStr);
        broadcastCount++;
      }
    });
    console.log(`VOICE-DEBUG: Broadcast message to ${broadcastCount} users`);
  }
}
