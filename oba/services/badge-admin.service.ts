import type { drizzle } from "drizzle-orm/postgres-js";
import { 
  InsufficientPermissionsError,
  BadgeValidationError
} from "../class/badge-errors";
import { hasServerPermission } from "../utils/permissions";
import { ADMINISTRATOR } from "../constants/permissions";
import {
  getBadgeAuditLog,
  getAdminBadgeStats,
  getBadgeSystemHealthCheck,
  cleanupOrphanedBadgeData,
  getUserBadgeSummary
} from "../db/utils/badge-admin-utils";
import type {
  BadgeAuditLogEntry,
  BadgeAuditLogFilters,
  BadgeAdminStats,
  BadgeSystemHealthCheck,
  BadgeCleanupResult,
  UserBadgeSummary
} from "../types/badge.types";

/**
 * Badge Admin Service - Administrative functions for badge management
 * Handles audit logging, statistics, health checks, and administrative operations
 */
export class BadgeAdminService {
  constructor(private db: ReturnType<typeof drizzle>) {}

  /**
   * Gets badge assignment audit log with filtering
   */
  async getBadgeAuditLog(
    adminUserId: string,
    filters: BadgeAuditLogFilters
  ): Promise<{
    entries: BadgeAuditLogEntry[];
    total: number;
    hasMore: boolean;
  }> {
    try {
      // Validate admin permission
      await this.validateAdminPermission(adminUserId);

      return await getBadgeAuditLog(this.db, filters);
    } catch (error) {
      if (error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error getting badge audit log:", error);
      throw new BadgeValidationError("Failed to get badge audit log");
    }
  }

  /**
   * Gets comprehensive badge statistics for administrators
   */
  async getAdminBadgeStats(adminUserId: string): Promise<BadgeAdminStats> {
    try {
      // Validate admin permission
      await this.validateAdminPermission(adminUserId);

      return await getAdminBadgeStats(this.db);
    } catch (error) {
      if (error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error getting admin badge stats:", error);
      throw new BadgeValidationError("Failed to get admin badge statistics");
    }
  }

  /**
   * Performs badge system health check
   */
  async getBadgeSystemHealthCheck(adminUserId: string): Promise<BadgeSystemHealthCheck> {
    try {
      // Validate admin permission
      await this.validateAdminPermission(adminUserId);

      return await getBadgeSystemHealthCheck(this.db);
    } catch (error) {
      if (error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error performing badge health check:", error);
      throw new BadgeValidationError("Failed to perform badge system health check");
    }
  }

  /**
   * Cleans up orphaned badge data
   */
  async cleanupOrphanedBadgeData(adminUserId: string): Promise<BadgeCleanupResult> {
    try {
      // Validate admin permission
      await this.validateAdminPermission(adminUserId);

      return await cleanupOrphanedBadgeData(this.db);
    } catch (error) {
      if (error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error cleaning up badge data:", error);
      throw new BadgeValidationError("Failed to cleanup badge data");
    }
  }

  /**
   * Gets comprehensive user badge summary for administrators
   */
  async getUserBadgeSummary(
    adminUserId: string,
    targetUserId: string
  ): Promise<UserBadgeSummary> {
    try {
      // Validate admin permission
      await this.validateAdminPermission(adminUserId);

      return await getUserBadgeSummary(this.db, targetUserId);
    } catch (error) {
      if (error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error getting user badge summary:", error);
      throw new BadgeValidationError("Failed to get user badge summary");
    }
  }

  /**
   * Validates admin permission for badge management operations
   */
  private async validateAdminPermission(userId: string): Promise<void> {
    try {
      // For now, we'll implement a simple check
      // In a real implementation, you would check user roles/permissions
      // This is a placeholder that should be replaced with actual permission logic
      
      // TODO: Implement proper admin permission checking
      // For now, we'll assume all operations are allowed for development
      // In production, you would:
      // 1. Check if user has global admin role
      // 2. Check if user has specific badge management permissions
      // 3. Validate against server-specific permissions if applicable
      
      // Placeholder implementation - replace with actual permission checking
      // const isGlobalAdmin = await checkUserGlobalAdminStatus(this.db, userId);
      // if (!isGlobalAdmin) {
      //   throw new InsufficientPermissionsError();
      // }
      
      // For development, allow all operations
      return;
    } catch (error) {
      console.error("Error validating admin permission:", error);
      throw new InsufficientPermissionsError();
    }
  }
}