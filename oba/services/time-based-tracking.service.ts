import type { drizzle } from "drizzle-orm/postgres-js";
import { eq, and, gte, lte, between, count, sql, desc, asc } from "drizzle-orm";
import { 
  UserSchema, 
  MessageSchema, 
  ServerMembershipSchema,
  FriendshipSchema,
  DirectMessageSchema,
  UserBadgeSchema
} from "../db/schema";

/**
 * Time-based tracking service for engagement metrics over periods
 * Tracks user activity patterns, engagement trends, and time-sensitive achievements
 */
export class TimeBasedTrackingService {
  constructor(private db: ReturnType<typeof drizzle>) {}

  /**
   * Tracks user engagement metrics over a specified time period
   */
  async trackEngagementMetrics(
    userId: string, 
    timeWindow: TimeWindow
  ): Promise<EngagementMetrics> {
    const { startDate, endDate } = this.parseTimeWindow(timeWindow);
    
    try {
      const [
        messageMetrics,
        socialMetrics,
        activityPatterns,
        consistencyMetrics
      ] = await Promise.all([
        this.getMessageMetrics(userId, startDate, endDate),
        this.getSocialMetrics(userId, startDate, endDate),
        this.getActivityPatterns(userId, startDate, endDate),
        this.getConsistencyMetrics(userId, startDate, endDate)
      ]);

      return {
        userId,
        timeWindow,
        period: { startDate, endDate },
        messageMetrics,
        socialMetrics,
        activityPatterns,
        consistencyMetrics,
        overallScore: this.calculateOverallEngagementScore({
          messageMetrics,
          socialMetrics,
          activityPatterns,
          consistencyMetrics
        })
      };
    } catch (error) {
      console.error("Error tracking engagement metrics:", error);
      throw new Error(`Failed to track engagement metrics: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Tracks engagement trends over multiple time periods
   */
  async trackEngagementTrends(
    userId: string,
    periods: TimeWindow[]
  ): Promise<EngagementTrend[]> {
    try {
      const trends: EngagementTrend[] = [];
      
      for (const period of periods) {
        const metrics = await this.trackEngagementMetrics(userId, period);
        trends.push({
          period: period.label,
          timeWindow: period,
          metrics,
          trend: trends.length > 0 
            ? this.calculateTrendDirection(metrics.overallScore, trends[trends.length - 1].metrics.overallScore)
            : 'stable'
        });
      }

      return trends;
    } catch (error) {
      console.error("Error tracking engagement trends:", error);
      throw new Error(`Failed to track engagement trends: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Gets activity streaks for a user (consecutive days of activity)
   */
  async getActivityStreaks(userId: string, maxDaysBack: number = 365): Promise<ActivityStreaks> {
    try {
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - (maxDaysBack * 24 * 60 * 60 * 1000));

      // Get daily activity data
      const dailyActivity = await this.db
        .select({
          date: sql<string>`DATE(${MessageSchema.createdAt})`,
          messageCount: count(MessageSchema.id)
        })
        .from(MessageSchema)
        .where(
          and(
            eq(MessageSchema.userId, userId),
            between(MessageSchema.createdAt, startDate, endDate)
          )
        )
        .groupBy(sql`DATE(${MessageSchema.createdAt})`)
        .orderBy(desc(sql`DATE(${MessageSchema.createdAt})`));

      const streaks = this.calculateStreaks(dailyActivity);
      
      return {
        userId,
        currentStreak: streaks.currentStreak,
        longestStreak: streaks.longestStreak,
        totalActiveDays: dailyActivity.length,
        streakHistory: streaks.streakHistory,
        lastActiveDate: dailyActivity.length > 0 ? new Date(dailyActivity[0].date) : null
      };
    } catch (error) {
      console.error("Error getting activity streaks:", error);
      throw new Error(`Failed to get activity streaks: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Tracks peak activity hours and patterns
   */
  async trackActivityPatterns(
    userId: string, 
    timeWindow: TimeWindow
  ): Promise<ActivityPatternAnalysis> {
    const { startDate, endDate } = this.parseTimeWindow(timeWindow);
    
    try {
      // Get hourly activity distribution
      const hourlyActivity = await this.db
        .select({
          hour: sql<number>`EXTRACT(HOUR FROM ${MessageSchema.createdAt})`,
          count: count(),
          avgLength: sql<number>`AVG(LENGTH(${MessageSchema.content}))`
        })
        .from(MessageSchema)
        .where(
          and(
            eq(MessageSchema.userId, userId),
            between(MessageSchema.createdAt, startDate, endDate)
          )
        )
        .groupBy(sql`EXTRACT(HOUR FROM ${MessageSchema.createdAt})`)
        .orderBy(asc(sql`EXTRACT(HOUR FROM ${MessageSchema.createdAt})`));

      // Get daily activity distribution
      const dailyActivity = await this.db
        .select({
          dayOfWeek: sql<number>`EXTRACT(DOW FROM ${MessageSchema.createdAt})`,
          count: count(),
          avgLength: sql<number>`AVG(LENGTH(${MessageSchema.content}))`
        })
        .from(MessageSchema)
        .where(
          and(
            eq(MessageSchema.userId, userId),
            between(MessageSchema.createdAt, startDate, endDate)
          )
        )
        .groupBy(sql`EXTRACT(DOW FROM ${MessageSchema.createdAt})`)
        .orderBy(asc(sql`EXTRACT(DOW FROM ${MessageSchema.createdAt})`));

      const peakHours = hourlyActivity
        .sort((a, b) => Number(b.count) - Number(a.count))
        .slice(0, 3)
        .map(h => Number(h.hour));

      const peakDays = dailyActivity
        .sort((a, b) => Number(b.count) - Number(a.count))
        .slice(0, 2)
        .map(d => Number(d.dayOfWeek));

      return {
        userId,
        timeWindow,
        hourlyDistribution: hourlyActivity.map(h => ({
          hour: Number(h.hour),
          messageCount: Number(h.count),
          averageLength: Number(h.avgLength || 0)
        })),
        dailyDistribution: dailyActivity.map(d => ({
          dayOfWeek: Number(d.dayOfWeek),
          messageCount: Number(d.count),
          averageLength: Number(d.avgLength || 0)
        })),
        peakActivityHours: peakHours,
        peakActivityDays: peakDays,
        activityPattern: this.classifyActivityPattern(hourlyActivity, dailyActivity)
      };
    } catch (error) {
      console.error("Error tracking activity patterns:", error);
      throw new Error(`Failed to track activity patterns: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Tracks seasonal engagement patterns
   */
  async trackSeasonalPatterns(
    userId: string,
    yearRange: { startYear: number; endYear: number }
  ): Promise<SeasonalPatterns> {
    try {
      const patterns: SeasonalPatterns = {
        userId,
        yearRange,
        seasonalData: [],
        monthlyData: [],
        trends: {
          mostActiveSeasons: [],
          leastActiveSeasons: [],
          growthTrend: 'stable'
        }
      };

      // Get monthly activity data
      for (let year = yearRange.startYear; year <= yearRange.endYear; year++) {
        for (let month = 1; month <= 12; month++) {
          const startDate = new Date(year, month - 1, 1);
          const endDate = new Date(year, month, 0);

          const monthlyActivity = await this.db
            .select({
              messageCount: count(MessageSchema.id),
              avgLength: sql<number>`AVG(LENGTH(${MessageSchema.content}))`
            })
            .from(MessageSchema)
            .where(
              and(
                eq(MessageSchema.userId, userId),
                between(MessageSchema.createdAt, startDate, endDate)
              )
            );

          const messageCount = Number(monthlyActivity[0]?.messageCount || 0);
          const avgLength = Number(monthlyActivity[0]?.avgLength || 0);

          patterns.monthlyData.push({
            year,
            month,
            messageCount,
            averageLength: avgLength,
            season: this.getSeason(month)
          });
        }
      }

      // Aggregate seasonal data
      const seasonalAggregation = patterns.monthlyData.reduce((acc, data) => {
        if (!acc[data.season]) {
          acc[data.season] = { messageCount: 0, averageLength: 0, months: 0 };
        }
        acc[data.season].messageCount += data.messageCount;
        acc[data.season].averageLength += data.averageLength;
        acc[data.season].months += 1;
        return acc;
      }, {} as Record<string, { messageCount: number; averageLength: number; months: number }>);

      patterns.seasonalData = Object.entries(seasonalAggregation).map(([season, data]) => ({
        season,
        totalMessages: data.messageCount,
        averageLength: data.averageLength / data.months,
        monthsTracked: data.months
      }));

      // Calculate trends
      patterns.trends = this.calculateSeasonalTrends(patterns.seasonalData, patterns.monthlyData);

      return patterns;
    } catch (error) {
      console.error("Error tracking seasonal patterns:", error);
      throw new Error(`Failed to track seasonal patterns: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Tracks milestone achievements over time
   */
  async trackMilestoneProgress(
    userId: string,
    milestones: MilestoneDefinition[]
  ): Promise<MilestoneProgress[]> {
    try {
      const progress: MilestoneProgress[] = [];

      for (const milestone of milestones) {
        const milestoneProgress = await this.calculateMilestoneProgress(userId, milestone);
        progress.push(milestoneProgress);
      }

      return progress;
    } catch (error) {
      console.error("Error tracking milestone progress:", error);
      throw new Error(`Failed to track milestone progress: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Gets message metrics for a time period
   */
  private async getMessageMetrics(
    userId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<MessageMetrics> {
    const messageStats = await this.db
      .select({
        totalMessages: count(),
        avgLength: sql<number>`AVG(LENGTH(${MessageSchema.content}))`,
        maxLength: sql<number>`MAX(LENGTH(${MessageSchema.content}))`,
        minLength: sql<number>`MIN(LENGTH(${MessageSchema.content}))`
      })
      .from(MessageSchema)
      .where(
        and(
          eq(MessageSchema.userId, userId),
          between(MessageSchema.createdAt, startDate, endDate)
        )
      );

    const stats = messageStats[0];
    const totalMessages = Number(stats?.totalMessages || 0);
    const avgLength = Number(stats?.avgLength || 0);

    // Calculate messages per day
    const daysDiff = Math.max(1, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)));
    const messagesPerDay = totalMessages / daysDiff;

    return {
      totalMessages,
      averageLength: avgLength,
      maxLength: Number(stats?.maxLength || 0),
      minLength: Number(stats?.minLength || 0),
      messagesPerDay
    };
  }

  /**
   * Gets social metrics for a time period
   */
  private async getSocialMetrics(
    userId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<SocialMetrics> {
    // Get new friendships in period
    const newFriendships = await this.db
      .select({ count: count() })
      .from(FriendshipSchema)
      .where(
        and(
          eq(FriendshipSchema.userId, userId),
          eq(FriendshipSchema.status, "ACCEPTED"),
          between(FriendshipSchema.createdAt, startDate, endDate)
        )
      );

    // Get direct messages sent in period
    const directMessages = await this.db
      .select({ count: count() })
      .from(DirectMessageSchema)
      .where(
        and(
          eq(DirectMessageSchema.senderId, userId),
          between(DirectMessageSchema.createdAt, startDate, endDate)
        )
      );

    return {
      newFriendships: Number(newFriendships[0]?.count || 0),
      directMessagesSent: Number(directMessages[0]?.count || 0),
      socialInteractionScore: this.calculateSocialInteractionScore(
        Number(newFriendships[0]?.count || 0),
        Number(directMessages[0]?.count || 0)
      )
    };
  }

  /**
   * Gets activity patterns for a time period
   */
  private async getActivityPatterns(
    userId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<ActivityPatterns> {
    // Get daily activity
    const dailyActivity = await this.db
      .select({
        date: sql<string>`DATE(${MessageSchema.createdAt})`,
        count: count()
      })
      .from(MessageSchema)
      .where(
        and(
          eq(MessageSchema.userId, userId),
          between(MessageSchema.createdAt, startDate, endDate)
        )
      )
      .groupBy(sql`DATE(${MessageSchema.createdAt})`);

    const activeDays = dailyActivity.length;
    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const activityRate = activeDays / totalDays;

    // Calculate streaks within this period
    const streaks = this.calculateStreaks(dailyActivity.map(d => ({
      date: d.date,
      messageCount: Number(d.count)
    })));

    return {
      activeDays,
      totalDays,
      activityRate,
      longestStreak: streaks.longestStreak,
      averageMessagesPerActiveDay: dailyActivity.length > 0 
        ? dailyActivity.reduce((sum, d) => sum + Number(d.count), 0) / dailyActivity.length
        : 0
    };
  }

  /**
   * Gets consistency metrics for a time period
   */
  private async getConsistencyMetrics(
    userId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<ConsistencyMetrics> {
    // Get weekly activity
    const weeklyActivity = await this.db
      .select({
        week: sql<string>`DATE_TRUNC('week', ${MessageSchema.createdAt})`,
        count: count()
      })
      .from(MessageSchema)
      .where(
        and(
          eq(MessageSchema.userId, userId),
          between(MessageSchema.createdAt, startDate, endDate)
        )
      )
      .groupBy(sql`DATE_TRUNC('week', ${MessageSchema.createdAt})`);

    const weeklyMessageCounts = weeklyActivity.map(w => Number(w.count));
    
    // Calculate consistency score (lower standard deviation = higher consistency)
    const mean = weeklyMessageCounts.reduce((sum, count) => sum + count, 0) / weeklyMessageCounts.length;
    const variance = weeklyMessageCounts.reduce((sum, count) => sum + Math.pow(count - mean, 2), 0) / weeklyMessageCounts.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Normalize consistency score (0-1, where 1 is most consistent)
    const consistencyScore = mean > 0 ? Math.max(0, 1 - (standardDeviation / mean)) : 0;

    return {
      weeklyActivity: weeklyActivity.map(w => ({
        week: w.week,
        messageCount: Number(w.count)
      })),
      consistencyScore,
      averageWeeklyMessages: mean,
      standardDeviation
    };
  }

  /**
   * Calculates overall engagement score from metrics
   */
  private calculateOverallEngagementScore(metrics: {
    messageMetrics: MessageMetrics;
    socialMetrics: SocialMetrics;
    activityPatterns: ActivityPatterns;
    consistencyMetrics: ConsistencyMetrics;
  }): number {
    const {
      messageMetrics,
      socialMetrics,
      activityPatterns,
      consistencyMetrics
    } = metrics;

    // Weighted scoring system
    const messageScore = Math.min(messageMetrics.messagesPerDay / 10, 1) * 0.3; // Max 10 messages/day
    const socialScore = Math.min(socialMetrics.socialInteractionScore / 100, 1) * 0.2; // Max 100 social score
    const activityScore = activityPatterns.activityRate * 0.3; // Activity rate is already 0-1
    const consistencyScore = consistencyMetrics.consistencyScore * 0.2; // Consistency is already 0-1

    return Math.min(messageScore + socialScore + activityScore + consistencyScore, 1);
  }

  /**
   * Calculates trend direction between two scores
   */
  private calculateTrendDirection(currentScore: number, previousScore: number): 'increasing' | 'decreasing' | 'stable' {
    const threshold = 0.05; // 5% threshold for stability
    const diff = currentScore - previousScore;
    
    if (Math.abs(diff) < threshold) return 'stable';
    return diff > 0 ? 'increasing' : 'decreasing';
  }

  /**
   * Calculates activity streaks from daily activity data
   */
  private calculateStreaks(dailyActivity: Array<{ date: string; messageCount: number }>): {
    currentStreak: number;
    longestStreak: number;
    streakHistory: Array<{ startDate: string; endDate: string; length: number }>;
  } {
    if (dailyActivity.length === 0) {
      return { currentStreak: 0, longestStreak: 0, streakHistory: [] };
    }

    // Sort by date descending
    const sortedActivity = dailyActivity.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    
    let currentStreak = 0;
    let longestStreak = 0;
    let tempStreak = 0;
    const streakHistory: Array<{ startDate: string; endDate: string; length: number }> = [];
    
    // Calculate current streak (from most recent date)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    for (let i = 0; i < sortedActivity.length; i++) {
      const activityDate = new Date(sortedActivity[i].date);
      const expectedDate = new Date(today.getTime() - (i * 24 * 60 * 60 * 1000));
      
      if (activityDate.getTime() === expectedDate.getTime()) {
        currentStreak++;
      } else {
        break;
      }
    }

    // Calculate all streaks
    let streakStart = 0;
    for (let i = 0; i < sortedActivity.length; i++) {
      if (i === 0) {
        tempStreak = 1;
        streakStart = i;
      } else {
        const currentDate = new Date(sortedActivity[i].date);
        const previousDate = new Date(sortedActivity[i - 1].date);
        const dayDiff = (previousDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24);
        
        if (dayDiff === 1) {
          tempStreak++;
        } else {
          // End of streak
          if (tempStreak > 1) {
            streakHistory.push({
              startDate: sortedActivity[i - 1].date,
              endDate: sortedActivity[streakStart].date,
              length: tempStreak
            });
          }
          longestStreak = Math.max(longestStreak, tempStreak);
          tempStreak = 1;
          streakStart = i;
        }
      }
    }
    
    // Handle final streak
    if (tempStreak > 1) {
      streakHistory.push({
        startDate: sortedActivity[sortedActivity.length - 1].date,
        endDate: sortedActivity[streakStart].date,
        length: tempStreak
      });
    }
    longestStreak = Math.max(longestStreak, tempStreak);

    return { currentStreak, longestStreak, streakHistory };
  }

  /**
   * Classifies activity pattern based on hourly and daily distributions
   */
  private classifyActivityPattern(
    hourlyActivity: Array<{ hour: number; count: bigint; avgLength: number }>,
    dailyActivity: Array<{ dayOfWeek: number; count: bigint; avgLength: number }>
  ): string {
    // Simplified pattern classification
    const totalHourlyMessages = hourlyActivity.reduce((sum, h) => sum + Number(h.count), 0);
    const totalDailyMessages = dailyActivity.reduce((sum, d) => sum + Number(d.count), 0);
    
    if (totalHourlyMessages === 0) return 'inactive';
    
    // Find peak hours
    const peakHour = hourlyActivity.reduce((max, h) => 
      Number(h.count) > Number(max.count) ? h : max
    );
    
    const hour = Number(peakHour.hour);
    
    if (hour >= 9 && hour <= 17) return 'business_hours';
    if (hour >= 18 && hour <= 22) return 'evening';
    if (hour >= 23 || hour <= 5) return 'night_owl';
    return 'morning';
  }

  /**
   * Gets season from month number
   */
  private getSeason(month: number): string {
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'fall';
    return 'winter';
  }

  /**
   * Calculates seasonal trends
   */
  private calculateSeasonalTrends(
    seasonalData: Array<{ season: string; totalMessages: number; averageLength: number; monthsTracked: number }>,
    monthlyData: Array<{ year: number; month: number; messageCount: number; averageLength: number; season: string }>
  ): {
    mostActiveSeasons: string[];
    leastActiveSeasons: string[];
    growthTrend: 'increasing' | 'decreasing' | 'stable';
  } {
    // Sort seasons by activity
    const sortedSeasons = seasonalData.sort((a, b) => b.totalMessages - a.totalMessages);
    
    const mostActiveSeasons = sortedSeasons.slice(0, 2).map(s => s.season);
    const leastActiveSeasons = sortedSeasons.slice(-2).map(s => s.season);
    
    // Calculate growth trend (simplified)
    const recentMonths = monthlyData.slice(-6); // Last 6 months
    const earlierMonths = monthlyData.slice(-12, -6); // 6 months before that
    
    const recentAvg = recentMonths.reduce((sum, m) => sum + m.messageCount, 0) / recentMonths.length;
    const earlierAvg = earlierMonths.reduce((sum, m) => sum + m.messageCount, 0) / earlierMonths.length;
    
    let growthTrend: 'increasing' | 'decreasing' | 'stable' = 'stable';
    if (recentAvg > earlierAvg * 1.1) growthTrend = 'increasing';
    else if (recentAvg < earlierAvg * 0.9) growthTrend = 'decreasing';
    
    return { mostActiveSeasons, leastActiveSeasons, growthTrend };
  }

  /**
   * Calculates milestone progress
   */
  private async calculateMilestoneProgress(
    userId: string,
    milestone: MilestoneDefinition
  ): Promise<MilestoneProgress> {
    let currentValue = 0;
    let isAchieved = false;
    let achievedDate: Date | null = null;

    // Get current value based on milestone type
    switch (milestone.type) {
      case 'total_messages':
        const messageCount = await this.db
          .select({ count: count() })
          .from(MessageSchema)
          .where(eq(MessageSchema.userId, userId));
        currentValue = Number(messageCount[0]?.count || 0);
        break;
      
      case 'consecutive_days':
        const streaks = await this.getActivityStreaks(userId);
        currentValue = streaks.currentStreak;
        break;
      
      case 'friends_count':
        const friendCount = await this.db
          .select({ count: count() })
          .from(FriendshipSchema)
          .where(
            and(
              eq(FriendshipSchema.userId, userId),
              eq(FriendshipSchema.status, "ACCEPTED")
            )
          );
        currentValue = Number(friendCount[0]?.count || 0);
        break;
    }

    isAchieved = currentValue >= milestone.target;
    
    // If achieved, try to find when it was achieved (simplified)
    if (isAchieved) {
      achievedDate = new Date(); // In a real implementation, you'd track this more precisely
    }

    return {
      milestoneId: milestone.id,
      milestone,
      currentValue,
      targetValue: milestone.target,
      progress: Math.min(currentValue / milestone.target, 1),
      isAchieved,
      achievedDate,
      estimatedCompletionDate: this.estimateCompletionDate(currentValue, milestone.target, milestone.type)
    };
  }

  /**
   * Estimates completion date for a milestone
   */
  private estimateCompletionDate(
    currentValue: number,
    targetValue: number,
    milestoneType: string
  ): Date | null {
    if (currentValue >= targetValue) return null;
    
    // Simplified estimation - in reality you'd use historical data
    const remaining = targetValue - currentValue;
    let estimatedDays = 0;
    
    switch (milestoneType) {
      case 'total_messages':
        estimatedDays = remaining / 5; // Assume 5 messages per day
        break;
      case 'consecutive_days':
        estimatedDays = remaining; // One day per day
        break;
      case 'friends_count':
        estimatedDays = remaining * 7; // One friend per week
        break;
      default:
        return null;
    }
    
    return new Date(Date.now() + (estimatedDays * 24 * 60 * 60 * 1000));
  }

  /**
   * Calculates social interaction score
   */
  private calculateSocialInteractionScore(newFriendships: number, directMessages: number): number {
    return (newFriendships * 10) + (directMessages * 2);
  }

  /**
   * Parses time window into start and end dates
   */
  private parseTimeWindow(timeWindow: TimeWindow): { startDate: Date; endDate: Date } {
    const endDate = new Date();
    let startDate: Date;

    switch (timeWindow.unit) {
      case 'hours':
        startDate = new Date(endDate.getTime() - (timeWindow.value * 60 * 60 * 1000));
        break;
      case 'days':
        startDate = new Date(endDate.getTime() - (timeWindow.value * 24 * 60 * 60 * 1000));
        break;
      case 'weeks':
        startDate = new Date(endDate.getTime() - (timeWindow.value * 7 * 24 * 60 * 60 * 1000));
        break;
      case 'months':
        startDate = new Date(endDate.getTime() - (timeWindow.value * 30 * 24 * 60 * 60 * 1000));
        break;
      case 'years':
        startDate = new Date(endDate.getTime() - (timeWindow.value * 365 * 24 * 60 * 60 * 1000));
        break;
      default:
        throw new Error(`Unsupported time unit: ${timeWindow.unit}`);
    }

    return { startDate, endDate };
  }
}

// Type definitions for time-based tracking

export interface TimeWindow {
  value: number;
  unit: 'hours' | 'days' | 'weeks' | 'months' | 'years';
  label: string;
}

export interface EngagementMetrics {
  userId: string;
  timeWindow: TimeWindow;
  period: { startDate: Date; endDate: Date };
  messageMetrics: MessageMetrics;
  socialMetrics: SocialMetrics;
  activityPatterns: ActivityPatterns;
  consistencyMetrics: ConsistencyMetrics;
  overallScore: number; // 0-1
}

export interface MessageMetrics {
  totalMessages: number;
  averageLength: number;
  maxLength: number;
  minLength: number;
  messagesPerDay: number;
}

export interface SocialMetrics {
  newFriendships: number;
  directMessagesSent: number;
  socialInteractionScore: number;
}

export interface ActivityPatterns {
  activeDays: number;
  totalDays: number;
  activityRate: number; // 0-1
  longestStreak: number;
  averageMessagesPerActiveDay: number;
}

export interface ConsistencyMetrics {
  weeklyActivity: Array<{ week: string; messageCount: number }>;
  consistencyScore: number; // 0-1
  averageWeeklyMessages: number;
  standardDeviation: number;
}

export interface EngagementTrend {
  period: string;
  timeWindow: TimeWindow;
  metrics: EngagementMetrics;
  trend: 'increasing' | 'decreasing' | 'stable';
}

export interface ActivityStreaks {
  userId: string;
  currentStreak: number;
  longestStreak: number;
  totalActiveDays: number;
  streakHistory: Array<{ startDate: string; endDate: string; length: number }>;
  lastActiveDate: Date | null;
}

export interface ActivityPatternAnalysis {
  userId: string;
  timeWindow: TimeWindow;
  hourlyDistribution: Array<{ hour: number; messageCount: number; averageLength: number }>;
  dailyDistribution: Array<{ dayOfWeek: number; messageCount: number; averageLength: number }>;
  peakActivityHours: number[];
  peakActivityDays: number[];
  activityPattern: string; // 'business_hours', 'evening', 'night_owl', 'morning', 'inactive'
}

export interface SeasonalPatterns {
  userId: string;
  yearRange: { startYear: number; endYear: number };
  seasonalData: Array<{
    season: string;
    totalMessages: number;
    averageLength: number;
    monthsTracked: number;
  }>;
  monthlyData: Array<{
    year: number;
    month: number;
    messageCount: number;
    averageLength: number;
    season: string;
  }>;
  trends: {
    mostActiveSeasons: string[];
    leastActiveSeasons: string[];
    growthTrend: 'increasing' | 'decreasing' | 'stable';
  };
}

export interface MilestoneDefinition {
  id: string;
  name: string;
  description: string;
  type: 'total_messages' | 'consecutive_days' | 'friends_count' | 'servers_joined' | 'custom';
  target: number;
  category: string;
}

export interface MilestoneProgress {
  milestoneId: string;
  milestone: MilestoneDefinition;
  currentValue: number;
  targetValue: number;
  progress: number; // 0-1
  isAchieved: boolean;
  achievedDate: Date | null;
  estimatedCompletionDate: Date | null;
}