import type { drizzle } from "drizzle-orm/postgres-js";
import { eq, and, count, sql } from "drizzle-orm";
import { 
  BadgeTypeSchema, 
  UserSchema, 
  UserBadgeSchema,
  ServerMembershipSchema,
  MessageSchema,
  FriendshipSchema
} from "../db/schema";
import {
  getUserStats,
  assignBadgeToUser,
  getUserBadges,
  getBadgeTypes,
} from "../db/utils/badge-utils";
import {
  evaluateUserForAutomaticBadges,
  batchEvaluateUsers,
  evaluateAllUsersForAutomaticBadges,
  reevaluateBadgeTypeForAllUsers,
  getUsersNearBadgeCompletion,
  evaluateBadgeCriteria
} from "../db/utils/badge-evaluation";
import { badgeWebSocketService } from "../utils/badge-websocket";
import type {
  BadgeCriteria,
  UserStats,
  EvaluationResult,
  BadgeType,
  UserBadge,
  BadgeProgress,
} from "../types/badge.types";

/**
 * Badge Evaluation Service - Handles automatic badge assignment and progress tracking
 * This service is responsible for evaluating users against badge criteria and managing
 * automatic badge assignments based on user activity and achievements.
 */
export class BadgeEvaluationService {
  constructor(private db: ReturnType<typeof drizzle>) {}

  // Core Evaluation Methods

  /**
   * Evaluates a single user for all automatic badges
   * Returns information about newly assigned badges and any errors
   */
  async evaluateUser(userId: string): Promise<EvaluationResult> {
    try {
      const result = await evaluateUserForAutomaticBadges(this.db, userId);
      
      // Broadcast WebSocket events for newly assigned badges
      if (result.newBadges.length > 0) {
        const broadcastPayloads = result.newBadges.map(badge => ({
          userId,
          badge,
          isAutomatic: true,
          assignedBy: undefined
        }));
        
        await badgeWebSocketService.broadcastBatchBadgeAssigned(broadcastPayloads);
      }
      
      return result;
    } catch (error) {
      console.error("Error in BadgeEvaluationService.evaluateUser:", error);
      return {
        userId,
        newBadges: [],
        evaluatedBadges: [],
        errors: [`Evaluation failed: ${error instanceof Error ? error.message : String(error)}`]
      };
    }
  }

  /**
   * Evaluates multiple users in batch for automatic badges
   * Processes users in smaller batches to avoid overwhelming the database
   */
  async evaluateUsers(userIds: string[]): Promise<EvaluationResult[]> {
    try {
      const results = await batchEvaluateUsers(this.db, userIds);
      
      // Collect all new badge assignments for broadcasting
      const allNewBadges: Array<{
        userId: string;
        badge: UserBadge;
        isAutomatic: boolean;
        assignedBy?: string;
      }> = [];
      
      results.forEach(result => {
        result.newBadges.forEach(badge => {
          allNewBadges.push({
            userId: result.userId,
            badge,
            isAutomatic: true,
            assignedBy: undefined
          });
        });
      });
      
      // Broadcast all new badge assignments
      if (allNewBadges.length > 0) {
        await badgeWebSocketService.broadcastBatchBadgeAssigned(allNewBadges);
      }
      
      return results;
    } catch (error) {
      console.error("Error in BadgeEvaluationService.evaluateUsers:", error);
      throw new Error(`Failed to batch evaluate users: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Evaluates all users in the system for automatic badges
   * Use with caution on large user bases - consider running as a background job
   */
  async evaluateAllUsers(batchSize: number = 50): Promise<{
    totalUsers: number;
    processedUsers: number;
    totalNewBadges: number;
    errors: string[];
  }> {
    try {
      return await evaluateAllUsersForAutomaticBadges(this.db, batchSize);
    } catch (error) {
      console.error("Error in BadgeEvaluationService.evaluateAllUsers:", error);
      throw new Error(`Failed to evaluate all users: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Re-evaluates a specific badge type for all users
   * Useful when badge criteria are updated
   */
  async reevaluateBadgeType(badgeTypeId: string): Promise<{
    evaluatedUsers: number;
    newAssignments: number;
    errors: string[];
  }> {
    try {
      return await reevaluateBadgeTypeForAllUsers(this.db, badgeTypeId);
    } catch (error) {
      console.error("Error in BadgeEvaluationService.reevaluateBadgeType:", error);
      throw new Error(`Failed to re-evaluate badge type: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // Criteria Evaluation Methods

  /**
   * Checks if a user meets the criteria for a specific badge
   */
  async checkCriteria(userId: string, criteria: BadgeCriteria): Promise<boolean> {
    try {
      const userStats = await this.getUserStats(userId);
      return await evaluateBadgeCriteria(userStats, criteria);
    } catch (error) {
      console.error("Error checking badge criteria:", error);
      return false;
    }
  }

  /**
   * Evaluates criteria for multiple users at once
   * Returns a map of userId to boolean indicating if they meet the criteria
   */
  async checkCriteriaForUsers(userIds: string[], criteria: BadgeCriteria): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();
    
    try {
      // Process users in batches to avoid overwhelming the database
      const batchSize = 20;
      for (let i = 0; i < userIds.length; i += batchSize) {
        const batch = userIds.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async (userId) => {
          try {
            const meetsCriteria = await this.checkCriteria(userId, criteria);
            return { userId, meetsCriteria };
          } catch (error) {
            console.warn(`Failed to check criteria for user ${userId}:`, error);
            return { userId, meetsCriteria: false };
          }
        });
        
        const batchResults = await Promise.all(batchPromises);
        batchResults.forEach(({ userId, meetsCriteria }) => {
          results.set(userId, meetsCriteria);
        });
      }
    } catch (error) {
      console.error("Error checking criteria for multiple users:", error);
    }
    
    return results;
  }

  // User Statistics Methods

  /**
   * Gets comprehensive user statistics for badge evaluation
   */
  async getUserStats(userId: string): Promise<UserStats> {
    try {
      return await getUserStats(this.db, userId);
    } catch (error) {
      console.error("Error getting user stats:", error);
      throw new Error(`Failed to get user statistics: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Gets user statistics for multiple users at once
   * Returns a map of userId to UserStats
   */
  async getUserStatsForUsers(userIds: string[]): Promise<Map<string, UserStats>> {
    const results = new Map<string, UserStats>();
    
    try {
      // Process users in batches
      const batchSize = 20;
      for (let i = 0; i < userIds.length; i += batchSize) {
        const batch = userIds.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async (userId) => {
          try {
            const stats = await this.getUserStats(userId);
            return { userId, stats };
          } catch (error) {
            console.warn(`Failed to get stats for user ${userId}:`, error);
            return null;
          }
        });
        
        const batchResults = await Promise.all(batchPromises);
        batchResults.forEach((result) => {
          if (result) {
            results.set(result.userId, result.stats);
          }
        });
      }
    } catch (error) {
      console.error("Error getting stats for multiple users:", error);
    }
    
    return results;
  }

  /**
   * Aggregates user statistics from raw database queries
   * This method provides more efficient statistics gathering for large-scale operations
   */
  async aggregateUserStats(userId: string): Promise<UserStats> {
    try {
      // Get user creation date for account age calculation
      const user = await this.db
        .select({
          createdAt: UserSchema.createdAt,
          lastActive: UserSchema.lastActive,
        })
        .from(UserSchema)
        .where(eq(UserSchema.id, userId))
        .limit(1);

      if (user.length === 0) {
        throw new Error("User not found");
      }

      const userData = user[0];
      const now = new Date();
      const accountAge = Math.floor((now.getTime() - userData.createdAt.getTime()) / (1000 * 60 * 60 * 24));
      
      // Calculate days active (days since last active, or account age if never active)
      const lastActiveDate = userData.lastActive || userData.createdAt;
      const daysActive = Math.floor((now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60 * 24));

      // Get message count
      const messageCountResult = await this.db
        .select({ count: count() })
        .from(MessageSchema)
        .where(eq(MessageSchema.userId, userId));
      
      const messageCount = messageCountResult[0]?.count || 0;

      // Get server count (servers where user is a member)
      const serverCountResult = await this.db
        .select({ count: count() })
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.userId, userId));
      
      const serverCount = serverCountResult[0]?.count || 0;

      // Get friend count
      const friendCountResult = await this.db
        .select({ count: count() })
        .from(FriendshipSchema)
        .where(
          and(
            eq(FriendshipSchema.userId, userId),
            eq(FriendshipSchema.status, "accepted")
          )
        );
      
      const friendCount = friendCountResult[0]?.count || 0;

      return {
        messageCount: Number(messageCount),
        serverCount: Number(serverCount),
        friendCount: Number(friendCount),
        daysActive: Math.max(0, daysActive),
        accountAge,
        lastActive: lastActiveDate,
      };
    } catch (error) {
      console.error("Error aggregating user stats:", error);
      throw new Error(`Failed to aggregate user statistics: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // Progress Tracking Methods

  /**
   * Gets badge progress for a user's automatic badges
   * Shows how close they are to earning badges they don't have yet
   */
  async getBadgeProgress(userId: string, badgeTypeId?: string): Promise<BadgeProgress[]> {
    try {
      const progress: BadgeProgress[] = [];
      
      // Get user's current badges
      const userBadges = await getUserBadges(this.db, userId);
      const earnedBadgeIds = new Set(userBadges.map(badge => badge.badgeTypeId));
      
      // Get automatic badge types to check progress for
      const filters = badgeTypeId 
        ? { assignmentType: "automatic" as const, isActive: true }
        : { assignmentType: "automatic" as const, isActive: true };
      
      const automaticBadges = await getBadgeTypes(this.db, filters);
      const badgesToCheck = badgeTypeId 
        ? automaticBadges.filter(badge => badge.id === badgeTypeId)
        : automaticBadges;
      
      // Get user stats once
      const userStats = await this.getUserStats(userId);
      
      for (const badgeType of badgesToCheck) {
        if (!badgeType.criteria || !badgeType.criteria.threshold) {
          continue;
        }
        
        const isEarned = earnedBadgeIds.has(badgeType.id);
        let currentProgress = 0;
        
        // Calculate current progress based on criteria type
        switch (badgeType.criteria.type) {
          case "message_count":
            currentProgress = userStats.messageCount;
            break;
          case "server_count":
            currentProgress = userStats.serverCount;
            break;
          case "friend_count":
            currentProgress = userStats.friendCount;
            break;
          case "days_active":
            currentProgress = userStats.daysActive;
            break;
          case "custom":
            // For custom criteria, we need to evaluate the specific conditions
            currentProgress = await this.calculateCustomProgress(userStats, badgeType.criteria);
            break;
          default:
            continue;
        }
        
        progress.push({
          badgeTypeId: badgeType.id,
          badgeType,
          progress: Math.min(currentProgress, badgeType.criteria.threshold),
          total: badgeType.criteria.threshold,
          isEarned,
        });
      }
      
      return progress;
    } catch (error) {
      console.error("Error getting badge progress:", error);
      throw new Error(`Failed to get badge progress: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Broadcasts progress updates for a user's automatic badges
   * This should be called after user activities that might affect badge progress
   */
  async broadcastProgressUpdates(userId: string, significantProgressOnly: boolean = true): Promise<void> {
    try {
      const progress = await this.getBadgeProgress(userId);
      
      // Filter to only significant progress updates if requested
      const progressToUpdate = significantProgressOnly 
        ? progress.filter(p => {
            const progressPercentage = (p.progress / p.total) * 100;
            // Only broadcast if progress is at 25%, 50%, 75%, or 90%+ milestones
            return progressPercentage >= 25 && 
                   (progressPercentage % 25 === 0 || progressPercentage >= 90);
          })
        : progress;
      
      if (progressToUpdate.length === 0) {
        return;
      }
      
      const progressPayloads = progressToUpdate.map(p => ({
        userId,
        badgeTypeId: p.badgeTypeId,
        badgeType: p.badgeType,
        progress: p.progress,
        total: p.total,
        progressPercentage: (p.progress / p.total) * 100
      }));
      
      await badgeWebSocketService.broadcastBatchProgressUpdates(progressPayloads);
    } catch (error) {
      console.error("Error broadcasting progress updates:", error);
      // Don't throw - this is a nice-to-have feature
    }
  }

  /**
   * Evaluates and broadcasts progress updates for multiple users
   * Useful for batch progress notifications
   */
  async broadcastProgressUpdatesForUsers(userIds: string[]): Promise<void> {
    try {
      // Process users in batches to avoid overwhelming the system
      const batchSize = 10;
      for (let i = 0; i < userIds.length; i += batchSize) {
        const batch = userIds.slice(i, i + batchSize);
        
        await Promise.all(
          batch.map(userId => this.broadcastProgressUpdates(userId, true))
        );
        
        // Small delay between batches
        if (i + batchSize < userIds.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
    } catch (error) {
      console.error("Error broadcasting progress updates for users:", error);
    }
  }

  /**
   * Gets users who are close to earning a specific badge
   */
  async getUsersNearCompletion(
    badgeTypeId: string, 
    thresholdPercentage: number = 0.8
  ): Promise<Array<{
    userId: string;
    username: string;
    progress: number;
    total: number;
    progressPercentage: number;
  }>> {
    try {
      return await getUsersNearBadgeCompletion(this.db, badgeTypeId, thresholdPercentage);
    } catch (error) {
      console.error("Error getting users near badge completion:", error);
      throw new Error(`Failed to get users near completion: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Calculates progress for custom badge criteria
   */
  private async calculateCustomProgress(userStats: UserStats, criteria: BadgeCriteria): Promise<number> {
    if (!criteria.conditions || !criteria.threshold) {
      return 0;
    }
    
    const conditions = criteria.conditions;
    let progress = 0;
    
    // Handle different types of custom criteria
    if (conditions.minAccountAge) {
      progress = Math.min(userStats.accountAge, conditions.minAccountAge);
    }
    
    if (conditions.minActivityRatio) {
      const activityRatio = userStats.messageCount / Math.max(userStats.daysActive, 1);
      progress = Math.min(activityRatio, conditions.minActivityRatio);
    }
    
    if (conditions.combinedRequirements) {
      const reqs = conditions.combinedRequirements;
      let totalRequirements = 0;
      let metRequirements = 0;
      
      if (reqs.messages) {
        totalRequirements++;
        if (userStats.messageCount >= reqs.messages) metRequirements++;
      }
      
      if (reqs.servers) {
        totalRequirements++;
        if (userStats.serverCount >= reqs.servers) metRequirements++;
      }
      
      if (reqs.friends) {
        totalRequirements++;
        if (userStats.friendCount >= reqs.friends) metRequirements++;
      }
      
      progress = totalRequirements > 0 ? (metRequirements / totalRequirements) * criteria.threshold : 0;
    }
    
    return progress;
  }

  // Batch Processing Methods

  /**
   * Processes badge evaluation for users who have been active recently
   * This is useful for scheduled badge evaluation jobs
   */
  async evaluateRecentlyActiveUsers(daysSinceActive: number = 7): Promise<{
    evaluatedUsers: number;
    totalNewBadges: number;
    errors: string[];
  }> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysSinceActive);
      
      // Get recently active users
      const recentlyActiveUsers = await this.db
        .select({ id: UserSchema.id })
        .from(UserSchema)
        .where(sql`${UserSchema.lastActive} >= ${cutoffDate}`)
        .limit(1000); // Limit to prevent overwhelming the system
      
      const userIds = recentlyActiveUsers.map(user => user.id);
      
      if (userIds.length === 0) {
        return {
          evaluatedUsers: 0,
          totalNewBadges: 0,
          errors: []
        };
      }
      
      const results = await this.evaluateUsers(userIds);
      
      return {
        evaluatedUsers: results.length,
        totalNewBadges: results.reduce((total, result) => total + result.newBadges.length, 0),
        errors: results.flatMap(result => result.errors)
      };
    } catch (error) {
      console.error("Error evaluating recently active users:", error);
      throw new Error(`Failed to evaluate recently active users: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Tracks incremental progress for badges with progressive criteria
   * This method should be called after user activities that affect badge progress
   */
  async trackIncrementalProgress(
    userId: string, 
    activityType: 'message' | 'server_join' | 'friend_add' | 'invite_sent' | 'invite_accepted'
  ): Promise<void> {
    try {
      // Get user's current progress for automatic badges
      const progress = await this.getBadgeProgress(userId);
      
      // Filter to badges that are close to completion (80%+ progress)
      const nearCompletionBadges = progress.filter(p => 
        !p.isEarned && (p.progress / p.total) >= 0.8
      );
      
      if (nearCompletionBadges.length === 0) {
        return;
      }
      
      // Re-evaluate user for badges they're close to earning
      const badgeTypeIds = nearCompletionBadges.map(p => p.badgeTypeId);
      
      for (const badgeTypeId of badgeTypeIds) {
        try {
          const result = await this.evaluateUsersForBadgeType([userId], badgeTypeId);
          
          if (result.newAssignments > 0) {
            console.log(`User ${userId} earned badge ${badgeTypeId} through incremental progress tracking`);
          }
        } catch (error) {
          console.warn(`Failed to evaluate incremental progress for badge ${badgeTypeId}:`, error);
        }
      }
      
      // Broadcast progress updates for remaining badges
      await this.broadcastProgressUpdates(userId, true);
      
    } catch (error) {
      console.error("Error tracking incremental progress:", error);
      // Don't throw - this is a background process
    }
  }

  /**
   * Evaluates users based on recent activity patterns
   * More intelligent than simple batch evaluation
   */
  async evaluateUsersWithActivityContext(
    userIds: string[],
    activityContext?: {
      recentMessageCount?: number;
      recentServerJoins?: number;
      recentFriendAdds?: number;
      timeWindow?: number; // hours
    }
  ): Promise<EvaluationResult[]> {
    try {
      // If activity context is provided, prioritize users with relevant activity
      let prioritizedUserIds = userIds;
      
      if (activityContext) {
        // In a real implementation, you'd query recent activity data
        // For now, we'll process all users but could optimize based on activity
        prioritizedUserIds = userIds;
      }
      
      // Process users in smaller batches for better performance
      const batchSize = 15;
      const results: EvaluationResult[] = [];
      
      for (let i = 0; i < prioritizedUserIds.length; i += batchSize) {
        const batch = prioritizedUserIds.slice(i, i + batchSize);
        
        try {
          const batchResults = await this.evaluateUsers(batch);
          results.push(...batchResults);
          
          // Small delay between batches to avoid overwhelming the system
          if (i + batchSize < prioritizedUserIds.length) {
            await new Promise(resolve => setTimeout(resolve, 50));
          }
        } catch (error) {
          console.error(`Error evaluating batch ${i}-${i + batchSize}:`, error);
          // Continue with next batch
        }
      }
      
      return results;
    } catch (error) {
      console.error("Error evaluating users with activity context:", error);
      throw new Error(`Failed to evaluate users with activity context: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Evaluates users for a specific badge type only
   * More efficient than full evaluation when you only care about one badge
   */
  async evaluateUsersForBadgeType(userIds: string[], badgeTypeId: string): Promise<{
    evaluatedUsers: number;
    newAssignments: number;
    errors: string[];
  }> {
    try {
      const summary = {
        evaluatedUsers: 0,
        newAssignments: 0,
        errors: [] as string[]
      };
      
      // Get the badge type
      const badgeTypes = await getBadgeTypes(this.db, { assignmentType: "automatic", isActive: true });
      const badgeType = badgeTypes.find(bt => bt.id === badgeTypeId);
      
      if (!badgeType || !badgeType.criteria) {
        throw new Error("Badge type not found or has no criteria");
      }
      
      // Get users who already have this badge
      const existingBadges = await this.db
        .select({ userId: UserBadgeSchema.userId })
        .from(UserBadgeSchema)
        .where(eq(UserBadgeSchema.badgeTypeId, badgeTypeId));
      
      const usersWithBadge = new Set(existingBadges.map(b => b.userId));
      
      // Filter out users who already have the badge
      const usersToEvaluate = userIds.filter(userId => !usersWithBadge.has(userId));
      
      // Evaluate each user
      for (const userId of usersToEvaluate) {
        try {
          summary.evaluatedUsers++;
          
          const meetsCriteria = await this.checkCriteria(userId, badgeType.criteria);
          
          if (meetsCriteria) {
            try {
              const newBadge = await assignBadgeToUser(this.db, userId, badgeTypeId);
              if (newBadge) {
                summary.newAssignments++;
              }
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : String(error);
              if (!errorMessage.includes("already has this badge")) {
                summary.errors.push(`Failed to assign badge to user ${userId}: ${errorMessage}`);
              }
            }
          }
        } catch (error) {
          summary.errors.push(`Failed to evaluate user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
      
      return summary;
    } catch (error) {
      console.error("Error evaluating users for badge type:", error);
      throw new Error(`Failed to evaluate users for badge type: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Smart batch evaluation that prioritizes users likely to earn badges
   * Uses heuristics to optimize evaluation order
   */
  async smartBatchEvaluation(
    userIds: string[],
    options: {
      prioritizeActiveUsers?: boolean;
      prioritizeNearCompletion?: boolean;
      maxBatchSize?: number;
      delayBetweenBatches?: number;
    } = {}
  ): Promise<{
    totalEvaluated: number;
    totalNewBadges: number;
    evaluationTime: number;
    errors: string[];
  }> {
    const startTime = Date.now();
    const {
      prioritizeActiveUsers = true,
      prioritizeNearCompletion = true,
      maxBatchSize = 20,
      delayBetweenBatches = 100
    } = options;

    try {
      let prioritizedUserIds = [...userIds];
      
      // If prioritization is enabled, reorder users based on likelihood to earn badges
      if (prioritizeActiveUsers || prioritizeNearCompletion) {
        const userPriorities = await Promise.all(
          userIds.map(async (userId) => {
            try {
              const stats = await this.getUserStats(userId);
              let priority = 0;
              
              if (prioritizeActiveUsers) {
                // Higher priority for more active users
                priority += stats.messageCount * 0.1;
                priority += stats.serverCount * 2;
                priority += stats.friendCount * 1;
              }
              
              if (prioritizeNearCompletion) {
                // Check if user is close to earning any badges
                const progress = await this.getBadgeProgress(userId);
                const nearCompletion = progress.filter(p => 
                  !p.isEarned && (p.progress / p.total) >= 0.7
                ).length;
                priority += nearCompletion * 10; // High priority for near completion
              }
              
              return { userId, priority };
            } catch (error) {
              return { userId, priority: 0 };
            }
          })
        );
        
        // Sort by priority (highest first)
        userPriorities.sort((a, b) => b.priority - a.priority);
        prioritizedUserIds = userPriorities.map(up => up.userId);
      }
      
      // Process in batches
      const summary = {
        totalEvaluated: 0,
        totalNewBadges: 0,
        evaluationTime: 0,
        errors: [] as string[]
      };
      
      for (let i = 0; i < prioritizedUserIds.length; i += maxBatchSize) {
        const batch = prioritizedUserIds.slice(i, i + maxBatchSize);
        
        try {
          const batchResults = await this.evaluateUsers(batch);
          
          summary.totalEvaluated += batchResults.length;
          summary.totalNewBadges += batchResults.reduce((total, result) => 
            total + result.newBadges.length, 0
          );
          summary.errors.push(...batchResults.flatMap(result => result.errors));
          
          // Delay between batches if specified
          if (delayBetweenBatches > 0 && i + maxBatchSize < prioritizedUserIds.length) {
            await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
          }
        } catch (error) {
          const errorMessage = `Batch ${i}-${i + maxBatchSize} failed: ${error instanceof Error ? error.message : String(error)}`;
          summary.errors.push(errorMessage);
          console.error(errorMessage);
        }
      }
      
      summary.evaluationTime = Date.now() - startTime;
      return summary;
      
    } catch (error) {
      console.error("Error in smart batch evaluation:", error);
      throw new Error(`Smart batch evaluation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Evaluates badges with time-sensitive criteria
   * Useful for badges that depend on recent activity or time windows
   */
  async evaluateTimeSensitiveBadges(
    userIds: string[],
    timeWindowHours: number = 24
  ): Promise<{
    evaluatedUsers: number;
    newAssignments: number;
    timeSensitiveBadges: string[];
    errors: string[];
  }> {
    try {
      // Get badge types that have time-sensitive criteria
      const allBadgeTypes = await getBadgeTypes(this.db, { 
        assignmentType: "automatic", 
        isActive: true 
      });
      
      const timeSensitiveBadges = allBadgeTypes.filter(badge => 
        badge.criteria?.timeframe || 
        badge.criteria?.conditions?.timeBasedRequirements
      );
      
      if (timeSensitiveBadges.length === 0) {
        return {
          evaluatedUsers: 0,
          newAssignments: 0,
          timeSensitiveBadges: [],
          errors: []
        };
      }
      
      const summary = {
        evaluatedUsers: 0,
        newAssignments: 0,
        timeSensitiveBadges: timeSensitiveBadges.map(b => b.id),
        errors: [] as string[]
      };
      
      // Evaluate each time-sensitive badge for all users
      for (const badgeType of timeSensitiveBadges) {
        try {
          const result = await this.evaluateUsersForBadgeType(userIds, badgeType.id);
          summary.evaluatedUsers += result.evaluatedUsers;
          summary.newAssignments += result.newAssignments;
          summary.errors.push(...result.errors);
        } catch (error) {
          const errorMessage = `Failed to evaluate time-sensitive badge ${badgeType.name}: ${error instanceof Error ? error.message : String(error)}`;
          summary.errors.push(errorMessage);
        }
      }
      
      return summary;
    } catch (error) {
      console.error("Error evaluating time-sensitive badges:", error);
      throw new Error(`Failed to evaluate time-sensitive badges: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}