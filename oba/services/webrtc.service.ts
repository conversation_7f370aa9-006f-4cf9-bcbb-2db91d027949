import type { ServerWebSocket } from "bun";
import { VoiceWebSocketManager } from "../manager/websocket";

// WebRTC event types
export const WebRTCEventTypes = {
  WEBRTC_OFFER: "webrtc_offer",
  WEBRTC_ANSWER: "webrtc_answer",
  WEBRTC_ICE_CANDIDATE: "webrtc_ice_candidate",
  WEBRTC_JOIN_ROOM: "webrtc_join_room",
  WEBRTC_LEAVE_ROOM: "webrtc_leave_room",
  WEBRTC_USER_JOINED: "webrtc_user_joined",
  WEBRTC_USER_LEFT: "webrtc_user_left",
  WEBRTC_MUTE_TOGGLE: "webrtc_mute_toggle",
  WEBRTC_DEAFEN_TOGGLE: "webrtc_deafen_toggle",
  WEBRTC_SPEAKING_START: "webrtc_speaking_start",
  WEBRTC_SPEAKING_END: "webrtc_speaking_end",
};

// STUN/TURN server configuration
export const stunTurnConfig = {
  iceServers: [
    {
      urls: [
        "stun:stun.l.google.com:19302", // Public STUN server (Google)
        "stun:stun1.l.google.com:19302",
        "stun:stun2.l.google.com:19302",
      ],
    },
    {
      urls: [
        "turn:coolify.berkormanli.dev:3478", // Your TURN server
      ],
      credential: "mypassword",
      username: "myuser",
    },
  ],
  iceCandidatePoolSize: 10,
};

// Interface for custom WebSocket data
interface CustomWebSocketData {
  userId: string;
  serverId?: string;
  channelId?: string;
  user?: {
    id: string;
    name: string;
    avatar: string;
    speaking: boolean;
    muted: boolean;
    deafened: boolean;
  };
  token: string;
  isAuthenticated?: boolean;
  isAlive: boolean;
  type?: string;
}

/**
 * WebRTC Signaling Service
 *
 * Handles WebRTC signaling for voice chat, including:
 * - Offer/answer exchange
 * - ICE candidate exchange
 * - Room management
 * - User state management (mute, deafen, speaking)
 */
export class WebRTCSignalingService {
  private voiceWsManager: VoiceWebSocketManager;

  constructor(voiceWsManager: VoiceWebSocketManager) {
    this.voiceWsManager = voiceWsManager;
  }

  /**
   * Handle a user joining a voice channel
   *
   * @param ws WebSocket connection of the joining user
   * @param message Join message data
   */
  handleJoinRoom(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { serverId, channelId, userId } = message.data;
    const topic = `${serverId}:${channelId}`;

    if (!serverId || !channelId || !userId) {
      console.error("Invalid join room message format");
      return;
    }

    // Update WebSocket data
    ws.data.serverId = serverId;
    ws.data.channelId = channelId;

    // Add user to voice channel
    this.voiceWsManager.add(ws);

    // Notify all users in the channel about the new user
    this.broadcastUserState(ws, WebRTCEventTypes.WEBRTC_USER_JOINED, {
      userId: ws.data.userId,
      user: ws.data.user,
      timestamp: Date.now(),
    });

    console.log(
      `User ${ws.data.userId} joined voice channel ${channelId} in server ${serverId}`,
    );
  }

  /**
   * Handle a user leaving a voice channel
   *
   * @param ws WebSocket connection of the leaving user
   * @param message Leave message data
   */
  handleLeaveRoom(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { serverId, channelId } = message.data || ws.data;

    if (!serverId || !channelId) {
      console.error("Invalid leave room message format");
      return;
    }

    // Notify all users in the channel about the leaving user
    this.broadcastUserState(ws, WebRTCEventTypes.WEBRTC_USER_LEFT, {
      userId: ws.data.userId,
      timestamp: Date.now(),
    });

    // Remove user from voice channel
    this.voiceWsManager.remove(ws);

    console.log(
      `User ${ws.data.userId} left voice channel ${channelId} in server ${serverId}`,
    );
  }

  /**
   * Handle WebRTC offer from a client
   *
   * @param ws WebSocket connection of the offering user
   * @param message Offer message data
   */
  handleOffer(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { sdp, targetUserId } = message.data;
    const { serverId, channelId } = ws.data;
    const topic = `${serverId}:${channelId}`;

    if (!sdp || !targetUserId || !serverId || !channelId) {
      console.error("Invalid WebRTC Offer message format");
      return;
    }

    // Get the target user's WebSocket connection
    const targetWs = this.voiceWsManager.getSocketByUserIdAndTopic(
      targetUserId,
      topic,
    );

    if (!targetWs || targetWs.readyState !== WebSocket.OPEN) {
      console.error(
        `Target user ${targetUserId} not found or connection not open`,
      );
      return;
    }

    // Forward the offer to the target user
    targetWs.send(
      JSON.stringify({
        type: WebRTCEventTypes.WEBRTC_OFFER,
        senderUserId: ws.data.userId,
        data: {
          sdp,
          userId: ws.data.userId, // Include the sender's user ID
        },
      }),
    );

    console.log(
      `Forwarded WebRTC Offer from ${ws.data.userId} to ${targetUserId}`,
    );
  }

  /**
   * Handle WebRTC answer from a client
   *
   * @param ws WebSocket connection of the answering user
   * @param message Answer message data
   */
  handleAnswer(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { sdp, targetUserId } = message.data;
    const { serverId, channelId } = ws.data;
    const topic = `${serverId}:${channelId}`;

    if (!sdp || !targetUserId || !serverId || !channelId) {
      console.error("Invalid WebRTC Answer message format");
      return;
    }

    // Get the target user's WebSocket connection
    const targetWs = this.voiceWsManager.getSocketByUserIdAndTopic(
      targetUserId,
      topic,
    );

    if (!targetWs || targetWs.readyState !== WebSocket.OPEN) {
      console.error(
        `Target user ${targetUserId} not found or connection not open`,
      );
      return;
    }

    // Forward the answer to the target user
    targetWs.send(
      JSON.stringify({
        type: WebRTCEventTypes.WEBRTC_ANSWER,
        senderUserId: ws.data.userId,
        data: {
          sdp,
          userId: ws.data.userId, // Include the sender's user ID
        },
      }),
    );

    console.log(
      `Forwarded WebRTC Answer from ${ws.data.userId} to ${targetUserId}`,
    );
  }

  /**
   * Handle ICE candidate from a client
   *
   * @param ws WebSocket connection of the user sending the ICE candidate
   * @param message ICE candidate message data
   */
  handleIceCandidate(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { candidate, targetUserId } = message.data;
    const { serverId, channelId } = ws.data;
    const topic = `${serverId}:${channelId}`;

    if (!candidate || !targetUserId || !serverId || !channelId) {
      console.error("Invalid WebRTC ICE Candidate message format");
      return;
    }

    // Get the target user's WebSocket connection
    const targetWs = this.voiceWsManager.getSocketByUserIdAndTopic(
      targetUserId,
      topic,
    );

    if (!targetWs || targetWs.readyState !== WebSocket.OPEN) {
      console.error(
        `Target user ${targetUserId} not found or connection not open`,
      );
      return;
    }

    // Forward the ICE candidate to the target user
    targetWs.send(
      JSON.stringify({
        type: WebRTCEventTypes.WEBRTC_ICE_CANDIDATE,
        senderUserId: ws.data.userId,
        data: {
          candidate,
          userId: ws.data.userId, // Include the sender's user ID
        },
      }),
    );

    // console.log(`Forwarded WebRTC ICE Candidate from ${ws.data.userId} to ${targetUserId}`);
  }

  /**
   * Handle mute toggle from a client
   *
   * @param ws WebSocket connection of the user toggling mute
   * @param message Mute toggle message data
   */
  handleMuteToggle(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { isMuted } = message.data;

    if (isMuted === undefined) {
      console.error("Invalid mute toggle message format");
      return;
    }

    // Update user state
    if (ws.data.user) {
      ws.data.user.muted = isMuted;
    }

    // Broadcast mute state to all users in the channel
    this.broadcastUserState(ws, WebRTCEventTypes.WEBRTC_MUTE_TOGGLE, {
      userId: ws.data.userId,
      isMuted,
      timestamp: Date.now(),
    });

    console.log(
      `User ${ws.data.userId} ${isMuted ? "muted" : "unmuted"} themselves`,
    );
  }

  /**
   * Handle deafen toggle from a client
   *
   * @param ws WebSocket connection of the user toggling deafen
   * @param message Deafen toggle message data
   */
  handleDeafenToggle(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { isDeafened } = message.data;

    if (isDeafened === undefined) {
      console.error("Invalid deafen toggle message format");
      return;
    }

    // Update user state
    if (ws.data.user) {
      ws.data.user.deafened = isDeafened;
    }

    // Broadcast deafen state to all users in the channel
    this.broadcastUserState(ws, WebRTCEventTypes.WEBRTC_DEAFEN_TOGGLE, {
      userId: ws.data.userId,
      isDeafened,
      timestamp: Date.now(),
    });

    console.log(
      `User ${ws.data.userId} ${isDeafened ? "deafened" : "undeafened"} themselves`,
    );
  }

  /**
   * Handle speaking state change from a client
   *
   * @param ws WebSocket connection of the user changing speaking state
   * @param message Speaking state message data
   */
  handleSpeakingChange(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { isSpeaking } = message.data;

    if (isSpeaking === undefined) {
      console.error("Invalid speaking state message format");
      return;
    }

    // Update user state
    if (ws.data.user) {
      ws.data.user.speaking = isSpeaking;
    }

    // Broadcast speaking state to all users in the channel
    this.broadcastUserState(
      ws,
      isSpeaking
        ? WebRTCEventTypes.WEBRTC_SPEAKING_START
        : WebRTCEventTypes.WEBRTC_SPEAKING_END,
      {
        userId: ws.data.userId,
        timestamp: Date.now(),
      },
    );

    // console.log(`User ${ws.data.userId} ${isSpeaking ? 'started' : 'stopped'} speaking`);
  }

  /**
   * Broadcast user state to all users in a channel
   *
   * @param ws WebSocket connection of the user whose state changed
   * @param eventType Type of event to broadcast
   * @param data Event data
   */
  private broadcastUserState(
    ws: ServerWebSocket<CustomWebSocketData>,
    eventType: string,
    data: any,
  ) {
    const { serverId, channelId } = ws.data;
    const topic = `${serverId}:${channelId}`;

    if (!serverId || !channelId) {
      console.error(
        "Cannot broadcast user state: missing serverId or channelId",
      );
      return;
    }

    const channelSockets = this.voiceWsManager.getSocketsInChannel(topic);

    if (!channelSockets) {
      console.error(`No sockets found for topic ${topic}`);
      return;
    }

    const message = JSON.stringify({
      type: eventType,
      data,
    });

    channelSockets.forEach((socket) => {
      if (socket.readyState === WebSocket.OPEN) {
        socket.send(message);
      }
    });
  }

  /**
   * Get all users in a voice channel
   *
   * @param serverId Server ID
   * @param channelId Channel ID
   * @returns Array of user objects
   */
  getUsersInVoiceChannel(serverId: string, channelId: string) {
    const topic = `${serverId}:${channelId}`;
    const channelSockets = this.voiceWsManager.getSocketsInChannel(topic);

    if (!channelSockets) {
      return [];
    }

    return Array.from(channelSockets)
      .filter((socket) => socket.data.user)
      .map((socket) => socket.data.user);
  }
}
