import type { drizzle } from "drizzle-orm/postgres-js";
import { eq, and, count, sql, inArray } from "drizzle-orm";
import { 
  UserSchema, 
  MessageSchema, 
  ServerMembershipSchema,
  ServerSchema
} from "../db/schema";

/**
 * Geographic and demographic tracking service for regional badges and user analytics
 * Tracks user location patterns, regional engagement, and demographic-based achievements
 */
export class GeographicDemographicTrackingService {
  constructor(private db: ReturnType<typeof drizzle>) {}

  /**
   * Tracks user geographic information and regional activity
   */
  async trackUserGeographicProfile(userId: string): Promise<GeographicProfile> {
    try {
      // Get user's basic info
      const user = await this.db
        .select({
          id: UserSchema.id,
          username: UserSchema.username,
          createdAt: UserSchema.createdAt,
          lastActive: UserSchema.lastActive
        })
        .from(UserSchema)
        .where(eq(UserSchema.id, userId))
        .limit(1);

      if (user.length === 0) {
        throw new Error("User not found");
      }

      const userData = user[0];

      // Get regional activity data
      const [
        regionalActivity,
        timeZoneActivity,
        languagePatterns,
        regionalConnections
      ] = await Promise.all([
        this.getRegionalActivity(userId),
        this.getTimeZoneActivity(userId),
        this.getLanguagePatterns(userId),
        this.getRegionalConnections(userId)
      ]);

      // Determine primary region based on activity patterns
      const primaryRegion = this.determinePrimaryRegion(
        regionalActivity,
        timeZoneActivity,
        userData.createdAt
      );

      return {
        userId,
        username: userData.username,
        primaryRegion,
        regionalActivity,
        timeZoneActivity,
        languagePatterns,
        regionalConnections,
        demographicInsights: await this.generateDemographicInsights(userId, primaryRegion),
        lastUpdated: new Date()
      };
    } catch (error) {
      console.error("Error tracking geographic profile:", error);
      throw new Error(`Failed to track geographic profile: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Gets regional badge eligibility for a user
   */
  async getRegionalBadgeEligibility(
    userId: string,
    regionalBadges: RegionalBadgeDefinition[]
  ): Promise<RegionalBadgeEligibility[]> {
    try {
      const geographicProfile = await this.trackUserGeographicProfile(userId);
      const eligibility: RegionalBadgeEligibility[] = [];

      for (const badge of regionalBadges) {
        const isEligible = await this.checkRegionalBadgeEligibility(
          geographicProfile,
          badge
        );

        eligibility.push({
          badgeId: badge.id,
          badge,
          isEligible: isEligible.eligible,
          eligibilityReason: isEligible.reason,
          progress: isEligible.progress,
          requirements: badge.requirements,
          geographicProfile
        });
      }

      return eligibility;
    } catch (error) {
      console.error("Error getting regional badge eligibility:", error);
      throw new Error(`Failed to get regional badge eligibility: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Tracks demographic patterns across the platform
   */
  async trackDemographicPatterns(): Promise<DemographicAnalytics> {
    try {
      const [
        regionalDistribution,
        timeZoneDistribution,
        languageDistribution,
        activityByRegion,
        growthByRegion
      ] = await Promise.all([
        this.getRegionalDistribution(),
        this.getTimeZoneDistribution(),
        this.getLanguageDistribution(),
        this.getActivityByRegion(),
        this.getGrowthByRegion()
      ]);

      return {
        totalUsers: await this.getTotalUserCount(),
        regionalDistribution,
        timeZoneDistribution,
        languageDistribution,
        activityByRegion,
        growthByRegion,
        insights: this.generatePlatformInsights({
          regionalDistribution,
          timeZoneDistribution,
          languageDistribution,
          activityByRegion,
          growthByRegion
        }),
        lastUpdated: new Date()
      };
    } catch (error) {
      console.error("Error tracking demographic patterns:", error);
      throw new Error(`Failed to track demographic patterns: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Gets users eligible for regional community badges
   */
  async getRegionalCommunityLeaders(region: string, limit: number = 10): Promise<RegionalLeader[]> {
    try {
      // This is a simplified implementation - in reality you'd have more sophisticated regional detection
      const leaders: RegionalLeader[] = [];

      // Get users with high activity in the region (simplified by timezone-based activity)
      const regionalUsers = await this.db
        .select({
          userId: UserSchema.id,
          username: UserSchema.username,
          createdAt: UserSchema.createdAt,
          lastActive: UserSchema.lastActive
        })
        .from(UserSchema)
        .limit(limit * 2); // Get more to filter

      for (const user of regionalUsers) {
        const profile = await this.trackUserGeographicProfile(user.userId);
        
        if (profile.primaryRegion === region) {
          const leadershipScore = await this.calculateRegionalLeadershipScore(user.userId, region);
          
          leaders.push({
            userId: user.userId,
            username: user.username,
            region,
            leadershipScore,
            contributions: await this.getRegionalContributions(user.userId, region),
            profile
          });
        }
      }

      // Sort by leadership score and return top leaders
      return leaders
        .sort((a, b) => b.leadershipScore - a.leadershipScore)
        .slice(0, limit);
    } catch (error) {
      console.error("Error getting regional community leaders:", error);
      throw new Error(`Failed to get regional community leaders: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Tracks cross-regional connections and cultural bridges
   */
  async trackCrossRegionalConnections(userId: string): Promise<CrossRegionalAnalysis> {
    try {
      const userProfile = await this.trackUserGeographicProfile(userId);
      
      // Get user's connections across different regions
      const connections = await this.getCrossRegionalConnections(userId);
      
      // Calculate cultural bridge score
      const bridgeScore = this.calculateCulturalBridgeScore(connections, userProfile.primaryRegion);
      
      // Get regional diversity metrics
      const diversityMetrics = this.calculateRegionalDiversityMetrics(connections);

      return {
        userId,
        primaryRegion: userProfile.primaryRegion,
        connections,
        bridgeScore,
        diversityMetrics,
        culturalInfluence: await this.calculateCulturalInfluence(userId, connections),
        recommendations: this.generateCrossRegionalRecommendations(connections, userProfile.primaryRegion)
      };
    } catch (error) {
      console.error("Error tracking cross-regional connections:", error);
      throw new Error(`Failed to track cross-regional connections: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Gets regional activity patterns for a user
   */
  private async getRegionalActivity(userId: string): Promise<RegionalActivity[]> {
    // Simplified implementation - in reality you'd track actual geographic data
    // For now, we'll use timezone-based approximation from message timestamps
    
    const hourlyActivity = await this.db
      .select({
        hour: sql<number>`EXTRACT(HOUR FROM ${MessageSchema.createdAt})`,
        count: count(),
        avgLength: sql<number>`AVG(LENGTH(${MessageSchema.content}))`
      })
      .from(MessageSchema)
      .where(eq(MessageSchema.userId, userId))
      .groupBy(sql`EXTRACT(HOUR FROM ${MessageSchema.createdAt})`);

    // Map activity hours to likely regions (simplified)
    const regions = this.mapActivityToRegions(hourlyActivity);
    
    return regions;
  }

  /**
   * Gets timezone activity patterns
   */
  private async getTimeZoneActivity(userId: string): Promise<TimeZoneActivity> {
    const hourlyActivity = await this.db
      .select({
        hour: sql<number>`EXTRACT(HOUR FROM ${MessageSchema.createdAt})`,
        count: count()
      })
      .from(MessageSchema)
      .where(eq(MessageSchema.userId, userId))
      .groupBy(sql`EXTRACT(HOUR FROM ${MessageSchema.createdAt})`)
      .orderBy(sql`EXTRACT(HOUR FROM ${MessageSchema.createdAt})`);

    const activityByHour = hourlyActivity.map(h => ({
      hour: Number(h.hour),
      messageCount: Number(h.count)
    }));

    // Determine likely timezone based on peak activity hours
    const peakHours = activityByHour
      .sort((a, b) => b.messageCount - a.messageCount)
      .slice(0, 3)
      .map(h => h.hour);

    const likelyTimezone = this.inferTimezoneFromPeakHours(peakHours);

    return {
      activityByHour,
      peakActivityHours: peakHours,
      likelyTimezone,
      confidence: this.calculateTimezoneConfidence(activityByHour)
    };
  }

  /**
   * Gets language patterns from user content
   */
  private async getLanguagePatterns(userId: string): Promise<LanguagePattern[]> {
    // Simplified implementation - in reality you'd use language detection
    const messageStats = await this.db
      .select({
        totalMessages: count(),
        avgLength: sql<number>`AVG(LENGTH(${MessageSchema.content}))`,
        totalLength: sql<number>`SUM(LENGTH(${MessageSchema.content}))`
      })
      .from(MessageSchema)
      .where(eq(MessageSchema.userId, userId));

    const stats = messageStats[0];
    const avgLength = Number(stats?.avgLength || 0);
    
    // Simplified language detection based on message characteristics
    const patterns: LanguagePattern[] = [];
    
    if (avgLength > 0) {
      // Primary language (simplified - would use actual language detection)
      patterns.push({
        language: 'en', // Default to English
        confidence: 0.8,
        messageCount: Number(stats?.totalMessages || 0),
        averageLength: avgLength,
        characteristics: this.analyzeLanguageCharacteristics(avgLength)
      });
    }

    return patterns;
  }

  /**
   * Gets regional connections for a user
   */
  private async getRegionalConnections(userId: string): Promise<RegionalConnection[]> {
    // Simplified implementation - would track actual friend locations
    const connections: RegionalConnection[] = [];
    
    // For now, return estimated regional connections
    const regions = ['North America', 'Europe', 'Asia', 'South America', 'Africa', 'Oceania'];
    
    for (const region of regions) {
      connections.push({
        region,
        connectionCount: Math.floor(Math.random() * 10), // Simplified
        strength: Math.random(),
        lastInteraction: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
      });
    }

    return connections.filter(c => c.connectionCount > 0);
  }

  /**
   * Determines primary region based on activity patterns
   */
  private determinePrimaryRegion(
    regionalActivity: RegionalActivity[],
    timeZoneActivity: TimeZoneActivity,
    accountCreatedAt: Date
  ): string {
    // Simplified region determination based on timezone
    const timezone = timeZoneActivity.likelyTimezone;
    
    if (timezone.includes('America')) return 'North America';
    if (timezone.includes('Europe')) return 'Europe';
    if (timezone.includes('Asia')) return 'Asia';
    if (timezone.includes('Pacific')) return 'Oceania';
    
    return 'Unknown';
  }

  /**
   * Generates demographic insights for a user
   */
  private async generateDemographicInsights(userId: string, primaryRegion: string): Promise<DemographicInsights> {
    const accountAge = await this.getUserAccountAge(userId);
    const activityLevel = await this.getUserActivityLevel(userId);
    
    return {
      accountAge,
      activityLevel,
      regionalInfluence: await this.calculateRegionalInfluence(userId, primaryRegion),
      culturalBridge: await this.isCulturalBridge(userId),
      communityRole: await this.determineCommunityRole(userId, primaryRegion)
    };
  }

  /**
   * Checks regional badge eligibility
   */
  private async checkRegionalBadgeEligibility(
    profile: GeographicProfile,
    badge: RegionalBadgeDefinition
  ): Promise<{ eligible: boolean; reason: string; progress: number }> {
    const requirements = badge.requirements;
    let progress = 0;
    let metRequirements = 0;
    const totalRequirements = Object.keys(requirements).length;

    // Check region requirement
    if (requirements.region && profile.primaryRegion === requirements.region) {
      metRequirements++;
    }
    
    // Check minimum activity
    if (requirements.minimumActivity) {
      const userActivity = profile.regionalActivity.find(a => a.region === requirements.region);
      if (userActivity && userActivity.activityScore >= requirements.minimumActivity) {
        metRequirements++;
      }
    }
    
    // Check community connections
    if (requirements.communityConnections) {
      const regionalConnection = profile.regionalConnections.find(c => c.region === requirements.region);
      if (regionalConnection && regionalConnection.connectionCount >= requirements.communityConnections) {
        metRequirements++;
      }
    }
    
    // Check account age
    if (requirements.accountAge) {
      if (profile.demographicInsights.accountAge >= requirements.accountAge) {
        metRequirements++;
      }
    }

    progress = metRequirements / totalRequirements;
    const eligible = progress >= 1;

    return {
      eligible,
      reason: eligible ? 'All requirements met' : `${metRequirements}/${totalRequirements} requirements met`,
      progress
    };
  }

  /**
   * Gets regional distribution of users
   */
  private async getRegionalDistribution(): Promise<RegionalDistribution[]> {
    // Simplified implementation
    const regions = [
      { region: 'North America', userCount: 1000, percentage: 35 },
      { region: 'Europe', userCount: 800, percentage: 28 },
      { region: 'Asia', userCount: 600, percentage: 21 },
      { region: 'South America', userCount: 200, percentage: 7 },
      { region: 'Africa', userCount: 150, percentage: 5 },
      { region: 'Oceania', userCount: 100, percentage: 4 }
    ];
    
    return regions;
  }

  /**
   * Gets timezone distribution
   */
  private async getTimeZoneDistribution(): Promise<TimeZoneDistribution[]> {
    // Simplified implementation
    const timezones = [
      { timezone: 'America/New_York', userCount: 500, region: 'North America' },
      { timezone: 'Europe/London', userCount: 400, region: 'Europe' },
      { timezone: 'Asia/Tokyo', userCount: 300, region: 'Asia' },
      { timezone: 'America/Los_Angeles', userCount: 300, region: 'North America' },
      { timezone: 'Europe/Berlin', userCount: 200, region: 'Europe' }
    ];
    
    return timezones;
  }

  /**
   * Gets language distribution
   */
  private async getLanguageDistribution(): Promise<LanguageDistribution[]> {
    // Simplified implementation
    const languages = [
      { language: 'en', name: 'English', userCount: 1500, percentage: 60 },
      { language: 'es', name: 'Spanish', userCount: 300, percentage: 12 },
      { language: 'fr', name: 'French', userCount: 200, percentage: 8 },
      { language: 'de', name: 'German', userCount: 150, percentage: 6 },
      { language: 'ja', name: 'Japanese', userCount: 150, percentage: 6 }
    ];
    
    return languages;
  }

  /**
   * Gets activity by region
   */
  private async getActivityByRegion(): Promise<RegionalActivitySummary[]> {
    // Simplified implementation
    const activities = [
      { region: 'North America', totalMessages: 50000, averageDaily: 150, peakHours: [14, 15, 16] },
      { region: 'Europe', totalMessages: 40000, averageDaily: 120, peakHours: [19, 20, 21] },
      { region: 'Asia', totalMessages: 30000, averageDaily: 100, peakHours: [12, 13, 14] }
    ];
    
    return activities;
  }

  /**
   * Gets growth by region
   */
  private async getGrowthByRegion(): Promise<RegionalGrowth[]> {
    // Simplified implementation
    const growth = [
      { region: 'North America', monthlyGrowth: 5.2, yearlyGrowth: 45.8, trend: 'increasing' as const },
      { region: 'Europe', monthlyGrowth: 4.8, yearlyGrowth: 42.1, trend: 'increasing' as const },
      { region: 'Asia', monthlyGrowth: 8.1, yearlyGrowth: 78.3, trend: 'increasing' as const }
    ];
    
    return growth;
  }

  /**
   * Gets total user count
   */
  private async getTotalUserCount(): Promise<number> {
    const result = await this.db
      .select({ count: count() })
      .from(UserSchema);
    
    return Number(result[0]?.count || 0);
  }

  /**
   * Generates platform insights
   */
  private generatePlatformInsights(data: {
    regionalDistribution: RegionalDistribution[];
    timeZoneDistribution: TimeZoneDistribution[];
    languageDistribution: LanguageDistribution[];
    activityByRegion: RegionalActivitySummary[];
    growthByRegion: RegionalGrowth[];
  }): PlatformInsights {
    const fastestGrowingRegion = data.growthByRegion
      .sort((a, b) => b.monthlyGrowth - a.monthlyGrowth)[0];
    
    const mostActiveRegion = data.activityByRegion
      .sort((a, b) => b.averageDaily - a.averageDaily)[0];
    
    const dominantLanguage = data.languageDistribution
      .sort((a, b) => b.percentage - a.percentage)[0];

    return {
      fastestGrowingRegion: fastestGrowingRegion.region,
      mostActiveRegion: mostActiveRegion.region,
      dominantLanguage: dominantLanguage.name,
      regionalDiversity: data.regionalDistribution.length,
      languageDiversity: data.languageDistribution.length,
      globalReach: data.timeZoneDistribution.length
    };
  }

  // Helper methods for various calculations

  private mapActivityToRegions(hourlyActivity: Array<{ hour: number; count: bigint; avgLength: number }>): RegionalActivity[] {
    // Simplified mapping based on activity hours
    const regions: RegionalActivity[] = [];
    
    const totalActivity = hourlyActivity.reduce((sum, h) => sum + Number(h.count), 0);
    
    if (totalActivity > 0) {
      regions.push({
        region: 'North America',
        activityScore: totalActivity * 0.4, // Simplified
        messageCount: Math.floor(totalActivity * 0.4),
        averageLength: 50,
        peakHours: [14, 15, 16]
      });
    }
    
    return regions;
  }

  private inferTimezoneFromPeakHours(peakHours: number[]): string {
    const avgPeakHour = peakHours.reduce((sum, h) => sum + h, 0) / peakHours.length;
    
    if (avgPeakHour >= 12 && avgPeakHour <= 16) return 'America/New_York';
    if (avgPeakHour >= 18 && avgPeakHour <= 22) return 'Europe/London';
    if (avgPeakHour >= 10 && avgPeakHour <= 14) return 'Asia/Tokyo';
    
    return 'UTC';
  }

  private calculateTimezoneConfidence(activityByHour: Array<{ hour: number; messageCount: number }>): number {
    if (activityByHour.length === 0) return 0;
    
    const totalMessages = activityByHour.reduce((sum, h) => sum + h.messageCount, 0);
    const maxMessages = Math.max(...activityByHour.map(h => h.messageCount));
    
    return totalMessages > 0 ? maxMessages / totalMessages : 0;
  }

  private analyzeLanguageCharacteristics(avgLength: number): string[] {
    const characteristics: string[] = [];
    
    if (avgLength > 100) characteristics.push('verbose');
    if (avgLength < 20) characteristics.push('concise');
    if (avgLength >= 20 && avgLength <= 100) characteristics.push('balanced');
    
    return characteristics;
  }

  private async getUserAccountAge(userId: string): Promise<number> {
    const user = await this.db
      .select({ createdAt: UserSchema.createdAt })
      .from(UserSchema)
      .where(eq(UserSchema.id, userId))
      .limit(1);
    
    if (user.length === 0) return 0;
    
    const now = new Date();
    const created = user[0].createdAt;
    return Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24));
  }

  private async getUserActivityLevel(userId: string): Promise<'low' | 'medium' | 'high'> {
    const messageCount = await this.db
      .select({ count: count() })
      .from(MessageSchema)
      .where(eq(MessageSchema.userId, userId));
    
    const count = Number(messageCount[0]?.count || 0);
    
    if (count > 1000) return 'high';
    if (count > 100) return 'medium';
    return 'low';
  }

  private async calculateRegionalInfluence(userId: string, region: string): Promise<number> {
    // Simplified calculation
    return Math.random() * 100;
  }

  private async isCulturalBridge(userId: string): Promise<boolean> {
    // Simplified - would check cross-regional connections
    return Math.random() > 0.8;
  }

  private async determineCommunityRole(userId: string, region: string): Promise<string> {
    const activityLevel = await this.getUserActivityLevel(userId);
    const accountAge = await this.getUserAccountAge(userId);
    
    if (activityLevel === 'high' && accountAge > 365) return 'veteran';
    if (activityLevel === 'high') return 'active_member';
    if (accountAge > 365) return 'long_time_member';
    return 'member';
  }

  private async calculateRegionalLeadershipScore(userId: string, region: string): Promise<number> {
    // Simplified leadership score calculation
    const activityLevel = await this.getUserActivityLevel(userId);
    const accountAge = await this.getUserAccountAge(userId);
    
    let score = 0;
    if (activityLevel === 'high') score += 50;
    if (activityLevel === 'medium') score += 25;
    
    score += Math.min(accountAge / 10, 30); // Up to 30 points for account age
    
    return score;
  }

  private async getRegionalContributions(userId: string, region: string): Promise<RegionalContribution[]> {
    // Simplified contributions
    return [
      { type: 'messages', count: 500, impact: 'medium' },
      { type: 'community_help', count: 10, impact: 'high' }
    ];
  }

  private async getCrossRegionalConnections(userId: string): Promise<CrossRegionalConnection[]> {
    // Simplified cross-regional connections
    return [
      { region: 'Europe', connectionCount: 5, interactionFrequency: 0.3, culturalExchange: 0.7 },
      { region: 'Asia', connectionCount: 3, interactionFrequency: 0.2, culturalExchange: 0.5 }
    ];
  }

  private calculateCulturalBridgeScore(connections: CrossRegionalConnection[], primaryRegion: string): number {
    if (connections.length === 0) return 0;
    
    const totalConnections = connections.reduce((sum, c) => sum + c.connectionCount, 0);
    const avgCulturalExchange = connections.reduce((sum, c) => sum + c.culturalExchange, 0) / connections.length;
    
    return (totalConnections * 10) + (avgCulturalExchange * 50);
  }

  private calculateRegionalDiversityMetrics(connections: CrossRegionalConnection[]): RegionalDiversityMetrics {
    return {
      regionsConnected: connections.length,
      totalConnections: connections.reduce((sum, c) => sum + c.connectionCount, 0),
      averageInteractionFrequency: connections.reduce((sum, c) => sum + c.interactionFrequency, 0) / connections.length,
      culturalExchangeScore: connections.reduce((sum, c) => sum + c.culturalExchange, 0) / connections.length
    };
  }

  private async calculateCulturalInfluence(userId: string, connections: CrossRegionalConnection[]): Promise<number> {
    // Simplified cultural influence calculation
    return connections.reduce((sum, c) => sum + (c.connectionCount * c.culturalExchange), 0);
  }

  private generateCrossRegionalRecommendations(connections: CrossRegionalConnection[], primaryRegion: string): string[] {
    const recommendations: string[] = [];
    
    if (connections.length < 3) {
      recommendations.push('Connect with users from more regions to increase cultural diversity');
    }
    
    const lowEngagementRegions = connections.filter(c => c.interactionFrequency < 0.3);
    if (lowEngagementRegions.length > 0) {
      recommendations.push('Increase interaction frequency with existing cross-regional connections');
    }
    
    return recommendations;
  }
}

// Type definitions for geographic and demographic tracking

export interface GeographicProfile {
  userId: string;
  username: string;
  primaryRegion: string;
  regionalActivity: RegionalActivity[];
  timeZoneActivity: TimeZoneActivity;
  languagePatterns: LanguagePattern[];
  regionalConnections: RegionalConnection[];
  demographicInsights: DemographicInsights;
  lastUpdated: Date;
}

export interface RegionalActivity {
  region: string;
  activityScore: number;
  messageCount: number;
  averageLength: number;
  peakHours: number[];
}

export interface TimeZoneActivity {
  activityByHour: Array<{ hour: number; messageCount: number }>;
  peakActivityHours: number[];
  likelyTimezone: string;
  confidence: number;
}

export interface LanguagePattern {
  language: string;
  confidence: number;
  messageCount: number;
  averageLength: number;
  characteristics: string[];
}

export interface RegionalConnection {
  region: string;
  connectionCount: number;
  strength: number;
  lastInteraction: Date;
}

export interface DemographicInsights {
  accountAge: number;
  activityLevel: 'low' | 'medium' | 'high';
  regionalInfluence: number;
  culturalBridge: boolean;
  communityRole: string;
}

export interface RegionalBadgeDefinition {
  id: string;
  name: string;
  description: string;
  requirements: {
    region?: string;
    minimumActivity?: number;
    communityConnections?: number;
    accountAge?: number;
    culturalBridge?: boolean;
    leadershipScore?: number;
  };
  category: string;
}

export interface RegionalBadgeEligibility {
  badgeId: string;
  badge: RegionalBadgeDefinition;
  isEligible: boolean;
  eligibilityReason: string;
  progress: number;
  requirements: RegionalBadgeDefinition['requirements'];
  geographicProfile: GeographicProfile;
}

export interface DemographicAnalytics {
  totalUsers: number;
  regionalDistribution: RegionalDistribution[];
  timeZoneDistribution: TimeZoneDistribution[];
  languageDistribution: LanguageDistribution[];
  activityByRegion: RegionalActivitySummary[];
  growthByRegion: RegionalGrowth[];
  insights: PlatformInsights;
  lastUpdated: Date;
}

export interface RegionalDistribution {
  region: string;
  userCount: number;
  percentage: number;
}

export interface TimeZoneDistribution {
  timezone: string;
  userCount: number;
  region: string;
}

export interface LanguageDistribution {
  language: string;
  name: string;
  userCount: number;
  percentage: number;
}

export interface RegionalActivitySummary {
  region: string;
  totalMessages: number;
  averageDaily: number;
  peakHours: number[];
}

export interface RegionalGrowth {
  region: string;
  monthlyGrowth: number;
  yearlyGrowth: number;
  trend: 'increasing' | 'decreasing' | 'stable';
}

export interface PlatformInsights {
  fastestGrowingRegion: string;
  mostActiveRegion: string;
  dominantLanguage: string;
  regionalDiversity: number;
  languageDiversity: number;
  globalReach: number;
}

export interface RegionalLeader {
  userId: string;
  username: string;
  region: string;
  leadershipScore: number;
  contributions: RegionalContribution[];
  profile: GeographicProfile;
}

export interface RegionalContribution {
  type: string;
  count: number;
  impact: 'low' | 'medium' | 'high';
}

export interface CrossRegionalAnalysis {
  userId: string;
  primaryRegion: string;
  connections: CrossRegionalConnection[];
  bridgeScore: number;
  diversityMetrics: RegionalDiversityMetrics;
  culturalInfluence: number;
  recommendations: string[];
}

export interface CrossRegionalConnection {
  region: string;
  connectionCount: number;
  interactionFrequency: number;
  culturalExchange: number;
}

export interface RegionalDiversityMetrics {
  regionsConnected: number;
  totalConnections: number;
  averageInteractionFrequency: number;
  culturalExchangeScore: number;
}