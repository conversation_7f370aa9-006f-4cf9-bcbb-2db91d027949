import type { drizzle } from "drizzle-orm/postgres-js";
import { eq, and, count, sql, desc, gte, lte, between } from "drizzle-orm";
import { 
  UserSchema, 
  ServerInviteSchema,
  ServerMembershipSchema,
  ServerSchema,
  FriendshipSchema
} from "../db/schema";

/**
 * Invitation and referral tracking service for community growth badges
 * Tracks user invitation patterns, referral success rates, and community building achievements
 */
export class InvitationReferralTrackingService {
  constructor(private db: ReturnType<typeof drizzle>) {}

  /**
   * Tracks comprehensive invitation and referral metrics for a user
   */
  async trackUserInvitationMetrics(userId: string): Promise<InvitationMetrics> {
    try {
      const [
        invitationStats,
        referralStats,
        networkGrowth,
        invitationQuality,
        communityImpact
      ] = await Promise.all([
        this.getInvitationStats(userId),
        this.getReferralStats(userId),
        this.getNetworkGrowthStats(userId),
        this.getInvitationQualityMetrics(userId),
        this.getCommunityImpactMetrics(userId)
      ]);

      const overallScore = this.calculateInvitationScore({
        invitationStats,
        referralStats,
        networkGrowth,
        invitationQuality,
        communityImpact
      });

      return {
        userId,
        invitationStats,
        referralStats,
        networkGrowth,
        invitationQuality,
        communityImpact,
        overallScore,
        achievements: await this.getInvitationAchievements(userId, {
          invitationStats,
          referralStats,
          networkGrowth,
          invitationQuality,
          communityImpact
        }),
        lastUpdated: new Date()
      };
    } catch (error) {
      console.error("Error tracking invitation metrics:", error);
      throw new Error(`Failed to track invitation metrics: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Tracks referral chains and network effects
   */
  async trackReferralChains(userId: string, maxDepth: number = 3): Promise<ReferralChain[]> {
    try {
      const chains: ReferralChain[] = [];
      
      // Get direct referrals (users who joined through this user's invites)
      const directReferrals = await this.getDirectReferrals(userId);
      
      for (const referral of directReferrals) {
        const chain = await this.buildReferralChain(referral, userId, 1, maxDepth);
        chains.push(chain);
      }

      return chains;
    } catch (error) {
      console.error("Error tracking referral chains:", error);
      throw new Error(`Failed to track referral chains: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Gets invitation leaderboard for community recognition
   */
  async getInvitationLeaderboard(
    timeWindow?: { startDate: Date; endDate: Date },
    limit: number = 50
  ): Promise<InvitationLeaderboardEntry[]> {
    try {
      const leaderboard: InvitationLeaderboardEntry[] = [];
      
      // Get users with invitation activity
      let query = this.db
        .select({
          userId: ServerInviteSchema.createdById,
          totalInvites: count(ServerInviteSchema.id),
          totalUses: sql<number>`SUM(${ServerInviteSchema.uses})`
        })
        .from(ServerInviteSchema)
        .where(sql`${ServerInviteSchema.createdById} IS NOT NULL`)
        .groupBy(ServerInviteSchema.createdById)
        .orderBy(desc(sql`SUM(${ServerInviteSchema.uses})`))
        .limit(limit);

      if (timeWindow) {
        query = query.where(
          between(ServerInviteSchema.createdAt, timeWindow.startDate, timeWindow.endDate)
        ) as any;
      }

      const results = await query;

      for (const result of results) {
        if (!result.userId) continue;

        const user = await this.db
          .select({
            username: UserSchema.username,
            createdAt: UserSchema.createdAt
          })
          .from(UserSchema)
          .where(eq(UserSchema.id, result.userId))
          .limit(1);

        if (user.length === 0) continue;

        const metrics = await this.trackUserInvitationMetrics(result.userId);
        
        leaderboard.push({
          userId: result.userId,
          username: user[0].username,
          rank: leaderboard.length + 1,
          totalInvitesSent: Number(result.totalInvites),
          totalInvitesUsed: Number(result.totalUses || 0),
          successRate: Number(result.totalInvites) > 0 
            ? Number(result.totalUses || 0) / Number(result.totalInvites) 
            : 0,
          overallScore: metrics.overallScore,
          achievements: metrics.achievements,
          accountAge: Math.floor(
            (Date.now() - user[0].createdAt.getTime()) / (1000 * 60 * 60 * 24)
          )
        });
      }

      return leaderboard;
    } catch (error) {
      console.error("Error getting invitation leaderboard:", error);
      throw new Error(`Failed to get invitation leaderboard: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Analyzes invitation patterns and trends
   */
  async analyzeInvitationPatterns(
    userId: string,
    timeWindow: { startDate: Date; endDate: Date }
  ): Promise<InvitationPatternAnalysis> {
    try {
      const [
        temporalPatterns,
        serverPatterns,
        successPatterns,
        networkEffects
      ] = await Promise.all([
        this.getTemporalInvitationPatterns(userId, timeWindow),
        this.getServerInvitationPatterns(userId, timeWindow),
        this.getInvitationSuccessPatterns(userId, timeWindow),
        this.getNetworkEffectPatterns(userId, timeWindow)
      ]);

      return {
        userId,
        timeWindow,
        temporalPatterns,
        serverPatterns,
        successPatterns,
        networkEffects,
        insights: this.generateInvitationInsights({
          temporalPatterns,
          serverPatterns,
          successPatterns,
          networkEffects
        }),
        recommendations: this.generateInvitationRecommendations({
          temporalPatterns,
          serverPatterns,
          successPatterns,
          networkEffects
        })
      };
    } catch (error) {
      console.error("Error analyzing invitation patterns:", error);
      throw new Error(`Failed to analyze invitation patterns: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Tracks community building impact
   */
  async trackCommunityBuildingImpact(userId: string): Promise<CommunityBuildingImpact> {
    try {
      const [
        directImpact,
        indirectImpact,
        retentionImpact,
        diversityImpact,
        engagementImpact
      ] = await Promise.all([
        this.getDirectCommunityImpact(userId),
        this.getIndirectCommunityImpact(userId),
        this.getRetentionImpact(userId),
        this.getDiversityImpact(userId),
        this.getEngagementImpact(userId)
      ]);

      const totalImpactScore = this.calculateCommunityImpactScore({
        directImpact,
        indirectImpact,
        retentionImpact,
        diversityImpact,
        engagementImpact
      });

      return {
        userId,
        directImpact,
        indirectImpact,
        retentionImpact,
        diversityImpact,
        engagementImpact,
        totalImpactScore,
        impactLevel: this.categorizeImpactLevel(totalImpactScore),
        recognitionEligibility: await this.checkRecognitionEligibility(userId, totalImpactScore)
      };
    } catch (error) {
      console.error("Error tracking community building impact:", error);
      throw new Error(`Failed to track community building impact: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Gets invitation statistics for a user
   */
  private async getInvitationStats(userId: string): Promise<InvitationStats> {
    const inviteStats = await this.db
      .select({
        totalInvites: count(),
        totalUses: sql<number>`SUM(${ServerInviteSchema.uses})`,
        maxUses: sql<number>`SUM(${ServerInviteSchema.maxUses})`,
        activeInvites: sql<number>`COUNT(CASE WHEN ${ServerInviteSchema.uses} < ${ServerInviteSchema.maxUses} THEN 1 END)`
      })
      .from(ServerInviteSchema)
      .where(eq(ServerInviteSchema.createdById, userId));

    const stats = inviteStats[0];
    const totalInvites = Number(stats?.totalInvites || 0);
    const totalUses = Number(stats?.totalUses || 0);
    const maxUses = Number(stats?.maxUses || 0);

    return {
      totalInvitesSent: totalInvites,
      totalInvitesUsed: totalUses,
      totalInviteCapacity: maxUses,
      activeInvites: Number(stats?.activeInvites || 0),
      successRate: totalInvites > 0 ? totalUses / totalInvites : 0,
      utilizationRate: maxUses > 0 ? totalUses / maxUses : 0
    };
  }

  /**
   * Gets referral statistics for a user
   */
  private async getReferralStats(userId: string): Promise<ReferralStats> {
    // Get users who joined through this user's invites
    const directReferrals = await this.getDirectReferrals(userId);
    
    // Calculate referral retention (simplified - users still active)
    const activeReferrals = await this.getActiveReferrals(userId);
    
    // Get referral network size (referrals + their referrals)
    const networkSize = await this.getReferralNetworkSize(userId);

    return {
      directReferrals: directReferrals.length,
      activeReferrals: activeReferrals.length,
      referralRetentionRate: directReferrals.length > 0 
        ? activeReferrals.length / directReferrals.length 
        : 0,
      networkSize,
      averageReferralLifespan: await this.getAverageReferralLifespan(userId),
      referralEngagementScore: await this.getReferralEngagementScore(userId)
    };
  }

  /**
   * Gets network growth statistics
   */
  private async getNetworkGrowthStats(userId: string): Promise<NetworkGrowthStats> {
    const monthlyGrowth = await this.getMonthlyNetworkGrowth(userId);
    const growthTrend = this.calculateGrowthTrend(monthlyGrowth);
    
    return {
      monthlyGrowth,
      growthTrend,
      peakGrowthMonth: this.findPeakGrowthMonth(monthlyGrowth),
      consistencyScore: this.calculateGrowthConsistency(monthlyGrowth),
      accelerationFactor: this.calculateGrowthAcceleration(monthlyGrowth)
    };
  }

  /**
   * Gets invitation quality metrics
   */
  private async getInvitationQualityMetrics(userId: string): Promise<InvitationQualityMetrics> {
    const referrals = await this.getDirectReferrals(userId);
    
    let highQualityReferrals = 0;
    let totalEngagementScore = 0;
    
    for (const referral of referrals) {
      const engagementScore = await this.calculateReferralEngagement(referral.userId);
      totalEngagementScore += engagementScore;
      
      if (engagementScore > 0.7) { // High engagement threshold
        highQualityReferrals++;
      }
    }

    return {
      qualityScore: referrals.length > 0 ? totalEngagementScore / referrals.length : 0,
      highQualityReferrals,
      averageReferralEngagement: referrals.length > 0 ? totalEngagementScore / referrals.length : 0,
      retentionQuality: await this.calculateRetentionQuality(userId),
      contributionQuality: await this.calculateContributionQuality(userId)
    };
  }

  /**
   * Gets community impact metrics
   */
  private async getCommunityImpactMetrics(userId: string): Promise<CommunityImpactMetrics> {
    const referrals = await this.getDirectReferrals(userId);
    
    // Calculate various impact metrics
    const serverDiversification = await this.calculateServerDiversification(userId);
    const communityGrowthContribution = referrals.length;
    const networkEffectMultiplier = await this.calculateNetworkEffectMultiplier(userId);
    
    return {
      communityGrowthContribution,
      serverDiversification,
      networkEffectMultiplier,
      culturalDiversityImpact: await this.calculateCulturalDiversityImpact(userId),
      longTermImpactScore: await this.calculateLongTermImpact(userId)
    };
  }

  /**
   * Gets direct referrals for a user
   */
  private async getDirectReferrals(userId: string): Promise<Array<{ userId: string; joinedAt: Date; serverId: string }>> {
    // Simplified implementation - in reality you'd track invite usage more precisely
    const invites = await this.db
      .select({
        id: ServerInviteSchema.id,
        serverId: ServerInviteSchema.serverId,
        uses: ServerInviteSchema.uses,
        createdAt: ServerInviteSchema.createdAt
      })
      .from(ServerInviteSchema)
      .where(eq(ServerInviteSchema.createdById, userId));

    const referrals: Array<{ userId: string; joinedAt: Date; serverId: string }> = [];
    
    // This is simplified - in a real implementation you'd have a proper referral tracking table
    for (const invite of invites) {
      const uses = Number(invite.uses);
      for (let i = 0; i < uses; i++) {
        referrals.push({
          userId: `referral_${invite.id}_${i}`, // Simplified
          joinedAt: invite.createdAt,
          serverId: invite.serverId || ''
        });
      }
    }

    return referrals;
  }

  /**
   * Builds a referral chain recursively
   */
  private async buildReferralChain(
    referral: { userId: string; joinedAt: Date; serverId: string },
    referrerId: string,
    depth: number,
    maxDepth: number
  ): Promise<ReferralChain> {
    const chain: ReferralChain = {
      referralId: referral.userId,
      referrerId,
      depth,
      joinedAt: referral.joinedAt,
      serverId: referral.serverId,
      isActive: await this.isUserActive(referral.userId),
      engagementScore: await this.calculateReferralEngagement(referral.userId),
      subReferrals: []
    };

    if (depth < maxDepth) {
      const subReferrals = await this.getDirectReferrals(referral.userId);
      for (const subReferral of subReferrals) {
        const subChain = await this.buildReferralChain(subReferral, referral.userId, depth + 1, maxDepth);
        chain.subReferrals.push(subChain);
      }
    }

    return chain;
  }

  /**
   * Gets active referrals (users still using the platform)
   */
  private async getActiveReferrals(userId: string): Promise<Array<{ userId: string; lastActive: Date }>> {
    // Simplified implementation
    const referrals = await this.getDirectReferrals(userId);
    const activeReferrals: Array<{ userId: string; lastActive: Date }> = [];
    
    for (const referral of referrals) {
      const isActive = await this.isUserActive(referral.userId);
      if (isActive) {
        activeReferrals.push({
          userId: referral.userId,
          lastActive: new Date() // Simplified
        });
      }
    }
    
    return activeReferrals;
  }

  /**
   * Gets referral network size (including indirect referrals)
   */
  private async getReferralNetworkSize(userId: string): Promise<number> {
    const chains = await this.trackReferralChains(userId, 3);
    
    let networkSize = 0;
    const countChain = (chain: ReferralChain) => {
      networkSize++;
      chain.subReferrals.forEach(countChain);
    };
    
    chains.forEach(countChain);
    return networkSize;
  }

  /**
   * Calculates invitation score from various metrics
   */
  private calculateInvitationScore(metrics: {
    invitationStats: InvitationStats;
    referralStats: ReferralStats;
    networkGrowth: NetworkGrowthStats;
    invitationQuality: InvitationQualityMetrics;
    communityImpact: CommunityImpactMetrics;
  }): number {
    const {
      invitationStats,
      referralStats,
      networkGrowth,
      invitationQuality,
      communityImpact
    } = metrics;

    // Weighted scoring system
    const invitationScore = Math.min(invitationStats.successRate * 100, 100) * 0.2;
    const referralScore = Math.min(referralStats.referralRetentionRate * 100, 100) * 0.25;
    const growthScore = Math.min(networkGrowth.consistencyScore * 100, 100) * 0.15;
    const qualityScore = Math.min(invitationQuality.qualityScore * 100, 100) * 0.25;
    const impactScore = Math.min(communityImpact.networkEffectMultiplier * 20, 100) * 0.15;

    return Math.min(invitationScore + referralScore + growthScore + qualityScore + impactScore, 100);
  }

  // Helper methods for various calculations

  private async getAverageReferralLifespan(userId: string): Promise<number> {
    // Simplified - would calculate actual user lifespans
    return 180; // 180 days average
  }

  private async getReferralEngagementScore(userId: string): Promise<number> {
    // Simplified engagement score calculation
    return Math.random() * 100;
  }

  private async getMonthlyNetworkGrowth(userId: string): Promise<Array<{ month: string; growth: number }>> {
    // Simplified monthly growth data
    const months = [];
    const now = new Date();
    
    for (let i = 11; i >= 0; i--) {
      const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
      months.push({
        month: month.toISOString().substring(0, 7),
        growth: Math.floor(Math.random() * 10)
      });
    }
    
    return months;
  }

  private calculateGrowthTrend(monthlyGrowth: Array<{ month: string; growth: number }>): 'increasing' | 'decreasing' | 'stable' {
    if (monthlyGrowth.length < 2) return 'stable';
    
    const recent = monthlyGrowth.slice(-3).reduce((sum, m) => sum + m.growth, 0) / 3;
    const earlier = monthlyGrowth.slice(-6, -3).reduce((sum, m) => sum + m.growth, 0) / 3;
    
    if (recent > earlier * 1.1) return 'increasing';
    if (recent < earlier * 0.9) return 'decreasing';
    return 'stable';
  }

  private findPeakGrowthMonth(monthlyGrowth: Array<{ month: string; growth: number }>): string {
    return monthlyGrowth.reduce((max, current) => 
      current.growth > max.growth ? current : max
    ).month;
  }

  private calculateGrowthConsistency(monthlyGrowth: Array<{ month: string; growth: number }>): number {
    if (monthlyGrowth.length === 0) return 0;
    
    const values = monthlyGrowth.map(m => m.growth);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    
    return mean > 0 ? Math.max(0, 1 - (stdDev / mean)) : 0;
  }

  private calculateGrowthAcceleration(monthlyGrowth: Array<{ month: string; growth: number }>): number {
    if (monthlyGrowth.length < 3) return 0;
    
    const recent = monthlyGrowth.slice(-3);
    const earlier = monthlyGrowth.slice(-6, -3);
    
    const recentAvg = recent.reduce((sum, m) => sum + m.growth, 0) / recent.length;
    const earlierAvg = earlier.reduce((sum, m) => sum + m.growth, 0) / earlier.length;
    
    return earlierAvg > 0 ? (recentAvg - earlierAvg) / earlierAvg : 0;
  }

  private async calculateReferralEngagement(userId: string): Promise<number> {
    // Simplified engagement calculation
    return Math.random();
  }

  private async calculateRetentionQuality(userId: string): Promise<number> {
    const referrals = await this.getDirectReferrals(userId);
    const activeReferrals = await this.getActiveReferrals(userId);
    
    return referrals.length > 0 ? activeReferrals.length / referrals.length : 0;
  }

  private async calculateContributionQuality(userId: string): Promise<number> {
    // Simplified contribution quality calculation
    return Math.random();
  }

  private async calculateServerDiversification(userId: string): Promise<number> {
    const invites = await this.db
      .select({
        serverId: ServerInviteSchema.serverId
      })
      .from(ServerInviteSchema)
      .where(eq(ServerInviteSchema.createdById, userId));

    const uniqueServers = new Set(invites.map(i => i.serverId).filter(Boolean));
    return uniqueServers.size;
  }

  private async calculateNetworkEffectMultiplier(userId: string): Promise<number> {
    const networkSize = await this.getReferralNetworkSize(userId);
    const directReferrals = await this.getDirectReferrals(userId);
    
    return directReferrals.length > 0 ? networkSize / directReferrals.length : 1;
  }

  private async calculateCulturalDiversityImpact(userId: string): Promise<number> {
    // Simplified cultural diversity calculation
    return Math.random() * 100;
  }

  private async calculateLongTermImpact(userId: string): Promise<number> {
    // Simplified long-term impact calculation
    return Math.random() * 100;
  }

  private async isUserActive(userId: string): Promise<boolean> {
    // Simplified activity check
    return Math.random() > 0.3;
  }

  private async getInvitationAchievements(
    userId: string,
    metrics: {
      invitationStats: InvitationStats;
      referralStats: ReferralStats;
      networkGrowth: NetworkGrowthStats;
      invitationQuality: InvitationQualityMetrics;
      communityImpact: CommunityImpactMetrics;
    }
  ): Promise<InvitationAchievement[]> {
    const achievements: InvitationAchievement[] = [];
    
    if (metrics.invitationStats.totalInvitesSent >= 10) {
      achievements.push({
        id: 'inviter_bronze',
        name: 'Bronze Inviter',
        description: 'Sent 10+ invitations',
        category: 'invitation_volume',
        earnedAt: new Date()
      });
    }
    
    if (metrics.referralStats.referralRetentionRate >= 0.8) {
      achievements.push({
        id: 'retention_master',
        name: 'Retention Master',
        description: '80%+ referral retention rate',
        category: 'quality',
        earnedAt: new Date()
      });
    }
    
    return achievements;
  }

  // Additional helper methods for pattern analysis

  private async getTemporalInvitationPatterns(
    userId: string,
    timeWindow: { startDate: Date; endDate: Date }
  ): Promise<TemporalInvitationPattern> {
    // Simplified temporal pattern analysis
    return {
      peakInvitationHours: [14, 15, 16],
      peakInvitationDays: [1, 2, 3], // Monday, Tuesday, Wednesday
      seasonalTrends: 'stable',
      invitationFrequency: 'weekly'
    };
  }

  private async getServerInvitationPatterns(
    userId: string,
    timeWindow: { startDate: Date; endDate: Date }
  ): Promise<ServerInvitationPattern> {
    const invites = await this.db
      .select({
        serverId: ServerInviteSchema.serverId,
        uses: ServerInviteSchema.uses
      })
      .from(ServerInviteSchema)
      .where(
        and(
          eq(ServerInviteSchema.createdById, userId),
          between(ServerInviteSchema.createdAt, timeWindow.startDate, timeWindow.endDate)
        )
      );

    const serverStats = invites.reduce((acc, invite) => {
      const serverId = invite.serverId || 'unknown';
      if (!acc[serverId]) {
        acc[serverId] = { inviteCount: 0, totalUses: 0 };
      }
      acc[serverId].inviteCount++;
      acc[serverId].totalUses += Number(invite.uses);
      return acc;
    }, {} as Record<string, { inviteCount: number; totalUses: number }>);

    return {
      serverDistribution: Object.entries(serverStats).map(([serverId, stats]) => ({
        serverId,
        inviteCount: stats.inviteCount,
        successRate: stats.inviteCount > 0 ? stats.totalUses / stats.inviteCount : 0
      })),
      preferredServers: Object.entries(serverStats)
        .sort(([,a], [,b]) => b.inviteCount - a.inviteCount)
        .slice(0, 3)
        .map(([serverId]) => serverId),
      diversificationScore: Object.keys(serverStats).length
    };
  }

  private async getInvitationSuccessPatterns(
    userId: string,
    timeWindow: { startDate: Date; endDate: Date }
  ): Promise<InvitationSuccessPattern> {
    // Simplified success pattern analysis
    return {
      successRateByTimeOfDay: [
        { hour: 14, successRate: 0.8 },
        { hour: 15, successRate: 0.7 },
        { hour: 16, successRate: 0.6 }
      ],
      successRateByDayOfWeek: [
        { day: 1, successRate: 0.7 },
        { day: 2, successRate: 0.8 },
        { day: 3, successRate: 0.6 }
      ],
      optimalInvitationTiming: '2-4 PM on weekdays',
      factorsInfluencingSuccess: ['timing', 'server_type', 'personal_connection']
    };
  }

  private async getNetworkEffectPatterns(
    userId: string,
    timeWindow: { startDate: Date; endDate: Date }
  ): Promise<NetworkEffectPattern> {
    // Simplified network effect analysis
    return {
      viralCoefficient: 1.2,
      networkGrowthRate: 0.15,
      influenceRadius: 3,
      cascadeEffects: [
        { generation: 1, size: 10 },
        { generation: 2, size: 12 },
        { generation: 3, size: 8 }
      ]
    };
  }

  private generateInvitationInsights(patterns: {
    temporalPatterns: TemporalInvitationPattern;
    serverPatterns: ServerInvitationPattern;
    successPatterns: InvitationSuccessPattern;
    networkEffects: NetworkEffectPattern;
  }): string[] {
    const insights: string[] = [];
    
    if (patterns.successPatterns.optimalInvitationTiming) {
      insights.push(`Optimal invitation timing: ${patterns.successPatterns.optimalInvitationTiming}`);
    }
    
    if (patterns.networkEffects.viralCoefficient > 1) {
      insights.push('Your invitations create positive viral growth');
    }
    
    return insights;
  }

  private generateInvitationRecommendations(patterns: {
    temporalPatterns: TemporalInvitationPattern;
    serverPatterns: ServerInvitationPattern;
    successPatterns: InvitationSuccessPattern;
    networkEffects: NetworkEffectPattern;
  }): string[] {
    const recommendations: string[] = [];
    
    if (patterns.serverPatterns.diversificationScore < 3) {
      recommendations.push('Consider inviting users to more diverse servers');
    }
    
    if (patterns.networkEffects.viralCoefficient < 1) {
      recommendations.push('Focus on inviting users who are likely to invite others');
    }
    
    return recommendations;
  }

  // Community building impact methods

  private async getDirectCommunityImpact(userId: string): Promise<DirectCommunityImpact> {
    const referrals = await this.getDirectReferrals(userId);
    
    return {
      usersReferred: referrals.length,
      serversGrown: await this.calculateServerDiversification(userId),
      immediateNetworkExpansion: referrals.length,
      directEngagementIncrease: referrals.length * 0.1 // Simplified
    };
  }

  private async getIndirectCommunityImpact(userId: string): Promise<IndirectCommunityImpact> {
    const networkSize = await this.getReferralNetworkSize(userId);
    const directReferrals = await this.getDirectReferrals(userId);
    
    return {
      secondaryReferrals: networkSize - directReferrals.length,
      networkMultiplierEffect: networkSize / Math.max(directReferrals.length, 1),
      communityConnectionsCreated: networkSize * 2, // Simplified
      crossServerConnections: Math.floor(networkSize * 0.3)
    };
  }

  private async getRetentionImpact(userId: string): Promise<RetentionImpact> {
    const referrals = await this.getDirectReferrals(userId);
    const activeReferrals = await this.getActiveReferrals(userId);
    
    return {
      referralRetentionRate: referrals.length > 0 ? activeReferrals.length / referrals.length : 0,
      averageReferralLifespan: await this.getAverageReferralLifespan(userId),
      longTermEngagementScore: Math.random() * 100, // Simplified
      communityStabilityContribution: activeReferrals.length * 0.1
    };
  }

  private async getDiversityImpact(userId: string): Promise<DiversityImpact> {
    return {
      culturalDiversityScore: await this.calculateCulturalDiversityImpact(userId),
      geographicReach: Math.floor(Math.random() * 10) + 1,
      demographicBridging: Math.random() * 100,
      inclusivityContribution: Math.random() * 100
    };
  }

  private async getEngagementImpact(userId: string): Promise<EngagementImpact> {
    const referrals = await this.getDirectReferrals(userId);
    
    return {
      totalEngagementIncrease: referrals.length * 10, // Simplified
      averageReferralActivity: await this.getReferralEngagementScore(userId),
      communityParticipationBoost: referrals.length * 0.05,
      contentCreationIncrease: referrals.length * 2
    };
  }

  private calculateCommunityImpactScore(impact: {
    directImpact: DirectCommunityImpact;
    indirectImpact: IndirectCommunityImpact;
    retentionImpact: RetentionImpact;
    diversityImpact: DiversityImpact;
    engagementImpact: EngagementImpact;
  }): number {
    const {
      directImpact,
      indirectImpact,
      retentionImpact,
      diversityImpact,
      engagementImpact
    } = impact;

    // Weighted scoring
    const directScore = Math.min(directImpact.usersReferred * 10, 100) * 0.25;
    const indirectScore = Math.min(indirectImpact.networkMultiplierEffect * 50, 100) * 0.2;
    const retentionScore = Math.min(retentionImpact.referralRetentionRate * 100, 100) * 0.25;
    const diversityScore = Math.min(diversityImpact.culturalDiversityScore, 100) * 0.15;
    const engagementScore = Math.min(engagementImpact.averageReferralActivity, 100) * 0.15;

    return directScore + indirectScore + retentionScore + diversityScore + engagementScore;
  }

  private categorizeImpactLevel(score: number): 'low' | 'medium' | 'high' | 'exceptional' {
    if (score >= 80) return 'exceptional';
    if (score >= 60) return 'high';
    if (score >= 30) return 'medium';
    return 'low';
  }

  private async checkRecognitionEligibility(userId: string, impactScore: number): Promise<RecognitionEligibility> {
    const metrics = await this.trackUserInvitationMetrics(userId);
    
    return {
      communityBuilder: impactScore >= 70 && metrics.referralStats.directReferrals >= 20,
      networkExpander: metrics.referralStats.networkSize >= 50,
      retentionChampion: metrics.referralStats.referralRetentionRate >= 0.8,
      diversityAdvocate: impactScore >= 60 && metrics.communityImpact.culturalDiversityImpact >= 70,
      invitationMaster: metrics.invitationStats.successRate >= 0.7 && metrics.invitationStats.totalInvitesSent >= 50
    };
  }
}

// Type definitions for invitation and referral tracking

export interface InvitationMetrics {
  userId: string;
  invitationStats: InvitationStats;
  referralStats: ReferralStats;
  networkGrowth: NetworkGrowthStats;
  invitationQuality: InvitationQualityMetrics;
  communityImpact: CommunityImpactMetrics;
  overallScore: number;
  achievements: InvitationAchievement[];
  lastUpdated: Date;
}

export interface InvitationStats {
  totalInvitesSent: number;
  totalInvitesUsed: number;
  totalInviteCapacity: number;
  activeInvites: number;
  successRate: number;
  utilizationRate: number;
}

export interface ReferralStats {
  directReferrals: number;
  activeReferrals: number;
  referralRetentionRate: number;
  networkSize: number;
  averageReferralLifespan: number;
  referralEngagementScore: number;
}

export interface NetworkGrowthStats {
  monthlyGrowth: Array<{ month: string; growth: number }>;
  growthTrend: 'increasing' | 'decreasing' | 'stable';
  peakGrowthMonth: string;
  consistencyScore: number;
  accelerationFactor: number;
}

export interface InvitationQualityMetrics {
  qualityScore: number;
  highQualityReferrals: number;
  averageReferralEngagement: number;
  retentionQuality: number;
  contributionQuality: number;
}

export interface CommunityImpactMetrics {
  communityGrowthContribution: number;
  serverDiversification: number;
  networkEffectMultiplier: number;
  culturalDiversityImpact: number;
  longTermImpactScore: number;
}

export interface ReferralChain {
  referralId: string;
  referrerId: string;
  depth: number;
  joinedAt: Date;
  serverId: string;
  isActive: boolean;
  engagementScore: number;
  subReferrals: ReferralChain[];
}

export interface InvitationLeaderboardEntry {
  userId: string;
  username: string;
  rank: number;
  totalInvitesSent: number;
  totalInvitesUsed: number;
  successRate: number;
  overallScore: number;
  achievements: InvitationAchievement[];
  accountAge: number;
}

export interface InvitationAchievement {
  id: string;
  name: string;
  description: string;
  category: string;
  earnedAt: Date;
}

export interface InvitationPatternAnalysis {
  userId: string;
  timeWindow: { startDate: Date; endDate: Date };
  temporalPatterns: TemporalInvitationPattern;
  serverPatterns: ServerInvitationPattern;
  successPatterns: InvitationSuccessPattern;
  networkEffects: NetworkEffectPattern;
  insights: string[];
  recommendations: string[];
}

export interface TemporalInvitationPattern {
  peakInvitationHours: number[];
  peakInvitationDays: number[];
  seasonalTrends: string;
  invitationFrequency: string;
}

export interface ServerInvitationPattern {
  serverDistribution: Array<{
    serverId: string;
    inviteCount: number;
    successRate: number;
  }>;
  preferredServers: string[];
  diversificationScore: number;
}

export interface InvitationSuccessPattern {
  successRateByTimeOfDay: Array<{ hour: number; successRate: number }>;
  successRateByDayOfWeek: Array<{ day: number; successRate: number }>;
  optimalInvitationTiming: string;
  factorsInfluencingSuccess: string[];
}

export interface NetworkEffectPattern {
  viralCoefficient: number;
  networkGrowthRate: number;
  influenceRadius: number;
  cascadeEffects: Array<{ generation: number; size: number }>;
}

export interface CommunityBuildingImpact {
  userId: string;
  directImpact: DirectCommunityImpact;
  indirectImpact: IndirectCommunityImpact;
  retentionImpact: RetentionImpact;
  diversityImpact: DiversityImpact;
  engagementImpact: EngagementImpact;
  totalImpactScore: number;
  impactLevel: 'low' | 'medium' | 'high' | 'exceptional';
  recognitionEligibility: RecognitionEligibility;
}

export interface DirectCommunityImpact {
  usersReferred: number;
  serversGrown: number;
  immediateNetworkExpansion: number;
  directEngagementIncrease: number;
}

export interface IndirectCommunityImpact {
  secondaryReferrals: number;
  networkMultiplierEffect: number;
  communityConnectionsCreated: number;
  crossServerConnections: number;
}

export interface RetentionImpact {
  referralRetentionRate: number;
  averageReferralLifespan: number;
  longTermEngagementScore: number;
  communityStabilityContribution: number;
}

export interface DiversityImpact {
  culturalDiversityScore: number;
  geographicReach: number;
  demographicBridging: number;
  inclusivityContribution: number;
}

export interface EngagementImpact {
  totalEngagementIncrease: number;
  averageReferralActivity: number;
  communityParticipationBoost: number;
  contentCreationIncrease: number;
}

export interface RecognitionEligibility {
  communityBuilder: boolean;
  networkExpander: boolean;
  retentionChampion: boolean;
  diversityAdvocate: boolean;
  invitationMaster: boolean;
}