import type { drizzle } from "drizzle-orm/postgres-js";
import { eq, and, desc, asc, count, sql } from "drizzle-orm";
import {
  BadgeCollectionSchema,
  BadgeTypeSchema,
  UserBadgeSchema,
  UserCollectionProgressSchema,
  UserSchema
} from "../db/schema";
import {
  BadgeNotFoundError,
  BadgeValidationError,
  InsufficientPermissionsError
} from "../class/badge-errors";
import {
  validateCreateBadgeCollection,
  validateUpdateBadgeCollection,
  validateBadgeCollectionFilters
} from "../utils/badge-validation-enhanced";
import { badgeWebSocketService } from "../utils/badge-websocket";
import type {
  BadgeCollection,
  UserCollectionProgress,
  CreateBadgeCollectionRequest,
  UpdateBadgeCollectionRequest,
  BadgeCollectionFilters,
  CollectionProgress,
  CompletionReward,
  BadgeType,
  UserBadge
} from "../types/badge.types";

/**
 * Badge Collection Service - Manages progressive badge collections and user progress
 * Handles collection CRUD operations, progress tracking, and completion rewards
 */
export class BadgeCollectionService {
  constructor(private db: ReturnType<typeof drizzle>) {}

  // Collection Management (CRUD Operations)

  /**
   * Creates a new badge collection with validation
   */
  async createCollection(
    collectionData: CreateBadgeCollectionRequest,
    createdBy: string
  ): Promise<BadgeCollection> {
    try {
      // Validate input data
      const validatedData = validateCreateBadgeCollection(collectionData);

      // Check admin permission
      await this.validateAdminPermission(createdBy);

      // Check for duplicate collection IDs
      const existingCollection = await this.db
        .select()
        .from(BadgeCollectionSchema)
        .where(eq(BadgeCollectionSchema.collectionId, validatedData.collectionId))
        .limit(1);

      if (existingCollection.length > 0) {
        throw new BadgeValidationError(`Collection with ID "${validatedData.collectionId}" already exists`);
      }

      // Create the collection
      const [newCollection] = await this.db
        .insert(BadgeCollectionSchema)
        .values({
          collectionId: validatedData.collectionId,
          name: validatedData.name,
          description: validatedData.description,
          type: validatedData.type,
          unlockedBy: validatedData.unlockedBy,
          completionReward: validatedData.completionReward ? JSON.stringify(validatedData.completionReward) : null,
          totalBadges: 0, // Will be updated when badges are added
          isActive: true
        })
        .returning();

      return this.formatCollectionResponse(newCollection);
    } catch (error) {
      if (error instanceof BadgeValidationError || error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error creating badge collection:", error);
      throw new BadgeValidationError("Failed to create badge collection");
    }
  }

  /**
   * Updates an existing badge collection
   */
  async updateCollection(
    collectionId: string,
    updates: UpdateBadgeCollectionRequest,
    updatedBy: string
  ): Promise<BadgeCollection> {
    try {
      // Validate input data
      const validatedUpdates = validateUpdateBadgeCollection(updates);

      // Check admin permission
      await this.validateAdminPermission(updatedBy);

      // Check if collection exists
      const existingCollection = await this.getCollectionById(collectionId);

      // Prepare update data
      const updateData: any = {};
      if (validatedUpdates.name !== undefined) updateData.name = validatedUpdates.name;
      if (validatedUpdates.description !== undefined) updateData.description = validatedUpdates.description;
      if (validatedUpdates.type !== undefined) updateData.type = validatedUpdates.type;
      if (validatedUpdates.unlockedBy !== undefined) updateData.unlockedBy = validatedUpdates.unlockedBy;
      if (validatedUpdates.completionReward !== undefined) {
        updateData.completionReward = validatedUpdates.completionReward ? 
          JSON.stringify(validatedUpdates.completionReward) : null;
      }
      if (validatedUpdates.isActive !== undefined) updateData.isActive = validatedUpdates.isActive;

      updateData.updatedAt = new Date();

      // Update the collection
      const [updatedCollection] = await this.db
        .update(BadgeCollectionSchema)
        .set(updateData)
        .where(eq(BadgeCollectionSchema.id, existingCollection.id))
        .returning();

      return this.formatCollectionResponse(updatedCollection);
    } catch (error) {
      if (error instanceof BadgeNotFoundError || 
          error instanceof BadgeValidationError || 
          error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error updating badge collection:", error);
      throw new BadgeValidationError("Failed to update badge collection");
    }
  }

  /**
   * Deletes a badge collection and handles cleanup
   */
  async deleteCollection(collectionId: string, deletedBy: string): Promise<void> {
    try {
      // Check admin permission
      await this.validateAdminPermission(deletedBy);

      // Check if collection exists
      const collection = await this.getCollectionById(collectionId);

      // Check if collection has badges
      const badgesInCollection = await this.db
        .select({ count: count() })
        .from(BadgeTypeSchema)
        .where(eq(BadgeTypeSchema.collectionId, collection.id));

      if (badgesInCollection[0]?.count > 0) {
        throw new BadgeValidationError("Cannot delete collection that contains badges. Remove badges first.");
      }

      // Delete the collection (this will cascade to user progress records)
      await this.db
        .delete(BadgeCollectionSchema)
        .where(eq(BadgeCollectionSchema.id, collection.id));

    } catch (error) {
      if (error instanceof BadgeNotFoundError || 
          error instanceof BadgeValidationError || 
          error instanceof InsufficientPermissionsError) {
        throw error;
      }
      console.error("Error deleting badge collection:", error);
      throw new BadgeValidationError("Failed to delete badge collection");
    }
  }

  /**
   * Gets all badge collections with optional filtering
   */
  async getCollections(
    filters?: BadgeCollectionFilters,
    limit: number = 50,
    offset: number = 0
  ): Promise<BadgeCollection[]> {
    try {
      const validatedFilters = filters ? validateBadgeCollectionFilters(filters) : undefined;

      let query = this.db.select().from(BadgeCollectionSchema);

      // Apply filters
      if (validatedFilters) {
        const conditions = [];
        
        if (validatedFilters.type) {
          conditions.push(eq(BadgeCollectionSchema.type, validatedFilters.type));
        }
        
        if (validatedFilters.isActive !== undefined) {
          conditions.push(eq(BadgeCollectionSchema.isActive, validatedFilters.isActive));
        }
        
        if (validatedFilters.search) {
          conditions.push(
            sql`${BadgeCollectionSchema.name} ILIKE ${`%${validatedFilters.search}%`} OR ${BadgeCollectionSchema.description} ILIKE ${`%${validatedFilters.search}%`}`
          );
        }

        if (conditions.length > 0) {
          query = query.where(and(...conditions));
        }
      }

      const collections = await query
        .orderBy(asc(BadgeCollectionSchema.name))
        .limit(limit)
        .offset(offset);

      return collections.map(collection => this.formatCollectionResponse(collection));
    } catch (error) {
      console.error("Error getting badge collections:", error);
      throw new BadgeValidationError("Failed to get badge collections");
    }
  }

  /**
   * Gets a specific badge collection by ID
   */
  async getCollectionById(collectionId: string): Promise<BadgeCollection> {
    try {
      const [collection] = await this.db
        .select()
        .from(BadgeCollectionSchema)
        .where(eq(BadgeCollectionSchema.collectionId, collectionId))
        .limit(1);

      if (!collection) {
        throw new BadgeNotFoundError(`Collection not found: ${collectionId}`);
      }

      return this.formatCollectionResponse(collection);
    } catch (error) {
      if (error instanceof BadgeNotFoundError) {
        throw error;
      }
      console.error("Error getting badge collection by ID:", error);
      throw new BadgeValidationError("Failed to get badge collection");
    }
  }

  // Collection Progress Tracking

  /**
   * Gets user's progress in a specific collection
   */
  async getUserCollectionProgress(
    userId: string,
    collectionId: string
  ): Promise<UserCollectionProgress | null> {
    try {
      const collection = await this.getCollectionById(collectionId);

      const [progress] = await this.db
        .select()
        .from(UserCollectionProgressSchema)
        .where(
          and(
            eq(UserCollectionProgressSchema.userId, userId),
            eq(UserCollectionProgressSchema.collectionId, collection.id)
          )
        )
        .limit(1);

      if (!progress) {
        return null;
      }

      return {
        id: progress.id,
        userId: progress.userId,
        collectionId: progress.collectionId,
        collection,
        badgesEarned: progress.badgesEarned,
        totalBadges: progress.totalBadges,
        isCompleted: progress.isCompleted,
        completionDate: progress.completionDate,
        completionRewardGranted: progress.completionRewardGranted,
        createdAt: progress.createdAt,
        updatedAt: progress.updatedAt
      };
    } catch (error) {
      if (error instanceof BadgeNotFoundError) {
        throw error;
      }
      console.error("Error getting user collection progress:", error);
      throw new BadgeValidationError("Failed to get collection progress");
    }
  }

  /**
   * Gets all collection progress for a user
   */
  async getUserCollectionProgressAll(userId: string): Promise<UserCollectionProgress[]> {
    try {
      const progressRecords = await this.db
        .select({
          progress: UserCollectionProgressSchema,
          collection: BadgeCollectionSchema
        })
        .from(UserCollectionProgressSchema)
        .innerJoin(
          BadgeCollectionSchema,
          eq(UserCollectionProgressSchema.collectionId, BadgeCollectionSchema.id)
        )
        .where(eq(UserCollectionProgressSchema.userId, userId))
        .orderBy(desc(UserCollectionProgressSchema.updatedAt));

      return progressRecords.map(record => ({
        id: record.progress.id,
        userId: record.progress.userId,
        collectionId: record.progress.collectionId,
        collection: this.formatCollectionResponse(record.collection),
        badgesEarned: record.progress.badgesEarned,
        totalBadges: record.progress.totalBadges,
        isCompleted: record.progress.isCompleted,
        completionDate: record.progress.completionDate,
        completionRewardGranted: record.progress.completionRewardGranted,
        createdAt: record.progress.createdAt,
        updatedAt: record.progress.updatedAt
      }));
    } catch (error) {
      console.error("Error getting user collection progress:", error);
      throw new BadgeValidationError("Failed to get user collection progress");
    }
  }

  /**
   * Updates user's progress in a collection
   */
  async updateUserCollectionProgress(
    userId: string,
    collectionId: string,
    badgeEarned?: boolean
  ): Promise<UserCollectionProgress> {
    try {
      const collection = await this.getCollectionById(collectionId);

      // Get current progress or create new record
      let progress = await this.getUserCollectionProgress(userId, collectionId);

      if (!progress) {
        // Create new progress record
        const [newProgress] = await this.db
          .insert(UserCollectionProgressSchema)
          .values({
            userId,
            collectionId: collection.id,
            badgesEarned: badgeEarned ? 1 : 0,
            totalBadges: collection.totalBadges,
            isCompleted: false,
            completionRewardGranted: false
          })
          .returning();

        progress = {
          id: newProgress.id,
          userId: newProgress.userId,
          collectionId: newProgress.collectionId,
          collection,
          badgesEarned: newProgress.badgesEarned,
          totalBadges: newProgress.totalBadges,
          isCompleted: newProgress.isCompleted,
          completionDate: newProgress.completionDate,
          completionRewardGranted: newProgress.completionRewardGranted,
          createdAt: newProgress.createdAt,
          updatedAt: newProgress.updatedAt
        };
      } else if (badgeEarned) {
        // Update existing progress
        const newBadgesEarned = progress.badgesEarned + 1;
        const isCompleted = newBadgesEarned >= collection.totalBadges;

        const [updatedProgress] = await this.db
          .update(UserCollectionProgressSchema)
          .set({
            badgesEarned: newBadgesEarned,
            totalBadges: collection.totalBadges,
            isCompleted,
            completionDate: isCompleted ? new Date() : null,
            updatedAt: new Date()
          })
          .where(eq(UserCollectionProgressSchema.id, progress.id))
          .returning();

        progress = {
          ...progress,
          badgesEarned: updatedProgress.badgesEarned,
          totalBadges: updatedProgress.totalBadges,
          isCompleted: updatedProgress.isCompleted,
          completionDate: updatedProgress.completionDate,
          updatedAt: updatedProgress.updatedAt
        };

        // Check for collection completion and award completion reward
        if (isCompleted && !progress.completionRewardGranted) {
          await this.handleCollectionCompletion(userId, collection, progress);
        }
      }

      return progress;
    } catch (error) {
      if (error instanceof BadgeNotFoundError) {
        throw error;
      }
      console.error("Error updating user collection progress:", error);
      throw new BadgeValidationError("Failed to update collection progress");
    }
  }

  // Sequential Badge Unlocking

  /**
   * Checks if a user can unlock the next badge in a collection
   */
  async canUnlockNextBadge(
    userId: string,
    collectionId: string,
    badgeDisplayOrder: number
  ): Promise<boolean> {
    try {
      const collection = await this.getCollectionById(collectionId);

      // For standalone collections, all badges can be unlocked independently
      if (collection.type === 'standalone') {
        return true;
      }

      // For progressive collections, check sequential unlocking
      if (badgeDisplayOrder === 0) {
        // First badge can always be unlocked
        return true;
      }

      // Check if user has the previous badge in the sequence
      const previousBadges = await this.db
        .select({
          badge: BadgeTypeSchema,
          userBadge: UserBadgeSchema
        })
        .from(BadgeTypeSchema)
        .leftJoin(
          UserBadgeSchema,
          and(
            eq(UserBadgeSchema.badgeTypeId, BadgeTypeSchema.id),
            eq(UserBadgeSchema.userId, userId)
          )
        )
        .where(
          and(
            eq(BadgeTypeSchema.collectionId, collection.id),
            sql`${BadgeTypeSchema.displayOrder} < ${badgeDisplayOrder}`
          )
        )
        .orderBy(asc(BadgeTypeSchema.displayOrder));

      // Check if all previous badges are earned
      for (const record of previousBadges) {
        if (!record.userBadge) {
          return false; // User is missing a previous badge
        }
      }

      return true;
    } catch (error) {
      if (error instanceof BadgeNotFoundError) {
        throw error;
      }
      console.error("Error checking badge unlock eligibility:", error);
      return false;
    }
  }

  /**
   * Gets the next badge a user can unlock in a collection
   */
  async getNextUnlockableBadge(
    userId: string,
    collectionId: string
  ): Promise<BadgeType | null> {
    try {
      const collection = await this.getCollectionById(collectionId);

      // Get all badges in the collection with user's progress
      const badgesWithProgress = await this.db
        .select({
          badge: BadgeTypeSchema,
          userBadge: UserBadgeSchema
        })
        .from(BadgeTypeSchema)
        .leftJoin(
          UserBadgeSchema,
          and(
            eq(UserBadgeSchema.badgeTypeId, BadgeTypeSchema.id),
            eq(UserBadgeSchema.userId, userId)
          )
        )
        .where(
          and(
            eq(BadgeTypeSchema.collectionId, collection.id),
            eq(BadgeTypeSchema.isActive, true)
          )
        )
        .orderBy(asc(BadgeTypeSchema.displayOrder));

      // For standalone collections, return any unearned badge
      if (collection.type === 'standalone') {
        const unearnedBadge = badgesWithProgress.find(record => !record.userBadge);
        return unearnedBadge ? this.formatBadgeTypeResponse(unearnedBadge.badge) : null;
      }

      // For progressive collections, find the next sequential badge
      for (const record of badgesWithProgress) {
        if (!record.userBadge) {
          // Check if this badge can be unlocked
          const canUnlock = await this.canUnlockNextBadge(
            userId,
            collectionId,
            record.badge.displayOrder
          );
          
          if (canUnlock) {
            return this.formatBadgeTypeResponse(record.badge);
          } else {
            // If this badge can't be unlocked, no further badges can be either
            break;
          }
        }
      }

      return null;
    } catch (error) {
      if (error instanceof BadgeNotFoundError) {
        throw error;
      }
      console.error("Error getting next unlockable badge:", error);
      return null;
    }
  }

  // Collection Completion Detection and Rewards

  /**
   * Handles collection completion and awards completion reward
   */
  private async handleCollectionCompletion(
    userId: string,
    collection: BadgeCollection,
    progress: UserCollectionProgress
  ): Promise<void> {
    try {
      if (!collection.completionReward) {
        return;
      }

      // Mark completion reward as granted
      await this.db
        .update(UserCollectionProgressSchema)
        .set({
          completionRewardGranted: true,
          updatedAt: new Date()
        })
        .where(eq(UserCollectionProgressSchema.id, progress.id));

      // Create completion reward badge if specified
      const completionReward = collection.completionReward;
      if (completionReward.badge) {
        // This would require integration with the badge service
        // For now, we'll emit a WebSocket event for completion
        await badgeWebSocketService.broadcastCollectionCompleted({
          userId,
          collection,
          completionReward
        });
      }

      console.log(`Collection "${collection.name}" completed by user ${userId}`);
    } catch (error) {
      console.error("Error handling collection completion:", error);
      // Don't throw error to avoid breaking the main flow
    }
  }

  /**
   * Checks if a collection is completed by a user
   */
  async isCollectionCompleted(userId: string, collectionId: string): Promise<boolean> {
    try {
      const progress = await this.getUserCollectionProgress(userId, collectionId);
      return progress?.isCompleted || false;
    } catch (error) {
      console.error("Error checking collection completion:", error);
      return false;
    }
  }

  // Collection Display and Progress Visualization

  /**
   * Gets detailed collection progress with badge information
   */
  async getDetailedCollectionProgress(
    userId: string,
    collectionId: string
  ): Promise<CollectionProgress> {
    try {
      const collection = await this.getCollectionById(collectionId);
      const progress = await this.getUserCollectionProgress(userId, collectionId);

      // Get earned badges in this collection
      const earnedBadges = await this.db
        .select({
          userBadge: UserBadgeSchema,
          badgeType: BadgeTypeSchema
        })
        .from(UserBadgeSchema)
        .innerJoin(BadgeTypeSchema, eq(UserBadgeSchema.badgeTypeId, BadgeTypeSchema.id))
        .where(
          and(
            eq(UserBadgeSchema.userId, userId),
            eq(UserBadgeSchema.collectionId, collection.id)
          )
        )
        .orderBy(asc(BadgeTypeSchema.displayOrder));

      // Get next unlockable badge
      const nextBadge = await this.getNextUnlockableBadge(userId, collectionId);

      const formattedEarnedBadges: UserBadge[] = earnedBadges.map(record => ({
        id: record.userBadge.id,
        userId: record.userBadge.userId,
        badgeTypeId: record.userBadge.badgeTypeId,
        collectionId: record.userBadge.collectionId,
        assignedBy: record.userBadge.assignedBy,
        assignedAt: record.userBadge.assignedAt,
        isVisible: record.userBadge.isVisible,
        progressData: record.userBadge.progressData ? JSON.parse(record.userBadge.progressData) : undefined,
        perksGranted: record.userBadge.perksGranted ? JSON.parse(record.userBadge.perksGranted) : undefined,
        badgeType: this.formatBadgeTypeResponse(record.badgeType)
      }));

      return {
        collection,
        progress: progress || {
          id: '',
          userId,
          collectionId: collection.id,
          collection,
          badgesEarned: 0,
          totalBadges: collection.totalBadges,
          isCompleted: false,
          completionRewardGranted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        earnedBadges: formattedEarnedBadges,
        nextBadge,
        completionReward: collection.completionReward
      };
    } catch (error) {
      if (error instanceof BadgeNotFoundError) {
        throw error;
      }
      console.error("Error getting detailed collection progress:", error);
      throw new BadgeValidationError("Failed to get detailed collection progress");
    }
  }

  // Collection-based Badge Assignment Logic

  /**
   * Assigns a badge with collection dependency checking
   */
  async assignBadgeWithCollectionCheck(
    userId: string,
    badgeTypeId: string,
    assignedBy?: string
  ): Promise<{ success: boolean; error?: string; progress?: UserCollectionProgress }> {
    try {
      // Get badge type information
      const [badgeType] = await this.db
        .select()
        .from(BadgeTypeSchema)
        .where(eq(BadgeTypeSchema.id, badgeTypeId))
        .limit(1);

      if (!badgeType) {
        return { success: false, error: "Badge type not found" };
      }

      // If badge is part of a collection, check dependencies
      if (badgeType.collectionId) {
        const collection = await this.db
          .select()
          .from(BadgeCollectionSchema)
          .where(eq(BadgeCollectionSchema.id, badgeType.collectionId))
          .limit(1);

        if (collection.length > 0) {
          const canUnlock = await this.canUnlockNextBadge(
            userId,
            collection[0].collectionId,
            badgeType.displayOrder
          );

          if (!canUnlock) {
            return { 
              success: false, 
              error: "Previous badges in collection must be earned first" 
            };
          }
        }
      }

      // Badge can be assigned - update collection progress
      let progress: UserCollectionProgress | undefined;
      if (badgeType.collectionId) {
        const collection = await this.db
          .select()
          .from(BadgeCollectionSchema)
          .where(eq(BadgeCollectionSchema.id, badgeType.collectionId))
          .limit(1);

        if (collection.length > 0) {
          progress = await this.updateUserCollectionProgress(
            userId,
            collection[0].collectionId,
            true
          );
        }
      }

      return { success: true, progress };
    } catch (error) {
      console.error("Error in collection-based badge assignment:", error);
      return { success: false, error: "Failed to check collection dependencies" };
    }
  }

  // Utility Methods

  /**
   * Updates the total badge count for a collection
   */
  async updateCollectionBadgeCount(collectionId: string): Promise<void> {
    try {
      const collection = await this.getCollectionById(collectionId);

      const [badgeCount] = await this.db
        .select({ count: count() })
        .from(BadgeTypeSchema)
        .where(
          and(
            eq(BadgeTypeSchema.collectionId, collection.id),
            eq(BadgeTypeSchema.isActive, true)
          )
        );

      await this.db
        .update(BadgeCollectionSchema)
        .set({
          totalBadges: badgeCount.count,
          updatedAt: new Date()
        })
        .where(eq(BadgeCollectionSchema.id, collection.id));

    } catch (error) {
      console.error("Error updating collection badge count:", error);
      // Don't throw error to avoid breaking other operations
    }
  }

  /**
   * Formats collection response from database record
   */
  private formatCollectionResponse(collection: any): BadgeCollection {
    return {
      id: collection.id,
      collectionId: collection.collectionId,
      name: collection.name,
      description: collection.description,
      type: collection.type,
      totalBadges: collection.totalBadges,
      unlockedBy: collection.unlockedBy,
      completionReward: collection.completionReward ? JSON.parse(collection.completionReward) : undefined,
      isActive: collection.isActive,
      createdAt: collection.createdAt,
      updatedAt: collection.updatedAt
    };
  }

  /**
   * Formats badge type response from database record
   */
  private formatBadgeTypeResponse(badgeType: any): BadgeType {
    return {
      id: badgeType.id,
      collectionId: badgeType.collectionId,
      badgeId: badgeType.badgeId,
      name: badgeType.name,
      title: badgeType.title,
      description: badgeType.description,
      icon: badgeType.icon,
      tooltip: badgeType.tooltip,
      design: JSON.parse(badgeType.design),
      criteria: JSON.parse(badgeType.criteria),
      perks: badgeType.perks ? JSON.parse(badgeType.perks) : undefined,
      unlockType: badgeType.unlockType,
      visualDescription: badgeType.visualDescription,
      animation: badgeType.animation,
      displayOrder: badgeType.displayOrder,
      category: badgeType.category,
      isActive: badgeType.isActive,
      createdAt: badgeType.createdAt,
      updatedAt: badgeType.updatedAt
    };
  }

  /**
   * Validates admin permission for collection management operations
   */
  private async validateAdminPermission(userId: string): Promise<void> {
    // TODO: Implement proper admin permission checking
    // For now, we'll assume all operations are allowed
    // In production, you would check user roles/permissions
    return;
  }
}