import type { ServerWebSocket } from "bun";
import { VoiceWebSocketManager } from "../manager/websocket";
import {
  parseVoiceMessage,
  validateVoiceMetadata,
} from "../utils/binaryProtocol";

// WebRTC event types for SFU
export const SFUEventTypes = {
  SFU_JOIN: "sfu_join",
  SFU_LEAVE: "sfu_leave",
  SFU_OFFER: "sfu_offer",
  SFU_ANSWER: "sfu_answer",
  SFU_ICE_CANDIDATE: "sfu_ice_candidate",
  SFU_STREAM_ADDED: "sfu_stream_added",
  SFU_STREAM_REMOVED: "sfu_stream_removed",
  SFU_MUTE_TOGGLE: "sfu_mute_toggle",
  SFU_DEAFEN_TOGGLE: "sfu_deafen_toggle",
  SFU_SPEAKING_STATE: "sfu_speaking_state",
  SFU_CONNECTION_STATE: "sfu_connection_state",
  SFU_ERROR: "sfu_error",
  SFU_VOICE_DATA: "voice_data",
};

// STUN/TURN server configuration
export const stunTurnConfig = {
  iceServers: [
    {
      urls: [
        "stun:stun.l.google.com:19302", // Public STUN server (Google)
        "stun:stun1.l.google.com:19302",
        "stun:stun2.l.google.com:19302",
      ],
    },
    {
      urls: [
        "turn:coolify.berkormanli.dev:3478", // Your TURN server
      ],
      credential: "mypassword",
      username: "myuser",
    },
  ],
  iceCandidatePoolSize: 10,
};

// Interface for custom WebSocket data
interface CustomWebSocketData {
  userId: string;
  serverId?: string;
  channelId?: string;
  user?: {
    id: string;
    name: string;
    avatar: string;
    speaking: boolean;
    muted: boolean;
    deafened: boolean;
  };
  token: string;
  isAuthenticated?: boolean;
  isAlive: boolean;
  type?: string;
}

// Interface for SFU client connection
interface SFUClientConnection {
  userId: string;
  peerConnection: any; // In a real implementation, this would be RTCPeerConnection
  audioTransceiver: any; // In a real implementation, this would be RTCRtpTransceiver
  dataChannel: any; // In a real implementation, this would be RTCDataChannel
  serverId: string;
  channelId: string;
  isMuted: boolean;
  isDeafened: boolean;
  isSpeaking: boolean;
  lastActivity: number;
  connectionState: string;
  streams: Map<string, any>; // Map of streams this client is receiving
  speakingTimeout?: any; // Timeout for speaking state
}

/**
 * Selective Forwarding Unit (SFU) Service
 *
 * Handles WebRTC connections for voice chat in a client-server-client model:
 * 1. Each client connects to the SFU server
 * 2. The SFU server forwards audio streams between clients
 * 3. This reduces client complexity and improves reliability
 */
export class SFUService {
  private voiceWsManager: VoiceWebSocketManager;
  private clientConnections: Map<string, SFUClientConnection> = new Map();
  private channelClients: Map<string, Set<string>> = new Map(); // Map of channelId -> Set of userIds

  constructor(voiceWsManager: VoiceWebSocketManager) {
    this.voiceWsManager = voiceWsManager;
  }

  /**
   * Handle a client joining a voice channel
   *
   * @param ws WebSocket connection of the joining user
   * @param message Join message data
   */
  async handleJoin(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { serverId, channelId, userId, user } = message.data;

    if (!serverId || !channelId || !userId) {
      this.sendError(ws, "Invalid join message format");
      return;
    }

    // Update WebSocket data
    ws.data.serverId = serverId;
    ws.data.channelId = channelId;
    ws.data.user = user;

    // Add user to voice channel
    this.voiceWsManager.add(ws);

    // Create a channel key
    const channelKey = `${serverId}:${channelId}`;

    // Initialize channel clients set if it doesn't exist
    if (!this.channelClients.has(channelKey)) {
      this.channelClients.set(channelKey, new Set<string>());
    }

    // Add user to channel clients
    this.channelClients.get(channelKey)?.add(userId);

    // Create a client connection object
    const clientConnection: SFUClientConnection = {
      userId,
      peerConnection: null, // Will be created during signaling
      audioTransceiver: null,
      dataChannel: null,
      serverId,
      channelId,
      isMuted: user?.muted || false,
      isDeafened: user?.deafened || false,
      isSpeaking: false,
      lastActivity: Date.now(),
      connectionState: "new",
      streams: new Map(),
    };

    // Store the client connection
    this.clientConnections.set(userId, clientConnection);

    // Send SFU join acknowledgment
    this.sendToClient(ws, {
      type: SFUEventTypes.SFU_JOIN,
      data: {
        success: true,
        channelId,
        serverId,
        stunTurnConfig,
      },
    });

    // Notify all users in the channel about the new user
    this.broadcastToChannel(
      channelKey,
      {
        type: SFUEventTypes.SFU_STREAM_ADDED,
        data: {
          userId,
          user,
          timestamp: Date.now(),
        },
      },
      userId,
    ); // Exclude the joining user

    // Send the list of existing users to the new user
    const existingUsers = this.getChannelUsers(channelKey, userId);

    if (existingUsers.length > 0) {
      this.sendToClient(ws, {
        type: "channel_user_list",
        data: {
          users: existingUsers,
        },
      });
    }

    console.log(
      `User ${userId} joined voice channel ${channelId} in server ${serverId} (SFU mode)`,
    );
  }

  /**
   * Handle a client leaving a voice channel
   *
   * @param ws WebSocket connection of the leaving user
   * @param message Leave message data
   */
  handleLeave(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { serverId, channelId } = message.data || ws.data;
    const userId = ws.data.userId;

    if (!serverId || !channelId || !userId) {
      this.sendError(ws, "Invalid leave message format");
      return;
    }

    // Create a channel key
    const channelKey = `${serverId}:${channelId}`;

    // Remove user from channel clients
    this.channelClients.get(channelKey)?.delete(userId);

    // Clean up empty channel
    if (this.channelClients.get(channelKey)?.size === 0) {
      this.channelClients.delete(channelKey);
    }

    // Clean up client connection
    const clientConnection = this.clientConnections.get(userId);
    if (clientConnection) {
      // Close peer connection if it exists
      if (clientConnection.peerConnection) {
        // In a real implementation, we would close the RTCPeerConnection
        // clientConnection.peerConnection.close();
      }

      this.clientConnections.delete(userId);
    }

    // Remove user from voice channel
    this.voiceWsManager.remove(ws);

    // Notify all users in the channel about the leaving user
    this.broadcastToChannel(channelKey, {
      type: SFUEventTypes.SFU_STREAM_REMOVED,
      data: {
        userId,
        timestamp: Date.now(),
      },
    });

    console.log(
      `User ${userId} left voice channel ${channelId} in server ${serverId} (SFU mode)`,
    );
  }

  /**
   * Handle WebRTC offer from a client
   *
   * @param ws WebSocket connection of the offering user
   * @param message Offer message data
   */
  async handleOffer(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { sdp } = message.data;
    const userId = ws.data.userId;

    if (!sdp || !userId) {
      this.sendError(ws, "Invalid offer message format");
      return;
    }

    const clientConnection = this.clientConnections.get(userId);
    if (!clientConnection) {
      this.sendError(ws, "Client connection not found");
      return;
    }

    // In a real implementation, we would:
    // 1. Create an RTCPeerConnection if it doesn't exist
    // 2. Set the remote description using the offer
    // 3. Create an answer
    // 4. Set the local description
    // 5. Send the answer back to the client

    // For now, we'll simulate this process

    // Send a simulated answer back to the client
    this.sendToClient(ws, {
      type: SFUEventTypes.SFU_ANSWER,
      data: {
        sdp: "simulated_sdp_answer",
        userId: "server",
      },
    });

    // Update connection state
    clientConnection.connectionState = "connected";

    console.log(`Processed WebRTC offer from user ${userId}`);
  }

  /**
   * Handle ICE candidate from a client
   *
   * @param ws WebSocket connection of the user sending the ICE candidate
   * @param message ICE candidate message data
   */
  handleIceCandidate(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { candidate } = message.data;
    const userId = ws.data.userId;

    if (!candidate || !userId) {
      this.sendError(ws, "Invalid ICE candidate message format");
      return;
    }

    const clientConnection = this.clientConnections.get(userId);
    if (!clientConnection) {
      this.sendError(ws, "Client connection not found");
      return;
    }

    // In a real implementation, we would add the ICE candidate to the RTCPeerConnection
    // clientConnection.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));

    // For now, we'll just log it
    console.log(`Received ICE candidate from user ${userId}`);
  }

  /**
   * Handle mute toggle from a client
   *
   * @param ws WebSocket connection of the user toggling mute
   * @param message Mute toggle message data
   */
  handleMuteToggle(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { isMuted } = message.data;
    const userId = ws.data.userId;

    if (isMuted === undefined || !userId) {
      this.sendError(ws, "Invalid mute toggle message format");
      return;
    }

    const clientConnection = this.clientConnections.get(userId);
    if (!clientConnection) {
      this.sendError(ws, "Client connection not found");
      return;
    }

    // Update client connection state
    clientConnection.isMuted = isMuted;

    // Update user data in WebSocket
    if (ws.data.user) {
      ws.data.user.muted = isMuted;
    }

    // Broadcast mute state to all users in the channel
    const channelKey = `${clientConnection.serverId}:${clientConnection.channelId}`;
    this.broadcastToChannel(channelKey, {
      type: SFUEventTypes.SFU_MUTE_TOGGLE,
      data: {
        userId,
        isMuted,
        timestamp: Date.now(),
      },
    });

    console.log(`User ${userId} ${isMuted ? "muted" : "unmuted"} themselves`);
  }

  /**
   * Handle deafen toggle from a client
   *
   * @param ws WebSocket connection of the user toggling deafen
   * @param message Deafen toggle message data
   */
  handleDeafenToggle(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { isDeafened } = message.data;
    const userId = ws.data.userId;

    if (isDeafened === undefined || !userId) {
      this.sendError(ws, "Invalid deafen toggle message format");
      return;
    }

    const clientConnection = this.clientConnections.get(userId);
    if (!clientConnection) {
      this.sendError(ws, "Client connection not found");
      return;
    }

    // Update client connection state
    clientConnection.isDeafened = isDeafened;

    // Update user data in WebSocket
    if (ws.data.user) {
      ws.data.user.deafened = isDeafened;
    }

    // Broadcast deafen state to all users in the channel
    const channelKey = `${clientConnection.serverId}:${clientConnection.channelId}`;
    this.broadcastToChannel(channelKey, {
      type: SFUEventTypes.SFU_DEAFEN_TOGGLE,
      data: {
        userId,
        isDeafened,
        timestamp: Date.now(),
      },
    });

    console.log(
      `User ${userId} ${isDeafened ? "deafened" : "undeafened"} themselves`,
    );
  }

  /**
   * Handle speaking state change from a client
   *
   * @param ws WebSocket connection of the user changing speaking state
   * @param message Speaking state message data
   */
  handleSpeakingState(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    const { isSpeaking } = message.data;
    const userId = ws.data.userId;

    if (isSpeaking === undefined || !userId) {
      this.sendError(ws, "Invalid speaking state message format");
      return;
    }

    const clientConnection = this.clientConnections.get(userId);
    if (!clientConnection) {
      this.sendError(ws, "Client connection not found");
      return;
    }

    // Update client connection state
    clientConnection.isSpeaking = isSpeaking;
    clientConnection.lastActivity = Date.now();

    // Update user data in WebSocket
    if (ws.data.user) {
      ws.data.user.speaking = isSpeaking;
    }

    // Broadcast speaking state to all users in the channel
    const channelKey = `${clientConnection.serverId}:${clientConnection.channelId}`;
    this.broadcastToChannel(channelKey, {
      type: SFUEventTypes.SFU_SPEAKING_STATE,
      data: {
        userId,
        isSpeaking,
        timestamp: Date.now(),
      },
    });

    // Only log speaking start to reduce noise
    if (isSpeaking) {
      console.log(`User ${userId} started speaking`);
    }
  }

  /**
   * Handle binary voice data from a client
   *
   * @param ws WebSocket connection of the user sending voice data
   * @param binaryData Binary voice data
   */
  handleVoiceData(
    ws: ServerWebSocket<CustomWebSocketData>,
    binaryData: ArrayBuffer,
  ) {
    try {
      // Parse the binary message
      const { metadata, audioData } = parseVoiceMessage(binaryData);

      // Validate metadata
      if (!validateVoiceMetadata(metadata)) {
        console.error("Invalid voice data metadata");
        return;
      }

      const { userId, timestamp, sequence } = metadata;

      // Ensure the user ID matches the WebSocket user ID
      if (userId !== ws.data.userId) {
        console.error("User ID mismatch in voice data");
        return;
      }

      // Get the channel key
      const serverId = ws.data.serverId;
      const channelId = ws.data.channelId;

      if (!serverId || !channelId) {
        console.error("Missing serverId or channelId for voice data");
        return;
      }

      const channelKey = `${serverId}:${channelId}`;

      // Check if the user is muted
      const clientConnection = this.clientConnections.get(userId);
      if (clientConnection?.isMuted) {
        // User is muted, don't forward their audio
        return;
      }

      // Forward the voice data to all other users in the channel
      const channelSockets =
        this.voiceWsManager.getSocketsInChannel(channelKey);

      if (!channelSockets) {
        return;
      }

      // Forward the original binary message to all other users
      channelSockets.forEach((socket) => {
        if (socket !== ws && socket.readyState === WebSocket.OPEN) {
          socket.send(binaryData);
        }
      });

      // Update speaking state if needed
      if (!clientConnection?.isSpeaking) {
        // User started speaking
        if (clientConnection) {
          clientConnection.isSpeaking = true;
          clientConnection.lastActivity = Date.now();

          // Update user data in WebSocket
          if (ws.data.user) {
            ws.data.user.speaking = true;
          }

          // Broadcast speaking state
          this.broadcastToChannel(channelKey, {
            type: SFUEventTypes.SFU_SPEAKING_STATE,
            data: {
              userId,
              isSpeaking: true,
              timestamp: Date.now(),
            },
          });
        }
      }

      // Set a timeout to mark the user as not speaking after a period of silence
      if (clientConnection) {
        if (clientConnection.speakingTimeout) {
          clearTimeout(clientConnection.speakingTimeout);
        }

        clientConnection.speakingTimeout = setTimeout(() => {
          if (clientConnection.isSpeaking) {
            clientConnection.isSpeaking = false;

            // Update user data in WebSocket
            const userSocket = this.voiceWsManager.getSocketByUserIdAndTopic(
              userId,
              channelKey,
            );
            if (userSocket?.data.user) {
              userSocket.data.user.speaking = false;
            }

            // Broadcast speaking state
            this.broadcastToChannel(channelKey, {
              type: SFUEventTypes.SFU_SPEAKING_STATE,
              data: {
                userId,
                isSpeaking: false,
                timestamp: Date.now(),
              },
            });
          }
        }, 500); // 500ms of silence before marking as not speaking
      }
    } catch (error) {
      console.error("Error handling voice data:", error);
    }
  }

  /**
   * Send a message to a client
   *
   * @param ws WebSocket connection of the client
   * @param message Message to send
   */
  private sendToClient(ws: ServerWebSocket<CustomWebSocketData>, message: any) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  /**
   * Send an error message to a client
   *
   * @param ws WebSocket connection of the client
   * @param errorMessage Error message
   */
  private sendError(
    ws: ServerWebSocket<CustomWebSocketData>,
    errorMessage: string,
  ) {
    this.sendToClient(ws, {
      type: SFUEventTypes.SFU_ERROR,
      data: {
        error: errorMessage,
        timestamp: Date.now(),
      },
    });

    console.error(`SFU error for user ${ws.data.userId}: ${errorMessage}`);
  }

  /**
   * Broadcast a message to all clients in a channel
   *
   * @param channelKey Channel key (serverId:channelId)
   * @param message Message to broadcast
   * @param excludeUserId User ID to exclude from the broadcast (optional)
   */
  private broadcastToChannel(
    channelKey: string,
    message: any,
    excludeUserId?: string,
  ) {
    const channelSockets = this.voiceWsManager.getSocketsInChannel(channelKey);

    if (!channelSockets) {
      console.warn(`No sockets found for channel ${channelKey}`);
      return;
    }

    const messageStr = JSON.stringify(message);

    channelSockets.forEach((socket) => {
      if (
        socket.readyState === WebSocket.OPEN &&
        (!excludeUserId || socket.data.userId !== excludeUserId)
      ) {
        socket.send(messageStr);
      }
    });
  }

  /**
   * Get all users in a channel
   *
   * @param channelKey Channel key (serverId:channelId)
   * @param excludeUserId User ID to exclude from the result (optional)
   * @returns Array of user objects
   */
  private getChannelUsers(channelKey: string, excludeUserId?: string): any[] {
    const channelSockets = this.voiceWsManager.getSocketsInChannel(channelKey);

    if (!channelSockets) {
      return [];
    }

    return Array.from(channelSockets)
      .filter(
        (socket) =>
          socket.data.user &&
          (!excludeUserId || socket.data.userId !== excludeUserId),
      )
      .map((socket) => ({
        userId: socket.data.userId,
        ...socket.data.user,
        isMuted: socket.data.user?.muted || false,
        isDeafened: socket.data.user?.deafened || false,
        isSpeaking: socket.data.user?.speaking || false,
      }));
  }

  /**
   * Get a client connection by user ID
   *
   * @param userId User ID
   * @returns Client connection or undefined if not found
   */
  getClientConnection(userId: string): SFUClientConnection | undefined {
    return this.clientConnections.get(userId);
  }

  /**
   * Get all users in a voice channel
   *
   * @param serverId Server ID
   * @param channelId Channel ID
   * @returns Array of user objects
   */
  getUsersInVoiceChannel(serverId: string, channelId: string): any[] {
    const channelKey = `${serverId}:${channelId}`;
    return this.getChannelUsers(channelKey);
  }

  /**
   * Check if a user is in a voice channel
   *
   * @param userId User ID
   * @param serverId Server ID
   * @param channelId Channel ID
   * @returns True if the user is in the channel, false otherwise
   */
  isUserInVoiceChannel(
    userId: string,
    serverId: string,
    channelId: string,
  ): boolean {
    const channelKey = `${serverId}:${channelId}`;
    const channelUsers = this.channelClients.get(channelKey);

    if (!channelUsers) {
      return false;
    }

    return channelUsers.has(userId);
  }
}
