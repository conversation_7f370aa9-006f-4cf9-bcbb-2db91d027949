# Database - Postgres (Development)
POSTGRES_DB=oba_test
POSTGRES_PASSWORD=metadata_password
POSTGRES_PORT=5432
POSTGRES_SCHEMA=postgres
POSTGRES_USERNAME=metadata_user

# For local development, use localhost instead of Docker container name
POSTGRES_HOST=localhost

# JWT Token (Development - shorter expiration times for testing)
JWT_SECRET_KEY=dev_jwtsecret
JWT_SUBJECT=dev_jwtsubject
JWT_TOKEN_PREFIX=oba
JWT_ALGORITHM=HS256
JWT_MIN=15
JWT_HOUR=1
JWT_DAY=1

# Hash Functions
HASHING_ALGORITHM_LAYER_1=bcrypt
HASHING_ALGORITHM_LAYER_2=argon2
HASHING_SALT=dev_saltysalt

# Development-specific settings
NODE_ENV=development
DEBUG=true
LOG_LEVEL=debug

# STUN/TURN Server (Development)
STUN_SERVER_URL=stun:stun.l.google.com:19302
TURN_SERVER_URL=turn:localhost:3478
TURN_SERVER_USERNAME=devuser
TURN_SERVER_CREDENTIAL=devpassword
