// <PERSON>ript to check database connection
import postgres from "postgres";

// Use the same connection parameters as in your application
const POSTGRES_SCHEMA = process.env.POSTGRES_SCHEMA || "postgresql";
const POSTGRES_USERNAME = process.env.POSTGRES_USERNAME || "berk";
const POSTGRES_PASSWORD = process.env.POSTGRES_PASSWORD || "obadatabase";
const POSTGRES_HOST = process.env.POSTGRES_HOST || "database";
const POSTGRES_PORT = process.env.POSTGRES_PORT || "5432";
const POSTGRES_DB = process.env.POSTGRES_DB || "oba_db";

// Construct database URL
const DATABASE_URL = `${POSTGRES_SCHEMA}://${POSTGRES_USERNAME}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}`;
console.log("Attempting to connect to database with URL:", DATABASE_URL);

async function checkConnection() {
  let client;
  try {
    // Create postgres client
    client = postgres(DATABASE_URL, {
      onnotice: () => {}, // Suppress notice messages
      max: 1, // Use only one connection for this test
      idle_timeout: 5, // Close idle connections after 5 seconds
      connect_timeout: 10, // Timeout after 10 seconds
    });

    console.log("Database client created, testing connection...");

    // Test the connection by executing a simple query
    const result = await client.unsafe("SELECT 1 as connection_test");
    console.log("Connection successful! Result:", result);

    // Try to get database version
    const versionResult = await client.unsafe("SELECT version()");
    console.log("Database version:", versionResult[0].version);

    return true;
  } catch (error) {
    console.error("Connection failed:", error);
    return false;
  } finally {
    // Close the client connection
    if (client) {
      await client.end();
      console.log("Database connection closed");
    }
  }
}

// Run the connection test
checkConnection()
  .then((success) => {
    if (success) {
      console.log("Database connection test completed successfully");
      process.exit(0);
    } else {
      console.error("Database connection test failed");
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error("Unexpected error during connection test:", error);
    process.exit(1);
  });
