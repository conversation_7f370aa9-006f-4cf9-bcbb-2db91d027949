// Test file to verify WebSocket standardization types
import type {
  IWebSocketMeta,
  IWebSocketTarget,
  IWebSocketSuccessMessage,
  IWebSocketErrorMessage,
  IWebSocketEventMessage,
  IEnhancedWebSocketData,
} from "./types/websocket-standardization.types";

import { WebSocketErrorCode } from "./types/websocket-standardization.types";

// Test basic message structure
const testMeta: IWebSocketMeta = {
  timestamp: new Date(),
  id: "test-123",
  correlationId: "corr-456",
  version: "1.0.0",
  source: "server",
  traceId: "trace-789",
  priority: "normal",
  ttl: 300,
};

const testTarget: IWebSocketTarget = {
  userId: "user-123",
  channelId: "channel-456",
  serverId: "server-789",
  topic: "messages",
  excludeUserId: "user-999",
};

// Test success message
const successMessage: IWebSocketSuccessMessage<{ content: string }> = {
  type: "MESSAGE_SENT",
  success: true,
  data: { content: "Hello World" },
  meta: testMeta,
  target: testTarget,
  message: "Message sent successfully",
};

// Test error message
const errorMessage: IWebSocketErrorMessage = {
  type: "ERROR",
  success: false,
  error: {
    code: WebSocketErrorCode.PERMISSION_DENIED,
    message: "Access denied",
    details: {
      field: "channelId",
      expectedFormat: "valid channel ID",
      receivedValue: "invalid-id",
    },
  },
  meta: testMeta,
};

// Test event message
const eventMessage: IWebSocketEventMessage<{ userId: string; status: string }> =
  {
    type: "USER_STATUS_CHANGED",
    event: "status_change",
    data: { userId: "user-123", status: "online" },
    meta: testMeta,
    category: "user",
    severity: "info",
  };

// Test enhanced WebSocket data
const enhancedData: Partial<IEnhancedWebSocketData> = {
  subscriptions: new Set(["channel-123", "server-456"]),
  lastHeartbeat: Date.now(),
  messageCount: 42,
  rateLimitTokens: 100,
  correlationMap: new Map(),
  connectionMetrics: {
    latency: 50,
    packetsLost: 0,
    bytesReceived: 1024,
    bytesSent: 2048,
  },
  capabilities: {
    supportsCompression: true,
    supportsBinaryProtocol: true,
    maxMessageSize: 65536,
    protocolVersion: "1.0.0",
  },
};

console.log("WebSocket standardization types test passed!");
console.log("Success message:", JSON.stringify(successMessage, null, 2));
console.log("Error message:", JSON.stringify(errorMessage, null, 2));
console.log("Event message:", JSON.stringify(eventMessage, null, 2));
console.log("Enhanced data:", JSON.stringify(enhancedData, null, 2));
