#!/usr/bin/env bun

/**
 * Quick test to verify Task 7 endpoints are working
 */

const BASE_URL = "http://localhost:3005";

async function quickTest() {
  console.log("🧪 Quick Task 7 Endpoint Test\n");

  // Test public endpoints that don't require auth
  const publicTests = [
    { name: "Badge Stats", url: `${BASE_URL}/api/badges/stats` },
    { name: "Badge Leaderboard", url: `${BASE_URL}/api/badges/leaderboard` }
  ];

  for (const test of publicTests) {
    try {
      console.log(`Testing ${test.name}...`);
      const response = await fetch(test.url);
      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   ✅ Success - Response has ${Object.keys(data).length} keys`);
      } else {
        console.log(`   ❌ Failed`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error}`);
    }
    console.log();
  }

  // Test protected endpoints (expect 401 without auth)
  const protectedTests = [
    { name: "Available Badges", url: `${BASE_URL}/api/badges/available` },
    { name: "Badge Evaluation", url: `${BASE_URL}/api/badges/evaluate/01234567-89ab-cdef-0123-456789abcdef`, method: "POST" },
    { name: "Badge Removal", url: `${BASE_URL}/api/users/01234567-89ab-cdef-0123-456789abcdef/badges/fedcba98-7654-3210-fedc-ba9876543210`, method: "DELETE" }
  ];

  for (const test of protectedTests) {
    try {
      console.log(`Testing ${test.name} (expecting 401)...`);
      const response = await fetch(test.url, { method: test.method || "GET" });
      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.status === 401) {
        console.log(`   ✅ Correctly protected (401 Unauthorized)`);
      } else {
        console.log(`   ⚠️  Unexpected status (expected 401)`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error}`);
    }
    console.log();
  }

  console.log("🎯 Quick test completed!");
}

if (import.meta.main) {
  quickTest().catch(console.error);
}