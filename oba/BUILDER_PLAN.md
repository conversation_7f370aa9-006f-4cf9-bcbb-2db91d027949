# Oba Backend - Builder Agent Plan

This document outlines a detailed, step-by-step plan for a builder agent to implement the key improvements identified in the initial project analysis. The tasks are prioritized based on their impact on security, maintainability, and developer experience.

## Task 1: Secure Application Endpoints

**Objective:** Convert all public routes that handle sensitive data or perform mutations to protected routes that require authentication.

**Priority:** Critical

### Steps:

1.  **Analyze `index.ts`:** Read the `index.ts` file to identify all route definitions.

2.  **Identify Insecure Routes:** Review each route. Any route that creates, updates, or deletes data, or retrieves user-specific information, should be protected. The following is a non-exhaustive list of routes that should be converted from `PublicRouteHandler` to `ProtectedRouteHandler`:
    - `/api/updateUserProfile`
    - `/api/messages/edit`
    - `/api/messages/delete`
    - `/api/createServerInvite`
    - All routes related to friends, status, channels, categories, roles, and server members.

3.  **Modify the Routes:** For each identified route, change the `PublicRouteHandler` to a `ProtectedRouteHandler`. The `authMiddleware` will be applied automatically.

    **Example:**

    _Before:_

    ```typescript
    router.add(
      "/api/messages/edit",
      new PublicRouteHandler(editMessageHandler, [loggerMiddleware]),
    );
    ```

    _After:_

    ```typescript
    router.add(
      "/api/messages/edit",
      new ProtectedRouteHandler(editMessageHandler, [
        loggerMiddleware,
        authMiddleware,
      ]),
    );
    ```

## Task 2: Refactor `index.ts` for Modularity

**Objective:** Improve maintainability by breaking down the monolithic `index.ts` file into smaller, feature-specific route modules.

**Priority:** High

### Steps:

1.  **Create a `routes` directory:** Inside the `src` directory (or root if `src` doesn't exist), create a new directory named `routes`.

2.  **Create Route Files:** For each major feature, create a new file in the `routes` directory (e.g., `auth.routes.ts`, `messages.routes.ts`, `servers.routes.ts`).

3.  **Move Route Definitions:** Cut the route definitions from `index.ts` and paste them into the corresponding new route files. Each file should export a configured router instance.

4.  **Update `index.ts`:** Import the routers from the new route files and register them with the main application router.

## Task 3: Implement Consistent Input Validation

**Objective:** Enhance security and data integrity by adding `zod` validation to all API endpoints.

**Priority:** High

### Steps:

1.  **Identify Handlers:** Go through each file in the `handlers/` directory.

2.  **Create Zod Schemas:** For each handler that receives data (from the request body, query parameters, or URL parameters), define a `zod` schema that validates the data's shape and type.

3.  **Apply Validation:** In each handler, use the schema to parse and validate the incoming data. If validation fails, return a 400 Bad Request response with the validation errors.

## Task 4: Correct the `Dockerfile` and Establish CI/CD

**Objective:** Create a correct and efficient Docker build process and automate testing and builds with a CI/CD pipeline.

**Priority:** Medium

### Steps:

1.  **Correct the `Dockerfile`:** Replace the content of the existing `Dockerfile` with a more efficient, multi-stage build process.

2.  **Create a CI/CD Workflow:**
    - Create a `.github/workflows` directory.
    - Inside it, create a `ci.yml` file with a GitHub Actions workflow that:
      - Checks out the code.
      - Sets up Bun.
      - Installs dependencies.
      - Runs linting and tests.
      - Builds the Docker image (optional).

## Task 5: Generate API Documentation

**Objective:** Create comprehensive API documentation to improve the developer experience.

**Priority:** Medium

### Steps:

1.  **Install Dependencies:**

    ```bash
    bun add drizzle-zod zod-to-openapi
    ```

2.  **Generate Zod Schemas:** Use `drizzle-zod` to generate Zod schemas from your Drizzle schemas.

3.  **Generate OpenAPI Specification:** Use `zod-to-openapi` to create an OpenAPI specification from your Zod schemas and route definitions.

4.  **Serve the Documentation:** Use a tool like Swagger UI to serve the generated OpenAPI specification as interactive API documentation.
