# Stage 1: Build
FROM oven/bun:alpine AS build

ARG APP_DIR=/app

WORKDIR ${APP_DIR}

COPY package.json .
COPY .npmrc .
RUN bun install

COPY . .

# Stage 2: Runtime
FROM oven/bun:alpine

ARG APP_DIR=/app

WORKDIR ${APP_DIR}

COPY --from=build ${APP_DIR}/node_modules ./node_modules
#COPY --from=build ${APP_DIR}/public ./public
COPY --from=build ${APP_DIR}/backups ./backups
COPY --from=build ${APP_DIR}/class ./class
COPY --from=build ${APP_DIR}/constants ./constants
COPY --from=build ${APP_DIR}/db ./db
COPY --from=build ${APP_DIR}/handlers ./handlers
COPY --from=build ${APP_DIR}/manager ./manager
COPY --from=build ${APP_DIR}/middlewares ./middlewares
COPY --from=build ${APP_DIR}/routes ./routes
COPY --from=build ${APP_DIR}/services ./services
COPY --from=build ${APP_DIR}/tests ./tests
COPY --from=build ${APP_DIR}/types ./types
COPY --from=build ${APP_DIR}/utils ./utils
COPY --from=build ${APP_DIR}/types.ts ./types.ts
COPY --from=build ${APP_DIR}/utils.ts ./utils.ts
COPY --from=build ${APP_DIR}/drizzle.config.ts ./drizzle.config.ts
COPY --from=build ${APP_DIR}/index.ts .

CMD ["bun", "drizzle-kit", "pull"]
CMD ["bun", "drizzle-kit", "generate"]
CMD ["bun", "drizzle-kit", "migrate"]

CMD ["bun", "run", "--watch", "index.ts"]