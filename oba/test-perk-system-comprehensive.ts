#!/usr/bin/env bun

/**
 * Comprehensive Perk System Test Script
 * 
 * This script tests the complete perk and benefits system implementation:
 * - Perk service functionality
 * - Badge-perk integration
 * - API endpoints
 * - Permission validation
 * - WebSocket events
 * - Role integration
 */

import { db } from "./db";
import { BadgeService } from "./services/badge.service";
import { PerkService } from "./services/perk.service";
import { 
  UserSchema, 
  BadgeTypeSchema, 
  UserBadgeSchema,
  ServerSchema,
  ServerRoleSchema,
  UserRoles
} from "./db/schema";
import { eq, and } from "drizzle-orm";
import type { BadgeType } from "./types/badge.types";

// Test configuration
const TEST_CONFIG = {
  cleanup: true,
  verbose: true,
  testWebSocket: false // Set to true to test WebSocket events
};

// Test data
const testData = {
  user: {
    id: "perk-test-user-123",
    username: "perktestuser",
    email: "<EMAIL>",
    password: "hashedpassword"
  },
  server: {
    id: "perk-test-server-123",
    name: "Perk Test Server",
    ownerId: "perk-test-user-123"
  },
  roles: [
    {
      id: "perk-test-role-mod-123",
      serverId: "perk-test-server-123",
      name: "Moderator",
      permissions: BigInt(16) // MANAGE_ROLES
    },
    {
      id: "perk-test-role-vip-123",
      serverId: "perk-test-server-123",
      name: "VIP",
      permissions: BigInt(512) // VIEW_CHANNEL
    }
  ],
  badgeTypes: [
    {
      id: "perk-test-badge-mod-123",
      badgeId: "moderator_badge",
      name: "Moderator Badge",
      description: "Badge for server moderators",
      icon: "🛡️",
      design: JSON.stringify({ 
        shape: "shield", 
        background: "blue", 
        colors: ["#0066CC"] 
      }),
      criteria: JSON.stringify({ 
        requirement: "manual", 
        tracked: "manual" 
      }),
      perks: JSON.stringify([
        "role:Moderator",
        "cosmetic:mod_glow",
        "access:mod_lounge"
      ]),
      unlockType: "manual" as const,
      displayOrder: 1,
      category: "role" as const,
      isActive: true
    },
    {
      id: "perk-test-badge-vip-123",
      badgeId: "vip_badge",
      name: "VIP Badge",
      description: "Badge for VIP members",
      icon: "💎",
      design: JSON.stringify({ 
        shape: "diamond", 
        background: "purple", 
        colors: ["#8A2BE2"] 
      }),
      criteria: JSON.stringify({ 
        requirement: "manual", 
        tracked: "manual" 
      }),
      perks: JSON.stringify([
        {
          type: "role_assignment",
          name: "VIP Role",
          description: "Assigns VIP role",
          value: "VIP",
          serverId: "perk-test-server-123"
        },
        {
          type: "cosmetic_feature",
          name: "VIP Sparkle",
          description: "Adds sparkle effect to profile",
          value: "vip_sparkle"
        },
        {
          type: "access_privilege",
          name: "VIP Lounge Access",
          description: "Access to VIP lounge",
          value: "vip_lounge"
        }
      ]),
      unlockType: "manual" as const,
      displayOrder: 2,
      category: "special" as const,
      isActive: true
    },
    {
      id: "perk-test-badge-complex-123",
      badgeId: "complex_badge",
      name: "Complex Perk Badge",
      description: "Badge with complex perk configuration",
      icon: "⚡",
      design: JSON.stringify({ 
        shape: "star", 
        background: "gold", 
        colors: ["#FFD700", "#FFA500"] 
      }),
      criteria: JSON.stringify({ 
        requirement: "automatic", 
        tracked: "message_count",
        threshold: 1000
      }),
      perks: JSON.stringify([
        "permission:MANAGE_MESSAGES",
        "cosmetic:lightning_effect",
        "access:premium_features",
        "custom:special_status"
      ]),
      unlockType: "automatic" as const,
      displayOrder: 3,
      category: "achievement" as const,
      isActive: true
    }
  ]
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  errors: [] as string[]
};

// Utility functions
function log(message: string, level: 'info' | 'success' | 'error' | 'warn' = 'info') {
  if (!TEST_CONFIG.verbose && level === 'info') return;
  
  const colors = {
    info: '\x1b[36m',    // Cyan
    success: '\x1b[32m', // Green
    error: '\x1b[31m',   // Red
    warn: '\x1b[33m',    // Yellow
    reset: '\x1b[0m'
  };
  
  console.log(`${colors[level]}[${level.toUpperCase()}] ${message}${colors.reset}`);
}

function assert(condition: boolean, message: string) {
  if (condition) {
    testResults.passed++;
    log(`✓ ${message}`, 'success');
  } else {
    testResults.failed++;
    testResults.errors.push(message);
    log(`✗ ${message}`, 'error');
  }
}

async function setupTestData() {
  log("Setting up test data...");
  
  try {
    // Clean up existing test data
    await cleanupTestData();
    
    // Insert test user
    await db.insert(UserSchema).values(testData.user);
    log("Created test user");
    
    // Insert test server
    await db.insert(ServerSchema).values(testData.server);
    log("Created test server");
    
    // Insert test roles
    for (const role of testData.roles) {
      await db.insert(ServerRoleSchema).values(role);
    }
    log(`Created ${testData.roles.length} test roles`);
    
    // Insert test badge types
    for (const badgeType of testData.badgeTypes) {
      await db.insert(BadgeTypeSchema).values(badgeType);
    }
    log(`Created ${testData.badgeTypes.length} test badge types`);
    
    log("Test data setup complete", 'success');
  } catch (error) {
    log(`Error setting up test data: ${error}`, 'error');
    throw error;
  }
}

async function cleanupTestData() {
  log("Cleaning up test data...");
  
  try {
    // Delete in reverse dependency order
    await db.delete(UserBadgeSchema).where(eq(UserBadgeSchema.userId, testData.user.id));
    await db.delete(UserRoles).where(eq(UserRoles.userId, testData.user.id));
    
    for (const badgeType of testData.badgeTypes) {
      await db.delete(BadgeTypeSchema).where(eq(BadgeTypeSchema.id, badgeType.id));
    }
    
    for (const role of testData.roles) {
      await db.delete(ServerRoleSchema).where(eq(ServerRoleSchema.id, role.id));
    }
    
    await db.delete(ServerSchema).where(eq(ServerSchema.id, testData.server.id));
    await db.delete(UserSchema).where(eq(UserSchema.id, testData.user.id));
    
    log("Test data cleanup complete");
  } catch (error) {
    log(`Error cleaning up test data: ${error}`, 'warn');
  }
}

async function testPerkService() {
  log("\n=== Testing Perk Service ===");
  
  const perkService = new PerkService(db);
  
  // Test 1: Parse string perks
  log("Testing string perk parsing...");
  try {
    const rolePerk = perkService['parseStringPerk']("role:Moderator", "test_id");
    assert(rolePerk.type === "role_assignment", "Role perk type should be role_assignment");
    assert(rolePerk.value === "Moderator", "Role perk value should be Moderator");
    
    const permissionPerk = perkService['parseStringPerk']("permission:MANAGE_MESSAGES", "test_id");
    assert(permissionPerk.type === "permission_grant", "Permission perk type should be permission_grant");
    assert(Array.isArray(permissionPerk.permissions), "Permission perk should have permissions array");
    
    const cosmeticPerk = perkService['parseStringPerk']("cosmetic:glow", "test_id");
    assert(cosmeticPerk.type === "cosmetic_feature", "Cosmetic perk type should be cosmetic_feature");
    
    log("String perk parsing tests passed", 'success');
  } catch (error) {
    log(`String perk parsing test failed: ${error}`, 'error');
    testResults.failed++;
  }
  
  // Test 2: Parse object perks
  log("Testing object perk parsing...");
  try {
    const objectPerk = {
      type: "role_assignment",
      name: "Test Role",
      description: "Test role perk",
      value: "TestRole"
    };
    
    const parsed = perkService['parseObjectPerk'](objectPerk, "test_id");
    assert(parsed.type === "role_assignment", "Object perk type should be preserved");
    assert(parsed.name === "Test Role", "Object perk name should be preserved");
    
    log("Object perk parsing tests passed", 'success');
  } catch (error) {
    log(`Object perk parsing test failed: ${error}`, 'error');
    testResults.failed++;
  }
  
  // Test 3: Validate perks
  log("Testing perk validation...");
  try {
    const validPerk = {
      id: "test",
      type: "cosmetic_feature" as const,
      name: "Test Cosmetic",
      description: "Test",
      value: "test_cosmetic",
      isActive: true
    };
    
    const validation = await perkService.validatePerk(validPerk, testData.user.id, testData.server.id);
    assert(validation.isValid, "Valid perk should pass validation");
    assert(validation.errors.length === 0, "Valid perk should have no errors");
    
    log("Perk validation tests passed", 'success');
  } catch (error) {
    log(`Perk validation test failed: ${error}`, 'error');
    testResults.failed++;
  }
  
  // Test 4: Get available perks
  log("Testing available perks retrieval...");
  try {
    const availablePerks = await perkService.getAvailablePerks(testData.server.id);
    assert(Array.isArray(availablePerks), "Available perks should be an array");
    assert(availablePerks.length > 0, "Should have available perks from test badge types");
    
    const perkTypes = availablePerks.map(p => p.type);
    assert(perkTypes.includes("role_assignment"), "Should include role assignment perks");
    assert(perkTypes.includes("cosmetic_feature"), "Should include cosmetic feature perks");
    
    log("Available perks retrieval tests passed", 'success');
  } catch (error) {
    log(`Available perks retrieval test failed: ${error}`, 'error');
    testResults.failed++;
  }
}

async function testBadgeServiceIntegration() {
  log("\n=== Testing Badge Service Integration ===");
  
  const badgeService = new BadgeService(db);
  
  // Test 1: Badge creation with perks
  log("Testing badge creation with perks...");
  try {
    const badgeType = await badgeService.getBadgeTypeById(testData.badgeTypes[0].id);
    assert(badgeType !== null, "Should retrieve created badge type");
    assert(Array.isArray(badgeType.perks), "Badge type should have perks array");
    assert(badgeType.perks!.length > 0, "Badge type should have perks");
    
    log("Badge creation with perks tests passed", 'success');
  } catch (error) {
    log(`Badge creation with perks test failed: ${error}`, 'error');
    testResults.failed++;
  }
  
  // Test 2: Badge assignment with perk assignment
  log("Testing badge assignment with automatic perk assignment...");
  try {
    const badgeType = await badgeService.getBadgeTypeById(testData.badgeTypes[0].id);
    
    // Assign badge (this should automatically assign perks)
    const userBadge = await badgeService.assignBadge(
      testData.user.id,
      testData.badgeTypes[0].id,
      testData.user.id,
      testData.server.id
    );
    
    assert(userBadge !== null, "Badge should be assigned successfully");
    assert(userBadge.userId === testData.user.id, "Badge should be assigned to correct user");
    
    // Check if perks were assigned (this would require checking roles, etc.)
    const userPerks = await badgeService.getUserPerks(testData.user.id, testData.server.id);
    assert(Array.isArray(userPerks), "User perks should be an array");
    
    log("Badge assignment with perk assignment tests passed", 'success');
  } catch (error) {
    log(`Badge assignment with perk assignment test failed: ${error}`, 'error');
    testResults.failed++;
  }
  
  // Test 3: Badge removal with perk revocation
  log("Testing badge removal with automatic perk revocation...");
  try {
    // Remove badge (this should automatically revoke perks)
    await badgeService.removeBadge(
      testData.user.id,
      testData.badgeTypes[0].id,
      testData.user.id,
      testData.server.id
    );
    
    // Verify badge was removed
    const userBadges = await badgeService.getUserBadges(testData.user.id);
    const hasBadge = userBadges.some(badge => badge.badgeTypeId === testData.badgeTypes[0].id);
    assert(!hasBadge, "Badge should be removed from user");
    
    log("Badge removal with perk revocation tests passed", 'success');
  } catch (error) {
    log(`Badge removal with perk revocation test failed: ${error}`, 'error');
    testResults.failed++;
  }
  
  // Test 4: Perk validation for badge types
  log("Testing perk validation for badge types...");
  try {
    const badgeType = await badgeService.getBadgeTypeById(testData.badgeTypes[1].id);
    const validation = await badgeService.validateBadgePerks(badgeType, testData.server.id);
    
    assert(typeof validation.isValid === 'boolean', "Validation should return isValid boolean");
    assert(Array.isArray(validation.errors), "Validation should return errors array");
    assert(Array.isArray(validation.warnings), "Validation should return warnings array");
    
    log("Perk validation for badge types tests passed", 'success');
  } catch (error) {
    log(`Perk validation for badge types test failed: ${error}`, 'error');
    testResults.failed++;
  }
}

async function testAPIEndpoints() {
  log("\n=== Testing API Endpoints ===");
  
  // Start a test server
  const testPort = 3002;
  let testServer: any;
  
  try {
    // Import the main app
    const { default: app } = await import("./index");
    testServer = Bun.serve({
      port: testPort,
      fetch: app.fetch
    });
    
    log(`Test server started on port ${testPort}`);
    
    // Test 1: GET /api/perks/available
    log("Testing GET /api/perks/available...");
    try {
      const response = await fetch(`http://localhost:${testPort}/api/perks/available?serverId=${testData.server.id}`);
      const data = await response.json();
      
      assert(response.status === 200, "Available perks endpoint should return 200");
      assert(data.success === true, "Response should indicate success");
      assert(Array.isArray(data.perks), "Response should contain perks array");
      
      log("GET /api/perks/available tests passed", 'success');
    } catch (error) {
      log(`GET /api/perks/available test failed: ${error}`, 'error');
      testResults.failed++;
    }
    
    // Test 2: GET /api/users/:userId/perks
    log("Testing GET /api/users/:userId/perks...");
    try {
      const response = await fetch(`http://localhost:${testPort}/api/users/${testData.user.id}/perks?serverId=${testData.server.id}`);
      const data = await response.json();
      
      assert(response.status === 200, "User perks endpoint should return 200");
      assert(data.success === true, "Response should indicate success");
      assert(Array.isArray(data.perks), "Response should contain perks array");
      
      log("GET /api/users/:userId/perks tests passed", 'success');
    } catch (error) {
      log(`GET /api/users/:userId/perks test failed: ${error}`, 'error');
      testResults.failed++;
    }
    
    // Test 3: GET /api/badges/:badgeTypeId/perks/validate
    log("Testing GET /api/badges/:badgeTypeId/perks/validate...");
    try {
      const response = await fetch(`http://localhost:${testPort}/api/badges/${testData.badgeTypes[0].id}/perks/validate?serverId=${testData.server.id}`);
      const data = await response.json();
      
      assert(response.status === 200, "Badge perk validation endpoint should return 200");
      assert(data.success === true, "Response should indicate success");
      assert(typeof data.validation === 'object', "Response should contain validation object");
      
      log("GET /api/badges/:badgeTypeId/perks/validate tests passed", 'success');
    } catch (error) {
      log(`GET /api/badges/:badgeTypeId/perks/validate test failed: ${error}`, 'error');
      testResults.failed++;
    }
    
    // Test 4: GET /api/perks/display
    log("Testing GET /api/perks/display...");
    try {
      const response = await fetch(`http://localhost:${testPort}/api/perks/display?serverId=${testData.server.id}&userId=${testData.user.id}`);
      const data = await response.json();
      
      assert(response.status === 200, "Perk display endpoint should return 200");
      assert(data.success === true, "Response should indicate success");
      assert(typeof data.display === 'object', "Response should contain display object");
      assert(Array.isArray(data.display.availablePerks), "Display should contain available perks");
      assert(Array.isArray(data.display.userPerks), "Display should contain user perks");
      
      log("GET /api/perks/display tests passed", 'success');
    } catch (error) {
      log(`GET /api/perks/display test failed: ${error}`, 'error');
      testResults.failed++;
    }
    
    // Test 5: POST /api/perks/assign
    log("Testing POST /api/perks/assign...");
    try {
      const requestBody = {
        userId: testData.user.id,
        badgeTypeId: testData.badgeTypes[1].id,
        requestedBy: testData.user.id,
        serverId: testData.server.id
      };
      
      const response = await fetch(`http://localhost:${testPort}/api/perks/assign`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody)
      });
      
      const data = await response.json();
      
      assert(response.status === 200, "Perk assignment endpoint should return 200");
      assert(data.success === true, "Response should indicate success");
      assert(typeof data.results === 'object', "Response should contain results object");
      
      log("POST /api/perks/assign tests passed", 'success');
    } catch (error) {
      log(`POST /api/perks/assign test failed: ${error}`, 'error');
      testResults.failed++;
    }
    
  } catch (error) {
    log(`API endpoint testing failed: ${error}`, 'error');
    testResults.failed++;
  } finally {
    if (testServer) {
      testServer.stop();
      log("Test server stopped");
    }
  }
}

async function testComplexScenarios() {
  log("\n=== Testing Complex Scenarios ===");
  
  const badgeService = new BadgeService(db);
  
  // Test 1: Multiple badges with overlapping perks
  log("Testing multiple badges with overlapping perks...");
  try {
    // Assign first badge
    await badgeService.assignBadge(
      testData.user.id,
      testData.badgeTypes[0].id,
      testData.user.id,
      testData.server.id
    );
    
    // Assign second badge
    await badgeService.assignBadge(
      testData.user.id,
      testData.badgeTypes[1].id,
      testData.user.id,
      testData.server.id
    );
    
    // Check user perks
    const userPerks = await badgeService.getUserPerks(testData.user.id, testData.server.id);
    assert(userPerks.length > 0, "User should have perks from multiple badges");
    
    // Remove one badge and check that shared perks are handled correctly
    await badgeService.removeBadge(
      testData.user.id,
      testData.badgeTypes[0].id,
      testData.user.id,
      testData.server.id
    );
    
    const remainingPerks = await badgeService.getUserPerks(testData.user.id, testData.server.id);
    assert(Array.isArray(remainingPerks), "Should still have perks from remaining badge");
    
    log("Multiple badges with overlapping perks tests passed", 'success');
  } catch (error) {
    log(`Multiple badges with overlapping perks test failed: ${error}`, 'error');
    testResults.failed++;
  }
  
  // Test 2: Badge with complex perk configuration
  log("Testing badge with complex perk configuration...");
  try {
    const complexBadge = await badgeService.getBadgeTypeById(testData.badgeTypes[2].id);
    const validation = await badgeService.validateBadgePerks(complexBadge, testData.server.id);
    
    assert(typeof validation.isValid === 'boolean', "Complex badge validation should return boolean");
    
    // Assign complex badge
    await badgeService.assignBadge(
      testData.user.id,
      testData.badgeTypes[2].id,
      testData.user.id,
      testData.server.id
    );
    
    const userPerks = await badgeService.getUserPerks(testData.user.id, testData.server.id);
    const perkTypes = userPerks.map(p => p.type);
    
    assert(perkTypes.includes("permission_grant"), "Should include permission grant perks");
    assert(perkTypes.includes("cosmetic_feature"), "Should include cosmetic feature perks");
    assert(perkTypes.includes("access_privilege"), "Should include access privilege perks");
    assert(perkTypes.includes("custom_privilege"), "Should include custom privilege perks");
    
    log("Complex perk configuration tests passed", 'success');
  } catch (error) {
    log(`Complex perk configuration test failed: ${error}`, 'error');
    testResults.failed++;
  }
}

async function runAllTests() {
  log("🚀 Starting Comprehensive Perk System Tests\n", 'info');
  
  try {
    // Setup
    await setupTestData();
    
    // Run test suites
    await testPerkService();
    await testBadgeServiceIntegration();
    await testAPIEndpoints();
    await testComplexScenarios();
    
    // Cleanup
    if (TEST_CONFIG.cleanup) {
      await cleanupTestData();
    }
    
    // Results
    log("\n=== Test Results ===", 'info');
    log(`✓ Passed: ${testResults.passed}`, 'success');
    log(`✗ Failed: ${testResults.failed}`, testResults.failed > 0 ? 'error' : 'success');
    
    if (testResults.errors.length > 0) {
      log("\nFailed Tests:", 'error');
      testResults.errors.forEach(error => log(`  - ${error}`, 'error'));
    }
    
    const successRate = (testResults.passed / (testResults.passed + testResults.failed)) * 100;
    log(`\nSuccess Rate: ${successRate.toFixed(1)}%`, successRate >= 90 ? 'success' : 'warn');
    
    if (testResults.failed === 0) {
      log("\n🎉 All tests passed! Perk system implementation is working correctly.", 'success');
    } else {
      log(`\n⚠️  ${testResults.failed} test(s) failed. Please review the implementation.`, 'warn');
    }
    
  } catch (error) {
    log(`\n💥 Test suite failed with error: ${error}`, 'error');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (import.meta.main) {
  runAllTests().catch(error => {
    console.error("Test execution failed:", error);
    process.exit(1);
  });
}

export { runAllTests, testPerkService, testBadgeServiceIntegration, testAPIEndpoints, testComplexScenarios };