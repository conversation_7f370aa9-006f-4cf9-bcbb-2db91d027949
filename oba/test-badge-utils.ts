import { db } from "./db/index";
import {
  createBadgeType,
  getBadgeTypeById,
  getBadgeTypes,
  updateBadgeType,
  deleteBadgeType,
  assignBadgeToUser,
  removeBadgeFromUser,
  getUserBadges,
  updateBadgeVisibility,
  getUserStats,
  getBadgeStats,
  getBadgeLeaderboard,
  bulkAssignBadges,
  getAvailableBadgesForUser,
  getBadgeProgress,
} from "./db/utils/badge-utils";
import {
  evaluateUserForAutomaticBadges,
  batchEvaluateUsers,
  evaluateAllUsersForAutomaticBadges,
  reevaluateBadgeTypeForAllUsers,
  getUsersNearBadgeCompletion,
} from "./db/utils/badge-evaluation";
import type {
  CreateBadgeTypeRequest,
  UpdateBadgeTypeRequest,
  BadgeTypeFilters,
} from "./types/badge.types";

async function testBadgeUtils() {
  console.log("🧪 Testing Badge Database Utilities...\n");

  try {
    // Test 1: Create Badge Types
    console.log("1. Testing Badge Type Creation...");
    
    const firstMessageBadge: CreateBadgeTypeRequest = {
      name: "First Message",
      description: "Sent your first message",
      category: "milestone",
      assignmentType: "automatic",
      criteria: {
        type: "message_count",
        threshold: 1,
      },
    };

    const chatMasterBadge: CreateBadgeTypeRequest = {
      name: "Chat Master",
      description: "Sent 1000 messages",
      category: "achievement",
      assignmentType: "automatic",
      criteria: {
        type: "message_count",
        threshold: 1000,
      },
    };

    const specialBadge: CreateBadgeTypeRequest = {
      name: "Community Helper",
      description: "Recognized for helping community members",
      category: "special",
      assignmentType: "manual",
    };

    const badge1 = await createBadgeType(db, firstMessageBadge);
    const badge2 = await createBadgeType(db, chatMasterBadge);
    const badge3 = await createBadgeType(db, specialBadge);

    console.log("✅ Created badge types:", {
      badge1: badge1.name,
      badge2: badge2.name,
      badge3: badge3.name,
    });

    // Test 2: Get Badge Types
    console.log("\n2. Testing Badge Type Retrieval...");
    
    const allBadges = await getBadgeTypes(db);
    console.log(`✅ Retrieved ${allBadges.length} badge types`);

    const automaticBadges = await getBadgeTypes(db, { assignmentType: "automatic" });
    console.log(`✅ Retrieved ${automaticBadges.length} automatic badges`);

    const achievementBadges = await getBadgeTypes(db, { category: "achievement" });
    console.log(`✅ Retrieved ${achievementBadges.length} achievement badges`);

    // Test 3: Update Badge Type
    console.log("\n3. Testing Badge Type Update...");
    
    const updateData: UpdateBadgeTypeRequest = {
      description: "Sent your very first message in the community",
      color: "#FFD700",
    };

    const updatedBadge = await updateBadgeType(db, badge1.id, updateData);
    console.log("✅ Updated badge:", updatedBadge?.name);

    // Test 4: Get Badge Type by ID
    console.log("\n4. Testing Badge Type by ID...");
    
    const retrievedBadge = await getBadgeTypeById(db, badge1.id);
    console.log("✅ Retrieved badge by ID:", retrievedBadge?.name);

    // Test 5: User Statistics (need a real user ID for this)
    console.log("\n5. Testing User Statistics...");
    
    // First, let's try to get a user from the database
    const users = await db.query.UserSchema.findMany({ limit: 1 });
    
    if (users.length > 0) {
      const userId = users[0].id;
      console.log(`Using user ID: ${userId}`);
      
      const userStats = await getUserStats(db, userId);
      console.log("✅ User stats:", {
        messageCount: userStats.messageCount,
        serverCount: userStats.serverCount,
        friendCount: userStats.friendCount,
        daysActive: userStats.daysActive,
        accountAge: userStats.accountAge,
      });

      // Test 6: Badge Assignment
      console.log("\n6. Testing Badge Assignment...");
      
      try {
        const assignedBadge = await assignBadgeToUser(db, userId, badge3.id, userId);
        console.log("✅ Assigned manual badge:", assignedBadge?.badgeType?.name);
      } catch (error) {
        console.log("⚠️ Badge assignment error (expected if already assigned):", error.message);
      }

      // Test 7: Get User Badges
      console.log("\n7. Testing User Badge Retrieval...");
      
      const userBadges = await getUserBadges(db, userId);
      console.log(`✅ User has ${userBadges.length} badges`);

      // Test 8: Available Badges
      console.log("\n8. Testing Available Badges...");
      
      const availableBadges = await getAvailableBadgesForUser(db, userId);
      console.log(`✅ User has ${availableBadges.length} available badges`);

      // Test 9: Badge Progress
      console.log("\n9. Testing Badge Progress...");
      
      const badgeProgress = await getBadgeProgress(db, userId);
      console.log(`✅ Retrieved progress for ${badgeProgress.length} automatic badges`);
      
      badgeProgress.forEach(progress => {
        console.log(`  - ${progress.badgeType.name}: ${progress.progress}/${progress.total} (${progress.isEarned ? 'Earned' : 'Not earned'})`);
      });

      // Test 10: Automatic Badge Evaluation
      console.log("\n10. Testing Automatic Badge Evaluation...");
      
      const evaluationResult = await evaluateUserForAutomaticBadges(db, userId);
      console.log("✅ Evaluation result:", {
        newBadges: evaluationResult.newBadges.length,
        evaluatedBadges: evaluationResult.evaluatedBadges.length,
        errors: evaluationResult.errors.length,
      });

      // Test 11: Badge Visibility Update
      console.log("\n11. Testing Badge Visibility Update...");
      
      if (userBadges.length > 0) {
        const success = await updateBadgeVisibility(db, userId, userBadges[0].badgeTypeId, false);
        console.log("✅ Updated badge visibility:", success);
        
        // Restore visibility
        await updateBadgeVisibility(db, userId, userBadges[0].badgeTypeId, true);
      }

      // Test 12: Bulk Badge Assignment
      console.log("\n12. Testing Bulk Badge Assignment...");
      
      const bulkAssignments = [
        { userId, badgeTypeId: badge1.id },
        { userId, badgeTypeId: badge2.id },
      ];
      
      const bulkResults = await bulkAssignBadges(db, bulkAssignments);
      console.log(`✅ Bulk assigned ${bulkResults.length} badges`);

    } else {
      console.log("⚠️ No users found in database, skipping user-related tests");
    }

    // Test 13: Badge Statistics
    console.log("\n13. Testing Badge Statistics...");
    
    const badgeStats = await getBadgeStats(db);
    console.log("✅ Badge statistics:", {
      totalBadges: badgeStats.totalBadges,
      totalAssignments: badgeStats.totalAssignments,
      categoryBreakdown: badgeStats.categoryBreakdown,
      mostPopularCount: badgeStats.mostPopularBadges.length,
    });

    // Test 14: Badge Leaderboard
    console.log("\n14. Testing Badge Leaderboard...");
    
    const leaderboard = await getBadgeLeaderboard(db, 5);
    console.log(`✅ Retrieved leaderboard with ${leaderboard.length} entries`);
    
    leaderboard.forEach((entry, index) => {
      console.log(`  ${index + 1}. ${entry.username}: ${entry.badgeCount} badges`);
    });

    // Test 15: Users Near Badge Completion
    console.log("\n15. Testing Users Near Badge Completion...");
    
    const usersNearCompletion = await getUsersNearBadgeCompletion(db, badge2.id, 0.5);
    console.log(`✅ Found ${usersNearCompletion.length} users near completion`);

    // Test 16: Batch User Evaluation
    console.log("\n16. Testing Batch User Evaluation...");
    
    if (users.length > 0) {
      const userIds = users.slice(0, 3).map(u => u.id); // Test with first 3 users
      const batchResults = await batchEvaluateUsers(db, userIds);
      console.log(`✅ Batch evaluated ${batchResults.length} users`);
      
      const totalNewBadges = batchResults.reduce((sum, result) => sum + result.newBadges.length, 0);
      console.log(`  Total new badges assigned: ${totalNewBadges}`);
    }

    // Cleanup: Delete test badges
    console.log("\n🧹 Cleaning up test data...");
    
    await deleteBadgeType(db, badge1.id);
    await deleteBadgeType(db, badge2.id);
    await deleteBadgeType(db, badge3.id);
    
    console.log("✅ Cleaned up test badge types");

    console.log("\n🎉 All badge utility tests completed successfully!");

  } catch (error) {
    console.error("❌ Test failed:", error);
    throw error;
  }
}

// Run the tests
if (require.main === module) {
  testBadgeUtils()
    .then(() => {
      console.log("\n✅ Badge utilities test suite completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n❌ Badge utilities test suite failed:", error);
      process.exit(1);
    });
}

export { testBadgeUtils };