import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ProtectedRouteHandler,
} from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import authWithRefreshMiddleware from "../middlewares/authWithRefreshMiddleware.ts";
import {
  submitNominationHandler,
  getReceivedNominationsHandler,
  getSubmittedNominationsHandler,
  getUserNominationsHandler,
  getNominationStatsHandler,
  getNominationCountHandler,
  approveNominationHandler,
  rejectNominationHandler,
  getNominationAnalyticsHandler,
} from "../handlers/badge-nominations.ts";
import type Router from "../class/router.ts";

export function registerBadgeNominationRoutes(router: Router) {
  // Nomination submission
  router.add(
    "/api/badges/nominations",
    new ProtectedRouteHandler(submitNominationHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  // Get nominations received by current user
  router.add(
    "/api/badges/nominations/received",
    new ProtectedRouteHandler(getReceivedNominationsHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  // Get nominations submitted by current user
  router.add(
    "/api/badges/nominations/submitted",
    new ProtectedRouteHandler(getSubmittedNominationsHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  // Get nomination statistics for a badge type
  router.add(
    "/api/badges/nominations/stats/:badgeTypeId",
    new ProtectedRouteHandler(getNominationStatsHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  // Get nomination count for specific user and badge type
  router.add(
    "/api/badges/nominations/count/:badgeTypeId/:userId",
    new ProtectedRouteHandler(getNominationCountHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  // Admin-only routes
  router.add(
    "/api/badges/nominations/user/:userId",
    new ProtectedRouteHandler(getUserNominationsHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  router.add(
    "/api/badges/nominations/:nominationId/approve",
    new ProtectedRouteHandler(approveNominationHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  router.add(
    "/api/badges/nominations/:nominationId/reject",
    new ProtectedRouteHandler(rejectNominationHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  router.add(
    "/api/badges/nominations/analytics",
    new ProtectedRouteHandler(getNominationAnalyticsHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );
}