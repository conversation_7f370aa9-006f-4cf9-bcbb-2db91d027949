import {
  <PERSON><PERSON>out<PERSON><PERSON><PERSON><PERSON>,
  ProtectedRouteHandler,
} from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import {
  userServers<PERSON>and<PERSON>,
  serverDetailsHandler,
  createServerInvite<PERSON>ink<PERSON>andler,
  getServerInformationFromInviteHand<PERSON>,
  serverJoinHandler,
  updateServerHandler,
  updateServerIconHandler,
  restructureServerHandler,
  reorderChannelsHandler,
  addChannelToCategoryHandler,
  removeChannelFromCategoryHandler,
} from "../handlers/servers";
import type Router from "../class/router.ts";

export function registerServerRoutes(router: Router) {
  router.add(
    "/api/getUserServerlist",
    new PublicRouteHandler(userServersHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/getServerDetails",
    new PublicRouteHandler(serverDetailsHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/createServerInvite",
    new ProtectedRouteHandler(createServerInviteLinkHandler, [
      loggerMiddleware,
    ]),
  );
  router.add(
    "/api/invites",
    new ProtectedRouteHandler(getServerInformationFromInviteHandler, [
      loggerMiddleware,
    ]),
  );
  router.add(
    "/api/join",
    new ProtectedRouteHandler(serverJoinHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/updateServer",
    new ProtectedRouteHandler(updateServerHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/server/setIcon",
    new ProtectedRouteHandler(updateServerIconHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/servers/:serverId/restructure",
    new ProtectedRouteHandler(restructureServerHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/servers/:serverId/channels/reorder",
    new ProtectedRouteHandler(reorderChannelsHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/servers/:serverId/categories/:categoryId/channels",
    new ProtectedRouteHandler(addChannelToCategoryHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/servers/:serverId/categories/:categoryId/channels/:channelId",
    new ProtectedRouteHandler(removeChannelFromCategoryHandler, [
      loggerMiddleware,
    ]),
  );
}
