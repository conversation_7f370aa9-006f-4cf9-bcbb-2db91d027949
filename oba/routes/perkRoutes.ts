import { Router } from "../class/router";
import {
  getUserPerks<PERSON>and<PERSON>,
  getAvailablePerksHandler,
  validateBadgePerksHandler,
  manuallyAssignPerksHandler,
  manuallyRevokePerksHandler,
  getPerkDisplayHandler
} from "../handlers/perks";

/**
 * Perk Routes
 * Handles all perk-related API endpoints
 */
export function setupPerkRoutes(router: Router): void {
  // User perk endpoints
  router.get("/api/users/:userId/perks", getUserPerksHandler);
  
  // Available perks endpoints
  router.get("/api/perks/available", getAvailablePerksHandler);
  router.get("/api/perks/display", getPerkDisplayHandler);
  
  // Badge perk validation
  router.get("/api/badges/:badgeTypeId/perks/validate", validateBadgePerksHandler);
  
  // Manual perk management (admin only)
  router.post("/api/perks/assign", manuallyAssignPerksHandler);
  router.post("/api/perks/revoke", manuallyRevokePerksHandler);
}