import {
  <PERSON><PERSON><PERSON>e<PERSON><PERSON><PERSON>,
  ProtectedRouteHandler,
} from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import {
  editMessageHandler,
  deleteMessageHandler,
  getChannelMessagesHandler,
} from "../handlers/messages.ts";
import type Router from "../class/router.ts";

export function registerMessageRoutes(router: Router) {
  router.add(
    "/api/messages/edit",
    new ProtectedRouteHandler(editMessageHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/messages/delete",
    new ProtectedRouteHandler(deleteMessageHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/messages",
    new ProtectedRouteHandler(getChannelMessagesHandler, [loggerMiddleware]),
  );
}
