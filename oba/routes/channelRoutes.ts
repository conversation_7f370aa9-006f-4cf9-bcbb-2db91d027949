import { ProtectedRouteHandler } from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import {
  create<PERSON>hannel<PERSON>andler,
  update<PERSON>hannel<PERSON>andler,
  delete<PERSON>hannel<PERSON>andler,
  getChannelDetailsHandler,
} from "../handlers/channels.ts";
import type Router from "../class/router.ts";

export function registerChannelRoutes(router: Router) {
  router.add(
    "/api/channels/create",
    new ProtectedRouteHandler(createChannelHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/channels/update",
    new ProtectedRouteHandler(updateChannelHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/channels/delete",
    new ProtectedRouteHandler(deleteChannelHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/channels/details",
    new ProtectedRouteHandler(getChannelDetailsHandler, [loggerMiddleware]),
  );
}
