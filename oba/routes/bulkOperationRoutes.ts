import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ProtectedRouteHandler,
} from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import {
  updateChannelPositionsHandler,
  updateCategoryPositionsHandler,
  moveChannelsHandler,
} from "../handlers/bulk-operations.ts";
import type Router from "../class/router.ts";

export function registerBulkOperationRoutes(router: Router) {
  router.add(
    "/api/channels/bulk-update-positions",
    new ProtectedRouteHandler(updateChannelPositionsHandler, [
      loggerMiddleware,
    ]),
  );
  router.add(
    "/api/bulk/categories/positions",
    new ProtectedRouteHandler(updateCategoryPositionsHandler, [
      loggerMiddleware,
    ]),
  );
  router.add(
    "/api/bulk/channels/move",
    new ProtectedRouteHandler(moveChannelsHandler, [loggerMiddleware]),
  );
}
