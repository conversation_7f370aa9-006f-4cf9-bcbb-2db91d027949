import { ProtectedRouteHandler } from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import {
  sendFriendRequestHandler,
  acceptFriendRequestHand<PERSON>,
  reject<PERSON>riend<PERSON><PERSON><PERSON><PERSON>and<PERSON>,
  cancel<PERSON>riend<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  remove<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  block<PERSON><PERSON><PERSON><PERSON><PERSON>,
  unblock<PERSON>ser<PERSON>and<PERSON>,
  getUserFriendsHandler,
  getPendingFriendRequestsHandler,
  getSentFriendRequestsHandler,
  getBlockedUsersHandler,
} from "../handlers/friends.ts";
import type Router from "../class/router.ts";

export function registerFriendRoutes(router: Router) {
  router.add(
    "/api/friends/send-request",
    new ProtectedRouteHandler(sendFriendRequestHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/friends/accept-request",
    new ProtectedRouteHandler(acceptFriendRequestHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/friends/reject-request",
    new ProtectedRouteHandler(rejectFriendRequestHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/friends/cancel-request",
    new ProtectedRouteHandler(cancelFriendRequestHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/friends/remove",
    new ProtectedRouteHandler(removeFriendHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/friends/block",
    new ProtectedRouteHandler(blockUserHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/friends/unblock",
    new ProtectedRouteHandler(unblockUserHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/friends",
    new ProtectedRouteHandler(getUserFriendsHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/friends/pending",
    new ProtectedRouteHandler(getPendingFriendRequestsHandler, [
      loggerMiddleware,
    ]),
  );
  router.add(
    "/api/friends/sent",
    new ProtectedRouteHandler(getSentFriendRequestsHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/friends/blocked",
    new ProtectedRouteHandler(getBlockedUsersHandler, [loggerMiddleware]),
  );
}
