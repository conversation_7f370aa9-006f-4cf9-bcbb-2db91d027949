import type Router from "../class/router";
import {
  PublicRouteHandler,
  ProtectedRouteHandler,
} from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import authMiddleware from "../middlewares/authMiddleware";
import { presignedMinio } from "../handlers/minio.ts";
import { logger } from "../services/logger.service.ts";
import authWithRefreshMiddleware from "../middlewares/authWithRefreshMiddleware.ts";

export function registerUtilityRoutes(router: Router) {
  router.add(
    "/",
    new PublicRouteHandler(() => new Response("Welcome!"), [loggerMiddleware]),
  );

  router.add(
    "/about",
    new PublicRouteHandler(() => new Response("About us"), [loggerMiddleware]),
  );

  router.add(
    "/profile",
    new ProtectedRouteHandler(
      (req: Request) => {
        const user = req.headers.get("X-Authenticated-User");
        return new Response(`Profile page. Welcome, ${user}!`);
      },
      [loggerMiddleware, authMiddleware],
    ),
  );

  router.add(
    "/users/:id",
    new PublicRouteHandler(
      (req: Request, params?: Record<string, string>) => {
        const userId = params?.id;
        return new Response(`User profile for user ID: ${userId}`);
      },
      [loggerMiddleware],
    ),
  );

  router.add(
    "/api/presigned",
    new ProtectedRouteHandler(presignedMinio, [loggerMiddleware])
  );
}
