import { Router } from '../class/router';
import {
  createBadgeCollection,
  updateBadgeCollection,
  deleteBadgeCollection,
  getBadgeCollections,
  getBadgeCollection,
  reorderCollectionBadges,
  bulkAssignCollectionBadges,
  getCollectionAnalytics,
  getCollectionProgressReport,
  testBadgeCollection,
  previewBadgeCollection
} from '../handlers/badge-collection-admin';
import { authMiddleware } from '../middlewares/authMiddleware';

const router = new Router();

// Apply authentication middleware to all admin routes
router.use(authMiddleware);

// Collection CRUD operations
router.post('/api/admin/badge-collections', createBadgeCollection);
router.get('/api/admin/badge-collections', getBadgeCollections);
router.get('/api/admin/badge-collections/:id', getBadgeCollection);
router.put('/api/admin/badge-collections/:id', updateBadgeCollection);
router.delete('/api/admin/badge-collections/:id', deleteBadgeCollection);

// Collection management operations
router.put('/api/admin/badge-collections/:id/reorder', reorderCollectionBadges);
router.post('/api/admin/badge-collections/:id/bulk-assign', bulkAssignCollectionBadges);

// Collection analytics and reporting
router.get('/api/admin/badge-collections/:id/analytics', getCollectionAnalytics);
router.get('/api/admin/badge-collections/:id/progress-report', getCollectionProgressReport);

// Collection testing and preview
router.get('/api/admin/badge-collections/:id/test', testBadgeCollection);
router.get('/api/admin/badge-collections/:id/preview', previewBadgeCollection);

export default router;