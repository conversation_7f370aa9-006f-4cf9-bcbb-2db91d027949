import { ProtectedRouteHandler } from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import authWithRefreshMiddleware from "../middlewares/authWithRefreshMiddleware";
import {
  getConversationsHandler,
  create<PERSON>onversation<PERSON>and<PERSON>,
  deleteConversation<PERSON>andler,
  getMessagesHandler,
} from "../handlers/conversations.ts";
import {
  sendDirectMessageHandler,
  editDirectMessageHandler,
  deleteDirect<PERSON>essageHandler,
  markDirectMessageAsReadHandler,
  getDirectMessagesHandler,
  getUserContactsHandler,
} from "../handlers/directMessages.ts";
import type Router from "../class/router.ts";

export function registerDirectMessageRoutes(router: Router) {
  // Conversation routes
  router.add(
    "/api/conversations",
    new ProtectedRouteHandler(getConversationsHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ]),
  );
  router.add(
    "/api/conversations/create",
    new ProtectedRouteHandler(createConversationHand<PERSON>, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ]),
  );
  router.add(
    "/api/conversations/delete/:conversationId",
    new ProtectedRouteHandler(deleteConversationHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ]),
  );
  router.add(
    "/api/conversations/:conversationId/messages",
    new ProtectedRouteHandler(getMessagesHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ]),
  );

  // Direct message routes
  router.add(
    "/api/directMessages/send",
    new ProtectedRouteHandler(sendDirectMessageHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/directMessages/edit",
    new ProtectedRouteHandler(editDirectMessageHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/directMessages/delete",
    new ProtectedRouteHandler(deleteDirectMessageHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/directMessages/markAsRead",
    new ProtectedRouteHandler(markDirectMessageAsReadHandler, [
      loggerMiddleware,
    ]),
  );
  router.add(
    "/api/directMessages",
    new ProtectedRouteHandler(getDirectMessagesHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/directMessages/contacts",
    new ProtectedRouteHandler(getUserContactsHandler, [loggerMiddleware]),
  );
}
