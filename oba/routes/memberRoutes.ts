import { ProtectedRouteHandler } from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import {
  kickMemberHandler,
  banMemberHandler,
  unbanMemberHandler,
  changeUserRoles<PERSON>andler,
  getBannedUsersHandler,
  getServerMembersHandler,
} from "../handlers/serverMembers.ts";
import type Router from "../class/router.ts";

export function registerMemberRoutes(router: Router) {
  router.add(
    "/api/members/kick",
    new ProtectedRouteHandler(kickMemberHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/members/ban",
    new ProtectedRouteHandler(banMemberHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/members/unban",
    new ProtectedRouteHandler(unbanMemberHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/members/roles",
    new ProtectedRouteHandler(changeUser<PERSON>olesHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/members/banned",
    new ProtectedRouteHandler(getBannedUsersHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/members/list",
    new ProtectedRouteHandler(getServerMembersHandler, [loggerMiddleware]),
  );
}
