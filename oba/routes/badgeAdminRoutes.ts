import { Router } from "../class/router";
import {
  bulkAssignBadgesHandler,
  getBadgeAuditLogHandler,
  getAdminBadgeStatsHandler,
  getBadgeHealthCheckHandler,
  evaluateAllUsersHandler,
  reevaluateBadgeTypeHand<PERSON>,
  cleanupBadgeDataHandler,
  getUserBadgeSummaryHandler
} from "../handlers/badge-admin";

/**
 * Badge Administration Routes
 * Admin-only endpoints for badge system management
 */
export function registerBadgeAdminRoutes(router: Router): void {
  // Bulk operations
  router.post("/api/admin/badges/bulk-assign", bulkAssignBadgesHandler);
  
  // Audit and monitoring
  router.get("/api/admin/badges/audit-log", getBadgeAuditLogHandler);
  router.get("/api/admin/badges/stats", getAdminBadgeStatsHandler);
  router.get("/api/admin/badges/health-check", getBadgeHealthCheckHandler);
  
  // System operations
  router.post("/api/admin/badges/evaluate-all", evaluateAllUsersHandler);
  router.post("/api/admin/badges/reevaluate/:badgeTypeId", reevaluateBadgeTypeHandler);
  router.delete("/api/admin/badges/cleanup", cleanupBadgeDataHandler);
  
  // User management
  router.get("/api/admin/badges/user-summary/:userId", getUserBadgeSummaryHandler);
}