import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ProtectedRoute<PERSON><PERSON><PERSON>,
} from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import authMiddleware from "../middlewares/authMiddleware";
import authWithRefreshMiddleware from "../middlewares/authWithRefreshMiddleware.ts";
import {
  getBadgeTypesHandler,
  createBadgeTypeHandler,
  updateBadgeTypeHandler,
  deleteBadgeTypeHandler,
  getUserBadgesHandler,
  assignBadgeToUserHandler,
  removeBadgeFromUserHandler,
  getAvailableBadgesHandler,
  evaluateUserBadgesHandler,
  getBadgeStatsHandler,
  getBadgeLeaderboardHandler,
} from "../handlers/badges.ts";
import {
  getBadgeProgressHandler,
  getBadgeDashboardHandler,
  getBadgeHistoryHandler,
  updateBadgeVisibilityHandler,
  getBadgeCriteriaHandler,
} from "../handlers/badge-dashboard.ts";
import {
  get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  get<PERSON>oll<PERSON><PERSON><PERSON><PERSON><PERSON>,
  create<PERSON>oll<PERSON><PERSON><PERSON><PERSON><PERSON>,
  update<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  delete<PERSON>oll<PERSON>tion<PERSON><PERSON><PERSON>,
  getUser<PERSON>ollectionProgressHandler,
  getUserCollectionProgressAllHandler,
  getDetailedCollectionProgressHandler,
  getNextUnlockableBadgeHandler,
  checkCollectionCompletionHandler,
} from "../handlers/badge-collections.ts";
import type Router from "../class/router.ts";

export function registerBadgeRoutes(router: Router) {
  // Badge type management endpoints
  router.add(
    "/api/badges/types",
    new PublicRouteHandler(getBadgeTypesHandler, [loggerMiddleware])
  );

  router.add(
    "/api/badges/types/create",
    new ProtectedRouteHandler(createBadgeTypeHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  router.add(
    "/api/badges/types/:id/update",
    new ProtectedRouteHandler(updateBadgeTypeHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  router.add(
    "/api/badges/types/:id/delete",
    new ProtectedRouteHandler(deleteBadgeTypeHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  // User badge endpoints
  router.add(
    "/api/users/:userId/badges",
    new PublicRouteHandler(getUserBadgesHandler, [loggerMiddleware])
  );

  router.add(
    "/api/users/:userId/badges/assign",
    new ProtectedRouteHandler(assignBadgeToUserHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  router.add(
    "/api/users/:userId/badges/:badgeId",
    new ProtectedRouteHandler(removeBadgeFromUserHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  // Badge utility endpoints
  router.add(
    "/api/badges/available",
    new ProtectedRouteHandler(getAvailableBadgesHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  router.add(
    "/api/badges/evaluate/:userId",
    new ProtectedRouteHandler(evaluateUserBadgesHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  router.add(
    "/api/badges/stats",
    new PublicRouteHandler(getBadgeStatsHandler, [loggerMiddleware])
  );

  router.add(
    "/api/badges/leaderboard",
    new PublicRouteHandler(getBadgeLeaderboardHandler, [loggerMiddleware])
  );

  // Badge progress and dashboard endpoints
  router.add(
    "/api/badges/progress",
    new ProtectedRouteHandler(getBadgeProgressHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  router.add(
    "/api/badges/dashboard",
    new ProtectedRouteHandler(getBadgeDashboardHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  router.add(
    "/api/badges/history",
    new ProtectedRouteHandler(getBadgeHistoryHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  router.add(
    "/api/badges/visibility",
    new ProtectedRouteHandler(updateBadgeVisibilityHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  router.add(
    "/api/badges/criteria/:badgeTypeId",
    new ProtectedRouteHandler(getBadgeCriteriaHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  // Badge collection management endpoints
  router.add(
    "/api/badges/collections",
    new PublicRouteHandler(getCollectionsHandler, [loggerMiddleware])
  );

  router.add(
    "/api/badges/collections/:collectionId",
    new PublicRouteHandler(getCollectionHandler, [loggerMiddleware])
  );

  router.add(
    "/api/badges/collections/create",
    new ProtectedRouteHandler(createCollectionHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  router.add(
    "/api/badges/collections/:collectionId/update",
    new ProtectedRouteHandler(updateCollectionHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  router.add(
    "/api/badges/collections/:collectionId/delete",
    new ProtectedRouteHandler(deleteCollectionHandler, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ])
  );

  // User collection progress endpoints
  router.add(
    "/api/users/:userId/collections/:collectionId/progress",
    new PublicRouteHandler(getUserCollectionProgressHandler, [loggerMiddleware])
  );

  router.add(
    "/api/users/:userId/collections/progress",
    new PublicRouteHandler(getUserCollectionProgressAllHandler, [loggerMiddleware])
  );

  router.add(
    "/api/users/:userId/collections/:collectionId/detailed-progress",
    new PublicRouteHandler(getDetailedCollectionProgressHandler, [loggerMiddleware])
  );

  router.add(
    "/api/users/:userId/collections/:collectionId/next-badge",
    new PublicRouteHandler(getNextUnlockableBadgeHandler, [loggerMiddleware])
  );

  router.add(
    "/api/users/:userId/collections/:collectionId/completion",
    new PublicRouteHandler(checkCollectionCompletionHandler, [loggerMiddleware])
  );
}