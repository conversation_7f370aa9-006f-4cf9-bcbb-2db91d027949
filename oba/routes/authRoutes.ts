import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ProtectedRouteHandler,
} from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import authMiddleware from "../middlewares/authMiddleware";
import {
  login<PERSON>and<PERSON>,
  registerHandler,
  updateUserProfileHandler,
  requestPassword<PERSON><PERSON>t<PERSON>and<PERSON>,
  resetPassword<PERSON>and<PERSON>,
  verify<PERSON>mail<PERSON><PERSON><PERSON>,
  refreshT<PERSON><PERSON>and<PERSON>,
  logoutHandler,
  authenticateUser<PERSON><PERSON>,
  getUserDetailsHandler,
} from "../handlers/auth.ts";
import {
  handleGetPresignedUrl,
  handleUpdateAvatar,
} from "../handlers/profilePicture";
import type Router from "../class/router.ts";
import authWithRefreshMiddleware from "../middlewares/authWithRefreshMiddleware.ts";

export function registerAuthRoutes(router: Router) {
  router.add(
    "/api/login",
    new PublicRouteHandler(loginHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/register",
    new PublicRouteHandler(registerHandler, [loggerMiddleware]),
  );
  // Avatar upload routes
  router.add(
    "/api/user/upload-avatar/presign",
    new ProtectedRouteHandler(handleGetPresignedUrl, [
      loggerMiddleware,
      authMiddleware,
    ]),
  );
  router.add(
    "/api/user/upload-avatar",
    new ProtectedRouteHandler(handleUpdateAvatar, [
      loggerMiddleware,
      authWithRefreshMiddleware,
    ]),
  );
  router.add(
    "/api/updateUserProfile",
    new ProtectedRouteHandler(updateUserProfileHandler, [loggerMiddleware]),
  );
  // Password reset routes
  router.add(
    "/api/request-password-reset",
    new PublicRouteHandler(requestPasswordResetHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/reset-password",
    new PublicRouteHandler(resetPasswordHandler, [loggerMiddleware]),
  );
  // Email verification route
  router.add(
    "/api/verify-email",
    new PublicRouteHandler(verifyEmailHandler, [loggerMiddleware]),
  );
  // Token refresh and logout routes
  router.add(
    "/api/refresh-token",
    new PublicRouteHandler(refreshTokenHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/logout",
    new PublicRouteHandler(logoutHandler, [loggerMiddleware]),
  );
  // User details route
  router.add(
    "/api/getUserDetails",
    new ProtectedRouteHandler(getUserDetailsHandler, [loggerMiddleware, authWithRefreshMiddleware]),
  );
}
