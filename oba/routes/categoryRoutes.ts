import { ProtectedRouteHandler } from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import {
  create<PERSON>ategory<PERSON>and<PERSON>,
  update<PERSON>ategoryHand<PERSON>,
  deleteCategoryHandler,
  getServerCategoriesHandler,
  getCategoryDetailsHandler,
  getCategoryChannelsHandler,
} from "../handlers/categories.ts";
import type Router from "../class/router.ts";

export function registerCategoryRoutes(router: Router) {
  router.add(
    "/api/categories/create",
    new ProtectedRouteHandler(createCategoryHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/categories/update",
    new ProtectedRouteHandler(updateCategoryHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/categories/delete",
    new ProtectedRouteHandler(deleteCategoryHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/categories/server",
    new ProtectedRouteHandler(getServerCategoriesHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/categories/details",
    new ProtectedRouteHandler(getCategoryDetailsHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/categories/channels",
    new ProtectedRouteHandler(getCategoryChannelsHandler, [loggerMiddleware]),
  );
}
