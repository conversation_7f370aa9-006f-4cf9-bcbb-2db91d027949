import { ProtectedRouteHandler } from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import {
  addReactionHandler,
  removeReaction<PERSON>andler,
  getReactionsHandler,
} from "../handlers/reactions.ts";
import type Router from "../class/router.ts";

export function registerReactionRoutes(router: Router) {
  router.add(
    "/api/reactions/add",
    new ProtectedRouteHandler(addReactionHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/reactions/remove",
    new ProtectedRouteHandler(removeReactionHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/reactions",
    new ProtectedRouteHandler(getReactionsHandler, [loggerMiddleware]),
  );
}
