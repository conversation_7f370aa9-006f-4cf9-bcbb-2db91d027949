import { ProtectedRouteHandler } from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import {
  updateUserStatusHandler,
  getUserStatus<PERSON>and<PERSON>,
  getOnlineFriendsHandler,
  updateLastActiveHandler,
} from "../handlers/status.ts";
import type Router from "../class/router.ts";

export function registerStatusRoutes(router: Router) {
  router.add(
    "/api/status/update",
    new ProtectedRouteHandler(updateUserStatusHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/status",
    new ProtectedRouteHandler(getUserStatusHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/status/friends/online",
    new ProtectedRouteHandler(getOnlineFriendsHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/status/last-active",
    new ProtectedRouteHandler(updateLastActiveHandler, [loggerMiddleware]),
  );
}
