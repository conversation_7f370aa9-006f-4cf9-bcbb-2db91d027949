import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ProtectedRouteHandler,
} from "../class/routeHandler.ts";
import loggerMiddleware from "../middlewares/loggerMiddleware";
import {
  createRoleHandler,
  updateR<PERSON><PERSON>andler,
  deleteR<PERSON>Handler,
  getServer<PERSON><PERSON>sHandler,
  getU<PERSON><PERSON><PERSON>sHandler,
  assign<PERSON><PERSON>Hand<PERSON>,
  remove<PERSON><PERSON>Handler,
} from "../handlers/roles.ts";
import type Router from "../class/router.ts";

export function registerRoleRoutes(router: Router) {
  router.add(
    "/api/roles/create",
    new ProtectedRouteHandler(createRoleHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/roles/update",
    new ProtectedRouteHandler(updateRoleHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/roles/delete",
    new ProtectedRouteHandler(deleteRoleHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/roles/server",
    new ProtectedRouteHandler(getServer<PERSON><PERSON>sHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/roles/user",
    new ProtectedRouteHandler(getUser<PERSON><PERSON>sHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/roles/assign",
    new ProtectedRouteHandler(assignRoleHandler, [loggerMiddleware]),
  );
  router.add(
    "/api/roles/remove",
    new ProtectedRouteHandler(removeRoleHandler, [loggerMiddleware]),
  );
}
