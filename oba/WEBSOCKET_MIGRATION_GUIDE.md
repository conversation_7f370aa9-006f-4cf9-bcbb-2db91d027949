# WebSocket Standardization Migration Guide

This guide helps developers migrate from the legacy WebSocket message format to the new standardized format using WebSocketUtils.

## Overview

The WebSocket standardization introduces:
- Consistent message structure across all WebSocket communications
- Built-in error handling and validation
- Correlation tracking for request-response patterns
- Type safety with TypeScript interfaces
- Backward compatibility during transition

## Migration Steps

### Step 1: Update Dependencies

Ensure you have the latest version with WebSocket standardization support:

```bash
# Update to latest version
bun install
```

### Step 2: Import New Utilities

Replace manual JSON message creation with WebSocketUtils:

```typescript
// Before (Legacy)
import { ServerWebSocket } from 'bun';

// After (Standardized)
import { WebSocketUtils } from '../utils/websocket-utils';
import { IWebSocketMessage, IWebSocketSuccessMessage } from '../types/websocket-standardization.types';
```

### Step 3: Update Message Creation

#### Success Messages

```typescript
// Before (Legacy)
ws.send(JSON.stringify({
  type: 'MESSAGE_SENT',
  data: { messageId: '123', content: 'Hello' },
  timestamp: new Date().toISOString()
}));

// After (Standardized)
const message = WebSocketUtils.success('MESSAGE_SENT', {
  messageId: '123',
  content: 'Hello'
});
WebSocketUtils.send(ws, message);
```

#### Error Messages

```typescript
// Before (Legacy)
ws.send(JSON.stringify({
  type: 'ERROR',
  error: 'Authentication required',
  timestamp: new Date().toISOString()
}));

// After (Standardized)
const errorMessage = WebSocketUtils.authenticationRequired();
WebSocketUtils.send(ws, errorMessage);

// Or custom errors
const customError = WebSocketUtils.error('CUSTOM_ERROR', 'Something went wrong', {
  details: { field: 'username' }
});
WebSocketUtils.send(ws, customError);
```

#### Event Messages (Broadcasts)

```typescript
// Before (Legacy)
connectedSockets.forEach(socket => {
  socket.send(JSON.stringify({
    type: 'USER_JOINED',
    data: { userId: '456', username: 'john' },
    timestamp: new Date().toISOString()
  }));
});

// After (Standardized)
const eventMessage = WebSocketUtils.event('USER_JOINED', {
  userId: '456',
  username: 'john'
});
WebSocketUtils.broadcast(connectedSockets, eventMessage);
```

### Step 4: Update Message Handling

#### Message Validation

```typescript
// Before (Legacy)
websocket.message = (ws, message) => {
  try {
    const data = JSON.parse(message.toString());
    // Manual validation...
  } catch (error) {
    ws.send(JSON.stringify({ error: 'Invalid JSON' }));
  }
};

// After (Standardized)
import { WebSocketValidator } from '../utils/websocket-validator';

websocket.message = (ws, message) => {
  const validation = WebSocketValidator.validate(message);
  
  if (!validation.isValid) {
    const errorMessage = WebSocketUtils.validationError(validation.errors!);
    WebSocketUtils.send(ws, errorMessage);
    return;
  }
  
  // Process validated message...
};
```

#### Correlation Tracking

```typescript
// Before (Legacy) - No correlation support
ws.send(JSON.stringify({ type: 'REQUEST', data: requestData }));

// After (Standardized)
import { CorrelationTracker } from '../utils/correlation-tracker';

const correlationId = CorrelationTracker.generateId();
const message = WebSocketUtils.success('REQUEST', requestData, { correlationId });

WebSocketUtils.send(ws, message);

// Handle response with correlation
const response = await CorrelationTracker.waitForResponse(ws, correlationId, 5000);
```

### Step 5: Update Handler Functions

#### Authentication Handler

```typescript
// Before (Legacy)
export async function handleAuth(ws: ServerWebSocket<CustomWebSocketData>, data: any) {
  try {
    const user = await authenticateUser(data.token);
    ws.send(JSON.stringify({
      type: 'AUTH_SUCCESS',
      data: { userId: user.id, username: user.username }
    }));
  } catch (error) {
    ws.send(JSON.stringify({
      type: 'AUTH_ERROR',
      error: 'Authentication failed'
    }));
  }
}

// After (Standardized)
export async function handleAuth(ws: ServerWebSocket<CustomWebSocketData>, data: any) {
  try {
    const user = await authenticateUser(data.token);
    const successMessage = WebSocketUtils.success('AUTH_SUCCESS', {
      userId: user.id,
      username: user.username
    });
    WebSocketUtils.send(ws, successMessage);
  } catch (error) {
    const errorMessage = WebSocketUtils.error('AUTH_FAILED', 'Authentication failed');
    WebSocketUtils.send(ws, errorMessage);
  }
}
```

#### Message Handler

```typescript
// Before (Legacy)
export async function handleMessage(ws: ServerWebSocket<CustomWebSocketData>, data: any) {
  const message = await createMessage(data);
  
  // Broadcast to channel
  const channelSockets = getChannelSockets(data.channelId);
  channelSockets.forEach(socket => {
    socket.send(JSON.stringify({
      type: 'MESSAGE_RECEIVED',
      data: message
    }));
  });
}

// After (Standardized)
export async function handleMessage(ws: ServerWebSocket<CustomWebSocketData>, data: any) {
  const message = await createMessage(data);
  
  // Broadcast to channel using WebSocketUtils
  const eventMessage = WebSocketUtils.event('MESSAGE_RECEIVED', message);
  WebSocketUtils.sendToChannel(data.channelId, eventMessage, websocketManager);
}
```

## Configuration

### Environment Variables

Add these environment variables to configure WebSocket behavior:

```bash
# .env
WS_ENABLE_STANDARDIZATION=true
WS_ENABLE_LEGACY_SUPPORT=true
WS_MIGRATION_MODE=gradual
WS_LOG_LEVEL=info
WS_ENABLE_VALIDATION=true
WS_ENABLE_CORRELATION=true
```

### Configuration File

Create or update your WebSocket configuration:

```typescript
import { loadWebSocketConfig } from './config/websocket.config';

const wsConfig = loadWebSocketConfig();

// Use configuration in your WebSocket setup
const websocketManager = new EnhancedWebSocketManager(wsConfig);
```

## Migration Strategies

### Gradual Migration (Recommended)

1. **Phase 1**: Enable standardization with legacy support
   ```typescript
   const config = {
     enableStandardization: true,
     enableLegacySupport: true,
     migrationMode: 'gradual'
   };
   ```

2. **Phase 2**: Migrate handlers one by one
   - Start with new features
   - Update existing handlers gradually
   - Test thoroughly at each step

3. **Phase 3**: Disable legacy support
   ```typescript
   const config = {
     enableStandardization: true,
     enableLegacySupport: false,
     migrationMode: 'forced'
   };
   ```

### Forced Migration

For new projects or complete rewrites:

```typescript
const config = {
  enableStandardization: true,
  enableLegacySupport: false,
  migrationMode: 'forced'
};
```

## Testing Migration

### Unit Tests

Update your tests to use the new format:

```typescript
// Before (Legacy)
test('should send message', () => {
  const mockWs = createMockWebSocket();
  handleMessage(mockWs, { content: 'test' });
  
  expect(mockWs.send).toHaveBeenCalledWith(
    JSON.stringify({ type: 'MESSAGE_SENT', data: expect.any(Object) })
  );
});

// After (Standardized)
test('should send standardized message', () => {
  const mockWs = createMockWebSocket();
  handleMessage(mockWs, { content: 'test' });
  
  const sentMessage = JSON.parse(mockWs.send.mock.calls[0][0]);
  expect(sentMessage).toMatchObject({
    success: true,
    type: 'MESSAGE_SENT',
    data: expect.any(Object),
    meta: {
      timestamp: expect.any(String),
      messageId: expect.any(String),
      version: '1.0.0',
      source: 'server'
    }
  });
});
```

### Integration Tests

Test the migration compatibility:

```typescript
test('should handle both legacy and standardized messages', async () => {
  const ws = await createTestWebSocket();
  
  // Test legacy format
  ws.send(JSON.stringify({ type: 'LEGACY_MESSAGE', data: 'test' }));
  
  // Test standardized format
  const standardMessage = WebSocketUtils.success('STANDARD_MESSAGE', 'test');
  ws.send(JSON.stringify(standardMessage));
  
  // Both should work during migration period
  expect(ws.received).toHaveLength(2);
});
```

## Common Pitfalls

### 1. Forgetting Correlation IDs

```typescript
// Wrong - No correlation for request-response
const message = WebSocketUtils.success('REQUEST', data);

// Correct - Include correlation ID
const correlationId = CorrelationTracker.generateId();
const message = WebSocketUtils.success('REQUEST', data, { correlationId });
```

### 2. Manual JSON Stringification

```typescript
// Wrong - Manual JSON handling
ws.send(JSON.stringify(WebSocketUtils.success('TEST', data)));

// Correct - Use WebSocketUtils.send
WebSocketUtils.send(ws, WebSocketUtils.success('TEST', data));
```

### 3. Inconsistent Error Handling

```typescript
// Wrong - Custom error format
ws.send(JSON.stringify({ error: 'Something failed' }));

// Correct - Standardized error
const errorMessage = WebSocketUtils.error('OPERATION_FAILED', 'Something failed');
WebSocketUtils.send(ws, errorMessage);
```

### 4. Missing Message Validation

```typescript
// Wrong - No validation
websocket.message = (ws, message) => {
  const data = JSON.parse(message.toString());
  handleMessage(ws, data);
};

// Correct - Validate first
websocket.message = (ws, message) => {
  const validation = WebSocketValidator.validate(message);
  if (!validation.isValid) {
    const errorMessage = WebSocketUtils.validationError(validation.errors!);
    WebSocketUtils.send(ws, errorMessage);
    return;
  }
  handleMessage(ws, validation.sanitizedData);
};
```

## Rollback Plan

If issues arise during migration:

1. **Immediate Rollback**:
   ```typescript
   const config = {
     enableStandardization: false,
     enableLegacySupport: true,
     migrationMode: 'disabled'
   };
   ```

2. **Partial Rollback**:
   ```typescript
   const config = {
     enableStandardization: true,
     enableLegacySupport: true,
     migrationMode: 'gradual',
     strictValidation: false
   };
   ```

3. **Debug Mode**:
   ```typescript
   const config = {
     logLevel: 'debug',
     enableMessageLogging: true,
     enableTracing: true
   };
   ```

## Support and Troubleshooting

### Common Issues

1. **Message Format Errors**: Enable debug logging to see message structure
2. **Validation Failures**: Check message schemas and data types
3. **Correlation Timeouts**: Increase timeout or check response handling
4. **Performance Issues**: Disable unnecessary features during migration

### Debug Configuration

```typescript
const debugConfig = {
  logLevel: 'debug',
  enableMessageLogging: true,
  enableTracing: true,
  enablePerformanceMetrics: true,
  strictValidation: false
};
```

### Getting Help

- Check the API documentation for detailed method signatures
- Review example implementations in the `examples/` directory
- Run the test suite to verify your implementation
- Enable debug logging to troubleshoot issues

## Next Steps

After successful migration:

1. Remove legacy code and dependencies
2. Enable strict validation for better error catching
3. Implement advanced features like message compression
4. Set up monitoring and metrics collection
5. Update client-side code to use standardized formats

This migration ensures your WebSocket implementation is robust, maintainable, and ready for future enhancements.