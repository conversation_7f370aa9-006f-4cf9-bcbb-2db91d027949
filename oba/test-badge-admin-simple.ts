#!/usr/bin/env bun

/**
 * Simple Badge Administration System Test
 * 
 * This script tests the badge administration features by directly using database utilities
 * to bypass validation issues while testing the core admin functionality.
 */

import { db } from "./db";
import { BadgeAdminService } from "./services/badge-admin.service";
import {
  getBadgeAuditLog,
  getAdminBadgeStats,
  getBadgeSystemHealthCheck,
  cleanupOrphanedBadgeData,
  getUserBadgeSummary
} from "./db/utils/badge-admin-utils";
import {
  createBadgeType,
  assignBadgeToUser,
  bulkAssignBadges
} from "./db/utils/badge-utils";
import type {
  CreateBadgeTypeRequest,
  BadgeAuditLogFilters
} from "./types/badge.types";

// Test configuration with proper UUIDs
const TEST_CONFIG = {
  testUserId: "01983445-0000-7000-8000-000000000001",
  adminUserId: "01983445-0000-7000-8000-000000000002",
  testServerId: "01983445-0000-7000-8000-000000000003"
};

// Colors for console output
const colors = {
  reset: "\x1b[0m",
  bright: "\x1b[1m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  cyan: "\x1b[36m"
};

function log(message: string, color: string = colors.reset): void {
  console.log(`${color}${message}${colors.reset}`);
}

function logSection(title: string): void {
  log(`\n${"=".repeat(60)}`, colors.cyan);
  log(`${title}`, colors.bright + colors.cyan);
  log(`${"=".repeat(60)}`, colors.cyan);
}

function logTest(testName: string): void {
  log(`\n🧪 Testing: ${testName}`, colors.yellow);
}

function logSuccess(message: string): void {
  log(`✅ ${message}`, colors.green);
}

function logError(message: string): void {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message: string): void {
  log(`ℹ️  ${message}`, colors.blue);
}

// Initialize admin service
const badgeAdminService = new BadgeAdminService(db);

/**
 * Test 1: Create test badge directly using database utility
 */
async function createTestBadge(): Promise<string> {
  logTest("Creating Test Badge for Admin Operations");

  try {
    const badgeRequest: CreateBadgeTypeRequest = {
      badgeId: "admin-test-badge",
      name: "Admin Test Badge",
      title: "Administrative Test",
      description: "A badge created for testing admin functionality",
      icon: "🛡️",
      tooltip: "Test badge for admin operations",
      design: {
        shape: "shield",
        background: "#ff6b6b",
        colors: ["#ff6b6b", "#4ecdc4"]
      },
      criteria: {
        requirement: "Manual assignment only",
        tracked: "manual_assignment"
      },
      perks: ["admin_recognition"],
      unlockType: "manual",
      visualDescription: "A shield-shaped test badge",
      animation: "glow",
      displayOrder: 1,
      category: "special"
    };

    const badge = await createBadgeType(db, badgeRequest);
    logSuccess(`Created test badge: ${badge.name} (ID: ${badge.id})`);
    return badge.id;
  } catch (error) {
    logError(`Failed to create test badge: ${error.message}`);
    throw error;
  }
}

/**
 * Test 2: Badge Assignment Audit Logging
 */
async function testBadgeAuditLogging(badgeTypeId: string): Promise<void> {
  logTest("Badge Assignment Audit Logging");

  try {
    // Create test badge assignments with proper UUIDs
    const testUsers = [
      "01983445-0000-7000-8000-000000000011",
      "01983445-0000-7000-8000-000000000012", 
      "01983445-0000-7000-8000-000000000013"
    ];
    
    for (const userId of testUsers) {
      await assignBadgeToUser(db, userId, badgeTypeId, TEST_CONFIG.adminUserId);
      logInfo(`Assigned badge to user: ${userId}`);
    }

    // Test audit log retrieval
    const auditFilters: BadgeAuditLogFilters = {
      badgeTypeId,
      assignedBy: TEST_CONFIG.adminUserId,
      limit: 10,
      offset: 0
    };

    const auditLog = await getBadgeAuditLog(db, auditFilters);
    logSuccess(`Retrieved ${auditLog.entries.length} audit log entries`);
    logInfo(`Total entries: ${auditLog.total}, Has more: ${auditLog.hasMore}`);

    // Display sample entries
    if (auditLog.entries.length > 0) {
      logInfo("Sample audit entries:");
      auditLog.entries.slice(0, 3).forEach((entry, index) => {
        log(`  ${index + 1}. User: ${entry.username}, Badge: ${entry.badgeName}, Date: ${entry.assignedAt.toISOString()}`);
      });
    }

  } catch (error) {
    logError(`Badge audit logging failed: ${error.message}`);
    throw error;
  }
}

/**
 * Test 3: Admin Badge Statistics
 */
async function testAdminBadgeStats(): Promise<void> {
  logTest("Admin Badge Statistics Dashboard");

  try {
    const stats = await getAdminBadgeStats(db);
    
    logSuccess("Retrieved comprehensive badge statistics");
    logInfo(`Total Badge Types: ${stats.totalBadgeTypes}`);
    logInfo(`Active Badge Types: ${stats.activeBadgeTypes}`);
    logInfo(`Total User Badges: ${stats.totalUserBadges}`);
    logInfo(`Recent Assignments: ${stats.recentAssignments}`);

    // Display category distribution
    if (stats.categoryDistribution.length > 0) {
      logInfo("Category Distribution:");
      stats.categoryDistribution.forEach(item => {
        log(`  ${item.category}: ${item.count} badges`);
      });
    }

    // Display most assigned badges
    if (stats.mostAssignedBadges.length > 0) {
      logInfo("Most Assigned Badges:");
      stats.mostAssignedBadges.slice(0, 3).forEach((item, index) => {
        log(`  ${index + 1}. ${item.badgeName}: ${item.assignmentCount} assignments`);
      });
    }

  } catch (error) {
    logError(`Admin badge statistics failed: ${error.message}`);
    throw error;
  }
}

/**
 * Test 4: Bulk Badge Assignment
 */
async function testBulkBadgeAssignment(badgeTypeId: string): Promise<void> {
  logTest("Bulk Badge Assignment");

  try {
    const bulkAssignments = [
      { userId: "01983445-0000-7000-8000-000000000021", badgeTypeId, assignedBy: TEST_CONFIG.adminUserId },
      { userId: "01983445-0000-7000-8000-000000000022", badgeTypeId, assignedBy: TEST_CONFIG.adminUserId },
      { userId: "01983445-0000-7000-8000-000000000023", badgeTypeId, assignedBy: TEST_CONFIG.adminUserId }
    ];

    const assignedBadges = await bulkAssignBadges(db, bulkAssignments);
    logSuccess(`Bulk assigned ${assignedBadges.length} badges`);

    // Verify assignments
    assignedBadges.forEach((badge, index) => {
      logInfo(`✓ Assigned badge to user: ${bulkAssignments[index].userId}`);
    });

  } catch (error) {
    logError(`Bulk badge assignment failed: ${error.message}`);
    throw error;
  }
}

/**
 * Test 5: Badge System Health Check
 */
async function testBadgeSystemHealthCheck(): Promise<void> {
  logTest("Badge System Health Check");

  try {
    const healthCheck = await getBadgeSystemHealthCheck(db);
    
    logSuccess("Health check completed");
    logInfo(`Health Score: ${healthCheck.healthScore}/100`);
    logInfo(`System Status: ${healthCheck.status.toUpperCase()}`);

    // Display metrics
    logInfo("Health Metrics:");
    log(`  Orphaned Badges: ${healthCheck.metrics.orphanedBadges}`);
    log(`  Unassigned Badge Types: ${healthCheck.metrics.unassignedBadgeTypes}`);
    log(`  Inconsistent Collections: ${healthCheck.metrics.inconsistentCollections}`);

    // Display issues and warnings
    if (healthCheck.issues.length > 0) {
      logError("Issues Found:");
      healthCheck.issues.forEach((issue, index) => {
        log(`  ${index + 1}. ${issue}`, colors.red);
      });
    } else {
      logSuccess("No critical issues found");
    }

    if (healthCheck.warnings.length > 0) {
      log("Warnings:", colors.yellow);
      healthCheck.warnings.forEach((warning, index) => {
        log(`  ${index + 1}. ${warning}`, colors.yellow);
      });
    }

  } catch (error) {
    logError(`Health check failed: ${error.message}`);
    throw error;
  }
}

/**
 * Test 6: User Badge Summary
 */
async function testUserBadgeSummary(): Promise<void> {
  logTest("User Badge Summary");

  try {
    const userSummary = await getUserBadgeSummary(db, TEST_CONFIG.testUserId);
    
    logSuccess(`Retrieved badge summary for user: ${userSummary.user.username}`);
    logInfo(`Total Badges: ${userSummary.statistics.totalBadges}`);
    logInfo(`Visible Badges: ${userSummary.statistics.visibleBadges}`);
    logInfo(`Manual Badges: ${userSummary.statistics.manualBadges}`);

    // Display recent badges
    if (userSummary.badges.length > 0) {
      logInfo("Recent Badges:");
      userSummary.badges.slice(0, 3).forEach((badge, index) => {
        log(`  ${index + 1}. ${badge.badgeName} (${badge.badgeCategory})`);
      });
    }

  } catch (error) {
    logError(`User badge summary failed: ${error.message}`);
    throw error;
  }
}

/**
 * Test 7: Admin Service Integration
 */
async function testAdminServiceIntegration(): Promise<void> {
  logTest("Admin Service Integration");

  try {
    // Test admin service methods
    const auditFilters: BadgeAuditLogFilters = {
      limit: 5,
      offset: 0
    };

    const auditResult = await badgeAdminService.getBadgeAuditLog(TEST_CONFIG.adminUserId, auditFilters);
    logSuccess(`Admin service retrieved ${auditResult.entries.length} audit entries`);

    const adminStats = await badgeAdminService.getAdminBadgeStats(TEST_CONFIG.adminUserId);
    logSuccess(`Admin service retrieved statistics`);

    const healthCheck = await badgeAdminService.getBadgeSystemHealthCheck(TEST_CONFIG.adminUserId);
    logSuccess(`Admin service health check: ${healthCheck.healthScore}/100`);

    const userSummary = await badgeAdminService.getUserBadgeSummary(TEST_CONFIG.adminUserId, TEST_CONFIG.testUserId);
    logSuccess(`Admin service user summary: ${userSummary.statistics.totalBadges} badges`);

    const cleanupResult = await badgeAdminService.cleanupOrphanedBadgeData(TEST_CONFIG.adminUserId);
    logSuccess(`Admin service cleanup: ${cleanupResult.cleanedItems} items processed`);

  } catch (error) {
    logError(`Admin service integration failed: ${error.message}`);
    throw error;
  }
}

/**
 * Main test execution
 */
async function runSimpleBadgeAdminTests(): Promise<void> {
  logSection("Simple Badge Administration System Test");
  
  let badgeTypeId: string;

  try {
    // Create test badge
    badgeTypeId = await createTestBadge();

    // Test admin functionality
    await testBadgeAuditLogging(badgeTypeId);
    await testAdminBadgeStats();
    await testBulkBadgeAssignment(badgeTypeId);
    await testBadgeSystemHealthCheck();
    await testUserBadgeSummary();
    await testAdminServiceIntegration();

    logSection("All Badge Administration Tests Completed Successfully!");
    logSuccess("✅ Badge assignment audit logging");
    logSuccess("✅ Badge statistics dashboard data");
    logSuccess("✅ Bulk badge assignment capabilities");
    logSuccess("✅ Badge system health check and monitoring");
    logSuccess("✅ Admin service integration");

  } catch (error) {
    logSection("Badge Administration Test Failed");
    logError(`Test execution failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run the tests
if (import.meta.main) {
  runSimpleBadgeAdminTests();
}