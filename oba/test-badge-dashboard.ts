#!/usr/bin/env bun

/**
 * Badge Dashboard Test Suite
 * Tests badge progress tracking and user dashboard features
 */

import { db } from "./db";
import { BadgeService } from "./services/badge.service";
import type {
  CreateBadgeTypeRequest,
  BadgeProgress,
  UserStats
} from "./types/badge.types";

const badgeService = new BadgeService(db);

// Test user IDs (these should exist in your test database)
const TEST_USER_ID = "01234567-89ab-cdef-0123-456789abcdef";
const TEST_ADMIN_ID = "01234567-89ab-cdef-0123-456789abcde0";

async function createTestBadgeTypes(): Promise<string[]> {
  console.log("🏗️  Creating test badge types...");
  
  const badgeTypes: CreateBadgeTypeRequest[] = [
    {
      badgeId: "first-message-test",
      name: "First Message",
      title: "Message Pioneer",
      description: "Send your first message in the community",
      icon: "💬",
      tooltip: "Awarded for sending your first message",
      design: {
        shape: "circle",
        background: "#4CAF50",
        colors: ["#4CAF50", "#81C784"]
      },
      criteria: {
        requirement: "Send 1 message",
        tracked: "message_count",
        type: "message_count",
        threshold: 1
      },
      unlockType: "automatic",
      category: "milestone",
      displayOrder: 1
    },
    {
      badgeId: "social-butterfly-test",
      name: "Social Butterfly",
      title: "Community Connector",
      description: "Make 5 friends in the community",
      icon: "🦋",
      tooltip: "Awarded for making 5 friends",
      design: {
        shape: "hexagon",
        background: "#E91E63",
        colors: ["#E91E63", "#F48FB1"]
      },
      criteria: {
        requirement: "Make 5 friends",
        tracked: "friend_count",
        type: "friend_count",
        threshold: 5
      },
      unlockType: "automatic",
      category: "community",
      displayOrder: 2
    },
    {
      badgeId: "server-explorer-test",
      name: "Server Explorer",
      title: "Digital Nomad",
      description: "Join 3 different servers",
      icon: "🗺️",
      tooltip: "Awarded for joining 3 servers",
      design: {
        shape: "star",
        background: "#FF9800",
        colors: ["#FF9800", "#FFB74D"]
      },
      criteria: {
        requirement: "Join 3 servers",
        tracked: "server_count",
        type: "server_count",
        threshold: 3
      },
      unlockType: "automatic",
      category: "achievement",
      displayOrder: 3
    },
    {
      badgeId: "veteran-test",
      name: "Veteran",
      title: "Old Timer",
      description: "Be active for 30 days",
      icon: "🏆",
      tooltip: "Awarded for 30 days of activity",
      design: {
        shape: "shield",
        background: "#9C27B0",
        colors: ["#9C27B0", "#CE93D8"]
      },
      criteria: {
        requirement: "Be active for 30 days",
        tracked: "days_active",
        type: "days_active",
        threshold: 30
      },
      unlockType: "automatic",
      category: "milestone",
      displayOrder: 4
    },
    {
      badgeId: "early-adopter-test",
      name: "Early Adopter",
      title: "Pioneer",
      description: "One of the first 100 users to join",
      icon: "🚀",
      tooltip: "Awarded to early community members",
      design: {
        shape: "diamond",
        background: "#3F51B5",
        colors: ["#3F51B5", "#7986CB"]
      },
      criteria: {
        requirement: "Be among first 100 users",
        tracked: "signup_order",
        type: "custom",
        conditions: {
          signup_order: 100
        }
      },
      unlockType: "automatic",
      category: "special",
      displayOrder: 5
    },
    {
      badgeId: "community-helper-test",
      name: "Community Helper",
      title: "Helpful Soul",
      description: "Manually awarded for helping others",
      icon: "🤝",
      tooltip: "Awarded by moderators for helping community members",
      design: {
        shape: "heart",
        background: "#F44336",
        colors: ["#F44336", "#EF5350"]
      },
      criteria: {
        requirement: "Help community members",
        tracked: "manual"
      },
      unlockType: "manual",
      category: "community",
      displayOrder: 6
    }
  ];

  const createdBadgeIds: string[] = [];
  
  for (const badgeType of badgeTypes) {
    try {
      const created = await badgeService.createBadgeType(badgeType, TEST_ADMIN_ID);
      createdBadgeIds.push(created.id);
      console.log(`✅ Created badge: ${created.name}`);
    } catch (error) {
      console.error(`❌ Failed to create badge ${badgeType.name}:`, error.message);
    }
  }
  
  return createdBadgeIds;
}

async function testBadgeProgress(): Promise<void> {
  console.log("\n📊 Testing badge progress calculation...");
  
  try {
    // Get badge progress for test user
    const progress = await badgeService.getBadgeProgress(TEST_USER_ID);
    
    console.log(`📈 Found ${progress.length} badges with progress tracking`);
    
    for (const p of progress) {
      const percentage = Math.round((p.progress / p.total) * 100);
      const status = p.isEarned ? "✅ EARNED" : "⏳ IN PROGRESS";
      
      console.log(`  ${p.badgeType.icon} ${p.badgeType.name}: ${p.progress}/${p.total} (${percentage}%) ${status}`);
      
      if (p.badgeType.criteria.type) {
        console.log(`    Criteria: ${p.badgeType.criteria.requirement}`);
      }
    }
    
    // Test specific badge progress
    if (progress.length > 0) {
      const specificProgress = await badgeService.getBadgeProgress(TEST_USER_ID, progress[0].badgeTypeId);
      console.log(`🎯 Specific badge progress for ${progress[0].badgeType.name}: ${specificProgress.length} result(s)`);
    }
    
  } catch (error) {
    console.error("❌ Badge progress test failed:", error.message);
  }
}

async function testUserStats(): Promise<void> {
  console.log("\n📊 Testing user statistics calculation...");
  
  try {
    const userStats = await badgeService.getUserStats(TEST_USER_ID);
    
    console.log("📈 User Statistics:");
    console.log(`  Messages: ${userStats.messageCount}`);
    console.log(`  Servers: ${userStats.serverCount}`);
    console.log(`  Friends: ${userStats.friendCount}`);
    console.log(`  Days Active: ${userStats.daysActive}`);
    console.log(`  Account Age: ${userStats.accountAge} days`);
    console.log(`  Invites Sent: ${userStats.invitesSent}`);
    console.log(`  Invites Accepted: ${userStats.invitesAccepted}`);
    console.log(`  Signup Order: ${userStats.signupOrder || 'Unknown'}`);
    console.log(`  Last Active: ${userStats.lastActive.toISOString()}`);
    
  } catch (error) {
    console.error("❌ User stats test failed:", error.message);
  }
}

async function testBadgeDashboard(): Promise<void> {
  console.log("\n🎛️  Testing badge dashboard functionality...");
  
  try {
    // This would test the dashboard handler, but we need to simulate HTTP requests
    // For now, we'll test the underlying service methods
    
    const userBadges = await badgeService.getUserBadges(TEST_USER_ID);
    const availableBadges = await badgeService.getAvailableBadgesForUser(TEST_USER_ID);
    const progress = await badgeService.getBadgeProgress(TEST_USER_ID);
    
    console.log("🏆 Dashboard Summary:");
    console.log(`  Earned Badges: ${userBadges.length}`);
    console.log(`  Available Badges: ${availableBadges.length}`);
    console.log(`  Badges with Progress: ${progress.length}`);
    
    // Show earned badges
    if (userBadges.length > 0) {
      console.log("\n🏅 Earned Badges:");
      for (const badge of userBadges.slice(0, 5)) { // Show first 5
        const visibility = badge.isVisible ? "👁️  Visible" : "🙈 Hidden";
        console.log(`  ${badge.badgeType?.icon} ${badge.badgeType?.name} - ${visibility}`);
        console.log(`    Earned: ${badge.assignedAt.toISOString()}`);
        if (badge.assignedBy) {
          console.log(`    Assigned by: ${badge.assignedBy}`);
        }
      }
    }
    
    // Show available badges
    if (availableBadges.length > 0) {
      console.log("\n🎯 Available Badges:");
      for (const badge of availableBadges.slice(0, 3)) { // Show first 3
        console.log(`  ${badge.icon} ${badge.name}`);
        console.log(`    ${badge.description}`);
        console.log(`    Type: ${badge.unlockType}`);
      }
    }
    
    // Show progress
    const inProgressBadges = progress.filter(p => !p.isEarned && p.progress > 0);
    if (inProgressBadges.length > 0) {
      console.log("\n⏳ Badges in Progress:");
      for (const p of inProgressBadges.slice(0, 3)) { // Show first 3
        const percentage = Math.round((p.progress / p.total) * 100);
        console.log(`  ${p.badgeType.icon} ${p.badgeType.name}: ${percentage}%`);
        console.log(`    Progress: ${p.progress}/${p.total}`);
      }
    }
    
  } catch (error) {
    console.error("❌ Badge dashboard test failed:", error.message);
  }
}

async function testBadgeVisibility(): Promise<void> {
  console.log("\n👁️  Testing badge visibility controls...");
  
  try {
    const userBadges = await badgeService.getUserBadges(TEST_USER_ID);
    
    if (userBadges.length > 0) {
      const testBadge = userBadges[0];
      const originalVisibility = testBadge.isVisible;
      
      console.log(`🔄 Testing visibility toggle for badge: ${testBadge.badgeType?.name}`);
      console.log(`   Original visibility: ${originalVisibility ? "Visible" : "Hidden"}`);
      
      // Toggle visibility
      await badgeService.updateBadgeVisibility(
        TEST_USER_ID,
        testBadge.badgeTypeId,
        !originalVisibility
      );
      
      console.log(`✅ Visibility toggled to: ${!originalVisibility ? "Visible" : "Hidden"}`);
      
      // Toggle back
      await badgeService.updateBadgeVisibility(
        TEST_USER_ID,
        testBadge.badgeTypeId,
        originalVisibility
      );
      
      console.log(`🔄 Visibility restored to: ${originalVisibility ? "Visible" : "Hidden"}`);
      
    } else {
      console.log("⚠️  No badges found for visibility testing");
    }
    
  } catch (error) {
    console.error("❌ Badge visibility test failed:", error.message);
  }
}

async function testBadgeEvaluation(): Promise<void> {
  console.log("\n🔍 Testing automatic badge evaluation...");
  
  try {
    const evaluationResult = await badgeService.evaluateUserBadges(TEST_USER_ID);
    
    console.log("📊 Evaluation Results:");
    console.log(`  New badges awarded: ${evaluationResult.newBadges.length}`);
    console.log(`  Badges evaluated: ${evaluationResult.evaluatedBadges.length}`);
    console.log(`  Errors: ${evaluationResult.errors.length}`);
    
    if (evaluationResult.newBadges.length > 0) {
      console.log("\n🎉 New badges awarded:");
      for (const badge of evaluationResult.newBadges) {
        console.log(`  ${badge.badgeType?.icon} ${badge.badgeType?.name}`);
      }
    }
    
    if (evaluationResult.errors.length > 0) {
      console.log("\n⚠️  Evaluation errors:");
      for (const error of evaluationResult.errors) {
        console.log(`  ${error}`);
      }
    }
    
  } catch (error) {
    console.error("❌ Badge evaluation test failed:", error.message);
  }
}

async function cleanup(badgeTypeIds: string[]): Promise<void> {
  console.log("\n🧹 Cleaning up test data...");
  
  for (const badgeTypeId of badgeTypeIds) {
    try {
      await badgeService.deleteBadgeType(badgeTypeId, TEST_ADMIN_ID);
      console.log(`✅ Deleted badge type: ${badgeTypeId}`);
    } catch (error) {
      console.error(`❌ Failed to delete badge type ${badgeTypeId}:`, error.message);
    }
  }
}

async function runTests(): Promise<void> {
  console.log("🚀 Starting Badge Dashboard Test Suite");
  console.log("=====================================");
  
  let createdBadgeIds: string[] = [];
  
  try {
    // Create test badge types
    createdBadgeIds = await createTestBadgeTypes();
    
    // Run tests
    await testUserStats();
    await testBadgeProgress();
    await testBadgeDashboard();
    await testBadgeVisibility();
    await testBadgeEvaluation();
    
    console.log("\n✅ All badge dashboard tests completed!");
    
  } catch (error) {
    console.error("\n❌ Test suite failed:", error);
  } finally {
    // Cleanup
    if (createdBadgeIds.length > 0) {
      await cleanup(createdBadgeIds);
    }
  }
  
  console.log("\n🏁 Badge Dashboard Test Suite Complete");
}

// Run the tests
if (import.meta.main) {
  runTests().catch(console.error);
}