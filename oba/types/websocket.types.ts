import type { ServerWebSocket } from "bun";

/**
 * User interface for WebSocket data
 */
export interface IUser {
  id: string;
  name: string;
  avatar: string | null;
  speaking: boolean;
  muted: boolean;
  deafened: boolean;
}

/**
 * Custom WebSocket data interface
 * This is used to store data associated with a WebSocket connection
 */
export interface ICustomWebSocketData {
  userId: string;
  serverId?: string;
  channelId?: string;
  user?: IUser;
  token: string;
  isAuthenticated?: boolean;
  isAlive: boolean;
  type?: string;
  deviceId?: string;
  sessionId?: string;
}

/**
 * Type alias for a set of WebSockets
 */
export type WebSocketSet = Set<ServerWebSocket<ICustomWebSocketData>>;

/**
 * Type alias for a map of topic subscriptions
 */
export type TopicSubscriptionsMap = Map<string, WebSocketSet>;

// Backward compatibility exports
export type CustomWebSocketData = ICustomWebSocketData;
export type User = IUser;
