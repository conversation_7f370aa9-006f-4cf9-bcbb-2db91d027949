/**
 * WebSocket event types for the enhanced badge system
 */

import type {
  BadgeType,
  UserBadge,
  BadgeCollection,
  UserCollectionProgress,
  BadgeNomination,
  BadgeProgress,
  CollectionProgress,
} from "./badge.types";

/**
 * Badge-related WebSocket event types
 */
export interface BadgeWebSocketEvents {
  // Badge assignment events
  BADGE_ASSIGNED: {
    userId: string;
    badge: UserBadge;
    badgeType: BadgeType;
    isNew: boolean;
    collectionProgress?: UserCollectionProgress;
    perksGranted?: string[];
  };

  BADGE_REMOVED: {
    userId: string;
    badgeTypeId: string;
    badgeType: BadgeType;
    reason?: string;
    perksRevoked?: string[];
  };

  // Progress tracking events
  BADGE_PROGRESS_UPDATE: {
    userId: string;
    badgeTypeId: string;
    badgeType: BadgeType;
    progress: number;
    total: number;
    percentComplete: number;
    collectionProgress?: {
      collectionId: string;
      collectionName: string;
      currentBadge: number;
      totalBadges: number;
      nextBadge?: BadgeType;
    };
  };

  // Collection events
  COLLECTION_PROGRESS_UPDATE: {
    userId: string;
    collectionId: string;
    collection: BadgeCollection;
    progress: UserCollectionProgress;
    earnedBadges: UserBadge[];
    nextBadge?: BadgeType;
  };

  COLLECTION_COMPLETED: {
    userId: string;
    collectionId: string;
    collection: BadgeCollection;
    completionReward?: {
      badge: BadgeType;
      perks: string[];
    };
    totalBadgesEarned: number;
  };

  // Nomination events
  BADGE_NOMINATED: {
    nominationId: string;
    nomination: BadgeNomination;
    nomineeUserId: string;
    nominatorUserId: string;
    badgeType: BadgeType;
  };

  NOMINATION_APPROVED: {
    nominationId: string;
    nomination: BadgeNomination;
    badgeAssigned: UserBadge;
    approvedBy: string;
  };

  NOMINATION_REJECTED: {
    nominationId: string;
    nomination: BadgeNomination;
    rejectedBy: string;
    reason?: string;
  };

  // Evaluation events
  BADGE_EVALUATION_STARTED: {
    userId: string;
    evaluationId: string;
    badgeTypesEvaluated: string[];
  };

  BADGE_EVALUATION_COMPLETED: {
    userId: string;
    evaluationId: string;
    newBadges: UserBadge[];
    updatedCollections: UserCollectionProgress[];
    totalBadgesEarned: number;
  };

  // Admin events
  BADGE_TYPE_CREATED: {
    badgeType: BadgeType;
    createdBy: string;
    collectionId?: string;
  };

  BADGE_TYPE_UPDATED: {
    badgeTypeId: string;
    badgeType: BadgeType;
    updatedBy: string;
    changes: Record<string, any>;
  };

  BADGE_TYPE_DELETED: {
    badgeTypeId: string;
    deletedBy: string;
    affectedUsers: number;
  };

  COLLECTION_CREATED: {
    collection: BadgeCollection;
    createdBy: string;
  };

  COLLECTION_UPDATED: {
    collectionId: string;
    collection: BadgeCollection;
    updatedBy: string;
    changes: Record<string, any>;
  };

  COLLECTION_DELETED: {
    collectionId: string;
    deletedBy: string;
    affectedBadges: number;
    affectedUsers: number;
  };
}

/**
 * WebSocket event payload types for type safety
 */
export type BadgeWebSocketEventType = keyof BadgeWebSocketEvents;
export type BadgeWebSocketEventPayload<T extends BadgeWebSocketEventType> = BadgeWebSocketEvents[T];

/**
 * WebSocket message structure for badge events
 */
export interface BadgeWebSocketMessage<T extends BadgeWebSocketEventType = BadgeWebSocketEventType> {
  type: T;
  payload: BadgeWebSocketEventPayload<T>;
  timestamp: string;
  userId?: string;
  serverId?: string;
  channelId?: string;
}

/**
 * Badge notification preferences
 */
export interface BadgeNotificationSettings {
  badgeAssigned: boolean;
  badgeRemoved: boolean;
  progressUpdates: boolean;
  collectionCompleted: boolean;
  nominationReceived: boolean;
  nominationProcessed: boolean;
  evaluationCompleted: boolean;
}

/**
 * Real-time badge dashboard data
 */
export interface BadgeDashboardData {
  userBadges: UserBadge[];
  collectionProgress: CollectionProgress[];
  availableBadges: BadgeProgress[];
  recentActivity: BadgeActivity[];
  leaderboard: BadgeLeaderboardEntry[];
  notifications: BadgeNotification[];
}

/**
 * Badge activity for real-time feeds
 */
export interface BadgeActivity {
  id: string;
  type: 'badge_earned' | 'collection_completed' | 'nomination_received' | 'nomination_approved';
  userId: string;
  username: string;
  badgeType?: BadgeType;
  collection?: BadgeCollection;
  timestamp: Date;
  description: string;
}

/**
 * Badge leaderboard entry for real-time updates
 */
export interface BadgeLeaderboardEntry {
  userId: string;
  username: string;
  avatar?: string;
  badgeCount: number;
  collectionsCompleted: number;
  recentBadges: UserBadge[];
  rank: number;
  rankChange: number; // +1, -1, 0 for up, down, no change
}

/**
 * Badge notification for real-time alerts
 */
export interface BadgeNotification {
  id: string;
  type: 'badge_earned' | 'collection_completed' | 'nomination_received' | 'progress_milestone';
  title: string;
  message: string;
  badgeType?: BadgeType;
  collection?: BadgeCollection;
  isRead: boolean;
  timestamp: Date;
  actionUrl?: string;
}

/**
 * WebSocket subscription types for badge events
 */
export interface BadgeSubscriptionTypes {
  USER_BADGES: {
    userId: string;
  };
  COLLECTION_PROGRESS: {
    userId: string;
    collectionId?: string;
  };
  BADGE_NOMINATIONS: {
    userId: string; // For nominations where user is nominee or nominator
  };
  BADGE_LEADERBOARD: {
    serverId?: string; // Server-specific leaderboard
    global?: boolean; // Global leaderboard
  };
  BADGE_ACTIVITY_FEED: {
    serverId?: string;
    userId?: string;
  };
  ADMIN_BADGE_EVENTS: {
    adminUserId: string;
  };
}

/**
 * WebSocket subscription message
 */
export interface BadgeSubscriptionMessage {
  action: 'subscribe' | 'unsubscribe';
  type: keyof BadgeSubscriptionTypes;
  params: BadgeSubscriptionTypes[keyof BadgeSubscriptionTypes];
}

/**
 * Badge system metrics for real-time monitoring
 */
export interface BadgeSystemMetrics {
  totalBadgesAssigned: number;
  totalCollectionsCompleted: number;
  totalNominations: number;
  activeEvaluations: number;
  recentActivity: {
    badgesAssignedToday: number;
    collectionsCompletedToday: number;
    nominationsToday: number;
  };
  topBadges: Array<{
    badgeType: BadgeType;
    assignmentCount: number;
    trend: 'up' | 'down' | 'stable';
  }>;
  topCollections: Array<{
    collection: BadgeCollection;
    completionCount: number;
    averageCompletionTime: number;
  }>;
}

/**
 * Real-time badge evaluation status
 */
export interface BadgeEvaluationStatus {
  evaluationId: string;
  userId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number; // 0-100
  currentStep: string;
  badgesEvaluated: number;
  totalBadges: number;
  newBadgesFound: number;
  errors: string[];
  startedAt: Date;
  completedAt?: Date;
}

/**
 * Batch operation status for admin operations
 */
export interface BadgeBatchOperationStatus {
  operationId: string;
  type: 'bulk_assign' | 'bulk_remove' | 'bulk_evaluate' | 'collection_migrate';
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  totalItems: number;
  processedItems: number;
  successCount: number;
  errorCount: number;
  errors: Array<{
    item: string;
    error: string;
  }>;
  startedAt: Date;
  completedAt?: Date;
  startedBy: string;
}