// Badge system type definitions
export const BADGE_CATEGORIES = [
  "achievement",
  "role", 
  "special",
  "community",
  "milestone",
] as const;
export type BadgeCategory = (typeof BADGE_CATEGORIES)[number];

export const UNLOCK_TYPES = [
  "automatic", 
  "manual", 
  "peer_voted", 
  "manual_invitation"
] as const;
export type UnlockType = (typeof UNLOCK_TYPES)[number];

// Legacy compatibility - some files still use ASSIGNMENT_TYPES
export const ASSIGNMENT_TYPES = ["automatic", "manual"] as const;
export type AssignmentType = (typeof ASSIGNMENT_TYPES)[number];

export const COLLECTION_TYPES = ["progressive", "standalone"] as const;
export type CollectionType = (typeof COLLECTION_TYPES)[number];

export const BADGE_CRITERIA_TYPES = [
  "message_count",
  "server_count", 
  "friend_count",
  "days_active",
  "custom",
  "complex"
] as const;
export type BadgeCriteriaType = (typeof BADGE_CRITERIA_TYPES)[number];

// Enhanced badge collection interface
export interface BadgeCollection {
  id: string;
  collectionId: string;
  name: string;
  description: string;
  type: CollectionType;
  totalBadges: number;
  unlockedBy?: string;
  completionReward?: CompletionReward;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CompletionReward {
  badge: string;
  title: string;
  perks: string[];
  visual: string;
  animation: string;
}

// Enhanced badge design interface
export interface BadgeDesign {
  shape: string;
  background: string;
  colors: string[];
  gradient?: string;
  pattern?: string;
  elements?: string[];
}

// Enhanced badge type interface
export interface BadgeType {
  id: string;
  collectionId?: string;
  badgeId: string;
  name: string;
  title?: string;
  description: string;
  icon: string;
  tooltip?: string;
  design: BadgeDesign;
  criteria: BadgeCriteria;
  perks?: string[];
  unlockType: UnlockType;
  visualDescription?: string;
  animation?: string;
  displayOrder: number;
  category: BadgeCategory;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Enhanced user badge interface
export interface UserBadge {
  id: string;
  userId: string;
  badgeTypeId: string;
  collectionId?: string;
  assignedBy?: string;
  assignedAt: Date;
  isVisible: boolean;
  progressData?: Record<string, any>;
  perksGranted?: string[];
  badgeType?: BadgeType;
}

// Enhanced badge criteria interface
export interface BadgeCriteria {
  requirement: string;
  tracked: string;
  type?: BadgeCriteriaType;
  threshold?: number;
  conditions?: Record<string, any>;
  timeframe?: string;
}

// User collection progress interface
export interface UserCollectionProgress {
  id: string;
  userId: string;
  collectionId: string;
  collection?: BadgeCollection;
  badgesEarned: number;
  totalBadges: number;
  isCompleted: boolean;
  completionDate?: Date;
  completionRewardGranted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Badge nomination interface
export interface BadgeNomination {
  id: string;
  badgeTypeId: string;
  badgeType?: BadgeType;
  nomineeUserId: string;
  nominatorUserId: string;
  nominationReason?: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
  processedAt?: Date;
}

// Enhanced user statistics interface
export interface UserStats {
  messageCount: number;
  serverCount: number;
  friendCount: number;
  daysActive: number;
  accountAge: number;
  lastActive: Date;
  invitesSent: number;
  invitesAccepted: number;
  feedbackSubmitted: number;
  moderationActions: number;
  signupOrder?: number;
  geographicRegion?: string;
}

// Additional badge system interfaces
export interface BadgeStats {
  totalBadges: number;
  totalAssignments: number;
  categoryBreakdown: Record<BadgeCategory, number>;
  mostPopularBadges: Array<{
    badgeType: BadgeType;
    assignmentCount: number;
  }>;
}

export interface BadgeLeaderboard {
  userId: string;
  username: string;
  badgeCount: number;
  badges: UserBadge[];
}

// Request/Response types for API
export interface CreateBadgeCollectionRequest {
  collectionId: string;
  name: string;
  description: string;
  type: CollectionType;
  unlockedBy?: string;
  completionReward?: CompletionReward;
}

export interface CreateBadgeTypeRequest {
  collectionId?: string;
  badgeId: string;
  name: string;
  title?: string;
  description: string;
  icon?: string;
  tooltip?: string;
  design: BadgeDesign;
  criteria: BadgeCriteria;
  perks?: string[];
  unlockType: UnlockType;
  visualDescription?: string;
  animation?: string;
  displayOrder?: number;
  category: BadgeCategory;
}

export interface UpdateBadgeTypeRequest {
  collectionId?: string;
  badgeId?: string;
  name?: string;
  title?: string;
  description?: string;
  icon?: string;
  tooltip?: string;
  design?: BadgeDesign;
  criteria?: BadgeCriteria;
  perks?: string[];
  unlockType?: UnlockType;
  visualDescription?: string;
  animation?: string;
  displayOrder?: number;
  category?: BadgeCategory;
  isActive?: boolean;
}

export interface AssignBadgeRequest {
  badgeTypeId: string;
  userId: string;
}

export interface CreateNominationRequest {
  badgeTypeId: string;
  nomineeUserId: string;
  nominationReason?: string;
}

export interface BadgeTypeFilters {
  category?: BadgeCategory;
  unlockType?: UnlockType;
  assignmentType?: AssignmentType; // Legacy compatibility
  collectionId?: string;
  isActive?: boolean;
  search?: string;
}

export interface BadgeCollectionFilters {
  type?: CollectionType;
  isActive?: boolean;
  search?: string;
}

// Evaluation result types
export interface EvaluationResult {
  userId: string;
  newBadges: UserBadge[];
  evaluatedBadges: string[];
  collectionProgress: UserCollectionProgress[];
  errors: string[];
}

export interface BadgeProgress {
  badgeTypeId: string;
  badgeType: BadgeType;
  progress: number;
  total: number;
  isEarned: boolean;
  collectionProgress?: {
    collectionId: string;
    collectionName: string;
    currentBadge: number;
    totalBadges: number;
    nextBadge?: BadgeType;
  };
}

export interface CollectionProgress {
  collection: BadgeCollection;
  progress: UserCollectionProgress;
  earnedBadges: UserBadge[];
  nextBadge?: BadgeType;
  completionReward?: CompletionReward;
}

// Admin-specific types for badge management
export interface BulkBadgeAssignmentRequest {
  assignments: Array<{
    userId: string;
    badgeTypeId: string;
  }>;
  serverId?: string;
}

export interface BadgeAuditLogEntry {
  id: string;
  userId: string;
  username: string;
  badgeTypeId: string;
  badgeName: string;
  assignedBy?: string;
  assignedAt: Date;
  action: 'assigned' | 'removed';
  isVisible: boolean;
}

export interface BadgeAuditLogFilters {
  userId?: string;
  badgeTypeId?: string;
  assignedBy?: string;
  action?: 'assigned' | 'removed';
  startDate?: string;
  endDate?: string;
  limit: number;
  offset: number;
}

export interface BadgeAdminStats {
  totalBadgeTypes: number;
  activeBadgeTypes: number;
  totalUserBadges: number;
  totalCollections: number;
  activeCollections: number;
  recentAssignments: number;
  categoryDistribution: Array<{
    category: BadgeCategory;
    count: number;
  }>;
  unlockTypeDistribution: Array<{
    unlockType: UnlockType;
    count: number;
  }>;
  mostAssignedBadges: Array<{
    badgeTypeId: string;
    badgeName: string;
    assignmentCount: number;
  }>;
  topBadgeHolders: Array<{
    userId: string;
    username: string;
    badgeCount: number;
  }>;
}

export interface BadgeSystemHealthCheck {
  healthScore: number;
  status: 'healthy' | 'warning' | 'critical';
  issues: string[];
  warnings: string[];
  lastChecked: Date;
  metrics: {
    orphanedBadges: number;
    unassignedBadgeTypes: number;
    inconsistentCollections: number;
    inactiveBadgeAssignments: number;
    duplicateAssignments: number;
  };
}

export interface BadgeCleanupResult {
  cleanedItems: number;
  cleanupActions: string[];
  cleanupDate: Date;
}

export interface UserBadgeSummary {
  user: {
    id: string;
    username: string;
    createdAt: Date;
  };
  badges: Array<{
    id: string;
    badgeTypeId: string;
    badgeName: string;
    badgeCategory: BadgeCategory;
    unlockType: UnlockType;
    assignedBy?: string;
    assignedAt: Date;
    isVisible: boolean;
  }>;
  collectionProgress: Array<{
    collectionId: string;
    collectionName: string;
    badgesEarned: number;
    totalBadges: number;
    isCompleted: boolean;
    completionDate?: Date;
  }>;
  nominationsReceived: Array<{
    id: string;
    badgeTypeId: string;
    badgeName: string;
    nominatorUserId: string;
    status: 'pending' | 'approved' | 'rejected';
    createdAt: Date;
  }>;
  statistics: {
    totalBadges: number;
    visibleBadges: number;
    automaticBadges: number;
    manualBadges: number;
    completedCollections: number;
    categoryBreakdown: Record<string, number>;
  };
}

// Collection admin-specific types
export interface CreateBadgeCollectionRequest {
  collectionId: string;
  name: string;
  description: string;
  type?: CollectionType;
  totalBadges?: number;
  unlockedBy?: string;
  completionReward?: CompletionReward;
  isActive?: boolean;
}

export interface UpdateBadgeCollectionRequest {
  name?: string;
  description?: string;
  type?: CollectionType;
  totalBadges?: number;
  unlockedBy?: string;
  completionReward?: CompletionReward;
  isActive?: boolean;
}

export interface BulkBadgeAssignmentRequest {
  collectionId: string;
  userIds: string[];
  badgeIds?: string[];
  assignedBy: string;
}

export interface CollectionAnalytics {
  collection: BadgeCollection;
  totalBadges: number;
  totalUsers: number;
  averageProgress: number;
  completionRate: number;
  badgeDistribution: Array<{
    badgeId: string;
    badgeName: string;
    assignmentCount: number;
  }>;
}

export interface CollectionProgressReport {
  collection: BadgeCollection;
  userProgress: Array<{
    userId: string;
    badgesEarned: number;
    totalBadges: number;
    progressPercentage: number;
    isCompleted: boolean;
    completionDate?: Date;
  }>;
}

export interface CollectionTestResult {
  collectionId: string;
  collectionName: string;
  isValid: boolean;
  issues: string[];
  warnings: string[];
  badgeCount: number;
  testedAt: Date;
}