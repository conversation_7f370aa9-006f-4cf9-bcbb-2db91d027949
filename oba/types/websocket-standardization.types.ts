import type { ServerWebSocket } from "bun";
import type { CustomWebSocketData } from "./websocket.types";

/**
 * WebSocket Error Codes
 * Standardized error codes for WebSocket communications
 */
export enum WebSocketErrorCode {
  // Authentication errors
  AUTH_REQUIRED = "AUTH_REQUIRED",
  AUTH_FAILED = "AUTH_FAILED",
  TOKEN_EXPIRED = "TOKEN_EXPIRED",
  INVALID_TOKEN = "INVALID_TOKEN",

  // Permission errors
  PERMISSION_DENIED = "PERMISSION_DENIED",
  INSUFFICIENT_ROLE = "INSUFFICIENT_ROLE",
  ACCESS_FORBIDDEN = "ACCESS_FORBIDDEN",

  // Validation errors
  INVALID_MESSAGE_FORMAT = "INVALID_MESSAGE_FORMAT",
  INVALID_DATA = "INVALID_DATA",
  MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD",
  INVALID_MESSAGE_TYPE = "INVALID_MESSAGE_TYPE",
  SCHEMA_VALIDATION_FAILED = "SCHEMA_VALIDATION_FAILED",

  // Resource errors
  CHANNEL_NOT_FOUND = "CHANNEL_NOT_FOUND",
  SERVER_NOT_FOUND = "SERVER_NOT_FOUND",
  USER_NOT_FOUND = "USER_NOT_FOUND",
  MESSAGE_NOT_FOUND = "MESSAGE_NOT_FOUND",
  RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND",

  // Rate limiting
  RATE_LIMITED = "RATE_LIMITED",
  TOO_MANY_CONNECTIONS = "TOO_MANY_CONNECTIONS",
  TOO_MANY_REQUESTS = "TOO_MANY_REQUESTS",

  // System errors
  INTERNAL_ERROR = "INTERNAL_ERROR",
  SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE",
  MESSAGE_TOO_LARGE = "MESSAGE_TOO_LARGE",
  CONNECTION_ERROR = "CONNECTION_ERROR",
  TIMEOUT = "TIMEOUT",

  // Voice/Binary protocol errors
  VOICE_CHANNEL_FULL = "VOICE_CHANNEL_FULL",
  VOICE_NOT_SUPPORTED = "VOICE_NOT_SUPPORTED",
  BINARY_DATA_INVALID = "BINARY_DATA_INVALID",

  // Subscription errors
  SUBSCRIPTION_FAILED = "SUBSCRIPTION_FAILED",
  ALREADY_SUBSCRIBED = "ALREADY_SUBSCRIBED",
  NOT_SUBSCRIBED = "NOT_SUBSCRIBED",
}

/**
 * Message targeting information
 * Defines where a WebSocket message should be sent or who it's intended for
 */
export interface IWebSocketTarget {
  /** Target specific user by ID */
  userId?: string;
  /** Target specific channel by ID */
  channelId?: string;
  /** Target specific server by ID */
  serverId?: string;
  /** Target specific topic/subscription */
  topic?: string;
  /** Target specific role by ID */
  roleId?: string;
  /** Exclude specific user from broadcast */
  excludeUserId?: string;
  /** Custom targeting filter */
  filter?: string;
}

/**
 * Message metadata
 * Contains standardized metadata for all WebSocket messages
 */
export interface IWebSocketMeta {
  /** ISO timestamp when message was created */
  timestamp: number | Date;
  /** Unique identifier for this message */
  id: string;
  /** Correlation ID for request-response tracking */
  correlationId?: string;
  /** Message format version */
  version: string;
  /** Source of the message */
  source: "server" | "client";
  /** Optional trace ID for debugging */
  traceId?: string;
  /** Message priority level */
  priority?: "low" | "normal" | "high" | "critical";
  /** TTL for message expiration (in seconds) */
  ttl?: number;
}

/**
 * Base WebSocket message structure
 * All WebSocket messages should extend or implement this interface
 */
export interface IWebSocketMessage<T = unknown> {
  /** Message type identifier */
  type: string | number;
  /** Message payload data */
  data?: T;
  /** Message metadata */
  meta: IWebSocketMeta;
  /** Message targeting information */
  target?: IWebSocketTarget;
}

/**
 * Success message structure
 * Used for successful operations and responses
 */
export interface IWebSocketSuccessMessage<T = unknown>
  extends IWebSocketMessage<T> {
  /** Indicates successful operation */
  success: true;
  /** Success response data */
  data: T;
  /** Optional success message */
  message?: string;
}

/**
 * Error details structure
 * Provides detailed information about errors
 */
export interface IWebSocketErrorDetails {
  /** Standardized error code */
  code: WebSocketErrorCode;
  /** Human-readable error message */
  message: string;
  /** Additional error details */
  details?: {
    /** Field that caused the error */
    field?: string;
    /** Expected format or value */
    expectedFormat?: string;
    /** Received value that caused error */
    receivedValue?: unknown;
    /** Retry after seconds for rate limiting */
    retryAfter?: number;
    /** Maximum allowed size */
    maxSize?: number;
    /** Current size that exceeded limit */
    currentSize?: number;
    /** Validation errors array */
    validationErrors?: Array<{
      field: string;
      message: string;
      code: string;
    }>;
    /** Stack trace for debugging (development only) */
    stack?: string;
    /** Permission name for permission errors */
    permission?: string;
    /** Resource type for not found errors */
    resource?: string;
    /** Channel ID for channel-specific errors */
    channelId?: string;
    /** Server ID for server-specific errors */
    serverId?: string;
    /** User ID for user-specific errors */
    userId?: string;
  };
}

/**
 * Error message structure
 * Used for error responses and notifications
 */
export interface IWebSocketErrorMessage extends IWebSocketMessage {
  /** Indicates failed operation */
  success: false;
  /** Error information */
  error: IWebSocketErrorDetails;
}

/**
 * Event message structure
 * Used for broadcasting events and notifications
 */
export interface IWebSocketEventMessage<T = unknown>
  extends IWebSocketMessage<T> {
  /** Event name/type */
  event: string;
  /** Event payload data */
  data: T;
  /** Event category for filtering */
  category?: string;
  /** Event severity level */
  severity?: "info" | "warning" | "error" | "critical";
}

/**
 * Request message structure
 * Used for client requests that expect a response
 */
export interface IWebSocketRequestMessage<T = unknown>
  extends IWebSocketMessage<T> {
  /** Request payload data */
  data: T;
  /** Indicates this message expects a response */
  expectsResponse: true;
  /** Timeout for response in milliseconds */
  responseTimeout?: number;
}

/**
 * Response message structure
 * Used for server responses to client requests
 */
export interface IWebSocketResponseMessage<T = unknown>
  extends IWebSocketMessage<T> {
  /** Response payload data */
  data: T;
  /** Indicates this is a response message */
  isResponse: true;
  /** Reference to original request message ID */
  requestMessageId: string;
}

/**
 * Correlation tracking information
 * Used internally for request-response pattern matching
 */
export interface ICorrelationInfo {
  /** Original message ID */
  messageId: string;
  /** Timestamp when correlation was created */
  timestamp: number;
  /** Timeout handle for cleanup */
  timeout?: NodeJS.Timeout;
  /** Callback function for response handling */
  callback?: (response: IWebSocketMessage) => void;
  /** Promise resolver for async request-response */
  resolve?: (value: IWebSocketMessage) => void;
  /** Promise rejector for async request-response */
  reject?: (reason: any) => void;
}

/**
 * Enhanced WebSocket data interface
 * Extends the existing CustomWebSocketData with standardization features
 */
export interface IEnhancedWebSocketData extends CustomWebSocketData {
  /** Set of topic subscriptions */
  subscriptions: Set<string>;
  /** Last heartbeat timestamp */
  lastHeartbeat: number;
  /** Message count for rate limiting */
  messageCount: number;
  /** Rate limiting tokens available */
  rateLimitTokens: number;
  /** Correlation tracking map */
  correlationMap: Map<string, ICorrelationInfo>;
  /** Connection quality metrics */
  connectionMetrics?: {
    latency: number;
    packetsLost: number;
    bytesReceived: number;
    bytesSent: number;
  };
  /** Client capabilities */
  capabilities?: {
    supportsCompression: boolean;
    supportsBinaryProtocol: boolean;
    maxMessageSize: number;
    protocolVersion: string;
  };
  /** Session information */
  session?: {
    sessionId: string;
    startTime: number;
    lastActivity: number;
    userAgent?: string;
    ipAddress?: string;
  };
}

/**
 * Message validation result
 * Used by validation system to report validation outcomes
 */
export interface IValidationResult {
  /** Whether validation passed */
  isValid: boolean;
  /** Validation errors if any */
  errors?: Array<{
    field: string;
    message: string;
    code: string;
    value?: any;
  }>;
  /** Sanitized/processed data */
  sanitizedData?: any;
  /** Validation warnings (non-blocking) */
  warnings?: Array<{
    field: string;
    message: string;
    code: string;
  }>;
}

/**
 * Message type definition for registry
 * Defines characteristics and requirements for each message type
 */
export interface IMessageTypeDefinition {
  /** Message type identifier */
  type: string | number;
  /** Message category for grouping */
  category: string;
  /** Message direction */
  direction: "client_to_server" | "server_to_client" | "bidirectional";
  /** Whether authentication is required */
  requiresAuth: boolean;
  /** Required permission for this message type */
  requiresPermission?: string;
  /** Zod schema for data validation */
  dataSchema?: any;
  /** Expected response type */
  responseType?: string | number;
  /** Rate limiting group */
  rateLimitGroup?: string;
  /** Whether message supports correlation */
  supportsCorrelation?: boolean;
  /** Maximum message size in bytes */
  maxSize?: number;
  /** Message description */
  description?: string;
}

/**
 * WebSocket configuration interface
 * Defines configuration options for WebSocket standardization
 */
export interface IWebSocketConfig {
  /** Message format version */
  messageVersion: string;
  /** Heartbeat interval in milliseconds */
  heartbeatInterval: number;
  /** Connection timeout in milliseconds */
  connectionTimeout: number;
  /** Maximum message size in bytes */
  maxMessageSize: number;
  /** Rate limiting configuration */
  rateLimits: {
    [group: string]: {
      maxRequests: number;
      windowMs: number;
    };
  };
  /** Enable correlation tracking */
  enableCorrelation: boolean;
  /** Enable message validation */
  enableValidation: boolean;
  /** Enable message compression */
  enableCompression: boolean;
  /** Log level for WebSocket operations */
  logLevel: "debug" | "info" | "warn" | "error";
  /** Enable development features */
  developmentMode: boolean;
  /** Correlation cleanup interval in milliseconds */
  correlationCleanupInterval: number;
  /** Default correlation timeout in milliseconds */
  defaultCorrelationTimeout: number;
}

/**
 * WebSocket metrics interface
 * Used for monitoring and observability
 */
export interface IWebSocketMetrics {
  /** Messages processed per second */
  messagesPerSecond: number;
  /** Currently active connections */
  activeConnections: number;
  /** Error rate percentage */
  errorRate: number;
  /** Average response time in milliseconds */
  averageResponseTime: number;
  /** Rate limit hits count */
  rateLimitHits: number;
  /** Correlation timeouts count */
  correlationTimeouts: number;
  /** Total bytes sent */
  bytesSent: number;
  /** Total bytes received */
  bytesReceived: number;
  /** Connection establishment rate */
  connectionRate: number;
  /** Message validation failures */
  validationFailures: number;
}

/**
 * Type aliases for convenience
 */
export type EnhancedWebSocket = ServerWebSocket<IEnhancedWebSocketData>;
export type WebSocketSet = Set<EnhancedWebSocket>;
export type TopicSubscriptionsMap = Map<string, WebSocketSet>;
export type MessageTypeRegistry = Map<string, IMessageTypeDefinition>;

/**
 * Union type for all standardized message types
 */
export type StandardizedWebSocketMessage =
  | IWebSocketSuccessMessage
  | IWebSocketErrorMessage
  | IWebSocketEventMessage
  | IWebSocketRequestMessage
  | IWebSocketResponseMessage;

/**
 * Message handler function type
 */
export type WebSocketMessageHandler<T = unknown> = (
  ws: EnhancedWebSocket,
  message: IWebSocketMessage<T>,
) => void | Promise<void>;

/**
 * Message validator function type
 */
export type MessageValidator<T = unknown> = (data: T) => IValidationResult;
