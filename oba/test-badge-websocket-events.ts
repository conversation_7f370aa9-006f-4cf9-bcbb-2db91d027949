#!/usr/bin/env bun

/**
 * Test script for badge WebSocket events
 * This script tests the badge WebSocket event broadcasting functionality
 */

import { db } from "./db";
import { BadgeService } from "./services/badge.service";
import { BadgeEvaluationService } from "./services/badge-evaluation.service";
import { badgeWebSocketService } from "./utils/badge-websocket";
import { WebSocketManager } from "./manager/websocket.manager";
import type { BadgeType, CreateBadgeTypeRequest } from "./types/badge.types";

// Mock WebSocket connection for testing
const mockWebSocket = {
  data: {
    userId: "test-user-1",
    serverId: "test-server-1",
    channelId: "test-channel-1",
    isAuthenticated: true,
    isAlive: true,
    token: "mock-token"
  },
  readyState: 1, // WebSocket.OPEN
  send: (message: string) => {
    console.log("📤 WebSocket message sent:", JSON.parse(message));
  }
} as any;

async function testBadgeWebSocketEvents() {
  console.log("🧪 Testing Badge WebSocket Events\n");

  const badgeService = new BadgeService(db);
  const evaluationService = new BadgeEvaluationService(db);
  const wsManager = WebSocketManager.getInstance();

  try {
    // Add mock WebSocket to manager for testing
    wsManager.clients.add(mockWebSocket);
    wsManager.addPrivateConnection(mockWebSocket);

    console.log("1️⃣ Testing Badge Assignment Event");
    console.log("=====================================");

    // Create a test badge type
    const testBadgeData: CreateBadgeTypeRequest = {
      name: "WebSocket Test Badge",
      description: "A badge for testing WebSocket events",
      iconUrl: "https://example.com/test-badge.png",
      color: "#FF6B6B",
      category: "achievement",
      assignmentType: "manual",
      isActive: true
    };

    let testBadgeType: BadgeType;
    try {
      testBadgeType = await badgeService.createBadgeType(testBadgeData, "admin-user");
      console.log("✅ Test badge type created:", testBadgeType.name);
    } catch (error) {
      console.log("ℹ️ Badge type might already exist, continuing...");
      const existingBadges = await badgeService.getBadgeTypes({ search: testBadgeData.name });
      testBadgeType = existingBadges[0];
    }

    // Test manual badge assignment (should trigger BADGE_ASSIGNED event)
    console.log("\n📋 Testing manual badge assignment...");
    try {
      const assignedBadge = await badgeService.assignBadge(
        "test-user-1",
        testBadgeType.id,
        "admin-user"
      );
      console.log("✅ Badge assigned successfully");
      console.log("Expected WebSocket event: BADGE_ASSIGNED");
    } catch (error) {
      if (error.message.includes("already has this badge")) {
        console.log("ℹ️ User already has this badge, testing removal first...");
        
        // Remove badge first
        await badgeService.removeBadge("test-user-1", testBadgeType.id, "admin-user");
        console.log("✅ Badge removed (should have triggered BADGE_REMOVED event)");
        
        // Now assign it again
        const assignedBadge = await badgeService.assignBadge(
          "test-user-1",
          testBadgeType.id,
          "admin-user"
        );
        console.log("✅ Badge assigned successfully after removal");
      } else {
        throw error;
      }
    }

    console.log("\n2️⃣ Testing Automatic Badge Evaluation");
    console.log("=====================================");

    // Create an automatic badge for testing
    const autoBadgeData: CreateBadgeTypeRequest = {
      name: "First Message Badge",
      description: "Earned after sending your first message",
      iconUrl: "https://example.com/first-message.png",
      color: "#4ECDC4",
      category: "milestone",
      assignmentType: "automatic",
      isActive: true,
      criteria: {
        type: "message_count",
        threshold: 1
      }
    };

    let autoBadgeType: BadgeType;
    try {
      autoBadgeType = await badgeService.createBadgeType(autoBadgeData, "admin-user");
      console.log("✅ Automatic badge type created:", autoBadgeType.name);
    } catch (error) {
      console.log("ℹ️ Automatic badge type might already exist, continuing...");
      const existingBadges = await badgeService.getBadgeTypes({ search: autoBadgeData.name });
      autoBadgeType = existingBadges[0];
    }

    // Test automatic badge evaluation (should trigger BADGE_ASSIGNED event for automatic badges)
    console.log("\n🔄 Testing automatic badge evaluation...");
    const evaluationResult = await evaluationService.evaluateUser("test-user-1");
    console.log("✅ User evaluation completed");
    console.log("New badges assigned:", evaluationResult.newBadges.length);
    console.log("Expected WebSocket events: BADGE_ASSIGNED (for any new automatic badges)");

    console.log("\n3️⃣ Testing Badge Progress Updates");
    console.log("=====================================");

    // Test progress update broadcasting
    console.log("\n📊 Testing badge progress updates...");
    await evaluationService.broadcastProgressUpdates("test-user-1", false);
    console.log("✅ Progress updates broadcasted");
    console.log("Expected WebSocket events: BADGE_PROGRESS_UPDATE (for badges in progress)");

    console.log("\n4️⃣ Testing Bulk Operations");
    console.log("=====================================");

    // Test bulk badge assignment
    console.log("\n📦 Testing bulk badge assignment...");
    try {
      const bulkAssignments = [
        { userId: "test-user-2", badgeTypeId: testBadgeType.id },
        { userId: "test-user-3", badgeTypeId: testBadgeType.id }
      ];

      // Add mock WebSockets for additional users
      const mockWS2 = { ...mockWebSocket, data: { ...mockWebSocket.data, userId: "test-user-2" } };
      const mockWS3 = { ...mockWebSocket, data: { ...mockWebSocket.data, userId: "test-user-3" } };
      wsManager.clients.add(mockWS2);
      wsManager.clients.add(mockWS3);

      const bulkResults = await badgeService.bulkAssignBadges(
        bulkAssignments,
        "admin-user"
      );
      console.log("✅ Bulk badge assignment completed");
      console.log("Badges assigned:", bulkResults.length);
      console.log("Expected WebSocket events: Multiple BADGE_ASSIGNED events");
    } catch (error) {
      console.log("ℹ️ Bulk assignment error (might be expected):", error.message);
    }

    console.log("\n5️⃣ Testing Direct WebSocket Service Methods");
    console.log("=====================================");

    // Test direct WebSocket service methods
    console.log("\n🔗 Testing direct badge WebSocket service...");
    
    // Test badge assigned event
    await badgeWebSocketService.broadcastBadgeAssigned({
      userId: "test-user-1",
      badge: {
        id: "test-badge-id",
        userId: "test-user-1",
        badgeTypeId: testBadgeType.id,
        badgeType: testBadgeType,
        assignedBy: "admin-user",
        assignedAt: new Date(),
        isVisible: true
      },
      isAutomatic: false,
      assignedBy: "admin-user"
    });
    console.log("✅ Direct badge assigned event sent");

    // Test badge progress update event
    await badgeWebSocketService.broadcastBadgeProgressUpdate({
      userId: "test-user-1",
      badgeTypeId: autoBadgeType.id,
      badgeType: autoBadgeType,
      progress: 5,
      total: 10,
      progressPercentage: 50
    });
    console.log("✅ Direct badge progress update event sent");

    // Test badge notification
    await badgeWebSocketService.sendBadgeNotification(
      "test-user-1",
      "achievement",
      { message: "You're making great progress!" }
    );
    console.log("✅ Badge notification sent");

    console.log("\n🎉 All badge WebSocket event tests completed!");
    console.log("\nSummary of tested events:");
    console.log("- ✅ BADGE_ASSIGNED (manual assignment)");
    console.log("- ✅ BADGE_REMOVED (manual removal)");
    console.log("- ✅ BADGE_ASSIGNED (automatic evaluation)");
    console.log("- ✅ BADGE_PROGRESS_UPDATE (progress tracking)");
    console.log("- ✅ Bulk badge assignment events");
    console.log("- ✅ Direct WebSocket service methods");

  } catch (error) {
    console.error("❌ Test failed:", error);
    throw error;
  } finally {
    // Cleanup
    wsManager.clients.clear();
  }
}

// Run the test
if (import.meta.main) {
  testBadgeWebSocketEvents()
    .then(() => {
      console.log("\n✅ Badge WebSocket event tests completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n❌ Badge WebSocket event tests failed:", error);
      process.exit(1);
    });
}

export { testBadgeWebSocketEvents };