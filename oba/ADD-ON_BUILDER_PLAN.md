# Oba Backend - Add-on Builder Plan

This document outlines additional improvements that build upon the foundational tasks in the primary `BUILDER_PLAN.md`. These tasks focus on enhancing robustness, performance, and scalability.

## Task 6: Refine Real-Time Communication Layer

**Objective:** Implement a more structured, scalable, and resilient WebSocket event handling system.

**Priority:** High

### Steps:

1.  **Create a WebSocket Event Registry:**
    - Design a central registry or map where WebSocket event names are explicitly defined and linked to their corresponding handler functions.
    - This ensures that all events are documented in one place and prevents the use of arbitrary event strings.

2.  **Standardize Event Payloads:**
    - Use `zod` to define and enforce schemas for the data payloads of both incoming and outgoing WebSocket events. This brings the same level of type safety and validation from the REST API to the real-time layer.

3.  **Implement WebSocket Middleware:**
    - Develop a middleware system for WebSocket connections, similar to the one for HTTP routes. This can be used for:
      - **Authentication:** Verifying a user's token upon connection and for specific events.
      - **Validation:** Automatically validating incoming event payloads against their Zod schemas.
      - **Logging:** Recording event activity.

4.  **Define Clear Communication Patterns:**
    - Establish clear patterns for different types of real-time communication:
      - **Request/Response:** A client sends an event and expects a direct response (e.g., `get-channel-history` -> `channel-history-response`).
      - **Broadcast:** An event is pushed to all clients in a specific room or channel (e.g., `new-message` -> broadcast to channel members).
      - **Targeted Push:** An event is sent to a single, specific user (e.g., `friend-request-received`).

## Task 7: Advanced Database & ORM Enhancements

**Objective:** Improve data integrity, performance, and development workflows with advanced database practices.

**Priority:** Medium

### Steps:

1.  **Implement Soft Deletes:**
    - Modify critical database schemas (e.g., `messages`, `channels`, `servers`) to include a `deletedAt` timestamp column.
    - Update repository/query logic to automatically filter out soft-deleted records from all reads.
    - Create a separate, protected endpoint or utility for hard-deleting data if necessary.

2.  **Optimize Database Indexing:**
    - Analyze common query patterns (e.g., fetching messages by `channelId`, looking up users by `username`).
    - Add database indexes to frequently queried columns in `db/schema.ts` to improve read performance.
    - Use a tool like `EXPLAIN ANALYZE` to verify that indexes are being used effectively.

3.  **Create a Data Seeding Script:**
    - Develop a script (`db/seed.ts`) that populates the database with realistic sample data (users, servers, channels, messages).
    - This is invaluable for creating a consistent development environment and for running reliable integration and E2E tests.

## Task 8: Implement Centralized Error Handling

**Objective:** Create a global error handling strategy to ensure all errors are caught, logged, and returned to the client in a consistent, standardized format.

**Priority:** High

### Steps:

1.  **Create a Custom Error Class:**
    - Define a custom `ApiError` class that extends `Error` and includes properties like `statusCode`, `errorCode` (a unique string identifier), and optional metadata.

2.  **Develop a Global Error Middleware:**
    - Create a middleware that is applied last in the chain.
    - This middleware will use a `try...catch` block to wrap the route handler execution.
    - If an error is caught, it should:
      - Log the full error details (including stack trace) for debugging purposes.
      - If the error is an instance of `ApiError`, use its properties to construct the HTTP response.
      - If it's an unknown error, return a generic 500 Internal Server Error response to avoid leaking implementation details.

3.  **Standardize Error Responses:**
    - Define a consistent JSON structure for all error responses, e.g.:
      ```json
      {
        "error": {
          "code": "RESOURCE_NOT_FOUND",
          "message": "The requested channel does not exist."
        }
      }
      ```

## Task 9: Overhaul Configuration Management

**Objective:** Validate and manage environment variables centrally to ensure the application starts up with a valid configuration and to improve maintainability.

**Priority:** Medium

### Steps:

1.  **Install a Validation Library:**
    - Add a library like `zod-envalid` or a similar tool to the project.

2.  **Create a Configuration Schema:**
    - Define a Zod schema that specifies all required environment variables, their types (string, number, etc.), and any default values.

3.  **Validate on Startup:**
    - In `index.ts`, before the server starts, parse and validate `process.env` against the schema.
    - If validation fails (e.g., a required variable is missing), the application should fail to start with a clear error message indicating what is wrong.

4.  **Provide a Typed, Centralized Config Object:**
    - Export the validated and typed configuration object to be used throughout the application, providing better type safety and autocompletion than `process.env`.

## Task 10: Implement a Caching Layer

**Objective:** Formalize the use of Redis by creating a dedicated caching service to reduce database load and improve API response times.

**Priority:** Low

### Steps:

1.  **Create a Cache Service:**
    - Develop a `CacheService` class that abstracts all Redis logic.
    - This service should handle the connection to Redis and provide simple, generic methods like `get`, `set`, `delete`, and `invalidateByPattern`.

2.  **Identify Caching Opportunities:**
    - Identify data that is frequently read but infrequently updated, such as:
      - User profiles and permissions.
      - Server and channel structures.
      - Role definitions.

3.  **Integrate Caching into Data Repositories:**
    - Update the data access logic to incorporate the `CacheService`.
    - The typical flow would be:
      1.  Attempt to fetch data from the cache.
      2.  If it's a cache miss, fetch the data from the database.
      3.  Store the result in the cache before returning it.

4.  **Implement Cache Invalidation:**
    - Crucially, ensure that whenever data is updated or deleted, the corresponding cache entries are invalidated or removed to prevent stale data.
