#!/usr/bin/env bun

/**
 * Integration test for BadgeEvaluationService
 * This script tests the badge evaluation service with real database operations
 */

import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { BadgeEvaluationService } from "./services/badge-evaluation.service";
import { BadgeService } from "./services/badge.service";
import type { CreateBadgeTypeRequest } from "./types/badge.types";

// Database connection
const connectionString = process.env.DATABASE_URL || "postgresql://postgres:password@localhost:5432/oba_test";
const sql = postgres(connectionString);
const db = drizzle(sql);

async function testBadgeEvaluationService() {
  console.log("🧪 Testing BadgeEvaluationService Integration...\n");

  const badgeService = new BadgeService(db);
  const evaluationService = new BadgeEvaluationService(db);

  try {
    // 1. Create test badge types
    console.log("1. Creating test badge types...");
    
    const messageBadge: CreateBadgeTypeRequest = {
      name: "Chatterbox",
      description: "Send 10 messages",
      category: "achievement",
      assignmentType: "automatic",
      criteria: {
        type: "message_count",
        threshold: 10,
      },
    };

    const serverBadge: CreateBadgeTypeRequest = {
      name: "Server Explorer",
      description: "Join 3 servers",
      category: "achievement", 
      assignmentType: "automatic",
      criteria: {
        type: "server_count",
        threshold: 3,
      },
    };

    const customBadge: CreateBadgeTypeRequest = {
      name: "Veteran",
      description: "Account older than 30 days with some activity",
      category: "milestone",
      assignmentType: "automatic",
      criteria: {
        type: "custom",
        threshold: 1,
        conditions: {
          minAccountAge: 30,
          minActivityRatio: 0.5, // At least 0.5 messages per day
        },
      },
    };

    const createdMessageBadge = await badgeService.createBadgeType(messageBadge, "test-admin");
    const createdServerBadge = await badgeService.createBadgeType(serverBadge, "test-admin");
    const createdCustomBadge = await badgeService.createBadgeType(customBadge, "test-admin");

    console.log(`✅ Created badges: ${createdMessageBadge.name}, ${createdServerBadge.name}, ${createdCustomBadge.name}`);

    // 2. Test user statistics aggregation
    console.log("\n2. Testing user statistics aggregation...");
    
    try {
      const userStats = await evaluationService.aggregateUserStats("test-user-1");
      console.log("✅ User stats aggregated:", {
        messageCount: userStats.messageCount,
        serverCount: userStats.serverCount,
        friendCount: userStats.friendCount,
        daysActive: userStats.daysActive,
        accountAge: userStats.accountAge,
      });
    } catch (error) {
      console.log("⚠️  User not found (expected for test):", error instanceof Error ? error.message : String(error));
    }

    // 3. Test criteria evaluation
    console.log("\n3. Testing criteria evaluation...");
    
    const testUserStats = {
      messageCount: 15,
      serverCount: 2,
      friendCount: 5,
      daysActive: 20,
      accountAge: 45,
      lastActive: new Date(),
    };

    const messageResult = await evaluationService.checkCriteria("test-user", messageBadge.criteria!);
    console.log(`✅ Message badge criteria check: ${messageResult} (should be true for 15 messages >= 10)`);

    const serverResult = await evaluationService.checkCriteria("test-user", serverBadge.criteria!);
    console.log(`✅ Server badge criteria check: ${serverResult} (should be false for 2 servers < 3)`);

    // 4. Test batch criteria evaluation
    console.log("\n4. Testing batch criteria evaluation...");
    
    const userIds = ["user-1", "user-2", "user-3"];
    const criteriaResults = await evaluationService.checkCriteriaForUsers(userIds, messageBadge.criteria!);
    
    console.log("✅ Batch criteria results:");
    criteriaResults.forEach((result, userId) => {
      console.log(`  - ${userId}: ${result}`);
    });

    // 5. Test badge progress calculation
    console.log("\n5. Testing badge progress calculation...");
    
    try {
      const progress = await evaluationService.getBadgeProgress("test-user");
      console.log(`✅ Badge progress calculated for ${progress.length} badges`);
      
      progress.forEach(p => {
        console.log(`  - ${p.badgeType.name}: ${p.progress}/${p.total} (${((p.progress/p.total)*100).toFixed(1)}%)`);
      });
    } catch (error) {
      console.log("⚠️  Progress calculation failed (expected for test user):", error instanceof Error ? error.message : String(error));
    }

    // 6. Test evaluation for recently active users
    console.log("\n6. Testing recently active users evaluation...");
    
    const recentEvaluation = await evaluationService.evaluateRecentlyActiveUsers(30);
    console.log("✅ Recently active users evaluation:", {
      evaluatedUsers: recentEvaluation.evaluatedUsers,
      totalNewBadges: recentEvaluation.totalNewBadges,
      errorCount: recentEvaluation.errors.length,
    });

    // 7. Test users near completion
    console.log("\n7. Testing users near badge completion...");
    
    try {
      const nearCompletion = await evaluationService.getUsersNearCompletion(createdMessageBadge.id, 0.8);
      console.log(`✅ Found ${nearCompletion.length} users near completion for message badge`);
      
      nearCompletion.slice(0, 3).forEach(user => {
        console.log(`  - ${user.username}: ${user.progress}/${user.total} (${(user.progressPercentage*100).toFixed(1)}%)`);
      });
    } catch (error) {
      console.log("⚠️  Near completion check failed:", error instanceof Error ? error.message : String(error));
    }

    // 8. Test badge type specific evaluation
    console.log("\n8. Testing badge type specific evaluation...");
    
    const specificEvaluation = await evaluationService.evaluateUsersForBadgeType(
      ["test-user-1", "test-user-2"], 
      createdMessageBadge.id
    );
    
    console.log("✅ Badge type specific evaluation:", {
      evaluatedUsers: specificEvaluation.evaluatedUsers,
      newAssignments: specificEvaluation.newAssignments,
      errorCount: specificEvaluation.errors.length,
    });

    // 9. Test full user evaluation
    console.log("\n9. Testing full user evaluation...");
    
    const fullEvaluation = await evaluationService.evaluateUser("test-user-1");
    console.log("✅ Full user evaluation:", {
      userId: fullEvaluation.userId,
      newBadges: fullEvaluation.newBadges.length,
      evaluatedBadges: fullEvaluation.evaluatedBadges.length,
      errors: fullEvaluation.errors.length,
    });

    if (fullEvaluation.errors.length > 0) {
      console.log("  Errors:", fullEvaluation.errors);
    }

    // 10. Test batch user evaluation
    console.log("\n10. Testing batch user evaluation...");
    
    const batchEvaluation = await evaluationService.evaluateUsers(["test-user-1", "test-user-2"]);
    console.log(`✅ Batch evaluation completed for ${batchEvaluation.length} users`);
    
    const totalNewBadges = batchEvaluation.reduce((sum, result) => sum + result.newBadges.length, 0);
    const totalErrors = batchEvaluation.reduce((sum, result) => sum + result.errors.length, 0);
    
    console.log(`  - Total new badges: ${totalNewBadges}`);
    console.log(`  - Total errors: ${totalErrors}`);

    console.log("\n🎉 BadgeEvaluationService integration test completed successfully!");

  } catch (error) {
    console.error("❌ Test failed:", error);
    throw error;
  } finally {
    await sql.end();
  }
}

// Run the test
if (import.meta.main) {
  testBadgeEvaluationService()
    .then(() => {
      console.log("\n✅ All tests passed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n❌ Test suite failed:", error);
      process.exit(1);
    });
}

export { testBadgeEvaluationService };