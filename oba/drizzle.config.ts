// drizzle.config.ts
import type { Config } from "drizzle-kit";

export default {
  schema: "./db/schema.ts", // Path to your schema file(s)
  out: "./drizzle",
  dialect: "postgresql",
  dbCredentials: {
    //url: "postgresql://berk:<EMAIL>:5432/oba_db"
    url: "postgres://metadata_user:metadata_password@localhost:5432/oba_test",
    // host: process.env.DB_HOST || 'localhost',
    // user: process.env.DB_USER,
    // password: process.env.DB_PASSWORD,
    // database: process.env.DB_NAME,
  },
} satisfies Config;
 