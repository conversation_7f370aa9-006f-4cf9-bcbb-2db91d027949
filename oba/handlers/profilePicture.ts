import { type IUploadProfilePictureRequest } from "@kurultai/oba-types";
import { UserSchema } from "../db/schema";
import { sql } from "drizzle-orm";
import { db } from "../db/index";
import { presignedMinio } from "./minio";
import { validateUser } from "../db/utils";
import { ResponseUtils } from "../utils/response-utils";

type RouteHandlerFunction = (
  req: Request,
  params?: Record<string, string>,
) => Response | Promise<Response>;

export const handleGetPresignedUrl = async (req: Request) => {
  try {
    const body = (await req.json()) as IUploadProfilePictureRequest;
    const fileName = `profile-pictures/${body.fileName}`;

    // Get presigned URL from MinIO
    const presignedUrlResponse = await presignedMinio(req, { name: fileName });
    const presignedUrl = await presignedUrlResponse.text();

    return ResponseUtils.success({
      presignedUrl,
      fileName,
      finalUrl: `https://minio.obaapp.com.tr/obaapp/${fileName}`,
    });
  } catch (error) {
    console.error("Error in handleGetPresignedUrl:", error);
    return ResponseUtils.internalError("Failed to generate upload URL");
  }
};

export const handleUpdateAvatar: RouteHandlerFunction = async (req, params) => {
  let body;
  try {
    // Get user ID from authenticated headers
    const userId = req.headers.get("X-Authenticated-User");
    if (!userId) {
      return ResponseUtils.unauthorized("User not authenticated");
    }

    // Parse request body
    body = await req.json();
    const { avatarUrl } = body;

    if (!avatarUrl) {
      return ResponseUtils.badRequest("Avatar URL is required");
    }

    // Update user's avatar URL in database
    const [updatedUser] = await db
      .update(UserSchema)
      .set({
        avatar: avatarUrl,
        updatedAt: new Date(),
      })
      .where(sql`${UserSchema.id} = ${userId}`)
      .returning();

    if (!updatedUser) {
      return ResponseUtils.internalError("Failed to update avatar");
    }

    return ResponseUtils.success(updatedUser);
  } catch (error) {
    console.error("Error in handleUpdateAvatar:", error);
    return ResponseUtils.internalError("Internal server error");
  }
};
