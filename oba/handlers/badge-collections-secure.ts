import { z } from "zod";
import { db } from "../db";
import { BadgeCollectionService } from "../services/badge-collection.service";
import { ResponseUtils } from "../utils/response-utils";
import {
  BadgeNotFoundError,
  BadgeValidationError,
  InsufficientPermissionsError
} from "../class/badge-errors";
import type {
  CreateBadgeCollectionRequest,
  UpdateBadgeCollectionRequest,
  BadgeCollectionFilters
} from "../types/badge.types";

// Initialize badge collection service
const badgeCollectionService = new BadgeCollectionService(db);

// Security interfaces
interface AuthenticatedRequest extends Request {
  user: {
    userId: string;
    email: string;
    username?: string;
    isEmailVerified?: boolean;
  };
}

// Validation schemas with enhanced security
const createCollectionSchema = z.object({
  collectionId: z.string().min(1).max(100).regex(/^[a-zA-Z0-9-_]+$/, "Invalid collection ID format"),
  name: z.string().min(1).max(200).trim(),
  description: z.string().min(1).max(1000).trim(),
  type: z.enum(["progressive", "standalone"]).default("progressive"),
  unlockedBy: z.string().max(100).optional(),
  completionReward: z.object({
    badge: z.string().max(100),
    title: z.string().max(200),
    perks: z.array(z.string().max(100)).max(10),
    visual: z.string().max(500),
    animation: z.string().max(500)
  }).optional()
});

const updateCollectionSchema = createCollectionSchema.partial().extend({
  isActive: z.boolean().optional()
});

const collectionFiltersSchema = z.object({
  type: z.enum(["progressive", "standalone"]).optional(),
  isActive: z.boolean().optional(),
  search: z.string().max(100).trim().optional()
});

// Security utility functions
function getAuthenticatedUser(request: Request): { userId: string; email: string } | null {
  const user = (request as any).user;
  if (!user?.userId || !user?.email) {
    return null;
  }
  return { userId: user.userId, email: user.email };
}

function extractCollectionId(url: URL): string | null {
  const pathMatch = url.pathname.match(/\/api\/badges\/collections\/([a-zA-Z0-9-_]+)/);
  return pathMatch?.[1] || null;
}

function extractUserAndCollectionIds(url: URL): { userId: string; collectionId: string } | null {
  const pathMatch = url.pathname.match(/\/api\/users\/([a-zA-Z0-9-_]+)\/collections\/([a-zA-Z0-9-_]+)/);
  if (!pathMatch) return null;
  return {
    userId: pathMatch[1],
    collectionId: pathMatch[2]
  };
}

async function isAdmin(userId: string): Promise<boolean> {
  // TODO: Implement proper admin check
  // This should check user roles/permissions in the database
  return false;
}

function sanitizePaginationParams(url: URL): { limit: number; offset: number } {
  const limit = Math.min(Math.max(parseInt(url.searchParams.get("limit") || "50"), 1), 100);
  const offset = Math.max(parseInt(url.searchParams.get("offset") || "0"), 0);
  return { limit, offset };
}

function handleError(error: unknown, operation: string): Response {
  console.error(`Error in ${operation}:`, error);
  
  if (error instanceof z.ZodError) {
    const sanitizedErrors = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message
    }));
    return ResponseUtils.badRequest("Validation failed", sanitizedErrors);
  }
  
  if (error instanceof BadgeNotFoundError) {
    return ResponseUtils.notFound("Resource not found");
  }
  
  if (error instanceof BadgeValidationError) {
    return ResponseUtils.badRequest("Invalid request data");
  }
  
  if (error instanceof InsufficientPermissionsError) {
    return ResponseUtils.forbidden("Insufficient permissions");
  }
  
  return ResponseUtils.internalServerError("Operation failed");
}

/**
 * Get all badge collections with optional filtering (PUBLIC)
 */
export async function getCollectionsHandler(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const { limit, offset } = sanitizePaginationParams(url);
    
    // Parse and validate filters
    const filters: BadgeCollectionFilters = {};
    
    const typeParam = url.searchParams.get("type");
    if (typeParam) {
      filters.type = typeParam as "progressive" | "standalone";
    }
    
    const isActiveParam = url.searchParams.get("isActive");
    if (isActiveParam) {
      filters.isActive = isActiveParam === "true";
    }
    
    const searchParam = url.searchParams.get("search");
    if (searchParam) {
      const sanitizedSearch = searchParam.trim().slice(0, 100);
      if (sanitizedSearch.length > 0) {
        filters.search = sanitizedSearch;
      }
    }

    const validatedFilters = collectionFiltersSchema.parse(filters);
    const collections = await badgeCollectionService.getCollections(validatedFilters, limit, offset);

    return ResponseUtils.success({
      collections,
      pagination: {
        limit,
        offset,
        total: collections.length
      }
    });
  } catch (error) {
    return handleError(error, "getCollections");
  }
}

/**
 * Get a specific badge collection by ID (PUBLIC)
 */
export async function getCollectionHandler(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const collectionId = extractCollectionId(url);

    if (!collectionId) {
      return ResponseUtils.badRequest("Invalid collection ID format");
    }

    const collection = await badgeCollectionService.getCollectionById(collectionId);
    return ResponseUtils.success({ collection });
  } catch (error) {
    return handleError(error, "getCollection");
  }
}

/**
 * Create a new badge collection (ADMIN ONLY)
 */
export async function createCollectionHandler(request: Request): Promise<Response> {
  try {
    const auth = getAuthenticatedUser(request);
    if (!auth) {
      return ResponseUtils.unauthorized("Authentication required");
    }

    // Check admin permissions
    if (!await isAdmin(auth.userId)) {
      return ResponseUtils.forbidden("Admin permissions required");
    }

    const body = await request.json();
    const validatedData = createCollectionSchema.parse(body);
    
    const collection = await badgeCollectionService.createCollection(
      validatedData as CreateBadgeCollectionRequest,
      auth.userId
    );

    return ResponseUtils.created({ collection });
  } catch (error) {
    return handleError(error, "createCollection");
  }
}

/**
 * Update a badge collection (ADMIN ONLY)
 */
export async function updateCollectionHandler(request: Request): Promise<Response> {
  try {
    const auth = getAuthenticatedUser(request);
    if (!auth) {
      return ResponseUtils.unauthorized("Authentication required");
    }

    // Check admin permissions
    if (!await isAdmin(auth.userId)) {
      return ResponseUtils.forbidden("Admin permissions required");
    }

    const url = new URL(request.url);
    const collectionId = extractCollectionId(url);

    if (!collectionId) {
      return ResponseUtils.badRequest("Invalid collection ID format");
    }

    const body = await request.json();
    const validatedData = updateCollectionSchema.parse(body);
    
    const collection = await badgeCollectionService.updateCollection(
      collectionId,
      validatedData as UpdateBadgeCollectionRequest,
      auth.userId
    );

    return ResponseUtils.success({ collection });
  } catch (error) {
    return handleError(error, "updateCollection");
  }
}

/**
 * Delete a badge collection (ADMIN ONLY)
 */
export async function deleteCollectionHandler(request: Request): Promise<Response> {
  try {
    const auth = getAuthenticatedUser(request);
    if (!auth) {
      return ResponseUtils.unauthorized("Authentication required");
    }

    // Check admin permissions
    if (!await isAdmin(auth.userId)) {
      return ResponseUtils.forbidden("Admin permissions required");
    }

    const url = new URL(request.url);
    const collectionId = extractCollectionId(url);

    if (!collectionId) {
      return ResponseUtils.badRequest("Invalid collection ID format");
    }

    await badgeCollectionService.deleteCollection(collectionId, auth.userId);
    return ResponseUtils.success({ message: "Collection deleted successfully" });
  } catch (error) {
    return handleError(error, "deleteCollection");
  }
}

/**
 * Get user's progress in a specific collection (PROTECTED)
 */
export async function getUserCollectionProgressHandler(request: Request): Promise<Response> {
  try {
    const auth = getAuthenticatedUser(request);
    if (!auth) {
      return ResponseUtils.unauthorized("Authentication required");
    }

    const url = new URL(request.url);
    const ids = extractUserAndCollectionIds(url);

    if (!ids) {
      return ResponseUtils.badRequest("Invalid URL format");
    }

    // Authorization check: users can only access their own data unless admin
    if (ids.userId !== auth.userId && !await isAdmin(auth.userId)) {
      return ResponseUtils.forbidden("Cannot access other users' progress");
    }

    const progress = await badgeCollectionService.getUserCollectionProgress(ids.userId, ids.collectionId);
    
    if (!progress) {
      return ResponseUtils.success({ 
        progress: null,
        message: "User has not started this collection yet"
      });
    }

    return ResponseUtils.success({ progress });
  } catch (error) {
    return handleError(error, "getUserCollectionProgress");
  }
}

/**
 * Get all collection progress for a user (PROTECTED)
 */
export async function getUserCollectionProgressAllHandler(request: Request): Promise<Response> {
  try {
    const auth = getAuthenticatedUser(request);
    if (!auth) {
      return ResponseUtils.unauthorized("Authentication required");
    }

    const url = new URL(request.url);
    const userIdMatch = url.pathname.match(/\/api\/users\/([a-zA-Z0-9-_]+)\/collections\/progress/);
    const userId = userIdMatch?.[1];

    if (!userId) {
      return ResponseUtils.badRequest("Invalid user ID format");
    }

    // Authorization check: users can only access their own data unless admin
    if (userId !== auth.userId && !await isAdmin(auth.userId)) {
      return ResponseUtils.forbidden("Cannot access other users' progress");
    }

    const progressList = await badgeCollectionService.getUserCollectionProgressAll(userId);
    return ResponseUtils.success({ progressList });
  } catch (error) {
    return handleError(error, "getUserCollectionProgressAll");
  }
}

/**
 * Get detailed collection progress with badge information (PROTECTED)
 */
export async function getDetailedCollectionProgressHandler(request: Request): Promise<Response> {
  try {
    const auth = getAuthenticatedUser(request);
    if (!auth) {
      return ResponseUtils.unauthorized("Authentication required");
    }

    const url = new URL(request.url);
    const ids = extractUserAndCollectionIds(url);

    if (!ids) {
      return ResponseUtils.badRequest("Invalid URL format");
    }

    // Authorization check: users can only access their own data unless admin
    if (ids.userId !== auth.userId && !await isAdmin(auth.userId)) {
      return ResponseUtils.forbidden("Cannot access other users' progress");
    }

    const detailedProgress = await badgeCollectionService.getDetailedCollectionProgress(ids.userId, ids.collectionId);
    return ResponseUtils.success({ detailedProgress });
  } catch (error) {
    return handleError(error, "getDetailedCollectionProgress");
  }
}

/**
 * Get the next badge a user can unlock in a collection (PROTECTED)
 */
export async function getNextUnlockableBadgeHandler(request: Request): Promise<Response> {
  try {
    const auth = getAuthenticatedUser(request);
    if (!auth) {
      return ResponseUtils.unauthorized("Authentication required");
    }

    const url = new URL(request.url);
    const ids = extractUserAndCollectionIds(url);

    if (!ids) {
      return ResponseUtils.badRequest("Invalid URL format");
    }

    // Authorization check: users can only access their own data unless admin
    if (ids.userId !== auth.userId && !await isAdmin(auth.userId)) {
      return ResponseUtils.forbidden("Cannot access other users' progress");
    }

    const nextBadge = await badgeCollectionService.getNextUnlockableBadge(ids.userId, ids.collectionId);
    
    return ResponseUtils.success({ 
      nextBadge,
      message: nextBadge ? "Next badge available" : "No more badges to unlock"
    });
  } catch (error) {
    return handleError(error, "getNextUnlockableBadge");
  }
}

/**
 * Check if a collection is completed by a user (PROTECTED)
 */
export async function checkCollectionCompletionHandler(request: Request): Promise<Response> {
  try {
    const auth = getAuthenticatedUser(request);
    if (!auth) {
      return ResponseUtils.unauthorized("Authentication required");
    }

    const url = new URL(request.url);
    const ids = extractUserAndCollectionIds(url);

    if (!ids) {
      return ResponseUtils.badRequest("Invalid URL format");
    }

    // Authorization check: users can only access their own data unless admin
    if (ids.userId !== auth.userId && !await isAdmin(auth.userId)) {
      return ResponseUtils.forbidden("Cannot access other users' progress");
    }

    const isCompleted = await badgeCollectionService.isCollectionCompleted(ids.userId, ids.collectionId);
    
    return ResponseUtils.success({ 
      isCompleted,
      message: isCompleted ? "Collection completed" : "Collection not completed"
    });
  } catch (error) {
    return handleError(error, "checkCollectionCompletion");
  }
}