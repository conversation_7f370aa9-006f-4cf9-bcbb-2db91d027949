import { z } from "zod";
import { db } from "../db";
import {
  createNewMessage,
  deleteMessage,
  editExistingMessage,
  getMessageById,
  retrieveLastNMessages,
} from "../db/utils";
import { EventTypes } from "@kurultai/oba-types";
import { WebSocketManager } from "../manager/websocket.manager";
import { ResponseUtils } from "../utils/response-utils";

// Get WebSocketManager singleton instance
const wsManager = WebSocketManager.getInstance();

// Zod schema for message editing
const editMessageSchema = z.object({
  messageId: z.string().uuid(),
  userId: z.string().uuid(),
  content: z.string().min(1).max(2000),
  channelId: z.string().uuid(),
  serverId: z.string().uuid(),
});

// Zod schema for message deletion
const deleteMessageSchema = z.object({
  messageId: z.string().uuid(),
  userId: z.string().uuid(),
  channelId: z.string().uuid(),
  serverId: z.string().uuid(),
});

// Zod schema for message retrieval
const getMessagesSchema = z.object({
  channelId: z.string().uuid(),
  limit: z.number().int().positive().default(50),
});

/**
 * Handler for editing a message
 */
export const editMessageHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let messageData: {
      messageId?: string;
      userId?: string;
      content?: string;
      channelId?: string;
      serverId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      messageData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      messageData = {
        messageId: formData.get("messageId") as string | undefined,
        userId: formData.get("userId") as string | undefined,
        content: formData.get("content") as string | undefined,
        channelId: formData.get("channelId") as string | undefined,
        serverId: formData.get("serverId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = editMessageSchema.parse(messageData);

    // Get the message to verify ownership
    const message = await getMessageById(db, validatedData.messageId);

    if (!message) {
      return ResponseUtils.notFound("Message");
    }

    // Verify the user is the message owner
    if (message.userId !== validatedData.userId) {
      return ResponseUtils.forbidden("Unauthorized to edit this message");
    }

    // Update the message
    const updatedMessage = await editExistingMessage(
      db,
      validatedData.messageId,
      validatedData.content,
    );

    // Broadcast the update via WebSocket
    const updateEvent = {
      type: EventTypes.MESSAGE_UPDATED,
      sender: validatedData.userId,
      data: {
        messageId: validatedData.messageId,
        content: validatedData.content,
        editedAt: updatedMessage.editedAt,
        channelId: validatedData.channelId,
        serverId: validatedData.serverId,
      },
    };

    wsManager.broadcast(
      JSON.stringify(updateEvent),
      validatedData.serverId,
      validatedData.channelId,
    );

    // Return success response
    return ResponseUtils.success(updatedMessage, {
      message: "Message updated successfully",
    });
  } catch (error) {
    console.error("Error editing message:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to edit message");
  }
};

/**
 * Handler for deleting a message
 */
export const deleteMessageHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let messageData: {
      messageId?: string;
      userId?: string;
      channelId?: string;
      serverId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      messageData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      messageData = {
        messageId: formData.get("messageId") as string | undefined,
        userId: formData.get("userId") as string | undefined,
        channelId: formData.get("channelId") as string | undefined,
        serverId: formData.get("serverId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = deleteMessageSchema.parse(messageData);

    // Get the message to verify ownership
    const message = await getMessageById(db, validatedData.messageId);

    if (!message) {
      return ResponseUtils.notFound("Message");
    }

    // Verify the user is the message owner
    if (message.userId !== validatedData.userId) {
      return ResponseUtils.forbidden("Unauthorized to delete this message");
    }

    // Delete the message
    await deleteMessage(db, validatedData.messageId);

    // Broadcast the deletion via WebSocket
    const deleteEvent = {
      type: EventTypes.MESSAGE_DELETED,
      sender: validatedData.userId,
      data: {
        messageId: validatedData.messageId,
        channelId: validatedData.channelId,
        serverId: validatedData.serverId,
      },
    };

    wsManager.broadcast(
      JSON.stringify(deleteEvent),
      validatedData.serverId,
      validatedData.channelId,
    );

    // Return success response
    return ResponseUtils.success(
      { messageId: validatedData.messageId },
      { message: "Message deleted successfully" },
    );
  } catch (error) {
    console.error("Error deleting message:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to delete message");
  }
};

/**
 * Handler for retrieving channel messages
 */
export const getChannelMessagesHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const channelId = url.searchParams.get("channelId");
    const limitParam = url.searchParams.get("limit");

    if (!channelId) {
      return ResponseUtils.badRequest("Channel ID is required");
    }

    const limit = limitParam ? parseInt(limitParam, 10) : 50;

    // Validate input
    const validatedData = getMessagesSchema.parse({
      channelId,
      limit,
    });

    // Retrieve messages
    const messages = await retrieveLastNMessages(
      db,
      validatedData.channelId,
      validatedData.limit,
    );

    // Return messages
    return ResponseUtils.success(messages, {
      message: "Messages retrieved successfully",
    });
  } catch (error) {
    console.error("Error retrieving messages:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to retrieve messages");
  }
};
