import * as Minio from "minio";
import { ResponseUtils } from "../utils/response-utils";

var client = new Minio.Client({
  endPoint: "minio.obaapp.com.tr",
  //port: 9000,
  useSSL: true,
  accessKey: "3tVv2AuS8E0X9dhG7DvZ",
  secretKey: "o4l5fe3iQwQ2Qv3cUkO73KwKWc6zeLpccoa34JhG",
});

export const presignedMinio = async (
  req: Request,
  params?: Record<string, string>,
) => {
  const filename = params?.name;
  if (!filename) {
    return ResponseUtils.badRequest("Missing filename parameter");
  }
  const presignedUrl = await client.presignedPutObject("obaapp", filename);
  return ResponseUtils.success(presignedUrl);
};

// export const deletePresignedMinio = async (req: Request, params?: Record<string, string>) => {
//     const filename = params?.name;
//     if (!filename) {
//         return ResponseUtils.badRequest("Missing filename parameter");
//     }
//     const presignedUrl = await client.removeObject('obaapp', filename)
//     return ResponseUtils.success(presignedUrl);
// }
