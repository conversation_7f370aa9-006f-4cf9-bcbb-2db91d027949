import { z } from "zod";
import { db } from "../db";
import {
  updateChannelPositions,
  updateCategoryPositions,
  moveChannelsToCategory,
} from "../db/utils/bulk-operations";
import { WebSocketManager } from "../manager/websocket.manager";
import { hasServerPermission } from "../utils/permissions";
import { MANAGE_CHANNELS } from "../constants/permissions";
import { EventTypes } from "@kurultai/oba-types";
import { ResponseUtils } from "../utils/response-utils";

// Get WebSocketManager singleton instance
const wsManager = WebSocketManager.getInstance();

// Zod schema for updating channel positions
const updateChannelPositionsSchema = z.object({
  serverId: z.string().uuid(),
  channelPositions: z.array(
    z.object({
      channelId: z.string().uuid(),
      position: z.number().int().min(0),
    }),
  ),
});

// Zod schema for updating category positions
const updateCategoryPositionsSchema = z.object({
  serverId: z.string().uuid(),
  categoryPositions: z.array(
    z.object({
      categoryId: z.string().uuid(),
      position: z.number().int().min(0),
    }),
  ),
});

// Zod schema for moving channels to a category
const moveChannelsSchema = z.object({
  serverId: z.string().uuid(),
  channelIds: z.array(z.string().uuid()),
  categoryId: z.string().uuid().nullable(),
});

/**
 * Handler for updating multiple channel positions at once
 */
export const updateChannelPositionsHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let data: {
      serverId?: string;
      channelPositions?: Array<{ channelId: string; position: number }>;
      userId?: string; // Added userId for permission checking
    };

    if (contentType === "application/json") {
      data = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      data = {
        serverId: formData.get("serverId") as string,
        channelPositions: formData.has("channelPositions")
          ? JSON.parse(formData.get("channelPositions") as string)
          : undefined,
        userId: formData.get("userId") as string,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = updateChannelPositionsSchema.parse(data);

    // Check if the user has permission to manage channels
    if (data.userId) {
      const hasPermission = await hasServerPermission(
        db,
        data.userId,
        validatedData.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to manage channels",
        );
      }
    }

    // Update channel positions
    const success = await updateChannelPositions(
      validatedData.channelPositions,
    );

    if (!success) {
      return ResponseUtils.internalError("Failed to update channel positions");
    }

    // Broadcast the channel positions update via WebSocket
    const updateEvent = {
      type: EventTypes.CHANNELS_REORDERED,
      sender: data.userId || "system",
      data: {
        server_id: validatedData.serverId,
        channel_positions: validatedData.channelPositions,
      },
    };

    wsManager.broadcast(JSON.stringify(updateEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success({
      message: "Channel positions updated successfully",
    });
  } catch (error) {
    console.error("Error updating channel positions:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Internal server error");
  }
};

/**
 * Handler for updating multiple category positions at once
 */
export const updateCategoryPositionsHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let data: {
      serverId?: string;
      categoryPositions?: Array<{ categoryId: string; position: number }>;
      userId?: string; // Added userId for permission checking
    };

    if (contentType === "application/json") {
      data = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      data = {
        serverId: formData.get("serverId") as string,
        categoryPositions: formData.has("categoryPositions")
          ? JSON.parse(formData.get("categoryPositions") as string)
          : undefined,
        userId: formData.get("userId") as string,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = updateCategoryPositionsSchema.parse(data);

    // Check if the user has permission to manage channels
    if (data.userId) {
      const hasPermission = await hasServerPermission(
        db,
        data.userId,
        validatedData.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to manage categories",
        );
      }
    }

    // Update category positions
    const success = await updateCategoryPositions(
      validatedData.categoryPositions,
    );

    if (!success) {
      return ResponseUtils.internalError("Failed to update category positions");
    }

    // Broadcast the category positions update via WebSocket
    const updateEvent = {
      type: EventTypes.CATEGORIES_REORDERED,
      sender: data.userId || "system",
      data: {
        server_id: validatedData.serverId,
        category_positions: validatedData.categoryPositions,
      },
    };

    wsManager.broadcast(JSON.stringify(updateEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success({
      message: "Category positions updated successfully",
    });
  } catch (error) {
    console.error("Error updating category positions:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Internal server error");
  }
};

/**
 * Handler for moving multiple channels to a category at once
 */
export const moveChannelsHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let data: {
      serverId?: string;
      channelIds?: string[];
      categoryId?: string | null;
      userId?: string; // Added userId for permission checking
    };

    if (contentType === "application/json") {
      data = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      data = {
        serverId: formData.get("serverId") as string,
        channelIds: formData.has("channelIds")
          ? JSON.parse(formData.get("channelIds") as string)
          : undefined,
        categoryId: formData.has("categoryId")
          ? (formData.get("categoryId") as string)
          : null,
        userId: formData.get("userId") as string,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = moveChannelsSchema.parse(data);

    // Check if the user has permission to manage channels
    if (data.userId) {
      const hasPermission = await hasServerPermission(
        db,
        data.userId,
        validatedData.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to manage channels",
        );
      }
    }

    // Move channels to category
    const success = await moveChannelsToCategory(
      validatedData.channelIds,
      validatedData.categoryId,
    );

    if (!success) {
      return ResponseUtils.internalError("Failed to move channels");
    }

    // Broadcast the channel move via WebSocket
    const moveEvent = {
      type: EventTypes.CHANNELS_MOVED,
      sender: data.userId || "system",
      data: {
        server_id: validatedData.serverId,
        channel_ids: validatedData.channelIds,
        category_id: validatedData.categoryId,
      },
    };

    wsManager.broadcast(JSON.stringify(moveEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success({
      message: "Channels moved successfully",
    });
  } catch (error) {
    console.error("Error moving channels:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Internal server error");
  }
};
