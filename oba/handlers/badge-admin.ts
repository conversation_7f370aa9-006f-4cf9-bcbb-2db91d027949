import { z } from "zod";
import { db } from "../db";
import { BadgeService } from "../services/badge.service";
import { BadgeAdminService } from "../services/badge-admin.service";
import { ResponseUtils } from "../utils/response-utils";
import {
  BadgeNotFoundError,
  BadgeAlreadyAssignedError,
  InsufficientPermissionsError,
  BadgeValidationError
} from "../class/badge-errors";
import type {
  BulkBadgeAssignmentRequest,
  BadgeAuditLogEntry,
  BadgeSystemHealthCheck,
  BadgeAdminStats
} from "../types/badge.types";

// Initialize services
const badgeService = new BadgeService(db);
const badgeAdminService = new BadgeAdminService(db);

// Validation schemas
const bulkAssignmentSchema = z.object({
  assignments: z.array(z.object({
    userId: z.string().uuid(),
    badgeTypeId: z.string().uuid()
  })).min(1).max(100), // Limit to 100 assignments per request
  serverId: z.string().uuid().optional()
});

const auditLogFiltersSchema = z.object({
  userId: z.string().uuid().optional(),
  badgeTypeId: z.string().uuid().optional(),
  assignedBy: z.string().uuid().optional(),
  action: z.enum(["assigned", "removed"]).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  limit: z.number().min(1).max(100).default(50),
  offset: z.number().min(0).default(0)
});

// Helper function to extract user ID from request
function getUserId(req: Request): string {
  const user = (req as any).user;
  if (!user?.userId) {
    throw new Error("User not authenticated");
  }
  return user.userId;
}

/**
 * POST /api/admin/badges/bulk-assign - Bulk assign badges to multiple users (admin only)
 */
export const bulkAssignBadgesHandler = async (req: Request): Promise<Response> => {
  try {
    if (req.method !== "POST") {
      return ResponseUtils.methodNotAllowed();
    }

    const adminUserId = getUserId(req);
    const body = await req.json();
    
    // Validate input
    const validatedData = bulkAssignmentSchema.parse(body);

    const assignedBadges = await badgeService.bulkAssignBadges(
      validatedData.assignments,
      adminUserId,
      validatedData.serverId
    );

    return ResponseUtils.success(assignedBadges, {
      message: `Successfully assigned ${assignedBadges.length} badges`
    });
  } catch (error) {
    console.error("Error bulk assigning badges:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map(err => ({
          field: err.path.join("."),
          message: err.message
        }))
      );
    }

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions for bulk badge assignment");
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to bulk assign badges");
  }
};

/**
 * GET /api/admin/badges/audit-log - Get badge assignment audit log (admin only)
 */
export const getBadgeAuditLogHandler = async (req: Request): Promise<Response> => {
  try {
    if (req.method !== "GET") {
      return ResponseUtils.methodNotAllowed();
    }

    const adminUserId = getUserId(req);
    const url = new URL(req.url);
    
    // Parse query parameters
    const filters = {
      userId: url.searchParams.get("userId") || undefined,
      badgeTypeId: url.searchParams.get("badgeTypeId") || undefined,
      assignedBy: url.searchParams.get("assignedBy") || undefined,
      action: url.searchParams.get("action") as "assigned" | "removed" | undefined,
      startDate: url.searchParams.get("startDate") || undefined,
      endDate: url.searchParams.get("endDate") || undefined,
      limit: url.searchParams.get("limit") ? parseInt(url.searchParams.get("limit")!) : 50,
      offset: url.searchParams.get("offset") ? parseInt(url.searchParams.get("offset")!) : 0
    };

    // Validate filters
    const validatedFilters = auditLogFiltersSchema.parse(filters);

    const auditLog = await badgeAdminService.getBadgeAuditLog(adminUserId, validatedFilters);

    return ResponseUtils.success(auditLog, {
      message: "Badge audit log retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting badge audit log:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map(err => ({
          field: err.path.join("."),
          message: err.message
        }))
      );
    }

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions to view audit log");
    }

    return ResponseUtils.internalError("Failed to retrieve badge audit log");
  }
};

/**
 * GET /api/admin/badges/stats - Get comprehensive badge statistics (admin only)
 */
export const getAdminBadgeStatsHandler = async (req: Request): Promise<Response> => {
  try {
    if (req.method !== "GET") {
      return ResponseUtils.methodNotAllowed();
    }

    const adminUserId = getUserId(req);

    const stats = await badgeAdminService.getAdminBadgeStats(adminUserId);

    return ResponseUtils.success(stats, {
      message: "Admin badge statistics retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting admin badge stats:", error);

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions to view admin statistics");
    }

    return ResponseUtils.internalError("Failed to retrieve admin badge statistics");
  }
};

/**
 * GET /api/admin/badges/health-check - Get badge system health check (admin only)
 */
export const getBadgeHealthCheckHandler = async (req: Request): Promise<Response> => {
  try {
    if (req.method !== "GET") {
      return ResponseUtils.methodNotAllowed();
    }

    const adminUserId = getUserId(req);

    const healthCheck = await badgeAdminService.getBadgeSystemHealthCheck(adminUserId);

    return ResponseUtils.success(healthCheck, {
      message: "Badge system health check completed"
    });
  } catch (error) {
    console.error("Error performing badge health check:", error);

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions to perform health check");
    }

    return ResponseUtils.internalError("Failed to perform badge system health check");
  }
};

/**
 * POST /api/admin/badges/evaluate-all - Trigger evaluation for all users (admin only)
 */
export const evaluateAllUsersHandler = async (req: Request): Promise<Response> => {
  try {
    if (req.method !== "POST") {
      return ResponseUtils.methodNotAllowed();
    }

    const adminUserId = getUserId(req);
    const body = await req.json().catch(() => ({}));
    
    const batchSize = body.batchSize && typeof body.batchSize === 'number' 
      ? Math.min(Math.max(body.batchSize, 10), 200) // Clamp between 10-200
      : 50;

    const result = await badgeService.evaluateAllUsers(adminUserId, batchSize);

    return ResponseUtils.success(result, {
      message: "Badge evaluation for all users completed"
    });
  } catch (error) {
    console.error("Error evaluating all users:", error);

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions to evaluate all users");
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to evaluate all users");
  }
};

/**
 * POST /api/admin/badges/reevaluate/:badgeTypeId - Re-evaluate specific badge type for all users (admin only)
 */
export const reevaluateBadgeTypeHandler = async (req: Request, params?: Record<string, string>): Promise<Response> => {
  try {
    if (req.method !== "POST") {
      return ResponseUtils.methodNotAllowed();
    }

    const adminUserId = getUserId(req);
    const badgeTypeId = params?.badgeTypeId;
    
    if (!badgeTypeId) {
      return ResponseUtils.badRequest("Badge type ID is required");
    }

    const result = await badgeService.reevaluateBadgeType(badgeTypeId, adminUserId);

    return ResponseUtils.success(result, {
      message: "Badge type re-evaluation completed"
    });
  } catch (error) {
    console.error("Error re-evaluating badge type:", error);

    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound("Badge type not found");
    }

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions to re-evaluate badge type");
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to re-evaluate badge type");
  }
};

/**
 * DELETE /api/admin/badges/cleanup - Clean up orphaned badge data (admin only)
 */
export const cleanupBadgeDataHandler = async (req: Request): Promise<Response> => {
  try {
    if (req.method !== "DELETE") {
      return ResponseUtils.methodNotAllowed();
    }

    const adminUserId = getUserId(req);

    const cleanupResult = await badgeAdminService.cleanupOrphanedBadgeData(adminUserId);

    return ResponseUtils.success(cleanupResult, {
      message: "Badge data cleanup completed"
    });
  } catch (error) {
    console.error("Error cleaning up badge data:", error);

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions to cleanup badge data");
    }

    return ResponseUtils.internalError("Failed to cleanup badge data");
  }
};

/**
 * GET /api/admin/badges/user-summary/:userId - Get comprehensive user badge summary (admin only)
 */
export const getUserBadgeSummaryHandler = async (req: Request, params?: Record<string, string>): Promise<Response> => {
  try {
    if (req.method !== "GET") {
      return ResponseUtils.methodNotAllowed();
    }

    const adminUserId = getUserId(req);
    const targetUserId = params?.userId;
    
    if (!targetUserId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    const summary = await badgeAdminService.getUserBadgeSummary(adminUserId, targetUserId);

    return ResponseUtils.success(summary, {
      message: "User badge summary retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting user badge summary:", error);

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions to view user badge summary");
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to retrieve user badge summary");
  }
};