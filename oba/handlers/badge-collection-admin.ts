import type { <PERSON>Hand<PERSON> } from '../class/routeHandler';
import { BadgeCollectionAdminService } from '../services/badge-collection-admin.service';
import { BadgeError } from '../class/badge-errors';
import { 
  createBadgeCollectionSchema,
  updateBadgeCollectionSchema,
  bulkBadgeAssignmentSchema,
  reorderBadgesSchema
} from '../utils/badge-schemas';
import { hasPermission } from '../utils/permissions';

const collectionAdminService = new BadgeCollectionAdminService();

/**
 * Create a new badge collection
 * POST /api/admin/badge-collections
 */
export const createBadgeCollection: RouteHandler = async (req, res) => {
  try {
    // Check admin permissions
    if (!hasPermission(req.user, 'MANAGE_BADGES')) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Admin permissions required to create badge collections'
        }
      });
    }

    const validatedData = createBadgeCollectionSchema.parse(req.body);
    const collection = await collectionAdminService.createCollection(validatedData);

    res.status(201).json({
      success: true,
      data: collection
    });
  } catch (error) {
    if (error instanceof BadgeError) {
      return res.status(400).json({
        error: {
          code: error.code,
          message: error.message
        }
      });
    }

    console.error('Error creating badge collection:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to create badge collection'
      }
    });
  }
};

/**
 * Update a badge collection
 * PUT /api/admin/badge-collections/:id
 */
export const updateBadgeCollection: RouteHandler = async (req, res) => {
  try {
    if (!hasPermission(req.user, 'MANAGE_BADGES')) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Admin permissions required to update badge collections'
        }
      });
    }

    const { id } = req.params;
    const validatedData = updateBadgeCollectionSchema.parse(req.body);
    
    const collection = await collectionAdminService.updateCollection(id, validatedData);

    res.json({
      success: true,
      data: collection
    });
  } catch (error) {
    if (error instanceof BadgeError) {
      return res.status(error.code === 'COLLECTION_NOT_FOUND' ? 404 : 400).json({
        error: {
          code: error.code,
          message: error.message
        }
      });
    }

    console.error('Error updating badge collection:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to update badge collection'
      }
    });
  }
};

/**
 * Delete a badge collection
 * DELETE /api/admin/badge-collections/:id
 */
export const deleteBadgeCollection: RouteHandler = async (req, res) => {
  try {
    if (!hasPermission(req.user, 'MANAGE_BADGES')) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Admin permissions required to delete badge collections'
        }
      });
    }

    const { id } = req.params;
    await collectionAdminService.deleteCollection(id);

    res.json({
      success: true,
      message: 'Badge collection deleted successfully'
    });
  } catch (error) {
    if (error instanceof BadgeError) {
      const statusCode = error.code === 'COLLECTION_NOT_FOUND' ? 404 : 
                        error.code === 'COLLECTION_HAS_BADGES' ? 409 : 400;
      
      return res.status(statusCode).json({
        error: {
          code: error.code,
          message: error.message
        }
      });
    }

    console.error('Error deleting badge collection:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to delete badge collection'
      }
    });
  }
};

/**
 * Get all badge collections with admin details
 * GET /api/admin/badge-collections
 */
export const getBadgeCollections: RouteHandler = async (req, res) => {
  try {
    if (!hasPermission(req.user, 'MANAGE_BADGES')) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Admin permissions required to view badge collections'
        }
      });
    }

    const { isActive, type, limit, offset } = req.query;
    
    const filters = {
      isActive: isActive === 'true' ? true : isActive === 'false' ? false : undefined,
      type: type as string,
      limit: limit ? parseInt(limit as string) : undefined,
      offset: offset ? parseInt(offset as string) : undefined
    };

    const collections = await collectionAdminService.getCollections(filters);

    res.json({
      success: true,
      data: collections
    });
  } catch (error) {
    console.error('Error fetching badge collections:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch badge collections'
      }
    });
  }
};

/**
 * Get a specific badge collection with details
 * GET /api/admin/badge-collections/:id
 */
export const getBadgeCollection: RouteHandler = async (req, res) => {
  try {
    if (!hasPermission(req.user, 'MANAGE_BADGES')) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Admin permissions required to view badge collection details'
        }
      });
    }

    const { id } = req.params;
    const collection = await collectionAdminService.getCollectionById(id);

    res.json({
      success: true,
      data: collection
    });
  } catch (error) {
    if (error instanceof BadgeError && error.code === 'COLLECTION_NOT_FOUND') {
      return res.status(404).json({
        error: {
          code: error.code,
          message: error.message
        }
      });
    }

    console.error('Error fetching badge collection:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch badge collection'
      }
    });
  }
};

/**
 * Reorder badges within a collection
 * PUT /api/admin/badge-collections/:id/reorder
 */
export const reorderCollectionBadges: RouteHandler = async (req, res) => {
  try {
    if (!hasPermission(req.user, 'MANAGE_BADGES')) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Admin permissions required to reorder badges'
        }
      });
    }

    const { id } = req.params;
    const validatedData = reorderBadgesSchema.parse(req.body);
    
    await collectionAdminService.reorderCollectionBadges(id, validatedData.badgeOrders);

    res.json({
      success: true,
      message: 'Badge order updated successfully'
    });
  } catch (error) {
    if (error instanceof BadgeError) {
      const statusCode = error.code === 'COLLECTION_NOT_FOUND' ? 404 : 400;
      
      return res.status(statusCode).json({
        error: {
          code: error.code,
          message: error.message
        }
      });
    }

    console.error('Error reordering badges:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to reorder badges'
      }
    });
  }
};

/**
 * Bulk assign badges from a collection to users
 * POST /api/admin/badge-collections/:id/bulk-assign
 */
export const bulkAssignCollectionBadges: RouteHandler = async (req, res) => {
  try {
    if (!hasPermission(req.user, 'MANAGE_BADGES')) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Admin permissions required to bulk assign badges'
        }
      });
    }

    const { id } = req.params;
    const validatedData = bulkBadgeAssignmentSchema.parse(req.body);
    
    const assignmentData = {
      ...validatedData,
      collectionId: id,
      assignedBy: req.user.id
    };

    const results = await collectionAdminService.bulkAssignCollectionBadges(assignmentData);

    res.json({
      success: true,
      data: results
    });
  } catch (error) {
    if (error instanceof BadgeError) {
      const statusCode = error.code === 'COLLECTION_NOT_FOUND' ? 404 : 400;
      
      return res.status(statusCode).json({
        error: {
          code: error.code,
          message: error.message
        }
      });
    }

    console.error('Error bulk assigning badges:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to bulk assign badges'
      }
    });
  }
};

/**
 * Get collection analytics
 * GET /api/admin/badge-collections/:id/analytics
 */
export const getCollectionAnalytics: RouteHandler = async (req, res) => {
  try {
    if (!hasPermission(req.user, 'MANAGE_BADGES')) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Admin permissions required to view analytics'
        }
      });
    }

    const { id } = req.params;
    const analytics = await collectionAdminService.getCollectionAnalytics(id);

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    if (error instanceof BadgeError && error.code === 'COLLECTION_NOT_FOUND') {
      return res.status(404).json({
        error: {
          code: error.code,
          message: error.message
        }
      });
    }

    console.error('Error fetching collection analytics:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch collection analytics'
      }
    });
  }
};

/**
 * Get collection progress report
 * GET /api/admin/badge-collections/:id/progress-report
 */
export const getCollectionProgressReport: RouteHandler = async (req, res) => {
  try {
    if (!hasPermission(req.user, 'MANAGE_BADGES')) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Admin permissions required to view progress reports'
        }
      });
    }

    const { id } = req.params;
    const { limit, offset, includeCompleted } = req.query;
    
    const options = {
      limit: limit ? parseInt(limit as string) : undefined,
      offset: offset ? parseInt(offset as string) : undefined,
      includeCompleted: includeCompleted === 'true'
    };

    const report = await collectionAdminService.getCollectionProgressReport(id, options);

    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    if (error instanceof BadgeError && error.code === 'COLLECTION_NOT_FOUND') {
      return res.status(404).json({
        error: {
          code: error.code,
          message: error.message
        }
      });
    }

    console.error('Error fetching progress report:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch progress report'
      }
    });
  }
};

/**
 * Test collection configuration
 * GET /api/admin/badge-collections/:id/test
 */
export const testBadgeCollection: RouteHandler = async (req, res) => {
  try {
    if (!hasPermission(req.user, 'MANAGE_BADGES')) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Admin permissions required to test collections'
        }
      });
    }

    const { id } = req.params;
    const testResult = await collectionAdminService.testCollection(id);

    res.json({
      success: true,
      data: testResult
    });
  } catch (error) {
    if (error instanceof BadgeError && error.code === 'COLLECTION_NOT_FOUND') {
      return res.status(404).json({
        error: {
          code: error.code,
          message: error.message
        }
      });
    }

    console.error('Error testing collection:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to test collection'
      }
    });
  }
};

/**
 * Preview collection
 * GET /api/admin/badge-collections/:id/preview
 */
export const previewBadgeCollection: RouteHandler = async (req, res) => {
  try {
    if (!hasPermission(req.user, 'MANAGE_BADGES')) {
      return res.status(403).json({
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Admin permissions required to preview collections'
        }
      });
    }

    const { id } = req.params;
    const preview = await collectionAdminService.previewCollection(id);

    res.json({
      success: true,
      data: preview
    });
  } catch (error) {
    if (error instanceof BadgeError && error.code === 'COLLECTION_NOT_FOUND') {
      return res.status(404).json({
        error: {
          code: error.code,
          message: error.message
        }
      });
    }

    console.error('Error previewing collection:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to preview collection'
      }
    });
  }
};