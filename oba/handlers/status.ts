import { z } from "zod";
import { db } from "../db";
import {
  updateUserStatus,
  getUserStatus,
  getOnlineFriends,
  updateLastActive,
  setAllUsersOffline,
} from "../db/utils";
import { EventTypes } from "@kurultai/oba-types";
import { WebSocketManager } from "../manager/websocket.manager";
import { ResponseUtils } from "../utils/response-utils";

// Get WebSocketManager singleton instance
const wsManager = WebSocketManager.getInstance();

// Zod schema for updating user status
const updateStatusSchema = z.object({
  userId: z.string().uuid(),
  status: z.enum(["ONLINE", "AWAY", "BUSY", "INVISIBLE", "OFFLINE"]),
  statusMessage: z.string().optional(),
});

// Zod schema for getting user status or online friends
const userIdSchema = z.object({
  userId: z.string().uuid(),
});

/**
 * <PERSON><PERSON> for updating user status
 */
export const updateUserStatusHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let statusData: {
      userId?: string;
      status?: "ONLINE" | "AWAY" | "BUSY" | "INVISIBLE" | "OFFLINE";
      statusMessage?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      statusData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      statusData = {
        userId: formData.get("userId") as string | undefined,
        status: formData.get("status") as
          | "ONLINE"
          | "AWAY"
          | "BUSY"
          | "INVISIBLE"
          | "OFFLINE"
          | undefined,
        statusMessage: formData.get("statusMessage") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = updateStatusSchema.parse(statusData);

    try {
      // Update the user's status
      const updatedUser = await updateUserStatus(
        db,
        validatedData.userId,
        validatedData.status,
        validatedData.statusMessage,
      );

      // Get the user's friends to broadcast status update
      const friends = await getOnlineFriends(db, validatedData.userId);
      const friendIds = friends.map((f) => f.friend.id);

      // Broadcast status update to friends
      const statusEvent = {
        type: EventTypes.USER_STATUS_UPDATE,
        sender: validatedData.userId,
        data: {
          userId: updatedUser.id,
          username: updatedUser.username,
          status: updatedUser.status,
          statusMessage: updatedUser.statusMessage,
          lastActive: updatedUser.lastActive,
          avatar: updatedUser.avatar,
        },
      };

      // Send to all online friends (except those who are INVISIBLE)
      friendIds.forEach((friendId) => {
        wsManager.broadcastToUser(JSON.stringify(statusEvent), friendId);
      });

      // Return success response
      return ResponseUtils.success(updatedUser, {
        message: "User status updated successfully",
      });
    } catch (error) {
      // Handle specific errors
      if (error instanceof Error) {
        if (error.message === "User not found") {
          return ResponseUtils.notFound("User");
        }
      }
      throw error;
    }
  } catch (error) {
    console.error("Error updating user status:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to update user status");
  }
};

/**
 * Handler for getting user status
 */
export const getUserStatusHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const userId = url.searchParams.get("userId");

    if (!userId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    // Validate input using Zod
    userIdSchema.parse({ userId });

    try {
      // Get the user's status
      const userStatus = await getUserStatus(db, userId);

      // Return the user status
      return ResponseUtils.success(userStatus, {
        message: "User status retrieved successfully",
      });
    } catch (error) {
      // Handle specific errors
      if (error instanceof Error) {
        if (error.message === "User not found") {
          return ResponseUtils.notFound("User");
        }
      }
      throw error;
    }
  } catch (error) {
    console.error("Error getting user status:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to get user status");
  }
};

/**
 * Handler for getting online friends
 */
export const getOnlineFriendsHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const userId = url.searchParams.get("userId");

    if (!userId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    // Validate input using Zod
    userIdSchema.parse({ userId });

    // Get the user's online friends
    const onlineFriends = await getOnlineFriends(db, userId);

    // Return the online friends
    return ResponseUtils.success(onlineFriends, {
      message: "Online friends retrieved successfully",
    });
  } catch (error) {
    console.error("Error getting online friends:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to get online friends");
  }
};

/**
 * Handler for updating last active timestamp
 */
export const updateLastActiveHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let userData: {
      userId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      userData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      userData = {
        userId: formData.get("userId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = userIdSchema.parse(userData);

    try {
      // Update the user's last active timestamp
      const updatedUser = await updateLastActive(db, validatedData.userId);

      // Return success response
      return ResponseUtils.success(updatedUser, {
        message: "Last active timestamp updated successfully",
      });
    } catch (error) {
      // Handle specific errors
      if (error instanceof Error) {
        if (error.message === "User not found") {
          return ResponseUtils.notFound("User");
        }
      }
      throw error;
    }
  } catch (error) {
    console.error("Error updating last active timestamp:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError(
      "Failed to update last active timestamp",
    );
  }
};
