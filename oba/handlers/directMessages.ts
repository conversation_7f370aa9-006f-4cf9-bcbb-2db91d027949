import { z } from "zod";
import { db } from "../db";
import {
  createDirectMessage,
  deleteDirectMessage,
  editDirectMessage,
  getDirectMessageById,
  getDirectMessagesBetweenUsers,
  getUserDirectMessageContacts,
  markDirectMessageAsRead,
} from "../db/utils";
import { WebSocketManager } from "../manager/websocket.manager";
import { EventTypes } from "@kurultai/oba-types";
import { ResponseUtils } from "../utils/response-utils";

// Get WebSocketManager singleton instance
const wsManager = WebSocketManager.getInstance();

// Zod schema for sending a direct message
const sendDirectMessageSchema = z.object({
  senderId: z.string().uuid(),
  receiverId: z.string().uuid(),
  content: z.string().min(1).max(2000),
  attachments: z.string().optional(),
});

// Zod schema for editing a direct message
const editDirectMessageSchema = z.object({
  messageId: z.string().uuid(),
  senderId: z.string().uuid(),
  content: z.string().min(1).max(2000),
});

// Zod schema for deleting a direct message
const deleteDirectMessageSchema = z.object({
  messageId: z.string().uuid(),
  senderId: z.string().uuid(),
});

// Zod schema for marking a direct message as read
const markAsReadSchema = z.object({
  messageId: z.string().uuid(),
  userId: z.string().uuid(),
});

// Zod schema for getting direct messages between users
const getDirectMessagesSchema = z.object({
  userId1: z.string().uuid(),
  userId2: z.string().uuid(),
  limit: z.number().int().positive().default(50),
});

/**
 * Handler for sending a direct message
 */
export const sendDirectMessageHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let messageData: {
      senderId?: string;
      receiverId?: string;
      content?: string;
      attachments?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      messageData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      messageData = {
        senderId: formData.get("senderId") as string | undefined,
        receiverId: formData.get("receiverId") as string | undefined,
        content: formData.get("content") as string | undefined,
        attachments: formData.get("attachments") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = sendDirectMessageSchema.parse(messageData);

    // Create the direct message
    const newMessage = await createDirectMessage(
      db,
      validatedData.senderId,
      validatedData.receiverId,
      validatedData.content,
      validatedData.attachments,
    );

    // Broadcast the message to the receiver via WebSocket
    // Note: This is a simplified approach. In a real app, you'd need a more sophisticated
    // way to track user connections and direct message channels
    const directMessageEvent = {
      type: EventTypes.DIRECT_MESSAGE_SENT, // Custom event type for direct messages
      sender: validatedData.senderId,
      data: {
        messageId: newMessage.id,
        senderId: newMessage.senderId,
        receiverId: newMessage.receiverId,
        content: newMessage.content,
        attachments: newMessage.attachments,
        createdAt: newMessage.createdAt,
      },
    };

    // For now, we'll broadcast to both users
    // In a real app, you'd want to send this only to the specific users' connections
    wsManager.broadcastToUser(
      validatedData.receiverId,
      JSON.stringify(directMessageEvent),
    );

    // Return success response
    return ResponseUtils.created(newMessage);
  } catch (error) {
    console.error("Error sending direct message:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to send direct message");
  }
};

/**
 * Handler for editing a direct message
 */
export const editDirectMessageHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let messageData: {
      messageId?: string;
      senderId?: string;
      content?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      messageData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      messageData = {
        messageId: formData.get("messageId") as string | undefined,
        senderId: formData.get("senderId") as string | undefined,
        content: formData.get("content") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = editDirectMessageSchema.parse(messageData);

    // Get the message to verify ownership
    const message = await getDirectMessageById(db, validatedData.messageId);

    if (!message) {
      return ResponseUtils.notFound("Message not found");
    }

    // Verify the user is the message sender
    if (message.senderId !== validatedData.senderId) {
      return ResponseUtils.forbidden("Unauthorized to edit this message");
    }

    // Update the message
    const updatedMessage = await editDirectMessage(
      db,
      validatedData.messageId,
      validatedData.content,
    );

    // Broadcast the update via WebSocket
    const updateEvent = {
      type: EventTypes.DIRECT_MESSAGE_UPDATED,
      sender: validatedData.senderId,
      data: {
        messageId: updatedMessage.id,
        content: updatedMessage.content,
        editedAt: updatedMessage.editedAt,
        senderId: updatedMessage.senderId,
        receiverId: updatedMessage.receiverId,
      },
    };

    // Broadcast to the receiver
    wsManager.broadcastToUser(
      updatedMessage.receiverId,
      JSON.stringify(updateEvent),
    );

    // Return success response
    return ResponseUtils.success(updatedMessage);
  } catch (error) {
    console.error("Error editing direct message:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to edit direct message");
  }
};

/**
 * Handler for deleting a direct message
 */
export const deleteDirectMessageHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let messageData: {
      messageId?: string;
      senderId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      messageData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      messageData = {
        messageId: formData.get("messageId") as string | undefined,
        senderId: formData.get("senderId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = deleteDirectMessageSchema.parse(messageData);

    // Get the message to verify ownership
    const message = await getDirectMessageById(db, validatedData.messageId);

    if (!message) {
      return ResponseUtils.notFound("Message not found");
    }

    // Verify the user is the message sender
    if (message.senderId !== validatedData.senderId) {
      return ResponseUtils.forbidden("Unauthorized to delete this message");
    }

    // Delete the message
    const deletedMessage = await deleteDirectMessage(
      db,
      validatedData.messageId,
    );

    // Broadcast the deletion via WebSocket
    const deleteEvent = {
      type: EventTypes.DIRECT_MESSAGE_DELETED,
      sender: validatedData.senderId,
      data: {
        messageId: deletedMessage.id,
        senderId: deletedMessage.senderId,
        receiverId: deletedMessage.receiverId,
      },
    };

    // Broadcast to the receiver
    wsManager.broadcastToUser(
      deletedMessage.receiverId,
      JSON.stringify(deleteEvent),
    );

    // Return success response
    return ResponseUtils.success({
      messageId: deletedMessage.id,
    });
  } catch (error) {
    console.error("Error deleting direct message:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to delete direct message");
  }
};

/**
 * Handler for marking a direct message as read
 */
export const markDirectMessageAsReadHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let messageData: {
      messageId?: string;
      userId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      messageData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      messageData = {
        messageId: formData.get("messageId") as string | undefined,
        userId: formData.get("userId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = markAsReadSchema.parse(messageData);

    // Get the message to verify it exists
    const message = await getDirectMessageById(db, validatedData.messageId);

    if (!message) {
      return ResponseUtils.notFound("Message not found");
    }

    // Verify the user is the message receiver
    if (message.receiverId !== validatedData.userId) {
      return ResponseUtils.forbidden(
        "Unauthorized to mark this message as read",
      );
    }

    // Mark the message as read
    await markDirectMessageAsRead(db, validatedData.messageId);

    // Return success response
    return ResponseUtils.success({
      messageId: validatedData.messageId,
    });
  } catch (error) {
    console.error("Error marking direct message as read:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to mark direct message as read");
  }
};

/**
 * Handler for getting direct messages between two users
 */
export const getDirectMessagesHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const userId1 = url.searchParams.get("userId1");
    const userId2 = url.searchParams.get("userId2");
    const limitParam = url.searchParams.get("limit");

    if (!userId1 || !userId2) {
      return ResponseUtils.badRequest("Both user IDs are required");
    }

    const limit = limitParam ? parseInt(limitParam, 10) : 50;

    // Validate input
    const validatedData = getDirectMessagesSchema.parse({
      userId1,
      userId2,
      limit,
    });

    // Retrieve messages
    const messages = await getDirectMessagesBetweenUsers(
      db,
      validatedData.userId1,
      validatedData.userId2,
      validatedData.limit,
    );

    // Return messages
    return ResponseUtils.success({ messages });
  } catch (error) {
    console.error("Error retrieving direct messages:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to retrieve direct messages");
  }
};

/**
 * Handler for getting a user's direct message contacts
 */
export const getUserContactsHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const userId = url.searchParams.get("userId");

    if (!userId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    // Retrieve contacts
    const contacts = await getUserDirectMessageContacts(db, userId);

    // Return contacts
    return ResponseUtils.success({ contacts });
  } catch (error) {
    console.error("Error retrieving user contacts:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to retrieve user contacts");
  }
};
