import { z } from "zod";
import { db } from "../db";
import { BadgeService } from "../services/badge.service";
import { PerkService } from "../services/perk.service";
import { ResponseUtils } from "../utils/response-utils";
import {
  BadgeNotFoundError,
  BadgeValidationError,
  InsufficientPermissionsError
} from "../class/badge-errors";

// Initialize services
const badgeService = new BadgeService(db);
const perkService = new PerkService(db);

// Validation schemas
const getUserPerksSchema = z.object({
  userId: z.string().uuid(),
  serverId: z.string().uuid().optional()
});

const getAvailablePerksSchema = z.object({
  serverId: z.string().uuid().optional()
});

const validateBadgePerksSchema = z.object({
  badgeTypeId: z.string().uuid(),
  serverId: z.string().uuid().optional()
});

const manualPerkAssignmentSchema = z.object({
  userId: z.string().uuid(),
  badgeTypeId: z.string().uuid(),
  requestedBy: z.string().uuid(),
  serverId: z.string().uuid().optional()
});

/**
 * Handler for getting user perks
 * GET /api/users/:userId/perks?serverId=...
 */
export const getUserPerksHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/');
    const userId = pathParts[pathParts.indexOf('users') + 1];
    const serverId = url.searchParams.get('serverId') || undefined;

    if (!userId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    // Validate input
    const validatedData = getUserPerksSchema.parse({ userId, serverId });

    // Get user perks
    const perks = await badgeService.getUserPerks(validatedData.userId, validatedData.serverId);

    return ResponseUtils.success({
      success: true,
      perks,
      userId: validatedData.userId,
      serverId: validatedData.serverId
    });

  } catch (error) {
    console.error("Error getting user perks:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to get user perks");
  }
};

/**
 * Handler for getting available perks
 * GET /api/perks/available?serverId=...
 */
export const getAvailablePerksHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const serverId = url.searchParams.get('serverId') || undefined;

    // Validate input
    const validatedData = getAvailablePerksSchema.parse({ serverId });

    // Get available perks
    const perks = await badgeService.getAvailablePerks(validatedData.serverId);

    return ResponseUtils.success({
      success: true,
      perks,
      serverId: validatedData.serverId
    });

  } catch (error) {
    console.error("Error getting available perks:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to get available perks");
  }
};

/**
 * Handler for validating badge perks
 * GET /api/badges/:badgeTypeId/perks/validate?serverId=...
 */
export const validateBadgePerksHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/');
    const badgeTypeId = pathParts[pathParts.indexOf('badges') + 1];
    const serverId = url.searchParams.get('serverId') || undefined;

    if (!badgeTypeId) {
      return ResponseUtils.badRequest("Badge type ID is required");
    }

    // Validate input
    const validatedData = validateBadgePerksSchema.parse({ badgeTypeId, serverId });

    // Get badge type
    const badgeType = await badgeService.getBadgeTypeById(validatedData.badgeTypeId);

    // Validate perks
    const validation = await badgeService.validateBadgePerks(badgeType, validatedData.serverId);

    return ResponseUtils.success({
      success: true,
      validation,
      badgeTypeId: validatedData.badgeTypeId,
      serverId: validatedData.serverId
    });

  } catch (error) {
    console.error("Error validating badge perks:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound(error.message);
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to validate badge perks");
  }
};

/**
 * Handler for manually assigning perks
 * POST /api/perks/assign
 */
export const manuallyAssignPerksHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let requestData: any;

    // Parse request body based on content type
    if (contentType === "application/json") {
      requestData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      requestData = {
        userId: formData.get("userId") as string,
        badgeTypeId: formData.get("badgeTypeId") as string,
        requestedBy: formData.get("requestedBy") as string,
        serverId: formData.get("serverId") as string || undefined
      };
    } else {
      return ResponseUtils.unsupportedContentType();
    }

    // Validate input
    const validatedData = manualPerkAssignmentSchema.parse(requestData);

    // Manually assign perks
    const results = await badgeService.manuallyAssignPerks(
      validatedData.userId,
      validatedData.badgeTypeId,
      validatedData.requestedBy,
      validatedData.serverId
    );

    const successfulAssignments = results.filter(r => r.success);
    const failedAssignments = results.filter(r => !r.success);

    return ResponseUtils.success({
      success: true,
      results: {
        total: results.length,
        successful: successfulAssignments.length,
        failed: failedAssignments.length,
        assignments: successfulAssignments,
        failures: failedAssignments
      },
      userId: validatedData.userId,
      badgeTypeId: validatedData.badgeTypeId,
      serverId: validatedData.serverId
    });

  } catch (error) {
    console.error("Error manually assigning perks:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound(error.message);
    }

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions to assign perks");
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to manually assign perks");
  }
};

/**
 * Handler for manually revoking perks
 * POST /api/perks/revoke
 */
export const manuallyRevokePerksHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let requestData: any;

    // Parse request body based on content type
    if (contentType === "application/json") {
      requestData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      requestData = {
        userId: formData.get("userId") as string,
        badgeTypeId: formData.get("badgeTypeId") as string,
        requestedBy: formData.get("requestedBy") as string,
        serverId: formData.get("serverId") as string || undefined
      };
    } else {
      return ResponseUtils.unsupportedContentType();
    }

    // Validate input
    const validatedData = manualPerkAssignmentSchema.parse(requestData);

    // Manually revoke perks
    const results = await badgeService.manuallyRevokePerks(
      validatedData.userId,
      validatedData.badgeTypeId,
      validatedData.requestedBy,
      validatedData.serverId
    );

    const successfulRevocations = results.filter(r => r.success);
    const failedRevocations = results.filter(r => !r.success);

    return ResponseUtils.success({
      success: true,
      results: {
        total: results.length,
        successful: successfulRevocations.length,
        failed: failedRevocations.length,
        revocations: successfulRevocations,
        failures: failedRevocations
      },
      userId: validatedData.userId,
      badgeTypeId: validatedData.badgeTypeId,
      serverId: validatedData.serverId
    });

  } catch (error) {
    console.error("Error manually revoking perks:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound(error.message);
    }

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions to revoke perks");
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to manually revoke perks");
  }
};

/**
 * Handler for getting perk display information
 * GET /api/perks/display?serverId=...&userId=...
 */
export const getPerkDisplayHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const serverId = url.searchParams.get('serverId') || undefined;
    const userId = url.searchParams.get('userId') || undefined;

    // Get available perks
    const availablePerks = await badgeService.getAvailablePerks(serverId);

    // Get user perks if userId is provided
    let userPerks: any[] = [];
    if (userId) {
      userPerks = await badgeService.getUserPerks(userId, serverId);
    }

    // Organize perks by type for display
    const perksByType = availablePerks.reduce((acc, perk) => {
      if (!acc[perk.type]) {
        acc[perk.type] = [];
      }
      acc[perk.type].push(perk);
      return acc;
    }, {} as Record<string, any[]>);

    return ResponseUtils.success({
      success: true,
      display: {
        availablePerks,
        userPerks,
        perksByType,
        summary: {
          totalAvailable: availablePerks.length,
          userPerksCount: userPerks.length,
          typeBreakdown: Object.keys(perksByType).map(type => ({
            type,
            count: perksByType[type].length
          }))
        }
      },
      serverId,
      userId
    });

  } catch (error) {
    console.error("Error getting perk display information:", error);

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to get perk display information");
  }
};