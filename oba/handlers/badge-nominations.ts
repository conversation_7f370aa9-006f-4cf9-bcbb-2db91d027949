import { BadgeNominationService } from "../services/badge-nomination.service";
import { BadgeService } from "../services/badge.service";
import { WebSocketManager } from "../manager/websocket.manager";
import { CreateNominationRequest } from "../types/badge.types";
import { z } from "zod";

// Validation schemas
const createNominationSchema = z.object({
  badgeTypeId: z.string().uuid("Invalid badge type ID"),
  nomineeUserId: z.string().uuid("Invalid nominee user ID"),
  nominationReason: z.string().min(1).max(500).optional(),
});

const nominationParamsSchema = z.object({
  nominationId: z.string().uuid("Invalid nomination ID"),
});

const userParamsSchema = z.object({
  userId: z.string().uuid("Invalid user ID"),
});

const badgeTypeParamsSchema = z.object({
  badgeTypeId: z.string().uuid("Invalid badge type ID"),
});

const nominationQuerySchema = z.object({
  status: z.enum(["pending", "approved", "rejected"]).optional(),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default("20"),
  offset: z.string().transform(Number).pipe(z.number().min(0)).default("0"),
});

// Initialize services
const badgeService = new BadgeService();
const wsManager = WebSocketManager.getInstance();
const nominationService = new BadgeNominationService(badgeService, wsManager);

/**
 * POST /api/badges/nominations
 * Submit a new badge nomination
 */
export async function submitNominationHandler(req: any, res: any): Promise<Response> {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return new Response(JSON.stringify({ error: "Authentication required" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    const validatedData = createNominationSchema.parse(req.body);
    
    const nomination = await nominationService.submitNomination(
      userId,
      validatedData as CreateNominationRequest
    );

    return new Response(JSON.stringify({
      success: true,
      data: {
        nomination,
        message: "Nomination submitted successfully"
      }
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    return new Response(JSON.stringify({ 
      success: false,
      error: error.message || "Internal server error" 
    }), {
      status: error.code === "BADGE_NOT_FOUND" ? 404 : 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * GET /api/badges/nominations/received
 * Get nominations received by the current user
 */
export async function getReceivedNominationsHandler(req: any, res: any): Promise<Response> {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return new Response(JSON.stringify({ error: "Authentication required" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    const { status } = nominationQuerySchema.parse(req.query);
    
    const nominations = await nominationService.getNominationsForUser(userId, status);

    return new Response(JSON.stringify({
      success: true,
      data: {
        nominations,
        count: nominations.length
      }
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    return new Response(JSON.stringify({ 
      success: false,
      error: error.message || "Internal server error" 
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * GET /api/badges/nominations/submitted
 * Get nominations submitted by the current user
 */
export async function getSubmittedNominationsHandler(req: any, res: any): Promise<Response> {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return new Response(JSON.stringify({ error: "Authentication required" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    const { status } = nominationQuerySchema.parse(req.query);
    
    const nominations = await nominationService.getNominationsByUser(userId, status);

    return new Response(JSON.stringify({
      success: true,
      data: {
        nominations,
        count: nominations.length
      }
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    return new Response(JSON.stringify({ 
      success: false,
      error: error.message || "Internal server error" 
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * GET /api/badges/nominations/user/:userId
 * Get nominations for a specific user (admin only)
 */
export async function getUserNominationsHandler(req: any, res: any): Promise<Response> {
  try {
    const adminUserId = req.user?.id;
    if (!adminUserId) {
      return new Response(JSON.stringify({ error: "Authentication required" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Check admin permissions
    const isAdmin = req.user?.role === 'admin' || req.user?.permissions?.includes('manage_badges');
    if (!isAdmin) {
      return new Response(JSON.stringify({ error: "Admin permissions required" }), {
        status: 403,
        headers: { "Content-Type": "application/json" },
      });
    }

    const { userId } = userParamsSchema.parse(req.params);
    const { status } = nominationQuerySchema.parse(req.query);
    
    const nominations = await nominationService.getNominationsForUser(userId, status);

    return new Response(JSON.stringify({
      success: true,
      data: {
        nominations,
        count: nominations.length
      }
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    return new Response(JSON.stringify({ 
      success: false,
      error: error.message || "Internal server error" 
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * GET /api/badges/nominations/stats/:badgeTypeId
 * Get nomination statistics for a badge type
 */
export async function getNominationStatsHandler(req: any, res: any): Promise<Response> {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return new Response(JSON.stringify({ error: "Authentication required" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    const { badgeTypeId } = badgeTypeParamsSchema.parse(req.params);
    
    const stats = await nominationService.getNominationStats(badgeTypeId);

    return new Response(JSON.stringify({
      success: true,
      data: { stats }
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    return new Response(JSON.stringify({ 
      success: false,
      error: error.message || "Internal server error" 
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * GET /api/badges/nominations/count/:badgeTypeId/:userId
 * Get nomination count for a specific user and badge type
 */
export async function getNominationCountHandler(req: any, res: any): Promise<Response> {
  try {
    const currentUserId = req.user?.id;
    if (!currentUserId) {
      return new Response(JSON.stringify({ error: "Authentication required" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    const { badgeTypeId } = badgeTypeParamsSchema.parse(req.params);
    const { userId } = userParamsSchema.parse(req.params);
    
    const count = await nominationService.getNominationCount(badgeTypeId, userId);

    return new Response(JSON.stringify({
      success: true,
      data: { 
        badgeTypeId,
        userId,
        nominationCount: count 
      }
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    return new Response(JSON.stringify({ 
      success: false,
      error: error.message || "Internal server error" 
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * POST /api/badges/nominations/:nominationId/approve
 * Approve a nomination (admin only)
 */
export async function approveNominationHandler(req: any, res: any): Promise<Response> {
  try {
    const adminUserId = req.user?.id;
    if (!adminUserId) {
      return new Response(JSON.stringify({ error: "Authentication required" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Check admin permissions
    const isAdmin = req.user?.role === 'admin' || req.user?.permissions?.includes('manage_badges');
    if (!isAdmin) {
      return new Response(JSON.stringify({ error: "Admin permissions required" }), {
        status: 403,
        headers: { "Content-Type": "application/json" },
      });
    }

    const { nominationId } = nominationParamsSchema.parse(req.params);
    
    const userBadge = await nominationService.approveNomination(nominationId, adminUserId);

    return new Response(JSON.stringify({
      success: true,
      data: {
        userBadge,
        message: "Nomination approved and badge assigned"
      }
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    return new Response(JSON.stringify({ 
      success: false,
      error: error.message || "Internal server error" 
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * POST /api/badges/nominations/:nominationId/reject
 * Reject a nomination (admin only)
 */
export async function rejectNominationHandler(req: any, res: any): Promise<Response> {
  try {
    const adminUserId = req.user?.id;
    if (!adminUserId) {
      return new Response(JSON.stringify({ error: "Authentication required" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Check admin permissions
    const isAdmin = req.user?.role === 'admin' || req.user?.permissions?.includes('manage_badges');
    if (!isAdmin) {
      return new Response(JSON.stringify({ error: "Admin permissions required" }), {
        status: 403,
        headers: { "Content-Type": "application/json" },
      });
    }

    const { nominationId } = nominationParamsSchema.parse(req.params);
    
    await nominationService.rejectNomination(nominationId, adminUserId);

    return new Response(JSON.stringify({
      success: true,
      data: {
        message: "Nomination rejected"
      }
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    return new Response(JSON.stringify({ 
      success: false,
      error: error.message || "Internal server error" 
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * GET /api/badges/nominations/analytics
 * Get nomination analytics and reporting (admin only)
 */
export async function getNominationAnalyticsHandler(req: any, res: any): Promise<Response> {
  try {
    const adminUserId = req.user?.id;
    if (!adminUserId) {
      return new Response(JSON.stringify({ error: "Authentication required" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Check admin permissions
    const isAdmin = req.user?.role === 'admin' || req.user?.permissions?.includes('manage_badges');
    if (!isAdmin) {
      return new Response(JSON.stringify({ error: "Admin permissions required" }), {
        status: 403,
        headers: { "Content-Type": "application/json" },
      });
    }

    // This would be implemented with more complex analytics queries
    // For now, return a placeholder structure
    const analytics = {
      totalNominations: 0,
      pendingNominations: 0,
      approvedNominations: 0,
      rejectedNominations: 0,
      topNominatedBadges: [],
      mostActiveNominators: [],
      recentActivity: [],
      conversionRate: 0,
    };

    return new Response(JSON.stringify({
      success: true,
      data: { analytics }
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    return new Response(JSON.stringify({ 
      success: false,
      error: error.message || "Internal server error" 
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}