import {
  addUserToServer,
  createServerInvite,
  getServerById,
  getServerDetails,
  getServerInviteByCode,
  getUserServers,
  incrementInviteUses,
  isUserServerMember,
  updateServerDetails,
} from "../db/utils";
import { db } from "../db";
import { WebSocketManager } from "../manager/websocket.manager";
import { z } from "zod";
import { hasServerPermission } from "../utils/permissions";
import { MANAGE_CHANNELS } from "../constants/permissions";
import { EventTypes } from "@kurultai/oba-types";
import {
  updateServerStructure,
  updateChannelPositions,
} from "../db/utils/bulk-operations";
import { ChannelSchema } from "../db/schema";
import { eq } from "drizzle-orm";
import { ResponseUtils } from "../utils/response-utils";

// Get WebSocketManager singleton instance for broadcasting updates
const wsManager = WebSocketManager.getInstance();

// Zod schema for server restructuring
const serverRestructureSchema = z.object({
  serverId: z.string().uuid(),
  structure: z.array(
    z.object({
      id: z.string().uuid(),
      type: z.enum(["category", "channel"]),
      position: z.number().int().min(0),
      parentId: z.string().uuid().nullable().optional(),
    }),
  ),
});

// Zod schema for channel reordering
const channelReorderSchema = z.object({
  serverId: z.string().uuid(),
  channelPositions: z.array(
    z.object({
      channelId: z.string().uuid(),
      position: z.number().int().min(0),
    }),
  ),
});

// Zod schema for adding channel to category
const addChannelToCategorySchema = z.object({
  serverId: z.string().uuid(),
  categoryId: z.string().uuid(),
  channelId: z.string().uuid(),
});

// Zod schema for removing channel from category
const removeChannelFromCategorySchema = z.object({
  serverId: z.string().uuid(),
  categoryId: z.string().uuid(),
  channelId: z.string().uuid(),
});

export const userServersHandler = async (req: Request) => {
  const contentType = req.headers.get("Content-Type");

  try {
    let user_id;
    if (contentType === "application/json") {
      const body = await req.json();
      //console.log(body);
      user_id = body.userId;
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      //console.log(formData);
      user_id = formData.get("userId") as string;
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    if (!user_id) return ResponseUtils.badRequest("User not found");
    const servers = await getUserServers(db, user_id);

    if (!servers) {
      return ResponseUtils.badRequest("No servers found for user");
    }

    // Return the server list in the response
    return ResponseUtils.success(servers, {
      message: "Servers retrieved successfully",
    });
  } catch (e) {
    return ResponseUtils.internalError();
  }
};

export const serverDetailsHandler = async (req: Request) => {
  const contentType = req.headers.get("Content-Type");

  try {
    let server_id;
    if (contentType === "application/json") {
      const body = await req.json();
      server_id = body.serverId;
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      server_id = formData.get("serverId") as string;
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    if (!server_id) return ResponseUtils.badRequest("Invalid Server ID");

    const details = await getServerDetails(db, server_id);

    if (!details) {
      return ResponseUtils.notFound("Server");
    }

    // Return the server details in the response
    return ResponseUtils.success(details, {
      message: "Server details retrieved successfully",
    });
  } catch (e) {
    return ResponseUtils.internalError();
  }
};

export const createServerInviteLinkHandler = async (req: Request) => {
  const contentType = req.headers.get("Content-Type");

  try {
    let serverId;
    let userId;
    let expiresAt;
    let maxUses;
    if (contentType === "application/json") {
      const body = await req.json();
      serverId = body.serverId;
      userId = body.userId;
      expiresAt = body.expiresAt;
      maxUses = body.maxUses;
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      serverId = formData.get("serverId") as string;
      userId = formData.get("userId") as string;
      expiresAt = formData.get("expiresAt") as string;
      maxUses = formData.get("maxUses") as string;
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Create the invite
    const newInvite = await createServerInvite(
      db,
      serverId,
      userId,
      expiresAt,
      maxUses,
    );

    if (!newInvite) {
      return ResponseUtils.internalError("Failed to create invite");
    }

    // Return the new invite data
    return ResponseUtils.created(newInvite, {
      message: "Invite created successfully",
    });
  } catch (e) {
    console.error("Error creating invite:", e);
    return ResponseUtils.internalError("Failed to create invite");
  }
};

export const getServerInformationFromInviteHandler = async (req: Request) => {
  const contentType = req.headers.get("Content-Type");

  try {
    let code;
    if (contentType === "application/json") {
      const body = await req.json();
      code = body.code;
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      code = formData.get("code") as string;
    } else {
      return ResponseUtils.unsupportedMediaType();
    }
    const invite = await getServerInviteByCode(db, code);

    if (!invite) {
      return ResponseUtils.badRequest("Invalid invite code");
    }

    const serverData = await getServerById(db, invite.serverId);
    // Return a success response
    return ResponseUtils.success(serverData, {
      message: "Server information retrieved successfully",
    });
  } catch (e) {
    console.error("Error fetching server details:", e);
    return ResponseUtils.internalError("Failed to fetch server details");
  }
};

export const serverJoinHandler = async (req: Request) => {
  const contentType = req.headers.get("Content-Type");

  try {
    let code;
    let userId;
    if (contentType === "application/json") {
      const body = await req.json();
      code = body.code;
      userId = body.userId;
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      code = formData.get("code") as string;
      userId = formData.get("userId") as string;
    } else {
      return ResponseUtils.unsupportedMediaType();
    }
    const invite = await getServerInviteByCode(db, code);

    if (!invite) {
      return ResponseUtils.badRequest("Invalid invite code");
    }

    // Check if the invite is expired
    if (invite.expiresAt && invite.expiresAt < new Date()) {
      return ResponseUtils.badRequest("Invite expired");
    }

    // Check if the invite has reached its maximum uses
    if (invite.maxUses !== null && invite.uses >= invite.maxUses) {
      return ResponseUtils.badRequest("Invite has reached its maximum uses");
    }

    // Check if the user is already a member of the server
    const isMember = await isUserServerMember(db, userId, invite.serverId);
    if (isMember) {
      return ResponseUtils.badRequest(
        "User is already a member of this server",
      );
    }

    // Add the user to the server
    await addUserToServer(db, userId, invite.serverId);

    // Increment the invite uses count
    await incrementInviteUses(db, invite.id);

    // Notify other clients about the server join
    // Not implemented, yet.
    //await notifyServerJoin(db, userId, invite.serverId);

    // Get the server information to return in the response
    const serverData = await getServerById(db, invite.serverId);

    // Return a success response
    return ResponseUtils.success(serverData, {
      message: "User joined server successfully",
    });
  } catch (error) {
    console.error("Error joining server:", error);
    return ResponseUtils.internalError("Failed to join server");
  }
};

/**
 * Handler for updating server details
 */
export const updateServerHandler = async (req: Request) => {
  const contentType = req.headers.get("Content-Type");

  try {
    let serverId;
    let userId;
    let name;
    let description;
    let icon;

    // Parse request body based on content type
    if (contentType === "application/json") {
      const body = await req.json();
      serverId = body.serverId;
      userId = body.userId;
      name = body.name;
      description = body.description;
      icon = body.icon;
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      serverId = formData.get("serverId") as string;
      userId = formData.get("userId") as string;
      name = formData.get("name") as string;
      description = formData.get("description") as string;
      icon = formData.get("icon") as string;
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate required fields
    if (!serverId || !userId) {
      return ResponseUtils.badRequest("Server ID and User ID are required");
    }

    // Prepare updates object
    const updates: {
      name?: string;
      description?: string;
      icon?: string;
    } = {};

    if (name) updates.name = name;
    if (description) updates.description = description;
    if (icon) updates.icon = icon;

    // If no updates provided, return error
    if (Object.keys(updates).length === 0) {
      return ResponseUtils.badRequest("No updates provided");
    }

    try {
      // Update server details
      const updatedServer = await updateServerDetails(
        db,
        serverId,
        updates,
        userId,
      );

      // Broadcast the update to all server members
      try {
        // Broadcast the update using our WebSocketManager instance
        await wsManager.broadcastServerUpdate(serverId, updatedServer, userId);
        console.log(`Server update for ${serverId} broadcast to all members`);
      } catch (broadcastError) {
        // Log the error but don't fail the request
        console.error("Error broadcasting server update:", broadcastError);
      }

      // Return success response
      return ResponseUtils.success(updatedServer, {
        message: "Server updated successfully",
      });
    } catch (error) {
      // Handle specific errors from updateServerDetails
      if (error instanceof Error) {
        if (error.message === "Server not found") {
          return ResponseUtils.notFound("Server");
        }
        if (error.message.includes("Unauthorized")) {
          return ResponseUtils.forbidden(
            "You do not have permission to update server details",
          );
        }
      }
      throw error; // Re-throw for the outer catch block
    }
  } catch (error) {
    console.error("Error updating server:", error);
    return ResponseUtils.internalError("Failed to update server");
  }
};

export const updateServerIconHandler = async (req: Request) => {
  const contentType = req.headers.get("Content-Type");

  try {
    let serverId;
    let userId;
    let icon;

    // Parse request body based on content type
    if (contentType === "application/json") {
      const body = await req.json();
      serverId = body.serverId;
      userId = body.userId;
      icon = body.icon;
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      serverId = formData.get("serverId") as string;
      userId = formData.get("userId") as string;
      icon = formData.get("icon") as string;
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate required fields
    if (!serverId || !userId || !icon) {
      console.error("Missing required fields:", { serverId, userId, icon });
      return ResponseUtils.badRequest(
        "Server ID, User ID, and icon are required",
      );
    }

    // Update server icon
    try {
      const updatedServer = await updateServerDetails(
        db,
        serverId,
        { icon },
        userId,
      );

      // Broadcast the update to all server members
      try {
        // Broadcast the update using our WebSocketManager instance
        await wsManager.broadcastServerUpdate(serverId, updatedServer, userId);
        console.log(
          `Server icon update for ${serverId} broadcast to all members`,
        );
      } catch (broadcastError) {
        // Log the error but don't fail the request
        console.error("Error broadcasting server icon update:", broadcastError);
      }

      // Return success response
      return ResponseUtils.success(updatedServer, {
        message: "Server icon updated successfully",
      });
    } catch (error) {
      // Handle specific errors from updateServerDetails
      if (error instanceof Error) {
        if (error.message === "Server not found") {
          return ResponseUtils.notFound("Server");
        }
        if (error.message.includes("Unauthorized")) {
          return ResponseUtils.forbidden(
            "You do not have permission to update server icon",
          );
        }
      }
      throw error; // Re-throw for the outer catch block
    }
  } catch (error) {
    console.error("Error updating server icon:", error);
    return ResponseUtils.internalError("Failed to update server icon");
  }
};

/**
 * Handler for restructuring a server
 */
export const restructureServerHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let data: {
      serverId?: string;
      structure?: Array<{
        id: string;
        type: "category" | "channel";
        position: number;
        parentId?: string | null;
      }>;
      userId?: string;
    };

    if (contentType === "application/json") {
      data = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      data = {
        serverId: formData.get("serverId") as string,
        structure: formData.has("structure")
          ? JSON.parse(formData.get("structure") as string)
          : undefined,
        userId: formData.get("userId") as string,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = serverRestructureSchema.parse(data);

    // Check if the user has permission to manage channels
    if (data.userId) {
      const hasPermission = await hasServerPermission(
        db,
        data.userId,
        validatedData.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to restructure the server",
        );
      }
    }

    // Update server structure
    const success = await updateServerStructure(
      validatedData.serverId,
      validatedData.structure,
    );

    if (!success) {
      return ResponseUtils.internalError("Failed to restructure server");
    }

    // Broadcast the server restructuring via WebSocket
    const serverEvent = {
      type: EventTypes.SERVER_RESTRUCTURE,
      sender: data.userId || "system",
      data: {
        server_id: validatedData.serverId,
        structure: validatedData.structure,
      },
    };

    wsManager.broadcast(JSON.stringify(serverEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success(
      {},
      { message: "Server restructured successfully" },
    );
  } catch (error) {
    console.error("Error restructuring server:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError();
  }
};

/**
 * Handler for reordering channels
 */
export const reorderChannelsHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let data: {
      serverId?: string;
      channelPositions?: Array<{ channelId: string; position: number }>;
      userId?: string;
    };

    if (contentType === "application/json") {
      data = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      data = {
        serverId: formData.get("serverId") as string,
        channelPositions: formData.has("channelPositions")
          ? JSON.parse(formData.get("channelPositions") as string)
          : undefined,
        userId: formData.get("userId") as string,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = channelReorderSchema.parse(data);

    // Check if the user has permission to manage channels
    if (data.userId) {
      const hasPermission = await hasServerPermission(
        db,
        data.userId,
        validatedData.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to reorder channels",
        );
      }
    }

    // Update channel positions
    const success = await updateChannelPositions(
      validatedData.channelPositions,
    );

    if (!success) {
      return ResponseUtils.internalError("Failed to reorder channels");
    }

    // Broadcast the channel reordering via WebSocket
    const channelEvent = {
      type: EventTypes.CHANNELS_REORDER,
      sender: data.userId || "system",
      data: {
        server_id: validatedData.serverId,
        channel_positions: validatedData.channelPositions,
      },
    };

    wsManager.broadcast(JSON.stringify(channelEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success(
      {},
      { message: "Channels reordered successfully" },
    );
  } catch (error) {
    console.error("Error reordering channels:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError();
  }
};

/**
 * Handler for adding a channel to a category
 */
export const addChannelToCategoryHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let data: {
      serverId?: string;
      categoryId?: string;
      channelId?: string;
      userId?: string;
    };

    if (contentType === "application/json") {
      data = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      data = {
        serverId: formData.get("serverId") as string,
        categoryId: formData.get("categoryId") as string,
        channelId: formData.get("channelId") as string,
        userId: formData.get("userId") as string,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = addChannelToCategorySchema.parse(data);

    // Check if the user has permission to manage channels
    if (data.userId) {
      const hasPermission = await hasServerPermission(
        db,
        data.userId,
        validatedData.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to manage channels",
        );
      }
    }

    // Update channel's category
    const success = await db
      .update(ChannelSchema)
      .set({ categoryId: validatedData.categoryId })
      .where(eq(ChannelSchema.id, validatedData.channelId))
      .returning();

    if (!success) {
      return ResponseUtils.internalError("Failed to add channel to category");
    }

    // Broadcast the channel addition via WebSocket
    const channelEvent = {
      type: EventTypes.CHANNEL_ADDED_TO_CATEGORY,
      sender: data.userId || "system",
      data: {
        server_id: validatedData.serverId,
        category_id: validatedData.categoryId,
        channel_id: validatedData.channelId,
      },
    };

    wsManager.broadcast(JSON.stringify(channelEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success(
      {},
      { message: "Channel added to category successfully" },
    );
  } catch (error) {
    console.error("Error adding channel to category:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError();
  }
};

/**
 * Handler for removing a channel from a category
 */
export const removeChannelFromCategoryHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let data: {
      serverId?: string;
      categoryId?: string;
      channelId?: string;
      userId?: string;
    };

    if (contentType === "application/json") {
      data = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      data = {
        serverId: formData.get("serverId") as string,
        categoryId: formData.get("categoryId") as string,
        channelId: formData.get("channelId") as string,
        userId: formData.get("userId") as string,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = removeChannelFromCategorySchema.parse(data);

    // Check if the user has permission to manage channels
    if (data.userId) {
      const hasPermission = await hasServerPermission(
        db,
        data.userId,
        validatedData.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to manage channels",
        );
      }
    }

    // Update channel's category to null
    const success = await db
      .update(ChannelSchema)
      .set({ categoryId: null })
      .where(eq(ChannelSchema.id, validatedData.channelId))
      .returning();

    if (!success) {
      return ResponseUtils.internalError(
        "Failed to remove channel from category",
      );
    }

    // Broadcast the channel removal via WebSocket
    const channelEvent = {
      type: EventTypes.CHANNEL_REMOVED_FROM_CATEGORY,
      sender: data.userId || "system",
      data: {
        server_id: validatedData.serverId,
        category_id: validatedData.categoryId,
        channel_id: validatedData.channelId,
      },
    };

    wsManager.broadcast(JSON.stringify(channelEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success(
      {},
      { message: "Channel removed from category successfully" },
    );
  } catch (error) {
    console.error("Error removing channel from category:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError();
  }
};
