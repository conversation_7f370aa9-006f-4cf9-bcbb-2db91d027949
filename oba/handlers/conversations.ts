import { z } from "zod";
import { db } from "../db";
import {
  getUserDirectMessageContacts,
  getDirectMessagesBetweenUsers,
  createDirectMessage,
  deleteDirectMessage,
  getUserById,
} from "../db/utils";
import { ResponseUtils } from "../utils/response-utils";

// Type definitions based on the API usage
interface Contact {
  id: string;
  username: string;
  displayName: string;
  avatar: string | null;
  status: "ONLINE" | "AWAY" | "BUSY" | "INVISIBLE" | "OFFLINE";
  lastSeen: Date | null;
  statusMessage: string | null;
}

interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  createdAt: Date;
  editedAt: Date | null;
  readAt: Date | null;
  attachments: string | null;
  sender: {
    id: string;
    username: string;
    avatar: string | null;
  };
}

interface Conversation {
  id: string; // Using contact.id as conversation ID
  contact: Contact;
  latestMessage: Message | null;
  unreadCount: number;
}

// Zod schemas
const getConversationsSchema = z.object({
  userId: z.string().uuid(),
  limit: z.number().int().positive().default(50),
  offset: z.number().int().min(0).default(0),
  statusFilter: z.enum(["online", "offline", "all"]).default("all"),
  searchQuery: z.string().optional(),
});

const createConversationSchema = z.object({
  userId: z.string().uuid(),
});

const getMessagesSchema = z.object({
  conversationId: z.string().uuid(),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().default(50),
});

/**
 * Handler for getting all conversations for a user
 * This includes both the contact list and the latest message for each contact
 */
export const getConversationsHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    let userId = url.searchParams.get("userId");
    const limitParam = url.searchParams.get("limit");
    const offsetParam = url.searchParams.get("offset");
    const statusFilterParam = url.searchParams.get("statusFilter");
    const searchQueryParam = url.searchParams.get("searchQuery");

    if (!userId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    if (userId === "me") {
      // If userId is 'me', get the authenticated user's ID from request context
      const user = (req as any).user;
      if (!user?.userId) {
        return ResponseUtils.unauthorized("User not authenticated");
      }
      userId = user.userId;
    }

    const limit = limitParam ? parseInt(limitParam, 10) : 50;
    const offset = offsetParam ? parseInt(offsetParam, 10) : 0;

    // Validate input
    const validatedData = getConversationsSchema.parse({
      userId,
      limit,
      offset,
      statusFilter:
        (statusFilterParam as "online" | "offline" | "all") || "all",
      searchQuery: searchQueryParam || "",
    });

    // Get all contacts
    const contacts = await getUserDirectMessageContacts(
      db,
      validatedData.userId,
    );

    // For each contact, get the latest message
    const conversations: Conversation[] = await Promise.all(
      contacts
        .filter((contact) => {
          if (
            validatedData.statusFilter !== "all" &&
            contact.status !== validatedData.statusFilter
          ) {
            return false;
          }
          if (
            validatedData.searchQuery &&
            !contact.username.includes(validatedData.searchQuery) &&
            !contact.displayName?.includes(validatedData.searchQuery)
          ) {
            return false;
          }
          return true;
        })
        .map(async (contact) => {
          const messages = await getDirectMessagesBetweenUsers(
            db,
            validatedData.userId,
            contact.id,
            1, // Only get the latest message
          );

          // Calculate unread count
          const allMessages = await getDirectMessagesBetweenUsers(
            db,
            validatedData.userId,
            contact.id,
            1000, // Get more messages to calculate unread count
          );

          const unreadCount = allMessages.filter(
            (m) =>
              m.message.receiverId === validatedData.userId &&
              !m.message.readAt,
          ).length;

          return {
            id: contact.id, // Use contact ID as conversation ID
            contact: {
              id: contact.id,
              username: contact.username,
              displayName: contact.username,
              avatar: contact.avatar,
              status: contact.status as any,
              lastSeen: contact.lastSeen,
              statusMessage: contact.statusMessage,
            },
            latestMessage: messages[0]
              ? {
                  id: messages[0].message.id,
                  senderId: messages[0].message.senderId,
                  receiverId: messages[0].message.receiverId,
                  content: messages[0].message.content,
                  createdAt: messages[0].message.createdAt,
                  editedAt: messages[0].message.editedAt,
                  readAt: messages[0].message.readAt,
                  attachments: messages[0].message.attachments,
                  sender: messages[0].sender,
                }
              : null,
            unreadCount,
          };
        }),
    );

    // Sort conversations by latest message date, if available
    const sortedConversations = conversations.sort((a, b) => {
      if (!a.latestMessage) return 1;
      if (!b.latestMessage) return -1;
      return (
        new Date(b.latestMessage.createdAt).getTime() -
        new Date(a.latestMessage.createdAt).getTime()
      );
    });

    // Apply pagination
    const paginatedConversations = sortedConversations.slice(
      validatedData.offset,
      validatedData.offset + validatedData.limit,
    );

    // Return conversations
    return ResponseUtils.success({
      conversations: paginatedConversations,
      total: sortedConversations.length,
      hasMore:
        validatedData.offset + validatedData.limit < sortedConversations.length,
    });
  } catch (error) {
    console.error("Error retrieving conversations:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to retrieve conversations");
  }
};

/**
 * Handler for creating a new conversation
 * In a direct message system, this essentially means starting a conversation with a user
 */
export const createConversationHandler = async (req: Request) => {
  try {
    const requestBody = await req.json();
    const { userId } = createConversationSchema.parse(requestBody);

    // Get the authenticated user's ID
    const currentUser = (req as any).user;
    if (!currentUser?.userId) {
      return ResponseUtils.unauthorized("User not authenticated");
    }

    // Check if the target user exists
    const targetUser = await getUserById(db, userId);
    if (!targetUser) {
      return ResponseUtils.notFound("User not found");
    }

    // Check if user is trying to create conversation with themselves
    if (currentUser.userId === userId) {
      return ResponseUtils.badRequest(
        "Cannot create conversation with yourself",
      );
    }

    // In a direct message system, creating a conversation doesn't require
    // inserting anything into the database until the first message is sent.
    // We'll just return the conversation structure.

    const conversation: Conversation = {
      id: targetUser.id,
      contact: {
        id: targetUser.id,
        username: targetUser.username,
        displayName: targetUser.username,
        avatar: targetUser.avatar,
        status: targetUser.status as any,
        lastSeen: targetUser.lastActive,
        statusMessage: targetUser.statusMessage,
      },
      latestMessage: null,
      unreadCount: 0,
    };

    return ResponseUtils.created(conversation);
  } catch (error) {
    console.error("Error creating conversation:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to create conversation");
  }
};

/**
 * Handler for deleting a conversation
 * In a direct message system, this typically means hiding the conversation
 * or deleting all messages between the users
 */
export const deleteConversationHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    // Extract conversationId from the URL path /api/conversations/delete/:conversationId
    const pathSegments = url.pathname.split("/");
    const conversationId = pathSegments[pathSegments.length - 1];

    if (!conversationId) {
      return ResponseUtils.badRequest("Conversation ID is required");
    }

    // Get the authenticated user's ID
    const currentUser = (req as any).user;
    if (!currentUser?.userId) {
      return ResponseUtils.unauthorized("User not authenticated");
    }

    // In a direct message system, the conversationId is actually the other user's ID
    // Get all messages between the current user and the target user
    const messages = await getDirectMessagesBetweenUsers(
      db,
      currentUser.userId,
      conversationId,
      1000, // Get a large number to delete all
    );

    // Delete all messages between the users (sent by current user)
    // Note: You might want to implement a "soft delete" instead
    for (const message of messages) {
      if (message.message.senderId === currentUser.userId) {
        await deleteDirectMessage(db, message.message.id);
      }
    }

    return ResponseUtils.success({
      message: "Conversation deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting conversation:", error);
    return ResponseUtils.internalError("Failed to delete conversation");
  }
};

/**
 * Handler for getting messages in a conversation
 */
export const getMessagesHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    // Extract conversationId from the URL path /api/conversations/:conversationId/messages
    const pathSegments = url.pathname.split("/");
    const conversationId = pathSegments[pathSegments.length - 2]; // Get conversation ID from path
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "50");

    if (!conversationId) {
      return ResponseUtils.badRequest("Conversation ID is required");
    }

    // Get the authenticated user's ID
    const currentUser = (req as any).user;
    if (!currentUser?.userId) {
      return ResponseUtils.unauthorized("User not authenticated");
    }

    // Validate input
    const validatedData = getMessagesSchema.parse({
      conversationId,
      page,
      limit,
    });

    // Calculate offset based on page
    const offset = (validatedData.page - 1) * validatedData.limit;

    // Get messages between the current user and the target user
    const messages = await getDirectMessagesBetweenUsers(
      db,
      currentUser.userId,
      conversationId,
      validatedData.limit,
    );

    // Transform messages to match the expected format
    const transformedMessages: Message[] = messages.map((msg) => ({
      id: msg.message.id,
      senderId: msg.message.senderId,
      receiverId: msg.message.receiverId,
      content: msg.message.content,
      createdAt: msg.message.createdAt,
      editedAt: msg.message.editedAt,
      readAt: msg.message.readAt,
      attachments: msg.message.attachments,
      sender: msg.sender,
    }));

    return ResponseUtils.success({
      data: transformedMessages,
      pagination: {
        page: validatedData.page,
        limit: validatedData.limit,
        total: transformedMessages.length,
        hasMore: transformedMessages.length === validatedData.limit,
      },
    });
  } catch (error) {
    console.error("Error retrieving messages:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to retrieve messages");
  }
};
