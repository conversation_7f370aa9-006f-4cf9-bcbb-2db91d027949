import { z } from "zod";
import { db } from "../db";
import {
  addMessageReaction,
  removeMessageReaction,
  getMessageReactions,
  getMessageReactionCounts,
  getMessageById,
} from "../db/utils";
import { EventTypes } from "@kurultai/oba-types";
import { WebSocketManager } from "../manager/websocket.manager";
import { ResponseUtils } from "../utils/response-utils";

// Get WebSocketManager singleton instance
const wsManager = WebSocketManager.getInstance();

// Zod schema for adding a reaction
const addReactionSchema = z.object({
  messageId: z.string().uuid(),
  userId: z.string().uuid(),
  emoji: z.string().min(1).max(10), // Limit emoji length
  channelId: z.string().uuid(),
  serverId: z.string().uuid(),
});

// Zod schema for removing a reaction
const removeReactionSchema = z.object({
  messageId: z.string().uuid(),
  userId: z.string().uuid(),
  emoji: z.string().min(1).max(10),
  channelId: z.string().uuid(),
  serverId: z.string().uuid(),
});

// Zod schema for getting reactions
const getReactionsSchema = z.object({
  messageId: z.string().uuid(),
});

/**
 * Handler for adding a reaction to a message
 */
export const addReactionHandler = async (req: Request): Promise<Response> => {
  try {
    const contentType = req.headers.get("Content-Type");
    let reactionData: {
      messageId?: string;
      userId?: string;
      emoji?: string;
      channelId?: string;
      serverId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      reactionData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      reactionData = {
        messageId: formData.get("messageId") as string | undefined,
        userId: formData.get("userId") as string | undefined,
        emoji: formData.get("emoji") as string | undefined,
        channelId: formData.get("channelId") as string | undefined,
        serverId: formData.get("serverId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = addReactionSchema.parse(reactionData);

    // Verify the message exists
    const message = await getMessageById(db, validatedData.messageId);
    if (!message) {
      return ResponseUtils.notFound("Message not found");
    }

    // Add the reaction
    const reaction = await addMessageReaction(
      db,
      validatedData.messageId,
      validatedData.userId,
      validatedData.emoji,
    );

    // Get updated reaction counts for the message
    const reactionCounts = await getMessageReactionCounts(
      db,
      validatedData.messageId,
    );

    // Broadcast the reaction via WebSocket
    const reactionEvent = {
      type: EventTypes.MESSAGE_REACTION_ADDED,
      sender: validatedData.userId,
      data: {
        messageId: validatedData.messageId,
        userId: validatedData.userId,
        emoji: validatedData.emoji,
        channelId: validatedData.channelId,
        serverId: validatedData.serverId,
        reactionCounts,
      },
    };

    wsManager.broadcast(
      JSON.stringify(reactionEvent),
      validatedData.serverId,
      validatedData.channelId,
    );

    // Return success response
    return ResponseUtils.success({
      reaction,
      reactionCounts,
    });
  } catch (error) {
    console.error("Error adding reaction:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map(err => ({
          field: err.path.join('.') || 'unknown',
          message: err.message
        }))
      );
    }

    return ResponseUtils.internalError("Failed to add reaction");
  }
};

/**
 * Handler for removing a reaction from a message
 */
export const removeReactionHandler = async (req: Request): Promise<Response> => {
  try {
    const contentType = req.headers.get("Content-Type");
    let reactionData: {
      messageId?: string;
      userId?: string;
      emoji?: string;
      channelId?: string;
      serverId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      reactionData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      reactionData = {
        messageId: formData.get("messageId") as string | undefined,
        userId: formData.get("userId") as string | undefined,
        emoji: formData.get("emoji") as string | undefined,
        channelId: formData.get("channelId") as string | undefined,
        serverId: formData.get("serverId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = removeReactionSchema.parse(reactionData);

    try {
      // Remove the reaction
      await removeMessageReaction(
        db,
        validatedData.messageId,
        validatedData.userId,
        validatedData.emoji,
      );

      // Get updated reaction counts for the message
      const reactionCounts = await getMessageReactionCounts(
        db,
        validatedData.messageId,
      );

      // Broadcast the reaction removal via WebSocket
      const reactionEvent = {
        type: EventTypes.MESSAGE_REACTION_REMOVED,
        sender: validatedData.userId,
        data: {
          messageId: validatedData.messageId,
          userId: validatedData.userId,
          emoji: validatedData.emoji,
          channelId: validatedData.channelId,
          serverId: validatedData.serverId,
          reactionCounts,
        },
      };

      wsManager.broadcast(
        JSON.stringify(reactionEvent),
        validatedData.serverId,
        validatedData.channelId,
      );

      // Return success response
      return ResponseUtils.success({
        reactionCounts,
      });
    } catch (error) {
      // If the reaction doesn't exist, return a 404
      if (error instanceof Error && error.message === "Reaction not found") {
        return ResponseUtils.notFound("Reaction not found");
      }
      throw error;
    }
  } catch (error) {
    console.error("Error removing reaction:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map(err => ({
          field: err.path.join('.') || 'unknown',
          message: err.message
        }))
      );
    }

    return ResponseUtils.internalError("Failed to remove reaction");
  }
};

/**
 * Handler for getting reactions for a message
 */
export const getReactionsHandler = async (req: Request): Promise<Response> => {
  try {
    const url = new URL(req.url);
    const messageId = url.searchParams.get("messageId");

    if (!messageId) {
      return ResponseUtils.badRequest("Message ID is required");
    }

    // Validate input
    const validatedData = getReactionsSchema.parse({ messageId });

    // Get reactions
    const reactions = await getMessageReactions(db, validatedData.messageId);

    // Get reaction counts
    const reactionCounts = await getMessageReactionCounts(
      db,
      validatedData.messageId,
    );

    // Return reactions
    return ResponseUtils.success({
      reactions,
      reactionCounts,
    });
  } catch (error) {
    console.error("Error retrieving reactions:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map(err => ({
          field: err.path.join('.') || 'unknown',
          message: err.message
        }))
      );
    }

    return ResponseUtils.internalError("Failed to retrieve reactions");
  }
};
