import { z } from "zod";
import { db } from "../db";
import {
  createChannelCategory,
  updateChannelCategory,
  deleteChannelCategory,
  getServerCategories,
  getCategoryById,
  getCategoryChannels,
} from "../db/utils";
import { WebSocketManager } from "../manager/websocket.manager";
import { hasServerPermission } from "../utils/permissions";
import { MANAGE_CHANNELS } from "../constants/permissions";
import { EventTypes } from "@kurultai/oba-types";
import { ResponseUtils } from "../utils/response-utils";

// Get WebSocketManager singleton instance
const wsManager = WebSocketManager.getInstance();

// Zod schema for category creation
const createCategorySchema = z.object({
  serverId: z.string().uuid(),
  name: z.string().min(1).max(100),
  description: z.string().max(1000).optional(),
  position: z.number().optional(),
});

// Zod schema for category update
const updateCategorySchema = z.object({
  categoryId: z.string().uuid(),
  serverId: z.string().uuid(),
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(1000).optional(),
  position: z.number().optional(),
});

// Zod schema for category deletion
const deleteCategorySchema = z.object({
  categoryId: z.string().uuid(),
  serverId: z.string().uuid(),
});

// Zod schema for getting category details
const getCategorySchema = z.object({
  categoryId: z.string().uuid(),
});

// Zod schema for getting server categories
const getServerCategoriesSchema = z.object({
  serverId: z.string().uuid(),
});

// Zod schema for getting category channels
const getCategoryChannelsSchema = z.object({
  categoryId: z.string().uuid(),
});

/**
 * Handler for creating a new channel category
 */
export const createCategoryHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let categoryData: {
      serverId?: string;
      name?: string;
      description?: string;
      position?: number;
      userId?: string; // Added userId for permission checking
    };

    if (contentType === "application/json") {
      categoryData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      categoryData = {
        serverId: formData.get("serverId") as string,
        name: formData.get("name") as string,
        description: formData.get("description") as string,
        position: formData.get("position")
          ? Number(formData.get("position"))
          : undefined,
        userId: formData.get("userId") as string,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = createCategorySchema.parse(categoryData);

    // Check if the user has permission to create categories
    if (categoryData.userId) {
      const hasPermission = await hasServerPermission(
        db,
        categoryData.userId,
        validatedData.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to create categories",
        );
      }
    }

    // Create the category
    const category = await createChannelCategory(
      db,
      validatedData.serverId,
      validatedData.name,
      validatedData.description,
      validatedData.position,
    );

    if (!category) {
      return ResponseUtils.internalError("Failed to create category");
    }

    // Broadcast the category creation via WebSocket
    const categoryEvent = {
      type: EventTypes.CATEGORY_CREATED,
      sender: categoryData.userId || "system",
      data: {
        category_id: category.id,
        category_name: category.name,
        server_id: category.serverId,
        description: category.description,
        position: category.position,
      },
    };

    wsManager.broadcast(JSON.stringify(categoryEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.created(category, {
      message: "Category created successfully",
    });
  } catch (error) {
    console.error("Error creating category:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError();
  }
};

/**
 * Handler for updating a channel category
 */
export const updateCategoryHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let categoryData: {
      categoryId?: string;
      serverId?: string;
      name?: string;
      description?: string;
      position?: number;
      userId?: string; // Added userId for permission checking
    };

    if (contentType === "application/json") {
      categoryData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      categoryData = {
        categoryId: formData.get("categoryId") as string,
        serverId: formData.get("serverId") as string,
        name: formData.get("name") as string,
        description: formData.get("description") as string,
        position: formData.get("position")
          ? Number(formData.get("position"))
          : undefined,
        userId: formData.get("userId") as string,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = updateCategorySchema.parse(categoryData);

    // Check if the user has permission to update categories
    if (categoryData.userId) {
      const hasPermission = await hasServerPermission(
        db,
        categoryData.userId,
        validatedData.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to update categories",
        );
      }
    }

    // Update the category
    const updates = {
      name: validatedData.name,
      description: validatedData.description,
      position: validatedData.position,
    };

    const category = await updateChannelCategory(
      db,
      validatedData.categoryId,
      updates,
    );

    if (!category) {
      return ResponseUtils.notFound("Category");
    }

    // Broadcast the category update via WebSocket
    const categoryEvent = {
      type: EventTypes.CATEGORY_UPDATED,
      sender: categoryData.userId || "system",
      data: {
        category_id: category.id,
        category_name: category.name,
        server_id: category.serverId,
        description: category.description,
        position: category.position,
      },
    };

    wsManager.broadcast(JSON.stringify(categoryEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success(category, {
      message: "Category updated successfully",
    });
  } catch (error) {
    console.error("Error updating category:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError();
  }
};

/**
 * Handler for deleting a channel category
 */
export const deleteCategoryHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let categoryData: {
      categoryId?: string;
      serverId?: string;
      userId?: string; // Added userId for permission checking
    };

    if (contentType === "application/json") {
      categoryData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      categoryData = {
        categoryId: formData.get("categoryId") as string,
        serverId: formData.get("serverId") as string,
        userId: formData.get("userId") as string,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = deleteCategorySchema.parse(categoryData);

    // Check if the user has permission to delete categories
    if (categoryData.userId) {
      const hasPermission = await hasServerPermission(
        db,
        categoryData.userId,
        validatedData.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to delete categories",
        );
      }
    }

    // Delete the category
    const success = await deleteChannelCategory(db, validatedData.categoryId);

    if (!success) {
      return ResponseUtils.notFound("Category");
    }

    // Broadcast the category deletion via WebSocket
    const categoryEvent = {
      type: EventTypes.CATEGORY_DELETED,
      sender: categoryData.userId || "system",
      data: {
        category_id: validatedData.categoryId,
        server_id: validatedData.serverId,
      },
    };

    wsManager.broadcast(JSON.stringify(categoryEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success(
      { categoryId: validatedData.categoryId },
      { message: "Category deleted successfully" },
    );
  } catch (error) {
    console.error("Error deleting category:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError();
  }
};

/**
 * Handler for getting server categories
 */
export const getServerCategoriesHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const serverId = url.searchParams.get("serverId");
    const userId = url.searchParams.get("userId");

    if (!serverId) {
      return ResponseUtils.badRequest("Server ID is required");
    }

    // Validate input using Zod
    const validatedData = getServerCategoriesSchema.parse({ serverId });

    // Get the server categories
    const categories = await getServerCategories(db, validatedData.serverId);

    // Return success response
    return ResponseUtils.success(categories, {
      message: "Server categories retrieved successfully",
    });
  } catch (error) {
    console.error("Error getting server categories:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError();
  }
};

/**
 * Handler for getting category details
 */
export const getCategoryDetailsHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const categoryId = url.searchParams.get("categoryId");

    if (!categoryId) {
      return ResponseUtils.badRequest("Category ID is required");
    }

    // Validate input using Zod
    const validatedData = getCategorySchema.parse({ categoryId });

    // Get the category details
    const category = await getCategoryById(db, validatedData.categoryId);

    if (!category) {
      return ResponseUtils.notFound("Category");
    }

    // Return success response
    return ResponseUtils.success(category, {
      message: "Category details retrieved successfully",
    });
  } catch (error) {
    console.error("Error getting category details:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError();
  }
};

/**
 * Handler for getting category channels
 */
export const getCategoryChannelsHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const categoryId = url.searchParams.get("categoryId");

    if (!categoryId) {
      return ResponseUtils.badRequest("Category ID is required");
    }

    // Validate input using Zod
    const validatedData = getCategoryChannelsSchema.parse({ categoryId });

    // Get the category channels
    const channels = await getCategoryChannels(db, validatedData.categoryId);

    // Return success response
    return ResponseUtils.success(channels, {
      message: "Category channels retrieved successfully",
    });
  } catch (error) {
    console.error("Error getting category channels:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError();
  }
};
