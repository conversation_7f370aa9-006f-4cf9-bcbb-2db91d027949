import { z } from "zod";
import { db } from "../db";
import {
  createRole,
  updateRole,
  deleteRole,
  getServerRoles,
  getUserRoles,
  assignRoleToUser,
  removeRoleFromUser,
  hasServerPermission,
} from "../utils/permissions";
import { WebSocketManager } from "../manager/websocket.manager";
import { MANAGE_ROLES, ADMINISTRATOR } from "../constants/permissions";
import {
  permissionBitmaskToNames,
  permissionNamesToBitmask,
} from "../constants/permissions";
import { EventTypes } from "@kurultai/oba-types";
import { ResponseUtils } from "../utils/response-utils";

// Get WebSocketManager singleton instance
const wsManager = WebSocketManager.getInstance();

// Zod schema for role creation
const createRoleSchema = z.object({
  serverId: z.string().uuid(),
  name: z.string().min(1).max(100),
  permissions: z.array(z.string()).optional(),
  color: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional(),
});

// Zod schema for role update
const updateRoleSchema = z.object({
  roleId: z.string().uuid(),
  serverId: z.string().uuid(),
  name: z.string().min(1).max(100).optional(),
  permissions: z.array(z.string()).optional(),
  color: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional(),
});

// Zod schema for role deletion
const deleteRoleSchema = z.object({
  roleId: z.string().uuid(),
  serverId: z.string().uuid(),
});

// Zod schema for getting server roles
const getServerRolesSchema = z.object({
  serverId: z.string().uuid(),
});

// Zod schema for getting user roles
const getUserRolesSchema = z.object({
  userId: z.string().uuid(),
  serverId: z.string().uuid(),
});

// Zod schema for assigning a role to a user
const assignRoleSchema = z.object({
  userId: z.string().uuid(),
  roleId: z.string().uuid(),
  serverId: z.string().uuid(),
});

// Zod schema for removing a role from a user
const removeRoleSchema = z.object({
  userId: z.string().uuid(),
  roleId: z.string().uuid(),
  serverId: z.string().uuid(),
});

/**
 * Handler for creating a new role
 */
export const createRoleHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let roleData: {
      serverId?: string;
      name?: string;
      permissions?: string[];
      color?: string;
      userId?: string; // Added userId for permission checking
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      roleData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      roleData = {
        serverId: formData.get("serverId") as string | undefined,
        name: formData.get("name") as string | undefined,
        permissions: formData.has("permissions")
          ? (formData.get("permissions") as string).split(",")
          : undefined,
        color: formData.get("color") as string | undefined,
        userId: formData.get("userId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedContentType();
    }

    // Validate input using Zod
    const validatedData = createRoleSchema.parse(roleData);

    // Check if the user has permission to manage roles
    if (roleData.userId) {
      const hasPermission = await hasServerPermission(
        db,
        roleData.userId,
        validatedData.serverId,
        MANAGE_ROLES,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to manage roles",
        );
      }
    }

    // Convert permission names to bitmask
    const permissionBitmask = validatedData.permissions
      ? permissionNamesToBitmask(validatedData.permissions)
      : BigInt(0);

    // Create the role
    const role = await createRole(
      db,
      validatedData.serverId,
      validatedData.name,
      permissionBitmask,
    );

    if (!role) {
      return ResponseUtils.internalError("Failed to create role");
    }

    // Broadcast the role creation via WebSocket
    const roleEvent = {
      type: EventTypes.ROLE_CREATED,
      sender: roleData.userId || "system",
      data: {
        role_id: role.id,
        role_name: role.name,
        server_id: role.serverId,
        permissions: permissionBitmaskToNames(role.permissions),
      },
    };

    wsManager.broadcast(JSON.stringify(roleEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success(
      {
        success: true,
        role: {
          ...role,
          permissions: permissionBitmaskToNames(role.permissions),
        },
      },
      201,
    );
  } catch (error) {
    console.error("Error creating role:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to create role");
  }
};

/**
 * Handler for updating a role
 */
export const updateRoleHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let roleData: {
      roleId?: string;
      serverId?: string;
      name?: string;
      permissions?: string[];
      color?: string;
      userId?: string; // Added userId for permission checking
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      roleData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      roleData = {
        roleId: formData.get("roleId") as string | undefined,
        serverId: formData.get("serverId") as string | undefined,
        name: formData.get("name") as string | undefined,
        permissions: formData.has("permissions")
          ? (formData.get("permissions") as string).split(",")
          : undefined,
        color: formData.get("color") as string | undefined,
        userId: formData.get("userId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedContentType();
    }

    // Validate input using Zod
    const validatedData = updateRoleSchema.parse(roleData);

    // Check if the user has permission to manage roles
    if (roleData.userId) {
      const hasPermission = await hasServerPermission(
        db,
        roleData.userId,
        validatedData.serverId,
        MANAGE_ROLES,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to manage roles",
        );
      }
    }

    // Create updates object with only the fields that are provided
    const updates: {
      name?: string;
      permissions?: bigint;
      color?: string;
    } = {};

    if (validatedData.name !== undefined) updates.name = validatedData.name;
    if (validatedData.permissions !== undefined) {
      updates.permissions = permissionNamesToBitmask(validatedData.permissions);
    }
    if (validatedData.color !== undefined) updates.color = validatedData.color;

    // Update the role
    const role = await updateRole(db, validatedData.roleId, updates);

    if (!role) {
      return ResponseUtils.notFound("Role not found");
    }

    // Broadcast the role update via WebSocket
    const roleEvent = {
      type: EventTypes.ROLE_UPDATED,
      sender: roleData.userId || "system",
      data: {
        role_id: role.id,
        role_name: role.name,
        server_id: role.serverId,
        permissions: permissionBitmaskToNames(role.permissions),
      },
    };

    wsManager.broadcast(JSON.stringify(roleEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success({
      success: true,
      role: {
        ...role,
        permissions: permissionBitmaskToNames(role.permissions),
      },
    });
  } catch (error) {
    console.error("Error updating role:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to update role");
  }
};

/**
 * Handler for deleting a role
 */
export const deleteRoleHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let roleData: {
      roleId?: string;
      serverId?: string;
      userId?: string; // Added userId for permission checking
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      roleData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      roleData = {
        roleId: formData.get("roleId") as string | undefined,
        serverId: formData.get("serverId") as string | undefined,
        userId: formData.get("userId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedContentType();
    }

    // Validate input using Zod
    const validatedData = deleteRoleSchema.parse(roleData);

    // Check if the user has permission to manage roles
    if (roleData.userId) {
      const hasPermission = await hasServerPermission(
        db,
        roleData.userId,
        validatedData.serverId,
        MANAGE_ROLES,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to manage roles",
        );
      }
    }

    // Delete the role
    const success = await deleteRole(db, validatedData.roleId);

    if (!success) {
      return ResponseUtils.notFound("Role not found or could not be deleted");
    }

    // Broadcast the role deletion via WebSocket
    const roleEvent = {
      type: EventTypes.ROLE_DELETED,
      sender: roleData.userId || "system",
      data: {
        role_id: validatedData.roleId,
        server_id: validatedData.serverId,
      },
    };

    wsManager.broadcast(JSON.stringify(roleEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success({
      success: true,
      roleId: validatedData.roleId,
    });
  } catch (error) {
    console.error("Error deleting role:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to delete role");
  }
};

/**
 * Handler for getting server roles
 */
export const getServerRolesHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const serverId = url.searchParams.get("serverId");
    const userId = url.searchParams.get("userId"); // Added userId for permission checking

    if (!serverId) {
      return ResponseUtils.badRequest("Server ID is required");
    }

    // Validate input using Zod
    const validatedData = getServerRolesSchema.parse({ serverId });

    // Check if the user has permission to view roles
    if (userId) {
      const hasPermission = await hasServerPermission(
        db,
        userId,
        validatedData.serverId,
        ADMINISTRATOR, // Only administrators can view all roles
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to view all roles",
        );
      }
    }

    // Get the server roles
    const roles = await getServerRoles(db, validatedData.serverId);

    // Convert permission bitmasks to names
    const rolesWithPermissionNames = roles.map((role) => ({
      ...role,
      permissions: permissionBitmaskToNames(role.permissions),
    }));

    // Return the roles
    return ResponseUtils.success({
      success: true,
      roles: rolesWithPermissionNames,
    });
  } catch (error) {
    console.error("Error getting server roles:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to get server roles");
  }
};

/**
 * Handler for getting user roles
 */
export const getUserRolesHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const userId = url.searchParams.get("userId");
    const serverId = url.searchParams.get("serverId");
    const requesterId = url.searchParams.get("requesterId"); // Added requesterId for permission checking

    if (!userId || !serverId) {
      return ResponseUtils.badRequest("User ID and Server ID are required");
    }

    // Validate input using Zod
    const validatedData = getUserRolesSchema.parse({ userId, serverId });

    // Check if the requester has permission to view user roles
    // Users can view their own roles, or administrators can view any user's roles
    if (requesterId && requesterId !== userId) {
      const hasPermission = await hasServerPermission(
        db,
        requesterId,
        validatedData.serverId,
        ADMINISTRATOR,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to view this user's roles",
        );
      }
    }

    // Get the user roles
    const roles = await getUserRoles(
      db,
      validatedData.userId,
      validatedData.serverId,
    );

    // Convert permission bitmasks to names
    const rolesWithPermissionNames = roles.map((role) => ({
      ...role,
      permissions: permissionBitmaskToNames(role.permissions),
    }));

    // Return the roles
    return ResponseUtils.success({
      success: true,
      roles: rolesWithPermissionNames,
    });
  } catch (error) {
    console.error("Error getting user roles:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to get user roles");
  }
};

/**
 * Handler for assigning a role to a user
 */
export const assignRoleHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let roleData: {
      userId?: string;
      roleId?: string;
      serverId?: string;
      assignerId?: string; // Added assignerId for permission checking
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      roleData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      roleData = {
        userId: formData.get("userId") as string | undefined,
        roleId: formData.get("roleId") as string | undefined,
        serverId: formData.get("serverId") as string | undefined,
        assignerId: formData.get("assignerId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedContentType();
    }

    // Validate input using Zod
    const validatedData = assignRoleSchema.parse(roleData);

    // Check if the assigner has permission to manage roles
    if (roleData.assignerId) {
      const hasPermission = await hasServerPermission(
        db,
        roleData.assignerId,
        validatedData.serverId,
        MANAGE_ROLES,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to assign roles",
        );
      }
    }

    // Assign the role
    const success = await assignRoleToUser(
      validatedData.userId,
      validatedData.roleId,
      validatedData.serverId,
    );

    if (!success) {
      return ResponseUtils.internalError("Failed to assign role");
    }

    // Broadcast the role assignment via WebSocket
    const roleEvent = {
      type: EventTypes.ROLE_ASSIGNED,
      sender: roleData.assignerId || "system",
      data: {
        user_id: validatedData.userId,
        role_id: validatedData.roleId,
        server_id: validatedData.serverId,
      },
    };

    wsManager.broadcast(JSON.stringify(roleEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success({
      success: true,
      userId: validatedData.userId,
      roleId: validatedData.roleId,
    });
  } catch (error) {
    console.error("Error assigning role:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to assign role");
  }
};

/**
 * Handler for removing a role from a user
 */
export const removeRoleHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let roleData: {
      userId?: string;
      roleId?: string;
      serverId?: string;
      removerId?: string; // Added removerId for permission checking
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      roleData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      roleData = {
        userId: formData.get("userId") as string | undefined,
        roleId: formData.get("roleId") as string | undefined,
        serverId: formData.get("serverId") as string | undefined,
        removerId: formData.get("removerId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedContentType();
    }

    // Validate input using Zod
    const validatedData = removeRoleSchema.parse(roleData);

    // Check if the remover has permission to manage roles
    if (roleData.removerId) {
      const hasPermission = await hasServerPermission(
        db,
        roleData.removerId,
        validatedData.serverId,
        MANAGE_ROLES,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to remove roles",
        );
      }
    }

    // Remove the role
    const success = await removeRoleFromUser(
      validatedData.userId,
      validatedData.roleId,
      validatedData.serverId,
    );

    if (!success) {
      return ResponseUtils.internalError("Failed to remove role");
    }

    // Broadcast the role removal via WebSocket
    const roleEvent = {
      type: EventTypes.ROLE_REMOVED,
      sender: roleData.removerId || "system",
      data: {
        user_id: validatedData.userId,
        role_id: validatedData.roleId,
        server_id: validatedData.serverId,
      },
    };

    wsManager.broadcast(JSON.stringify(roleEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success({
      success: true,
      userId: validatedData.userId,
      roleId: validatedData.roleId,
    });
  } catch (error) {
    console.error("Error removing role:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to remove role");
  }
};
