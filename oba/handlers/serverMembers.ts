import { z } from "zod";
import { db } from "../db";
import {
  kickMember,
  banMember,
  unbanMember,
  changeUserRoles,
  getBannedUsers,
  getServerMembers,
  isUserBanned,
} from "../utils/serverMembers";
import { WebSocketManager } from "../manager/websocket.manager";
import { EventTypes } from "@kurultai/oba-types";
import { ResponseUtils } from "../utils/response-utils";

// Get WebSocketManager singleton instance
const wsManager = WebSocketManager.getInstance();

// Zod schema for kicking a member
const kickMemberSchema = z.object({
  serverId: z.string().uuid(),
  userId: z.string().uuid(),
  kickedById: z.string().uuid(),
});

// Zod schema for banning a member
const banMemberSchema = z.object({
  serverId: z.string().uuid(),
  userId: z.string().uuid(),
  bannedById: z.string().uuid(),
  reason: z.string().optional(),
});

// Zod schema for unbanning a member
const unbanMemberSchema = z.object({
  serverId: z.string().uuid(),
  userId: z.string().uuid(),
  unbannedById: z.string().uuid(),
});

// Zod schema for changing a member's roles
const changeUserRolesSchema = z.object({
  serverId: z.string().uuid(),
  userId: z.string().uuid(),
  roleIds: z.array(z.string().uuid()),
  changedById: z.string().uuid(),
});

// Zod schema for getting banned users
const getBannedUsersSchema = z.object({
  serverId: z.string().uuid(),
  requesterId: z.string().uuid(),
});

// Zod schema for getting server members
const getServerMembersSchema = z.object({
  serverId: z.string().uuid(),
});

/**
 * Handler for kicking a member from a server
 */
export const kickMemberHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let memberData: {
      serverId?: string;
      userId?: string;
      kickedById?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      memberData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      memberData = {
        serverId: formData.get("serverId") as string | undefined,
        userId: formData.get("userId") as string | undefined,
        kickedById: formData.get("kickedById") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = kickMemberSchema.parse(memberData);

    // Kick the member
    const success = await kickMember(
      db,
      validatedData.serverId,
      validatedData.userId,
      validatedData.kickedById,
    );

    if (!success) {
      return ResponseUtils.forbidden(
        "Failed to kick member. You may not have permission or the user is the server owner.",
      );
    }

    // Broadcast the member kick via WebSocket
    const kickEvent = {
      type: EventTypes.MEMBER_KICKED,
      sender: validatedData.kickedById,
      data: {
        server_id: validatedData.serverId,
        user_id: validatedData.userId,
        kicked_by: validatedData.kickedById,
      },
    };

    wsManager.broadcast(JSON.stringify(kickEvent), validatedData.serverId);

    // Get updated server members and broadcast the update
    const members = await getServerMembers(db, validatedData.serverId);

    // Use the handleServerMembersUpdate method to broadcast to all server members
    wsManager.handleServerMembersUpdate(validatedData.kickedById, {
      serverId: validatedData.serverId,
      members: members,
    });

    // Also send a direct notification to the kicked user
    // wsManager.broadcastToUser(validatedData.userId, JSON.stringify({
    //   type: 'YOU_WERE_KICKED',
    //   data: {
    //     server_id: validatedData.serverId
    //   }
    // }));

    // Return success response
    return ResponseUtils.success({
      userId: validatedData.userId,
      serverId: validatedData.serverId,
    });
  } catch (error) {
    console.error("Error kicking member:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to kick member");
  }
};

/**
 * Handler for banning a member from a server
 */
export const banMemberHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let memberData: {
      serverId?: string;
      userId?: string;
      bannedById?: string;
      reason?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      memberData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      memberData = {
        serverId: formData.get("serverId") as string | undefined,
        userId: formData.get("userId") as string | undefined,
        bannedById: formData.get("bannedById") as string | undefined,
        reason: formData.get("reason") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = banMemberSchema.parse(memberData);

    // Ban the member
    const success = await banMember(
      db,
      validatedData.serverId,
      validatedData.userId,
      validatedData.bannedById,
      validatedData.reason,
    );

    if (!success) {
      return ResponseUtils.forbidden(
        "Failed to ban member. You may not have permission or the user is the server owner.",
      );
    }

    // Broadcast the member ban via WebSocket
    const banEvent = {
      type: EventTypes.MEMBER_BANNED,
      sender: validatedData.bannedById,
      data: {
        server_id: validatedData.serverId,
        user_id: validatedData.userId,
        banned_by: validatedData.bannedById,
        reason: validatedData.reason || "No reason provided",
      },
    };

    wsManager.broadcast(JSON.stringify(banEvent), validatedData.serverId);

    // Get updated server members and broadcast the update
    const members = await getServerMembers(db, validatedData.serverId);

    // Use the handleServerMembersUpdate method to broadcast to all server members
    wsManager.handleServerMembersUpdate(validatedData.bannedById, {
      serverId: validatedData.serverId,
      members: members,
    });

    // Also send a direct notification to the banned user
    // wsManager.broadcastToUser(validatedData.userId, JSON.stringify({
    //   type: 'YOU_WERE_BANNED',
    //   data: {
    //     server_id: validatedData.serverId,
    //     reason: validatedData.reason || 'No reason provided'
    //   }
    // }));

    // Return success response
    return ResponseUtils.success({
      userId: validatedData.userId,
      serverId: validatedData.serverId,
      reason: validatedData.reason || "No reason provided",
    });
  } catch (error) {
    console.error("Error banning member:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to ban member");
  }
};

/**
 * Handler for unbanning a member from a server
 */
export const unbanMemberHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let memberData: {
      serverId?: string;
      userId?: string;
      unbannedById?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      memberData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      memberData = {
        serverId: formData.get("serverId") as string | undefined,
        userId: formData.get("userId") as string | undefined,
        unbannedById: formData.get("unbannedById") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = unbanMemberSchema.parse(memberData);

    // Unban the member
    const success = await unbanMember(
      db,
      validatedData.serverId,
      validatedData.userId,
      validatedData.unbannedById,
    );

    if (!success) {
      return ResponseUtils.forbidden(
        "Failed to unban member. You may not have permission.",
      );
    }

    // Broadcast the member unban via WebSocket
    const unbanEvent = {
      type: EventTypes.MEMBER_UNBANNED,
      sender: validatedData.unbannedById,
      data: {
        server_id: validatedData.serverId,
        user_id: validatedData.userId,
        unbanned_by: validatedData.unbannedById,
      },
    };

    wsManager.broadcast(JSON.stringify(unbanEvent), validatedData.serverId);

    // Note: We don't need to broadcast SERVER_MEMBERS_UPDATED here since
    // unbanning doesn't immediately add the user back to the server

    // Return success response
    return ResponseUtils.success({
      userId: validatedData.userId,
      serverId: validatedData.serverId,
    });
  } catch (error) {
    console.error("Error unbanning member:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to unban member");
  }
};

/**
 * Handler for changing a member's roles
 */
export const changeUserRolesHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let memberData: {
      serverId?: string;
      userId?: string;
      roleIds?: string[];
      changedById?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      memberData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      memberData = {
        serverId: formData.get("serverId") as string | undefined,
        userId: formData.get("userId") as string | undefined,
        roleIds: formData.has("roleIds")
          ? (formData.get("roleIds") as string).split(",")
          : undefined,
        changedById: formData.get("changedById") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = changeUserRolesSchema.parse(memberData);

    // Change the member's roles
    const success = await changeUserRoles(
      db,
      validatedData.serverId,
      validatedData.userId,
      validatedData.roleIds,
      validatedData.changedById,
    );

    if (!success) {
      return ResponseUtils.forbidden(
        "Failed to change member roles. You may not have permission or the user is the server owner.",
      );
    }

    // Broadcast the role change via WebSocket
    const roleChangeEvent = {
      type: EventTypes.MEMBER_ROLE_UPDATED,
      sender: validatedData.changedById,
      data: {
        server_id: validatedData.serverId,
        user_id: validatedData.userId,
        role_ids: validatedData.roleIds,
        changed_by: validatedData.changedById,
      },
    };

    wsManager.broadcast(
      JSON.stringify(roleChangeEvent),
      validatedData.serverId,
    );

    // Get updated server members and broadcast the update
    const members = await getServerMembers(db, validatedData.serverId);

    // Use the handleServerMembersUpdate method to broadcast to all server members
    wsManager.handleServerMembersUpdate(validatedData.changedById, {
      serverId: validatedData.serverId,
      members: members,
    });

    // Also send a direct notification to the user whose roles were changed
    wsManager.broadcastToUser(
      validatedData.userId,
      JSON.stringify({
        type: "YOUR_ROLES_CHANGED",
        data: {
          server_id: validatedData.serverId,
          role_ids: validatedData.roleIds,
        },
      }),
    );

    // Return success response
    return ResponseUtils.success({
      userId: validatedData.userId,
      serverId: validatedData.serverId,
      roleIds: validatedData.roleIds,
    });
  } catch (error) {
    console.error("Error changing member roles:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to change member roles");
  }
};

/**
 * Handler for getting banned users
 */
export const getBannedUsersHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const serverId = url.searchParams.get("serverId");
    const requesterId = url.searchParams.get("requesterId");

    if (!serverId || !requesterId) {
      return ResponseUtils.badRequest(
        "Server ID and Requester ID are required",
      );
    }

    // Validate input using Zod
    const validatedData = getBannedUsersSchema.parse({ serverId, requesterId });

    // Get banned users
    const bannedUsers = await getBannedUsers(
      db,
      validatedData.serverId,
      validatedData.requesterId,
    );

    if (bannedUsers === null) {
      return ResponseUtils.forbidden(
        "You do not have permission to view banned users",
      );
    }

    // Return the banned users
    return ResponseUtils.success({ bannedUsers });
  } catch (error) {
    console.error("Error getting banned users:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to get banned users");
  }
};

/**
 * Handler for getting server members
 */
export const getServerMembersHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const serverId = url.searchParams.get("serverId");
    const requesterId = url.searchParams.get("requesterId"); // Optional: who requested the members list

    if (!serverId) {
      return ResponseUtils.badRequest("Server ID is required");
    }

    // Validate input using Zod
    const validatedData = getServerMembersSchema.parse({ serverId });

    // Get server members with roles
    const members = await getServerMembers(db, validatedData.serverId);

    // Broadcast the updated member list via WebSocket if a requester is specified
    if (requesterId) {
      // Use the handleServerMembersUpdate method to broadcast to all server members
      wsManager.handleServerMembersUpdate(requesterId, {
        serverId: validatedData.serverId,
        members: members,
      });
    }

    // Return the server members
    return ResponseUtils.success({ members });
  } catch (error) {
    console.error("Error getting server members:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to get server members");
  }
};
