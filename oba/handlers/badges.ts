import { z } from "zod";
import { db } from "../db";
import { BadgeService } from "../services/badge.service";
import { ResponseUtils } from "../utils/response-utils";
import {
  BadgeNotFoundError,
  BadgeAlreadyAssignedError,
  InsufficientPermissionsError,
  BadgeValidationError
} from "../class/badge-errors";
import type {
  CreateBadgeTypeRequest,
  UpdateBadgeTypeRequest,
  BadgeTypeFilters,
  AssignBadgeRequest
} from "../types/badge.types";

// Initialize badge service
const badgeService = new BadgeService(db);

// Validation schemas
const createBadgeTypeSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().min(1).max(500),
  iconUrl: z.string().url().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).default("#000000"),
  category: z.enum(["achievement", "role", "special", "community", "milestone"]),
  assignmentType: z.enum(["automatic", "manual"]),
  criteria: z.object({
    type: z.enum(["message_count", "server_count", "friend_count", "days_active", "custom"]),
    threshold: z.number().positive().optional(),
    conditions: z.record(z.any()).optional()
  }).optional()
});

const updateBadgeTypeSchema = createBadgeTypeSchema.partial().extend({
  isActive: z.boolean().optional()
});

const badgeFiltersSchema = z.object({
  category: z.enum(["achievement", "role", "special", "community", "milestone"]).optional(),
  assignmentType: z.enum(["automatic", "manual"]).optional(),
  isActive: z.boolean().optional(),
  search: z.string().optional()
});

const assignBadgeSchema = z.object({
  badgeTypeId: z.string().uuid(),
  userId: z.string().uuid()
});

// Helper function to extract user ID from request
function getUserId(req: Request): string {
  const user = (req as any).user;
  if (!user?.userId) {
    throw new Error("User not authenticated");
  }
  return user.userId;
}

/**
 * GET /api/badges/types - List all badge types
 */
export const getBadgeTypesHandler = async (req: Request): Promise<Response> => {
  try {
    // Only allow GET requests
    if (req.method !== "GET") {
      return ResponseUtils.methodNotAllowed();
    }

    const url = new URL(req.url);
    const limit = url.searchParams.get("limit") ? parseInt(url.searchParams.get("limit")!) : undefined;
    const offset = url.searchParams.get("offset") ? parseInt(url.searchParams.get("offset")!) : undefined;
    
    // Parse filters from query parameters
    const filters: BadgeTypeFilters = {};
    if (url.searchParams.get("category")) {
      filters.category = url.searchParams.get("category") as any;
    }
    if (url.searchParams.get("assignmentType")) {
      filters.assignmentType = url.searchParams.get("assignmentType") as any;
    }
    if (url.searchParams.get("isActive")) {
      filters.isActive = url.searchParams.get("isActive") === "true";
    }
    if (url.searchParams.get("search")) {
      filters.search = url.searchParams.get("search")!;
    }

    // Validate filters
    const validatedFilters = Object.keys(filters).length > 0 
      ? badgeFiltersSchema.parse(filters) 
      : undefined;

    const badgeTypes = await badgeService.getBadgeTypes(validatedFilters, limit, offset);

    return ResponseUtils.success(badgeTypes, {
      message: "Badge types retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting badge types:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map(err => ({
          field: err.path.join("."),
          message: err.message
        }))
      );
    }

    return ResponseUtils.internalError("Failed to retrieve badge types");
  }
};

/**
 * POST /api/badges/types/create - Create a new badge type (admin only)
 */
export const createBadgeTypeHandler = async (req: Request): Promise<Response> => {
  try {
    // Only allow POST requests
    if (req.method !== "POST") {
      return ResponseUtils.methodNotAllowed();
    }

    const userId = getUserId(req);
    const body = await req.json();
    
    // Validate input
    const validatedData = createBadgeTypeSchema.parse(body);

    const badgeType = await badgeService.createBadgeType(validatedData, userId);

    return ResponseUtils.created(badgeType, {
      message: "Badge type created successfully"
    });
  } catch (error) {
    console.error("Error creating badge type:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map(err => ({
          field: err.path.join("."),
          message: err.message
        }))
      );
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions to create badge types");
    }

    return ResponseUtils.internalError("Failed to create badge type");
  }
};

/**
 * PUT /api/badges/types/:id/update - Update a badge type (admin only)
 */
export const updateBadgeTypeHandler = async (req: Request, params?: Record<string, string>): Promise<Response> => {
  try {
    // Only allow PUT requests
    if (req.method !== "PUT") {
      return ResponseUtils.methodNotAllowed();
    }

    const userId = getUserId(req);
    const badgeTypeId = params?.id;
    
    if (!badgeTypeId) {
      return ResponseUtils.badRequest("Badge type ID is required");
    }

    const body = await req.json();
    
    // Validate input
    const validatedData = updateBadgeTypeSchema.parse(body);

    const badgeType = await badgeService.updateBadgeType(badgeTypeId, validatedData, userId);

    return ResponseUtils.success(badgeType, {
      message: "Badge type updated successfully"
    });
  } catch (error) {
    console.error("Error updating badge type:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map(err => ({
          field: err.path.join("."),
          message: err.message
        }))
      );
    }

    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound("Badge type not found");
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions to update badge types");
    }

    return ResponseUtils.internalError("Failed to update badge type");
  }
};

/**
 * DELETE /api/badges/types/:id/delete - Delete a badge type (admin only)
 */
export const deleteBadgeTypeHandler = async (req: Request, params?: Record<string, string>): Promise<Response> => {
  try {
    // Only allow DELETE requests
    if (req.method !== "DELETE") {
      return ResponseUtils.methodNotAllowed();
    }

    const userId = getUserId(req);
    const badgeTypeId = params?.id;
    
    if (!badgeTypeId) {
      return ResponseUtils.badRequest("Badge type ID is required");
    }

    await badgeService.deleteBadgeType(badgeTypeId, userId);

    return ResponseUtils.success(null, {
      message: "Badge type deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting badge type:", error);

    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound("Badge type not found");
    }

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions to delete badge types");
    }

    return ResponseUtils.internalError("Failed to delete badge type");
  }
};

/**
 * GET /api/users/:userId/badges - Get user's badges
 */
export const getUserBadgesHandler = async (req: Request, params?: Record<string, string>): Promise<Response> => {
  try {
    // Only allow GET requests
    if (req.method !== "GET") {
      return ResponseUtils.methodNotAllowed();
    }

    const targetUserId = params?.userId;
    
    if (!targetUserId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    const url = new URL(req.url);
    const visibleOnly = url.searchParams.get("visibleOnly") === "true";

    const userBadges = await badgeService.getUserBadges(targetUserId, visibleOnly);

    return ResponseUtils.success(userBadges, {
      message: "User badges retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting user badges:", error);

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to retrieve user badges");
  }
};

/**
 * POST /api/users/:userId/badges/assign - Assign badge to user (admin/moderator only)
 */
export const assignBadgeToUserHandler = async (req: Request, params?: Record<string, string>): Promise<Response> => {
  try {
    // Only allow POST requests
    if (req.method !== "POST") {
      return ResponseUtils.methodNotAllowed();
    }

    const assignedBy = getUserId(req);
    const targetUserId = params?.userId;
    
    if (!targetUserId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    const body = await req.json();
    
    // Validate input
    const validatedData = assignBadgeSchema.parse({
      ...body,
      userId: targetUserId
    });

    const url = new URL(req.url);
    const serverId = url.searchParams.get("serverId") || undefined;

    const userBadge = await badgeService.assignBadge(
      validatedData.userId,
      validatedData.badgeTypeId,
      assignedBy,
      serverId
    );

    return ResponseUtils.created(userBadge, {
      message: "Badge assigned successfully"
    });
  } catch (error) {
    console.error("Error assigning badge:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map(err => ({
          field: err.path.join("."),
          message: err.message
        }))
      );
    }

    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound("Badge type not found");
    }

    if (error instanceof BadgeAlreadyAssignedError) {
      return ResponseUtils.conflict("User already has this badge");
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions to assign badges");
    }

    return ResponseUtils.internalError("Failed to assign badge");
  }
};

/**
 * DELETE /api/users/:userId/badges/:badgeId/remove - Remove badge from user (admin/moderator only)
 */
export const removeBadgeFromUserHandler = async (req: Request, params?: Record<string, string>): Promise<Response> => {
  try {
    // Only allow DELETE requests
    if (req.method !== "DELETE") {
      return ResponseUtils.methodNotAllowed();
    }

    const removedBy = getUserId(req);
    const targetUserId = params?.userId;
    const badgeTypeId = params?.badgeId;
    
    if (!targetUserId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    if (!badgeTypeId) {
      return ResponseUtils.badRequest("Badge ID is required");
    }

    const url = new URL(req.url);
    const serverId = url.searchParams.get("serverId") || undefined;

    await badgeService.removeBadge(targetUserId, badgeTypeId, removedBy, serverId);

    return ResponseUtils.success(null, {
      message: "Badge removed successfully"
    });
  } catch (error) {
    console.error("Error removing badge:", error);

    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound("Badge type not found");
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden("Insufficient permissions to remove badges");
    }

    return ResponseUtils.internalError("Failed to remove badge");
  }
};

/**
 * GET /api/badges/available - Get available badges for current user
 */
export const getAvailableBadgesHandler = async (req: Request): Promise<Response> => {
  try {
    // Only allow GET requests
    if (req.method !== "GET") {
      return ResponseUtils.methodNotAllowed();
    }

    const userId = getUserId(req);

    const availableBadges = await badgeService.getAvailableBadgesForUser(userId);

    return ResponseUtils.success(availableBadges, {
      message: "Available badges retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting available badges:", error);

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to retrieve available badges");
  }
};

/**
 * POST /api/badges/evaluate/:userId - Trigger badge evaluation for user
 */
export const evaluateUserBadgesHandler = async (req: Request, params?: Record<string, string>): Promise<Response> => {
  try {
    // Only allow POST requests
    if (req.method !== "POST") {
      return ResponseUtils.methodNotAllowed();
    }

    const targetUserId = params?.userId;
    
    if (!targetUserId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    const evaluationResult = await badgeService.evaluateUserBadges(targetUserId);

    return ResponseUtils.success(evaluationResult, {
      message: "Badge evaluation completed successfully"
    });
  } catch (error) {
    console.error("Error evaluating user badges:", error);

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to evaluate user badges");
  }
};

/**
 * GET /api/badges/stats - Get badge statistics
 */
export const getBadgeStatsHandler = async (req: Request): Promise<Response> => {
  try {
    // Only allow GET requests
    if (req.method !== "GET") {
      return ResponseUtils.methodNotAllowed();
    }

    const stats = await badgeService.getBadgeStats();

    return ResponseUtils.success(stats, {
      message: "Badge statistics retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting badge stats:", error);

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to retrieve badge statistics");
  }
};

/**
 * GET /api/badges/leaderboard - Get badge leaderboard
 */
export const getBadgeLeaderboardHandler = async (req: Request): Promise<Response> => {
  try {
    // Only allow GET requests
    if (req.method !== "GET") {
      return ResponseUtils.methodNotAllowed();
    }

    const url = new URL(req.url);
    const limit = url.searchParams.get("limit") ? parseInt(url.searchParams.get("limit")!) : 10;

    const leaderboard = await badgeService.getBadgeLeaderboard(limit);

    return ResponseUtils.success(leaderboard, {
      message: "Badge leaderboard retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting badge leaderboard:", error);

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to retrieve badge leaderboard");
  }
};