import { z } from "zod";
import { db } from "../db";
import { FriendshipSchema } from "../db/schema";
import { eq } from "drizzle-orm";
import {
  sendFriendRequest,
  acceptFriendRequest,
  rejectFriendRequest,
  cancelFriendRequest,
  removeFriend,
  blockUser,
  unblockUser,
  getUserFriends,
  getPendingFriendRequests,
  getSentFriendRequests,
  getBlockedUsers,
  isUserBlocked,
} from "../db/utils";
import { EventTypes } from "@kurultai/oba-types";
import { WebSocketManager } from "../manager/websocket.manager";
import { ResponseUtils } from "../utils/response-utils";

// Get WebSocketManager singleton instance
const wsManager = WebSocketManager.getInstance();

// Zod schema for friend request
const friendRequestSchema = z.object({
  userId: z.string().uuid(),
  friendId: z.string().uuid(),
});

// Zod schema for friendship actions (accept, reject, cancel, remove)
const friendshipActionSchema = z.object({
  userId: z.string().uuid(),
  friendshipId: z.string().uuid(),
});

// Zod schema for blocking a user
const blockUserSchema = z.object({
  userId: z.string().uuid(),
  targetUserId: z.string().uuid(),
});

/**
 * Handler for sending a friend request
 */
export const sendFriendRequestHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let requestData: {
      userId?: string;
      friendId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      requestData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      requestData = {
        userId: formData.get("userId") as string | undefined,
        friendId: formData.get("friendId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = friendRequestSchema.parse(requestData);

    try {
      // Send the friend request
      const friendship = await sendFriendRequest(
        db,
        validatedData.userId,
        validatedData.friendId,
      );

      // If the friendship status is ACCEPTED, it means the request was auto-accepted
      // because the other user had already sent a request
      const isAutoAccepted = friendship.status === "ACCEPTED";

      // Broadcast the friend request via WebSocket
      const eventType = isAutoAccepted
        ? EventTypes.FRIEND_ADDED
        : EventTypes.FRIEND_REQUEST_SENT;

      const friendEvent = {
        type: eventType,
        sender: validatedData.userId,
        data: {
          friendship,
        },
      };

      // Send to both users
      wsManager.broadcastToUser(
        JSON.stringify(friendEvent),
        validatedData.userId,
      );
      wsManager.broadcastToUser(
        JSON.stringify(friendEvent),
        validatedData.friendId,
      );

      // Return success response
      return ResponseUtils.success(
        { friendship, isAutoAccepted },
        { message: "Friend request sent successfully" },
      );
    } catch (error) {
      // Handle specific errors
      if (error instanceof Error) {
        if (error.message === "Cannot send friend request to yourself") {
          return ResponseUtils.badRequest(
            "Cannot send friend request to yourself",
          );
        }
        if (error.message === "User not found") {
          return ResponseUtils.notFound("User");
        }
        if (error.message === "Already friends") {
          return ResponseUtils.badRequest("Already friends with this user");
        }
        if (error.message === "Friend request already sent") {
          return ResponseUtils.badRequest("Friend request already sent");
        }
        if (error.message === "Cannot send friend request") {
          return ResponseUtils.badRequest(
            "Cannot send friend request to this user",
          );
        }
      }
      throw error;
    }
  } catch (error) {
    console.error("Error sending friend request:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to send friend request");
  }
};

/**
 * Handler for accepting a friend request
 */
export const acceptFriendRequestHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let requestData: {
      userId?: string;
      friendshipId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      requestData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      requestData = {
        userId: formData.get("userId") as string | undefined,
        friendshipId: formData.get("friendshipId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = friendshipActionSchema.parse(requestData);

    try {
      // Accept the friend request
      const friendship = await acceptFriendRequest(
        db,
        validatedData.userId,
        validatedData.friendshipId,
      );

      // Broadcast the friend acceptance via WebSocket
      const friendEvent = {
        type: EventTypes.FRIEND_REQUEST_ACCEPTED,
        sender: validatedData.userId,
        data: {
          friendship,
        },
      };

      // Send to both users
      wsManager.broadcastToUser(
        JSON.stringify(friendEvent),
        validatedData.userId,
      );
      wsManager.broadcastToUser(JSON.stringify(friendEvent), friendship.userId);

      // Return success response
      return ResponseUtils.success(friendship, {
        message: "Friend request accepted successfully",
      });
    } catch (error) {
      // Handle specific errors
      if (error instanceof Error) {
        if (error.message === "Friend request not found") {
          return ResponseUtils.notFound("Friend request");
        }
      }
      throw error;
    }
  } catch (error) {
    console.error("Error accepting friend request:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to accept friend request");
  }
};

/**
 * Handler for rejecting a friend request
 */
export const rejectFriendRequestHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let requestData: {
      userId?: string;
      friendshipId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      requestData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      requestData = {
        userId: formData.get("userId") as string | undefined,
        friendshipId: formData.get("friendshipId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = friendshipActionSchema.parse(requestData);

    try {
      // Get the friendship before rejecting it (to know who sent it)
      const friendship = await db
        .select()
        .from(FriendshipSchema)
        .where(eq(FriendshipSchema.id, validatedData.friendshipId))
        .limit(1);

      if (friendship.length === 0) {
        return ResponseUtils.notFound("Friend request");
      }

      const requestSenderId = friendship[0].userId;

      // Reject the friend request
      await rejectFriendRequest(
        db,
        validatedData.userId,
        validatedData.friendshipId,
      );

      // Broadcast the friend rejection via WebSocket
      const friendEvent = {
        type: EventTypes.FRIEND_REQUEST_REJECTED,
        sender: validatedData.userId,
        data: {
          friendshipId: validatedData.friendshipId,
        },
      };

      // Send to both users
      wsManager.broadcastToUser(
        JSON.stringify(friendEvent),
        validatedData.userId,
      );
      wsManager.broadcastToUser(JSON.stringify(friendEvent), requestSenderId);

      // Return success response
      return ResponseUtils.success(
        {},
        { message: "Friend request rejected successfully" },
      );
    } catch (error) {
      // Handle specific errors
      if (error instanceof Error) {
        if (error.message === "Friend request not found") {
          return ResponseUtils.notFound("Friend request");
        }
      }
      throw error;
    }
  } catch (error) {
    console.error("Error rejecting friend request:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to reject friend request");
  }
};

/**
 * Handler for canceling a friend request
 */
export const cancelFriendRequestHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let requestData: {
      userId?: string;
      friendshipId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      requestData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      requestData = {
        userId: formData.get("userId") as string | undefined,
        friendshipId: formData.get("friendshipId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = friendshipActionSchema.parse(requestData);

    try {
      // Get the friendship before canceling it (to know who it was sent to)
      const friendship = await db
        .select()
        .from(FriendshipSchema)
        .where(eq(FriendshipSchema.id, validatedData.friendshipId))
        .limit(1);

      if (friendship.length === 0) {
        return ResponseUtils.notFound("Friend request");
      }

      const requestRecipientId = friendship[0].friendId;

      // Cancel the friend request
      await cancelFriendRequest(
        db,
        validatedData.userId,
        validatedData.friendshipId,
      );

      // Broadcast the friend request cancellation via WebSocket
      const friendEvent = {
        type: EventTypes.FRIEND_REQUEST_CANCELED,
        sender: validatedData.userId,
        data: {
          friendshipId: validatedData.friendshipId,
        },
      };

      // Send to both users
      wsManager.broadcastToUser(
        JSON.stringify(friendEvent),
        validatedData.userId,
      );
      wsManager.broadcastToUser(
        JSON.stringify(friendEvent),
        requestRecipientId,
      );

      // Return success response
      return ResponseUtils.success(
        {},
        { message: "Friend request canceled successfully" },
      );
    } catch (error) {
      // Handle specific errors
      if (error instanceof Error) {
        if (error.message === "Friend request not found") {
          return ResponseUtils.notFound("Friend request");
        }
      }
      throw error;
    }
  } catch (error) {
    console.error("Error canceling friend request:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to cancel friend request");
  }
};

/**
 * Handler for removing a friend
 */
export const removeFriendHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let requestData: {
      userId?: string;
      friendshipId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      requestData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      requestData = {
        userId: formData.get("userId") as string | undefined,
        friendshipId: formData.get("friendshipId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = friendshipActionSchema.parse(requestData);

    try {
      // Get the friendship before removing it (to know who the friend is)
      const friendship = await db
        .select()
        .from(FriendshipSchema)
        .where(eq(FriendshipSchema.id, validatedData.friendshipId))
        .limit(1);

      if (friendship.length === 0) {
        return ResponseUtils.notFound("Friendship");
      }

      // Determine the friend's ID (the other user in the friendship)
      const friendId =
        friendship[0].userId === validatedData.userId
          ? friendship[0].friendId
          : friendship[0].userId;

      // Remove the friend
      await removeFriend(db, validatedData.userId, validatedData.friendshipId);

      // Broadcast the friend removal via WebSocket
      const friendEvent = {
        type: EventTypes.FRIEND_REMOVED,
        sender: validatedData.userId,
        data: {
          friendshipId: validatedData.friendshipId,
        },
      };

      // Send to both users
      wsManager.broadcastToUser(
        JSON.stringify(friendEvent),
        validatedData.userId,
      );
      wsManager.broadcastToUser(JSON.stringify(friendEvent), friendId);

      // Return success response
      return ResponseUtils.success(
        {},
        { message: "Friend removed successfully" },
      );
    } catch (error) {
      // Handle specific errors
      if (error instanceof Error) {
        if (error.message === "Friendship not found") {
          return ResponseUtils.notFound("Friendship");
        }
      }
      throw error;
    }
  } catch (error) {
    console.error("Error removing friend:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to remove friend");
  }
};

/**
 * Handler for blocking a user
 */
export const blockUserHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let requestData: {
      userId?: string;
      targetUserId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      requestData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      requestData = {
        userId: formData.get("userId") as string | undefined,
        targetUserId: formData.get("targetUserId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = blockUserSchema.parse(requestData);

    try {
      // Block the user
      const friendship = await blockUser(
        db,
        validatedData.userId,
        validatedData.targetUserId,
      );

      // Broadcast the user blocking via WebSocket
      const blockEvent = {
        type: EventTypes.USER_BLOCKED,
        sender: validatedData.userId,
        data: {
          friendship,
        },
      };

      // Only send to the blocker (not the blocked user)
      wsManager.broadcastToUser(
        JSON.stringify(blockEvent),
        validatedData.userId,
      );

      // Return success response
      return ResponseUtils.success(friendship, {
        message: "User blocked successfully",
      });
    } catch (error) {
      // Handle specific errors
      if (error instanceof Error) {
        if (error.message === "Cannot block yourself") {
          return ResponseUtils.badRequest("Cannot block yourself");
        }
        if (error.message === "User not found") {
          return ResponseUtils.notFound("User");
        }
      }
      throw error;
    }
  } catch (error) {
    console.error("Error blocking user:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to block user");
  }
};

/**
 * Handler for unblocking a user
 */
export const unblockUserHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let requestData: {
      userId?: string;
      friendshipId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      requestData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      requestData = {
        userId: formData.get("userId") as string | undefined,
        friendshipId: formData.get("friendshipId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = friendshipActionSchema.parse(requestData);

    try {
      // Unblock the user
      await unblockUser(db, validatedData.userId, validatedData.friendshipId);

      // Broadcast the user unblocking via WebSocket
      const unblockEvent = {
        type: EventTypes.USER_UNBLOCKED,
        sender: validatedData.userId,
        data: {
          friendshipId: validatedData.friendshipId,
        },
      };

      // Only send to the user who unblocked
      wsManager.broadcastToUser(
        JSON.stringify(unblockEvent),
        validatedData.userId,
      );

      // Return success response
      return ResponseUtils.success(
        {},
        { message: "User unblocked successfully" },
      );
    } catch (error) {
      // Handle specific errors
      if (error instanceof Error) {
        if (error.message === "Blocked relationship not found") {
          return ResponseUtils.notFound("Blocked relationship");
        }
      }
      throw error;
    }
  } catch (error) {
    console.error("Error unblocking user:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to unblock user");
  }
};

/**
 * Handler for getting a user's friends
 */
export const getUserFriendsHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const userId = url.searchParams.get("userId");

    if (!userId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    // Get the user's friends
    const friends = await getUserFriends(db, userId);

    // Return the friends
    return ResponseUtils.success(friends, {
      message: "Friends retrieved successfully",
    });
  } catch (error) {
    console.error("Error retrieving friends:", error);

    return ResponseUtils.internalError("Failed to retrieve friends");
  }
};

/**
 * Handler for getting a user's pending friend requests
 */
export const getPendingFriendRequestsHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const userId = url.searchParams.get("userId");

    if (!userId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    // Get the user's pending friend requests
    const pendingRequests = await getPendingFriendRequests(db, userId);

    // Return the pending requests
    return ResponseUtils.success(pendingRequests, {
      message: "Pending friend requests retrieved successfully",
    });
  } catch (error) {
    console.error("Error retrieving pending friend requests:", error);

    return ResponseUtils.internalError(
      "Failed to retrieve pending friend requests",
    );
  }
};

/**
 * Handler for getting a user's sent friend requests
 */
export const getSentFriendRequestsHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const userId = url.searchParams.get("userId");

    if (!userId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    // Get the user's sent friend requests
    const sentRequests = await getSentFriendRequests(db, userId);

    // Return the sent requests
    return ResponseUtils.success(sentRequests, {
      message: "Sent friend requests retrieved successfully",
    });
  } catch (error) {
    console.error("Error retrieving sent friend requests:", error);

    return ResponseUtils.internalError(
      "Failed to retrieve sent friend requests",
    );
  }
};

/**
 * Handler for getting a user's blocked users
 */
export const getBlockedUsersHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const userId = url.searchParams.get("userId");

    if (!userId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    // Get the user's blocked users
    const blockedUsers = await getBlockedUsers(db, userId);

    // Return the blocked users
    return ResponseUtils.success(blockedUsers, {
      message: "Blocked users retrieved successfully",
    });
  } catch (error) {
    console.error("Error retrieving blocked users:", error);

    return ResponseUtils.internalError("Failed to retrieve blocked users");
  }
};
