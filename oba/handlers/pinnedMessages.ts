import { z } from "zod";
import { EventTypes } from "@kurultai/oba-types";
import { db as database } from "./../db/index";
import { MessageSchema } from "../db/schema";
import { sql } from "drizzle-orm";
import { WebSocketManager } from "../manager/websocket.manager";
import { ResponseUtils } from "../utils/response-utils";

// Get WebSocketManager singleton instance
const wsManager = WebSocketManager.getInstance();

// Validation schema for pin/unpin requests
const pinRequestSchema = z.object({
  messageId: z.string().uuid(),
  channelId: z.string().uuid(),
});

// Validation schema for getting pinned messages
const getPinnedMessagesSchema = z.object({
  channelId: z.string().uuid(),
});

/**
 * <PERSON><PERSON> for pinning a message
 */
export const pinMessageHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let pinData: {
      messageId?: string;
      channelId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      pinData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      pinData = {
        messageId: formData.get("messageId") as string,
        channelId: formData.get("channelId") as string,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = pinRequestSchema.parse(pinData);

    // Update message in database
    const [updatedMessage] = await database
      .update(MessageSchema)
      .set({
        isPinned: true,
        updatedAt: new Date(),
      })
      .where(sql`${MessageSchema.id} = ${validatedData.messageId}`)
      .returning();

    if (!updatedMessage) {
      return ResponseUtils.notFound("Message not found");
    }

    // Broadcast pin event to channel
    wsManager.broadcastToChannel(validatedData.channelId, {
      type: EventTypes.MESSAGE_PINNED,
      data: updatedMessage,
    });

    return ResponseUtils.success(updatedMessage);
  } catch (error) {
    console.error("Error pinning message:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to pin message");
  }
};

/**
 * Handler for unpinning a message
 */
export const unpinMessageHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let unpinData: {
      messageId?: string;
      channelId?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      unpinData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      unpinData = {
        messageId: formData.get("messageId") as string,
        channelId: formData.get("channelId") as string,
      };
    } else {
      return ResponseUtils.unsupportedMediaType("Unsupported content type");
    }

    // Validate input using Zod
    const validatedData = pinRequestSchema.parse(unpinData);

    // Update message in database
    const [updatedMessage] = await database
      .update(MessageSchema)
      .set({
        isPinned: false,
        updatedAt: new Date(),
      })
      .where(sql`${MessageSchema.id} = ${validatedData.messageId}`)
      .returning();

    if (!updatedMessage) {
      return ResponseUtils.notFound("Message not found");
    }

    // Broadcast unpin event to channel
    wsManager.broadcastToChannel(validatedData.channelId, {
      type: EventTypes.MESSAGE_UNPINNED,
      data: updatedMessage,
    });

    return ResponseUtils.success(updatedMessage);
  } catch (error) {
    console.error("Error unpinning message:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to unpin message");
  }
};

/**
 * Handler for getting pinned messages
 */
export const getPinnedMessagesHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const channelId = url.searchParams.get("channelId");

    if (!channelId) {
      return ResponseUtils.badRequest("Channel ID is required");
    }

    // Validate input using Zod
    const validatedData = getPinnedMessagesSchema.parse({ channelId });

    // Get pinned messages from database
    const pinnedMessages = await database
      .select()
      .from(MessageSchema)
      .where(
        sql`${MessageSchema.channelId} = ${validatedData.channelId} AND ${MessageSchema.isPinned} = true`,
      )
      .orderBy(MessageSchema.createdAt);

    return ResponseUtils.success({ messages: pinnedMessages });
  } catch (error) {
    console.error("Error getting pinned messages:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(error.errors);
    }

    return ResponseUtils.internalError("Failed to get pinned messages");
  }
};
