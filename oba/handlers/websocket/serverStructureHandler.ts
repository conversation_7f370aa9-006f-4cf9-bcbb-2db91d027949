import { db } from "../../db";
import type { WebSocketManager } from "../../manager/websocket.manager";
import { getServerStructureForUser } from "../../utils/server-structure";
import { EventTypes } from "@kurultai/oba-types";

/**
 * Handler for sending server structure to a user when they connect to a server
 *
 * @param wsManager - The WebSocket manager instance
 * @param userId - The ID of the user
 * @param serverId - The ID of the server
 */
export async function sendServerStructureHandler(
  wsManager: WebSocketManager,
  userId: string,
  serverId: string,
): Promise<void> {
  try {
    // Get the server structure for the user
    const serverStructure = await getServerStructureForUser(
      db,
      userId,
      serverId,
    );

    // Send the server structure to the user
    wsManager.sendToUser(userId, {
      type: EventTypes.SERVER_STRUCTURE,
      data: {
        serverId,
        structure: serverStructure,
      },
    });
  } catch (error) {
    console.error("Error sending server structure:", error);
  }
}

/**
 * Handler for when a user joins a server
 * This will send the server structure to the user
 *
 * @param wsManager - The WebSocket manager instance
 * @param userId - The ID of the user
 * @param serverId - The ID of the server
 */
export async function userJoinServerHandler(
  wsManager: WebSocketManager,
  userId: string,
  serverId: string,
): Promise<void> {
  try {
    // Send the server structure to the user
    await sendServerStructureHandler(wsManager, userId, serverId);
  } catch (error) {
    console.error("Error handling user join server:", error);
  }
}

/**
 * Handler for when server structure changes
 * This will send the updated server structure to all users in the server
 *
 * @param wsManager - The WebSocket manager instance
 * @param serverId - The ID of the server
 */
export async function serverStructureChangedHandler(
  wsManager: WebSocketManager,
  serverId: string,
): Promise<void> {
  try {
    // Get all connected users in the server
    const connectedUsers = wsManager.getConnectedUsersInServer(serverId);

    // Send the updated server structure to each user
    for (const userId of connectedUsers) {
      await sendServerStructureHandler(wsManager, userId, serverId);
    }
  } catch (error) {
    console.error("Error handling server structure change:", error);
  }
}
