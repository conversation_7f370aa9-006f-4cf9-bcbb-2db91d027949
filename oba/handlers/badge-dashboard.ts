import { z } from "zod";
import { db } from "../db";
import { BadgeService } from "../services/badge.service";
import { ResponseUtils } from "../utils/response-utils";
import {
  BadgeNotFoundError,
  BadgeValidationError
} from "../class/badge-errors";
import type {
  BadgeProgress,
  UserBadgeSummary,
  BadgeTypeFilters
} from "../types/badge.types";

// Initialize badge service
const badgeService = new BadgeService(db);

// Validation schemas
const badgeProgressSchema = z.object({
  badgeTypeId: z.string().uuid().optional()
});

const badgeVisibilitySchema = z.object({
  badgeTypeId: z.string().uuid(),
  isVisible: z.boolean()
});

const badgeHistoryFiltersSchema = z.object({
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
  category: z.enum(["achievement", "role", "special", "community", "milestone"]).optional(),
  unlockType: z.enum(["automatic", "manual", "peer_voted", "manual_invitation"]).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional()
});

// Helper function to extract user ID from request
function getUserId(req: Request): string {
  const user = (req as any).user;
  if (!user?.userId) {
    throw new Error("User not authenticated");
  }
  return user.userId;
}

/**
 * GET /api/badges/progress - Get badge progress for current user
 */
export const getBadgeProgressHandler = async (req: Request): Promise<Response> => {
  try {
    if (req.method !== "GET") {
      return ResponseUtils.methodNotAllowed();
    }

    const userId = getUserId(req);
    const url = new URL(req.url);
    
    // Parse query parameters
    const badgeTypeId = url.searchParams.get("badgeTypeId") || undefined;
    
    if (badgeTypeId) {
      // Validate badge type ID format
      const validation = badgeProgressSchema.safeParse({ badgeTypeId });
      if (!validation.success) {
        return ResponseUtils.validationError(
          validation.error.errors.map(err => ({
            field: err.path.join("."),
            message: err.message
          }))
        );
      }
    }

    const progress = await badgeService.getBadgeProgress(userId, badgeTypeId);

    return ResponseUtils.success(progress, {
      message: "Badge progress retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting badge progress:", error);

    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound("Badge type not found");
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to retrieve badge progress");
  }
};

/**
 * GET /api/badges/dashboard - Get comprehensive badge dashboard for current user
 */
export const getBadgeDashboardHandler = async (req: Request): Promise<Response> => {
  try {
    if (req.method !== "GET") {
      return ResponseUtils.methodNotAllowed();
    }

    const userId = getUserId(req);

    // Get user badges
    const userBadges = await badgeService.getUserBadges(userId);
    
    // Get available badges
    const availableBadges = await badgeService.getAvailableBadgesForUser(userId);
    
    // Get badge progress for automatic badges
    const badgeProgress = await badgeService.getBadgeProgress(userId);
    
    // Get user stats
    const userStats = await badgeService.getUserStats(userId);

    // Calculate summary statistics
    const totalBadges = userBadges.length;
    const visibleBadges = userBadges.filter(badge => badge.isVisible).length;
    const automaticBadges = userBadges.filter(badge => 
      badge.badgeType?.unlockType === 'automatic'
    ).length;
    const manualBadges = userBadges.filter(badge => 
      badge.badgeType?.unlockType === 'manual'
    ).length;

    // Category breakdown
    const categoryBreakdown: Record<string, number> = {};
    userBadges.forEach(badge => {
      if (badge.badgeType?.category) {
        categoryBreakdown[badge.badgeType.category] = 
          (categoryBreakdown[badge.badgeType.category] || 0) + 1;
      }
    });

    const dashboard = {
      user: {
        id: userId,
        stats: userStats
      },
      badges: {
        earned: userBadges,
        available: availableBadges,
        progress: badgeProgress
      },
      summary: {
        totalBadges,
        visibleBadges,
        automaticBadges,
        manualBadges,
        categoryBreakdown,
        availableCount: availableBadges.length,
        progressCount: badgeProgress.filter(p => !p.isEarned && p.progress > 0).length
      }
    };

    return ResponseUtils.success(dashboard, {
      message: "Badge dashboard retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting badge dashboard:", error);

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to retrieve badge dashboard");
  }
};

/**
 * GET /api/badges/history - Get badge earning history for current user
 */
export const getBadgeHistoryHandler = async (req: Request): Promise<Response> => {
  try {
    if (req.method !== "GET") {
      return ResponseUtils.methodNotAllowed();
    }

    const userId = getUserId(req);
    const url = new URL(req.url);
    
    // Parse and validate query parameters
    const queryParams = {
      limit: url.searchParams.get("limit") ? parseInt(url.searchParams.get("limit")!) : 20,
      offset: url.searchParams.get("offset") ? parseInt(url.searchParams.get("offset")!) : 0,
      category: url.searchParams.get("category") || undefined,
      unlockType: url.searchParams.get("unlockType") || undefined,
      startDate: url.searchParams.get("startDate") || undefined,
      endDate: url.searchParams.get("endDate") || undefined
    };

    const validatedParams = badgeHistoryFiltersSchema.parse(queryParams);

    // Get user badges with filtering
    const allUserBadges = await badgeService.getUserBadges(userId);
    
    // Apply filters
    let filteredBadges = allUserBadges;
    
    if (validatedParams.category) {
      filteredBadges = filteredBadges.filter(badge => 
        badge.badgeType?.category === validatedParams.category
      );
    }
    
    if (validatedParams.unlockType) {
      filteredBadges = filteredBadges.filter(badge => 
        badge.badgeType?.unlockType === validatedParams.unlockType
      );
    }
    
    if (validatedParams.startDate) {
      const startDate = new Date(validatedParams.startDate);
      filteredBadges = filteredBadges.filter(badge => 
        badge.assignedAt >= startDate
      );
    }
    
    if (validatedParams.endDate) {
      const endDate = new Date(validatedParams.endDate);
      filteredBadges = filteredBadges.filter(badge => 
        badge.assignedAt <= endDate
      );
    }

    // Apply pagination
    const totalCount = filteredBadges.length;
    const paginatedBadges = filteredBadges
      .slice(validatedParams.offset, validatedParams.offset + validatedParams.limit);

    // Format history entries
    const history = paginatedBadges.map(badge => ({
      id: badge.id,
      badgeTypeId: badge.badgeTypeId,
      badgeName: badge.badgeType?.name || "Unknown Badge",
      badgeTitle: badge.badgeType?.title,
      badgeDescription: badge.badgeType?.description || "",
      badgeIcon: badge.badgeType?.icon || "🏆",
      badgeCategory: badge.badgeType?.category || "general",
      unlockType: badge.badgeType?.unlockType || "manual",
      assignedBy: badge.assignedBy,
      assignedAt: badge.assignedAt,
      isVisible: badge.isVisible,
      criteria: badge.badgeType?.criteria,
      perks: badge.badgeType?.perks || []
    }));

    return ResponseUtils.success({
      history,
      pagination: {
        total: totalCount,
        limit: validatedParams.limit,
        offset: validatedParams.offset,
        hasMore: validatedParams.offset + validatedParams.limit < totalCount
      }
    }, {
      message: "Badge history retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting badge history:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map(err => ({
          field: err.path.join("."),
          message: err.message
        }))
      );
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to retrieve badge history");
  }
};

/**
 * PUT /api/badges/visibility - Update badge visibility settings
 */
export const updateBadgeVisibilityHandler = async (req: Request): Promise<Response> => {
  try {
    if (req.method !== "PUT") {
      return ResponseUtils.methodNotAllowed();
    }

    const userId = getUserId(req);
    const body = await req.json();
    
    // Validate input
    const validatedData = badgeVisibilitySchema.parse(body);

    // Check if user has this badge
    const userBadges = await badgeService.getUserBadges(userId);
    const hasBadge = userBadges.some(badge => badge.badgeTypeId === validatedData.badgeTypeId);
    
    if (!hasBadge) {
      return ResponseUtils.notFound("User does not have this badge");
    }

    // Update visibility using database utility
    const { updateBadgeVisibility } = await import("../db/utils/badge-utils");
    const success = await updateBadgeVisibility(
      db, 
      userId, 
      validatedData.badgeTypeId, 
      validatedData.isVisible
    );

    if (!success) {
      return ResponseUtils.internalError("Failed to update badge visibility");
    }

    return ResponseUtils.success(null, {
      message: "Badge visibility updated successfully"
    });
  } catch (error) {
    console.error("Error updating badge visibility:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map(err => ({
          field: err.path.join("."),
          message: err.message
        }))
      );
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to update badge visibility");
  }
};

/**
 * GET /api/badges/criteria/:badgeTypeId - Get detailed badge criteria with progress
 */
export const getBadgeCriteriaHandler = async (req: Request, params?: Record<string, string>): Promise<Response> => {
  try {
    if (req.method !== "GET") {
      return ResponseUtils.methodNotAllowed();
    }

    const userId = getUserId(req);
    const badgeTypeId = params?.badgeTypeId;
    
    if (!badgeTypeId) {
      return ResponseUtils.badRequest("Badge type ID is required");
    }

    // Get badge type
    const badgeType = await badgeService.getBadgeTypeById(badgeTypeId);
    
    // Get user's progress for this badge
    const progress = await badgeService.getBadgeProgress(userId, badgeTypeId);
    const badgeProgress = progress[0]; // Should only be one result
    
    // Get user stats for context
    const userStats = await badgeService.getUserStats(userId);

    // Check if user already has this badge
    const userBadges = await badgeService.getUserBadges(userId);
    const earnedBadge = userBadges.find(badge => badge.badgeTypeId === badgeTypeId);

    const criteriaDetails = {
      badgeType,
      criteria: badgeType.criteria,
      progress: badgeProgress ? {
        current: badgeProgress.progress,
        total: badgeProgress.total,
        percentage: Math.round((badgeProgress.progress / badgeProgress.total) * 100),
        isEarned: badgeProgress.isEarned
      } : null,
      userStats,
      earnedBadge: earnedBadge ? {
        assignedAt: earnedBadge.assignedAt,
        assignedBy: earnedBadge.assignedBy,
        isVisible: earnedBadge.isVisible
      } : null,
      requirements: {
        description: badgeType.criteria.requirement,
        tracked: badgeType.criteria.tracked,
        threshold: badgeType.criteria.threshold,
        timeframe: badgeType.criteria.timeframe,
        conditions: badgeType.criteria.conditions
      }
    };

    return ResponseUtils.success(criteriaDetails, {
      message: "Badge criteria retrieved successfully"
    });
  } catch (error) {
    console.error("Error getting badge criteria:", error);

    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound("Badge type not found");
    }

    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }

    return ResponseUtils.internalError("Failed to retrieve badge criteria");
  }
};