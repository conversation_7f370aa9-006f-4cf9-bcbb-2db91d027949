import { z } from "zod";
import { db } from "../db";
import { BadgeCollectionService } from "../services/badge-collection.service";
import { ResponseUtils } from "../utils/response-utils";
import {
  BadgeNotFoundError,
  BadgeValidationError,
  InsufficientPermissionsError
} from "../class/badge-errors";
import type {
  CreateBadgeCollectionRequest,
  UpdateBadgeCollectionRequest,
  BadgeCollectionFilters
} from "../types/badge.types";

// Initialize badge collection service
const badgeCollectionService = new BadgeCollectionService(db);

// Validation schemas
const createCollectionSchema = z.object({
  collectionId: z.string().min(1).max(100),
  name: z.string().min(1).max(200),
  description: z.string().min(1).max(1000),
  type: z.enum(["progressive", "standalone"]).default("progressive"),
  unlockedBy: z.string().optional(),
  completionReward: z.object({
    badge: z.string(),
    title: z.string(),
    perks: z.array(z.string()),
    visual: z.string(),
    animation: z.string()
  }).optional()
});

const updateCollectionSchema = createCollectionSchema.partial().extend({
  isActive: z.boolean().optional()
});

const collectionFiltersSchema = z.object({
  type: z.enum(["progressive", "standalone"]).optional(),
  isActive: z.boolean().optional(),
  search: z.string().optional()
});

/**
 * Get all badge collections with optional filtering
 */
export async function getCollectionsHandler(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get("limit") || "50");
    const offset = parseInt(url.searchParams.get("offset") || "0");
    
    // Parse filters
    const filters: BadgeCollectionFilters = {};
    if (url.searchParams.get("type")) {
      filters.type = url.searchParams.get("type") as "progressive" | "standalone";
    }
    if (url.searchParams.get("isActive")) {
      filters.isActive = url.searchParams.get("isActive") === "true";
    }
    if (url.searchParams.get("search")) {
      filters.search = url.searchParams.get("search")!;
    }

    const validatedFilters = collectionFiltersSchema.parse(filters);
    const collections = await badgeCollectionService.getCollections(validatedFilters, limit, offset);

    return ResponseUtils.success({
      collections,
      pagination: {
        limit,
        offset,
        total: collections.length
      }
    });
  } catch (error) {
    console.error("Error getting badge collections:", error);
    if (error instanceof z.ZodError) {
      return ResponseUtils.badRequest("Invalid filter parameters", error.errors);
    }
    return ResponseUtils.internalServerError("Failed to get badge collections");
  }
}

/**
 * Get a specific badge collection by ID
 */
export async function getCollectionHandler(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const pathParts = url.pathname.split("/");
    const collectionId = pathParts[pathParts.length - 1];

    if (!collectionId) {
      return ResponseUtils.badRequest("Collection ID is required");
    }

    const collection = await badgeCollectionService.getCollectionById(collectionId);
    return ResponseUtils.success({ collection });
  } catch (error) {
    console.error("Error getting badge collection:", error);
    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound(error.message);
    }
    return ResponseUtils.internalServerError("Failed to get badge collection");
  }
}

/**
 * Create a new badge collection (admin only)
 */
export async function createCollectionHandler(request: Request): Promise<Response> {
  try {
    const body = await request.json();
    const validatedData = createCollectionSchema.parse(body);
    
    // Get user ID from request context (set by auth middleware)
    const userId = (request as any).userId;
    if (!userId) {
      return ResponseUtils.unauthorized("Authentication required");
    }

    const collection = await badgeCollectionService.createCollection(
      validatedData as CreateBadgeCollectionRequest,
      userId
    );

    return ResponseUtils.created({ collection });
  } catch (error) {
    console.error("Error creating badge collection:", error);
    if (error instanceof z.ZodError) {
      return ResponseUtils.badRequest("Invalid collection data", error.errors);
    }
    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }
    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden(error.message);
    }
    return ResponseUtils.internalServerError("Failed to create badge collection");
  }
}

/**
 * Update a badge collection (admin only)
 */
export async function updateCollectionHandler(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const pathParts = url.pathname.split("/");
    const collectionId = pathParts[pathParts.indexOf("collections") + 1];

    if (!collectionId) {
      return ResponseUtils.badRequest("Collection ID is required");
    }

    const body = await request.json();
    const validatedData = updateCollectionSchema.parse(body);
    
    // Get user ID from request context (set by auth middleware)
    const userId = (request as any).userId;
    if (!userId) {
      return ResponseUtils.unauthorized("Authentication required");
    }

    const collection = await badgeCollectionService.updateCollection(
      collectionId,
      validatedData as UpdateBadgeCollectionRequest,
      userId
    );

    return ResponseUtils.success({ collection });
  } catch (error) {
    console.error("Error updating badge collection:", error);
    if (error instanceof z.ZodError) {
      return ResponseUtils.badRequest("Invalid collection data", error.errors);
    }
    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound(error.message);
    }
    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }
    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden(error.message);
    }
    return ResponseUtils.internalServerError("Failed to update badge collection");
  }
}

/**
 * Delete a badge collection (admin only)
 */
export async function deleteCollectionHandler(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const pathParts = url.pathname.split("/");
    const collectionId = pathParts[pathParts.indexOf("collections") + 1];

    if (!collectionId) {
      return ResponseUtils.badRequest("Collection ID is required");
    }

    // Get user ID from request context (set by auth middleware)
    const userId = (request as any).userId;
    if (!userId) {
      return ResponseUtils.unauthorized("Authentication required");
    }

    await badgeCollectionService.deleteCollection(collectionId, userId);
    return ResponseUtils.success({ message: "Collection deleted successfully" });
  } catch (error) {
    console.error("Error deleting badge collection:", error);
    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound(error.message);
    }
    if (error instanceof BadgeValidationError) {
      return ResponseUtils.badRequest(error.message);
    }
    if (error instanceof InsufficientPermissionsError) {
      return ResponseUtils.forbidden(error.message);
    }
    return ResponseUtils.internalServerError("Failed to delete badge collection");
  }
}

/**
 * Get user's progress in a specific collection
 */
export async function getUserCollectionProgressHandler(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const pathParts = url.pathname.split("/");
    const userId = pathParts[pathParts.indexOf("users") + 1];
    const collectionId = pathParts[pathParts.indexOf("collections") + 1];

    if (!userId || !collectionId) {
      return ResponseUtils.badRequest("User ID and Collection ID are required");
    }

    const progress = await badgeCollectionService.getUserCollectionProgress(userId, collectionId);
    
    if (!progress) {
      return ResponseUtils.success({ 
        progress: null,
        message: "User has not started this collection yet"
      });
    }

    return ResponseUtils.success({ progress });
  } catch (error) {
    console.error("Error getting user collection progress:", error);
    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound(error.message);
    }
    return ResponseUtils.internalServerError("Failed to get collection progress");
  }
}

/**
 * Get all collection progress for a user
 */
export async function getUserCollectionProgressAllHandler(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const pathParts = url.pathname.split("/");
    const userId = pathParts[pathParts.indexOf("users") + 1];

    if (!userId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    const progressList = await badgeCollectionService.getUserCollectionProgressAll(userId);
    return ResponseUtils.success({ progressList });
  } catch (error) {
    console.error("Error getting user collection progress:", error);
    return ResponseUtils.internalServerError("Failed to get user collection progress");
  }
}

/**
 * Get detailed collection progress with badge information
 */
export async function getDetailedCollectionProgressHandler(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const pathParts = url.pathname.split("/");
    const userId = pathParts[pathParts.indexOf("users") + 1];
    const collectionId = pathParts[pathParts.indexOf("collections") + 1];

    if (!userId || !collectionId) {
      return ResponseUtils.badRequest("User ID and Collection ID are required");
    }

    const detailedProgress = await badgeCollectionService.getDetailedCollectionProgress(userId, collectionId);
    return ResponseUtils.success({ detailedProgress });
  } catch (error) {
    console.error("Error getting detailed collection progress:", error);
    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound(error.message);
    }
    return ResponseUtils.internalServerError("Failed to get detailed collection progress");
  }
}

/**
 * Get the next badge a user can unlock in a collection
 */
export async function getNextUnlockableBadgeHandler(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const pathParts = url.pathname.split("/");
    const userId = pathParts[pathParts.indexOf("users") + 1];
    const collectionId = pathParts[pathParts.indexOf("collections") + 1];

    if (!userId || !collectionId) {
      return ResponseUtils.badRequest("User ID and Collection ID are required");
    }

    const nextBadge = await badgeCollectionService.getNextUnlockableBadge(userId, collectionId);
    
    return ResponseUtils.success({ 
      nextBadge,
      message: nextBadge ? "Next badge available" : "No more badges to unlock"
    });
  } catch (error) {
    console.error("Error getting next unlockable badge:", error);
    if (error instanceof BadgeNotFoundError) {
      return ResponseUtils.notFound(error.message);
    }
    return ResponseUtils.internalServerError("Failed to get next unlockable badge");
  }
}

/**
 * Check if a collection is completed by a user
 */
export async function checkCollectionCompletionHandler(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const pathParts = url.pathname.split("/");
    const userId = pathParts[pathParts.indexOf("users") + 1];
    const collectionId = pathParts[pathParts.indexOf("collections") + 1];

    if (!userId || !collectionId) {
      return ResponseUtils.badRequest("User ID and Collection ID are required");
    }

    const isCompleted = await badgeCollectionService.isCollectionCompleted(userId, collectionId);
    
    return ResponseUtils.success({ 
      isCompleted,
      message: isCompleted ? "Collection completed" : "Collection not completed"
    });
  } catch (error) {
    console.error("Error checking collection completion:", error);
    return ResponseUtils.internalServerError("Failed to check collection completion");
  }
}