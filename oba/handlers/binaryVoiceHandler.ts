import { VoiceWebSocketManager } from "../manager/websocket.debug";
import { WebSocketUtils } from "../utils/websocket-utils";
import { logger } from "../services/logger.service";

const voiceLogger = logger.createLogger("BinaryVoiceHandler");

/**
 * Handle binary voice data from a WebSocket with standardized metadata
 *
 * @param voiceWsManager Voice WebSocket manager instance
 * @param userId User ID sending the voice data
 * @param serverId Server ID where voice data is being sent
 * @param channelId Channel ID where voice data is being sent
 * @param data Binary voice data (ArrayBuffer)
 */
export async function handleBinaryVoiceData(
  voiceWsManager: VoiceWebSocketManager,
  userId: string,
  serverId: string,
  channelId: string,
  data: ArrayBuffer,
): Promise<void> {
  try {
    voiceLogger.debug("Processing binary voice data", undefined, {
      userId,
      serverId,
      channelId,
      dataSize: data.byteLength,
    });

    // Validate input parameters
    if (!userId || !serverId || !channelId) {
      voiceLogger.error("Invalid voice data parameters", undefined, {
        userId,
        serverId,
        channelId,
      });
      return;
    }

    if (!data || data.byteLength === 0) {
      voiceLogger.error("Invalid voice data: empty or null", undefined, {
        userId,
        dataSize: data?.byteLength || 0,
      });
      return;
    }

    // Create standardized voice data event with metadata
    const voiceDataEvent = WebSocketUtils.event("VOICE_DATA_RECEIVED", {
      userId,
      serverId,
      channelId,
      dataSize: data.byteLength,
      timestamp: new Date(),
    });

    // Log the voice data transmission
    voiceLogger.debug("Broadcasting voice data to channel", undefined, {
      userId,
      serverId,
      channelId,
      dataSize: data.byteLength,
      eventId: voiceDataEvent.meta.id,
    });

    // Broadcast the binary voice data to all users in the channel
    // Note: Binary data is still sent as-is, but we log it with standardized metadata
    voiceWsManager.broadcast(userId, serverId, channelId, data);

    voiceLogger.debug("Voice data broadcast completed", undefined, {
      userId,
      eventId: voiceDataEvent.meta.id,
    });
  } catch (error) {
    voiceLogger.error("Error handling binary voice data:", undefined, error);
    throw error;
  }
}
