import { z } from "zod";
import * as jose from "jose";
import { eq, or } from "drizzle-orm";
import * as argon2 from "argon2";
import validator from "validator";
import { AuthenticationError } from "../class/errors";
import { db } from "../db";
import { UserSchema } from "../db/schema";
import { logger } from "../services/logger.service";
import { ResponseUtils } from "../utils/response-utils";
import {
  sanitizeTextInput,
  validateUser,
  verifyUserPassword,
  updateUserProfile,
  generatePasswordResetToken,
  resetPassword,
  generateEmailVerificationToken,
  verifyEmailToken,
  generateRefreshToken,
  verifyRefreshToken,
  invalidateRefreshToken,
  updateUserStatus,
} from "../db/utils";
import { getUserBadgesForResponse } from "../utils/badge-response-utils";
// ... other imports (db, UserSchema, sanitizeTextInput, etc.)

const jwtSecret = process.env.JWT_SECRET_KEY;

if (!jwtSecret) {
  throw new Error("JWT_SECRET environment variable not set!");
}

// Create a logger for auth handlers
const authLogger = logger.createLogger("Auth");

export const loginHandler = async (req: Request): Promise<Response> => {
  authLogger.debug("Login handler started");
  const contentType = req.headers.get("Content-Type");
  authLogger.debug("Content-Type:", undefined, { contentType });
  try {
    let username: string;
    let password: string;

    if (contentType === "application/json") {
      console.log("Parsing JSON request body...");
      const body = await req.json();
      console.log("Request body:", body);
      username = body.username;
      password = body.password;
    } else if (contentType === "application/x-www-form-urlencoded") {
      console.log("Parsing form data...");
      const formData = await req.formData();
      username = formData.get("username") as string;
      password = formData.get("password") as string;
    } else {
      console.log("Unsupported content type:", contentType);
      return ResponseUtils.error(
        "UNSUPPORTED_MEDIA_TYPE",
        "Unsupported content type",
        { status: 415 },
      );
    }

    // Validate username and password using Zod (or your preferred method)
    // ... (your Zod validation logic here) ...

    // Authenticate the user (using validateUser from your previous code)
    authLogger.debug("Authenticating user", undefined, { username });
    let user;
    try {
      user = await validateUser(db, { username, password });
      authLogger.debug("User validation result", undefined, {
        success: !!user,
      });

      if (!user) {
        authLogger.warn(
          "Authentication failed: Invalid username or password",
          undefined,
          { username },
        );
        throw new AuthenticationError("Invalid username or password");
      }
      authLogger.info("User authenticated successfully", undefined, {
        userId: user.id,
      });
    } catch (error) {
      authLogger.error("Error during user validation:", undefined, error);
      throw error;
    }

    // Check if email is verified (optional - you can decide whether to enforce this)
    if (!user.isEmailVerified) {
      // Option 1: Block login until email is verified
      // throw new AuthenticationError('Please verify your email before logging in');

      // Option 2: Allow login but include verification status in response
      console.log("Warning: User email is not verified");
    }

    // Create the JWT with shorter expiration time (15 minutes)
    const secret = new TextEncoder().encode(jwtSecret);
    const alg = "HS256"; // Consider using a stronger algorithm like 'RS256' if possible

    const jwt = await new jose.SignJWT({
      userId: user.id, // Payload: include user ID and any other relevant claims
      username: user.username,
      avatar: user.avatar,
      isEmailVerified: user.isEmailVerified,
      // You can add more claims if needed, e.g.,
      // role: user.role,
      // permissions: user.permissions,
    })
      .setProtectedHeader({ alg })
      .setIssuedAt()
      .setIssuer("https://api.berkormanli.dev") // Replace with your issuer
      .setAudience("https://api.berkormanli.dev") // Replace with your audience
      .setExpirationTime("15m") // Short-lived access token (15 minutes)
      .sign(secret);

    // Generate a refresh token
    const refreshTokenData = await generateRefreshToken(db, user.id);

    // Get user badges for response
    const userBadges = await getUserBadgesForResponse(db, user.id);

    // Set cookies for both tokens
    const cookieOptions = "HttpOnly; Secure; SameSite=Strict; Path=/;";
    const accessTokenCookie = `accessToken=${jwt}; ${cookieOptions} Max-Age=900`; // 15 minutes
    const refreshTokenCookie = `refreshToken=${refreshTokenData.refreshToken}; ${cookieOptions} Max-Age=2592000`; // 30 days
    await updateUserStatus(db, user.id, "ONLINE");

    // Return the tokens in the response using ResponseUtils
    const responseData = {
      user: {
        userId: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        isEmailVerified: user.isEmailVerified,
        badges: userBadges,
      },
      token: jwt, // Access token
      refreshToken: refreshTokenData.refreshToken,
      expiresIn: 900, // 15 minutes in seconds
    };

    const response = ResponseUtils.success(responseData, {
      message: "Login successful",
    });

    // Set cookies for both tokens
    response.headers.append("Set-Cookie", accessTokenCookie);
    response.headers.append("Set-Cookie", refreshTokenCookie);

    return response;
  } catch (error) {
    console.error("Login error:", error);

    if (error instanceof AuthenticationError) {
      return ResponseUtils.unauthorized(error.message);
    }

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Internal server error");
  }
};

// Zod schema for registration input validation
const registerSchema = z.object({
  username: z
    .string({
      required_error: "Username is required",
      invalid_type_error: "Username must be a string",
    })
    .min(3, { message: "Username must be at least 3 characters long" })
    .max(255, { message: "Username cannot be longer than 255 characters" })
    .refine(sanitizeTextInput),
  email: z.string().email(),
  password: z
    .string()
    .min(8, { message: "Password must be at least 8 characters long" })
    .regex(/[A-Z]/, {
      message: "Password must contain at least one uppercase letter",
    })
    .regex(/[a-z]/, {
      message: "Password must contain at least one lowercase letter",
    })
    .regex(/[0-9]/, { message: "Password must contain at least one number" })
    .regex(/[^A-Za-z0-9]/, {
      message: "Password must contain at least one special character",
    }),
});

export const registerHandler = async (req: Request): Promise<Response> => {
  try {
    const contentType = req.headers.get("Content-Type");
    let userData: { username?: string; email?: string; password?: string };

    // Parse request body based on content type
    if (contentType === "application/json") {
      userData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      userData = {
        username: formData.get("username") as string | undefined,
        email: formData.get("email") as string | undefined,
        password: formData.get("password") as string | undefined,
      };
    } else {
      return ResponseUtils.error(
        "UNSUPPORTED_MEDIA_TYPE",
        "Unsupported content type",
        { status: 415 },
      );
    }

    // Validate input using Zod
    const validatedData = registerSchema.parse(userData);

    // Sanitize input
    const sanitizedUsername = sanitizeTextInput(validatedData.username);
    const sanitizedEmail = validator.normalizeEmail(validatedData.email) || "";

    if (!sanitizedEmail) {
      throw new AuthenticationError("Invalid email format");
    }

    // Check for existing user
    const existingUser = await db
      .select()
      .from(UserSchema)
      .where(
        or(
          eq(UserSchema.username, sanitizedUsername),
          eq(UserSchema.email, sanitizedEmail),
        ),
      )
      .limit(1);

    if (existingUser.length > 0) {
      throw new AuthenticationError("Username or email already exists");
    }

    // Hash password
    const hashedPassword = await argon2.hash(validatedData.password);

    // Insert new user
    const newUser = await db
      .insert(UserSchema)
      .values({
        username: sanitizedUsername,
        email: sanitizedEmail,
        password: hashedPassword,
        isEmailVerified: false, // New users start with unverified email
      })
      .returning({
        id: UserSchema.id,
        username: UserSchema.username,
        email: UserSchema.email,
        isEmailVerified: UserSchema.isEmailVerified,
        createdAt: UserSchema.createdAt,
        updatedAt: UserSchema.updatedAt,
      });

    // Generate email verification token
    const verificationInfo = await generateEmailVerificationToken(
      db,
      newUser[0].id,
    );

    // In a real application, you would send an email with the verification link
    // For this example, we'll just log the verification link
    const verificationLink = `https://yourapp.com/verify-email?userId=${verificationInfo.userId}&token=${verificationInfo.verificationToken}`;
    authLogger.info(
      "Email verification link would be sent to user",
      undefined,
      {
        email: verificationInfo.email,
        verificationLink,
      },
    );

    // Create a JWT for the new user
    const secret = new TextEncoder().encode(jwtSecret);
    const alg = "HS256";
    const jwt = await new jose.SignJWT({
      userId: newUser[0].id,
      username: newUser[0].username,
    })
      .setProtectedHeader({ alg })
      .setIssuedAt()
      .setIssuer("your-issuer-name")
      .setAudience("your-audience-name")
      .setExpirationTime("2h")
      .sign(secret);

    // Generate a refresh token
    const refreshTokenData = await generateRefreshToken(db, newUser[0].id);

    // Get user badges for response (new users will have empty badges)
    const userBadges = await getUserBadgesForResponse(db, newUser[0].id);

    // Set cookies for both tokens
    const cookieOptions = "HttpOnly; Secure; SameSite=Strict; Path=/;";
    const accessTokenCookie = `accessToken=${jwt}; ${cookieOptions} Max-Age=900`; // 15 minutes
    const refreshTokenCookie = `refreshToken=${refreshTokenData.refreshToken}; ${cookieOptions} Max-Age=2592000`; // 30 days

    const registerHeaders = new Headers();
    registerHeaders.append("Content-Type", "application/json");
    registerHeaders.append("Set-Cookie", accessTokenCookie);
    registerHeaders.append("Set-Cookie", refreshTokenCookie);

    // Return success response with JWT
    const responseData = {
      user: {
        userId: newUser[0].id,
        username: newUser[0].username,
        email: newUser[0].email,
        isEmailVerified: newUser[0].isEmailVerified,
        badges: userBadges,
      },
      token: jwt,
      refreshToken: refreshTokenData.refreshToken,
      expiresIn: 7200, // 2 hours in seconds
    };

    const response = ResponseUtils.created(responseData, {
      message: "Registration successful",
    });

    // Set cookies for both tokens
    response.headers.append("Set-Cookie", accessTokenCookie);
    response.headers.append("Set-Cookie", refreshTokenCookie);

    return response;
  } catch (error) {
    console.error("Registration error:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    if (error instanceof AuthenticationError) {
      return ResponseUtils.conflict(error.message);
    }

    return ResponseUtils.internalError("Internal server error");
  }
};

export const authenticateUserToken = async (
  req: Request,
): Promise<Response> => {
  try {
    const contentType = req.headers.get("Content-Type");
    let body: { token?: string };

    // Parse request body based on content type
    if (contentType === "application/json") {
      body = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      body = {
        token: formData.get("token") as string | undefined,
      };
    } else {
      return ResponseUtils.error(
        "UNSUPPORTED_MEDIA_TYPE",
        "Unsupported content type",
        { status: 415 },
      );
    }

    if (!body.token) {
      throw new Error("Invalid token!");
    }
    const payload = jose.decodeJwt(body.token);
    //console.log(payload)



    return ResponseUtils.success(payload, {
      message: "Token authenticated successfully",
    });
  } catch (error) {
    authLogger.error("Authentication error:", undefined, error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    if (error instanceof AuthenticationError) {
      return ResponseUtils.unauthorized(error.message);
    }

    return ResponseUtils.badRequest("Authentication failed");
  }
};

// Zod schema for profile update validation
const updateProfileSchema = z
  .object({
    userId: z.string().uuid(),
    username: z.string().min(3).max(255).optional(),
    email: z.string().email().optional(),
    avatar: z.string().optional(),
    currentPassword: z.string().optional(),
    newPassword: z
      .string()
      .min(8, { message: "Password must be at least 8 characters long" })
      .regex(/[A-Z]/, {
        message: "Password must contain at least one uppercase letter",
      })
      .regex(/[a-z]/, {
        message: "Password must contain at least one lowercase letter",
      })
      .regex(/[0-9]/, { message: "Password must contain at least one number" })
      .regex(/[^A-Za-z0-9]/, {
        message: "Password must contain at least one special character",
      })
      .optional(),
  })
  .refine(
    (data) => {
      // If newPassword is provided, currentPassword must also be provided
      return !(data.newPassword && !data.currentPassword);
    },
    {
      message: "Current password is required when setting a new password",
      path: ["currentPassword"],
    },
  );

// Zod schema for password reset request
const requestPasswordResetSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
});

// Zod schema for password reset
const resetPasswordSchema = z.object({
  userId: z.string().uuid({ message: "Invalid user ID" }),
  token: z.string().min(1, { message: "Reset token is required" }),
  newPassword: z
    .string()
    .min(8, { message: "Password must be at least 8 characters long" })
    .regex(/[A-Z]/, {
      message: "Password must contain at least one uppercase letter",
    })
    .regex(/[a-z]/, {
      message: "Password must contain at least one lowercase letter",
    })
    .regex(/[0-9]/, { message: "Password must contain at least one number" })
    .regex(/[^A-Za-z0-9]/, {
      message: "Password must contain at least one special character",
    }),
});

/**
 * Handler for requesting a password reset
 */
export const requestPasswordResetHandler = async (
  req: Request,
): Promise<Response> => {
  try {
    const contentType = req.headers.get("Content-Type");
    let resetData: { email?: string };

    // Parse request body based on content type
    if (contentType === "application/json") {
      resetData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      resetData = {
        email: formData.get("email") as string | undefined,
      };
    } else {
      return ResponseUtils.error(
        "UNSUPPORTED_MEDIA_TYPE",
        "Unsupported content type",
        { status: 415 },
      );
    }

    // Validate input using Zod
    const validatedData = requestPasswordResetSchema.parse(resetData);

    try {
      // Generate a password reset token
      const resetInfo = await generatePasswordResetToken(
        db,
        validatedData.email,
      );

      // In a real application, you would send an email with the reset link
      // For this example, we'll just return the token in the response
      // NOTE: In production, you should NEVER return the token in the response
      // Instead, send an email with a link containing the token

      // Example email content:
      const resetLink = `https://yourapp.com/reset-password?userId=${resetInfo.userId}&token=${resetInfo.resetToken}`;
      const emailContent = `
        Hello,

        You have requested to reset your password. Please click the link below to reset your password:

        ${resetLink}

        This link will expire in 1 hour.

        If you did not request a password reset, please ignore this email.

        Best regards,
        Your App Team
      `;

      console.log("Password reset email would be sent to:", resetInfo.email);
      console.log("Email content:", emailContent);

      // Return success response (in production, don't include the token)
      return ResponseUtils.success(
        {
          // The following would be removed in production:
          debug: {
            userId: resetInfo.userId,
            resetToken: resetInfo.resetToken,
            resetLink,
          },
        },
        {
          message: "Password reset instructions sent to your email",
        },
      );
    } catch (error) {
      // If the user is not found, still return a success response for security
      // This prevents user enumeration attacks
      if (error instanceof Error && error.message === "User not found") {
        return ResponseUtils.success(
          {},
          {
            message:
              "If your email is registered, you will receive password reset instructions",
          },
        );
      }
      throw error;
    }
  } catch (error) {
    console.error("Password reset request error:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError(
      "Failed to process password reset request",
    );
  }
};

/**
 * Handler for resetting a password with a valid token
 */
/**
 * Handler for verifying email
 */
export const verifyEmailHandler = async (req: Request): Promise<Response> => {
  try {
    const url = new URL(req.url);
    const userId = url.searchParams.get("userId");
    const token = url.searchParams.get("token");

    if (!userId || !token) {
      return ResponseUtils.badRequest("Missing required parameters");
    }

    try {
      // Verify the email token
      const user = await verifyEmailToken(db, userId, token);

      // Return success response
      return ResponseUtils.success(
        {
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            isEmailVerified: user.isEmailVerified,
          },
        },
        {
          message: "Email verified successfully",
        },
      );
    } catch (error) {
      // Handle specific errors
      if (error instanceof Error) {
        if (error.message === "User not found") {
          return ResponseUtils.badRequest("Invalid verification request");
        }
        if (error.message === "Invalid verification token") {
          return ResponseUtils.badRequest(
            "Invalid or expired verification token",
          );
        }
        if (error.message === "Verification token has expired") {
          return ResponseUtils.badRequest("Verification token has expired");
        }
      }
      throw error;
    }
  } catch (error) {
    console.error("Email verification error:", error);

    return ResponseUtils.internalError("Failed to verify email");
  }
};

/**
 * Handler for refreshing access token using refresh token
 */
export const refreshTokenHandler = async (req: Request): Promise<Response> => {
  try {
    const contentType = req.headers.get("Content-Type");
    let tokenData: { userId?: string; refreshToken?: string };

    // Get refresh token from cookie if available
    const cookies = req.headers.get("Cookie");
    const refreshTokenCookie = cookies
      ?.split(";")
      .find((cookie) => cookie.trim().startsWith("refreshToken="));
    const refreshTokenFromCookie = refreshTokenCookie?.split("=")[1];

    // Parse request body based on content type
    if (contentType === "application/json") {
      tokenData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      tokenData = {
        userId: formData.get("userId") as string | undefined,
        refreshToken: formData.get("refreshToken") as string | undefined,
      };
    } else {
      // If no content type, try to use the cookie
      if (refreshTokenFromCookie) {
        // Extract userId from the JWT in the cookie if possible
        // This is a simplified example - in a real app, you might store the userId in the refresh token claims
        const userId = req.headers.get("X-User-ID"); // Assuming client sends user ID in header

        if (!userId) {
          return ResponseUtils.badRequest("User ID is required");
        }

        tokenData = {
          userId,
          refreshToken: refreshTokenFromCookie,
        };
      } else {
        return ResponseUtils.badRequest("Refresh token is required");
      }
    }

    // Use the refresh token from the request body or cookie
    const refreshToken = tokenData.refreshToken || refreshTokenFromCookie;

    if (!tokenData.userId || !refreshToken) {
      return ResponseUtils.badRequest("User ID and refresh token are required");
    }

    try {
      // Verify the refresh token
      const user = await verifyRefreshToken(db, tokenData.userId, refreshToken);

      // Generate a new access token
      const secret = new TextEncoder().encode(jwtSecret);
      const alg = "HS256";

      const newAccessToken = await new jose.SignJWT({
        userId: user.id,
        username: user.username,
        avatar: user.avatar,
        isEmailVerified: user.isEmailVerified,
      })
        .setProtectedHeader({ alg })
        .setIssuedAt()
        .setIssuer("https://api.berkormanli.dev")
        .setAudience("https://api.berkormanli.dev")
        .setExpirationTime("15m") // Short-lived access token (15 minutes)
        .sign(secret);

      // Set cookie for the new access token
      const cookieOptions = "HttpOnly; Secure; SameSite=Strict; Path=/;";
      const accessTokenCookie = `accessToken=${newAccessToken}; ${cookieOptions} Max-Age=900`; // 15 minutes

      // Return the new access token
      const responseData = {
        token: newAccessToken,
        expiresIn: 900, // 15 minutes in seconds
      };

      const response = ResponseUtils.success(responseData, {
        message: "Token refreshed successfully",
      });

      // Set cookie for the new access token
      response.headers.append("Set-Cookie", accessTokenCookie);

      return response;
    } catch (error) {
      // Handle specific errors
      if (error instanceof Error) {
        if (
          error.message === "User not found" ||
          error.message === "Invalid refresh token" ||
          error.message === "Refresh token has expired"
        ) {
          // Clear the invalid refresh token cookie
          const clearCookie =
            "refreshToken=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0";

          const response = ResponseUtils.unauthorized(
            "Invalid or expired refresh token",
          );
          response.headers.append("Set-Cookie", clearCookie);
          return response;
        }
      }
      throw error;
    }
  } catch (error) {
    console.error("Token refresh error:", error);

    return ResponseUtils.internalError("Failed to refresh token");
  }
};

/**
 * Handler for logging out (invalidating refresh token)
 */
export const logoutHandler = async (req: Request): Promise<Response> => {
  try {
    const contentType = req.headers.get("Content-Type");
    let userData: { userId?: string };

    // Parse request body based on content type
    if (contentType === "application/json") {
      userData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      userData = {
        userId: formData.get("userId") as string | undefined,
      };
    } else {
      // Extract userId from the JWT in the authorization header if possible
      const userId = req.headers.get("X-User-ID"); // Assuming client sends user ID in header

      if (!userId) {
        return ResponseUtils.badRequest("User ID is required");
      }

      userData = { userId };
    }

    if (!userData.userId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    // Invalidate the refresh token
    await invalidateRefreshToken(db, userData.userId);

    // Clear cookies
    const clearAccessCookie =
      "accessToken=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0";
    const clearRefreshCookie =
      "refreshToken=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0";

    // Return success response
    const response = ResponseUtils.success(
      {},
      {
        message: "Logged out successfully",
      },
    );

    // Clear cookies
    response.headers.append("Set-Cookie", clearAccessCookie);
    response.headers.append("Set-Cookie", clearRefreshCookie);

    return response;
  } catch (error) {
    console.error("Logout error:", error);

    return ResponseUtils.internalError("Failed to logout");
  }
};

export const resetPasswordHandler = async (req: Request): Promise<Response> => {
  try {
    const contentType = req.headers.get("Content-Type");
    let resetData: { userId?: string; token?: string; newPassword?: string };

    // Parse request body based on content type
    if (contentType === "application/json") {
      resetData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      resetData = {
        userId: formData.get("userId") as string | undefined,
        token: formData.get("token") as string | undefined,
        newPassword: formData.get("newPassword") as string | undefined,
      };
    } else {
      return ResponseUtils.error(
        "UNSUPPORTED_MEDIA_TYPE",
        "Unsupported content type",
        { status: 415 },
      );
    }

    // Validate input using Zod
    const validatedData = resetPasswordSchema.parse(resetData);

    try {
      // Reset the password
      const user = await resetPassword(
        db,
        validatedData.userId,
        validatedData.token,
        validatedData.newPassword,
      );

      // Return success response
      return ResponseUtils.success(
        {
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
          },
        },
        {
          message: "Password has been reset successfully",
        },
      );
    } catch (error) {
      // Handle specific errors
      if (error instanceof Error) {
        if (error.message === "User not found") {
          return ResponseUtils.badRequest("Invalid reset request");
        }
        if (error.message === "Invalid reset token") {
          return ResponseUtils.badRequest("Invalid or expired reset token");
        }
        if (error.message === "Reset token has expired") {
          return ResponseUtils.badRequest("Reset token has expired");
        }
      }
      throw error;
    }
  } catch (error) {
    console.error("Password reset error:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to reset password");
  }
};

export const updateUserProfileHandler = async (
  req: Request,
): Promise<Response> => {
  try {
    const contentType = req.headers.get("Content-Type");
    let userData: {
      userId?: string;
      username?: string;
      email?: string;
      avatar?: string;
      currentPassword?: string;
      newPassword?: string;
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      userData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      userData = {
        userId: formData.get("userId") as string | undefined,
        username: formData.get("username") as string | undefined,
        email: formData.get("email") as string | undefined,
        avatar: formData.get("avatar") as string | undefined,
        currentPassword: formData.get("currentPassword") as string | undefined,
        newPassword: formData.get("newPassword") as string | undefined,
      };
    } else {
      return ResponseUtils.error(
        "UNSUPPORTED_MEDIA_TYPE",
        "Unsupported content type",
        { status: 415 },
      );
    }

    // Validate input using Zod
    const validatedData = updateProfileSchema.parse(userData);

    // Verify current password if changing password
    if (validatedData.newPassword) {
      const isValid = await verifyUserPassword(
        db,
        validatedData.userId,
        validatedData.currentPassword!,
      );

      if (!isValid) {
        return ResponseUtils.unauthorized("Current password is incorrect");
      }
    }

    // Prepare updates object
    const updates: {
      username?: string;
      email?: string;
      avatar?: string;
      password?: string;
    } = {};

    if (validatedData.username) updates.username = validatedData.username;
    if (validatedData.email) updates.email = validatedData.email;
    if (validatedData.avatar) updates.avatar = validatedData.avatar;
    if (validatedData.newPassword) {
      // Hash the new password
      updates.password = await argon2.hash(validatedData.newPassword);
    }

    // Update user profile
    try {
      const updatedUser = await updateUserProfile(
        db,
        validatedData.userId,
        updates,
      );

      // Get user badges for response
      const userBadges = await getUserBadgesForResponse(db, validatedData.userId);

      // Return success response
      return ResponseUtils.success(
        {
          user: {
            id: updatedUser.id,
            username: updatedUser.username,
            email: updatedUser.email,
            avatar: updatedUser.avatar,
            updatedAt: updatedUser.updatedAt,
            badges: userBadges,
          },
        },
        {
          message: "Profile updated successfully",
        },
      );
    } catch (error) {
      // Handle specific errors from updateUserProfile
      if (error instanceof Error) {
        if (error.message === "Username already taken") {
          return ResponseUtils.conflict("Username is already taken");
        }
        if (error.message === "Email already taken") {
          return ResponseUtils.conflict("Email is already taken");
        }
        if (error.message === "User not found") {
          return ResponseUtils.notFound("User");
        }
      }
      throw error; // Re-throw for the outer catch block
    }
  } catch (error) {
    console.error("Profile update error:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to update profile");
  }
};

export const getUserDetailsHandler = async (
  req: Request,
): Promise<Response> => {
  try {
    const contentType = req.headers.get("Content-Type");
    let userData: { userId?: string };

    // Parse request body based on content type
    if (contentType === "application/json") {
      userData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      userData = {
        userId: formData.get("userId") as string | undefined,
      };
    } else {
      // Extract userId from the JWT in the authorization header if possible
      if (contentType !== "application/json" && contentType !== "application/x-www-form-urlencoded") {
        return ResponseUtils.error(
          "UNSUPPORTED_MEDIA_TYPE",
          "Unsupported content type",
          { status: 415 },
        );
      }
      userData = {};
    }

    // Extract userId from the JWT in the authorization header if possible
    const userId = req.headers.get("X-Authenticated-User"); // Assuming client sends user ID in header

    if (!userId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    userData = { userId };

    if (!userData.userId) {
      return ResponseUtils.badRequest("User ID is required");
    }

    // Fetch user details from the database
    const user = await db
      .select()
      .from(UserSchema)
      .where(eq(UserSchema.id, userData.userId))
      .limit(1);

    if (user.length === 0) {
      return ResponseUtils.notFound("User not found");
    }

    // Get user badges for response
    const userBadges = await getUserBadgesForResponse(db, userData.userId);

    // Return success response with user details
    return ResponseUtils.success(
      {
        user: {
          id: user[0].id,
          username: user[0].username,
          email: user[0].email,
          avatar: user[0].avatar,
          isEmailVerified: user[0].isEmailVerified,
          createdAt: user[0].createdAt,
          updatedAt: user[0].updatedAt,
          badges: userBadges,
        },
      },
      { message: "User details fetched successfully" },
    );
  } catch (error) {
    console.error("Get user details error:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to fetch user details");
  }
}