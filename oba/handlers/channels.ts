import { z } from "zod";
import { db } from "../db";
import {
  createChannel,
  updateChannel,
  deleteChannel,
  getChannelDetails,
} from "../db/utils";
import { EventTypes } from "@kurultai/oba-types";
import { WebSocketManager } from "../manager/websocket.manager";
import {
  hasChannelPermission,
  hasServerPermission,
} from "../utils/permissions";
import { MANAGE_CHANNELS, VIEW_CHANNEL } from "../constants/permissions";
import { ResponseUtils } from "../utils/response-utils";

// Get WebSocketManager singleton instance
const wsManager = WebSocketManager.getInstance();

// Zod schema for channel creation
const createChannelSchema = z.object({
  serverId: z.string().uuid(),
  name: z.string().min(1).max(100),
  description: z.string().max(1000).optional(),
  type: z.enum(["TEXT", "VOICE", "ANNOUNCEMENT"]).default("TEXT"),
  isPublic: z.boolean().default(true),
  allowedRoleIds: z.array(z.string().uuid()).optional(),
  categoryId: z.string().uuid().optional(),
  position: z.number().optional(),
});

// Zod schema for channel update
const updateChannelSchema = z.object({
  channelId: z.string().uuid(),
  serverId: z.string().uuid(),
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(1000).optional(),
  type: z.enum(["TEXT", "VOICE", "ANNOUNCEMENT"]).optional(),
  isPublic: z.boolean().optional(),
  allowedRoleIds: z.array(z.string().uuid()).optional(),
  categoryId: z.string().uuid().optional(),
  position: z.number().optional(),
});

// Zod schema for channel deletion
const deleteChannelSchema = z.object({
  channelId: z.string().uuid(),
  serverId: z.string().uuid(),
});

// Zod schema for getting channel details
const getChannelSchema = z.object({
  channelId: z.string().uuid(),
});

/**
 * Handler for creating a new channel
 */
export const createChannelHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let channelData: {
      serverId?: string;
      name?: string;
      description?: string;
      type?: "TEXT" | "VOICE" | "ANNOUNCEMENT";
      isPublic?: boolean;
      allowedRoleIds?: string[];
      categoryId?: string;
      position?: number;
      userId?: string; // Added userId for permission checking
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      channelData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      channelData = {
        serverId: formData.get("serverId") as string | undefined,
        name: formData.get("name") as string | undefined,
        description: formData.get("description") as string | undefined,
        type: formData.get("type") as
          | "TEXT"
          | "VOICE"
          | "ANNOUNCEMENT"
          | undefined,
        isPublic: formData.has("isPublic")
          ? formData.get("isPublic") === "true"
          : undefined,
        allowedRoleIds: formData.has("allowedRoleIds")
          ? (formData.get("allowedRoleIds") as string).split(",")
          : undefined,
        categoryId: formData.get("categoryId") as string | undefined,
        position: formData.has("position")
          ? Number(formData.get("position"))
          : undefined,
        userId: formData.get("userId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = createChannelSchema.parse(channelData);

    // Check if the user has permission to create channels
    if (channelData.userId) {
      const hasPermission = await hasServerPermission(
        db,
        channelData.userId,
        validatedData.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to create channels",
        );
      }
    }

    // Create the channel
    const channel = await createChannel(
      db,
      validatedData.serverId,
      validatedData.name,
      {
        description: validatedData.description,
        type: validatedData.type,
        isPublic: validatedData.isPublic,
        allowedRoleIds: validatedData.allowedRoleIds,
        categoryId: validatedData.categoryId,
        position: validatedData.position,
      },
    );

    if (!channel) {
      return ResponseUtils.internalError("Failed to create channel");
    }

    // Broadcast the channel creation via WebSocket
    const channelEvent = {
      type: EventTypes.CHANNEL_CREATED,
      sender: channelData.userId || "system",
      data: {
        channel_id: channel.id,
        channel_name: channel.name,
        channel_type: channel.type,
        server_id: channel.serverId,
        category_id: channel.categoryId,
        position: channel.position,
        description: channel.description,
      },
    };

    wsManager.broadcast(JSON.stringify(channelEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.created(channel, {
      message: "Channel created successfully",
    });
  } catch (error) {
    console.error("Error creating channel:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to create channel");
  }
};

/**
 * Handler for updating a channel
 */
export const updateChannelHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let channelData: {
      channelId?: string;
      serverId?: string;
      name?: string;
      description?: string;
      type?: "TEXT" | "VOICE" | "ANNOUNCEMENT";
      isPublic?: boolean;
      allowedRoleIds?: string[];
      categoryId?: string;
      position?: number;
      userId?: string; // Added userId for permission checking
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      channelData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      channelData = {
        channelId: formData.get("channelId") as string | undefined,
        serverId: formData.get("serverId") as string | undefined,
        name: formData.get("name") as string | undefined,
        description: formData.get("description") as string | undefined,
        type: formData.get("type") as
          | "TEXT"
          | "VOICE"
          | "ANNOUNCEMENT"
          | undefined,
        isPublic: formData.has("isPublic")
          ? formData.get("isPublic") === "true"
          : undefined,
        allowedRoleIds: formData.has("allowedRoleIds")
          ? (formData.get("allowedRoleIds") as string).split(",")
          : undefined,
        categoryId: formData.get("categoryId") as string | undefined,
        position: formData.has("position")
          ? Number(formData.get("position"))
          : undefined,
        userId: formData.get("userId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = updateChannelSchema.parse(channelData);

    // Check if the user has permission to update channels
    if (channelData.userId) {
      const hasPermission = await hasChannelPermission(
        db,
        channelData.userId,
        validatedData.channelId,
        validatedData.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to update this channel",
        );
      }
    }

    // Create updates object with only the fields that are provided
    const updates: {
      name?: string;
      description?: string;
      type?: "TEXT" | "VOICE" | "ANNOUNCEMENT";
      isPublic?: boolean;
      categoryId?: string;
      position?: number;
    } = {};

    if (validatedData.name !== undefined) updates.name = validatedData.name;
    if (validatedData.description !== undefined)
      updates.description = validatedData.description;
    if (validatedData.type !== undefined) updates.type = validatedData.type;
    if (validatedData.isPublic !== undefined)
      updates.isPublic = validatedData.isPublic;
    if (validatedData.categoryId !== undefined)
      updates.categoryId = validatedData.categoryId;
    if (validatedData.position !== undefined)
      updates.position = validatedData.position;

    // Update the channel
    const channel = await updateChannel(
      db,
      validatedData.channelId,
      updates,
      validatedData.allowedRoleIds,
    );

    if (!channel) {
      return ResponseUtils.notFound("Channel");
    }

    // Broadcast the channel update via WebSocket
    const channelEvent = {
      type: EventTypes.CHANNEL_UPDATED, // EventTypes.CHANNEL_UPDATED,
      sender: channelData.userId || "system",
      data: {
        channel_id: channel.id,
        channel_name: channel.name,
        channel_type: channel.type,
        server_id: channel.serverId,
        category_id: channel.categoryId,
        position: channel.position,
        description: channel.description,
      },
    };

    wsManager.broadcast(JSON.stringify(channelEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success(channel, {
      message: "Channel updated successfully",
    });
  } catch (error) {
    console.error("Error updating channel:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to update channel");
  }
};

/**
 * Handler for deleting a channel
 */
export const deleteChannelHandler = async (req: Request) => {
  try {
    const contentType = req.headers.get("Content-Type");
    let channelData: {
      channelId?: string;
      serverId?: string;
      userId?: string; // Added userId for permission checking
    };

    // Parse request body based on content type
    if (contentType === "application/json") {
      channelData = await req.json();
    } else if (contentType === "application/x-www-form-urlencoded") {
      const formData = await req.formData();
      channelData = {
        channelId: formData.get("channelId") as string | undefined,
        serverId: formData.get("serverId") as string | undefined,
        userId: formData.get("userId") as string | undefined,
      };
    } else {
      return ResponseUtils.unsupportedMediaType();
    }

    // Validate input using Zod
    const validatedData = deleteChannelSchema.parse(channelData);

    // Check if the user has permission to delete channels
    if (channelData.userId) {
      const hasPermission = await hasChannelPermission(
        db,
        channelData.userId,
        validatedData.channelId,
        validatedData.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to delete this channel",
        );
      }
    }

    // Delete the channel
    const success = await deleteChannel(db, validatedData.channelId);

    if (!success) {
      return ResponseUtils.notFound("Channel");
    }

    // Broadcast the channel deletion via WebSocket
    const channelEvent = {
      type: EventTypes.CHANNEL_DELETED, // EventTypes.CHANNEL_DELETED,
      sender: channelData.userId || "system",
      data: {
        channel_id: validatedData.channelId,
        server_id: validatedData.serverId,
      },
    };

    wsManager.broadcast(JSON.stringify(channelEvent), validatedData.serverId);

    // Return success response
    return ResponseUtils.success(
      { channelId: validatedData.channelId },
      { message: "Channel deleted successfully" },
    );
  } catch (error) {
    console.error("Error deleting channel:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to delete channel");
  }
};

/**
 * Handler for getting channel details
 */
export const getChannelDetailsHandler = async (req: Request) => {
  try {
    const url = new URL(req.url);
    const channelId = url.searchParams.get("channelId");
    const userId = url.searchParams.get("userId"); // Added userId for permission checking
    const serverId = url.searchParams.get("serverId"); // Added serverId for permission checking

    if (!channelId) {
      return ResponseUtils.badRequest("Channel ID is required");
    }

    // Validate input using Zod
    const validatedData = getChannelSchema.parse({ channelId });

    // Check if the user has permission to view the channel
    if (userId && serverId) {
      const hasPermission = await hasChannelPermission(
        db,
        userId,
        validatedData.channelId,
        serverId,
        VIEW_CHANNEL,
      );

      if (!hasPermission) {
        return ResponseUtils.forbidden(
          "You do not have permission to view this channel",
        );
      }
    }

    // Get the channel details
    const channel = await getChannelDetails(db, validatedData.channelId);

    if (!channel) {
      return ResponseUtils.notFound("Channel");
    }

    // Return the channel details
    return ResponseUtils.success(channel, {
      message: "Channel details retrieved successfully",
    });
  } catch (error) {
    console.error("Error getting channel details:", error);

    if (error instanceof z.ZodError) {
      return ResponseUtils.validationError(
        error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      );
    }

    return ResponseUtils.internalError("Failed to get channel details");
  }
};
