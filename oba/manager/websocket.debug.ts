import type { ServerWebSocket } from "bun";
import type {
  CustomWebSocketData,
  WebSocketSet,
  TopicSubscriptionsMap,
  User,
} from "../types/websocket.types";
import { logger } from "../services/logger.service";

// Import shared types to ensure consistency across the application

export class VoiceWebSocketManager {
  //private sockets: Set<ServerWebSocket<CustomWebSocketData>>
  public topicSubscriptions: TopicSubscriptionsMap;
  private logger = logger.createLogger("VoiceWebSocketManager");

  // Singleton instance
  private static instance: VoiceWebSocketManager;

  // Private constructor to prevent direct instantiation
  private constructor() {
    this.logger.info("VoiceWebSocketManager initialized");
    //this.sockets = new Set();
    this.topicSubscriptions = new Map<string, WebSocketSet>();
  }

  // Static method to get the singleton instance
  public static getInstance(): VoiceWebSocketManager {
    if (!VoiceWebSocketManager.instance) {
      VoiceWebSocketManager.instance = new VoiceWebSocketManager();
    }
    return VoiceWebSocketManager.instance;
  }

  add(ws: ServerWebSocket<CustomWebSocketData>) {
    this.logger.debug("Adding WebSocket to voice channel", undefined, {
      userId: ws.data.userId,
    });

    // Make sure userId is set
    if (!ws.data.userId) {
      this.logger.error(
        "Cannot add WebSocket to voice channel: userId is not set",
      );
      return;
    }

    const { serverId, channelId } = ws.data;

    // Make sure serverId and channelId are set
    if (!serverId || !channelId) {
      this.logger.error(
        `Missing serverId or channelId for user ${ws.data.userId}`,
      );
      return;
    }

    const serverChannelCombo = `${serverId}:${channelId}`;
    this.logger.debug(
      `Adding user ${ws.data.userId} to voice channel topic: ${serverChannelCombo}`,
    );

    // Create a new topic if it doesn't exist
    if (!this.topicSubscriptions.has(serverChannelCombo)) {
      this.logger.debug(`Creating new topic for ${serverChannelCombo}`);
      this.topicSubscriptions.set(
        serverChannelCombo,
        new Set<ServerWebSocket<CustomWebSocketData>>(),
      );
    }

    // Add the WebSocket to the topic
    this.topicSubscriptions.get(serverChannelCombo)?.add(ws);
    this.logger.debug(
      `User ${ws.data.userId} added to topic ${serverChannelCombo}`,
    );
    this.logger.debug(
      `Topic ${serverChannelCombo} now has ${this.topicSubscriptions.get(serverChannelCombo)?.size} users`,
    );

    // Notify other users and send user list
    this.notifyChannelUsers(ws, serverChannelCombo, "user_joined"); // Notify existing users about new joiner, using topic
    this.sendUserListToNewUser(ws, serverChannelCombo, ws.data.userId); // Send list of existing users to the new user, using topic and userId
  }

  remove(ws: ServerWebSocket<CustomWebSocketData>) {
    this.logger.debug("Removing WebSocket from voice channel", undefined, {
      userId: ws.data.userId,
    });

    // Make sure userId is set
    if (!ws.data.userId) {
      this.logger.error(
        "Cannot remove WebSocket from voice channel: userId is not set",
      );
      return;
    }

    const { serverId, channelId } = ws.data;

    // Make sure serverId and channelId are set
    if (!serverId || !channelId) {
      this.logger.error(
        `Missing serverId or channelId for user ${ws.data.userId}`,
      );
      return;
    }

    const serverChannelCombo = `${serverId}:${channelId}`;
    this.logger.debug(
      `Removing user ${ws.data.userId} from voice channel topic: ${serverChannelCombo}`,
    );

    // Check if the topic exists
    if (!this.topicSubscriptions.has(serverChannelCombo)) {
      this.logger.debug(`Topic ${serverChannelCombo} does not exist`);
      return;
    }

    // Remove the WebSocket from the topic
    this.topicSubscriptions.get(serverChannelCombo)?.delete(ws);
    this.logger.debug(
      `User ${ws.data.userId} removed from topic ${serverChannelCombo}`,
    );
    this.logger.debug(
      `Topic ${serverChannelCombo} now has ${this.topicSubscriptions.get(serverChannelCombo)?.size} users`,
    );

    // Notify other users
    this.notifyChannelUsers(ws, serverChannelCombo, "user_left"); // Notify remaining users about leaving user, using topic
  }

  broadcast(
    userId: string,
    serverId: string,
    channelId: string,
    data: ArrayBuffer,
  ) {
    this.logger.debug("Broadcasting voice data", undefined, {
      userId,
      serverId,
      channelId,
      dataSize: data.byteLength,
    });

    // Make sure userId, serverId, and channelId are set
    if (!userId) {
      this.logger.error("Cannot broadcast: userId is not set");
      return;
    }

    if (!serverId || !channelId) {
      this.logger.error(
        `Missing serverId or channelId for broadcast from user ${userId}`,
      );
      return;
    }

    const serverChannelCombo = `${serverId}:${channelId}`;
    //console.log(`Broadcasting binary data to channel ${serverChannelCombo} from user ${userId}, data size: ${data.byteLength} bytes`);

    // Check if the topic exists
    if (!this.topicSubscriptions.has(serverChannelCombo)) {
      //console.log(`Topic ${serverChannelCombo} does not exist for broadcast`);
      return;
    }

    // Get the sockets in the topic
    const sockets = this.topicSubscriptions.get(serverChannelCombo);
    if (!sockets) {
      //console.log(`No sockets found for topic ${serverChannelCombo}`);
      return;
    }

    //console.log(`Found ${sockets.size} sockets in topic ${serverChannelCombo}`);

    // Broadcast to all sockets except the sender
    let broadcastCount = 0;
    sockets.forEach((ws) => {
      // Check if the socket is open and not the sender
      // First try ws.data.userId, then fall back to ws.data.user?.id
      const socketUserId = ws.data.userId || ws.data.user?.id;
      //console.log(`Socket user ID: ${socketUserId}, sender ID: ${userId}, readyState: ${ws.readyState}`);

      //if (ws.readyState === WebSocket.OPEN && socketUserId && socketUserId !== userId) {
      if (ws.readyState === WebSocket.OPEN) {
        //console.log(`Sending binary data to user ${socketUserId}`);
        try {
          ws.send(data);
          broadcastCount++;
        } catch (error) {
          this.logger.error(
            `Error sending binary data to user ${socketUserId}:`,
            undefined,
            error,
          );
        }
      }
    });
    //console.log(`Broadcast binary data to ${broadcastCount} users`);
  }

  private notifyChannelUsers(
    ws: ServerWebSocket<CustomWebSocketData>,
    topic: string,
    eventType: "user_joined" | "user_left",
  ) {
    // Make sure userId is set
    if (!ws.data.userId) {
      this.logger.error("Cannot notify channel users: userId is not set");
      return;
    }

    this.logger.debug(
      `Notifying users in topic ${topic} about user ${ws.data.userId} ${eventType}`,
    );

    const channelSockets = this.topicSubscriptions.get(topic);
    if (!channelSockets) {
      this.logger.warn(
        `No sockets found for topic ${topic} to notify about user ${eventType}`,
      );
      return; // No users in the channel to notify
    }

    this.logger.debug(`Found ${channelSockets.size} sockets in topic ${topic}`);

    // Create user object if it doesn't exist
    const user = ws.data.user || {
      id: ws.data.userId,
      name: ws.data.userId, // Use userId as name if not available
      avatar: null,
      speaking: false,
      muted: false,
      deafened: false,
    };

    const notification = JSON.stringify({
      type:
        eventType === "user_joined"
          ? "user_joined_channel"
          : "user_left_channel",
      userId: ws.data.userId, // User ID of the user who joined/left
      user: user,
    });

    let notifyCount = 0;
    channelSockets.forEach((socket) => {
      if (socket !== ws && socket.readyState === WebSocket.OPEN) {
        // Don't notify the user who just joined/left, notify others
        const socketUserId = socket.data.userId || socket.data.user?.id;
        this.logger.debug(
          `Notifying user ${socketUserId} about user ${ws.data.userId} ${eventType}`,
        );
        try {
          socket.send(notification);
          notifyCount++;
        } catch (error) {
          this.logger.error(
            `Error notifying user ${socketUserId}:`,
            undefined,
            error,
          );
        }
      }
    });

    this.logger.debug(
      `Notified ${notifyCount} users in topic ${topic} about user ${ws.data.userId} ${eventType}`,
    );
  }

  private sendUserListToNewUser(
    ws: ServerWebSocket<CustomWebSocketData>,
    topic: string,
    newUserId: string,
  ) {
    // Make sure newUserId is set
    if (!newUserId) {
      this.logger.error("Cannot send user list: newUserId is not set");
      return;
    }

    this.logger.debug(
      `Sending user list to new user ${newUserId} in topic ${topic}`,
    );

    const channelSockets = this.topicSubscriptions.get(topic);
    if (!channelSockets) {
      this.logger.debug(
        `No other users in topic ${topic} when user ${newUserId} joined.`,
      );
      try {
        ws.send(JSON.stringify({ type: "channel_user_list", userIds: [] })); // Send empty list if no users
      } catch (error) {
        this.logger.error(
          `Error sending empty user list to user ${newUserId}:`,
          undefined,
          error,
        );
      }
      return;
    }

    this.logger.debug(`Found ${channelSockets.size} sockets in topic ${topic}`);

    // Collect user IDs and user objects
    const users: Array<User> = [];
    const userIds: Array<string> = [];

    Array.from(channelSockets).forEach((socket) => {
      // Skip the new user
      const socketUserId = socket.data.userId || socket.data.user?.id;
      if (socketUserId && socketUserId !== newUserId) {
        userIds.push(socketUserId);

        // Add user object if available
        if (socket.data.user) {
          users.push(socket.data.user);
        } else {
          // Create minimal user object
          users.push({
            id: socketUserId,
            name: socketUserId,
            avatar: null,
            speaking: false,
            muted: false,
            deafened: false,
          });
        }
      }
    });

    this.logger.debug(`User list for topic ${topic}`, undefined, { userIds });
    try {
      ws.send(
        JSON.stringify({
          type: "channel_user_list",
          userIds: userIds,
          users: users,
        }),
      );
      this.logger.debug(
        `Sent user list to new user ${newUserId} in topic ${topic}`,
      );
    } catch (error) {
      this.logger.error(
        `Error sending user list to user ${newUserId}:`,
        undefined,
        error,
      );
    }
  }

  getSocketsInChannel(
    topic: string,
  ): Set<ServerWebSocket<CustomWebSocketData>> | undefined {
    this.logger.debug(`Getting sockets in channel ${topic}`);
    const sockets = this.topicSubscriptions.get(topic);
    this.logger.debug(`Found ${sockets?.size || 0} sockets in topic ${topic}`);
    return sockets;
  }

  getSocketByUserIdAndTopic(
    userId: string,
    topic: string,
  ): ServerWebSocket<CustomWebSocketData> | undefined {
    // Make sure userId is set
    if (!userId) {
      this.logger.error("Cannot get socket: userId is not set");
      return undefined;
    }

    this.logger.debug(`Getting socket for user ${userId} in topic ${topic}`);

    const channelSockets = this.topicSubscriptions.get(topic);
    if (!channelSockets) {
      this.logger.debug(`No sockets found for topic ${topic}`);
      return undefined;
    }

    this.logger.debug(`Found ${channelSockets.size} sockets in topic ${topic}`);

    // First try to find by userId
    for (const ws of channelSockets) {
      if (ws.data.userId === userId) {
        this.logger.debug(
          `Found socket for user ${userId} in topic ${topic} by userId`,
        );
        return ws;
      }
    }

    // Then try to find by user.id
    for (const ws of channelSockets) {
      if (ws.data.user?.id === userId) {
        this.logger.debug(
          `Found socket for user ${userId} in topic ${topic} by user.id`,
        );
        return ws;
      }
    }

    this.logger.debug(`Socket not found for user ${userId} in topic ${topic}`);
    return undefined;
  }
}
