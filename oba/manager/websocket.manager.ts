import type { ServerWebSocket } from "bun";
import { nanoid } from "nanoid";
import { type WebSocketMessage } from "../types";
import {
  EventTypes,
  EventTypes as WebSocketMessageType,
  type ChannelCreatePayload,
  type ServerCreatePayload,
  type ServerJoinPayload,
} from "@kurultai/oba-types";

BigInt.prototype.toJSON = function () {
  return Number(this);
};

// Define custom event types
const CustomEventTypes = {
  ERROR: "ERROR",
  MESSAGES_RETRIEVED: "MESSAGES_RETRIEVED",
};
import { debounce } from "../utils";
import {
  addUserToServer,
  createChannel,
  createChannelCategory,
  createNewMessage,
  createServer,
  retrieveLastNMessages,
  getMessageById,
  editExistingMessage,
  deleteMessage,
  addMessageReaction,
  removeMessageReaction,
  getMessageReactionCounts,
  updateUserStatus,
  getOnlineFriends,
  getDirectMessagesBetweenUsers,
  sendFriendRequest,
  acceptFriendRequest,
  rejectFriendRequest,
  cancelFriendRequest,
  getPendingFriendRequests,
  getUserIdByUsername,
  getSentFriendRequests,
  getUserByUsername,
  getUserByUserId,
  getAllFriends,
} from "../db/utils";
import { getUserBadgesForWebSocket } from "../utils/badge-response-utils";
import { db } from "../db";
import {
  FriendshipSchema,
  ServerBanSchema,
  ServerMembershipSchema,
  ServerRoleSchema,
  ServerSchema,
  UserRoles,
  UserSchema,
} from "../db/schema";
import { and, eq } from "drizzle-orm";
import { hasServerPermission } from "../utils/permissions";
import { MANAGE_CHANNELS } from "../constants/permissions";
import { logger } from "../services/logger.service";
import { correlationTracker } from "../utils/correlation-tracker";
import {
  updateServerStructure,
  updateChannelPositions,
} from "../db/utils/bulk-operations";
import { ChannelSchema } from "../db/schema";
import type { CustomWebSocketData, User } from "../types/websocket.types";
import { WebSocketUtils } from "../utils/websocket-utils";
import {
  WebSocketErrorCode,
  type IEnhancedWebSocketData,
  type IWebSocketMessage,
  type ICorrelationInfo,
  type IWebSocketConfig,
  type IWebSocketMetrics,
  type EnhancedWebSocket,
} from "../types/websocket-standardization.types";

type sockets = Set<ServerWebSocket<CustomWebSocketData>>;

// A Map to store topic subscriptions, keyed by topic name
type TopicSubscriptionsMap = Map<string, sockets>;

// Add these enums at the top of the file
enum ConnectionState {
  CONNECTING = "CONNECTING",
  CONNECTED = "CONNECTED",
  DISCONNECTING = "DISCONNECTING",
  DISCONNECTED = "DISCONNECTED",
  RECONNECTING = "RECONNECTING",
}

interface ConnectionMetadata {
  state: ConnectionState;
  lastPing: number;
  reconnectAttempts: number;
  lastTokenRefresh: number;
  sessionId: string;
}

interface RateLimitInfo {
  count: number;
  lastReset: number;
  blockedUntil?: number;
}

interface LogEntry {
  timestamp: number;
  type: string;
  userId: string;
  eventType: string;
  details: any;
}

interface ErrorEvent {
  type: "ERROR";
  code: string;
  message: string;
  details?: any;
  timestamp: number;
}

interface PerformanceMetrics {
  messageCount: number;
  errorCount: number;
  averageLatency: number;
  lastUpdated: number;
}

interface SessionData {
  userId: string;
  deviceId: string;
  lastActivity: number;
  token: string;
  tokenExpiry: number;
  refreshToken: string;
  isActive: boolean;
}

interface DeviceSession {
  ws: ServerWebSocket<CustomWebSocketData>;
  lastActivity: number;
}

export class WebSocketManager {
  public topicSubscriptions: TopicSubscriptionsMap;
  public clients: Set<ServerWebSocket<CustomWebSocketData>>;
  // Map to store private connections by userId
  private privateConnections: Map<string, ServerWebSocket<CustomWebSocketData>>;
  private logger = logger.createLogger("WebSocketManager");
  private connectionMetadata: WeakMap<
    ServerWebSocket<CustomWebSocketData>,
    ConnectionMetadata
  >;
  private readonly MAX_RECONNECT_ATTEMPTS = 5;
  private readonly RECONNECT_BACKOFF_MS = 1000;
  private readonly TOKEN_REFRESH_THRESHOLD_MS = 5 * 60 * 1000; // 5 minutes
  private readonly PING_INTERVAL_MS = 30000; // 30 seconds
  private rateLimits: Map<string, RateLimitInfo>;
  private readonly RATE_LIMIT_WINDOW_MS = 60000; // 1 minute
  private readonly MAX_EVENTS_PER_WINDOW = 100;
  private readonly BLOCK_DURATION_MS = 300000; // 5 minutes
  private readonly MAX_MESSAGE_SIZE = 1024 * 1024; // 1MB
  private readonly logBuffer: LogEntry[];
  private readonly MAX_LOG_ENTRIES = 1000;
  private errorBuffer: ErrorEvent[];
  private readonly MAX_ERROR_BUFFER_SIZE = 1000;
  private performanceMetrics: Map<string, PerformanceMetrics>;
  private readonly METRICS_UPDATE_INTERVAL = 60000; // 1 minute

  private sessions: Map<string, SessionData> = new Map(); // userId -> SessionData
  private deviceSessions: Map<string, Map<string, DeviceSession>> = new Map(); // userId -> Map<deviceId, DeviceSession>
  private connectionContext: Map<
    string,
    { serverId?: string; channelId?: string; type?: string }
  > = new Map(); // userId -> connection context
  private readonly SESSION_TIMEOUT_MS = 24 * 60 * 60 * 1000; // 24 hours
  private readonly MAX_DEVICES_PER_USER = 5;

  // Enhanced WebSocket standardization features
  private enhancedConnections: Map<string, IEnhancedWebSocketData> = new Map(); // userId -> enhanced data
  private subscriptionManager: Map<string, Set<string>> = new Map(); // userId -> Set<topics>
  private heartbeatIntervals: Map<string, Timer> = new Map(); // userId -> interval
  private rateLimitBuckets: Map<
    string,
    { tokens: number; lastRefill: number }
  > = new Map(); // userId -> rate limit bucket
  private correlationTimeouts: Map<string, Timer> = new Map(); // correlationId -> timeout
  private config: IWebSocketConfig = {
    messageVersion: "1.0.0",
    heartbeatInterval: 30000,
    connectionTimeout: 60000,
    maxMessageSize: 1024 * 1024,
    rateLimits: {
      default: { maxRequests: 100, windowMs: 60000 },
      messaging: { maxRequests: 50, windowMs: 60000 },
      voice: { maxRequests: 200, windowMs: 60000 },
    },
    enableCorrelation: true,
    enableValidation: true,
    enableCompression: false,
    logLevel: "info",
    developmentMode: process.env.NODE_ENV === "development",
    correlationCleanupInterval: 300000, // 5 minutes
    defaultCorrelationTimeout: 30000, // 30 seconds
  };
  private metrics: IWebSocketMetrics = {
    messagesPerSecond: 0,
    activeConnections: 0,
    errorRate: 0,
    averageResponseTime: 0,
    rateLimitHits: 0,
    correlationTimeouts: 0,
    bytesSent: 0,
    bytesReceived: 0,
    connectionRate: 0,
    validationFailures: 0,
  };

  // Singleton instance
  private static instance: WebSocketManager;

  // Private constructor to prevent direct instantiation
  private constructor() {
    this.topicSubscriptions = new Map<string, sockets>();
    this.clients = new Set<ServerWebSocket<CustomWebSocketData>>();
    this.privateConnections = new Map<
      string,
      ServerWebSocket<CustomWebSocketData>
    >();
    this.connectionMetadata = new WeakMap();
    this.rateLimits = new Map();
    this.logBuffer = [];
    this.errorBuffer = [];
    this.performanceMetrics = new Map();

    this.logger.info("WebSocketManager initialized");

    // Initialize enhanced features
    this.initializeEnhancedFeatures();

    // Set all users to offline when the server starts
    this.setAllUsersOffline();
  }

  /**
   * Initialize enhanced WebSocket standardization features
   */
  private initializeEnhancedFeatures(): void {
    // Start correlation cleanup interval
    if (this.config.enableCorrelation) {
      setInterval(() => {
        this.cleanupExpiredCorrelations();
      }, this.config.correlationCleanupInterval);
    }

    // Start metrics collection interval
    setInterval(() => {
      this.updateMetrics();
    }, 60000); // Update metrics every minute

    this.logger.info("Enhanced WebSocket features initialized", undefined, {
      correlationEnabled: this.config.enableCorrelation,
      validationEnabled: this.config.enableValidation,
      heartbeatInterval: this.config.heartbeatInterval,
    });
  }

  // Static method to get the singleton instance
  public static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  // Set all users to offline when the server starts
  async setAllUsersOffline() {
    try {
      // Import the function to avoid naming conflict
      const { setAllUsersOffline: setOffline } = await import("../db/utils");
      await setOffline(db);
      this.logger.info("All users set to offline");
    } catch (error) {
      this.logger.error(
        "Error setting all users to offline:",
        undefined,
        error,
      );
    }
  }

  add(ws: ServerWebSocket<CustomWebSocketData>) {
    const { serverId, channelId, userId } = ws.data;

    // Add to clients set
    this.clients.add(ws);

    // Initialize enhanced data for the user
    if (userId) {
      this.getOrCreateEnhancedData(userId);

      // Start heartbeat monitoring
      this.startHeartbeat(ws);

      // Initialize rate limiting bucket
      if (!this.rateLimitBuckets.has(userId)) {
        this.rateLimitBuckets.set(userId, {
          tokens: this.config.rateLimits.default.maxRequests,
          lastRefill: Date.now(),
        });
      }

      // Send connection established event using standardized format
      const connectionEvent = WebSocketUtils.event("CONNECTION_ESTABLISHED", {
        userId,
        timestamp: new Date(),
        sessionId: nanoid(),
      });
      WebSocketUtils.send(ws, connectionEvent);
    }

    // If serverId and channelId are provided, add to a channel topic
    if (serverId && channelId) {
      this.logger.debug(`Adding user ${userId} to channel`, undefined, {
        serverId,
        channelId,
      });

      const serverChannelCombo = `${serverId}:${channelId}`;
      if (!this.topicSubscriptions.has(serverChannelCombo)) {
        this.topicSubscriptions.set(
          serverChannelCombo,
          new Set<ServerWebSocket<CustomWebSocketData>>(),
        );
      }
      this.topicSubscriptions.get(serverChannelCombo)?.add(ws);

      // Subscribe to the channel topic using enhanced subscription management
      if (userId) {
        this.subscribe(ws, serverChannelCombo);
      }

      this.notifyChannelUsers(ws, serverChannelCombo, "user_channel_subscribe"); // Notify existing users about new joiner, using topic
      this.sendUserListToNewUser(ws, serverChannelCombo, userId); // Send list of existing users to the new user, using topic and userId
    }

    // Update metrics
    this.metrics.activeConnections = this.clients.size;
    this.metrics.connectionRate++;
  }

  /**
   * Add a WebSocket connection as a private connection for direct messaging and friend events
   * @param ws The WebSocket connection to add
   */
  addPrivateConnection(ws: ServerWebSocket<CustomWebSocketData>) {
    const { userId } = ws.data;

    if (!userId) {
      this.logger.error("Cannot add private connection: userId is not set");
      return;
    }

    // Add to clients set
    this.clients.add(ws);

    // Store in private connections map
    this.privateConnections.set(userId, ws);

    // Initialize enhanced data for the user
    this.getOrCreateEnhancedData(userId);

    // Start heartbeat monitoring
    this.startHeartbeat(ws);

    // Initialize rate limiting bucket
    if (!this.rateLimitBuckets.has(userId)) {
      this.rateLimitBuckets.set(userId, {
        tokens: this.config.rateLimits.default.maxRequests,
        lastRefill: Date.now(),
      });
    }

    // Send private connection established event using standardized format
    const connectionEvent = WebSocketUtils.event(
      "PRIVATE_CONNECTION_ESTABLISHED",
      {
        userId,
        timestamp: new Date(),
        sessionId: nanoid(),
      },
    );
    WebSocketUtils.send(ws, connectionEvent);

    this.logger.debug(`Added private connection for user ${userId}`);

    // Set the connection type to 'private'
    ws.data.type = "private";

    // Update metrics
    this.metrics.activeConnections = this.clients.size;
    this.metrics.connectionRate++;
  }

  remove(ws: ServerWebSocket<CustomWebSocketData>) {
    const { serverId, channelId, userId, type } = ws.data;

    // Remove from clients set
    this.clients.delete(ws);

    // Send connection closed event using standardized format
    if (userId) {
      const disconnectionEvent = WebSocketUtils.event("CONNECTION_CLOSED", {
        userId,
        timestamp: new Date(),
        reason: "client_disconnect",
      });

      // Try to send the event if the connection is still open
      if (ws.readyState === WebSocket.OPEN) {
        WebSocketUtils.send(ws, disconnectionEvent);
      }

      // Perform enhanced cleanup for the user
      this.enhancedCleanup(ws);
    }

    // If it's a private connection, remove from private connections map
    if (type === "private" && userId) {
      this.privateConnections.delete(userId);
      this.logger.debug(`Removed private connection for user ${userId}`);

      // Update metrics
      this.metrics.activeConnections = this.clients.size;
      return;
    }

    // If it's a channel connection, remove from topic subscriptions
    if (serverId && channelId) {
      const serverChannelCombo = `${serverId}:${channelId}`;
      if (!this.topicSubscriptions.has(serverChannelCombo)) return;

      this.topicSubscriptions.get(serverChannelCombo)?.delete(ws);

      // Unsubscribe from the topic using enhanced subscription management
      if (userId) {
        this.unsubscribe(ws, serverChannelCombo);
      }

      this.notifyChannelUsers(
        ws,
        serverChannelCombo,
        "user_channel_unsubscribe",
      ); // Notify remaining users about leaving user, using topic

      // Use WebSocketUtils for standardized event message
      const unsubscribeEvent = WebSocketUtils.event("CHANNEL_UNSUBSCRIBE", {
        userId: userId,
        timestamp: new Date(),
      });

      // Broadcast to other users in the channel
      const channelSockets = this.topicSubscriptions.get(serverChannelCombo);
      if (channelSockets && channelSockets.size > 0) {
        WebSocketUtils.broadcast(channelSockets, unsubscribeEvent);
      }

      this.logger.debug(
        `Removed user ${userId} from channel ${channelId} in server ${serverId}`,
      );
    }

    // Update metrics
    this.metrics.activeConnections = this.clients.size;
  }

  // Enhanced broadcast method using WebSocketUtils
  broadcast(
    message: string | IWebSocketMessage,
    serverId?: string,
    channelId?: string,
    userId?: string,
  ) {
    if (!serverId) {
      this.logger.error("Server ID not available, cannot broadcast!");
      return;
    }

    // Convert string message to standardized format if needed
    let standardizedMessage: IWebSocketMessage;
    if (typeof message === "string") {
      try {
        // Try to parse as JSON first
        const parsed = JSON.parse(message);
        if (WebSocketUtils.validate(parsed)) {
          standardizedMessage = parsed;
        } else {
          // Create a standardized event message for legacy string messages
          standardizedMessage = WebSocketUtils.event("LEGACY_MESSAGE", {
            content: message,
          });
        }
      } catch {
        // Create a standardized event message for plain string messages
        standardizedMessage = WebSocketUtils.event("LEGACY_MESSAGE", {
          content: message,
        });
      }
    } else {
      standardizedMessage = message;
    }

    // If channelId is provided, broadcast only to that specific channel
    if (channelId) {
      const serverChannelCombo = `${serverId}:${channelId}`;
      const channelSockets = this.topicSubscriptions.get(serverChannelCombo);

      if (!channelSockets || channelSockets.size === 0) {
        this.logger.debug(
          `No subscribers found for ${serverChannelCombo}, skipping broadcast`,
        );
        return;
      }

      this.logger.debug(
        `Broadcasting to channel ${channelId} in server ${serverId}`,
      );

      // Use WebSocketUtils.broadcast with filtering
      WebSocketUtils.broadcast(channelSockets, standardizedMessage, {
        excludeUserId: userId,
      });
      return;
    } else {
      this.logger.debug(`Broadcasting to all channels in server ${serverId}`);
    }

    // If no channelId is provided, broadcast to all channels in the server
    const serverSockets = new Set<ServerWebSocket<CustomWebSocketData>>();

    // Collect all sockets for the server
    for (const [topic, connections] of this.topicSubscriptions.entries()) {
      // Check if the topic starts with the serverId (format is "serverId:channelId")
      if (topic.startsWith(`${serverId}`)) {
        connections.forEach((ws) => {
          serverSockets.add(ws);
        });
      }
    }

    if (serverSockets.size > 0) {
      // Use WebSocketUtils.broadcast for standardized broadcasting
      WebSocketUtils.broadcast(serverSockets, standardizedMessage, {
        excludeUserId: userId,
      });

      this.logger.debug(
        `Broadcast message to ${serverSockets.size} connections across all channels in server ${serverId}`,
      );
    } else {
      this.logger.debug(`No connections found for server ${serverId}`);
    }
  }

  // Enhanced method to broadcast a message to a specific user using WebSocketUtils
  broadcastToUser(userId: string, message: string | IWebSocketMessage) {
    this.logger.debug(`Broadcasting message to user ${userId}`);

    if (!userId) {
      this.logger.error("Cannot broadcast: userId is undefined or null");
      return;
    }

    if (!message) {
      this.logger.error("Cannot broadcast: message is undefined or null");
      return;
    }

    // Convert string message to standardized format if needed
    let standardizedMessage: IWebSocketMessage;
    if (typeof message === "string") {
      try {
        // Try to parse as JSON first
        const parsed = JSON.parse(message);
        if (WebSocketUtils.validate(parsed)) {
          standardizedMessage = parsed;
        } else {
          // Create a standardized event message for legacy string messages
          standardizedMessage = WebSocketUtils.event("LEGACY_MESSAGE", {
            content: message,
          });
        }
      } catch {
        // Create a standardized event message for plain string messages
        standardizedMessage = WebSocketUtils.event("LEGACY_MESSAGE", {
          content: message,
        });
      }
    } else {
      standardizedMessage = message;
    }

    let found = false;

    // First check if the user has a private connection
    const privateConnection = this.privateConnections.get(userId);
    if (privateConnection && privateConnection.readyState === WebSocket.OPEN) {
      this.logger.debug(
        `Found user ${userId} in private connections, sending message`,
      );

      // Use WebSocketUtils for standardized sending
      WebSocketUtils.send(privateConnection, standardizedMessage);
      found = true;
    } else {
      // If no private connection, check all topic subscriptions
      for (const [topic, connections] of this.topicSubscriptions) {
        for (const ws of connections) {
          if (ws.data.userId === userId && ws.readyState === WebSocket.OPEN) {
            this.logger.debug(
              `Found user ${userId} in topic ${topic}, sending message`,
            );

            // Use WebSocketUtils for standardized sending
            WebSocketUtils.send(ws, standardizedMessage);
            found = true;
          }
        }
      }
    }

    if (!found) {
      this.logger.warn(
        `User ${userId} not found in any connection or not connected`,
      );
    }
  }

  /**
   * Send to a specific user
   * @param userId The ID of the user to send to
   * @param message The message to send (object or string)
   */
  sendToUser(userId: string, message: any) {
    const messageStr =
      typeof message === "string" ? message : JSON.stringify(message);
    this.broadcastToUser(userId, messageStr);
  }

  /**
   * Get all connected users in a server
   * @param serverId The ID of the server
   * @returns Array of user IDs
   */
  getConnectedUsersInServer(serverId: string): string[] {
    const connectedUsers = new Set<string>();

    // Check all clients
    for (const client of this.clients) {
      if (client.data.serverId === serverId && client.data.userId) {
        connectedUsers.add(client.data.userId);
      }
    }

    return Array.from(connectedUsers);
  }

  async createServer(
    ws: ServerWebSocket<CustomWebSocketData>,
    sender: string,
    data: ServerCreatePayload,
  ) {
    console.log("createServer called with data:", data);
    const { name, ownerId } = data;

    if (!name || !ownerId) {
      console.error("Invalid server creation data: missing name or ownerId");
      // Use WebSocketUtils for standardized error response
      const errorMessage = WebSocketUtils.validationError([
        { field: "name", message: "Server name is required", code: "REQUIRED" },
        { field: "ownerId", message: "Owner ID is required", code: "REQUIRED" },
      ]);
      WebSocketUtils.send(ws, errorMessage);
      return;
    }

    try {
      console.log(
        "Calling createServer DB function with name:",
        name,
        "ownerId:",
        ownerId,
      );
      const result = await createServer(db, name, ownerId);
      console.log("Server created successfully:", result);

      // Use WebSocketUtils for standardized success response
      const successMessage = WebSocketUtils.success(EventTypes.SERVER_CREATED, {
        serverId: result.id,
        serverName: result.name,
        ownerId: result.ownerId,
        createdAt: result.createdAt,
      });
      WebSocketUtils.send(ws, successMessage);

      console.log("Sending server creation success message to client");
    } catch (error: any) {
      console.error("Error creating server:", error);
      // Use WebSocketUtils for standardized error response
      const errorMessage = WebSocketUtils.internalError(
        `Failed to create server: ${error.message || "Unknown error"}`,
      );
      WebSocketUtils.send(ws, errorMessage);
    }
  }

  async joinServer(
    ws: ServerWebSocket<CustomWebSocketData>,
    _sender: string,
    data: ServerJoinPayload,
  ) {
    console.log("joinServer called with data:", data);
    const { id, userId } = data;
    const { serverId, channelId } = ws.data;

    if (!id || !userId) {
      console.error("Invalid server join data: missing id or userId");
      // Use WebSocketUtils for standardized error response
      const errorMessage = WebSocketUtils.validationError([
        { field: "id", message: "Server ID is required", code: "REQUIRED" },
        { field: "userId", message: "User ID is required", code: "REQUIRED" },
      ]);
      WebSocketUtils.send(ws, errorMessage);
      return;
    }

    try {
      console.log(`Adding user ${userId} to server ${id}...`);
      await addUserToServer(db, userId, id);
      console.log(`User ${userId} added to server ${id} successfully`);

      // Use WebSocketUtils for standardized success response
      const successMessage = WebSocketUtils.success("SERVER_JOINED", {
        serverId: id,
        userId: userId,
      });
      WebSocketUtils.send(ws, successMessage);

      // Also broadcast to other users in the server
      const joinEvent = WebSocketUtils.event("USER_JOINED_SERVER", {
        userId: userId,
        serverId: id,
        timestamp: new Date(),
      });
      this.broadcast(
        WebSocketUtils.serialize(joinEvent),
        serverId,
        channelId,
        userId,
      );

      // Update the WebSocket data with the new server ID
      ws.data.serverId = id;

      // Fetch and send comprehensive server details
      await this.sendServerDetailsToClient(ws, id);

      // Send server structure with visibility and access information
      const { userJoinServerHandler } = await import(
        "../handlers/websocket/serverStructureHandler"
      );
      await userJoinServerHandler(this, userId, id);
    } catch (error: any) {
      console.error("Error joining server:", error);
      if (error.message) {
        console.error("Error message:", error.message);
      }
      // Use WebSocketUtils for standardized error response
      const errorMessage = WebSocketUtils.internalError(
        `Failed to join server: ${error.message || "Unknown error"}`,
      );
      WebSocketUtils.send(ws, errorMessage);
    }
  }

  /**
   * Handle server view event
   * This is triggered when a user views a server they're already a member of
   *
   * @param ws The WebSocket connection
   * @param sender The user ID of the sender
   * @param data The server view data
   */
  async handleServerView(
    ws: ServerWebSocket<CustomWebSocketData>,
    sender: string,
    data: { serverId: string },
  ) {
    this.logger.debug("handleServerView called with data:", undefined, data);
    const { serverId } = data;

    if (!serverId) {
      this.logger.error("Invalid server view data: missing serverId");
      // Use WebSocketUtils for standardized error response
      const errorMessage = WebSocketUtils.validationError([
        {
          field: "serverId",
          message: "Server ID is required",
          code: "REQUIRED",
        },
      ]);
      WebSocketUtils.send(ws, errorMessage);
      return;
    }

    try {
      // Check if the user is a member of the server
      const { isUserServerMember } = await import("../db/utils");
      const isMember = await isUserServerMember(db, sender, serverId);

      if (!isMember) {
        this.logger.error(
          `User ${sender} is not a member of server ${serverId}`,
        );
        // Use WebSocketUtils for standardized error response
        const errorMessage = WebSocketUtils.permissionDenied("SERVER_ACCESS");
        WebSocketUtils.send(ws, errorMessage);
        return;
      }

      // Update the WebSocket data with the server ID
      ws.data.serverId = serverId;

      // Fetch and send comprehensive server details
      await this.sendServerDetailsToClient(ws, serverId);

      // Send server structure with visibility and access information
      const { userJoinServerHandler } = await import(
        "../handlers/websocket/serverStructureHandler"
      );
      await userJoinServerHandler(this, sender, serverId);

      this.logger.debug(
        `Server view handled successfully for user ${sender} and server ${serverId}`,
      );
    } catch (error: any) {
      this.logger.error("Error handling server view:", undefined, error);
      // Use WebSocketUtils for standardized error response
      const errorMessage = WebSocketUtils.internalError(
        `Failed to view server: ${error.message || "Unknown error"}`,
      );
      WebSocketUtils.send(ws, errorMessage);
    }
  }

  async createChannel(
    ws: ServerWebSocket<CustomWebSocketData>,
    sender: string,
    data: ChannelCreatePayload,
  ) {
    console.log("createChannel called with data:", data);

    try {
      const { serverId, name, ...details } = data;

      if (!serverId || !name) {
        console.error(
          "Invalid channel creation data: missing serverId or name",
        );
        // Use WebSocketUtils for standardized error response
        const errorMessage = WebSocketUtils.validationError([
          {
            field: "serverId",
            message: "Server ID is required",
            code: "REQUIRED",
          },
          {
            field: "name",
            message: "Channel name is required",
            code: "REQUIRED",
          },
        ]);
        WebSocketUtils.send(ws, errorMessage);
        return;
      }

      const hasPermission = await hasServerPermission(
        db,
        sender,
        serverId,
        MANAGE_CHANNELS,
      );
      if (!hasPermission) {
        console.error("User does not have permission to create channels");
        // Use WebSocketUtils for standardized error response
        const errorMessage = WebSocketUtils.permissionDenied("MANAGE_CHANNELS");
        WebSocketUtils.send(ws, errorMessage);
        return;
      }

      console.log(`Creating channel ${name} in server ${serverId}...`);
      const result = await createChannel(db, serverId, name, {
        description: details.description,
        type: details.type,
        isPublic: details.isPublic,
        allowedRoleIds: details.allowedRoleIds,
        parentId: details.categoryId || undefined,
        // Use any to bypass TypeScript checking for these properties
        ...(details.position !== undefined
          ? ({ position: details.position } as any)
          : {}),
      });
      console.log("Channel created successfully:", result);

      if (!result) throw new Error("Channel couldn't be created.");

      // Use WebSocketUtils for standardized success response
      const successMessage = WebSocketUtils.success(EventTypes.CHANNEL_CREATED, {
        channelId: result.id,
        channelName: result.name,
        channelType: result.type,
        serverId: result.serverId,
        categoryId: result.categoryId,
        position: result.position,
        description: result.description,
      });
      WebSocketUtils.send(ws, successMessage);

      // Also broadcast to other users in the server
      const channelEvent = WebSocketUtils.event(EventTypes.CHANNEL_CREATED, {
        channelId: result.id,
        channelName: result.name,
        channelType: result.type,
        serverId: result.serverId,
        categoryId: result.categoryId,
        position: result.position,
        description: result.description,
        createdBy: sender,
        timestamp: new Date(),
      });

      console.log("Broadcasting channel creation message...");
      this.broadcast(
        WebSocketUtils.serialize(channelEvent),
        serverId,
        undefined,
        sender,
      );
    } catch (error: any) {
      console.error("Error creating channel:", error);
      if (error.message) {
        console.error("Error message:", error.message);
      }
      // Use WebSocketUtils for standardized error response
      const errorMessage = WebSocketUtils.internalError(
        `Failed to create channel: ${error.message || "Unknown error"}`,
      );
      WebSocketUtils.send(ws, errorMessage);
    }
  }

  private sendLatestMessagesToNewUser(
    ws: ServerWebSocket<CustomWebSocketData>,
    sender: string,
    messages: any[],
  ) {
    console.log(`Sending ${messages.length} messages to user ${sender}`);

    if (!messages || !Array.isArray(messages)) {
      console.error("Invalid messages data:", messages);
      return;
    }

    try {
      // Format messages to match the expected frontend format
      const formattedMessages = messages.map((msg) => ({
        author: {
          username: msg.author.username,
          userId: msg.author.userId,
          avatar: msg.author.avatar || null,
        },
        message: {
          id: msg.message.id,
          content: msg.message.content,
          createdAt: msg.message.createdAt,
          editedAt: msg.message.editedAt,
          channelId: msg.message.channelId,
        },
      }));

      this.logger.debug("Formatted messages for frontend", undefined, {
        count: formattedMessages.length,
      });

      // Use WebSocketUtils for standardized success response
      const historyMessage = WebSocketUtils.success(
        "MESSAGE_HISTORY_RETRIEVED",
        {
          messages: formattedMessages,
          count: formattedMessages.length,
        },
      );
      WebSocketUtils.send(ws, historyMessage);

      this.logger.debug("Message history sent successfully");
    } catch (error) {
      this.logger.error("Error sending message history:", undefined, error);
    }
  }

  /**
   * Handle direct message history request
   * @param sender The user ID of the sender
   * @param data Object containing the other user's ID and optional limit
   */
  async handleDirectMessageHistory(
    ws: ServerWebSocket<CustomWebSocketData>,
    sender: string,
    data: { otherUserId: string; limit?: number },
  ) {
    try {
      this.logger.debug(
        `Retrieving direct message history between ${sender} and ${data.otherUserId}`,
      );

      if (!sender || !data.otherUserId) {
        this.logger.error("Missing user IDs for direct message history");
        // Use WebSocketUtils for standardized error response
        const errorMessage = WebSocketUtils.validationError([
          {
            field: "sender",
            message: "Sender user ID is required",
            code: "REQUIRED",
          },
          {
            field: "otherUserId",
            message: "Other user ID is required",
            code: "REQUIRED",
          },
        ]);
        WebSocketUtils.send(ws, errorMessage);
        return;
      }

      // Default limit to 50 if not provided
      const limit = data.limit || 50;

      // Retrieve direct messages between the two users
      const messages = await getDirectMessagesBetweenUsers(
        db,
        sender,
        data.otherUserId,
        limit,
      );
      this.logger.debug(
        `Retrieved ${messages.length} direct messages between users`,
      );

      // Format messages for the frontend
      const formattedMessages = messages.map((msg) => ({
        author: {
          id: msg.sender.id,
          username: msg.sender.username,
          avatar: msg.sender.avatar || null,
        },
        message: {
          id: msg.message.id,
          content: msg.message.content,
          senderId: msg.message.senderId,
          receiverId: msg.message.receiverId,
          createdAt: msg.message.createdAt,
          editedAt: msg.message.editedAt,
          readAt: msg.message.readAt,
          attachments: msg.message.attachments,
        },
      }));

      // Use WebSocketUtils for standardized success response
      const successMessage = WebSocketUtils.success(
        "DIRECT_MESSAGE_HISTORY_RETRIEVED",
        {
          messages: formattedMessages,
          otherUserId: data.otherUserId,
          count: formattedMessages.length,
        },
      );
      WebSocketUtils.send(ws, successMessage);

      this.logger.debug("Direct message history sent successfully");
    } catch (error) {
      this.logger.error(
        "Error retrieving direct message history:",
        undefined,
        error,
      );
      // Use WebSocketUtils for standardized error response
      const errorMessage = WebSocketUtils.internalError(
        `Failed to retrieve direct message history: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
      WebSocketUtils.send(ws, errorMessage);
    }
  }

  async handleFriendListRequest(
    ws: ServerWebSocket<CustomWebSocketData>,
    sender: string,
  ) {
    try {
      //const onlineFriends = await getOnlineFriends(db, sender);
      const allFriends = await getAllFriends(db, sender);

      // Use WebSocketUtils for standardized success response
      const successMessage = WebSocketUtils.success("FRIEND_LIST_RETRIEVED", {
        friends: allFriends,
        count: allFriends.length,
      });
      WebSocketUtils.send(ws, successMessage);
    } catch (error) {
      console.error("Error retrieving friend list:", error);
      // Use WebSocketUtils for standardized error response
      const errorMessage = WebSocketUtils.internalError(
        "Failed to retrieve friend list",
      );
      WebSocketUtils.send(ws, errorMessage);
    }
  }

  async handleFriendRequestsRequest(
    ws: ServerWebSocket<CustomWebSocketData>,
    sender: string,
  ) {
    try {
      const pendingRequests = await getPendingFriendRequests(db, sender);
      const sentRequests = await getSentFriendRequests(db, sender);

      // Use WebSocketUtils for standardized success response
      const successMessage = WebSocketUtils.success(
        "FRIEND_REQUESTS_RETRIEVED",
        {
          requests: [...pendingRequests, ...sentRequests],
          pendingCount: pendingRequests.length,
          sentCount: sentRequests.length,
          totalCount: pendingRequests.length + sentRequests.length,
        },
      );
      WebSocketUtils.send(ws, successMessage);
    } catch (error) {
      console.error("Error retrieving friend requests:", error);
      // Use WebSocketUtils for standardized error response
      const errorMessage = WebSocketUtils.internalError(
        "Failed to retrieve friend requests",
      );
      WebSocketUtils.send(ws, errorMessage);
    }
  }

  /**
   * Subscribe to a channel with correlation tracking
   * @param ws WebSocket connection
   * @param sender User ID of the sender
   * @param serverId Server ID
   * @param channelId Channel ID
   * @param correlationId Optional correlation ID for request tracking
   */
  async subscribeChannel(
    ws: ServerWebSocket<CustomWebSocketData>,
    sender: string,
    serverId: string,
    channelId: string,
    correlationId?: string,
  ) {
    console.log(
      `subscribeChannel called with sender: ${sender}, serverId: ${serverId}, channelId: ${channelId}`,
    );
    console.log("Current WebSocket data:", JSON.stringify(ws.data));

    if (!serverId) {
      console.error("Invalid channel subscription data: missing serverId");
      // Use WebSocketUtils for standardized error response with correlation tracking
      const errorMessage = WebSocketUtils.validationError(
        [
          {
            field: "serverId",
            message: "Server ID is required",
            code: "REQUIRED",
          },
        ],
        correlationId,
      );
      WebSocketUtils.send(ws, errorMessage);
      return;
    }

    // Update WebSocket data
    ws.data.serverId = serverId;
    ws.data.channelId = channelId;

    // Handle unsubscribing from previous channel if any
    const oldServerId = ws.data.serverId;
    const oldChannelId = ws.data.channelId;
    const serverChannelCombo = oldChannelId
      ? `${oldServerId}:${oldChannelId}`
      : oldServerId;

    if (this.topicSubscriptions.has(serverChannelCombo)) {
      console.log(
        `Unsubscribing user ${sender} from topic ${serverChannelCombo}`,
      );
      this.topicSubscriptions.get(serverChannelCombo)?.delete(ws);
      this.notifyChannelUsers(
        ws,
        serverChannelCombo,
        "user_channel_unsubscribe",
      ); // Notify remaining users about leaving user, using topic
      // Use WebSocketUtils for standardized event message
      const unsubscribeEvent = WebSocketUtils.event("CHANNEL_UNSUBSCRIBE", {
        userId: sender,
        timestamp: new Date(),
      });
      WebSocketUtils.send(ws, unsubscribeEvent);
    }

    // Subscribe to new channel
    const newServerChannelCombo = `${serverId}:${channelId}`;
    console.log(
      `Subscribing user ${sender} to channel ${newServerChannelCombo}`,
    );

    if (!this.topicSubscriptions.has(newServerChannelCombo)) {
      this.topicSubscriptions.set(
        newServerChannelCombo,
        new Set<ServerWebSocket<CustomWebSocketData>>(),
      );
    }

    this.topicSubscriptions.get(newServerChannelCombo)?.add(ws);

    if (!channelId) {
      return;
    }

    // Notify existing users about new joiner
    this.notifyChannelUsers(
      ws,
      newServerChannelCombo,
      "user_channel_subscribe",
    );

    // Send list of existing users to the new user
    //this.sendUserListToNewUser(ws, newServerChannelCombo, sender);

    // Send channel subscription confirmation using WebSocketUtils
    const subscriptionSuccess = WebSocketUtils.success("CHANNEL_SUBSCRIBED", {
      serverId,
      channelId,
      success: true,
    });
    WebSocketUtils.send(ws, subscriptionSuccess);

    // Retrieve and send message history
    try {
      console.log(`Retrieving last messages for channel ${channelId}...`);
      const messages = await retrieveLastNMessages(db, channelId, 50); // Get last 50 messages
      console.log(
        `Retrieved ${messages.length} messages for channel ${channelId}`,
      );

      // Send messages to the user
      this.sendLatestMessagesToNewUser(ws, sender, messages);

      // Also send comprehensive server details
      //await this.sendServerDetailsToClient(ws, serverId);
    } catch (error: any) {
      // Type assertion to access error.message
      console.error(
        `Error retrieving messages for channel ${channelId}:`,
        error,
      );
      // Use WebSocketUtils for standardized error response
      const errorMessage = WebSocketUtils.internalError(
        `Failed to retrieve message history: ${error?.message || "Unknown error"}`,
      );
      WebSocketUtils.send(ws, errorMessage);
    }
  }

  // Handler for MESSAGE_TYPING event (debounced)
  handleTyping = debounce(
    (sender: string, typingData: { isTyping: boolean }) => {
      this.logger.debug(`User ${sender} typing state: ${typingData.isTyping}`);
      // Use WebSocketUtils for standardized event broadcasting
      const typingEvent = WebSocketUtils.event("MESSAGE_TYPING_START", {
        isTyping: typingData.isTyping,
        userId: sender,
        timestamp: new Date(),
      });
      this.broadcast(WebSocketUtils.serialize(typingEvent));
    },
    500,
  );

  // Handler for MESSAGE_SEND event
  handleMessageSend(
    sender: string,
    data: {
      avatar: string;
      username: string;
      message: string;
      serverId: string;
      channelId: string;
    },
  ) {
    this.logger.debug("Handling message send", undefined, {
      sender,
      channelId: data.channelId,
    });

    createNewMessage(db, sender, data.channelId, data.message)
      .then((newMessage) => {
        // Use WebSocketUtils for standardized event broadcasting
        const messageEvent = WebSocketUtils.event("MESSAGE_SENT", {
          ...data,
          messageId: newMessage.id,
          timestamp: new Date(),
        });
        this.broadcast(
          WebSocketUtils.serialize(messageEvent),
          data.serverId,
          data.channelId,
        );
        this.logger.debug(
          "Message created and broadcast successfully",
          undefined,
          { messageId: newMessage.id },
        );
      })
      .catch((error) => {
        this.logger.error("Failed to create message:", undefined, error);
      });
  }

  // Handler for MESSAGE_UPDATE event
  async handleMessageUpdate(
    sender: string,
    data: {
      messageId: string;
      newContent: string;
      serverId: string;
      channelId: string;
    },
  ) {
    try {
      // Get the message to verify ownership
      const message = await getMessageById(db, data.messageId);

      if (!message) {
        this.logger.error(`Message ${data.messageId} not found`);
        return;
      }

      // Verify the sender is the message owner
      if (message.userId !== sender) {
        this.logger.error(
          `User ${sender} is not authorized to edit message ${data.messageId}`,
        );
        return;
      }

      // Update the message
      const updatedMessage = await editExistingMessage(
        db,
        data.messageId,
        data.newContent,
      );

      // Use WebSocketUtils for standardized event broadcasting
      const updateEvent = WebSocketUtils.event("MESSAGE_UPDATED", {
        messageId: data.messageId,
        content: data.newContent,
        editedAt: updatedMessage.editedAt,
        channelId: data.channelId,
        serverId: data.serverId,
        updatedBy: sender,
        timestamp: new Date(),
      });

      this.broadcast(
        WebSocketUtils.serialize(updateEvent),
        data.serverId,
        data.channelId,
      );

      this.logger.debug(
        "Message updated and broadcast successfully",
        undefined,
        { messageId: data.messageId },
      );
    } catch (error) {
      this.logger.error("Error updating message:", undefined, error);
    }
  }

  // Handler for MESSAGE_DELETE event
  async handleMessageDelete(
    sender: string,
    data: { messageId: string; serverId: string; channelId: string },
  ) {
    try {
      // Get the message to verify ownership
      const message = await getMessageById(db, data.messageId);

      if (!message) {
        this.logger.error(`Message ${data.messageId} not found`);
        return;
      }

      // Verify the sender is the message owner
      if (message.userId !== sender) {
        this.logger.error(
          `User ${sender} is not authorized to delete message ${data.messageId}`,
        );
        return;
      }

      // Delete the message
      await deleteMessage(db, data.messageId);

      // Use WebSocketUtils for standardized event broadcasting
      const deleteEvent = WebSocketUtils.event("MESSAGE_DELETED", {
        messageId: data.messageId,
        channelId: data.channelId,
        serverId: data.serverId,
        deletedBy: sender,
        timestamp: new Date(),
      });

      this.broadcast(
        WebSocketUtils.serialize(deleteEvent),
        data.serverId,
        data.channelId,
      );

      this.logger.debug(
        "Message deleted and broadcast successfully",
        undefined,
        { messageId: data.messageId },
      );
    } catch (error) {
      this.logger.error("Error deleting message:", undefined, error);
    }
  }

  // Handler for MESSAGE_REACTION_ADD event
  async handleMessageReactionAdd(
    sender: string,
    data: {
      messageId: string;
      emoji: string;
      serverId: string;
      channelId: string;
    },
  ) {
    try {
      // Get the message to verify it exists
      const message = await getMessageById(db, data.messageId);

      if (!message) {
        this.logger.error(`Message ${data.messageId} not found`);
        return;
      }

      // Add the reaction
      await addMessageReaction(db, data.messageId, sender, data.emoji);

      // Get updated reaction counts
      const reactionCounts = await getMessageReactionCounts(db, data.messageId);

      // Use WebSocketUtils for standardized event broadcasting
      const reactionEvent = WebSocketUtils.event("MESSAGE_REACTION_ADDED", {
        messageId: data.messageId,
        emoji: data.emoji,
        userId: sender,
        channelId: data.channelId,
        serverId: data.serverId,
        reactionCounts,
        timestamp: new Date(),
      });

      this.broadcast(
        WebSocketUtils.serialize(reactionEvent),
        data.serverId,
        data.channelId,
      );

      this.logger.debug(
        "Message reaction added and broadcast successfully",
        undefined,
        { messageId: data.messageId, emoji: data.emoji },
      );
    } catch (error) {
      this.logger.error("Error adding reaction:", undefined, error);
    }
  }

  // Handler for MESSAGE_REACTION_REMOVE event
  async handleMessageReactionRemove(
    sender: string,
    data: {
      messageId: string;
      emoji: string;
      serverId: string;
      channelId: string;
    },
  ) {
    try {
      // Remove the reaction
      await removeMessageReaction(db, data.messageId, sender, data.emoji);

      // Get updated reaction counts
      const reactionCounts = await getMessageReactionCounts(db, data.messageId);

      // Use WebSocketUtils for standardized event broadcasting
      const reactionEvent = WebSocketUtils.event("MESSAGE_REACTION_REMOVED", {
        messageId: data.messageId,
        emoji: data.emoji,
        userId: sender,
        channelId: data.channelId,
        serverId: data.serverId,
        reactionCounts,
        timestamp: new Date(),
      });

      this.broadcast(
        WebSocketUtils.serialize(reactionEvent),
        data.serverId,
        data.channelId,
      );

      this.logger.debug(
        "Message reaction removed and broadcast successfully",
        undefined,
        { messageId: data.messageId, emoji: data.emoji },
      );
    } catch (error) {
      console.error("Error removing reaction:", error);
    }
  }

  async handleMessageReactionUpdate(
    sender: string,
    data: {
      messageId: string;
      emoji: string;
      serverId: string;
      channelId: string;
    },
  ) {
    throw new Error("Method not implemented.");
  }

  handleAudio(sender: string, voiceData: string) {
    try {
      this.logger.debug("Processing audio data", undefined, {
        sender,
        dataSize: voiceData.length,
      });

      // Use WebSocketUtils for standardized event broadcasting
      const voiceEvent = WebSocketUtils.event("VOICE_DATA_SENT", {
        userId: sender,
        data: voiceData,
        dataSize: voiceData.length,
        timestamp: new Date(),
      });

      this.broadcast(
        WebSocketUtils.serialize(voiceEvent),
        undefined, // serverId - will be determined by broadcast method
        undefined, // channelId - will be determined by broadcast method
        sender, // exclude sender from receiving their own audio
      );

      this.logger.debug("Audio data broadcast completed", undefined, {
        sender,
        eventId: voiceEvent.meta.id,
      });
    } catch (error) {
      this.logger.error("Error handling audio data:", undefined, error);
    }
  }

  private notifyChannelUsers(
    ws: ServerWebSocket<CustomWebSocketData>,
    topic: string,
    eventType: "user_channel_subscribe" | "user_channel_unsubscribe",
  ) {
    const channelSockets = this.topicSubscriptions.get(topic);
    if (!channelSockets) {
      console.warn(
        `No sockets found for topic ${topic} to notify about user ${eventType}`,
      );
      return; // No users in the channel to notify
    }

    const notification = JSON.stringify({
      type:
        eventType === "user_channel_subscribe"
          ? EventTypes.CHANNEL_SUBSCRIBED
          : EventTypes.CHANNEL_UNSUBSCRIBED,
      userId: ws.data.userId, // User ID of the user who joined/left
      user: ws.data.user,
    });

    channelSockets.forEach((socket) => {
      if (socket.readyState === WebSocket.OPEN) {
        // Don't notify the user who just joined/left, notify others
        socket.send(notification);
      }
    });
  }

  private sendUserListToNewUser(
    ws: ServerWebSocket<CustomWebSocketData>,
    topic: string,
    newUserId: string,
  ) {
    const channelSockets = this.topicSubscriptions.get(topic);
    if (!channelSockets) {
      // Use WebSocketUtils for standardized success response
      const emptySubscribeMessage = WebSocketUtils.success(
        "CHANNEL_SUBSCRIBE",
        {
          userIds: [],
          count: 0,
        },
      );
      WebSocketUtils.send(ws, emptySubscribeMessage);
      return;
    }

    const userIds = Array.from(channelSockets)
      .filter((socket) => socket.data.userId !== newUserId) // Exclude the new user from the list
      .map((socket) => socket.data.userId);

    // Use WebSocketUtils for standardized success response
    const subscribeMessage = WebSocketUtils.success("CHANNEL_SUBSCRIBE", {
      userIds: userIds,
      count: userIds.length,
    });
    WebSocketUtils.send(ws, subscribeMessage);
  }

  getSocketsInChannel(
    topic: string,
  ): Set<ServerWebSocket<CustomWebSocketData>> | undefined {
    return this.topicSubscriptions.get(topic);
  }

  getSocketByUserIdAndTopic(
    userId: string,
    topic: string,
  ): ServerWebSocket<CustomWebSocketData> | undefined {
    const channelSockets = this.topicSubscriptions.get(topic);
    if (!channelSockets) return undefined;

    for (const ws of channelSockets) {
      if (ws.data.userId === userId) {
        return ws;
      }
    }
    return undefined;
  }

  // Method to handle a WebSocket connection is implemented above

  // Method to handle a WebSocket disconnection
  async handleDisconnection(ws: ServerWebSocket<CustomWebSocketData>) {
    const { userId, type } = ws.data;

    // Remove from appropriate collections
    this.clients.delete(ws);

    if (type === "private") {
      this.privateConnections.delete(userId);
    }

    this.logger.info(`WebSocket disconnected: ${userId}`);

    // Clean up device session
    if (userId && ws.data.deviceId) {
      const deviceSessions = this.deviceSessions.get(userId);
      if (deviceSessions) {
        deviceSessions.delete(ws.data.deviceId);
        // If no more devices, clean up the entire user entry
        if (deviceSessions.size === 0) {
          this.deviceSessions.delete(userId);
          // Don't delete connection context - keep it for potential reconnection
        }
      }
    }

    // If the user is authenticated, update their status to OFFLINE
    if (ws.data.isAuthenticated && userId) {
      await this.handleUserDisconnect(userId);
    }
  }

  // Method to handle a WebSocket connection
  async handleConnection(ws: ServerWebSocket<CustomWebSocketData>) {
    const { userId, type } = ws.data;

    // Add to appropriate collections
    this.clients.add(ws);

    if (type === "private") {
      this.privateConnections.set(userId, ws);
    }

    this.logger.info(
      `WebSocket connected: ${userId} (type: ${type || "channel"})`,
    );

    // If the user is authenticated, update their status to ONLINE
    if (ws.data.isAuthenticated && userId) {
      await this.handleUserConnect(userId);
    }
  }

  /**
   * Handle friend request events
   */
  async handleFriendRequest(sender: string, data: { friendId: string }) {
    // This is handled directly in the friend request handler
    // since it requires database operations
    console.log("handle friend request values:", sender, data);
    try {
      // Send the friend request
      const friendId = await getUserIdByUsername(db, data.friendId);
      const senderUser = await getUserByUserId(db, sender);
      if (friendId === senderUser.id) {
        throw new Error("Cannot send friend request to yourself");
      }
      if (!friendId || !senderUser) {
        throw new Error("User not found");
      }

      const friendship = await sendFriendRequest(db, sender, friendId);

      // If the friendship status is ACCEPTED, it means the request was auto-accepted
      // because the other user had already sent a request
      const isAutoAccepted = friendship.status === "ACCEPTED";

      // Broadcast the friend request via WebSocket
      //const eventType = isAutoAccepted
      //  ? EventTypes.FRIEND_ADDED
      //  : EventTypes.FRIEND_REQUEST_SENT;

      const senderFriendEvent = {
        type: isAutoAccepted
          ? EventTypes.FRIEND_ADDED
          : EventTypes.FRIEND_REQUEST_SENT,
        sender: sender,
        data: {
          friendship,
          requester: {
            id: sender,
            username: senderUser.username,
            avatar: senderUser.avatar,
          },
        },
      };

      const friendEvent = {
        type: isAutoAccepted
          ? EventTypes.FRIEND_ADDED
          : EventTypes.FRIEND_REQUEST_RECEIVED,
        sender: sender,
        data: {
          friendship,
          requester: {
            id: sender,
            username: senderUser.username,
            avatar: senderUser.avatar,
          },
        },
      };

      // Send to both users
      this.broadcastToUser(sender, JSON.stringify(friendEvent));
      this.broadcastToUser(friendId, JSON.stringify(friendEvent));
    } catch (error) {
      // Handle specific errors
      if (error instanceof Error) {
        console.error("Error removing reaction:", error);
      }
      throw error;
    }

    this.logger.debug(`Friend request from ${sender} to ${data.friendId}`);
  }

  /**
   * Handle friend request acceptance
   */
  async handleFriendRequestAccept(sender: string, data: { requestId: string }) {
    this.logger.debug(
      `Processing friend request acceptance: ${data.requestId} by ${sender}`,
    );

    try {
      // Accept the friend request in the database
      const friendship = await acceptFriendRequest(db, sender, data.requestId);

      // Create the event to broadcast
      const friendEvent = {
        type: EventTypes.FRIEND_REQUEST_ACCEPTED,
        sender: sender,
        data: {
          friendship,
        },
      };

      // Broadcast to both users involved in the friendship
      this.broadcastToUser(sender, JSON.stringify(friendEvent));
      this.broadcastToUser(friendship.userId, JSON.stringify(friendEvent));

      this.logger.debug(
        `Friend request ${data.requestId} accepted successfully`,
      );
    } catch (error) {
      this.logger.error(
        `Error accepting friend request ${data.requestId}:`,
        undefined,
        error,
      );

      // If the error is because the user doesn't have permission or the request doesn't exist
      if (error instanceof Error) {
        // We could send an error message back to the client here if needed
        this.logger.error(
          "Friend request acceptance error:",
          undefined,
          error.message,
        );
      }

      throw error; // Re-throw to allow the caller to handle it
    }
  }

  /**
   * Handle friend request rejection
   */
  async handleFriendRequestReject(sender: string, data: { requestId: string }) {
    this.logger.debug(
      `Processing friend request rejection: ${data.requestId} by ${sender}`,
    );

    try {
      // Get the friendship before rejecting it to know who sent it
      const friendship = await db
        .select()
        .from(FriendshipSchema)
        .where(eq(FriendshipSchema.id, data.requestId))
        .limit(1);

      if (friendship.length === 0) {
        throw new Error("Friend request not found");
      }

      const requestSenderId = friendship[0].userId;

      // Reject the friend request in the database
      await rejectFriendRequest(db, sender, data.requestId);

      // Create the event to broadcast
      const friendEvent = {
        type: EventTypes.FRIEND_REQUEST_REJECTED,
        sender: sender,
        data: {
          requestId: data.requestId,
        },
      };

      // Broadcast to both users involved
      this.broadcastToUser(sender, JSON.stringify(friendEvent));
      this.broadcastToUser(requestSenderId, JSON.stringify(friendEvent));

      this.logger.debug(
        `Friend request ${data.requestId} rejected successfully`,
      );
    } catch (error) {
      this.logger.error(
        `Error rejecting friend request ${data.requestId}:`,
        undefined,
        error,
      );

      if (error instanceof Error) {
        this.logger.error(
          "Friend request rejection error:",
          undefined,
          error.message,
        );
      }

      throw error; // Re-throw to allow the caller to handle it
    }
  }

  /**
   * Handle friend request cancellation
   */
  async handleFriendRequestCancel(sender: string, data: { requestId: string }) {
    this.logger.debug(
      `Processing friend request cancellation: ${data.requestId} by ${sender}`,
    );

    try {
      // Get the friendship before canceling it to know who it was sent to
      const friendship = await db
        .select()
        .from(FriendshipSchema)
        .where(eq(FriendshipSchema.id, data.requestId))
        .limit(1);

      if (friendship.length === 0) {
        throw new Error("Friend request not found");
      }

      const requestRecipientId = friendship[0].friendId;

      // Cancel the friend request in the database
      await cancelFriendRequest(db, sender, data.requestId);

      // Create the event to broadcast
      const friendEvent = {
        type: EventTypes.FRIEND_REQUEST_CANCELED,
        sender: sender,
        data: {
          requestId: data.requestId,
        },
      };

      // Broadcast to both users involved
      this.broadcastToUser(sender, JSON.stringify(friendEvent));
      this.broadcastToUser(requestRecipientId, JSON.stringify(friendEvent));

      this.logger.debug(
        `Friend request ${data.requestId} cancelled successfully`,
      );
    } catch (error) {
      this.logger.error(
        `Error cancelling friend request ${data.requestId}:`,
        undefined,
        error,
      );

      if (error instanceof Error) {
        this.logger.error(
          "Friend request cancellation error:",
          undefined,
          error.message,
        );
      }

      throw error; // Re-throw to allow the caller to handle it
    }
  }

  /**
   * Handle friend removal
   */
  handleFriendRemove(sender: string, data: { friendshipId: string }) {
    // This is handled directly in the friend removal handler
    // since it requires database operations
    console.log(`Friend ${data.friendshipId} removed by ${sender}`);
  }

  /**
   * Handle user blocking
   */
  handleUserBlock(sender: string, data: { targetUserId: string }) {
    // This is handled directly in the user blocking handler
    // since it requires database operations
    console.log(`User ${data.targetUserId} blocked by ${sender}`);
  }

  /**
   * Handle user unblocking
   */
  handleUserUnblock(sender: string, data: { friendshipId: string }) {
    // This is handled directly in the user unblocking handler
    // since it requires database operations
    console.log(
      `User unblocked via friendship ${data.friendshipId} by ${sender}`,
    );
  }

  /**
   * Handle channel update
   */
  async handleChannelUpdate(
    sender: string,
    data: {
      channelId: string;
      name?: string;
      description?: string;
      type?: string;
      serverId: string;
      categoryId?: string;
      position?: number;
    },
  ) {
    try {
      this.logger.debug(`Channel ${data.channelId} updated by ${sender}`);

      // Check if the user has permission to manage channels
      const hasPermission = await hasServerPermission(
        db,
        sender,
        data.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        this.logger.error(
          `User ${sender} does not have permission to update channels in server ${data.serverId}`,
        );
        return;
      }

      // Use WebSocketUtils for standardized event broadcasting
      const updateEvent = WebSocketUtils.event("CHANNEL_UPDATED", {
        channelId: data.channelId,
        name: data.name,
        description: data.description,
        type: data.type,
        serverId: data.serverId,
        categoryId: data.categoryId,
        position: data.position,
        updatedBy: sender,
        timestamp: new Date(),
      });

      this.broadcast(WebSocketUtils.serialize(updateEvent), data.serverId);

      this.logger.debug("Channel update broadcast successfully", undefined, {
        channelId: data.channelId,
      });
    } catch (error) {
      this.logger.error("Error handling channel update:", undefined, error);
    }
  }

  /**
   * Handle channel deletion
   */
  async handleChannelDelete(
    sender: string,
    data: { channelId: string; serverId: string },
  ) {
    try {
      this.logger.debug(`Channel ${data.channelId} deleted by ${sender}`);

      // Check if the user has permission to manage channels
      const hasPermission = await hasServerPermission(
        db,
        sender,
        data.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        this.logger.error(
          `User ${sender} does not have permission to delete channels in server ${data.serverId}`,
        );
        return;
      }

      // Use WebSocketUtils for standardized event broadcasting
      const deleteEvent = WebSocketUtils.event("CHANNEL_DELETED", {
        channelId: data.channelId,
        serverId: data.serverId,
        deletedBy: sender,
        timestamp: new Date(),
      });

      this.broadcast(WebSocketUtils.serialize(deleteEvent), data.serverId);

      this.logger.debug("Channel deletion broadcast successfully", undefined, {
        channelId: data.channelId,
      });
    } catch (error) {
      this.logger.error("Error handling channel deletion:", undefined, error);
    }
  }

  /**
   * Handle channels reordering
   * This method processes the CHANNELS_REORDER event from the client and updates channel positions
   *
   * @param sender - The ID of the user who triggered the reordering
   * @param data - Object containing serverId and channelPositions array
   */
  async handleChannelsReorder(
    sender: string,
    data: {
      serverId: string;
      channelPositions: Array<{ channelId: string; position: number }>;
    },
  ) {
    try {
      if (!data.serverId) {
        console.error("Cannot handle channel reordering: serverId is missing");
        return false;
      }

      if (
        !data.channelPositions ||
        !Array.isArray(data.channelPositions) ||
        data.channelPositions.length === 0
      ) {
        console.error(
          "Cannot handle channel reordering: channelPositions array is invalid or empty",
        );
        return false;
      }

      console.log(
        `Processing channel reordering for server ${data.serverId} by user ${sender}`,
      );

      // Check if the user has permission to manage channels
      const { hasServerPermission } = await import("../utils/permissions");
      const { MANAGE_CHANNELS } = await import("../constants/permissions");

      const hasPermission = await hasServerPermission(
        db,
        sender,
        data.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        console.error(
          `User ${sender} does not have permission to reorder channels in server ${data.serverId}`,
        );
        return false;
      }

      // Update channel positions in the database
      const { updateChannelPositions } = await import(
        "../db/utils/bulk-operations"
      );
      const success = await updateChannelPositions(data.channelPositions);

      if (!success) {
        console.error(
          `Failed to update channel positions for server ${data.serverId}`,
        );
        return false;
      }

      // Broadcast the update to all server members using WebSocketUtils
      const updateEvent = WebSocketUtils.event("CHANNELS_REORDERED", {
        serverId: data.serverId,
        channelPositions: data.channelPositions,
        reorderedBy: sender,
        timestamp: new Date(),
      });

      this.broadcast(WebSocketUtils.serialize(updateEvent), data.serverId);

      console.log(
        `Channel positions updated and broadcast for server ${data.serverId}`,
      );
      return true;
    } catch (error) {
      console.error(
        `Error handling channel reordering for server ${data.serverId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Handle category creation via WebSocket
   * @param ws The WebSocket connection
   * @param sender The user ID of the sender
   * @param data The category creation data
   */
  async handleCategoryCreate(
    ws: ServerWebSocket<CustomWebSocketData>,
    sender: string,
    data: {
      serverId: string;
      name: string;
      description?: string;
      position?: number;
    },
  ) {
    this.logger.debug(
      "handleCategoryCreate called with data:",
      undefined,
      data,
    );

    try {
      const { serverId, name, description, position } = data;

      if (!serverId || !name) {
        this.logger.error(
          "Invalid category creation data: missing serverId or name",
        );
        // Use WebSocketUtils for standardized error response
        const errorMessage = WebSocketUtils.validationError([
          {
            field: "serverId",
            message: "Server ID is required",
            code: "REQUIRED",
          },
          {
            field: "name",
            message: "Category name is required",
            code: "REQUIRED",
          },
        ]);
        WebSocketUtils.send(ws, errorMessage);
        return;
      }

      // Check if the user has permission to create categories
      const hasPermission = await hasServerPermission(
        db,
        sender,
        serverId,
        MANAGE_CHANNELS,
      );
      if (!hasPermission) {
        this.logger.error(
          `User ${sender} does not have permission to create categories in server ${serverId}`,
        );
        // Use WebSocketUtils for standardized error response
        const errorMessage = WebSocketUtils.permissionDenied("MANAGE_CHANNELS");
        WebSocketUtils.send(ws, errorMessage);
        return;
      }

      this.logger.debug(`Creating category ${name} in server ${serverId}...`);

      // Create the category using the utility function
      const result = await createChannelCategory(
        db,
        serverId,
        name,
        description,
        position,
      );

      if (!result) {
        throw new Error("Category couldn't be created.");
      }

      this.logger.debug("Category created successfully:", undefined, result);

      // Broadcast the category creation event to all users in the server using WebSocketUtils
      const categoryEvent = WebSocketUtils.event("CATEGORY_CREATED", {
        categoryId: result.id,
        categoryName: result.name,
        serverId: result.serverId,
        description: result.description,
        position: result.position,
        createdBy: sender,
        timestamp: new Date(),
      });

      this.logger.debug("Broadcasting category creation message...");
      this.broadcast(WebSocketUtils.serialize(categoryEvent), serverId);

      // Update server structure for all users
      const { serverStructureChangedHandler } = await import(
        "../handlers/websocket/serverStructureHandler"
      );
      await serverStructureChangedHandler(this, serverId);
    } catch (error: any) {
      this.logger.error("Error creating category:", undefined, error);
      // Use WebSocketUtils for standardized error response
      const errorMessage = WebSocketUtils.internalError(
        `Failed to create category: ${error.message || "Unknown error"}`,
      );
      WebSocketUtils.send(ws, errorMessage);
    }
  }

  /**
   * Handle category update
   */
  handleCategoryUpdate(
    sender: string,
    data: {
      categoryId: string;
      name?: string;
      description?: string;
      serverId: string;
      position?: number;
    },
  ) {
    // This is handled directly in the category update handler
    // since it requires database operations
    console.log(`Category ${data.categoryId} updated by ${sender}`);
  }

  /**
   * Handle category deletion
   */
  handleCategoryDelete(
    sender: string,
    data: { categoryId: string; serverId: string },
  ) {
    // This is handled directly in the category delete handler
    // since it requires database operations
    console.log(`Category ${data.categoryId} deleted by ${sender}`);
  }

  /**
   * Handle categories reordering
   * This method processes the CATEGORIES_REORDER event from the client and updates category positions
   *
   * @param sender - The ID of the user who triggered the reordering
   * @param data - Object containing serverId and categoryPositions array
   */
  async handleCategoriesReorder(
    sender: string,
    data: {
      serverId: string;
      categoryPositions: Array<{ categoryId: string; position: number }>;
    },
  ) {
    try {
      if (!data.serverId) {
        console.error("Cannot handle category reordering: serverId is missing");
        return false;
      }

      if (
        !data.categoryPositions ||
        !Array.isArray(data.categoryPositions) ||
        data.categoryPositions.length === 0
      ) {
        console.error(
          "Cannot handle category reordering: categoryPositions array is invalid or empty",
        );
        return false;
      }

      console.log(
        `Processing category reordering for server ${data.serverId} by user ${sender}`,
      );

      // Check if the user has permission to manage channels
      const { hasServerPermission } = await import("../utils/permissions");
      const { MANAGE_CHANNELS } = await import("../constants/permissions");

      const hasPermission = await hasServerPermission(
        db,
        sender,
        data.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        console.error(
          `User ${sender} does not have permission to reorder categories in server ${data.serverId}`,
        );
        return false;
      }

      // Update category positions in the database
      const { updateCategoryPositions } = await import(
        "../db/utils/bulk-operations"
      );
      const success = await updateCategoryPositions(data.categoryPositions);

      if (!success) {
        console.error(
          `Failed to update category positions for server ${data.serverId}`,
        );
        return false;
      }

      // Broadcast the update to all server members using WebSocketUtils
      const updateEvent = WebSocketUtils.event("CATEGORIES_REORDERED", {
        serverId: data.serverId,
        categoryPositions: data.categoryPositions,
        reorderedBy: sender,
        timestamp: new Date(),
      });

      this.broadcast(WebSocketUtils.serialize(updateEvent), data.serverId);

      console.log(
        `Category positions updated and broadcast for server ${data.serverId}`,
      );
      return true;
    } catch (error) {
      console.error(
        `Error handling category reordering for server ${data.serverId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Handle server restructure
   * This method processes a complete server structure update from the client
   * and updates all channel and category positions and relationships
   *
   * @param ws - The WebSocket connection
   * @param sender - The ID of the user who triggered the restructure
   * @param data - Object containing serverId and structure array
   */
  async handleServerRestructure(
    ws: ServerWebSocket<CustomWebSocketData>,
    sender: string,
    data: {
      serverId: string;
      structure: Array<{
        id: string;
        type: "channel" | "category";
        position: number;
        name: string;
        parentId?: string | null;
      }>;
    },
  ) {
    this.logger.debug(
      "handleServerRestructure called with data:",
      undefined,
      data,
    );

    try {
      if (!data.serverId) {
        this.logger.error(
          "Cannot handle server restructure: serverId is missing",
        );
        // Use WebSocketUtils for standardized error response
        const errorMessage = WebSocketUtils.validationError([
          {
            field: "serverId",
            message: "Server ID is required",
            code: "REQUIRED",
          },
        ]);
        WebSocketUtils.send(ws, errorMessage);
        return false;
      }

      if (
        !data.structure ||
        !Array.isArray(data.structure) ||
        data.structure.length === 0
      ) {
        this.logger.error(
          "Cannot handle server restructure: structure array is invalid or empty",
        );
        // Use WebSocketUtils for standardized error response
        const errorMessage = WebSocketUtils.validationError([
          {
            field: "structure",
            message: "Structure array is required and cannot be empty",
            code: "REQUIRED",
          },
        ]);
        WebSocketUtils.send(ws, errorMessage);
        return false;
      }

      this.logger.debug(
        `Processing server restructure for server ${data.serverId} by user ${sender}`,
      );

      // Check if the user has permission to manage channels
      const hasPermission = await hasServerPermission(
        db,
        sender,
        data.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        this.logger.error(
          `User ${sender} does not have permission to restructure server ${data.serverId}`,
        );
        // Use WebSocketUtils for standardized error response
        const errorMessage = WebSocketUtils.permissionDenied("MANAGE_CHANNELS");
        WebSocketUtils.send(ws, errorMessage);
        return false;
      }

      // Update server structure in the database
      const { updateServerStructure } = await import(
        "../db/utils/bulk-operations"
      );
      const success = await updateServerStructure(
        data.serverId,
        data.structure,
      );

      if (!success) {
        this.logger.error(
          `Failed to update server structure for server ${data.serverId}`,
        );
        // Use WebSocketUtils for standardized error response
        const errorMessage = WebSocketUtils.internalError(
          "Failed to update server structure",
        );
        WebSocketUtils.send(ws, errorMessage);
        return false;
      }

      // Broadcast the updated server structure to all server members
      const { serverStructureChangedHandler } = await import(
        "../handlers/websocket/serverStructureHandler"
      );
      await serverStructureChangedHandler(this, data.serverId);

      this.logger.debug(
        `Server structure updated and broadcast for server ${data.serverId}`,
      );

      // Send success response to the client
      ws.send(
        JSON.stringify({
          type: "SERVER_RESTRUCTURE_SUCCESS",
          sender: "system",
          data: {
            serverId: data.serverId,
            message: "Server structure updated successfully",
          },
        }),
      );

      return true;
    } catch (error: any) {
      this.logger.error(
        `Error handling server restructure for server ${data.serverId}:`,
        undefined,
        error,
      );
      // Use WebSocketUtils for standardized error response
      const errorMessage = WebSocketUtils.internalError(
        `Failed to update server structure: ${error.message || "Unknown error"}`,
      );
      WebSocketUtils.send(ws, errorMessage);
      return false;
    }
  }

  /**
   * Handle channels move to category
   * This method processes the CHANNELS_MOVE event from the client and moves channels to a category
   *
   * @param sender - The ID of the user who triggered the move
   * @param data - Object containing serverId, channelIds, and categoryId
   */
  async handleChannelsMove(
    sender: string,
    data: {
      serverId: string;
      channelIds: string[];
      categoryId: string | null;
    },
  ) {
    try {
      if (!data.serverId) {
        console.error("Cannot handle channel move: serverId is missing");
        return false;
      }

      if (
        !data.channelIds ||
        !Array.isArray(data.channelIds) ||
        data.channelIds.length === 0
      ) {
        console.error(
          "Cannot handle channel move: channelIds array is invalid or empty",
        );
        return false;
      }

      console.log(
        `Processing channel move for server ${data.serverId} by user ${sender}`,
      );

      // Check if the user has permission to manage channels
      const { hasServerPermission } = await import("../utils/permissions");
      const { MANAGE_CHANNELS } = await import("../constants/permissions");

      const hasPermission = await hasServerPermission(
        db,
        sender,
        data.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        console.error(
          `User ${sender} does not have permission to move channels in server ${data.serverId}`,
        );
        return false;
      }

      // Move channels to category in the database
      const { moveChannelsToCategory } = await import(
        "../db/utils/bulk-operations"
      );
      const success = await moveChannelsToCategory(
        data.channelIds,
        data.categoryId,
      );

      if (!success) {
        console.error(`Failed to move channels for server ${data.serverId}`);
        return false;
      }

      // Broadcast the update to all server members using WebSocketUtils
      const moveEvent = WebSocketUtils.event("CHANNELS_MOVED", {
        serverId: data.serverId,
        channelIds: data.channelIds,
        categoryId: data.categoryId,
        movedBy: sender,
        timestamp: new Date(),
      });

      this.broadcast(WebSocketUtils.serialize(moveEvent), data.serverId);

      console.log(`Channels moved and broadcast for server ${data.serverId}`);
      return true;
    } catch (error) {
      console.error(
        `Error handling channel move for server ${data.serverId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Handle server members update
   * Broadcasts server members update to all members in the server
   *
   * @param sender - The ID of the user who triggered the update
   * @param data - Object containing serverId and members array
   */
  handleServerMembersUpdate(
    sender: string,
    data: { serverId: string; members: any[] },
  ) {
    try {
      if (!data.serverId) {
        console.error(
          "Cannot broadcast server members update: serverId is missing",
        );
        return false;
      }

      if (!data.members || !Array.isArray(data.members)) {
        console.error(
          "Cannot broadcast server members update: members array is invalid",
        );
        return false;
      }

      console.log(
        `Broadcasting server members update for server ${data.serverId}`,
      );

      // Create the update event using WebSocketUtils
      const membersEvent = WebSocketUtils.event("SERVER_MEMBERS_UPDATED", {
        serverId: data.serverId,
        members: data.members,
        updatedBy: sender,
        timestamp: new Date(),
      });

      // Broadcast to all members in all channels of the server
      this.broadcast(WebSocketUtils.serialize(membersEvent), data.serverId);

      console.log(
        `Broadcast server members update to all members in server ${data.serverId}`,
      );
      return true;
    } catch (error) {
      console.error(
        `Error broadcasting server members update for server ${data.serverId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Broadcast server update to all members of a server
   * This method sends a server update event to all members of a server, not just those in a specific channel
   *
   * @param serverId - The ID of the server that was updated
   * @param updatedServer - The updated server object
   * @param sender - The ID of the user who made the update
   */
  async broadcastServerUpdate(
    serverId: string,
    updatedServer: any,
    sender: string = "system",
  ) {
    try {
      console.log(`Broadcasting server update for server ${serverId}`);

      // Create the update event
      const serverUpdateEvent = {
        type: EventTypes.SERVER_UPDATED,
        sender,
        data: {
          server_id: serverId,
          server: updatedServer,
        },
      };

      const eventJson = JSON.stringify(serverUpdateEvent);

      // Broadcast to all members in all channels of the server
      // The enhanced broadcast method will handle sending to all channels
      this.broadcast(eventJson, serverId);

      // Also broadcast updated server structure with visibility and access information
      const { serverStructureChangedHandler } = await import(
        "../handlers/websocket/serverStructureHandler"
      );
      await serverStructureChangedHandler(this, serverId);

      console.log(
        `Broadcast server update to all members in server ${serverId}`,
      );
      return true;
    } catch (error) {
      console.error(
        `Error broadcasting server update for server ${serverId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Send server members to a client
   * This is a utility method to fetch and send server members to a client
   */
  async sendServerMembersToClient(
    ws: ServerWebSocket<CustomWebSocketData>,
    serverId: string,
  ) {
    try {
      const { getServerMembers } = await import("../utils/serverMembers");
      const members = await getServerMembers(db, serverId);

      // Send the server members list to the client
      ws.send(
        JSON.stringify({
          type: EventTypes.SERVER_MEMBERS_UPDATED,
          sender: "system",
          data: {
            server_id: serverId,
            members: members,
          },
        }),
      );

      console.log(
        `Sent server members list to user ${ws.data.userId} for server ${serverId}`,
      );
      return true;
    } catch (error) {
      console.error(
        `Error fetching server members for server ${serverId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Helper method to broadcast user status updates to server members
   * @param userId - The ID of the user whose status changed
   * @param statusData - The updated status data to broadcast
   */
  async broadcastStatusToServerMembers(userId: string, statusData: any) {
    try {
      this.logger.debug(
        `Broadcasting status update to server members for user ${userId}`,
      );

      // Get the servers the user is a member of
      const { getUserServers } = await import("../db/utils");
      const userServers = await getUserServers(db, userId);

      if (
        !userServers ||
        !Array.isArray(userServers) ||
        userServers.length === 0
      ) {
        this.logger.debug(`User ${userId} is not a member of any servers`);
        return;
      }

      this.logger.debug(
        `User ${userId} is a member of ${userServers.length} servers`,
      );

      // Create the status update event
      const statusEvent = {
        type: EventTypes.USER_STATUS_UPDATED,
        sender: userId,
        data: statusData,
      };

      const statusEventJson = JSON.stringify(statusEvent);

      // Broadcast to all servers the user is a member of
      for (const server of userServers) {
        if (server && server.id) {
          this.logger.debug(
            `Broadcasting status update to server ${server.id}`,
          );
          this.broadcast(statusEventJson, server.id);
        }
      }

      this.logger.debug(
        `Status update broadcast to all server members completed`,
      );
    } catch (error) {
      this.logger.error(
        `Error broadcasting status to server members:`,
        undefined,
        error,
      );
    }
  }

  /**
   * Handle user status update
   */
  async handleUserStatusUpdate(
    sender: string,
    data: {
      status: "ONLINE" | "AWAY" | "BUSY" | "INVISIBLE" | "OFFLINE";
      statusMessage?: string;
    },
  ) {
    try {
      console.log(`Handling user status update for userId: ${sender}`);

      if (!sender) {
        console.error("Cannot update status: userId is undefined or null");
        return;
      }

      // Update the user's status in the database
      // This is a manual status change, so update the preferred status as well (updatePreferred=true)
      console.log("Updating user status in database...");
      const updatedUser = await updateUserStatus(
        db,
        sender,
        data.status,
        data.statusMessage,
        true,
      );
      console.log("User status updated successfully:", updatedUser);

      // Get user badges for presence event
      const userBadges = await getUserBadgesForWebSocket(db, sender);

      // Create status data object for broadcasting
      const statusData = {
        userId: updatedUser.id,
        username: updatedUser.username,
        status: updatedUser.status,
        statusMessage: updatedUser.statusMessage,
        lastActive: updatedUser.lastActive,
        avatar: updatedUser.avatar,
        badges: userBadges,
      };

      // Get the user's friends to broadcast status update
      console.log("Getting online friends...");
      const friends = await getOnlineFriends(db, sender);
      console.log(`Found ${friends.length} online friends`);

      // Check if friends array is valid
      if (!Array.isArray(friends)) {
        console.error("getOnlineFriends did not return an array");
        return;
      }

      // Extract friend IDs safely
      const friendIds = [];
      for (const friend of friends) {
        if (friend && friend.friend && friend.friend.id) {
          friendIds.push(friend.friend.id);
        }
      }

      console.log(`Extracted ${friendIds.length} valid friend IDs`);

      // Broadcast status update to friends
      const statusEvent = {
        type: EventTypes.USER_STATUS_UPDATED,
        sender: sender,
        data: statusData,
      };

      // Send to all online friends
      const statusEventJson = JSON.stringify(statusEvent);
      console.log("Broadcasting status update to friends...");

      for (const friendId of friendIds) {
        console.log(`Broadcasting to friend: ${friendId}`);
        this.broadcastToUser(friendId, statusEventJson);
      }

      console.log("Status update broadcast to all friends");

      // Broadcast status update to server members
      await this.broadcastStatusToServerMembers(sender, statusData);

      return true;
    } catch (error) {
      console.error("Error handling user status update:", error);
      return false;
    }
  }

  /**
   * Send comprehensive server details to a client
   * This includes server info, owner, members with roles, server roles, channels, and categories
   */
  async sendServerDetailsToClient(
    ws: ServerWebSocket<CustomWebSocketData>,
    serverId: string,
  ) {
    try {
      const { getServerDetails } = await import("../db/utils");
      const serverDetails = await getServerDetails(db, serverId);

      if (!serverDetails) {
        console.error(`Server ${serverId} not found`);
        // Use WebSocketUtils for standardized error response
        const errorMessage = WebSocketUtils.serverNotFound(serverId);
        WebSocketUtils.send(ws, errorMessage);
        return false;
      }

      // Send the comprehensive server details to the client using WebSocketUtils
      const serverDetailsMessage = WebSocketUtils.success(
        "SERVER_DETAILS_UPDATED",
        {
          serverId: serverId,
          server: serverDetails.server,
          owner: serverDetails.owner,
          members: serverDetails.members,
          roles: serverDetails.roles,
          channels: serverDetails.channels,
          categories: serverDetails.categories,
        },
      );
      WebSocketUtils.send(ws, serverDetailsMessage);

      console.log(
        `Sent comprehensive server details to user ${ws.data.userId} for server ${serverId}`,
      );
      return true;
    } catch (error) {
      console.error(
        `Error fetching server details for server ${serverId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Handle user connection (set status to preferred status)
   */
  async handleUserConnect(userId: string) {
    console.log(`Handling user connect for userId: ${userId}`);

    if (!userId) {
      console.error("Cannot update status: userId is undefined or null");
      return;
    }

    try {
      // Get the user's preferred status
      console.log("Getting user preferred status...");
      const user = await db
        .select({
          preferredStatus: UserSchema.preferredStatus,
        })
        .from(UserSchema)
        .where(eq(UserSchema.id, userId))
        .limit(1);

      // Use the preferred status or default to ONLINE if not found
      const preferredStatus =
        user.length > 0 ? user[0].preferredStatus : "ONLINE";

      // Make sure we have a valid status (not null)
      const statusToSet = preferredStatus || "ONLINE";
      console.log(`User ${userId} preferred status: ${statusToSet}`);

      // Update the user's status to their preferred status in the database
      // Set updatePreferred to false to avoid overwriting the preferred status
      console.log(`Updating user status to ${statusToSet}...`);
      const updatedUser = await updateUserStatus(
        db,
        userId,
        statusToSet,
        undefined,
        false,
      );
      console.log("User status updated successfully:", updatedUser);

      // Get user badges for presence event
      const userBadges = await getUserBadgesForWebSocket(db, userId);

      // Get the user's friends to broadcast status update
      console.log("Getting online friends...");
      const friends = await getOnlineFriends(db, userId);
      console.log(`Found ${friends.length} online friends`);

      // Check if friends array is valid
      if (!Array.isArray(friends)) {
        console.error("getOnlineFriends did not return an array");
        return;
      }

      // Extract friend IDs safely
      const friendIds = [];
      for (const friend of friends) {
        if (friend && friend.friend && friend.friend.id) {
          friendIds.push(friend.friend.id);
        }
      }

      console.log(`Extracted ${friendIds.length} valid friend IDs`);

      // Create status data object for broadcasting
      const statusData = {
        userId: updatedUser.id,
        username: updatedUser.username,
        status: updatedUser.status,
        statusMessage: updatedUser.statusMessage,
        lastActive: updatedUser.lastActive,
        avatar: updatedUser.avatar,
        badges: userBadges,
      };

      // Broadcast status update to friends
      const statusEvent = {
        type: EventTypes.USER_STATUS_UPDATED,
        sender: userId,
        data: statusData,
      };

      // Send to all online friends
      const statusEventJson = JSON.stringify(statusEvent);
      console.log("Broadcasting status update to friends...");

      for (const friendId of friendIds) {
        console.log(`Broadcasting to friend: ${friendId}`);
        this.broadcastToUser(friendId, statusEventJson);
      }

      // Broadcast status update to server members
      await this.broadcastStatusToServerMembers(userId, statusData);

      console.log(`User ${userId} connected and set to ${statusToSet}`);
    } catch (error) {
      console.error(`Error setting user ${userId} to ONLINE:`, error);
    }
  }

  /**
   * Handle user disconnection (set status to OFFLINE)
   */
  async handleUserDisconnect(userId: string) {
    console.log(`Handling user disconnect for userId: ${userId}`);

    if (!userId) {
      console.error("Cannot update status: userId is undefined or null");
      return;
    }

    try {
      // Update the user's status to OFFLINE in the database
      // Set updatePreferred to false to avoid overwriting the preferred status
      console.log("Updating user status to OFFLINE...");
      const updatedUser = await updateUserStatus(
        db,
        userId,
        "OFFLINE",
        undefined,
        false,
      );
      console.log("User status updated successfully:", updatedUser);

      // Get user badges for presence event
      const userBadges = await getUserBadgesForWebSocket(db, userId);

      // Get the user's friends to broadcast status update
      console.log("Getting online friends...");
      const friends = await getOnlineFriends(db, userId);
      console.log(`Found ${friends.length} online friends`);

      // Check if friends array is valid
      if (!Array.isArray(friends)) {
        console.error("getOnlineFriends did not return an array");
        return;
      }

      // Extract friend IDs safely
      const friendIds = [];
      for (const friend of friends) {
        if (friend && friend.friend && friend.friend.id) {
          friendIds.push(friend.friend.id);
        }
      }

      console.log(`Extracted ${friendIds.length} valid friend IDs`);

      // Create status data object for broadcasting
      const statusData = {
        userId: updatedUser.id,
        username: updatedUser.username,
        status: updatedUser.status,
        statusMessage: updatedUser.statusMessage,
        lastActive: updatedUser.lastActive,
        avatar: updatedUser.avatar,
        badges: userBadges,
      };

      // Broadcast status update to friends
      const statusEvent = {
        type: EventTypes.USER_STATUS_UPDATED,
        sender: userId,
        data: statusData,
      };

      // Send to all online friends
      const statusEventJson = JSON.stringify(statusEvent);
      console.log("Broadcasting status update to friends...");

      for (const friendId of friendIds) {
        console.log(`Broadcasting to friend: ${friendId}`);
        this.broadcastToUser(friendId, statusEventJson);
      }

      // Broadcast status update to server members
      await this.broadcastStatusToServerMembers(userId, statusData);

      console.log(`User ${userId} disconnected and set to OFFLINE`);
    } catch (error) {
      console.error(`Error setting user ${userId} to OFFLINE:`, error);
    }
  }

  /**
   * Handle server restructuring
   */
  async handleServerRestructure(
    ws: ServerWebSocket<CustomWebSocketData>,
    sender: string,
    data: {
      serverId: string;
      structure: Array<{
        id: string;
        type: "category" | "channel";
        position: number;
        name: string;
        parentId?: string | null;
      }>;
    },
  ) {
    try {
      // Check if the user has permission to manage channels
      const hasPermission = await hasServerPermission(
        db,
        sender,
        data.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        this.sendError(
          ws,
          "You do not have permission to restructure the server",
        );
        return;
      }

      // Update server structure
      const success = await updateServerStructure(
        data.serverId,
        data.structure,
      );

      if (!success) {
        this.sendError(ws, "Failed to restructure server");
        return;
      }

      // Broadcast the server restructuring to all users in the server using WebSocketUtils
      const serverEvent = WebSocketUtils.event("SERVER_RESTRUCTURE", {
        serverId: data.serverId,
        structure: data.structure,
        restructuredBy: sender,
        timestamp: new Date(),
      });

      this.broadcast(WebSocketUtils.serialize(serverEvent), data.serverId);
    } catch (error) {
      console.error("Error handling server restructure:", error);
      this.sendError(ws, "Internal server error");
    }
  }

  /**
   * Handle channel reordering
   */
  async handleChannelsReorder(
    sender: string,
    data: {
      serverId: string;
      channelPositions: Array<{ channelId: string; position: number }>;
    },
  ) {
    try {
      // Check if the user has permission to manage channels
      const hasPermission = await hasServerPermission(
        db,
        sender,
        data.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        throw new Error("You do not have permission to reorder channels");
      }

      // Update channel positions
      const success = await updateChannelPositions(data.channelPositions);

      if (!success) {
        throw new Error("Failed to reorder channels");
      }

      // Broadcast the channel reordering to all users in the server using WebSocketUtils
      const channelEvent = WebSocketUtils.event("CHANNELS_REORDER", {
        serverId: data.serverId,
        channelPositions: data.channelPositions,
        reorderedBy: sender,
        timestamp: new Date(),
      });

      this.broadcast(WebSocketUtils.serialize(channelEvent), data.serverId);
    } catch (error) {
      console.error("Error handling channels reorder:", error);
      throw error;
    }
  }

  /**
   * Handle adding a channel to a category
   */
  async handleChannelAddToCategory(
    sender: string,
    data: {
      serverId: string;
      categoryId: string;
      channelId: string;
    },
  ) {
    try {
      // Check if the user has permission to manage channels
      const hasPermission = await hasServerPermission(
        db,
        sender,
        data.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        throw new Error("You do not have permission to manage channels");
      }

      // Update channel's category
      const success = await db
        .update(ChannelSchema)
        .set({ categoryId: data.categoryId })
        .where(eq(ChannelSchema.id, data.channelId))
        .returning();

      if (!success) {
        throw new Error("Failed to add channel to category");
      }

      // Broadcast the channel addition to all users in the server using WebSocketUtils
      const channelEvent = WebSocketUtils.event("CHANNEL_ADDED_TO_CATEGORY", {
        serverId: data.serverId,
        categoryId: data.categoryId,
        channelId: data.channelId,
        addedBy: sender,
        timestamp: new Date(),
      });

      this.broadcast(WebSocketUtils.serialize(channelEvent), data.serverId);
    } catch (error) {
      console.error("Error handling channel add to category:", error);
      throw error;
    }
  }

  /**
   * Handle removing a channel from a category
   */
  async handleChannelRemoveFromCategory(
    sender: string,
    data: {
      serverId: string;
      categoryId: string;
      channelId: string;
    },
  ) {
    try {
      // Check if the user has permission to manage channels
      const hasPermission = await hasServerPermission(
        db,
        sender,
        data.serverId,
        MANAGE_CHANNELS,
      );

      if (!hasPermission) {
        throw new Error("You do not have permission to manage channels");
      }

      // Update channel's category to null
      const success = await db
        .update(ChannelSchema)
        .set({ categoryId: null })
        .where(eq(ChannelSchema.id, data.channelId))
        .returning();

      if (!success) {
        throw new Error("Failed to remove channel from category");
      }

      // Broadcast the channel removal to all users in the server using WebSocketUtils
      const channelEvent = WebSocketUtils.event(
        "CHANNEL_REMOVED_FROM_CATEGORY",
        {
          serverId: data.serverId,
          categoryId: data.categoryId,
          channelId: data.channelId,
          removedBy: sender,
          timestamp: new Date(),
        },
      );

      this.broadcast(WebSocketUtils.serialize(channelEvent), data.serverId);
    } catch (error) {
      console.error("Error handling channel remove from category:", error);
      throw error;
    }
  }

  /**
   * Send an error message to a WebSocket client
   */
  private sendError(
    ws: ServerWebSocket<CustomWebSocketData>,
    message: string,
    code: string = "GENERAL_ERROR",
    details?: any,
  ): void {
    // Map the string code to WebSocketErrorCode enum
    const errorCode = this.mapToWebSocketErrorCode(code);

    // Use WebSocketUtils for standardized error response
    const errorEvent = WebSocketUtils.error(errorCode, message, {
      details: details,
    });

    this.logError(code, message, details);
    WebSocketUtils.send(ws, errorEvent);
  }

  /**
   * Map string error codes to WebSocketErrorCode enum
   */
  private mapToWebSocketErrorCode(code: string): WebSocketErrorCode {
    switch (code) {
      case "PERMISSION_DENIED":
        return WebSocketErrorCode.PERMISSION_DENIED;
      case "AUTH_REQUIRED":
        return WebSocketErrorCode.AUTH_REQUIRED;
      case "AUTH_FAILED":
        return WebSocketErrorCode.AUTH_FAILED;
      case "TOKEN_EXPIRED":
        return WebSocketErrorCode.TOKEN_EXPIRED;
      case "RATE_LIMITED":
        return WebSocketErrorCode.RATE_LIMITED;
      case "INVALID_MESSAGE_FORMAT":
        return WebSocketErrorCode.INVALID_MESSAGE_FORMAT;
      case "SCHEMA_VALIDATION_FAILED":
        return WebSocketErrorCode.SCHEMA_VALIDATION_FAILED;
      case "CHANNEL_NOT_FOUND":
        return WebSocketErrorCode.CHANNEL_NOT_FOUND;
      case "SERVER_NOT_FOUND":
        return WebSocketErrorCode.SERVER_NOT_FOUND;
      case "USER_NOT_FOUND":
        return WebSocketErrorCode.USER_NOT_FOUND;
      case "MESSAGE_TOO_LARGE":
        return WebSocketErrorCode.MESSAGE_TOO_LARGE;
      default:
        return WebSocketErrorCode.INTERNAL_ERROR;
    }
  }

  private initializeConnectionMetadata(
    ws: ServerWebSocket<CustomWebSocketData>,
  ): void {
    this.connectionMetadata.set(ws, {
      state: ConnectionState.CONNECTING,
      lastPing: Date.now(),
      reconnectAttempts: 0,
      lastTokenRefresh: Date.now(),
      sessionId: crypto.randomUUID(),
    });
  }

  private updateConnectionState(
    ws: ServerWebSocket<CustomWebSocketData>,
    state: ConnectionState,
  ): void {
    const metadata = this.connectionMetadata.get(ws);
    if (metadata) {
      metadata.state = state;
      if (state === ConnectionState.CONNECTED) {
        metadata.reconnectAttempts = 0;
      }
    }
  }

  private async handleReconnection(
    ws: ServerWebSocket<CustomWebSocketData>,
  ): Promise<void> {
    const metadata = this.connectionMetadata.get(ws);
    if (!metadata) return;

    if (metadata.reconnectAttempts >= this.MAX_RECONNECT_ATTEMPTS) {
      ws.close(1000, "Max reconnection attempts reached");
      return;
    }

    metadata.state = ConnectionState.RECONNECTING;
    metadata.reconnectAttempts++;

    const backoffTime =
      this.RECONNECT_BACKOFF_MS * Math.pow(2, metadata.reconnectAttempts - 1);
    await new Promise((resolve) => setTimeout(resolve, backoffTime));

    // Attempt to refresh token if needed
    if (
      Date.now() - metadata.lastTokenRefresh >
      this.TOKEN_REFRESH_THRESHOLD_MS
    ) {
      try {
        await this.refreshToken(ws);
      } catch (error) {
        ws.close(1000, "Token refresh failed");
        return;
      }
    }

    // Re-establish connection
    this.updateConnectionState(ws, ConnectionState.CONNECTED);
  }

  private async refreshToken(
    ws: ServerWebSocket<CustomWebSocketData>,
  ): Promise<void> {
    const metadata = this.connectionMetadata.get(ws);
    if (!metadata) return;

    try {
      // Call your token refresh endpoint
      const response = await fetch("/api/refresh-token", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${ws.data.token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Token refresh failed");
      }

      const { token } = await response.json();
      ws.data.token = token;
      metadata.lastTokenRefresh = Date.now();
    } catch (error) {
      throw new Error("Token refresh failed");
    }
  }

  public async handleConnection(
    ws: ServerWebSocket<CustomWebSocketData>,
  ): Promise<void> {
    this.initializeConnectionMetadata(ws);
    this.updateConnectionState(ws, ConnectionState.CONNECTED);

    // Start ping interval for this connection
    const pingInterval = setInterval(() => {
      const metadata = this.connectionMetadata.get(ws);
      if (!metadata) {
        clearInterval(pingInterval);
        return;
      }

      if (Date.now() - metadata.lastPing > this.PING_INTERVAL_MS * 2) {
        this.handleReconnection(ws);
      } else {
        ws.ping();
      }
    }, this.PING_INTERVAL_MS);

    // Store the interval ID in the metadata for cleanup
    const metadata = this.connectionMetadata.get(ws);
    if (metadata) {
      (metadata as any).pingInterval = pingInterval;
    }
  }

  public async handleDisconnection(
    ws: ServerWebSocket<CustomWebSocketData>,
  ): Promise<void> {
    const metadata = this.connectionMetadata.get(ws);
    if (metadata) {
      this.updateConnectionState(ws, ConnectionState.DISCONNECTED);

      // Clear ping interval
      if ((metadata as any).pingInterval) {
        clearInterval((metadata as any).pingInterval);
      }

      // Clean up metadata
      this.connectionMetadata.delete(ws);
    }

    // Remove from all collections
    this.remove(ws);
  }

  private logEvent(
    type: string,
    userId: string,
    eventType: string,
    details: any,
  ): void {
    const logEntry: LogEntry = {
      timestamp: Date.now(),
      type,
      userId,
      eventType,
      details,
    };

    this.logBuffer.push(logEntry);
    if (this.logBuffer.length > this.MAX_LOG_ENTRIES) {
      this.logBuffer.shift();
    }

    // Log to console for development
    this.logger.debug(`[${type}] User ${userId}: ${eventType}`, details);
  }

  private checkRateLimit(userId: string, eventType: string): boolean {
    const key = `${userId}:${eventType}`;
    const now = Date.now();
    let limitInfo = this.rateLimits.get(key);

    if (!limitInfo) {
      limitInfo = {
        count: 0,
        lastReset: now,
      };
      this.rateLimits.set(key, limitInfo);
    }

    // Reset counter if window has passed
    if (now - limitInfo.lastReset > this.RATE_LIMIT_WINDOW_MS) {
      limitInfo.count = 0;
      limitInfo.lastReset = now;
      limitInfo.blockedUntil = undefined;
    }

    // Check if user is blocked
    if (limitInfo.blockedUntil && now < limitInfo.blockedUntil) {
      return false;
    }

    // Increment counter
    limitInfo.count++;

    // Check if rate limit exceeded
    if (limitInfo.count > this.MAX_EVENTS_PER_WINDOW) {
      limitInfo.blockedUntil = now + this.BLOCK_DURATION_MS;
      this.logEvent("RATE_LIMIT_EXCEEDED", userId, eventType, {
        count: limitInfo.count,
        blockedUntil: limitInfo.blockedUntil,
      });
      return false;
    }

    return true;
  }

  private validateMessageSize(message: any): boolean {
    const messageSize = JSON.stringify(message).length;
    return messageSize <= this.MAX_MESSAGE_SIZE;
  }

  public getLogs(filter?: Partial<LogEntry>): LogEntry[] {
    if (!filter) {
      return [...this.logBuffer];
    }

    return this.logBuffer.filter((entry) => {
      return Object.entries(filter).every(([key, value]) => {
        return entry[key as keyof LogEntry] === value;
      });
    });
  }

  public getRateLimitInfo(
    userId: string,
    eventType: string,
  ): RateLimitInfo | undefined {
    return this.rateLimits.get(`${userId}:${eventType}`);
  }

  private logError(code: string, message: string, details?: any): void {
    const errorEvent: ErrorEvent = {
      type: "ERROR",
      code,
      message,
      details,
      timestamp: Date.now(),
    };

    this.errorBuffer.push(errorEvent);
    if (this.errorBuffer.length > this.MAX_ERROR_BUFFER_SIZE) {
      this.errorBuffer.shift();
    }

    // Log to console for development
    this.logger.error(`[${code}] ${message}`, details);
  }

  /**
   * Broadcast an event with correlation tracking
   * @param eventType Event type
   * @param data Event data
   * @param options Broadcasting options
   * @param correlationId Optional correlation ID to link with request
   */
  private broadcastEvent<T>(
    eventType: string,
    data: T,
    options: {
      serverId?: string;
      channelId?: string;
      excludeUserId?: string;
      category?: string;
      severity?: "info" | "warning" | "error";
    } = {},
    correlationId?: string,
  ) {
    const event = WebSocketUtils.event(eventType, data, {
      category: options.category || "general",
      severity: options.severity || "info",
    });

    // Add correlation ID if provided
    if (correlationId) {
      event.meta.correlationId = correlationId;
    }

    this.broadcast(
      WebSocketUtils.serialize(event),
      options.serverId,
      options.channelId,
      options.excludeUserId,
    );

    this.logger.debug(`Broadcast event ${eventType}`, undefined, {
      serverId: options.serverId,
      channelId: options.channelId,
      correlationId,
      messageId: event.meta.id,
    });
  }

  private updatePerformanceMetrics(
    userId: string,
    latency: number,
    isError: boolean = false,
  ): void {
    let metrics = this.performanceMetrics.get(userId);
    if (!metrics) {
      metrics = {
        messageCount: 0,
        errorCount: 0,
        averageLatency: 0,
        lastUpdated: Date.now(),
      };
      this.performanceMetrics.set(userId, metrics);
    }

    metrics.messageCount++;
    if (isError) {
      metrics.errorCount++;
    }

    // Update average latency using exponential moving average
    const alpha = 0.1; // Smoothing factor
    metrics.averageLatency =
      (1 - alpha) * metrics.averageLatency + alpha * latency;
    metrics.lastUpdated = Date.now();
  }

  public getErrorLogs(filter?: Partial<ErrorEvent>): ErrorEvent[] {
    if (!filter) {
      return [...this.errorBuffer];
    }

    return this.errorBuffer.filter((entry) => {
      return Object.entries(filter).every(([key, value]) => {
        return entry[key as keyof ErrorEvent] === value;
      });
    });
  }

  public getPerformanceMetrics(
    userId?: string,
  ): Map<string, PerformanceMetrics> | PerformanceMetrics | undefined {
    if (userId) {
      return this.performanceMetrics.get(userId);
    }
    return new Map(this.performanceMetrics);
  }

  // Add error handling wrapper for message processing
  private async processMessageWithErrorHandling(
    ws: ServerWebSocket<CustomWebSocketData>,
    message: any,
  ): Promise<void> {
    const startTime = Date.now();
    try {
      await this.handleMessage(ws, message);
      const latency = Date.now() - startTime;
      this.updatePerformanceMetrics(ws.data.userId, latency);
    } catch (error) {
      const latency = Date.now() - startTime;
      this.updatePerformanceMetrics(ws.data.userId, latency, true);

      this.logError("MESSAGE_PROCESSING_ERROR", "Error processing message", {
        error: error instanceof Error ? error.message : "Unknown error",
        messageType: message.type,
        userId: ws.data.userId,
      });

      this.sendError(
        ws,
        "An error occurred while processing your message",
        "MESSAGE_PROCESSING_ERROR",
        { messageType: message.type },
      );
    }
  }

  // Add periodic metrics cleanup
  private startMetricsCleanup(): void {
    setInterval(() => {
      const now = Date.now();
      for (const [userId, metrics] of this.performanceMetrics.entries()) {
        if (now - metrics.lastUpdated > this.METRICS_UPDATE_INTERVAL * 2) {
          this.performanceMetrics.delete(userId);
        }
      }
    }, this.METRICS_UPDATE_INTERVAL);
  }

  private async validateSession(
    userId: string,
    deviceId: string,
  ): Promise<boolean> {
    const session = this.sessions.get(userId);
    console.log("session of this user:", session);
    if (!session) return false;

    // Check if session is expired
    if (Date.now() - session.lastActivity > this.SESSION_TIMEOUT_MS) {
      this.invalidateSession(userId);
      console.log("invalidating session of this user:", userId);
      return false;
    }

    // Check if token needs refresh
    if (Date.now() > session.tokenExpiry - this.TOKEN_REFRESH_THRESHOLD_MS) {
      if (!session.refreshToken || session.refreshToken.trim() === "") {
        // No refresh token available, request re-authentication from client
        this.logger.warn(
          `No refresh token available for user ${userId}, requesting re-authentication`,
        );
        this.requestReauthentication(userId);
        return false;
      } else {
        // Use existing refresh logic
        try {
          await this.refreshTokenForSession(userId);
        } catch (error) {
          this.logError("TOKEN_REFRESH_FAILED", "Failed to refresh token", {
            userId,
            error,
          });
          // If refresh fails, also request re-authentication
          this.requestReauthentication(userId);
          return false;
        }
      }
    }

    // Update last activity
    session.lastActivity = Date.now();
    return true;
  }

  private async refreshTokenForSession(userId: string): Promise<void> {
    const session = this.sessions.get(userId);
    if (!session) throw new Error("Session not found");

    if (!session.refreshToken || session.refreshToken.trim() === "") {
      throw new Error("No refresh token available");
    }

    try {
      // Call your token refresh endpoint
      console.log("before refresh token api endpoint");
      const response = await fetch("http://localhost:3005/api/refresh-token", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          refreshToken: session.refreshToken,
        }),
      });

      if (!response.ok) {
        throw new Error("Token refresh failed");
      }

      const { token, refreshToken, expiresIn } = await response.json();

      // Update session with new tokens
      session.token = token;
      session.refreshToken = refreshToken;
      session.tokenExpiry = Date.now() + expiresIn * 1000;

      // Notify all devices of the token refresh using WebSocketUtils
      const deviceSessions = this.deviceSessions.get(userId);
      if (deviceSessions) {
        const refreshEvent = WebSocketUtils.success(
          "TOKEN_REFRESHED",
          {
            token,
            expiresIn,
          },
          {
            message: "Token refreshed successfully",
          },
        );

        for (const [_, deviceSession] of deviceSessions) {
          WebSocketUtils.send(deviceSession.ws, refreshEvent);
        }
      }
    } catch (error) {
      this.logError("TOKEN_REFRESH_ERROR", "Error refreshing token", {
        userId,
        error,
      });
      throw error;
    }
  }

  private invalidateSession(userId: string): void {
    const deviceSessions = this.deviceSessions.get(userId);
    if (deviceSessions) {
      // Notify all devices of session invalidation using WebSocketUtils
      const invalidateEvent = WebSocketUtils.error(
        WebSocketErrorCode.SESSION_EXPIRED,
        "Session has been invalidated",
        {
          details: {
            reason: "SESSION_EXPIRED",
          },
        },
      );

      for (const [_, deviceSession] of deviceSessions) {
        WebSocketUtils.send(deviceSession.ws, invalidateEvent);
        deviceSession.ws.close();
      }
    }

    this.sessions.delete(userId);
    this.deviceSessions.delete(userId);
  }

  /**
   * Request re-authentication from client when token refresh is not possible
   */
  private requestReauthentication(userId: string): void {
    const deviceSessions = this.deviceSessions.get(userId);
    if (deviceSessions) {
      // Use WebSocketUtils for standardized re-authentication request
      const reauthEvent = WebSocketUtils.error(
        WebSocketErrorCode.TOKEN_EXPIRED,
        "Please re-authenticate to continue",
        {
          details: {
            reason: "TOKEN_EXPIRED_NO_REFRESH",
          },
        },
      );

      for (const [_, deviceSession] of deviceSessions) {
        WebSocketUtils.send(deviceSession.ws, reauthEvent);
      }
    }
  }

  public async handleAuthentication(
    ws: ServerWebSocket<CustomWebSocketData>,
    authData: {
      userId: string;
      deviceId: string;
      token: string;
      refreshToken: string;
      expiresIn: number;
      serverId?: string;
      channelId?: string;
      type?: string;
    },
  ): Promise<boolean> {
    const {
      userId,
      deviceId,
      token,
      refreshToken,
      expiresIn,
      serverId,
      channelId,
      type,
    } = authData;

    // Log refresh token status for debugging
    const hasRefreshToken = refreshToken && refreshToken.trim() !== "";
    this.logger.debug(
      `Authentication for user ${userId}: hasRefreshToken=${hasRefreshToken}`,
    );

    // Check if user has too many active sessions
    const deviceSessions = this.deviceSessions.get(userId) || new Map();
    if (deviceSessions.size >= this.MAX_DEVICES_PER_USER) {
      // Remove oldest session
      const oldestDevice = Array.from(deviceSessions.entries()).sort(
        ([, a], [, b]) => a.lastActivity - b.lastActivity,
      )[0];
      if (oldestDevice) {
        const [oldDeviceId, oldSession] = oldestDevice;
        oldSession.ws.close();
        deviceSessions.delete(oldDeviceId);
      }
    }

    // Create or update session (refreshToken can be empty)
    this.sessions.set(userId, {
      userId,
      deviceId,
      lastActivity: Date.now(),
      token,
      tokenExpiry: Date.now() + expiresIn * 1000,
      refreshToken: refreshToken || "", // Handle empty refresh token
      isActive: true,
    });

    // Add device session
    if (!this.deviceSessions.has(userId)) {
      this.deviceSessions.set(userId, new Map());
    }
    this.deviceSessions.get(userId)!.set(deviceId, {
      ws,
      lastActivity: Date.now(),
    });

    // Store connection context for reconnection
    if (serverId || channelId || type) {
      this.connectionContext.set(userId, {
        serverId,
        channelId,
        type,
      });
    }

    // Restore previous connection context on reconnection
    const previousContext = this.connectionContext.get(userId);
    if (previousContext) {
      // If client provided reconnection info, restore previous subscription
      if (
        previousContext.serverId &&
        previousContext.channelId &&
        serverId &&
        channelId
      ) {
        // Auto-subscribe to previous channel
        await this.subscribeChannel(
          ws,
          userId,
          previousContext.serverId,
          previousContext.channelId,
        );
      }
    }

    return true;
  }

  public async validateMessage(
    ws: ServerWebSocket<CustomWebSocketData>,
    message: any,
  ): Promise<{ isValid: boolean; error?: { code: string; message: string } }> {
    const { userId, deviceId } = ws.data;

    console.log("validateMessage called with:", {
      userId,
      deviceId,
      messageType: message.type,
      messageKeys: Object.keys(message),
      messageSize: JSON.stringify(message).length,
    });

    // Validate session first
    if (!(await this.validateSession(userId, deviceId))) {
      console.log("Session validation failed for user:", userId);
      return {
        isValid: false,
        error: {
          code: "SESSION_INVALID",
          message: "Session is invalid or expired",
        },
      };
    }

    // Continue with existing validation
    const eventType = message.type;
    this.logEvent("MESSAGE_RECEIVED", userId, eventType, message);

    if (!this.checkRateLimit(userId, eventType)) {
      console.log(
        "Rate limit exceeded for user:",
        userId,
        "eventType:",
        eventType,
      );
      return {
        isValid: false,
        error: {
          code: "RATE_LIMIT_EXCEEDED",
          message: "Rate limit exceeded. Please try again later.",
        },
      };
    }

    if (!this.validateMessageSize(message)) {
      console.log(
        "Message too large for user:",
        userId,
        "size:",
        JSON.stringify(message).length,
      );
      return {
        isValid: false,
        error: {
          code: "MESSAGE_TOO_LARGE",
          message: "Message size exceeds limit",
        },
      };
    }

    console.log("Message validation passed for user:", userId);
    return { isValid: true };
  }

  // Server Management
  public async handleServerUpdate(
    userId: string,
    data: { serverId: string; updates: any },
  ): Promise<void> {
    try {
      const { serverId, updates } = data;

      this.logger.debug(`Server ${serverId} updated by ${userId}`);

      // Update server in database
      const [updatedServer] = await db
        .update(ServerSchema)
        .set(updates)
        .where(eq(ServerSchema.id, serverId))
        .returning();

      // Use WebSocketUtils for standardized event broadcasting
      const updateEvent = WebSocketUtils.event("SERVER_UPDATED", {
        serverId: serverId,
        server: updatedServer,
        updatedBy: userId,
        timestamp: new Date(),
      });

      // Broadcast to all server members
      this.broadcast(WebSocketUtils.serialize(updateEvent), serverId);

      this.logger.debug("Server update broadcast successfully", undefined, {
        serverId,
      });
    } catch (error) {
      this.logger.error("Failed to update server:", undefined, error);
      throw error;
    }
  }

  public async handleServerDelete(
    userId: string,
    data: { serverId: string },
  ): Promise<void> {
    try {
      const { serverId } = data;

      this.logger.debug(`Server ${serverId} deleted by ${userId}`);

      // Delete server from database
      await db.delete(ServerSchema).where(eq(ServerSchema.id, serverId));

      // Use WebSocketUtils for standardized event broadcasting
      const deleteEvent = WebSocketUtils.event("SERVER_DELETED", {
        serverId: serverId,
        deletedBy: userId,
        timestamp: new Date(),
      });

      const serverMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of serverMembers) {
        this.broadcastToUser(
          member.userId,
          WebSocketUtils.serialize(deleteEvent),
        );
      }

      this.logger.debug("Server deletion broadcast successfully", undefined, {
        serverId,
      });
    } catch (error) {
      this.logger.error("Failed to delete server:", undefined, error);
      throw error;
    }
  }

  public async handleServerLeave(
    userId: string,
    data: { serverId: string },
  ): Promise<void> {
    try {
      const { serverId } = data;

      this.logger.debug(`User ${userId} leaving server ${serverId}`);

      // Remove user from server
      await db
        .delete(ServerMembershipSchema)
        .where(
          and(
            eq(ServerMembershipSchema.userId, userId),
            eq(ServerMembershipSchema.serverId, serverId),
          ),
        );

      // Use WebSocketUtils for standardized event broadcasting
      const leaveEvent = WebSocketUtils.event("MEMBER_LEFT", {
        serverId: serverId,
        userId: userId,
        timestamp: new Date(),
      });

      const serverMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of serverMembers) {
        this.broadcastToUser(
          member.userId,
          WebSocketUtils.serialize(leaveEvent),
        );
      }

      this.logger.debug("Server leave broadcast successfully", undefined, {
        serverId,
        userId,
      });
    } catch (error) {
      this.logger.error("Failed to leave server:", undefined, error);
      throw error;
    }
  }

  // Channel Management
  public async handleChannelUnsubscribe(
    ws: ServerWebSocket<CustomWebSocketData>,
    userId: string,
    data: { serverId: string; channelId: string },
  ): Promise<void> {
    try {
      const { serverId, channelId } = data;
      const topic = `${serverId}:${channelId}`;

      // Remove user from channel subscribers
      const subscribers = this.topicSubscriptions.get(topic);
      if (subscribers) {
        subscribers.delete(ws);
        if (subscribers.size === 0) {
          this.topicSubscriptions.delete(topic);
        }
      }

      // Notify other channel members
      this.notifyChannelUsers(ws, topic, "user_channel_unsubscribe");
    } catch (error) {
      this.logError(
        "CHANNEL_UNSUBSCRIBE_ERROR",
        "Failed to unsubscribe from channel",
        { userId, error },
      );
      throw error;
    }
  }

  public async handleChannelAddToCategory(
    userId: string,
    data: { serverId: string; categoryId: string; channelId: string },
  ): Promise<void> {
    try {
      const { serverId, categoryId, channelId } = data;

      // Update channel's category
      await db
        .update(ChannelSchema)
        .set({ categoryId })
        .where(eq(ChannelSchema.id, channelId));

      // Broadcast the change to all server members
      const channelEvent = {
        type: EventTypes.CHANNEL_ADDED_TO_CATEGORY,
        sender: userId,
        data: {
          server_id: serverId,
          channel_id: channelId,
          category_id: categoryId,
        },
      };

      const serverMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of serverMembers) {
        this.broadcastToUser(member.userId, JSON.stringify(channelEvent));
      }
    } catch (error) {
      this.logError(
        "CHANNEL_ADD_TO_CATEGORY_ERROR",
        "Failed to add channel to category",
        { userId, error },
      );
      throw error;
    }
  }

  public async handleChannelRemoveFromCategory(
    userId: string,
    data: { serverId: string; categoryId: string; channelId: string },
  ): Promise<void> {
    try {
      const { serverId, categoryId, channelId } = data;

      // Remove channel from category
      await db
        .update(ChannelSchema)
        .set({ categoryId: null })
        .where(eq(ChannelSchema.id, channelId));

      // Broadcast the change to all server members
      const channelEvent = {
        type: EventTypes.CHANNEL_REMOVED_FROM_CATEGORY,
        sender: userId,
        data: {
          server_id: serverId,
          channel_id: channelId,
          category_id: null,
        },
      };

      const serverMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of serverMembers) {
        this.broadcastToUser(member.userId, JSON.stringify(channelEvent));
      }
    } catch (error) {
      this.logError(
        "CHANNEL_REMOVE_FROM_CATEGORY_ERROR",
        "Failed to remove channel from category",
        { userId, error },
      );
      throw error;
    }
  }

  // Role Management
  public async handleRoleCreate(
    userId: string,
    data: { serverId: string; name: string; permissions: number },
  ): Promise<void> {
    try {
      const { serverId, name, permissions } = data;

      // Create new role
      const [role] = await db
        .insert(ServerRoleSchema)
        .values({
          serverId,
          name,
          permissions: BigInt(permissions),
        })
        .returning();

      // Broadcast role creation to server members
      const roleEvent = {
        type: EventTypes.ROLE_CREATED,
        sender: userId,
        data: {
          server_id: serverId,
          role,
        },
      };

      const serverMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of serverMembers) {
        this.broadcastToUser(member.userId, JSON.stringify(roleEvent));
      }
    } catch (error) {
      this.logError("ROLE_CREATE_ERROR", "Failed to create role", {
        userId,
        error,
      });
      throw error;
    }
  }

  public async handleRoleUpdate(
    userId: string,
    data: { serverId: string; roleId: string; updates: any },
  ): Promise<void> {
    try {
      const { serverId, roleId, updates } = data;

      // Update role
      const [updatedRole] = await db
        .update(ServerRoleSchema)
        .set(updates)
        .where(eq(ServerRoleSchema.id, roleId))
        .returning();

      // Broadcast role update to server members
      const roleEvent = {
        type: "ROLE_UPDATED",
        sender: userId,
        data: {
          server_id: serverId,
          role: updatedRole,
        },
      };

      const serverMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of serverMembers) {
        this.broadcastToUser(member.userId, JSON.stringify(roleEvent));
      }
    } catch (error) {
      this.logError("ROLE_UPDATE_ERROR", "Failed to update role", {
        userId,
        error,
      });
      throw error;
    }
  }

  public async handleRoleDelete(
    userId: string,
    data: { serverId: string; roleId: string },
  ): Promise<void> {
    try {
      const { serverId, roleId } = data;

      // Delete role
      await db.delete(ServerRoleSchema).where(eq(ServerRoleSchema.id, roleId));

      // Broadcast role deletion to server members
      const roleEvent = {
        type: "ROLE_DELETED",
        sender: userId,
        data: {
          server_id: serverId,
          role_id: roleId,
        },
      };

      const serverMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of serverMembers) {
        this.broadcastToUser(member.userId, JSON.stringify(roleEvent));
      }
    } catch (error) {
      this.logError("ROLE_DELETE_ERROR", "Failed to delete role", {
        userId,
        error,
      });
      throw error;
    }
  }

  public async handleRoleAssign(
    userId: string,
    data: { serverId: string; roleId: string; targetUserId: string },
  ): Promise<void> {
    try {
      const { serverId, roleId, targetUserId } = data;

      // Assign role to user
      await db.insert(UserRoles).values({
        userId: targetUserId,
        roleId,
        serverId,
      });

      // Broadcast role assignment to server members
      const roleEvent = {
        type: "ROLE_ASSIGNED",
        sender: userId,
        data: {
          server_id: serverId,
          role_id: roleId,
          user_id: targetUserId,
        },
      };

      const serverMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of serverMembers) {
        this.broadcastToUser(member.userId, JSON.stringify(roleEvent));
      }
    } catch (error) {
      this.logError("ROLE_ASSIGN_ERROR", "Failed to assign role", {
        userId,
        error,
      });
      throw error;
    }
  }

  public async handleRoleRemove(
    userId: string,
    data: { serverId: string; roleId: string; targetUserId: string },
  ): Promise<void> {
    try {
      const { serverId, roleId, targetUserId } = data;

      // Remove role from user
      await db
        .delete(UserRoles)
        .where(
          and(
            eq(UserRoles.userId, targetUserId),
            eq(UserRoles.roleId, roleId),
            eq(UserRoles.serverId, serverId),
          ),
        );

      // Broadcast role removal to server members
      const roleEvent = {
        type: "ROLE_REMOVED",
        sender: userId,
        data: {
          server_id: serverId,
          role_id: roleId,
          user_id: targetUserId,
        },
      };

      const serverMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of serverMembers) {
        this.broadcastToUser(member.userId, JSON.stringify(roleEvent));
      }
    } catch (error) {
      this.logError("ROLE_REMOVE_ERROR", "Failed to remove role", {
        userId,
        error,
      });
      throw error;
    }
  }

  // Member Management
  public async handleMemberKick(
    userId: string,
    data: { serverId: string; targetUserId: string },
  ): Promise<void> {
    try {
      const { serverId, targetUserId } = data;

      // Remove member from server
      await db
        .delete(ServerMembershipSchema)
        .where(
          and(
            eq(ServerMembershipSchema.userId, targetUserId),
            eq(ServerMembershipSchema.serverId, serverId),
          ),
        );

      // Broadcast kick event to server members
      const kickEvent = {
        type: "MEMBER_KICKED",
        sender: userId,
        data: {
          server_id: serverId,
          user_id: targetUserId,
        },
      };

      const serverMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of serverMembers) {
        this.broadcastToUser(member.userId, JSON.stringify(kickEvent));
      }
    } catch (error) {
      this.logError("MEMBER_KICK_ERROR", "Failed to kick member", {
        userId,
        error,
      });
      throw error;
    }
  }

  public async handleMemberBan(
    userId: string,
    data: { serverId: string; targetUserId: string; reason?: string },
  ): Promise<void> {
    try {
      const { serverId, targetUserId, reason } = data;

      // Create ban record
      await db.insert(ServerBanSchema).values({
        serverId,
        userId: targetUserId,
        bannedById: userId,
        reason,
      });

      // Remove member from server
      await db
        .delete(ServerMembershipSchema)
        .where(
          and(
            eq(ServerMembershipSchema.userId, targetUserId),
            eq(ServerMembershipSchema.serverId, serverId),
          ),
        );

      // Broadcast ban event to server members
      const banEvent = {
        type: "MEMBER_BANNED",
        sender: userId,
        data: {
          server_id: serverId,
          user_id: targetUserId,
          reason,
        },
      };

      const serverMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of serverMembers) {
        this.broadcastToUser(member.userId, JSON.stringify(banEvent));
      }
    } catch (error) {
      this.logError("MEMBER_BAN_ERROR", "Failed to ban member", {
        userId,
        error,
      });
      throw error;
    }
  }

  public async handleMemberUnban(
    userId: string,
    data: { serverId: string; targetUserId: string },
  ): Promise<void> {
    try {
      const { serverId, targetUserId } = data;

      // Remove ban record
      await db
        .delete(ServerBanSchema)
        .where(
          and(
            eq(ServerBanSchema.userId, targetUserId),
            eq(ServerBanSchema.serverId, serverId),
          ),
        );

      // Broadcast unban event to server members
      const unbanEvent = {
        type: "MEMBER_UNBANNED",
        sender: userId,
        data: {
          server_id: serverId,
          user_id: targetUserId,
        },
      };

      const serverMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of serverMembers) {
        this.broadcastToUser(member.userId, JSON.stringify(unbanEvent));
      }
    } catch (error) {
      this.logError("MEMBER_UNBAN_ERROR", "Failed to unban member", {
        userId,
        error,
      });
      throw error;
    }
  }

  // Voice Channel Features
  public async handleVoiceJoin(
    userId: string,
    data: { serverId: string; channelId: string },
  ): Promise<void> {
    try {
      const { serverId, channelId } = data;

      this.logger.debug("User joining voice channel", undefined, {
        userId,
        serverId,
        channelId,
      });

      // Update user's voice channel
      await db
        .update(UserSchema)
        .set({ currentVoiceChannelId: channelId })
        .where(eq(UserSchema.id, userId));

      // Use WebSocketUtils for standardized event broadcasting
      const voiceEvent = WebSocketUtils.event("VOICE_JOINED", {
        serverId: serverId,
        channelId: channelId,
        userId: userId,
        timestamp: new Date(),
      });

      // Get all server members since ServerMembershipSchema doesn't have channelId
      const channelMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of channelMembers) {
        this.broadcastToUser(
          member.userId,
          WebSocketUtils.serialize(voiceEvent),
        );
      }

      this.logger.debug("Voice join event broadcast completed", undefined, {
        userId,
        channelId,
        eventId: voiceEvent.meta.id,
      });
    } catch (error) {
      this.logger.error("Failed to join voice channel:", undefined, error);
      throw error;
    }
  }

  public async handleVoiceLeave(
    userId: string,
    data: { serverId: string; channelId: string },
  ): Promise<void> {
    try {
      const { serverId, channelId } = data;

      this.logger.debug("User leaving voice channel", undefined, {
        userId,
        serverId,
        channelId,
      });

      // Update user's voice channel
      await db
        .update(UserSchema)
        .set({ currentVoiceChannelId: null })
        .where(eq(UserSchema.id, userId));

      // Use WebSocketUtils for standardized event broadcasting
      const voiceEvent = WebSocketUtils.event("VOICE_LEFT", {
        serverId: serverId,
        channelId: channelId,
        userId: userId,
        timestamp: new Date(),
      });

      // Get all server members since ServerMembershipSchema doesn't have channelId
      const channelMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of channelMembers) {
        this.broadcastToUser(
          member.userId,
          WebSocketUtils.serialize(voiceEvent),
        );
      }

      this.logger.debug("Voice leave event broadcast completed", undefined, {
        userId,
        channelId,
        eventId: voiceEvent.meta.id,
      });
    } catch (error) {
      this.logger.error("Failed to leave voice channel:", undefined, error);
      throw error;
    }
  }

  public async handleVoiceMuteToggle(
    userId: string,
    data: { serverId: string; channelId: string },
  ): Promise<void> {
    try {
      const { serverId, channelId } = data;

      this.logger.debug("Toggling user mute status", undefined, {
        userId,
        serverId,
        channelId,
      });

      // Toggle user's mute status
      const [user] = await db
        .select()
        .from(UserSchema)
        .where(eq(UserSchema.id, userId))
        .limit(1);

      const newMuteStatus = !user?.isMuted;
      await db
        .update(UserSchema)
        .set({ isMuted: newMuteStatus })
        .where(eq(UserSchema.id, userId));

      // Use WebSocketUtils for standardized event broadcasting
      const voiceEvent = WebSocketUtils.event("VOICE_MUTE_TOGGLED", {
        serverId: serverId,
        channelId: channelId,
        userId: userId,
        isMuted: newMuteStatus,
        timestamp: new Date(),
      });

      // Get all server members since ServerMembershipSchema doesn't have channelId
      const channelMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of channelMembers) {
        this.broadcastToUser(
          member.userId,
          WebSocketUtils.serialize(voiceEvent),
        );
      }

      this.logger.debug(
        "Voice mute toggle event broadcast completed",
        undefined,
        {
          userId,
          isMuted: newMuteStatus,
          eventId: voiceEvent.meta.id,
        },
      );
    } catch (error) {
      this.logger.error("Failed to toggle mute status:", undefined, error);
      throw error;
    }
  }

  public async handleVoiceDeafenToggle(
    userId: string,
    data: { serverId: string; channelId: string },
  ): Promise<void> {
    try {
      const { serverId, channelId } = data;

      this.logger.debug("Toggling user deafen status", undefined, {
        userId,
        serverId,
        channelId,
      });

      // Toggle user's deafen status
      // Note: User deafen status should be stored in a separate voice state table
      // For now, we'll track this in memory or handle it client-side
      const newDeafenStatus = true; // This should be retrieved from voice state storage

      // Use WebSocketUtils for standardized event broadcasting
      const voiceEvent = WebSocketUtils.event("VOICE_DEAFEN_TOGGLED", {
        serverId: serverId,
        channelId: channelId,
        userId: userId,
        isDeafened: newDeafenStatus,
        timestamp: new Date(),
      });

      // Get all server members since ServerMembershipSchema doesn't have channelId
      const channelMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.serverId, serverId));

      for (const member of channelMembers) {
        this.broadcastToUser(
          member.userId,
          WebSocketUtils.serialize(voiceEvent),
        );
      }

      this.logger.debug(
        "Voice deafen toggle event broadcast completed",
        undefined,
        {
          userId,
          isDeafened: newDeafenStatus,
          eventId: voiceEvent.meta.id,
        },
      );
    } catch (error) {
      this.logger.error("Failed to toggle deafen status:", undefined, error);
      throw error;
    }
  }

  // User Management
  public async handleUserUpdate(
    userId: string,
    data: { updates: any },
  ): Promise<void> {
    try {
      const { updates } = data;

      // Update user profile
      const [updatedUser] = await db
        .update(UserSchema)
        .set(updates)
        .where(eq(UserSchema.id, userId))
        .returning();

      // Broadcast user update to all connected clients
      const userEvent = {
        type: "USER_UPDATED",
        sender: userId,
        data: {
          user: updatedUser,
        },
      };

      // Get all servers the user is a member of
      const userServers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.userId, userId));

      // Notify all members of these servers
      for (const server of userServers) {
        const serverMembers = await db
          .select()
          .from(ServerMembershipSchema)
          .where(eq(ServerMembershipSchema.serverId, server.serverId));

        for (const member of serverMembers) {
          this.broadcastToUser(member.userId, JSON.stringify(userEvent));
        }
      }
    } catch (error) {
      this.logError("USER_UPDATE_ERROR", "Failed to update user", {
        userId,
        error,
      });
      throw error;
    }
  }

  public async handleUserStatusChange(
    userId: string,
    data: {
      status: "ONLINE" | "AWAY" | "BUSY" | "INVISIBLE" | "OFFLINE";
      statusMessage?: string;
    },
  ): Promise<void> {
    try {
      const { status, statusMessage } = data;

      const user = await db
        .select()
        .from(UserSchema)
        .where(eq(UserSchema.id, userId));

      // Update channel's category
      const success = await db
        .update(UserSchema)
        .set({ status: data.status, statusMessage: data.statusMessage })
        .where(eq(UserSchema.id, userId))
        .returning();

      if (!success) {
        throw new Error("Failed to update user status");
      }

      // Broadcast status change to all connected clients
      const statusEvent = {
        type: "USER_STATUS_CHANGED",
        sender: userId,
        data: {
          status,
          statusMessage,
        },
      };

      const userServers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(eq(ServerMembershipSchema.userId, userId));

      // Notify all members of these servers
      for (const server of userServers) {
        const serverMembers = await db
          .select()
          .from(ServerMembershipSchema)
          .where(eq(ServerMembershipSchema.serverId, server.serverId));

        for (const member of serverMembers) {
          this.broadcastToUser(member.userId, JSON.stringify(statusEvent));
        }
      }
    } catch (error) {
      this.logError(
        "USER_STATUS_CHANGE_ERROR",
        "Failed to update user status",
        { userId, error },
      );
      throw error;
    }
  }

  // Message Management
  public async handleMessageTypingStart(
    userId: string,
    data: { serverId: string; channelId: string },
  ): Promise<void> {
    try {
      const { serverId, channelId } = data;

      // Broadcast typing start event to channel members
      const typingEvent = {
        type: "TYPING_STARTED",
        sender: userId,
        data: {
          server_id: serverId,
          channel_id: channelId,
          user_id: userId,
        },
      };

      // Get all members of the channel from KV Collection (in memory cache for now)
      const channelMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(
          and(
            eq(ServerMembershipSchema.serverId, serverId),
            eq(ChannelSchema.id, channelId),
          ),
        );

      for (const member of channelMembers) {
        if (member.userId !== userId) {
          // Don't send to the typing user
          this.broadcastToUser(member.userId, JSON.stringify(typingEvent));
        }
      }
    } catch (error) {
      this.logError("TYPING_START_ERROR", "Failed to handle typing start", {
        userId,
        error,
      });
      throw error;
    }
  }

  public async handleMessageTypingEnd(
    userId: string,
    data: { serverId: string; channelId: string },
  ): Promise<void> {
    try {
      const { serverId, channelId } = data;

      // Broadcast typing end event to channel members
      const typingEvent = {
        type: "TYPING_ENDED",
        sender: userId,
        data: {
          server_id: serverId,
          channel_id: channelId,
          user_id: userId,
        },
      };

      // Get all members of the channel from KV Collection (in memory cache for now)
      const channelMembers = await db
        .select()
        .from(ServerMembershipSchema)
        .where(
          and(
            eq(ServerMembershipSchema.serverId, serverId),
            eq(ChannelSchema.id, channelId),
          ),
        );

      for (const member of channelMembers) {
        if (member.userId !== userId) {
          // Don't send to the typing user
          this.broadcastToUser(member.userId, JSON.stringify(typingEvent));
        }
      }
    } catch (error) {
      this.logError("TYPING_END_ERROR", "Failed to handle typing end", {
        userId,
        error,
      });
      throw error;
    }
  }

  /**
   * Add a method to get connection context for debugging
   */
  public getConnectionContext(userId: string) {
    return this.connectionContext.get(userId);
  }

  /**
   * Manually clear connection context (useful for testing or admin purposes)
   */
  public clearConnectionContext(userId: string): boolean {
    const existed = this.connectionContext.has(userId);
    this.connectionContext.delete(userId);
    this.logger.debug(
      `Connection context cleared for user ${userId}, existed: ${existed}`,
    );
    return existed;
  }

  /**
   * Get all active connection contexts (useful for debugging)
   */
  public getAllConnectionContexts(): Map<
    string,
    { serverId?: string; channelId?: string; type?: string }
  > {
    return new Map(this.connectionContext);
  }

  /**
   * Enhanced method to restore user connection state on reconnection
   */
  public async restoreConnectionState(
    ws: ServerWebSocket<CustomWebSocketData>,
    userId: string,
    contextToRestore?: { serverId?: string; channelId?: string; type?: string },
  ): Promise<void> {
    if (!contextToRestore) {
      this.logger.debug(`No context to restore for user ${userId}`);
      return;
    }

    this.logger.debug(
      `Restoring connection state for user ${userId}`,
      undefined,
      contextToRestore,
    );

    const { serverId, channelId, type } = contextToRestore;

    // Set the WebSocket data with restored context
    ws.data.type = type;
    ws.data.serverId = serverId;
    ws.data.channelId = channelId;

    try {
      // Handle different connection types
      if (type === "voice") {
        // Add to voice manager
        // Note: VoiceWebSocketManager import might be needed
        this.logger.debug(`Restored voice connection for user ${userId}`);
      } else if (type === "private") {
        // Restore private connection
        this.addPrivateConnection(ws);
        this.logger.debug(`Restored private connection for user ${userId}`);
      } else if (type === "channel" && serverId && channelId) {
        // Restore channel subscription
        await this.subscribeChannel(ws, userId, serverId, channelId);
        this.logger.debug(
          `Restored channel subscription for user ${userId} to ${serverId}:${channelId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error restoring connection state for user ${userId}:`,
        undefined,
        error,
      );
      throw error;
    }
  }

  // ===== ENHANCED WEBSOCKET STANDARDIZATION METHODS =====

  /**
   * Send a standardized message to a WebSocket connection
   * @param ws WebSocket connection
   * @param message Standardized message to send
   */
  public sendStandardMessage(
    ws: ServerWebSocket<CustomWebSocketData>,
    message: IWebSocketMessage,
  ): void {
    try {
      // Check rate limiting
      if (!this.checkRateLimitForConnection(ws)) {
        const rateLimitError = WebSocketUtils.rateLimited(
          this.getRateLimitRetryAfter(ws.data.userId),
          message.meta.correlationId,
        );
        WebSocketUtils.send(ws, rateLimitError);
        this.metrics.rateLimitHits++;
        return;
      }

      // Validate message if validation is enabled
      if (this.config.enableValidation && !WebSocketUtils.validate(message)) {
        const validationError = WebSocketUtils.validationError(
          [
            {
              field: "message",
              message: "Invalid message format",
              code: "INVALID_FORMAT",
            },
          ],
          message.meta.correlationId,
        );
        WebSocketUtils.send(ws, validationError);
        this.metrics.validationFailures++;
        return;
      }

      // Send the message using WebSocketUtils
      WebSocketUtils.send(ws, message);

      // Update metrics
      this.updateMessageMetrics(message);

      this.logger.debug("Standardized message sent", undefined, {
        messageId: message.meta.id,
        type: message.type,
        userId: ws.data.userId,
      });
    } catch (error) {
      this.logger.error("Failed to send standardized message", undefined, {
        error,
        messageId: message.meta?.id,
        userId: ws.data.userId,
      });
    }
  }

  /**
   * Send a request message and wait for response with correlation tracking
   * @param ws WebSocket connection
   * @param message Request message
   * @param timeout Optional timeout in milliseconds
   * @returns Promise that resolves with the response message
   */
  public sendRequest<T>(
    ws: ServerWebSocket<CustomWebSocketData>,
    message: IWebSocketMessage,
    timeout?: number,
  ): Promise<IWebSocketMessage<T>> {
    return new Promise((resolve, reject) => {
      if (!this.config.enableCorrelation) {
        reject(new Error("Correlation tracking is disabled"));
        return;
      }

      const correlationId = message.meta.correlationId || nanoid();
      const timeoutMs = timeout || this.config.defaultCorrelationTimeout;

      // Store correlation info
      const correlationInfo: ICorrelationInfo = {
        messageId: message.meta.id,
        timestamp: Date.now(),
        resolve,
        reject,
      };

      // Set up timeout
      const timeoutHandle = setTimeout(() => {
        this.correlationTimeouts.delete(correlationId);
        this.metrics.correlationTimeouts++;
        reject(new Error(`Request timeout after ${timeoutMs}ms`));
      }, timeoutMs);

      correlationInfo.timeout = timeoutHandle;
      this.correlationTimeouts.set(correlationId, timeoutHandle);

      // Store correlation in enhanced data
      const userId = ws.data.userId;
      const enhancedData = this.getOrCreateEnhancedData(userId);
      enhancedData.correlationMap.set(correlationId, correlationInfo);

      // Update message with correlation ID
      message.meta.correlationId = correlationId;

      // Send the request
      this.sendStandardMessage(ws, message);
    });
  }

  /**
   * Handle incoming response message with correlation tracking
   * @param ws WebSocket connection
   * @param message Response message
   */
  public handleCorrelatedResponse(
    ws: ServerWebSocket<CustomWebSocketData>,
    message: IWebSocketMessage,
  ): boolean {
    if (!this.config.enableCorrelation || !message.meta.correlationId) {
      return false;
    }

    const correlationId = message.meta.correlationId;
    const userId = ws.data.userId;
    const enhancedData = this.enhancedConnections.get(userId);

    if (!enhancedData) {
      return false;
    }

    const correlationInfo = enhancedData.correlationMap.get(correlationId);
    if (!correlationInfo) {
      return false;
    }

    // Clear timeout
    if (correlationInfo.timeout) {
      clearTimeout(correlationInfo.timeout);
      this.correlationTimeouts.delete(correlationId);
    }

    // Remove correlation
    enhancedData.correlationMap.delete(correlationId);

    // Resolve the promise
    if (correlationInfo.resolve) {
      correlationInfo.resolve(message);
    } else if (correlationInfo.callback) {
      correlationInfo.callback(message);
    }

    return true;
  }

  /**
   * Subscribe to a topic with standardized format
   * @param ws WebSocket connection
   * @param topic Topic to subscribe to
   */
  public subscribe(
    ws: ServerWebSocket<CustomWebSocketData>,
    topic: string,
  ): void {
    try {
      const userId = ws.data.userId;
      if (!userId) {
        const errorMessage = WebSocketUtils.authenticationRequired();
        WebSocketUtils.send(ws, errorMessage);
        return;
      }

      // Check rate limiting for subscription operations
      if (!this.checkRateLimitForConnection(ws)) {
        const rateLimitError = WebSocketUtils.rateLimited(
          this.getRateLimitRetryAfter(userId),
        );
        WebSocketUtils.send(ws, rateLimitError);
        this.metrics.rateLimitHits++;
        return;
      }

      // Get or create subscription set for user
      let userSubscriptions = this.subscriptionManager.get(userId);
      if (!userSubscriptions) {
        userSubscriptions = new Set<string>();
        this.subscriptionManager.set(userId, userSubscriptions);
      }

      // Add subscription
      userSubscriptions.add(topic);

      // Update enhanced data
      const enhancedData = this.getOrCreateEnhancedData(userId);
      enhancedData.subscriptions.add(topic);

      // Send confirmation using standardized format
      const successMessage = WebSocketUtils.success("SUBSCRIPTION_ADDED", {
        topic,
        timestamp: new Date(),
        totalSubscriptions: userSubscriptions.size,
      });
      WebSocketUtils.send(ws, successMessage);

      this.logger.debug("User subscribed to topic", undefined, {
        userId,
        topic,
        totalSubscriptions: userSubscriptions.size,
      });
    } catch (error) {
      this.logger.error("Failed to subscribe to topic", undefined, {
        error,
        userId: ws.data.userId,
        topic,
      });

      const errorMessage = WebSocketUtils.internalError("Subscription failed");
      WebSocketUtils.send(ws, errorMessage);
    }
  }

  /**
   * Unsubscribe from a topic
   * @param ws WebSocket connection
   * @param topic Topic to unsubscribe from
   */
  public unsubscribe(
    ws: ServerWebSocket<CustomWebSocketData>,
    topic: string,
  ): void {
    try {
      const userId = ws.data.userId;
      if (!userId) {
        const errorMessage = WebSocketUtils.authenticationRequired();
        WebSocketUtils.send(ws, errorMessage);
        return;
      }

      // Check rate limiting for subscription operations
      if (!this.checkRateLimitForConnection(ws)) {
        const rateLimitError = WebSocketUtils.rateLimited(
          this.getRateLimitRetryAfter(userId),
        );
        WebSocketUtils.send(ws, rateLimitError);
        this.metrics.rateLimitHits++;
        return;
      }

      // Remove from subscription manager
      const userSubscriptions = this.subscriptionManager.get(userId);
      if (userSubscriptions) {
        userSubscriptions.delete(topic);
        if (userSubscriptions.size === 0) {
          this.subscriptionManager.delete(userId);
        }
      }

      // Update enhanced data
      const enhancedData = this.enhancedConnections.get(userId);
      if (enhancedData) {
        enhancedData.subscriptions.delete(topic);
      }

      // Send confirmation using standardized format
      const successMessage = WebSocketUtils.success("SUBSCRIPTION_REMOVED", {
        topic,
        timestamp: new Date(),
        totalSubscriptions: userSubscriptions ? userSubscriptions.size : 0,
      });
      WebSocketUtils.send(ws, successMessage);

      this.logger.debug("User unsubscribed from topic", undefined, {
        userId,
        topic,
        totalSubscriptions: userSubscriptions ? userSubscriptions.size : 0,
      });
    } catch (error) {
      this.logger.error("Failed to unsubscribe from topic", undefined, {
        error,
        userId: ws.data.userId,
        topic,
      });

      const errorMessage = WebSocketUtils.internalError(
        "Unsubscription failed",
      );
      WebSocketUtils.send(ws, errorMessage);
    }
  }

  /**
   * Check rate limiting for a WebSocket connection with enhanced features
   * @param ws WebSocket connection
   * @param rateLimitGroup Optional rate limit group (default, messaging, voice, etc.)
   * @returns Whether the request is within rate limits
   */
  public checkRateLimitForConnection(
    ws: ServerWebSocket<CustomWebSocketData>,
    rateLimitGroup: string = "default",
  ): boolean {
    const userId = ws.data.userId;
    if (!userId) {
      return false;
    }

    const now = Date.now();
    const bucketKey = `${userId}:${rateLimitGroup}`;
    let bucket = this.rateLimitBuckets.get(bucketKey);

    // Get rate limit config for the specific group
    const rateLimitConfig =
      this.config.rateLimits[rateLimitGroup] || this.config.rateLimits.default;

    if (!bucket) {
      // Initialize bucket
      this.rateLimitBuckets.set(bucketKey, {
        tokens: rateLimitConfig.maxRequests - 1,
        lastRefill: now,
      });
      return true;
    }

    // Refill tokens based on time elapsed
    const timeSinceLastRefill = now - bucket.lastRefill;
    const tokensToAdd = Math.floor(
      (timeSinceLastRefill / rateLimitConfig.windowMs) *
        rateLimitConfig.maxRequests,
    );

    if (tokensToAdd > 0) {
      bucket.tokens = Math.min(
        rateLimitConfig.maxRequests,
        bucket.tokens + tokensToAdd,
      );
      bucket.lastRefill = now;
    }

    // Check if tokens are available
    if (bucket.tokens > 0) {
      bucket.tokens--;

      // Update enhanced data
      const enhancedData = this.enhancedConnections.get(userId);
      if (enhancedData) {
        enhancedData.rateLimitTokens = bucket.tokens;
      }

      return true;
    }

    // Rate limit exceeded
    this.logger.warn("Rate limit exceeded", undefined, {
      userId,
      rateLimitGroup,
      tokensRemaining: bucket.tokens,
    });

    return false;
  }

  /**
   * Get rate limit retry after time with enhanced support for different rate limit groups
   * @param userId User ID
   * @param rateLimitGroup Optional rate limit group
   * @returns Retry after time in seconds
   */
  private getRateLimitRetryAfter(
    userId: string,
    rateLimitGroup: string = "default",
  ): number {
    const bucketKey = `${userId}:${rateLimitGroup}`;
    const bucket = this.rateLimitBuckets.get(bucketKey);

    if (!bucket) {
      return 0;
    }

    const rateLimitConfig =
      this.config.rateLimits[rateLimitGroup] || this.config.rateLimits.default;
    const now = Date.now();
    const timeUntilRefill =
      rateLimitConfig.windowMs - (now - bucket.lastRefill);

    return Math.ceil(Math.max(0, timeUntilRefill) / 1000);
  }

  /**
   * Handle heartbeat for a WebSocket connection
   * @param ws WebSocket connection
   */
  public handleHeartbeat(ws: ServerWebSocket<CustomWebSocketData>): void {
    const userId = ws.data.userId;
    if (!userId) {
      return;
    }

    const enhancedData = this.getOrCreateEnhancedData(userId);
    enhancedData.lastHeartbeat = Date.now();

    // Send heartbeat response
    const heartbeatMessage = WebSocketUtils.event("HEARTBEAT_ACK", {
      timestamp: new Date(),
    });
    WebSocketUtils.send(ws, heartbeatMessage);

    this.logger.debug("Heartbeat handled", undefined, { userId });
  }

  /**
   * Start heartbeat monitoring for a connection
   * @param ws WebSocket connection
   */
  public startHeartbeat(ws: ServerWebSocket<CustomWebSocketData>): void {
    const userId = ws.data.userId;
    if (!userId) {
      return;
    }

    // Clear existing heartbeat if any
    this.stopHeartbeat(userId);

    const interval = setInterval(() => {
      if (ws.readyState !== WebSocket.OPEN) {
        this.stopHeartbeat(userId);
        return;
      }

      const heartbeatMessage = WebSocketUtils.event("HEARTBEAT", {
        timestamp: new Date(),
      });
      WebSocketUtils.send(ws, heartbeatMessage);
    }, this.config.heartbeatInterval);

    this.heartbeatIntervals.set(userId, interval);

    this.logger.debug("Heartbeat started", undefined, {
      userId,
      interval: this.config.heartbeatInterval,
    });
  }

  /**
   * Stop heartbeat monitoring for a user
   * @param userId User ID
   */
  public stopHeartbeat(userId: string): void {
    const interval = this.heartbeatIntervals.get(userId);
    if (interval) {
      clearInterval(interval);
      this.heartbeatIntervals.delete(userId);
      this.logger.debug("Heartbeat stopped", undefined, { userId });
    }
  }

  /**
   * Get or create enhanced data for a user
   * @param userId User ID
   * @returns Enhanced WebSocket data
   */
  private getOrCreateEnhancedData(userId: string): IEnhancedWebSocketData {
    let enhancedData = this.enhancedConnections.get(userId);
    if (!enhancedData) {
      enhancedData = {
        userId,
        token: "",
        isAlive: true,
        subscriptions: new Set<string>(),
        lastHeartbeat: Date.now(),
        messageCount: 0,
        rateLimitTokens: this.config.rateLimits.default.maxRequests,
        correlationMap: new Map<string, ICorrelationInfo>(),
      };
      this.enhancedConnections.set(userId, enhancedData);
    }
    return enhancedData;
  }

  /**
   * Clean up expired correlations
   */
  private cleanupExpiredCorrelations(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [userId, enhancedData] of this.enhancedConnections.entries()) {
      for (const [
        correlationId,
        correlationInfo,
      ] of enhancedData.correlationMap.entries()) {
        if (
          now - correlationInfo.timestamp >
          this.config.defaultCorrelationTimeout
        ) {
          // Clear timeout if exists
          if (correlationInfo.timeout) {
            clearTimeout(correlationInfo.timeout);
            this.correlationTimeouts.delete(correlationId);
          }

          // Reject promise if exists
          if (correlationInfo.reject) {
            correlationInfo.reject(new Error("Correlation expired"));
          }

          // Remove correlation
          enhancedData.correlationMap.delete(correlationId);
          cleanedCount++;
        }
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug("Cleaned up expired correlations", undefined, {
        cleanedCount,
      });
    }
  }

  /**
   * Update message metrics
   * @param message WebSocket message
   */
  private updateMessageMetrics(message: IWebSocketMessage): void {
    this.metrics.messagesPerSecond++;

    const messageSize = JSON.stringify(message).length;
    this.metrics.bytesSent += messageSize;
  }

  /**
   * Update connection and performance metrics
   */
  private updateMetrics(): void {
    this.metrics.activeConnections = this.clients.size;

    // Reset per-second counters
    this.metrics.messagesPerSecond = 0;

    this.logger.debug("Metrics updated", undefined, {
      activeConnections: this.metrics.activeConnections,
      rateLimitHits: this.metrics.rateLimitHits,
      correlationTimeouts: this.metrics.correlationTimeouts,
      validationFailures: this.metrics.validationFailures,
    });
  }

  /**
   * Get current WebSocket metrics
   * @returns Current metrics
   */
  public getMetrics(): IWebSocketMetrics {
    return { ...this.metrics };
  }

  /**
   * Get WebSocket configuration
   * @returns Current configuration
   */
  public getConfig(): IWebSocketConfig {
    return { ...this.config };
  }

  /**
   * Update WebSocket configuration
   * @param newConfig Partial configuration to update
   */
  public updateConfig(newConfig: Partial<IWebSocketConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.logger.info("WebSocket configuration updated", undefined, newConfig);
  }

  /**
   * Enhanced connection cleanup with standardized features
   * @param ws WebSocket connection
   */
  public enhancedCleanup(ws: ServerWebSocket<CustomWebSocketData>): void {
    const userId = ws.data.userId;
    if (!userId) {
      return;
    }

    // Stop heartbeat
    this.stopHeartbeat(userId);

    // Clean up subscriptions
    this.subscriptionManager.delete(userId);

    // Clean up enhanced data
    const enhancedData = this.enhancedConnections.get(userId);
    if (enhancedData) {
      // Clean up correlations
      for (const [
        correlationId,
        correlationInfo,
      ] of enhancedData.correlationMap.entries()) {
        if (correlationInfo.timeout) {
          clearTimeout(correlationInfo.timeout);
          this.correlationTimeouts.delete(correlationId);
        }
        if (correlationInfo.reject) {
          correlationInfo.reject(new Error("Connection closed"));
        }
      }

      this.enhancedConnections.delete(userId);
    }

    // Clean up rate limiting
    this.rateLimitBuckets.delete(userId);

    this.logger.debug("Enhanced cleanup completed", undefined, { userId });
  }
}
