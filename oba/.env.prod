# Database - Postgres (Production)
POSTGRES_DB=oba_db
POSTGRES_PASSWORD=
POSTGRES_PORT=5432
POSTGRES_SCHEMA=postgresql
POSTGRES_USERNAME=berk

# This is the host for Docker Postgres Image in docker-compose.yaml
POSTGRES_HOST=database

# JWT Token
JWT_SECRET_KEY=jwtsecret
JWT_SUBJECT=jwtsubject
JWT_TOKEN_PREFIX=oba
JWT_ALGORITHM=HS256
JWT_MIN=60
JWT_HOUR=23
JWT_DAY=6

# Hash Functions
HASHING_ALGORITHM_LAYER_1=bcrypt
HASHING_ALGORITHM_LAYER_2=argon2
HASHING_SALT=saltysalt

# Development-specific settings
NODE_ENV=production
DEBUG=false
LOG_LEVEL=debug

# STUN/TURN Server (Development)
STUN_SERVER_URL=stun:stun.l.google.com:19302
TURN_SERVER_URL=turn:coolify.berkormanli.dev:3478
TURN_SERVER_USERNAME=myuser
TURN_SERVER_CREDENTIAL=mypassword
