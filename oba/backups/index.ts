// --- Server ---

import type { ServerWebSocket } from "bun";
import {
  PublicRoute<PERSON><PERSON><PERSON>,
  ProtectedRouteHand<PERSON>,
  WebSocketRouteHandler,
} from "./class/routeHandler.ts";
import Router from "./class/router";
import authMiddleware from "./middlewares/authMiddleware";
import loggerMiddleware from "./middlewares/loggerMiddleware";
import { type WebSocketMessage } from "./types.ts";
import { EventTypes } from "@kurultai/oba-types";
import { WebSocketManager } from "./manager/websocket.manager.ts";
import { corsMiddleware } from "./middlewares/corsMiddleware.ts";

const router = new Router();

import * as jose from "jose";
import { AuthenticationError } from "./class/errors.ts";
import {
  validateUser,
  createDirectMessage,
  getDirectMessageById,
  editDirectMessage,
  deleteDirectMessage,
} from "./db/utils.ts";
import { db } from "./db/index.ts";
import {
  authenticate<PERSON><PERSON><PERSON><PERSON>,
  login<PERSON>andler,
  registerHandler,
  updateUserProfileHandler,
  requestPasswordResetHandler,
  resetPasswordHandler,
  verifyEmailHandler,
  refreshTokenHandler,
  logoutHandler,
} from "./handlers/auth.ts";
import {
  sendFriendRequestHandler,
  acceptFriendRequestHandler,
  rejectFriendRequestHandler,
  cancelFriendRequestHandler,
  removeFriendHandler,
  blockUserHandler,
  unblockUserHandler,
  getUserFriendsHandler,
  getPendingFriendRequestsHandler,
  getSentFriendRequestsHandler,
  getBlockedUsersHandler,
} from "./handlers/friends.ts";
import {
  updateUserStatusHandler,
  getUserStatusHandler,
  getOnlineFriendsHandler,
  updateLastActiveHandler,
} from "./handlers/status.ts";
import {
  createChannelHandler,
  updateChannelHandler,
  deleteChannelHandler,
  getChannelDetailsHandler,
} from "./handlers/channels.ts";
import {
  createRoleHandler,
  updateRoleHandler,
  deleteRoleHandler,
  getServerRolesHandler,
  getUserRolesHandler,
  assignRoleHandler,
  removeRoleHandler,
} from "./handlers/roles.ts";
import {
  kickMemberHandler,
  banMemberHandler,
  unbanMemberHandler,
  changeUserRolesHandler,
  getBannedUsersHandler,
  getServerMembersHandler,
} from "./handlers/serverMembers.ts";
import {
  deleteMessageHandler,
  editMessageHandler,
  getChannelMessagesHandler,
} from "./handlers/messages.ts";
import {
  createServerInviteLinkHandler,
  getServerInformationFromInviteHandler,
  serverDetailsHandler,
  serverJoinHandler,
  updateServerHandler,
  userServersHandler,
} from "./handlers/servers.ts";
import {
  deleteDirectMessageHandler,
  editDirectMessageHandler,
  getDirectMessagesHandler,
  getUserContactsHandler,
  markDirectMessageAsReadHandler,
  sendDirectMessageHandler,
} from "./handlers/directMessages.ts";
import {
  addReactionHandler,
  removeReactionHandler,
  getReactionsHandler,
} from "./handlers/reactions.ts";
import { VoiceWebSocketManager } from "./manager/websocket.ts";
import { presignedMinio } from "./handlers/minio.ts";
// ... other imports

import {
  WebRTCSignalingService,
  WebRTCEventTypes,
} from "./services/webrtc.service";
import { SFUService, SFUEventTypes } from "./services/sfu.service";
import { handleBinaryVoiceData } from "./handlers/binaryVoiceHandler";
import {
  parseVoiceMessage,
  validateVoiceMetadata,
} from "./utils/binaryProtocol";

router.add(
  "/api/presigned",
  new PublicRouteHandler(presignedMinio, [loggerMiddleware]),
);

router.add(
  "/api/login",
  new PublicRouteHandler(loginHandler, [loggerMiddleware]),
);

router.add(
  "/api/register",
  new PublicRouteHandler(registerHandler, [loggerMiddleware]),
);

router.add(
  "/api/getUserServerlist",
  new PublicRouteHandler(userServersHandler, [loggerMiddleware]),
);

router.add(
  "/api/getServerDetails",
  new PublicRouteHandler(serverDetailsHandler, [loggerMiddleware]),
);

router.add(
  "/api/getUserDetails",
  new PublicRouteHandler(authenticateUserToken, [loggerMiddleware]),
);

router.add(
  "/api/updateUserProfile",
  new PublicRouteHandler(updateUserProfileHandler, [loggerMiddleware]),
);

// Password reset routes
router.add(
  "/api/request-password-reset",
  new PublicRouteHandler(requestPasswordResetHandler, [loggerMiddleware]),
);

router.add(
  "/api/reset-password",
  new PublicRouteHandler(resetPasswordHandler, [loggerMiddleware]),
);

// Email verification route
router.add(
  "/api/verify-email",
  new PublicRouteHandler(verifyEmailHandler, [loggerMiddleware]),
);

// Token refresh and logout routes
router.add(
  "/api/refresh-token",
  new PublicRouteHandler(refreshTokenHandler, [loggerMiddleware]),
);

router.add(
  "/api/logout",
  new PublicRouteHandler(logoutHandler, [loggerMiddleware]),
);

// Message routes
router.add(
  "/api/messages/edit",
  new PublicRouteHandler(editMessageHandler, [loggerMiddleware]),
);

router.add(
  "/api/messages/delete",
  new PublicRouteHandler(deleteMessageHandler, [loggerMiddleware]),
);

router.add(
  "/api/messages",
  new PublicRouteHandler(getChannelMessagesHandler, [loggerMiddleware]),
);

router.add(
  "/api/createServerInvite",
  new PublicRouteHandler(createServerInviteLinkHandler, [loggerMiddleware]),
);

router.add(
  "/api/invites",
  new PublicRouteHandler(getServerInformationFromInviteHandler, [
    loggerMiddleware,
  ]),
);

router.add(
  "/api/join",
  new PublicRouteHandler(serverJoinHandler, [loggerMiddleware]),
);

router.add(
  "/api/updateServer",
  new PublicRouteHandler(updateServerHandler, [loggerMiddleware]),
);

// Direct Message routes
router.add(
  "/api/directMessages/send",
  new PublicRouteHandler(sendDirectMessageHandler, [loggerMiddleware]),
);

router.add(
  "/api/directMessages/edit",
  new PublicRouteHandler(editDirectMessageHandler, [loggerMiddleware]),
);

router.add(
  "/api/directMessages/delete",
  new PublicRouteHandler(deleteDirectMessageHandler, [loggerMiddleware]),
);

router.add(
  "/api/directMessages/markAsRead",
  new PublicRouteHandler(markDirectMessageAsReadHandler, [loggerMiddleware]),
);

router.add(
  "/api/directMessages",
  new PublicRouteHandler(getDirectMessagesHandler, [loggerMiddleware]),
);

router.add(
  "/api/directMessages/contacts",
  new PublicRouteHandler(getUserContactsHandler, [loggerMiddleware]),
);

// Message Reaction routes
router.add(
  "/api/reactions/add",
  new PublicRouteHandler(addReactionHandler, [loggerMiddleware]),
);

router.add(
  "/api/reactions/remove",
  new PublicRouteHandler(removeReactionHandler, [loggerMiddleware]),
);

router.add(
  "/api/reactions",
  new PublicRouteHandler(getReactionsHandler, [loggerMiddleware]),
);

// Friend Management routes
router.add(
  "/api/friends/send-request",
  new PublicRouteHandler(sendFriendRequestHandler, [loggerMiddleware]),
);

router.add(
  "/api/friends/accept-request",
  new PublicRouteHandler(acceptFriendRequestHandler, [loggerMiddleware]),
);

router.add(
  "/api/friends/reject-request",
  new PublicRouteHandler(rejectFriendRequestHandler, [loggerMiddleware]),
);

router.add(
  "/api/friends/cancel-request",
  new PublicRouteHandler(cancelFriendRequestHandler, [loggerMiddleware]),
);

router.add(
  "/api/friends/remove",
  new PublicRouteHandler(removeFriendHandler, [loggerMiddleware]),
);

router.add(
  "/api/friends/block",
  new PublicRouteHandler(blockUserHandler, [loggerMiddleware]),
);

router.add(
  "/api/friends/unblock",
  new PublicRouteHandler(unblockUserHandler, [loggerMiddleware]),
);

router.add(
  "/api/friends",
  new PublicRouteHandler(getUserFriendsHandler, [loggerMiddleware]),
);

router.add(
  "/api/friends/pending",
  new PublicRouteHandler(getPendingFriendRequestsHandler, [loggerMiddleware]),
);

router.add(
  "/api/friends/sent",
  new PublicRouteHandler(getSentFriendRequestsHandler, [loggerMiddleware]),
);

router.add(
  "/api/friends/blocked",
  new PublicRouteHandler(getBlockedUsersHandler, [loggerMiddleware]),
);

// User Status routes
router.add(
  "/api/status/update",
  new PublicRouteHandler(updateUserStatusHandler, [loggerMiddleware]),
);

router.add(
  "/api/status",
  new PublicRouteHandler(getUserStatusHandler, [loggerMiddleware]),
);

router.add(
  "/api/status/friends/online",
  new PublicRouteHandler(getOnlineFriendsHandler, [loggerMiddleware]),
);

router.add(
  "/api/status/last-active",
  new PublicRouteHandler(updateLastActiveHandler, [loggerMiddleware]),
);

// Channel Management routes
router.add(
  "/api/channels/create",
  new PublicRouteHandler(createChannelHandler, [loggerMiddleware]),
);

router.add(
  "/api/channels/update",
  new PublicRouteHandler(updateChannelHandler, [loggerMiddleware]),
);

router.add(
  "/api/channels/delete",
  new PublicRouteHandler(deleteChannelHandler, [loggerMiddleware]),
);

router.add(
  "/api/channels/details",
  new PublicRouteHandler(getChannelDetailsHandler, [loggerMiddleware]),
);

// Role Management routes
router.add(
  "/api/roles/create",
  new PublicRouteHandler(createRoleHandler, [loggerMiddleware]),
);

router.add(
  "/api/roles/update",
  new PublicRouteHandler(updateRoleHandler, [loggerMiddleware]),
);

router.add(
  "/api/roles/delete",
  new PublicRouteHandler(deleteRoleHandler, [loggerMiddleware]),
);

router.add(
  "/api/roles/server",
  new PublicRouteHandler(getServerRolesHandler, [loggerMiddleware]),
);

router.add(
  "/api/roles/user",
  new PublicRouteHandler(getUserRolesHandler, [loggerMiddleware]),
);

router.add(
  "/api/roles/assign",
  new PublicRouteHandler(assignRoleHandler, [loggerMiddleware]),
);

router.add(
  "/api/roles/remove",
  new PublicRouteHandler(removeRoleHandler, [loggerMiddleware]),
);

// Server Member Management routes
router.add(
  "/api/members/kick",
  new PublicRouteHandler(kickMemberHandler, [loggerMiddleware]),
);

router.add(
  "/api/members/ban",
  new PublicRouteHandler(banMemberHandler, [loggerMiddleware]),
);

router.add(
  "/api/members/unban",
  new PublicRouteHandler(unbanMemberHandler, [loggerMiddleware]),
);

router.add(
  "/api/members/roles",
  new PublicRouteHandler(changeUserRolesHandler, [loggerMiddleware]),
);

router.add(
  "/api/members/banned",
  new PublicRouteHandler(getBannedUsersHandler, [loggerMiddleware]),
);

router.add(
  "/api/members/list",
  new PublicRouteHandler(getServerMembersHandler, [loggerMiddleware]),
);

// Public route
router.add(
  "/",
  new PublicRouteHandler(() => new Response("Welcome!"), [loggerMiddleware]),
);

// Another public route
router.add(
  "/about",
  new PublicRouteHandler(() => new Response("About us"), [loggerMiddleware]),
);

// Protected route
router.add(
  "/profile",
  new ProtectedRouteHandler(
    (req: Request) => {
      const user = req.headers.get("X-Authenticated-User");
      return new Response(`Profile page. Welcome, ${user}!`);
    },
    [loggerMiddleware, authMiddleware], // You can add middlewares in the constructor
  ),
);

//Dynamic route
router.add(
  "/users/:id",
  new PublicRouteHandler(
    (req: Request, params?: Record<string, string>) => {
      const userId = params?.id;
      return new Response(`User profile for user ID: ${userId}`);
    },
    [loggerMiddleware], // Middleware for dynamic route
  ),
);

// --- Bun Server ---

const wsManager = new WebSocketManager();
const voiceWsManager = new VoiceWebSocketManager();

interface User {
  id: string;
  name: string;
  avatar: string;
  speaking: boolean;
  muted: boolean;
  deafened: boolean;
}

interface CustomWebSocketData {
  userId: string;
  serverId?: string;
  channelId?: string;
  user?: User;
  token: string;
  isAuthenticated?: boolean;
  isAlive: boolean;
  type?: string;
}

// Use a WeakMap to track connection status
const connectionStatus = new WeakMap<
  ServerWebSocket<CustomWebSocketData>,
  { isAlive: boolean }
>();

const corsHeaders = {
  "Access-Control-Allow-Origin": "*", // Adjust as needed for security
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

const PING_INTERVAL = 60000; // 60 seconds

// Change this later on for more robust approach.
function authenticateUser() {
  return true;
}

const stunTurnConfig = {
  iceServers: [
    {
      urls: [
        "stun:stun.l.google.com:19302", // Public STUN server (Google)
      ],
    },
    {
      urls: [
        // Domain ayarları yapıldıktan sonra çalışacak.
        "turn:coolify.berkormanli.dev:3478", // TURN server - check port!
      ],
      credential: "mypassword",
      username: "myuser",
    },
  ],
};

const server = Bun.serve({
  port: 3000,
  websocket: {
    async open(ws: ServerWebSocket<CustomWebSocketData>) {
      //console.log(ws)
      ws.send(
        JSON.stringify({
          type: "auth_required",
          message: "Please authenticate",
        }),
      );

      // Initialize connection status
      connectionStatus.set(ws, { isAlive: true });

      //wsManager.add(ws);
      //console.log("WebSocket opened", ws.data);
      // You might want to send a welcome message or initial data here
      //ws.send(JSON.stringify({ type: "WELCOME", data: "Connected!" }));
      // Ping-pong logic
      //ws.data.isAlive = true;
      //connectionStatus.set(ws, { isAlive: true });
    },
    ping(ws) {
      // When server sends ping
      //console.log(`Ping sent to user ${ws.data.userId}`);
    },
    pong(ws) {
      // When server receives pong
      //console.log(`Pong received from user ${ws.data.userId}`);
      const status = connectionStatus.get(ws);
      if (status) {
        status.isAlive = true;
      }
    },
    message: async function (
      ws: ServerWebSocket<CustomWebSocketData>,
      rawMessage,
    ): Promise<void> {
      const { isAuthenticated, userId, token, isAlive } = ws.data;

      if (!isAuthenticated) {
        if (typeof rawMessage === "string") {
          const message = JSON.parse(rawMessage);
          const authResult = authenticateUser(); // for now it returns always true;
          if (authResult) {
            ws.data.isAuthenticated = true;
            ws.data.userId = message.data.userId;
            ws.data.token = message.data.token;
            connectionStatus.set(ws, { isAlive: true });
            if (message.data.type === "voice") {
              ws.data.channelId = message.data.channelId;
              ws.data.serverId = message.data.serverId;
              ws.data.user = message.data.user;
              ws.send(
                JSON.stringify({
                  type: "auth_success",
                  message: "Authentication successful",
                  stunTurnConfig: stunTurnConfig, // Send STUN/TURN config to client on auth success
                }),
              );
              // Handle connection (will update user status to ONLINE if authenticated)
              await wsManager.handleConnection(ws);
              voiceWsManager.add(ws);
              //console.log("voice ws manager should add the socket now")
            } else {
              ws.data.channelId = message.data.channelId;
              ws.data.serverId = message.data.serverId;
              ws.data.user = message.data.user;
              //console.log("websocket added:",ws)
              wsManager.add(ws);

              ws.send(
                JSON.stringify({
                  type: "auth_success",
                  message: "Authentication successful",
                  stunTurnConfig: stunTurnConfig, // Send STUN/TURN config to client on auth success
                }),
              );
            }
          } else {
            ws.send(
              JSON.stringify({
                type: "auth_failure",
                message: "Authentication failed",
              }),
            );
            ws.close(); // Close the connection
          }
        } else {
          ws.send(
            JSON.stringify({
              type: "auth_failure",
              message: "Authentication message must be a string",
            }),
          );
          ws.close();
        }
      } else if (
        rawMessage instanceof Blob ||
        rawMessage instanceof ArrayBuffer
      ) {
        if (!ws.data.serverId || !ws.data.channelId) return;

        const arrayBuffer =
          rawMessage instanceof Blob
            ? await rawMessage.arrayBuffer()
            : rawMessage;
        // maybe try and do something here?
        //parseVoiceMessage(arrayBuffer);
        sfuService.handleVoiceData(ws, arrayBuffer);
      } else if (rawMessage instanceof Buffer) {
        if (!ws.data.serverId || !ws.data.channelId) return;

        const arrayBufferLike = rawMessage.buffer;
        const slicedBuffer = arrayBufferLike.slice(
          rawMessage.byteOffset,
          rawMessage.byteOffset + rawMessage.byteLength,
        );

        if (slicedBuffer instanceof ArrayBuffer) {
          voiceWsManager.broadcast(
            ws.data.userId,
            ws.data.serverId,
            ws.data.channelId,
            slicedBuffer,
          );
        } else {
          // Handle SharedArrayBuffer case (if needed for your application)
          console.error("Unexpected SharedArrayBuffer encountered!");
          // Option 1: Convert to ArrayBuffer (if data doesn't need to be shared)
          const arrayBuffer = new ArrayBuffer(slicedBuffer.byteLength);
          new Uint8Array(arrayBuffer).set(new Uint8Array(slicedBuffer));
          voiceWsManager.broadcast(
            ws.data.userId,
            ws.data.serverId,
            ws.data.channelId,
            arrayBuffer,
          );

          // Option 2: Throw an error or use a different communication mechanism
          // throw new Error("SharedArrayBuffer not supported for broadcast.");
        }
      } else {
        //console.log(rawMessage, typeof rawMessage);
        const message = JSON.parse(rawMessage);
        // Handle text messages (e.g., metadata, subscribe, unsubscribe)
        //connectionManager.routeMessage(username, type, parsedMessage);
        // !!!!!For test purposes only!!!!!
        //console.log(message)
        const user = message.sender || message.userId;
        // Handle different message types
        switch (message.type) {
          case EventTypes.MESSAGE_SEND:
            const data = {
              avatar: message.avatar,
              username: message.username,
              message: message.data,
              serverId: message.serverId,
              channelId: message.channelId,
            };
            wsManager.handleMessageSend(user, data);
            break;
          case EventTypes.MESSAGE_UPDATE:
            wsManager.handleMessageUpdate(user, {
              messageId: message.messageId,
              newContent: message.content,
              serverId: message.serverId,
              channelId: message.channelId,
            });
            break;
          case EventTypes.MESSAGE_DELETE:
            wsManager.handleMessageDelete(user, {
              messageId: message.messageId,
              serverId: message.serverId,
              channelId: message.channelId,
            });
            break;
          case EventTypes.MESSAGE_REACTION_ADD:
            wsManager.handleMessageReactionAdd(user, {
              messageId: message.messageId,
              emoji: message.emoji,
              serverId: message.serverId,
              channelId: message.channelId,
            });
            break;
          case EventTypes.MESSAGE_REACTION_REMOVE:
            wsManager.handleMessageReactionRemove(user, {
              messageId: message.messageId,
              emoji: message.emoji,
              serverId: message.serverId,
              channelId: message.channelId,
            });
            break;
          case "FRIEND_REQUEST_SEND": // EventTypes.FRIEND_REQUEST_SEND
            wsManager.handleFriendRequest(user, {
              friendId: message.friendId,
            });
            break;
          case "FRIEND_REQUEST_ACCEPT": // EventTypes.FRIEND_REQUEST_ACCEPT
            wsManager.handleFriendRequestAccept(user, {
              friendshipId: message.friendshipId,
            });
            break;
          case "FRIEND_REQUEST_REJECT": // EventTypes.FRIEND_REQUEST_REJECT
            wsManager.handleFriendRequestReject(user, {
              friendshipId: message.friendshipId,
            });
            break;
          case "FRIEND_REQUEST_CANCEL": // EventTypes.FRIEND_REQUEST_CANCEL
            wsManager.handleFriendRequestCancel(user, {
              friendshipId: message.friendshipId,
            });
            break;
          case "FRIEND_REMOVE": // EventTypes.FRIEND_REMOVE
            wsManager.handleFriendRemove(user, {
              friendshipId: message.friendshipId,
            });
            break;
          case "USER_BLOCK": // EventTypes.USER_BLOCK
            wsManager.handleUserBlock(user, {
              targetUserId: message.targetUserId,
            });
            break;
          case "USER_UNBLOCK": // EventTypes.USER_UNBLOCK
            wsManager.handleUserUnblock(user, {
              friendshipId: message.friendshipId,
            });
            break;
          case "USER_STATUS_UPDATE": // EventTypes.USER_STATUS_UPDATE
            wsManager.handleUserStatusUpdate(user, {
              status: message.status,
              statusMessage: message.statusMessage,
            });
            break;
          case "CHANNEL_UPDATE": // EventTypes.CHANNEL_UPDATE
            wsManager.handleChannelUpdate(user, {
              channelId: message.channelId,
              name: message.name,
              description: message.description,
              type: message.type,
              serverId: message.serverId,
            });
            break;
          case "CHANNEL_DELETE": // EventTypes.CHANNEL_DELETE
            wsManager.handleChannelDelete(user, {
              channelId: message.channelId,
              serverId: message.serverId,
            });
            break;
          // Direct message events
          case "DIRECT_MESSAGE_SEND":
            // Handle direct message sending
            const dmData = {
              senderId: user,
              receiverId: message.receiverId,
              content: message.content,
              attachments: message.attachments,
            };
            // Call the utility function directly
            createDirectMessage(
              db,
              dmData.senderId,
              dmData.receiverId,
              dmData.content,
              dmData.attachments,
            )
              .then((newMessage) => {
                // Broadcast to the receiver
                const directMessageEvent = {
                  type: "DIRECT_MESSAGE_SENT",
                  sender: user,
                  data: {
                    messageId: newMessage.id,
                    senderId: newMessage.senderId,
                    receiverId: newMessage.receiverId,
                    content: newMessage.content,
                    attachments: newMessage.attachments,
                    createdAt: newMessage.createdAt,
                  },
                };
                wsManager.broadcastToUser(
                  dmData.receiverId,
                  JSON.stringify(directMessageEvent),
                );
              })
              .catch((error) => {
                console.error("Error sending direct message:", error);
              });
            break;
          case "DIRECT_MESSAGE_UPDATE":
            // Handle direct message editing
            const dmUpdateData = {
              messageId: message.messageId,
              senderId: user,
              content: message.content,
            };
            // Get the message to verify ownership
            getDirectMessageById(db, dmUpdateData.messageId)
              .then((existingMessage) => {
                if (!existingMessage) {
                  console.error(
                    `Direct message ${dmUpdateData.messageId} not found`,
                  );
                  return;
                }

                // Verify the user is the message sender
                if (existingMessage.senderId !== dmUpdateData.senderId) {
                  console.error(
                    `User ${dmUpdateData.senderId} is not authorized to edit message ${dmUpdateData.messageId}`,
                  );
                  return;
                }

                // Update the message
                editDirectMessage(
                  db,
                  dmUpdateData.messageId,
                  dmUpdateData.content,
                )
                  .then((updatedMessage) => {
                    // Broadcast to the receiver
                    const updateEvent = {
                      type: "DIRECT_MESSAGE_UPDATED",
                      sender: user,
                      data: {
                        messageId: updatedMessage.id,
                        content: updatedMessage.content,
                        editedAt: updatedMessage.editedAt,
                        senderId: updatedMessage.senderId,
                        receiverId: updatedMessage.receiverId,
                      },
                    };

                    if (updatedMessage.receiverId) {
                      wsManager.broadcastToUser(
                        updatedMessage.receiverId,
                        JSON.stringify(updateEvent),
                      );
                    }
                  })
                  .catch((error) => {
                    console.error("Error updating direct message:", error);
                  });
              })
              .catch((error) => {
                console.error("Error getting direct message:", error);
              });
            break;
          case "DIRECT_MESSAGE_DELETE":
            // Handle direct message deletion
            const dmDeleteData = {
              messageId: message.messageId,
              senderId: user,
            };
            // Get the message to verify ownership
            getDirectMessageById(db, dmDeleteData.messageId)
              .then((existingMessage) => {
                if (!existingMessage) {
                  console.error(
                    `Direct message ${dmDeleteData.messageId} not found`,
                  );
                  return;
                }

                // Verify the user is the message sender
                if (existingMessage.senderId !== dmDeleteData.senderId) {
                  console.error(
                    `User ${dmDeleteData.senderId} is not authorized to delete message ${dmDeleteData.messageId}`,
                  );
                  return;
                }

                // Delete the message
                deleteDirectMessage(db, dmDeleteData.messageId)
                  .then((deletedMessage) => {
                    // Broadcast to the receiver
                    const deleteEvent = {
                      type: "DIRECT_MESSAGE_DELETED",
                      sender: user,
                      data: {
                        messageId: deletedMessage.id,
                        senderId: deletedMessage.senderId,
                        receiverId: deletedMessage.receiverId,
                      },
                    };

                    if (deletedMessage.receiverId) {
                      wsManager.broadcastToUser(
                        deletedMessage.receiverId,
                        JSON.stringify(deleteEvent),
                      );
                    }
                  })
                  .catch((error) => {
                    console.error("Error deleting direct message:", error);
                  });
              })
              .catch((error) => {
                console.error("Error getting direct message:", error);
              });
            break;
          case EventTypes.SERVER_CREATE:
            wsManager.createServer(ws, user, message.data);
            break;
          case EventTypes.CHANNEL_CREATE:
            wsManager.createChannel(ws, user, message.data);
            break;
          case EventTypes.SERVER_JOIN:
            wsManager.joinServer(ws, user, message.data);
            break;
          case EventTypes.CHANNEL_SUBSCRIBE:
            wsManager.subscribeChannel(
              ws,
              user,
              message.serverId,
              message.channelId,
            );
            break;
          case EventTypes.VOICE_DATA_SEND:
            wsManager.handleAudio(user, message.data);
            break;
          // WebRTC Signaling Handlers
          case WebRTCEventTypes.WEBRTC_OFFER: // or EventTypes.WEBRTC_OFFER if you modified @kurultai/oba-types
          case "webrtc_offer": // Fallback if you can't modify EventTypes
            handleWebRTCOffer(ws, message);
            break;
          case WebRTCEventTypes.WEBRTC_ANSWER: // or EventTypes.WEBRTC_ANSWER
          case "webrtc_answer":
            handleWebRTCAnswer(ws, message);
            break;
          case WebRTCEventTypes.WEBRTC_ICE_CANDIDATE: // or EventTypes.WEBRTC_ICE_CANDIDATE
          case "webrtc_ice_candidate":
            handleWebRTCIceCandidate(ws, message);
            break;
          case WebRTCEventTypes.WEBRTC_JOIN_ROOM:
          case "webrtc_join_room":
            webRTCSignalingService.handleJoinRoom(ws, message);
            break;
          case WebRTCEventTypes.WEBRTC_LEAVE_ROOM:
          case "webrtc_leave_room":
            webRTCSignalingService.handleLeaveRoom(ws, message);
            break;
          case WebRTCEventTypes.WEBRTC_MUTE_TOGGLE:
          case "webrtc_mute_toggle":
            webRTCSignalingService.handleMuteToggle(ws, message);
            break;
          case WebRTCEventTypes.WEBRTC_DEAFEN_TOGGLE:
          case "webrtc_deafen_toggle":
            webRTCSignalingService.handleDeafenToggle(ws, message);
            break;
          case WebRTCEventTypes.WEBRTC_SPEAKING_START:
          case "webrtc_speaking_start":
          case WebRTCEventTypes.WEBRTC_SPEAKING_END:
          case "webrtc_speaking_end":
            webRTCSignalingService.handleSpeakingChange(ws, message);
            break;
          // SFU Handlers
          case SFUEventTypes.SFU_JOIN:
          case "sfu_join":
            sfuService.handleJoin(ws, message);
            break;
          case SFUEventTypes.SFU_LEAVE:
          case "sfu_leave":
            sfuService.handleLeave(ws, message);
            break;
          case SFUEventTypes.SFU_OFFER:
          case "sfu_offer":
            sfuService.handleOffer(ws, message);
            break;
          case SFUEventTypes.SFU_ICE_CANDIDATE:
          case "sfu_ice_candidate":
            sfuService.handleIceCandidate(ws, message);
            break;
          case SFUEventTypes.SFU_MUTE_TOGGLE:
          case "sfu_mute_toggle":
            sfuService.handleMuteToggle(ws, message);
            break;
          case SFUEventTypes.SFU_DEAFEN_TOGGLE:
          case "sfu_deafen_toggle":
            sfuService.handleDeafenToggle(ws, message);
            break;
          case SFUEventTypes.SFU_SPEAKING_STATE:
          case "sfu_speaking_state":
            sfuService.handleSpeakingState(ws, message);
            break;
          default:
            console.error("Unknown message type:", message.type);
        }
      }

      //const userId = (ws.data as any).userId;
      //console.log("Received message:", rawMessage/* , "from user:", userId */);
      // Parse the incoming message (assuming it's JSON)
      /*       let message: WebSocketMessage;
      try {
        message = JSON.parse(rawMessage.toString()); // Assuming the message is a string
      } catch (error) {
        console.error("Invalid message format:", rawMessage);
        return;
      }
      // !!!!!For test purposes only!!!!!
      const user = message.sender || message.userId;
      // Handle different message types
      switch (message.type) {
        case EventTypes.MESSAGE_SEND:
          wsManager.handleMessageSend(user, message.data);
          break;
        case EventTypes.SERVER_CREATE:
          wsManager.createServer(ws, user, message.data);
          break;
        case EventTypes.CHANNEL_CREATE:
          wsManager.createChannel(ws, user, message.data);
          break;
        case EventTypes.SERVER_JOIN:
          wsManager.joinServer(ws, user, message.data);
          break;
        case EventTypes.VOICE_DATA_SEND:
          wsManager.handleAudio(user, message.data);
          break;
        default:
          console.error("Unknown message type:", message.type);
      } */
    },
    async close(
      ws: ServerWebSocket<CustomWebSocketData>,
      code: number,
      reason: string,
    ) {
      const { userId } = ws.data;
      //console.log(`WebSocket closed for user ${userId} with code ${code}, reason: ${reason}`);

      // Handle disconnection (will update user status to OFFLINE)
      await wsManager.handleDisconnection(ws);

      // Remove from connection tracking
      connectionStatus.delete(ws); // Remove from connection status map
    },
  }, // handlers
  async fetch(req, server) {
    const url = new URL(req.url);
    const matchedRoute = router.match(req);

    if (
      //req.headers.get("Upgrade") === "websocket" &&
      server.upgrade<CustomWebSocketData>(req, {
        data: {
          // Extract userId, other relevant data from the request, and add type
          userId: url.searchParams.get("userId") || "",
          token: url.searchParams.get("token") || "",
          isAuthenticated: false,
          isAlive: true,
        },
      })
    ) {
      return; // Do not return a Response in case of WebSocket upgrade
    }

    // Handle OPTIONS requests for CORS preflight
    if (req.method === "OPTIONS") {
      //console.log(req)
      return new Response(null, {
        status: 204,
        headers: corsHeaders,
      });
    }

    if (matchedRoute) {
      try {
        let response = matchedRoute.handler.handle(
          req,
          matchedRoute.params,
          server,
        );

        if (response instanceof Promise) {
          response = await response;
        }

        if (response) {
          let res: Response;

          // Check if the handler already returned a Response object
          if (response instanceof Response) {
            res = response; // Use it directly
            // Add CORS headers if they are not present already
            for (const [key, value] of Object.entries(corsHeaders)) {
              if (!res.headers.has(key)) {
                res.headers.set(key, value);
              }
            }
          } else {
            // Create a new Response, handling body and headers
            res = new Response(response, {
              headers: {
                ...corsHeaders, // Apply CORS headers
              },
            });
          }

          return res;
        }
      } catch (error) {
        // ... (error handling)
      }

      return;
    }

    return new Response("Not Found", { status: 404, headers: corsHeaders });
  },
  error(error) {
    return new Response(`<pre>${error}\n${error.stack}</pre>`, {
      headers: {
        "Content-Type": "text/html",
        ...corsHeaders,
      },
    });
  },
});

console.log(`Listening on http://localhost:3000`);

// Set up periodic ping/pong (heartbeat)
setInterval(() => {
  const allSocketsFlatMap = Array.from(
    wsManager.topicSubscriptions.values(),
  ).flatMap((socketSet) => Array.from(socketSet));
  for (const ws of allSocketsFlatMap) {
    const status = connectionStatus.get(ws);
    if (status && status.isAlive === false) {
      //console.log(`Terminating connection due to inactivity: ${ws.data.userId}`);
      return ws.close();
    }

    if (status) {
      status.isAlive = false;
    }
    ws.ping();
  }
}, PING_INTERVAL);

// --- WebRTC Services ---
const webRTCSignalingService = new WebRTCSignalingService(voiceWsManager);
const sfuService = new SFUService(voiceWsManager);

// --- WebRTC Signaling Handlers ---

function handleWebRTCOffer(
  ws: ServerWebSocket<CustomWebSocketData>,
  message: any,
) {
  webRTCSignalingService.handleOffer(ws, message);
}

function handleWebRTCAnswer(
  ws: ServerWebSocket<CustomWebSocketData>,
  message: any,
) {
  webRTCSignalingService.handleAnswer(ws, message);
}

function handleWebRTCIceCandidate(
  ws: ServerWebSocket<CustomWebSocketData>,
  message: any,
) {
  webRTCSignalingService.handleIceCandidate(ws, message);
}
