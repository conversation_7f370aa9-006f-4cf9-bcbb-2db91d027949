import type { ServerWebSocket } from "bun";

interface User {
  id: string;
  name: string;
  avatar: string;
  speaking: boolean;
  muted: boolean;
  deafened: boolean;
}

interface CustomWebSocketData {
  userId: string;
  serverId?: string;
  channelId?: string;
  user?: User;
  token: string;
  isAuthenticated?: boolean;
  isAlive: boolean;
  type: string;
}

type sockets = Set<ServerWebSocket<CustomWebSocketData>>;

// A Map to store topic subscriptions, keyed by topic name
type TopicSubscriptionsMap = Map<string, sockets>;

export class VoiceWebSocketManager {
  //private sockets: Set<ServerWebSocket<CustomWebSocketData>>
  public topicSubscriptions: TopicSubscriptionsMap;

  constructor() {
    //this.sockets = new Set();
    this.topicSubscriptions = new Map<string, sockets>();
  }

  add(ws: ServerWebSocket<CustomWebSocketData>) {
    const { serverId, channelId } = ws.data;
    const serverChannelCombo = `${serverId}:${channelId}`;
    //console.log(`Adding user ${ws.data.userId} to voice channel topic: ${serverChannelCombo}`);
    if (!this.topicSubscriptions.has(serverChannelCombo)) {
      this.topicSubscriptions.set(
        serverChannelCombo,
        new Set<ServerWebSocket<CustomWebSocketData>>(),
      );
    }
    this.topicSubscriptions.get(serverChannelCombo)?.add(ws);
    this.notifyChannelUsers(ws, serverChannelCombo, "user_joined"); // Notify existing users about new joiner, using topic
    this.sendUserListToNewUser(ws, serverChannelCombo, ws.data.userId); // Send list of existing users to the new user, using topic and userId
  }

  remove(ws: ServerWebSocket<CustomWebSocketData>) {
    const { serverId, channelId } = ws.data;
    const serverChannelCombo = `${serverId}:${channelId}`;
    //console.log(`Removing user ${ws.data.userId} from voice channel topic: ${serverChannelCombo}`);
    if (!this.topicSubscriptions.has(serverChannelCombo)) return;
    this.topicSubscriptions.get(serverChannelCombo)?.delete(ws);
    this.notifyChannelUsers(ws, serverChannelCombo, "user_left"); // Notify remaining users about leaving user, using topic
  }

  broadcast(
    userId: string,
    serverId: string,
    channelId: string,
    data: ArrayBuffer,
  ) {
    const serverChannelCombo = `${serverId}:${channelId}`;
    if (!this.topicSubscriptions.has(serverChannelCombo)) return;
    this.topicSubscriptions.get(serverChannelCombo)?.forEach((ws) => {
      if (
        ws.readyState === WebSocket.OPEN &&
        userId &&
        ws.data.userId !== userId
      ) {
        ws.send(data);
      }
    });
  }
  private notifyChannelUsers(
    ws: ServerWebSocket<CustomWebSocketData>,
    topic: string,
    eventType: "user_joined" | "user_left",
  ) {
    const channelSockets = this.topicSubscriptions.get(topic);
    if (!channelSockets) {
      console.warn(
        `No sockets found for topic ${topic} to notify about user ${eventType}`,
      );
      return; // No users in the channel to notify
    }

    const notification = JSON.stringify({
      type:
        eventType === "user_joined"
          ? "user_joined_channel"
          : "user_left_channel",
      userId: ws.data.userId, // User ID of the user who joined/left
      user: ws.data.user,
    });

    channelSockets.forEach((socket) => {
      if (socket !== ws && socket.readyState === WebSocket.OPEN) {
        // Don't notify the user who just joined/left, notify others
        socket.send(notification);
      }
    });
    //console.log(`Notified users in topic ${topic} about user ${ws.data.userId} ${eventType}`);
  }

  private sendUserListToNewUser(
    ws: ServerWebSocket<CustomWebSocketData>,
    topic: string,
    newUserId: string,
  ) {
    const channelSockets = this.topicSubscriptions.get(topic);
    if (!channelSockets) {
      //console.log(`No other users in topic ${topic} when user ${newUserId} joined.`);
      ws.send(JSON.stringify({ type: "channel_user_list", userIds: [] })); // Send empty list if no users
      return;
    }

    const userIds = Array.from(channelSockets)
      .filter((socket) => socket.data.userId !== newUserId) // Exclude the new user from the list
      .map((socket) => socket.data.userId);

    ws.send(JSON.stringify({ type: "channel_user_list", userIds: userIds }));
    //console.log(`Sent user list to new user ${newUserId} in topic ${topic}: ${JSON.stringify({ userIds })}`);
  }

  getSocketsInChannel(
    topic: string,
  ): Set<ServerWebSocket<CustomWebSocketData>> | undefined {
    return this.topicSubscriptions.get(topic);
  }

  getSocketByUserIdAndTopic(
    userId: string,
    topic: string,
  ): ServerWebSocket<CustomWebSocketData> | undefined {
    const channelSockets = this.topicSubscriptions.get(topic);
    if (!channelSockets) return undefined;

    for (const ws of channelSockets) {
      if (ws.data.userId === userId) {
        return ws;
      }
    }
    return undefined;
  }
}
