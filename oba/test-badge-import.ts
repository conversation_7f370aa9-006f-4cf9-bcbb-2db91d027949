#!/usr/bin/env bun

/**
 * Test script to verify badge handlers can be imported
 */

try {
  console.log("Testing badge handler imports...");
  
  // Test importing badge handlers
  const badgeHandlers = await import("./handlers/badges.ts");
  console.log("✅ Badge handlers imported successfully");
  console.log("Available handlers:", Object.keys(badgeHandlers));
  
  // Test importing badge routes
  const badgeRoutes = await import("./routes/badgeRoutes.ts");
  console.log("✅ Badge routes imported successfully");
  console.log("Available routes:", Object.keys(badgeRoutes));
  
  console.log("\n🎉 All badge imports successful!");
  
} catch (error) {
  console.error("❌ Import failed:", error);
  process.exit(1);
}