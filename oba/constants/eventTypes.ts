/**
 * Enum for WebSocket event types
 */
export enum EventTypes {
  // Message events
  MESSAGE_SEND = "MESSAGE_SEND",
  MESSAGE_SENT = "MESSAGE_SENT",
  MESSAGE_UPDATE = "MESSAGE_UPDATE",
  MESSAGE_UPDATED = "MESSAGE_UPDATED",
  MESSAGE_DELETE = "MESSAGE_DELETE",
  MESSAGE_DELETED = "MESSAGE_DELETED",
  MESSAGE_TYPING_START = "MESSAGE_TYPING_START",
  MESSAGE_TYPING_END = "MESSAGE_TYPING_END",

  // Direct message events
  DIRECT_MESSAGE_SEND = "DIRECT_MESSAGE_SEND",
  DIRECT_MESSAGE_SENT = "DIRECT_MESSAGE_SENT",
  DIRECT_MESSAGE_UPDATE = "DIRECT_MESSAGE_UPDATE",
  DIRECT_MESSAGE_UPDATED = "DIRECT_MESSAGE_UPDATED",
  DIRECT_MESSAGE_DELETE = "DIRECT_MESSAGE_DELETE",
  DIRECT_MESSAGE_DELETED = "DIRECT_MESSAGE_DELETED",

  // Server events
  SERVER_CREATE = "SERVER_CREATE",
  SERVER_CREATED = "SERVER_CREATED",
  SERVER_UPDATE = "SERVER_UPDATE",
  SERVER_UPDATED = "SERVER_UPDATED",
  SERVER_DELETE = "SERVER_DELETE",
  SERVER_DELETED = "SERVER_DELETED",
  SERVER_JOIN = "SERVER_JOIN",
  SERVER_JOINED = "SERVER_JOINED",
  SERVER_LEAVE = "SERVER_LEAVE",
  SERVER_LEFT = "SERVER_LEFT",
  SERVER_VIEW = "SERVER_VIEW",

  // Channel events
  CHANNEL_CREATE = "CHANNEL_CREATE",
  CHANNEL_CREATED = "CHANNEL_CREATED",
  CHANNEL_UPDATE = "CHANNEL_UPDATE",
  CHANNEL_UPDATED = "CHANNEL_UPDATED",
  CHANNEL_DELETE = "CHANNEL_DELETE",
  CHANNEL_DELETED = "CHANNEL_DELETED",
  CHANNEL_SUBSCRIBE = "CHANNEL_SUBSCRIBE",
  CHANNEL_SUBSCRIBED = "CHANNEL_SUBSCRIBED",
  CHANNEL_UNSUBSCRIBE = "CHANNEL_UNSUBSCRIBE",
  CHANNEL_UNSUBSCRIBED = "CHANNEL_UNSUBSCRIBED",
  CHANNEL_ADDED_TO_CATEGORY = "CHANNEL_ADDED_TO_CATEGORY",
  CHANNEL_REMOVED_FROM_CATEGORY = "CHANNEL_REMOVED_FROM_CATEGORY",

  // Category events
  CATEGORY_CREATE = "CATEGORY_CREATE",
  CATEGORY_CREATED = "CATEGORY_CREATED",
  CATEGORY_UPDATE = "CATEGORY_UPDATE",
  CATEGORY_UPDATED = "CATEGORY_UPDATED",
  CATEGORY_DELETE = "CATEGORY_DELETE",
  CATEGORY_DELETED = "CATEGORY_DELETED",

  // Role events
  ROLE_CREATE = "ROLE_CREATE",
  ROLE_CREATED = "ROLE_CREATED",
  ROLE_UPDATE = "ROLE_UPDATE",
  ROLE_UPDATED = "ROLE_UPDATED",
  ROLE_DELETE = "ROLE_DELETE",
  ROLE_DELETED = "ROLE_DELETED",

  // User events
  USER_UPDATE = "USER_UPDATE",
  USER_UPDATED = "USER_UPDATED",
  USER_STATUS_CHANGE = "USER_STATUS_CHANGE",
  USER_STATUS_CHANGED = "USER_STATUS_CHANGED",

  // Voice events
  VOICE_JOIN = "VOICE_JOIN",
  VOICE_JOINED = "VOICE_JOINED",
  VOICE_LEAVE = "VOICE_LEAVE",
  VOICE_LEFT = "VOICE_LEFT",
  VOICE_DATA_SEND = "VOICE_DATA_SEND",
  VOICE_DATA_RECEIVED = "VOICE_DATA_RECEIVED",
  VOICE_MUTE_TOGGLE = "VOICE_MUTE_TOGGLE",
  VOICE_DEAFEN_TOGGLE = "VOICE_DEAFEN_TOGGLE",
  VOICE_SPEAKING_START = "VOICE_SPEAKING_START",
  VOICE_SPEAKING_END = "VOICE_SPEAKING_END",

  // Reaction events
  REACTION_ADD = "REACTION_ADD",
  REACTION_ADDED = "REACTION_ADDED",
  REACTION_REMOVE = "REACTION_REMOVE",
  REACTION_REMOVED = "REACTION_REMOVED",

  // Friend events
  FRIEND_REQUEST_SEND = "FRIEND_REQUEST_SEND",
  FRIEND_REQUEST_SENT = "FRIEND_REQUEST_SENT",
  FRIEND_REQUEST_ACCEPT = "FRIEND_REQUEST_ACCEPT",
  FRIEND_REQUEST_ACCEPTED = "FRIEND_REQUEST_ACCEPTED",
  FRIEND_REQUEST_DECLINE = "FRIEND_REQUEST_DECLINE",
  FRIEND_REQUEST_DECLINED = "FRIEND_REQUEST_DECLINED",
  FRIEND_REMOVE = "FRIEND_REMOVE",
  FRIEND_REMOVED = "FRIEND_REMOVED",

  // Server member events
  MEMBER_JOIN = "MEMBER_JOIN",
  MEMBER_JOINED = "MEMBER_JOINED",
  MEMBER_LEAVE = "MEMBER_LEAVE",
  MEMBER_LEFT = "MEMBER_LEFT",
  MEMBER_KICK = "MEMBER_KICK",
  MEMBER_KICKED = "MEMBER_KICKED",
  MEMBER_BAN = "MEMBER_BAN",
  MEMBER_BANNED = "MEMBER_BANNED",
  MEMBER_UNBAN = "MEMBER_UNBAN",
  MEMBER_UNBANNED = "MEMBER_UNBANNED",

  // Ordering events
  CHANNELS_REORDER = "CHANNELS_REORDER",
  CHANNELS_REORDERED = "CHANNELS_REORDERED",
  CATEGORIES_REORDER = "CATEGORIES_REORDER",
  CATEGORIES_REORDERED = "CATEGORIES_REORDERED",
  CHANNELS_MOVE = "CHANNELS_MOVE",
  CHANNELS_MOVED = "CHANNELS_MOVED",

  // Server structure events
  SERVER_STRUCTURE = "SERVER_STRUCTURE",
  SERVER_RESTRUCTURE = "SERVER_RESTRUCTURE",

  // Error events
  ERROR = "ERROR",

  // System events
  PING = "PING",
  PONG = "PONG",

  // Authentication events
  AUTHENTICATE = "AUTHENTICATE",
  AUTH_SUCCESS = "AUTH_SUCCESS",
  AUTH_FAILED = "AUTH_FAILED",

  // User status events
  USER_STATUS_UPDATE = "USER_STATUS_UPDATE",
  USER_STATUS_UPDATED = "USER_STATUS_UPDATED",

  // Voice state events
  VOICE_STATE_UPDATE = "VOICE_STATE_UPDATE",
  VOICE_STATE_UPDATED = "VOICE_STATE_UPDATED",

  // Badge events
  BADGE_ASSIGNED = "BADGE_ASSIGNED",
  BADGE_REMOVED = "BADGE_REMOVED",
  BADGE_PROGRESS_UPDATE = "BADGE_PROGRESS_UPDATE",
}
