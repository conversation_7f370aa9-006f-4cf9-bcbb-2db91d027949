/**
 * Permission system constants
 *
 * This file defines all the permission constants used in the application.
 * Permissions are stored as bitmasks, allowing efficient storage and checking.
 */

// Permission Categories
export enum PermissionCategory {
  SERVER_MANAGEMENT = "Server Management",
  USER_ROLE_MANAGEMENT = "User & Role Management",
  CHANNEL_MANAGEMENT = "Channel Management",
  EMOJI_STICKER_MANAGEMENT = "Emojis & Stickers",
  VOICE_PERMISSIONS = "Voice Permissions",
  ADVANCED_FEATURES = "Advanced Features",
  EVENTS = "Events",
  MENTIONS = "Mentions & Special Actions",
  FILE_MEDIA = "File & Media",
}

// Server Management Permissions
export const ADMINISTRATOR = BigInt(0x00000001); // 1
export const MANAGE_SERVER = BigInt(0x00000002); // 2
export const VIEW_AUDIT_LOGS = BigInt(0x00000004); // 4
export const VIEW_SERVER_INFO = BigInt(0x00000008); // 8

// User & Role Management Permissions
export const MANAGE_ROLES = BigInt(0x00000010); // 16
export const CHANGE_NICKNAME = BigInt(0x00000020); // 32
export const MANAGE_NICKNAMES = BigInt(0x00000040); // 64
export const KICK_ACCEPT_DENY_USERS = BigInt(0x00000080); // 128
export const BLOCK_USERS = BigInt(0x00000100); // 256

// Channel Management Permissions
export const VIEW_CHANNEL = BigInt(0x00000200); // 512
export const MANAGE_CHANNELS = BigInt(0x00000400); // 1024
export const MANAGE_MESSAGES = BigInt(0x00000800); // 2048
export const MANAGE_SUBCHANNELS = BigInt(0x00001000); // 4096
export const SEND_MESSAGES = BigInt(0x00002000); // 8192
export const SEND_SUBCHANNEL_MESSAGES = BigInt(0x00004000); // 16384
export const CREATE_PUBLIC_SUBCHANNELS = BigInt(0x00008000); // 32768
export const CREATE_PRIVATE_SUBCHANNELS = BigInt(0x00010000); // 65536
export const VIEW_MESSAGE_HISTORY = BigInt(0x00020000); // 131072
export const SEND_TTS_MESSAGES = BigInt(0x00040000); // 262144
export const SEND_VOICE_MESSAGE = BigInt(0x00080000); // 524288
export const RESTRICT_MESSAGE_INTERVAL = BigInt(0x00100000); // 1048576
export const SEND_LINKS = BigInt(0x00200000); // 2097152

// Emoji, Stickers, and Reactions Permissions
export const CREATE_EMOJIS = BigInt(0x00400000); // 4194304
export const MANAGE_EMOJIS = BigInt(0x00800000); // 8388608
export const USE_REACTIONS = BigInt(0x01000000); // 16777216
export const USE_EXTERNAL_EMOJIS = BigInt(0x02000000); // 33554432
export const USE_EXTERNAL_STICKERS = BigInt(0x04000000); // 67108864

// Voice Channel Permissions
export const JOIN_VOICE = BigInt(0x08000000); // 134217728
export const SPEAK_VOICE = BigInt(0x10000000); // 268435456
export const VIDEO_CHAT = BigInt(0x20000000); // 536870912
export const USE_SOUNDBOARD = BigInt(0x40000000); // 1073741824
export const USE_EXTERNAL_SOUNDBOARD = BigInt(0x80000000); // 2147483648
export const USE_VOICE_ACTIVITY = BigInt("0x0000000100000000"); // 4294967296
export const PRIORITY_SPEAKER = BigInt("0x0000000200000000"); // 8589934592
export const REQUEST_TO_SPEAK = BigInt("0x0000000400000000"); // 17179869184
export const MANAGE_VOICE_STATUS = BigInt("0x0000000800000000"); // 34359738368

// Advanced Features & App Commands Permissions
export const USE_APP_COMMANDS = BigInt("0x0000001000000000"); // 68719476736
export const USE_USER_ACTIVITY = BigInt("0x0000002000000000"); // 137438953472
export const USE_THIRD_PARTY_APP = BigInt("0x0000004000000000"); // 274877906944

// Events & Polls Permissions
export const CREATE_EVENT = BigInt("0x0000008000000000"); // 549755813888
export const MANAGE_EVENT = BigInt("0x0000010000000000"); // 1099511627776
export const CREATE_POLL = BigInt("0x0000020000000000"); // 2199023255552

// Mentions & Special Actions Permissions
export const MENTION_EVERYONE = BigInt("0x0000040000000000"); // 4398046511104

// File & Media Permissions
export const SEND_FILES = BigInt("0x0000080000000000"); // 8796093022208

// Permission Groups
export const ALL_PERMISSIONS =
  ADMINISTRATOR |
  MANAGE_SERVER |
  VIEW_AUDIT_LOGS |
  VIEW_SERVER_INFO |
  MANAGE_ROLES |
  CHANGE_NICKNAME |
  MANAGE_NICKNAMES |
  KICK_ACCEPT_DENY_USERS |
  BLOCK_USERS |
  VIEW_CHANNEL |
  MANAGE_CHANNELS |
  MANAGE_MESSAGES |
  MANAGE_SUBCHANNELS |
  SEND_MESSAGES |
  SEND_SUBCHANNEL_MESSAGES |
  CREATE_PUBLIC_SUBCHANNELS |
  CREATE_PRIVATE_SUBCHANNELS |
  VIEW_MESSAGE_HISTORY |
  SEND_TTS_MESSAGES |
  SEND_VOICE_MESSAGE |
  RESTRICT_MESSAGE_INTERVAL |
  SEND_LINKS |
  CREATE_EMOJIS |
  MANAGE_EMOJIS |
  USE_REACTIONS |
  USE_EXTERNAL_EMOJIS |
  USE_EXTERNAL_STICKERS |
  JOIN_VOICE |
  SPEAK_VOICE |
  VIDEO_CHAT |
  USE_SOUNDBOARD |
  USE_EXTERNAL_SOUNDBOARD |
  USE_VOICE_ACTIVITY |
  PRIORITY_SPEAKER |
  REQUEST_TO_SPEAK |
  MANAGE_VOICE_STATUS |
  USE_APP_COMMANDS |
  USE_USER_ACTIVITY |
  USE_THIRD_PARTY_APP |
  CREATE_EVENT |
  MANAGE_EVENT |
  CREATE_POLL |
  MENTION_EVERYONE |
  SEND_FILES;

// Default role permissions
export const DEFAULT_ADMIN_PERMISSIONS = ALL_PERMISSIONS;

export const DEFAULT_MODERATOR_PERMISSIONS =
  MANAGE_MESSAGES |
  MANAGE_CHANNELS |
  KICK_ACCEPT_DENY_USERS |
  BLOCK_USERS |
  VIEW_AUDIT_LOGS |
  VIEW_SERVER_INFO |
  VIEW_CHANNEL |
  SEND_MESSAGES |
  VIEW_MESSAGE_HISTORY |
  SEND_LINKS |
  USE_REACTIONS |
  JOIN_VOICE |
  SPEAK_VOICE |
  VIDEO_CHAT |
  MENTION_EVERYONE |
  SEND_FILES;

export const DEFAULT_MEMBER_PERMISSIONS =
  VIEW_CHANNEL |
  SEND_MESSAGES |
  VIEW_MESSAGE_HISTORY |
  SEND_LINKS |
  USE_REACTIONS |
  JOIN_VOICE |
  SPEAK_VOICE |
  SEND_FILES;

// Map of permission names to values for easier lookup
export const PERMISSION_MAP: Record<string, bigint> = {
  ADMINISTRATOR,
  MANAGE_SERVER,
  VIEW_AUDIT_LOGS,
  VIEW_SERVER_INFO,
  MANAGE_ROLES,
  CHANGE_NICKNAME,
  MANAGE_NICKNAMES,
  KICK_ACCEPT_DENY_USERS,
  BLOCK_USERS,
  VIEW_CHANNEL,
  MANAGE_CHANNELS,
  MANAGE_MESSAGES,
  MANAGE_SUBCHANNELS,
  SEND_MESSAGES,
  SEND_SUBCHANNEL_MESSAGES,
  CREATE_PUBLIC_SUBCHANNELS,
  CREATE_PRIVATE_SUBCHANNELS,
  VIEW_MESSAGE_HISTORY,
  SEND_TTS_MESSAGES,
  SEND_VOICE_MESSAGE,
  RESTRICT_MESSAGE_INTERVAL,
  SEND_LINKS,
  CREATE_EMOJIS,
  MANAGE_EMOJIS,
  USE_REACTIONS,
  USE_EXTERNAL_EMOJIS,
  USE_EXTERNAL_STICKERS,
  JOIN_VOICE,
  SPEAK_VOICE,
  VIDEO_CHAT,
  USE_SOUNDBOARD,
  USE_EXTERNAL_SOUNDBOARD,
  USE_VOICE_ACTIVITY,
  PRIORITY_SPEAKER,
  REQUEST_TO_SPEAK,
  MANAGE_VOICE_STATUS,
  USE_APP_COMMANDS,
  USE_USER_ACTIVITY,
  USE_THIRD_PARTY_APP,
  CREATE_EVENT,
  MANAGE_EVENT,
  CREATE_POLL,
  MENTION_EVERYONE,
  SEND_FILES,
};

// Map of permissions to their categories
export const PERMISSION_CATEGORIES: Record<string, PermissionCategory> = {
  ADMINISTRATOR: PermissionCategory.SERVER_MANAGEMENT,
  MANAGE_SERVER: PermissionCategory.SERVER_MANAGEMENT,
  VIEW_AUDIT_LOGS: PermissionCategory.SERVER_MANAGEMENT,
  VIEW_SERVER_INFO: PermissionCategory.SERVER_MANAGEMENT,
  MANAGE_ROLES: PermissionCategory.USER_ROLE_MANAGEMENT,
  CHANGE_NICKNAME: PermissionCategory.USER_ROLE_MANAGEMENT,
  MANAGE_NICKNAMES: PermissionCategory.USER_ROLE_MANAGEMENT,
  KICK_ACCEPT_DENY_USERS: PermissionCategory.USER_ROLE_MANAGEMENT,
  BLOCK_USERS: PermissionCategory.USER_ROLE_MANAGEMENT,
  VIEW_CHANNEL: PermissionCategory.CHANNEL_MANAGEMENT,
  MANAGE_CHANNELS: PermissionCategory.CHANNEL_MANAGEMENT,
  MANAGE_MESSAGES: PermissionCategory.CHANNEL_MANAGEMENT,
  MANAGE_SUBCHANNELS: PermissionCategory.CHANNEL_MANAGEMENT,
  SEND_MESSAGES: PermissionCategory.CHANNEL_MANAGEMENT,
  SEND_SUBCHANNEL_MESSAGES: PermissionCategory.CHANNEL_MANAGEMENT,
  CREATE_PUBLIC_SUBCHANNELS: PermissionCategory.CHANNEL_MANAGEMENT,
  CREATE_PRIVATE_SUBCHANNELS: PermissionCategory.CHANNEL_MANAGEMENT,
  VIEW_MESSAGE_HISTORY: PermissionCategory.CHANNEL_MANAGEMENT,
  SEND_TTS_MESSAGES: PermissionCategory.CHANNEL_MANAGEMENT,
  SEND_VOICE_MESSAGE: PermissionCategory.CHANNEL_MANAGEMENT,
  RESTRICT_MESSAGE_INTERVAL: PermissionCategory.CHANNEL_MANAGEMENT,
  SEND_LINKS: PermissionCategory.CHANNEL_MANAGEMENT,
  CREATE_EMOJIS: PermissionCategory.EMOJI_STICKER_MANAGEMENT,
  MANAGE_EMOJIS: PermissionCategory.EMOJI_STICKER_MANAGEMENT,
  USE_REACTIONS: PermissionCategory.EMOJI_STICKER_MANAGEMENT,
  USE_EXTERNAL_EMOJIS: PermissionCategory.EMOJI_STICKER_MANAGEMENT,
  USE_EXTERNAL_STICKERS: PermissionCategory.EMOJI_STICKER_MANAGEMENT,
  JOIN_VOICE: PermissionCategory.VOICE_PERMISSIONS,
  SPEAK_VOICE: PermissionCategory.VOICE_PERMISSIONS,
  VIDEO_CHAT: PermissionCategory.VOICE_PERMISSIONS,
  USE_SOUNDBOARD: PermissionCategory.VOICE_PERMISSIONS,
  USE_EXTERNAL_SOUNDBOARD: PermissionCategory.VOICE_PERMISSIONS,
  USE_VOICE_ACTIVITY: PermissionCategory.VOICE_PERMISSIONS,
  PRIORITY_SPEAKER: PermissionCategory.VOICE_PERMISSIONS,
  REQUEST_TO_SPEAK: PermissionCategory.VOICE_PERMISSIONS,
  MANAGE_VOICE_STATUS: PermissionCategory.VOICE_PERMISSIONS,
  USE_APP_COMMANDS: PermissionCategory.ADVANCED_FEATURES,
  USE_USER_ACTIVITY: PermissionCategory.ADVANCED_FEATURES,
  USE_THIRD_PARTY_APP: PermissionCategory.ADVANCED_FEATURES,
  CREATE_EVENT: PermissionCategory.EVENTS,
  MANAGE_EVENT: PermissionCategory.EVENTS,
  CREATE_POLL: PermissionCategory.EVENTS,
  MENTION_EVERYONE: PermissionCategory.MENTIONS,
  SEND_FILES: PermissionCategory.FILE_MEDIA,
};

// Helper function to convert permission name to value
export function getPermissionValue(permissionName: string): bigint | null {
  return PERMISSION_MAP[permissionName] || null;
}

// Helper function to get permission category
export function getPermissionCategory(
  permissionName: string,
): PermissionCategory | null {
  return PERMISSION_CATEGORIES[permissionName] || null;
}

// Helper function to get all permissions in a category
export function getPermissionsByCategory(
  category: PermissionCategory,
): Record<string, bigint> {
  const result: Record<string, bigint> = {};

  for (const [name, value] of Object.entries(PERMISSION_MAP)) {
    if (PERMISSION_CATEGORIES[name] === category) {
      result[name] = value;
    }
  }

  return result;
}

// Helper function to convert a permission bitmask to an array of permission names
export function permissionBitmaskToNames(bitmask: bigint): string[] {
  const permissionNames: string[] = [];

  for (const [name, value] of Object.entries(PERMISSION_MAP)) {
    if ((bitmask & value) === value) {
      permissionNames.push(name);
    }
  }

  return permissionNames;
}

// Helper function to convert an array of permission names to a bitmask
export function permissionNamesToBitmask(permissionNames: string[]): bigint {
  let bitmask = BigInt(0);

  for (const name of permissionNames) {
    const value = PERMISSION_MAP[name];
    if (value) {
      bitmask |= value;
    }
  }

  return bitmask;
}
