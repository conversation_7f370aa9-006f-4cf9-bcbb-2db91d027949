import type { Middleware } from "../types";
import * as jose from "jose";
import { verifyRefreshToken } from "../db/utils";
import { db } from "../db";

// JWT token payload interface
interface JWTPayload extends jose.JWTPayload {
  userId: string;
  email: string;
  username?: string;
  avatar?: string;
  isEmailVerified?: boolean;
}

// Environment variables validation
const jwtSecret = process.env.JWT_SECRET_KEY;
if (!jwtSecret) {
  throw new Error("JWT_SECRET_KEY environment variable is not set");
}

const JWT_CONFIG = {
  issuer: process.env.JWT_ISSUER || "https://api.berkormanli.dev",
  audience: process.env.JWT_AUDIENCE || "https://api.berkormanli.dev",
};

/**
 * Enhanced authentication middleware that automatically refreshes expired tokens
 */
const authWithRefreshMiddleware: Middleware = async (req, next) => {
  try {
    const cookies = req.headers.get("Cookie");
    const accessTokenCookie = cookies
      ?.split(";")
      .find((cookie) => cookie.trim().startsWith("accessToken="));
    console.log("========AUTH MIDDLEWARE=============");
    console.log("Access Token Cookie:", accessTokenCookie);
    console.log("Cookies:", cookies);
    console.log("====================================");
    if (!accessTokenCookie) {
      return new Response(
        JSON.stringify({ error: "Missing or invalid Bearer token" }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        },
      );
    }

    const token = accessTokenCookie?.split("=")[1];
    const secret = new TextEncoder().encode(jwtSecret);

    try {
      // Try to verify the access token
      const { payload } = await jose.jwtVerify(token, secret, {
        issuer: JWT_CONFIG.issuer,
        audience: JWT_CONFIG.audience,
      });

      const jwtPayload = payload as JWTPayload;

      if (!jwtPayload.userId) {
        throw new Error("Invalid token payload: missing userId");
      }

      // Token is valid, add user info to request context
      (req as any).user = {
        userId: jwtPayload.userId,
        email: jwtPayload.email,
        username: jwtPayload.username,
        avatar: jwtPayload.avatar,
        isEmailVerified: jwtPayload.isEmailVerified,
      };

      return next(req);
    } catch (tokenError) {
      // If access token is expired or invalid, try to refresh it
      if (
        tokenError instanceof jose.errors.JWTExpired ||
        tokenError instanceof jose.errors.JWSSignatureVerificationFailed
      ) {
        console.log("Access token expired or invalid, attempting refresh...");

        // Try to get refresh token from cookies
        const cookies = req.headers.get("Cookie");
        const refreshTokenCookie = cookies
          ?.split(";")
          .find((cookie) => cookie.trim().startsWith("refreshToken="));
        const refreshToken = refreshTokenCookie?.split("=")[1];

        if (!refreshToken) {
          return new Response(
            JSON.stringify({
              error: "Access token expired and no refresh token provided",
              requiresReauth: true,
            }),
            {
              status: 401,
              headers: { "Content-Type": "application/json" },
            },
          );
        }

        try {
          // Extract userId from the expired token (without verification)
          const decodedToken = jose.decodeJwt(token) as JWTPayload;

          if (!decodedToken.userId) {
            throw new Error("Cannot extract userId from expired token");
          }

          // Verify refresh token and get user data
          const user = await verifyRefreshToken(
            db,
            decodedToken.userId,
            refreshToken,
          );

          // Generate new access token
          const newAccessToken = await new jose.SignJWT({
            userId: user.id,
            username: user.username,
            email: user.email,
            avatar: user.avatar,
            isEmailVerified: user.isEmailVerified,
          })
            .setProtectedHeader({ alg: "HS256" })
            .setIssuedAt()
            .setIssuer(JWT_CONFIG.issuer)
            .setAudience(JWT_CONFIG.audience)
            .setExpirationTime("15m") // 15 minutes
            .sign(secret);

          // Add user info to request context
          (req as any).user = {
            userId: user.id,
            email: user.email,
            username: user.username,
            avatar: user.avatar,
            isEmailVerified: user.isEmailVerified,
          };

          // Store the new token to be sent in response headers
          (req as any).newAccessToken = newAccessToken;

          // Call the next middleware/handler
          const response = await next(req);

          // Clone the response to modify headers
          const newResponse = new Response(response.body, {
            status: response.status,
            statusText: response.statusText,
            headers: new Headers(response.headers),
          });

          // Add the new access token to response headers
          newResponse.headers.set("X-New-Access-Token", newAccessToken);
          newResponse.headers.set("X-Token-Refreshed", "true");

          // Also set as cookie
          const cookieOptions = "HttpOnly; Secure; SameSite=Strict; Path=/;";
          const accessTokenCookie = `accessToken=${newAccessToken}; ${cookieOptions} Max-Age=900`; // 15 minutes

          // Get existing set-cookie headers
          const existingCookies = newResponse.headers.get("Set-Cookie") || "";
          const newCookies = existingCookies
            ? `${existingCookies}, ${accessTokenCookie}`
            : accessTokenCookie;
          newResponse.headers.set("Set-Cookie", newCookies);

          console.log("Token refreshed successfully for user:", user.id);
          return newResponse;
        } catch (refreshError) {
          console.error("Token refresh failed:", refreshError);

          // Clear invalid refresh token cookie
          const clearCookie =
            "refreshToken=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0";

          return new Response(
            JSON.stringify({
              error: "Invalid or expired refresh token",
              requiresReauth: true,
            }),
            {
              status: 401,
              headers: {
                "Content-Type": "application/json",
                "Set-Cookie": clearCookie,
              },
            },
          );
        }
      } else {
        // Token error is not related to expiration
        throw tokenError;
      }
    }
  } catch (error) {
    console.error("Authentication Error:", error);

    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : "Invalid token",
      }),
      {
        status: 403,
        headers: { "Content-Type": "application/json" },
      },
    );
  }
};

export default authWithRefreshMiddleware;
