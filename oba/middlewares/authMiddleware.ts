import type { Middleware } from "../types";
import * as jose from "jose";

// JWT token payload interface
interface JWTPayload extends jose.JWTPayload {
  userId: string;
  email: string;
}

// Environment variables validation
const jwtSecret = process.env.JWT_SECRET_KEY;
if (!jwtSecret) {
  throw new Error("JWT_SECRET_KEY environment variable is not set");
}

const JWT_CONFIG = {
  issuer: process.env.JWT_ISSUER || "https://api.berkormanli.dev", //|| 'oba.app',
  audience: process.env.JWT_AUDIENCE || "https://api.berkormanli.dev",
};

const authMiddleware: Middleware = async (req, next) => {
  try {
    const authHeader = req.headers.get("Authorization");

    if (!authHeader?.startsWith("Bearer ")) {
      return new Response(
        JSON.stringify({ error: "Missing or invalid Bearer token" }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        },
      );
    }

    const token = authHeader.split(" ")[1];
    const secret = new TextEncoder().encode(jwtSecret);

    const { payload } = await jose.jwtVerify(token, secret, {
      issuer: JWT_CONFIG.issuer,
      audience: JWT_CONFIG.audience,
    });

    const jwtPayload = payload as JWTPayload;

    if (!jwtPayload.userId) {
      throw new Error("Invalid token payload: missing userId");
    }

    // Clone the request and get the body content
    let bodyContent = null;
    if (req.body) {
      const clonedReq = req.clone();
      bodyContent = await clonedReq.text();
    }

    // Add user info to the request context
    (req as any).user = {
      userId: jwtPayload.userId,
      email: jwtPayload.email,
    };

    return next(req);
  } catch (error) {
    console.error("Authentication Error:", error);

    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : "Invalid token",
      }),
      {
        status: 403,
        headers: { "Content-Type": "application/json" },
      },
    );
  }
};

export default authMiddleware;
