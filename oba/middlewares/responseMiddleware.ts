import { nanoid } from "nanoid";
import type { MiddlewareFunction } from "../types/middleware.types";

/**
 * Response middleware that adds request ID and standardizes response headers
 */
export const responseMiddleware: MiddlewareFunction = async (req, next) => {
  // Generate request ID
  const requestId = nanoid();

  // Add request ID to request context (if you have a context system)
  (req as any).requestId = requestId;

  try {
    // Call the next middleware/handler
    const response = await next();

    // Add standard headers to all responses
    if (response instanceof Response) {
      const headers = new Headers(response.headers);

      // Add request ID header if not already present
      if (!headers.has("X-Request-ID")) {
        headers.set("X-Request-ID", requestId);
      }

      // Add CORS headers if needed
      headers.set("Access-Control-Allow-Origin", "*");
      headers.set(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS",
      );
      headers.set(
        "Access-Control-Allow-Headers",
        "Content-Type, Authorization",
      );
      headers.set("Access-Control-Expose-Headers", "X-Request-ID");

      // Create new response with updated headers
      return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers,
      });
    }

    return response;
  } catch (error) {
    // Handle unexpected errors with standardized format
    console.error("Unhandled error in response middleware:", error);

    return new Response(
      JSON.stringify({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "An unexpected error occurred",
        },
        meta: {
          timestamp: new Date(),
          requestId,
        },
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "X-Request-ID": requestId,
        },
      },
    );
  }
};

export default responseMiddleware;
