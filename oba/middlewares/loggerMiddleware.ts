import type { Middleware } from "../types";

const loggerMiddleware: Middleware = (req, next) => {
  const start = Date.now();
  const response = next(req);
  const end = Date.now();
  const responseTime = end - start;

  const logResponse = (res: Response) => {
    //console.log(`${req.method} ${req.url} - ${res.status} - ${responseTime}ms`);
  };

  if (response instanceof Promise) {
    response.then((res) => {
      logResponse(res);
      return res;
    });
  } else {
    logResponse(response);
  }

  return response;
};

export default loggerMiddleware;
