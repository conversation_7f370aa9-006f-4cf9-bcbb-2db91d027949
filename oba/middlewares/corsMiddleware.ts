import type { Middleware } from "../types";

export const corsMiddleware: Middleware = (req, next) => {
  const origin = req.headers.get("origin");
  const res = next(req);

  const applyCorsHeaders = (res: Response) => {
    res.headers.set("Access-Control-Allow-Origin", origin || "*");
    res.headers.set(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, OPTIONS",
    );
    res.headers.set(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization",
    );
    res.headers.set("Access-Control-Allow-Credentials", "true");
    return res;
  };

  if (res instanceof Promise) {
    return res.then(applyCorsHeaders);
  } else {
    return applyCorsHeaders(res);
  }
};
