#!/usr/bin/env bun

// Comprehensive test script for badge validation schemas and error handling
import { 
  validateCreateBadgeType,
  validateUpdateBadgeType,
  validateAssignBadge,
  validateBadgeFilters,
  validateUserStats,
  validateBadgeProgress
} from "./utils/badge-validation";

import {
  BadgeNotFoundError,
  BadgeAlreadyAssignedError,
  BadgeValidationError,
  InvalidBadgeCriteriaError,
  BadgeEvaluationError,
  BadgeAssignmentError,
  BadgeRemovalError,
  DuplicateBadgeNameError,
  InactiveBadgeError,
  UserStatsError,
  InsufficientPermissionsError,
  BadgeRateLimitError,
  isBadgeError,
  formatBadgeError,
  BADGE_ERROR_CODES
} from "./class/badge-errors";

console.log("🧪 Running comprehensive badge validation tests...\n");

// Test valid badge type creation
console.log("1. Testing valid badge type creation:");
try {
  const validBadgeType = validateCreateBadgeType({
    name: "First Message",
    description: "Sent your first message",
    color: "#FF0000",
    category: "milestone",
    assignmentType: "automatic",
    criteria: {
      type: "message_count",
      threshold: 1
    }
  });
  console.log("✅ Valid automatic badge creation passed");
} catch (error) {
  console.error("❌ Valid automatic badge creation failed:", error);
}

try {
  const validManualBadge = validateCreateBadgeType({
    name: "Community Helper",
    description: "Recognized for helping community members",
    color: "#00FF00",
    category: "special",
    assignmentType: "manual"
  });
  console.log("✅ Valid manual badge creation passed");
} catch (error) {
  console.error("❌ Valid manual badge creation failed:", error);
}

// Test invalid badge type creation
console.log("\n2. Testing invalid badge type creation:");
try {
  validateCreateBadgeType({
    name: "",
    description: "Empty name test",
    category: "achievement",
    assignmentType: "manual"
  });
  console.error("❌ Should have failed for empty name");
} catch (error) {
  console.log("✅ Correctly rejected empty name");
}

try {
  validateCreateBadgeType({
    name: "Test Badge",
    description: "Test",
    color: "invalid-color",
    category: "achievement",
    assignmentType: "manual"
  });
  console.error("❌ Should have failed for invalid color");
} catch (error) {
  console.log("✅ Correctly rejected invalid color");
}

try {
  validateCreateBadgeType({
    name: "Auto Badge",
    description: "Automatic badge without criteria",
    category: "achievement",
    assignmentType: "automatic"
    // Missing criteria
  });
  console.error("❌ Should have failed for automatic badge without criteria");
} catch (error) {
  console.log("✅ Correctly rejected automatic badge without criteria");
}

// Test badge criteria validation
console.log("\n3. Testing badge criteria validation:");
try {
  validateCreateBadgeType({
    name: "Message Master",
    description: "Sent many messages",
    category: "achievement",
    assignmentType: "automatic",
    criteria: {
      type: "message_count",
      threshold: 0 // Invalid threshold
    }
  });
  console.error("❌ Should have failed for zero threshold");
} catch (error) {
  console.log("✅ Correctly rejected zero threshold");
}

try {
  validateCreateBadgeType({
    name: "Custom Badge",
    description: "Custom criteria badge",
    category: "special",
    assignmentType: "automatic",
    criteria: {
      type: "custom"
      // Missing conditions
    }
  });
  console.error("❌ Should have failed for custom criteria without conditions");
} catch (error) {
  console.log("✅ Correctly rejected custom criteria without conditions");
}

// Test user stats validation
console.log("\n4. Testing user stats validation:");
try {
  const validStats = validateUserStats({
    messageCount: 150,
    serverCount: 3,
    friendCount: 25,
    daysActive: 30,
    accountAge: 90,
    lastActive: new Date()
  });
  console.log("✅ Valid user stats passed");
} catch (error) {
  console.error("❌ Valid user stats failed:", error);
}

try {
  validateUserStats({
    messageCount: -5, // Invalid negative count
    serverCount: 3,
    friendCount: 25,
    daysActive: 30,
    accountAge: 90,
    lastActive: new Date()
  });
  console.error("❌ Should have failed for negative message count");
} catch (error) {
  console.log("✅ Correctly rejected negative message count");
}

// Test all error classes
console.log("\n5. Testing all badge error classes:");

const errorTests = [
  () => new BadgeNotFoundError("test-badge-id"),
  () => new BadgeAlreadyAssignedError("user-id", "badge-id"),
  () => new BadgeValidationError("Validation failed", { name: ["Required"] }),
  () => new InvalidBadgeCriteriaError("message_count", "Invalid threshold"),
  () => new BadgeEvaluationError("user-id", "badge-id", "Evaluation failed"),
  () => new BadgeAssignmentError("Assignment failed", "user-id", "badge-id"),
  () => new BadgeRemovalError("user-id", "badge-id", "Removal failed"),
  () => new DuplicateBadgeNameError("Duplicate Badge"),
  () => new InactiveBadgeError("badge-id"),
  () => new UserStatsError("user-id", "Stats calculation failed"),
  () => new InsufficientPermissionsError("badge assignment"),
  () => new BadgeRateLimitError("badge assignment", 60)
];

errorTests.forEach((createError, index) => {
  try {
    const error = createError();
    if (isBadgeError(error)) {
      const formatted = formatBadgeError(error);
      console.log(`✅ Error ${index + 1} (${error.constructor.name}): ${error.code}`);
    }
  } catch (e) {
    console.error(`❌ Error ${index + 1} failed:`, e);
  }
});

// Test error codes enum
console.log("\n6. Testing error codes enum:");
const expectedCodes = [
  'BADGE_NOT_FOUND',
  'BADGE_ALREADY_ASSIGNED',
  'INSUFFICIENT_PERMISSIONS',
  'BADGE_VALIDATION_ERROR',
  'BADGE_EVALUATION_ERROR',
  'BADGE_ASSIGNMENT_ERROR',
  'BADGE_REMOVAL_ERROR',
  'DUPLICATE_BADGE_NAME',
  'INACTIVE_BADGE_ERROR',
  'USER_STATS_ERROR',
  'INVALID_BADGE_CRITERIA',
  'BADGE_RATE_LIMIT'
];

expectedCodes.forEach(code => {
  if (BADGE_ERROR_CODES[code as keyof typeof BADGE_ERROR_CODES]) {
    console.log(`✅ Error code ${code} exists`);
  } else {
    console.error(`❌ Error code ${code} missing`);
  }
});

console.log("\n🎉 Comprehensive badge validation tests completed!");