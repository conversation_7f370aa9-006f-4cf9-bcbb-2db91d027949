# OBA Backend Tests

This directory contains tests for the OBA Backend application.

## Structure

- `setup.ts`: Test setup file that configures the test environment
- `helpers.ts`: Helper functions for creating test data and utilities
- `unit/`: Unit tests for individual functions
- `integration/`: Integration tests for API endpoints and handlers

## Running Tests

Due to database connection handling, it's recommended to run tests individually using the following commands:

```bash
# Run all tests sequentially (default)
bun test

# Run all unit tests
bun test:unit

# Run all integration tests
bun test:integration

# Run specific test files
bun test:user                # User utility tests
bun test:message             # Message utility tests
bun test:directMessage       # Direct message utility tests
bun test:auth:unit           # Authentication utility tests (email verification, refresh tokens)
bun test:friends:unit        # Friend management utility tests
bun test:status:unit         # User status management utility tests
bun test:channels:unit       # Channel management utility tests
bun test:permissions:unit    # Permission system utility tests
bun test:serverMembers:unit  # Server member management utility tests
bun test:binaryProtocol     # Binary protocol utility tests
bun test:sfu                # Selective Forwarding Unit (SFU) service tests
bun test:auth:integration    # Authentication handler tests
bun test:messages            # Message handler tests
bun test:directMessages      # Direct message handler tests
bun test:friends:integration # Friend management handler tests
bun test:status:integration  # User status management handler tests
bun test:channels:integration # Channel management handler tests
bun test:roles:integration   # Role management handler tests

# Run tests in watch mode (automatically re-run when files change)
bun test:watch
```

**Note:** Running multiple test files simultaneously can cause database connection issues due to how the postgres client handles connections. It's recommended to run tests one file at a time.

## Writing Tests

### Unit Tests

Unit tests should test individual functions in isolation. They should be placed in the `unit/` directory and named with the `.test.ts` extension.

Example:

```typescript
import { describe, expect, test } from "bun:test";
import { someFunction } from "../../path/to/function";

describe("someFunction", () => {
  test("should do something", () => {
    const result = someFunction();
    expect(result).toBe(expectedValue);
  });
});
```

### Integration Tests

Integration tests should test the interaction between multiple components, such as API endpoints and handlers. They should be placed in the `integration/` directory and named with the `.test.ts` extension.

Example:

```typescript
import { describe, expect, test } from "bun:test";
import { createMockRequest, parseResponseJson } from "../helpers";
import { someHandler } from "../../handlers/someHandler";

describe("someHandler", () => {
  test("should handle a request", async () => {
    const req = createMockRequest({ key: "value" });
    const response = await someHandler(req);
    expect(response.status).toBe(200);
    const data = await parseResponseJson(response);
    expect(data.success).toBe(true);
  });
});
```

## Test Helpers

The `helpers.ts` file contains utility functions for creating test data and performing common operations:

- `createTestUser()`: Creates a test user
- `createTestServer()`: Creates a test server
- `createTestChannel()`: Creates a test channel
- `createTestMessage()`: Creates a test message
- `createTestDirectMessage()`: Creates a test direct message
- `cleanupTestData()`: Cleans up all test data
- `createMockRequest()`: Creates a mock Request object
- `parseResponseJson()`: Parses a Response object into JSON

## Test Database

Tests use a separate schema in the database to avoid interfering with development data. The connection is configured in `setup.ts`.

## Note on EventTypes for Direct Messages

Currently, the direct message events use string literals for event types (e.g., 'DIRECT_MESSAGE_SENT', 'DIRECT_MESSAGE_UPDATED'). In the future, these should be added to the `@kurultai/oba-types` package's `EventTypes` enum for better type safety and consistency.

Example update for `@kurultai/oba-types`:

```typescript
export enum EventTypes {
  // Existing event types...

  // Direct message events
  DIRECT_MESSAGE_SEND = "DIRECT_MESSAGE_SEND",
  DIRECT_MESSAGE_SENT = "DIRECT_MESSAGE_SENT",
  DIRECT_MESSAGE_UPDATE = "DIRECT_MESSAGE_UPDATE",
  DIRECT_MESSAGE_UPDATED = "DIRECT_MESSAGE_UPDATED",
  DIRECT_MESSAGE_DELETE = "DIRECT_MESSAGE_DELETE",
  DIRECT_MESSAGE_DELETED = "DIRECT_MESSAGE_DELETED",
}
```

After updating the package, the string literals in the codebase should be replaced with the enum values.
