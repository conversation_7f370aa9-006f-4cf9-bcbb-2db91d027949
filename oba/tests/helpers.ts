// Test helper functions
import { db } from "./setup";
import {
  UserSchema,
  ServerSchema,
  ChannelSchema,
  MessageSchema,
  DirectMessageSchema,
  FriendshipSchema,
  ChannelPrivacySchema,
  ChannelAllowedRolesSchema,
  ServerRoleSchema,
  MessageReactionSchema,
  MessageReadSchema,
  ServerInviteSchema,
  ServerMembershipSchema,
  UserRoles,
  ChannelCategorySchema,
} from "../db/schema";
import { eq } from "drizzle-orm";
import * as argon2 from "argon2";
import crypto from "crypto";

/**
 * Create a test user
 */
export async function createTestUser(
  username: string = "testuser",
  email: string = "<EMAIL>",
  password: string = "Password123!",
  isEmailVerified: boolean = true,
) {
  const hashedPassword = await argon2.hash(password);

  const user = await db
    .insert(UserSchema)
    .values({
      username,
      email,
      password: hashedPassword,
      avatar: "https://example.com/avatar.png",
      isEmailVerified,
    })
    .returning();

  return user[0];
}

/**
 * Create a test server
 */
export async function createTestServer(
  ownerId: string,
  name: string = "Test Server",
  description: string = "A server for testing",
) {
  const server = await db
    .insert(ServerSchema)
    .values({
      name,
      description,
      ownerId,
      icon: "https://example.com/server-icon.png",
    })
    .returning();

  return server[0];
}

/**
 * Create a test channel
 */
export async function createTestChannel(
  serverId: string,
  name: string = "test-channel",
  type: "TEXT" | "VOICE" | "ANNOUNCEMENT" = "TEXT",
  categoryId?: string,
  position: number = 0,
) {
  const channel = await db
    .insert(ChannelSchema)
    .values({
      name,
      serverId,
      type,
      categoryId,
      position,
    })
    .returning();

  return channel[0];
}

/**
 * Create a test channel category
 */
export async function createTestCategory(
  serverId: string,
  name: string = "Test Category",
  description: string = "A test category",
  position: number = 0,
) {
  const category = await db
    .insert(ChannelCategorySchema)
    .values({
      name,
      description,
      serverId,
      position,
    })
    .returning();

  return category[0];
}

/**
 * Create a test message
 */
export async function createTestMessage(
  userId: string,
  channelId: string,
  content: string = "Test message content",
) {
  const message = await db
    .insert(MessageSchema)
    .values({
      userId,
      channelId,
      content,
    })
    .returning();

  return message[0];
}

/**
 * Create a test direct message
 */
export async function createTestDirectMessage(
  senderId: string,
  receiverId: string,
  content: string = "Test direct message content",
) {
  const message = await db
    .insert(DirectMessageSchema)
    .values({
      senderId,
      receiverId,
      content,
    })
    .returning();

  return message[0];
}

/**
 * Clean up test data
 */
export async function cleanupTestData() {
  try {
    // Delete in reverse order of dependencies
    // First, delete message reactions
    await db.delete(MessageReactionSchema);

    // Delete message reads
    await db.delete(MessageReadSchema);

    // Delete messages
    await db.delete(MessageSchema);

    // Delete direct messages
    await db.delete(DirectMessageSchema);

    // Delete channel allowed roles
    await db.delete(ChannelAllowedRolesSchema);

    // Delete channel privacy settings
    await db.delete(ChannelPrivacySchema);

    // Delete channels
    await db.delete(ChannelSchema);

    // Delete channel categories
    await db.delete(ChannelCategorySchema);

    // Delete server invites
    await db.delete(ServerInviteSchema);

    // Delete server memberships
    await db.delete(ServerMembershipSchema);

    // Delete user roles
    await db.delete(UserRoles);

    // Delete roles
    await db.delete(ServerRoleSchema);

    // Delete servers
    await db.delete(ServerSchema);

    // Delete friendships
    await db.delete(FriendshipSchema);

    // Delete users
    await db.delete(UserSchema);
  } catch (error) {
    console.error("Error cleaning up test data:", error);
  }
}

/**
 * Create a mock Request object
 */
export function createMockRequest(
  body: any,
  method: string = "POST",
  headers: Record<string, string> = { "Content-Type": "application/json" },
) {
  return new Request("http://localhost:3000", {
    method,
    headers,
    body: JSON.stringify(body),
  });
}

/**
 * Create a user with email verification token
 */
export async function createUserWithVerificationToken(
  username: string = "unverified",
  email: string = "<EMAIL>",
  password: string = "Password123!",
) {
  // Create an unverified user
  const user = await createTestUser(username, email, password, false);

  // Generate a verification token
  const token = crypto.randomUUID();
  const expiry = new Date(Date.now() + 86400000); // 24 hours from now

  // Update the user with the verification token
  await db
    .update(UserSchema)
    .set({
      emailVerificationToken: token,
      emailVerificationExpiry: expiry,
    })
    .where(eq(UserSchema.id, user.id));

  // Get the updated user
  const updatedUser = await db
    .select()
    .from(UserSchema)
    .where(eq(UserSchema.id, user.id))
    .limit(1);

  return {
    user: updatedUser[0],
    token,
  };
}

/**
 * Create a user with refresh token
 */
export async function createUserWithRefreshToken(
  username: string = "refreshuser",
  email: string = "<EMAIL>",
  password: string = "Password123!",
) {
  // Create a verified user
  const user = await createTestUser(username, email, password, true);

  // Generate a refresh token
  const refreshToken = crypto.randomUUID();
  const refreshTokenExpiry = new Date(Date.now() + 30 * 86400000); // 30 days from now

  // Update the user with the refresh token
  await db
    .update(UserSchema)
    .set({
      refreshToken,
      refreshTokenExpiry,
    })
    .where(eq(UserSchema.id, user.id));

  // Get the updated user
  const updatedUser = await db
    .select()
    .from(UserSchema)
    .where(eq(UserSchema.id, user.id))
    .limit(1);

  return {
    user: updatedUser[0],
    refreshToken,
  };
}

/**
 * Parse response JSON
 */
export async function parseResponseJson(response: Response) {
  const text = await response.text();
  try {
    return JSON.parse(text);
  } catch (error) {
    console.error("Error parsing response JSON:", error);
    console.error("Response text:", text);
    throw error;
  }
}

/**
 * Extract cookies from response
 */
export function extractCookies(response: Response): Record<string, string> {
  const cookies: Record<string, string> = {};
  const cookieHeader = response.headers.get("Set-Cookie");

  if (cookieHeader) {
    cookieHeader.split(",").forEach((cookie) => {
      const parts = cookie.split(";")[0].trim().split("=");
      if (parts.length === 2) {
        const name = parts[0].trim();
        const value = parts[1].trim();
        cookies[name] = value;
      }
    });
  }

  return cookies;
}

/**
 * Create a friendship between two users
 */
export async function createFriendship(
  userId: string,
  friendId: string,
  status: "PENDING" | "ACCEPTED" | "BLOCKED" = "ACCEPTED",
  initiatedBy: string = userId,
) {
  const friendship = await db
    .insert(FriendshipSchema)
    .values({
      userId,
      friendId,
      status,
      initiatedBy,
    })
    .returning();

  return friendship[0];
}

/**
 * Create a pending friend request
 */
export async function createPendingFriendRequest(
  senderId: string,
  recipientId: string,
) {
  return createFriendship(senderId, recipientId, "PENDING", senderId);
}

/**
 * Create an accepted friendship
 */
export async function createAcceptedFriendship(
  user1Id: string,
  user2Id: string,
) {
  return createFriendship(user1Id, user2Id, "ACCEPTED", user2Id);
}

/**
 * Create a blocked relationship
 */
export async function createBlockedRelationship(
  blockerId: string,
  blockedId: string,
) {
  return createFriendship(blockerId, blockedId, "BLOCKED", blockerId);
}

/**
 * Create a user with a specific status
 */
export async function createUserWithStatus(
  username: string = "statususer",
  email: string = "<EMAIL>",
  status: "ONLINE" | "AWAY" | "BUSY" | "INVISIBLE" | "OFFLINE" = "ONLINE",
  statusMessage: string = "Test status message",
) {
  const hashedPassword = await argon2.hash("Password123!");

  const user = await db
    .insert(UserSchema)
    .values({
      username,
      email,
      password: hashedPassword,
      avatar: "https://example.com/avatar.png",
      status,
      statusMessage,
      lastActive: new Date(),
      isEmailVerified: true,
    })
    .returning();

  return user[0];
}

/**
 * Create a test channel with privacy settings
 */
export async function createTestChannelWithPrivacy(
  serverId: string,
  name: string = "test-channel",
  type: "TEXT" | "VOICE" | "ANNOUNCEMENT" = "TEXT",
  isPublic: boolean = true,
  _description: string = "A test channel", // Unused parameter, prefixed with underscore
) {
  // Create the channel
  const channel = await createTestChannel(serverId, name, type);

  // Create privacy settings
  await db.insert(ChannelPrivacySchema).values({
    channelId: channel.id,
    isPublic,
  });

  // Return the channel with privacy settings
  const channelWithPrivacy = await db
    .select()
    .from(ChannelSchema)
    .where(eq(ChannelSchema.id, channel.id))
    .limit(1);

  const privacySettings = await db
    .select()
    .from(ChannelPrivacySchema)
    .where(eq(ChannelPrivacySchema.channelId, channel.id))
    .limit(1);

  return {
    ...channelWithPrivacy[0],
    privacySettings: privacySettings[0],
  };
}

/**
 * Create a test role
 */
export async function createTestRole(
  serverId: string,
  name: string = "Test Role",
  permissions: bigint = BigInt(3), // READ_MESSAGES (1) + SEND_MESSAGES (2)
) {
  const role = await db
    .insert(ServerRoleSchema)
    .values({
      serverId,
      name,
      permissions,
    })
    .returning();

  return role[0];
}

/**
 * Assign a role to a user
 */
export async function assignRoleToUser(
  userId: string,
  roleId: string,
  serverId: string,
) {
  const userRole = await db
    .insert(UserRoles)
    .values({
      userId,
      roleId,
      serverId,
    })
    .returning();

  return userRole[0];
}

/**
 * Create a test server with default roles
 */
export async function createTestServerWithRoles(
  ownerId: string,
  name: string = "Test Server With Roles",
) {
  // Create the server
  const server = await createTestServer(ownerId, name);

  // Import permission constants
  const { ADMINISTRATOR, MANAGE_CHANNELS, SEND_MESSAGES, VIEW_CHANNEL } =
    await import("../constants/permissions");

  // Create admin role
  const adminRole = await createTestRole(server.id, "Admin", ADMINISTRATOR);

  // Create moderator role
  const modRole = await createTestRole(
    server.id,
    "Moderator",
    MANAGE_CHANNELS | SEND_MESSAGES | VIEW_CHANNEL,
  );

  // Create member role
  const memberRole = await createTestRole(
    server.id,
    "Member",
    SEND_MESSAGES | VIEW_CHANNEL,
  );

  // Assign admin role to owner
  await assignRoleToUser(ownerId, adminRole.id, server.id);

  return {
    server,
    roles: {
      admin: adminRole,
      moderator: modRole,
      member: memberRole,
    },
  };
}

/**
 * Add allowed role to a channel
 */
export async function addAllowedRoleToChannel(
  channelId: string,
  roleId: string,
) {
  const allowedRole = await db
    .insert(ChannelAllowedRolesSchema)
    .values({
      channelId,
      roleId,
    })
    .returning();

  return allowedRole[0];
}

/**
 * Create multiple users with different statuses
 */
export async function createUsersWithStatuses() {
  const onlineUser = await createUserWithStatus(
    "online_user",
    "<EMAIL>",
    "ONLINE",
    "I am online",
  );
  const awayUser = await createUserWithStatus(
    "away_user",
    "<EMAIL>",
    "AWAY",
    "I am away",
  );
  const busyUser = await createUserWithStatus(
    "busy_user",
    "<EMAIL>",
    "BUSY",
    "I am busy",
  );
  const invisibleUser = await createUserWithStatus(
    "invisible_user",
    "<EMAIL>",
    "INVISIBLE",
    "I am invisible",
  );
  const offlineUser = await createUserWithStatus(
    "offline_user",
    "<EMAIL>",
    "OFFLINE",
    "I am offline",
  );

  return {
    onlineUser,
    awayUser,
    busyUser,
    invisibleUser,
    offlineUser,
  };
}
