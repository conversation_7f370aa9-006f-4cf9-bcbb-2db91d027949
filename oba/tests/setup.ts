// Test setup file
import { afterAll, beforeAll } from "bun:test";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { migrate } from "drizzle-orm/postgres-js/migrator";
import { join } from "path";

// Create a test database connection
// Note: For testing, we'll use the same database but with a different schema prefix
// In a production environment, you'd want to use a separate test database
const TEST_DB_URL =
  process.env.TEST_DB_URL ||
  process.env.DATABASE_URL ||
  "postgres://metadata_user:metadata_password@localhost:5432/oba_test";
console.log(TEST_DB_URL);
const client = postgres(TEST_DB_URL);
// Use the same schema as the main app
export const db = drizzle(client);

// Setup function to run before all tests
beforeAll(async () => {
  console.log("Setting up test database...");

  //Skip migrations for now as they're not properly set up
  //In a real project, you'd want to run migrations to ensure the test database schema is correct
  try {
    //await migrate(db, { migrationsFolder: join(__dirname, '../drizzle') });
    console.log("Migrations completed successfully");
  } catch (error) {
    console.error("Error running migrations:", error);
  }

  // Additional setup can be added here
});

// Cleanup function to run after all tests
afterAll(async () => {
  console.log("Cleaning up test database...");

  // Add a small delay to allow pending queries to complete
  await new Promise((resolve) => setTimeout(resolve, 5));

  try {
    // Close the database connection
    await client.end({ timeout: 15 });
    console.log("Database connection closed successfully");
  } catch (error) {
    console.error("Error closing database connection:", error);
  }
});
