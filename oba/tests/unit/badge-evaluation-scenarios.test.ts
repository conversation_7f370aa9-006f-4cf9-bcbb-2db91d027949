import { describe, test, expect, beforeEach, afterEach, mock, spyOn } from "bun:test";
import { BadgeEvaluationService } from "../../services/badge-evaluation.service";
import type {
  BadgeType,
  UserBadge,
  UserStats,
  BadgeCriteria,
  EvaluationResult,
  BadgeProgress
} from "../../types/badge.types";

// Mock database
const mockDb = {} as any;

// Mock utility functions
const mockBadgeUtils = {
  getUserStats: mock(),
  assignBadgeToUser: mock(),
  getUserBadges: mock(),
  getBadgeTypes: mock()
};

const mockBadgeEvaluation = {
  evaluateUserForAutomaticBadges: mock(),
  batchEvaluateUsers: mock(),
  evaluateAllUsersForAutomaticBadges: mock(),
  reevaluateBadgeTypeForAllUsers: mock(),
  getUsersNearBadgeCompletion: mock(),
  evaluateBadgeCriteria: mock()
};

const mockBadgeWebSocket = {
  broadcastBatchBadgeAssigned: mock(),
  broadcastBatchProgressUpdates: mock()
};

// Mock the imports
mock.module("../../db/utils/badge-utils", () => mockBadgeUtils);
mock.module("../../db/utils/badge-evaluation", () => mockBadgeEvaluation);
mock.module("../../utils/badge-websocket", () => ({ badgeWebSocketService: mockBadgeWebSocket }));
mock.module("../../db", () => ({ db: mockDb }));

describe("Badge Evaluation Scenarios Tests", () => {
  let badgeEvaluationService: BadgeEvaluationService;

  const mockUserId = "550e8400-e29b-41d4-a716-446655440000";

  beforeEach(() => {
    badgeEvaluationService = new BadgeEvaluationService(mockDb);
    
    // Reset all mocks
    Object.values(mockBadgeUtils).forEach(mock => mock.mockReset());
    Object.values(mockBadgeEvaluation).forEach(mock => mock.mockReset());
    Object.values(mockBadgeWebSocket).forEach(mock => mock.mockReset());
  });

  afterEach(() => {
    // Clean up any test state
  });

  describe("Message Count Badge Scenarios", () => {
    const messageCountBadges: BadgeType[] = [
      {
        id: "first-message",
        badgeId: "first-message",
        name: "First Message",
        description: "Sent your first message",
        icon: "💬",
        design: { shape: "circle", background: "#4CAF50", colors: ["#4CAF50"] },
        criteria: {
          requirement: "Send your first message",
          tracked: "message_count",
          type: "message_count",
          threshold: 1
        },
        unlockType: "automatic",
        category: "milestone",
        displayOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "chatty",
        badgeId: "chatty",
        name: "Chatty",
        description: "Sent 100 messages",
        icon: "💭",
        design: { shape: "circle", background: "#2196F3", colors: ["#2196F3"] },
        criteria: {
          requirement: "Send 100 messages",
          tracked: "message_count",
          type: "message_count",
          threshold: 100
        },
        unlockType: "automatic",
        category: "achievement",
        displayOrder: 2,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "conversation-master",
        badgeId: "conversation-master",
        name: "Conversation Master",
        description: "Sent 1000 messages",
        icon: "🗣️",
        design: { shape: "circle", background: "#FF9800", colors: ["#FF9800"] },
        criteria: {
          requirement: "Send 1000 messages",
          tracked: "message_count",
          type: "message_count",
          threshold: 1000
        },
        unlockType: "automatic",
        category: "achievement",
        displayOrder: 3,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    test("should award first message badge to new user", async () => {
      const newUserStats: UserStats = {
        messageCount: 1,
        serverCount: 1,
        friendCount: 0,
        daysActive: 1,
        accountAge: 1,
        lastActive: new Date(),
        invitesSent: 0,
        invitesAccepted: 0,
        feedbackSubmitted: 0,
        moderationActions: 0
      };

      const expectedNewBadge: UserBadge = {
        id: "new-first-message-badge",
        userId: mockUserId,
        badgeTypeId: "first-message",
        assignedAt: new Date(),
        isVisible: true,
        badgeType: messageCountBadges[0]
      };

      const mockResult: EvaluationResult = {
        userId: mockUserId,
        newBadges: [expectedNewBadge],
        evaluatedBadges: ["first-message"],
        collectionProgress: [],
        errors: []
      };

      mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockResolvedValue(mockResult);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const result = await badgeEvaluationService.evaluateUser(mockUserId);

      expect(result.newBadges).toHaveLength(1);
      expect(result.newBadges[0].badgeTypeId).toBe("first-message");
      expect(result.errors).toHaveLength(0);
      expect(mockBadgeWebSocket.broadcastBatchBadgeAssigned).toHaveBeenCalledWith([
        {
          userId: mockUserId,
          badge: expectedNewBadge,
          isAutomatic: true,
          assignedBy: undefined
        }
      ]);
    });

    test("should award multiple message badges for active user", async () => {
      const activeUserStats: UserStats = {
        messageCount: 150,
        serverCount: 3,
        friendCount: 5,
        daysActive: 30,
        accountAge: 45,
        lastActive: new Date(),
        invitesSent: 2,
        invitesAccepted: 1,
        feedbackSubmitted: 1,
        moderationActions: 0
      };

      const expectedNewBadges: UserBadge[] = [
        {
          id: "new-first-message-badge",
          userId: mockUserId,
          badgeTypeId: "first-message",
          assignedAt: new Date(),
          isVisible: true,
          badgeType: messageCountBadges[0]
        },
        {
          id: "new-chatty-badge",
          userId: mockUserId,
          badgeTypeId: "chatty",
          assignedAt: new Date(),
          isVisible: true,
          badgeType: messageCountBadges[1]
        }
      ];

      const mockResult: EvaluationResult = {
        userId: mockUserId,
        newBadges: expectedNewBadges,
        evaluatedBadges: ["first-message", "chatty", "conversation-master"],
        collectionProgress: [],
        errors: []
      };

      mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockResolvedValue(mockResult);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const result = await badgeEvaluationService.evaluateUser(mockUserId);

      expect(result.newBadges).toHaveLength(2);
      expect(result.newBadges.map(b => b.badgeTypeId)).toContain("first-message");
      expect(result.newBadges.map(b => b.badgeTypeId)).toContain("chatty");
      expect(result.errors).toHaveLength(0);
    });

    test("should not award badges already earned", async () => {
      const userStats: UserStats = {
        messageCount: 50,
        serverCount: 2,
        friendCount: 3,
        daysActive: 15,
        accountAge: 20,
        lastActive: new Date(),
        invitesSent: 1,
        invitesAccepted: 0,
        feedbackSubmitted: 0,
        moderationActions: 0
      };

      const existingBadge: UserBadge = {
        id: "existing-first-message-badge",
        userId: mockUserId,
        badgeTypeId: "first-message",
        assignedAt: new Date(Date.now() - ********), // 1 day ago
        isVisible: true,
        badgeType: messageCountBadges[0]
      };

      const mockResult: EvaluationResult = {
        userId: mockUserId,
        newBadges: [], // No new badges since first-message already earned
        evaluatedBadges: ["first-message", "chatty", "conversation-master"],
        collectionProgress: [],
        errors: []
      };

      mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockResolvedValue(mockResult);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const result = await badgeEvaluationService.evaluateUser(mockUserId);

      expect(result.newBadges).toHaveLength(0);
      expect(result.evaluatedBadges).toContain("first-message");
      expect(result.errors).toHaveLength(0);
    });
  });

  describe("Server Count Badge Scenarios", () => {
    const serverBadges: BadgeType[] = [
      {
        id: "server-creator",
        badgeId: "server-creator",
        name: "Server Creator",
        description: "Created your first server",
        icon: "🏗️",
        design: { shape: "hexagon", background: "#9C27B0", colors: ["#9C27B0"] },
        criteria: {
          requirement: "Create your first server",
          tracked: "server_count",
          type: "server_count",
          threshold: 1
        },
        unlockType: "automatic",
        category: "achievement",
        displayOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "community-builder",
        badgeId: "community-builder",
        name: "Community Builder",
        description: "Created 5 servers",
        icon: "🏘️",
        design: { shape: "hexagon", background: "#673AB7", colors: ["#673AB7"] },
        criteria: {
          requirement: "Create 5 servers",
          tracked: "server_count",
          type: "server_count",
          threshold: 5
        },
        unlockType: "automatic",
        category: "achievement",
        displayOrder: 2,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    test("should award server creator badge", async () => {
      const serverCreatorStats: UserStats = {
        messageCount: 25,
        serverCount: 1,
        friendCount: 2,
        daysActive: 5,
        accountAge: 7,
        lastActive: new Date(),
        invitesSent: 3,
        invitesAccepted: 2,
        feedbackSubmitted: 0,
        moderationActions: 0
      };

      const expectedNewBadge: UserBadge = {
        id: "new-server-creator-badge",
        userId: mockUserId,
        badgeTypeId: "server-creator",
        assignedAt: new Date(),
        isVisible: true,
        badgeType: serverBadges[0]
      };

      const mockResult: EvaluationResult = {
        userId: mockUserId,
        newBadges: [expectedNewBadge],
        evaluatedBadges: ["server-creator"],
        collectionProgress: [],
        errors: []
      };

      mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockResolvedValue(mockResult);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const result = await badgeEvaluationService.evaluateUser(mockUserId);

      expect(result.newBadges).toHaveLength(1);
      expect(result.newBadges[0].badgeTypeId).toBe("server-creator");
      expect(result.errors).toHaveLength(0);
    });

    test("should award community builder badge for multiple servers", async () => {
      const communityBuilderStats: UserStats = {
        messageCount: 200,
        serverCount: 6,
        friendCount: 15,
        daysActive: 60,
        accountAge: 90,
        lastActive: new Date(),
        invitesSent: 20,
        invitesAccepted: 15,
        feedbackSubmitted: 3,
        moderationActions: 2
      };

      const expectedNewBadges: UserBadge[] = [
        {
          id: "new-server-creator-badge",
          userId: mockUserId,
          badgeTypeId: "server-creator",
          assignedAt: new Date(),
          isVisible: true,
          badgeType: serverBadges[0]
        },
        {
          id: "new-community-builder-badge",
          userId: mockUserId,
          badgeTypeId: "community-builder",
          assignedAt: new Date(),
          isVisible: true,
          badgeType: serverBadges[1]
        }
      ];

      const mockResult: EvaluationResult = {
        userId: mockUserId,
        newBadges: expectedNewBadges,
        evaluatedBadges: ["server-creator", "community-builder"],
        collectionProgress: [],
        errors: []
      };

      mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockResolvedValue(mockResult);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const result = await badgeEvaluationService.evaluateUser(mockUserId);

      expect(result.newBadges).toHaveLength(2);
      expect(result.newBadges.map(b => b.badgeTypeId)).toContain("server-creator");
      expect(result.newBadges.map(b => b.badgeTypeId)).toContain("community-builder");
    });
  });

  describe("Friend Count Badge Scenarios", () => {
    const friendBadges: BadgeType[] = [
      {
        id: "social-butterfly",
        badgeId: "social-butterfly",
        name: "Social Butterfly",
        description: "Made 10 friends",
        icon: "🦋",
        design: { shape: "circle", background: "#E91E63", colors: ["#E91E63"] },
        criteria: {
          requirement: "Make 10 friends",
          tracked: "friend_count",
          type: "friend_count",
          threshold: 10
        },
        unlockType: "automatic",
        category: "community",
        displayOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "network-master",
        badgeId: "network-master",
        name: "Network Master",
        description: "Made 50 friends",
        icon: "🕸️",
        design: { shape: "circle", background: "#3F51B5", colors: ["#3F51B5"] },
        criteria: {
          requirement: "Make 50 friends",
          tracked: "friend_count",
          type: "friend_count",
          threshold: 50
        },
        unlockType: "automatic",
        category: "community",
        displayOrder: 2,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    test("should award social butterfly badge", async () => {
      const socialUserStats: UserStats = {
        messageCount: 75,
        serverCount: 2,
        friendCount: 12,
        daysActive: 20,
        accountAge: 25,
        lastActive: new Date(),
        invitesSent: 5,
        invitesAccepted: 3,
        feedbackSubmitted: 1,
        moderationActions: 0
      };

      const expectedNewBadge: UserBadge = {
        id: "new-social-butterfly-badge",
        userId: mockUserId,
        badgeTypeId: "social-butterfly",
        assignedAt: new Date(),
        isVisible: true,
        badgeType: friendBadges[0]
      };

      const mockResult: EvaluationResult = {
        userId: mockUserId,
        newBadges: [expectedNewBadge],
        evaluatedBadges: ["social-butterfly"],
        collectionProgress: [],
        errors: []
      };

      mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockResolvedValue(mockResult);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const result = await badgeEvaluationService.evaluateUser(mockUserId);

      expect(result.newBadges).toHaveLength(1);
      expect(result.newBadges[0].badgeTypeId).toBe("social-butterfly");
    });
  });

  describe("Complex Criteria Badge Scenarios", () => {
    const complexBadges: BadgeType[] = [
      {
        id: "early-adopter",
        badgeId: "early-adopter",
        name: "Early Adopter",
        description: "Joined within the first 100 users",
        icon: "🚀",
        design: { shape: "star", background: "#FF5722", colors: ["#FF5722"] },
        criteria: {
          requirement: "Be among the first 100 users",
          tracked: "signup_order",
          type: "custom",
          threshold: 100,
          conditions: {
            signupOrder: 100
          }
        },
        unlockType: "automatic",
        category: "special",
        displayOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "veteran",
        badgeId: "veteran",
        name: "Veteran",
        description: "Active for 365 days with 500+ messages",
        icon: "🎖️",
        design: { shape: "shield", background: "#795548", colors: ["#795548"] },
        criteria: {
          requirement: "Be active for 365 days and send 500+ messages",
          tracked: "combined",
          type: "custom",
          threshold: 1,
          conditions: {
            combinedRequirements: {
              daysActive: 365,
              messages: 500
            }
          }
        },
        unlockType: "automatic",
        category: "special",
        displayOrder: 2,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    test("should award early adopter badge to early user", async () => {
      const earlyUserStats: UserStats = {
        messageCount: 10,
        serverCount: 1,
        friendCount: 2,
        daysActive: 5,
        accountAge: 5,
        lastActive: new Date(),
        invitesSent: 1,
        invitesAccepted: 0,
        feedbackSubmitted: 0,
        moderationActions: 0,
        signupOrder: 42 // Early user
      };

      const expectedNewBadge: UserBadge = {
        id: "new-early-adopter-badge",
        userId: mockUserId,
        badgeTypeId: "early-adopter",
        assignedAt: new Date(),
        isVisible: true,
        badgeType: complexBadges[0]
      };

      const mockResult: EvaluationResult = {
        userId: mockUserId,
        newBadges: [expectedNewBadge],
        evaluatedBadges: ["early-adopter"],
        collectionProgress: [],
        errors: []
      };

      mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockResolvedValue(mockResult);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const result = await badgeEvaluationService.evaluateUser(mockUserId);

      expect(result.newBadges).toHaveLength(1);
      expect(result.newBadges[0].badgeTypeId).toBe("early-adopter");
    });

    test("should award veteran badge to long-term active user", async () => {
      const veteranUserStats: UserStats = {
        messageCount: 750,
        serverCount: 8,
        friendCount: 25,
        daysActive: 400,
        accountAge: 450,
        lastActive: new Date(),
        invitesSent: 15,
        invitesAccepted: 12,
        feedbackSubmitted: 5,
        moderationActions: 3
      };

      const expectedNewBadge: UserBadge = {
        id: "new-veteran-badge",
        userId: mockUserId,
        badgeTypeId: "veteran",
        assignedAt: new Date(),
        isVisible: true,
        badgeType: complexBadges[1]
      };

      const mockResult: EvaluationResult = {
        userId: mockUserId,
        newBadges: [expectedNewBadge],
        evaluatedBadges: ["veteran"],
        collectionProgress: [],
        errors: []
      };

      mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockResolvedValue(mockResult);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const result = await badgeEvaluationService.evaluateUser(mockUserId);

      expect(result.newBadges).toHaveLength(1);
      expect(result.newBadges[0].badgeTypeId).toBe("veteran");
    });

    test("should not award veteran badge if criteria not fully met", async () => {
      const almostVeteranStats: UserStats = {
        messageCount: 300, // Not enough messages (needs 500+)
        serverCount: 5,
        friendCount: 20,
        daysActive: 400, // Enough days
        accountAge: 450,
        lastActive: new Date(),
        invitesSent: 10,
        invitesAccepted: 8,
        feedbackSubmitted: 2,
        moderationActions: 1
      };

      const mockResult: EvaluationResult = {
        userId: mockUserId,
        newBadges: [], // No new badges - criteria not met
        evaluatedBadges: ["veteran"],
        collectionProgress: [],
        errors: []
      };

      mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockResolvedValue(mockResult);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const result = await badgeEvaluationService.evaluateUser(mockUserId);

      expect(result.newBadges).toHaveLength(0);
      expect(result.evaluatedBadges).toContain("veteran");
    });
  });

  describe("Badge Progress Tracking Scenarios", () => {
    test("should track progress toward message count badges", async () => {
      const progressUserStats: UserStats = {
        messageCount: 75, // 75% toward chatty badge (100 messages)
        serverCount: 2,
        friendCount: 5,
        daysActive: 15,
        accountAge: 20,
        lastActive: new Date(),
        invitesSent: 2,
        invitesAccepted: 1,
        feedbackSubmitted: 0,
        moderationActions: 0
      };

      const chattyBadge: BadgeType = {
        id: "chatty",
        badgeId: "chatty",
        name: "Chatty",
        description: "Send 100 messages",
        icon: "💭",
        design: { shape: "circle", background: "#2196F3", colors: ["#2196F3"] },
        criteria: {
          requirement: "Send 100 messages",
          tracked: "message_count",
          type: "message_count",
          threshold: 100
        },
        unlockType: "automatic",
        category: "achievement",
        displayOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockBadgeUtils.getUserBadges.mockResolvedValue([]);
      mockBadgeUtils.getBadgeTypes.mockResolvedValue([chattyBadge]);
      mockBadgeUtils.getUserStats.mockResolvedValue(progressUserStats);

      const result = await badgeEvaluationService.getBadgeProgress(mockUserId);

      expect(result).toHaveLength(1);
      expect(result[0].badgeTypeId).toBe("chatty");
      expect(result[0].progress).toBe(75);
      expect(result[0].total).toBe(100);
      expect(result[0].isEarned).toBe(false);
    });

    test("should show completed progress for earned badges", async () => {
      const completedUserStats: UserStats = {
        messageCount: 150,
        serverCount: 3,
        friendCount: 8,
        daysActive: 25,
        accountAge: 30,
        lastActive: new Date(),
        invitesSent: 3,
        invitesAccepted: 2,
        feedbackSubmitted: 1,
        moderationActions: 0
      };

      const chattyBadge: BadgeType = {
        id: "chatty",
        badgeId: "chatty",
        name: "Chatty",
        description: "Send 100 messages",
        icon: "💭",
        design: { shape: "circle", background: "#2196F3", colors: ["#2196F3"] },
        criteria: {
          requirement: "Send 100 messages",
          tracked: "message_count",
          type: "message_count",
          threshold: 100
        },
        unlockType: "automatic",
        category: "achievement",
        displayOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const earnedBadge: UserBadge = {
        id: "earned-chatty-badge",
        userId: mockUserId,
        badgeTypeId: "chatty",
        assignedAt: new Date(),
        isVisible: true,
        badgeType: chattyBadge
      };

      mockBadgeUtils.getUserBadges.mockResolvedValue([earnedBadge]);
      mockBadgeUtils.getBadgeTypes.mockResolvedValue([chattyBadge]);
      mockBadgeUtils.getUserStats.mockResolvedValue(completedUserStats);

      const result = await badgeEvaluationService.getBadgeProgress(mockUserId);

      expect(result).toHaveLength(1);
      expect(result[0].badgeTypeId).toBe("chatty");
      expect(result[0].progress).toBe(100); // Capped at threshold
      expect(result[0].total).toBe(100);
      expect(result[0].isEarned).toBe(true);
    });
  });

  describe("Batch Evaluation Scenarios", () => {
    test("should evaluate multiple users with different outcomes", async () => {
      const userIds = ["user-1", "user-2", "user-3"];
      
      const mockResults: EvaluationResult[] = [
        {
          userId: "user-1",
          newBadges: [{
            id: "new-badge-user-1",
            userId: "user-1",
            badgeTypeId: "first-message",
            assignedAt: new Date(),
            isVisible: true
          }],
          evaluatedBadges: ["first-message"],
          collectionProgress: [],
          errors: []
        },
        {
          userId: "user-2",
          newBadges: [], // No new badges
          evaluatedBadges: ["first-message", "chatty"],
          collectionProgress: [],
          errors: []
        },
        {
          userId: "user-3",
          newBadges: [{
            id: "new-badge-user-3-1",
            userId: "user-3",
            badgeTypeId: "first-message",
            assignedAt: new Date(),
            isVisible: true
          }, {
            id: "new-badge-user-3-2",
            userId: "user-3",
            badgeTypeId: "server-creator",
            assignedAt: new Date(),
            isVisible: true
          }],
          evaluatedBadges: ["first-message", "server-creator"],
          collectionProgress: [],
          errors: []
        }
      ];

      mockBadgeEvaluation.batchEvaluateUsers.mockResolvedValue(mockResults);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const results = await badgeEvaluationService.evaluateUsers(userIds);

      expect(results).toHaveLength(3);
      expect(results[0].newBadges).toHaveLength(1);
      expect(results[1].newBadges).toHaveLength(0);
      expect(results[2].newBadges).toHaveLength(2);

      // Should broadcast all new badges
      expect(mockBadgeWebSocket.broadcastBatchBadgeAssigned).toHaveBeenCalledWith([
        { userId: "user-1", badge: mockResults[0].newBadges[0], isAutomatic: true, assignedBy: undefined },
        { userId: "user-3", badge: mockResults[2].newBadges[0], isAutomatic: true, assignedBy: undefined },
        { userId: "user-3", badge: mockResults[2].newBadges[1], isAutomatic: true, assignedBy: undefined }
      ]);
    });

    test("should handle evaluation errors gracefully in batch", async () => {
      const userIds = ["user-1", "user-2", "user-3"];
      
      const mockResults: EvaluationResult[] = [
        {
          userId: "user-1",
          newBadges: [],
          evaluatedBadges: [],
          collectionProgress: [],
          errors: ["Database connection failed"]
        },
        {
          userId: "user-2",
          newBadges: [{
            id: "new-badge-user-2",
            userId: "user-2",
            badgeTypeId: "first-message",
            assignedAt: new Date(),
            isVisible: true
          }],
          evaluatedBadges: ["first-message"],
          collectionProgress: [],
          errors: []
        },
        {
          userId: "user-3",
          newBadges: [],
          evaluatedBadges: [],
          collectionProgress: [],
          errors: ["Invalid user stats"]
        }
      ];

      mockBadgeEvaluation.batchEvaluateUsers.mockResolvedValue(mockResults);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const results = await badgeEvaluationService.evaluateUsers(userIds);

      expect(results).toHaveLength(3);
      expect(results[0].errors).toHaveLength(1);
      expect(results[1].errors).toHaveLength(0);
      expect(results[2].errors).toHaveLength(1);

      // Should only broadcast successful badge assignments
      expect(mockBadgeWebSocket.broadcastBatchBadgeAssigned).toHaveBeenCalledWith([
        { userId: "user-2", badge: mockResults[1].newBadges[0], isAutomatic: true, assignedBy: undefined }
      ]);
    });
  });

  describe("Users Near Completion Scenarios", () => {
    test("should identify users close to earning badges", async () => {
      const nearCompletionUsers = [
        {
          userId: "user-1",
          username: "AlmostThere1",
          progress: 95,
          total: 100,
          progressPercentage: 0.95
        },
        {
          userId: "user-2",
          username: "SoClose2",
          progress: 88,
          total: 100,
          progressPercentage: 0.88
        }
      ];

      mockBadgeEvaluation.getUsersNearBadgeCompletion.mockResolvedValue(nearCompletionUsers);

      const result = await badgeEvaluationService.getUsersNearCompletion("chatty", 0.8);

      expect(result).toEqual(nearCompletionUsers);
      expect(result).toHaveLength(2);
      expect(result.every(user => user.progressPercentage >= 0.8)).toBe(true);
      expect(mockBadgeEvaluation.getUsersNearBadgeCompletion).toHaveBeenCalledWith(mockDb, "chatty", 0.8);
    });
  });

  describe("Smart Batch Evaluation Scenarios", () => {
    test("should prioritize active users in smart evaluation", async () => {
      const userIds = ["user-1", "user-2", "user-3"];
      
      // Mock getUserStats to return different activity levels
      mockBadgeUtils.getUserStats
        .mockResolvedValueOnce({ messageCount: 500, serverCount: 5, friendCount: 20, daysActive: 100, accountAge: 120, lastActive: new Date(), invitesSent: 10, invitesAccepted: 8, feedbackSubmitted: 3, moderationActions: 1 })
        .mockResolvedValueOnce({ messageCount: 10, serverCount: 1, friendCount: 2, daysActive: 5, accountAge: 7, lastActive: new Date(), invitesSent: 0, invitesAccepted: 0, feedbackSubmitted: 0, moderationActions: 0 })
        .mockResolvedValueOnce({ messageCount: 200, serverCount: 3, friendCount: 10, daysActive: 50, accountAge: 60, lastActive: new Date(), invitesSent: 5, invitesAccepted: 4, feedbackSubmitted: 1, moderationActions: 0 });

      // Mock getBadgeProgress to return near completion for active users
      const getBadgeProgressSpy = spyOn(badgeEvaluationService, "getBadgeProgress");
      getBadgeProgressSpy
        .mockResolvedValueOnce([{ badgeTypeId: "veteran", badgeType: {} as BadgeType, progress: 95, total: 100, isEarned: false }])
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([{ badgeTypeId: "chatty", badgeType: {} as BadgeType, progress: 80, total: 100, isEarned: false }]);

      // Mock evaluateUsers to return results
      const evaluateUsersSpy = spyOn(badgeEvaluationService, "evaluateUsers");
      evaluateUsersSpy.mockResolvedValue([
        { userId: "user-1", newBadges: [], evaluatedBadges: [], collectionProgress: [], errors: [] },
        { userId: "user-3", newBadges: [], evaluatedBadges: [], collectionProgress: [], errors: [] },
        { userId: "user-2", newBadges: [], evaluatedBadges: [], collectionProgress: [], errors: [] }
      ]);

      const result = await badgeEvaluationService.smartBatchEvaluation(userIds, {
        prioritizeActiveUsers: true,
        prioritizeNearCompletion: true,
        maxBatchSize: 10
      });

      expect(result.totalEvaluated).toBe(3);
      expect(evaluateUsersSpy).toHaveBeenCalled();
      
      // Verify that users were prioritized (user-1 should be first due to high activity)
      const evaluatedUsers = evaluateUsersSpy.mock.calls[0][0];
      expect(evaluatedUsers[0]).toBe("user-1"); // Most active user should be first
    });
  });
});