import { describe, test, expect, beforeEach } from "bun:test";
import {
  validateCreateBadgeType,
  validateUpdateBadgeType,
  validateAssignBadge,
  validateBadgeFilters
} from "../../utils/badge-validation";
import { BadgeValidationError } from "../../class/badge-errors";
import type {
  CreateBadgeTypeRequest,
  UpdateBadgeTypeRequest,
  BadgeTypeFilters
} from "../../types/badge.types";

describe("Badge Validation Tests", () => {
  describe("Badge Type Creation Validation", () => {
    test("should validate valid badge type creation request", () => {
      const validRequest: CreateBadgeTypeRequest = {
        badgeId: "test-badge",
        name: "Test Badge",
        description: "A test badge for validation",
        icon: "🏆",
        design: {
          shape: "circle",
          background: "#FFD700",
          colors: ["#FFD700", "#FFA500"]
        },
        criteria: {
          requirement: "Send 10 messages",
          tracked: "message_count",
          type: "message_count",
          threshold: 10
        },
        unlockType: "automatic",
        category: "achievement",
        displayOrder: 1
      };

      expect(() => validateCreateBadgeType(validRequest)).not.toThrow();
    });

    test("should reject badge type with empty name", () => {
      const invalidRequest: CreateBadgeTypeRequest = {
        badgeId: "test-badge",
        name: "",
        description: "A test badge",
        icon: "🏆",
        design: {
          shape: "circle",
          background: "#FFD700",
          colors: ["#FFD700"]
        },
        criteria: {
          requirement: "Test",
          tracked: "test",
          type: "message_count",
          threshold: 1
        },
        unlockType: "manual",
        category: "achievement",
        displayOrder: 1
      };

      expect(() => validateCreateBadgeType(invalidRequest)).toThrow(BadgeValidationError);
    });

    test("should reject badge type with invalid category", () => {
      const invalidRequest = {
        badgeId: "test-badge",
        name: "Test Badge",
        description: "A test badge",
        icon: "🏆",
        design: {
          shape: "circle",
          background: "#FFD700",
          colors: ["#FFD700"]
        },
        criteria: {
          requirement: "Test",
          tracked: "test",
          type: "message_count",
          threshold: 1
        },
        unlockType: "manual",
        category: "invalid-category", // Invalid category
        displayOrder: 1
      } as any;

      expect(() => validateCreateBadgeType(invalidRequest)).toThrow(BadgeValidationError);
    });

    test("should reject automatic badge without criteria", () => {
      const invalidRequest: CreateBadgeTypeRequest = {
        badgeId: "auto-badge",
        name: "Auto Badge",
        description: "Automatic badge without criteria",
        icon: "🤖",
        design: {
          shape: "circle",
          background: "#00FF00",
          colors: ["#00FF00"]
        },
        criteria: {
          requirement: "",
          tracked: "",
          type: "message_count"
          // Missing threshold for automatic badge
        },
        unlockType: "automatic",
        category: "achievement",
        displayOrder: 1
      };

      expect(() => validateCreateBadgeType(invalidRequest)).toThrow(BadgeValidationError);
    });

    test("should validate manual badge without threshold", () => {
      const validRequest: CreateBadgeTypeRequest = {
        badgeId: "manual-badge",
        name: "Manual Badge",
        description: "Manually assigned badge",
        icon: "👤",
        design: {
          shape: "square",
          background: "#FF0000",
          colors: ["#FF0000"]
        },
        criteria: {
          requirement: "Special recognition",
          tracked: "manual",
          type: "custom"
          // No threshold needed for manual badges
        },
        unlockType: "manual",
        category: "special",
        displayOrder: 1
      };

      expect(() => validateCreateBadgeType(validRequest)).not.toThrow();
    });
  });

  describe("Badge Type Update Validation", () => {
    test("should validate partial badge type updates", () => {
      const validUpdate: UpdateBadgeTypeRequest = {
        name: "Updated Badge Name",
        description: "Updated description"
      };

      expect(() => validateUpdateBadgeType(validUpdate)).not.toThrow();
    });

    test("should validate design updates", () => {
      const validUpdate: UpdateBadgeTypeRequest = {
        design: {
          shape: "hexagon",
          background: "#9C27B0",
          colors: ["#9C27B0", "#673AB7"]
        }
      };

      expect(() => validateUpdateBadgeType(validUpdate)).not.toThrow();
    });

    test("should reject invalid category in update", () => {
      const invalidUpdate = {
        category: "invalid-category"
      } as any;

      expect(() => validateUpdateBadgeType(invalidUpdate)).toThrow(BadgeValidationError);
    });

    test("should reject empty name in update", () => {
      const invalidUpdate: UpdateBadgeTypeRequest = {
        name: ""
      };

      expect(() => validateUpdateBadgeType(invalidUpdate)).toThrow(BadgeValidationError);
    });
  });

  describe("Badge Assignment Validation", () => {
    test("should validate valid badge assignment", () => {
      const validAssignment = {
        userId: "550e8400-e29b-41d4-a716-************",
        badgeTypeId: "550e8400-e29b-41d4-a716-************"
      };

      expect(() => validateAssignBadge(validAssignment)).not.toThrow();
    });

    test("should reject assignment with invalid user ID", () => {
      const invalidAssignment = {
        userId: "invalid-uuid",
        badgeTypeId: "550e8400-e29b-41d4-a716-************"
      };

      expect(() => validateAssignBadge(invalidAssignment)).toThrow(BadgeValidationError);
    });

    test("should reject assignment with invalid badge type ID", () => {
      const invalidAssignment = {
        userId: "550e8400-e29b-41d4-a716-************",
        badgeTypeId: "invalid-uuid"
      };

      expect(() => validateAssignBadge(invalidAssignment)).toThrow(BadgeValidationError);
    });

    test("should reject assignment with missing fields", () => {
      const invalidAssignment = {
        userId: "550e8400-e29b-41d4-a716-************"
        // Missing badgeTypeId
      } as any;

      expect(() => validateAssignBadge(invalidAssignment)).toThrow(BadgeValidationError);
    });
  });

  describe("Badge Filters Validation", () => {
    test("should validate valid badge filters", () => {
      const validFilters: BadgeTypeFilters = {
        category: "achievement",
        unlockType: "automatic",
        isActive: true,
        search: "test"
      };

      expect(() => validateBadgeFilters(validFilters)).not.toThrow();
    });

    test("should validate empty filters", () => {
      const emptyFilters: BadgeTypeFilters = {};

      expect(() => validateBadgeFilters(emptyFilters)).not.toThrow();
    });

    test("should reject invalid category filter", () => {
      const invalidFilters = {
        category: "invalid-category"
      } as any;

      expect(() => validateBadgeFilters(invalidFilters)).toThrow(BadgeValidationError);
    });

    test("should reject invalid unlock type filter", () => {
      const invalidFilters = {
        unlockType: "invalid-type"
      } as any;

      expect(() => validateBadgeFilters(invalidFilters)).toThrow(BadgeValidationError);
    });

    test("should validate boolean isActive filter", () => {
      const validFilters: BadgeTypeFilters = {
        isActive: false
      };

      expect(() => validateBadgeFilters(validFilters)).not.toThrow();
    });

    test("should validate search string filter", () => {
      const validFilters: BadgeTypeFilters = {
        search: "achievement badge"
      };

      expect(() => validateBadgeFilters(validFilters)).not.toThrow();
    });
  });

  describe("Badge Criteria Validation", () => {
    test("should validate message count criteria", () => {
      const validRequest: CreateBadgeTypeRequest = {
        badgeId: "message-badge",
        name: "Message Badge",
        description: "Badge for sending messages",
        icon: "💬",
        design: {
          shape: "circle",
          background: "#4CAF50",
          colors: ["#4CAF50"]
        },
        criteria: {
          requirement: "Send 100 messages",
          tracked: "message_count",
          type: "message_count",
          threshold: 100
        },
        unlockType: "automatic",
        category: "achievement",
        displayOrder: 1
      };

      expect(() => validateCreateBadgeType(validRequest)).not.toThrow();
    });

    test("should validate server count criteria", () => {
      const validRequest: CreateBadgeTypeRequest = {
        badgeId: "server-badge",
        name: "Server Badge",
        description: "Badge for creating servers",
        icon: "🏗️",
        design: {
          shape: "hexagon",
          background: "#9C27B0",
          colors: ["#9C27B0"]
        },
        criteria: {
          requirement: "Create 5 servers",
          tracked: "server_count",
          type: "server_count",
          threshold: 5
        },
        unlockType: "automatic",
        category: "achievement",
        displayOrder: 1
      };

      expect(() => validateCreateBadgeType(validRequest)).not.toThrow();
    });

    test("should validate friend count criteria", () => {
      const validRequest: CreateBadgeTypeRequest = {
        badgeId: "friend-badge",
        name: "Friend Badge",
        description: "Badge for making friends",
        icon: "👥",
        design: {
          shape: "circle",
          background: "#E91E63",
          colors: ["#E91E63"]
        },
        criteria: {
          requirement: "Make 10 friends",
          tracked: "friend_count",
          type: "friend_count",
          threshold: 10
        },
        unlockType: "automatic",
        category: "community",
        displayOrder: 1
      };

      expect(() => validateCreateBadgeType(validRequest)).not.toThrow();
    });

    test("should validate custom criteria with conditions", () => {
      const validRequest: CreateBadgeTypeRequest = {
        badgeId: "custom-badge",
        name: "Custom Badge",
        description: "Badge with custom criteria",
        icon: "⚙️",
        design: {
          shape: "star",
          background: "#FF9800",
          colors: ["#FF9800"]
        },
        criteria: {
          requirement: "Meet complex requirements",
          tracked: "custom",
          type: "custom",
          threshold: 1,
          conditions: {
            combinedRequirements: {
              messages: 100,
              servers: 2,
              friends: 5
            }
          }
        },
        unlockType: "automatic",
        category: "special",
        displayOrder: 1
      };

      expect(() => validateCreateBadgeType(validRequest)).not.toThrow();
    });

    test("should reject criteria with negative threshold", () => {
      const invalidRequest: CreateBadgeTypeRequest = {
        badgeId: "invalid-badge",
        name: "Invalid Badge",
        description: "Badge with invalid criteria",
        icon: "❌",
        design: {
          shape: "circle",
          background: "#FF0000",
          colors: ["#FF0000"]
        },
        criteria: {
          requirement: "Invalid requirement",
          tracked: "message_count",
          type: "message_count",
          threshold: -10 // Invalid negative threshold
        },
        unlockType: "automatic",
        category: "achievement",
        displayOrder: 1
      };

      expect(() => validateCreateBadgeType(invalidRequest)).toThrow(BadgeValidationError);
    });
  });

  describe("Badge Design Validation", () => {
    test("should validate complete badge design", () => {
      const validRequest: CreateBadgeTypeRequest = {
        badgeId: "design-badge",
        name: "Design Badge",
        description: "Badge with complete design",
        icon: "🎨",
        design: {
          shape: "circle",
          background: "#2196F3",
          colors: ["#2196F3", "#1976D2", "#0D47A1"],
          gradient: "linear-gradient(45deg, #2196F3, #1976D2)",
          pattern: "dots",
          elements: ["border", "shadow"]
        },
        criteria: {
          requirement: "Test design",
          tracked: "test",
          type: "message_count",
          threshold: 1
        },
        unlockType: "manual",
        category: "achievement",
        displayOrder: 1
      };

      expect(() => validateCreateBadgeType(validRequest)).not.toThrow();
    });

    test("should validate minimal badge design", () => {
      const validRequest: CreateBadgeTypeRequest = {
        badgeId: "minimal-badge",
        name: "Minimal Badge",
        description: "Badge with minimal design",
        icon: "⚪",
        design: {
          shape: "circle",
          background: "#FFFFFF",
          colors: ["#FFFFFF"]
        },
        criteria: {
          requirement: "Test minimal",
          tracked: "test",
          type: "message_count",
          threshold: 1
        },
        unlockType: "manual",
        category: "achievement",
        displayOrder: 1
      };

      expect(() => validateCreateBadgeType(validRequest)).not.toThrow();
    });

    test("should reject design with empty colors array", () => {
      const invalidRequest: CreateBadgeTypeRequest = {
        badgeId: "invalid-design-badge",
        name: "Invalid Design Badge",
        description: "Badge with invalid design",
        icon: "❌",
        design: {
          shape: "circle",
          background: "#FF0000",
          colors: [] // Empty colors array
        },
        criteria: {
          requirement: "Test invalid",
          tracked: "test",
          type: "message_count",
          threshold: 1
        },
        unlockType: "manual",
        category: "achievement",
        displayOrder: 1
      };

      expect(() => validateCreateBadgeType(invalidRequest)).toThrow(BadgeValidationError);
    });
  });
});