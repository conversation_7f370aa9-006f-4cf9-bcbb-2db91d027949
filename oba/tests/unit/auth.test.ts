import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  afterAll,
} from "bun:test";
import { db } from "../setup";
import { UserSchema } from "../../db/schema";
import { eq } from "drizzle-orm";
import {
  createTestUser,
  cleanupTestData,
  createUserWithVerificationToken,
  createUserWithRefreshToken,
} from "../helpers";
import {
  generateEmailVerificationToken,
  verifyEmailToken,
  generateRefreshToken,
  verifyRefreshToken,
  invalidateRefreshToken,
} from "../../db/utils";

// Global cleanup after all tests
afterAll(async () => {
  await cleanupTestData();
});

describe("Authentication Utility Functions", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  // Email Verification Tests
  test("generateEmailVerificationToken should create a verification token", async () => {
    // Create a test user
    const testUser = await createTestUser(
      "verifyuser",
      "<EMAIL>",
      "Password123!",
      false,
    );

    // Generate a verification token
    const verificationInfo = await generateEmailVerificationToken(
      db,
      testUser.id,
    );

    // Check that the token was generated
    expect(verificationInfo).toBeDefined();
    expect(verificationInfo.userId).toBe(testUser.id);
    expect(verificationInfo.email).toBe(testUser.email);
    expect(verificationInfo.verificationToken).toBeDefined();

    // Verify the token was saved in the database
    const updatedUser = await db
      .select()
      .from(UserSchema)
      .where(eq(UserSchema.id, testUser.id))
      .limit(1);

    expect(updatedUser[0].emailVerificationToken).toBe(
      verificationInfo.verificationToken,
    );
    expect(updatedUser[0].emailVerificationExpiry).toBeDefined();
  });

  test("verifyEmailToken should verify a user email", async () => {
    // Create a user with verification token
    const { user, token } = await createUserWithVerificationToken();

    // Verify the email
    const verifiedUser = await verifyEmailToken(db, user.id, token);

    // Check that the email was verified
    expect(verifiedUser).toBeDefined();
    expect(verifiedUser.id).toBe(user.id);
    expect(verifiedUser.isEmailVerified).toBe(true);
    // The token and expiry might be null or undefined depending on the database driver
    expect(verifiedUser.emailVerificationToken == null).toBe(true);
    expect(verifiedUser.emailVerificationExpiry == null).toBe(true);

    // Verify the database was updated
    const updatedUser = await db
      .select()
      .from(UserSchema)
      .where(eq(UserSchema.id, user.id))
      .limit(1);

    expect(updatedUser[0].isEmailVerified).toBe(true);
    expect(updatedUser[0].emailVerificationToken == null).toBe(true);
    expect(updatedUser[0].emailVerificationExpiry == null).toBe(true);
  });

  test("verifyEmailToken should reject invalid token", async () => {
    // Create a user with verification token
    const { user } = await createUserWithVerificationToken();

    // Try to verify with invalid token
    try {
      await verifyEmailToken(db, user.id, "invalid-token");
      // If we get here, the test should fail
      expect(true).toBe(false);
    } catch (error) {
      expect((error as Error).message).toBe("Invalid verification token");
    }
  });

  test("verifyEmailToken should reject expired token", async () => {
    // Create a user with verification token
    const { user, token } = await createUserWithVerificationToken();

    // Set the token to be expired
    await db
      .update(UserSchema)
      .set({
        emailVerificationExpiry: new Date(Date.now() - 86400000), // 1 day in the past
      })
      .where(eq(UserSchema.id, user.id));

    // Try to verify with expired token
    try {
      await verifyEmailToken(db, user.id, token);
      // If we get here, the test should fail
      expect(true).toBe(false);
    } catch (error) {
      expect((error as Error).message).toBe("Verification token has expired");
    }
  });

  // Refresh Token Tests
  test("generateRefreshToken should create a refresh token", async () => {
    // Create a test user
    const testUser = await createTestUser("refreshuser", "<EMAIL>");

    // Generate a refresh token
    const refreshInfo = await generateRefreshToken(db, testUser.id);

    // Check that the token was generated
    expect(refreshInfo).toBeDefined();
    expect(refreshInfo.userId).toBe(testUser.id);
    expect(refreshInfo.refreshToken).toBeDefined();
    expect(refreshInfo.refreshTokenExpiry).toBeDefined();

    // Verify the token was saved in the database
    const updatedUser = await db
      .select()
      .from(UserSchema)
      .where(eq(UserSchema.id, testUser.id))
      .limit(1);

    expect(updatedUser[0].refreshToken).toBe(refreshInfo.refreshToken);
    expect(updatedUser[0].refreshTokenExpiry).toBeDefined();
  });

  test("verifyRefreshToken should verify a valid refresh token", async () => {
    // Create a user with refresh token
    const { user, refreshToken } = await createUserWithRefreshToken();

    // Verify the refresh token
    const verifiedUser = await verifyRefreshToken(db, user.id, refreshToken);

    // Check that the token was verified
    expect(verifiedUser).toBeDefined();
    expect(verifiedUser.id).toBe(user.id);
    expect(verifiedUser.refreshToken).toBe(refreshToken);
  });

  test("verifyRefreshToken should reject invalid token", async () => {
    // Create a user with refresh token
    const { user } = await createUserWithRefreshToken();

    // Try to verify with invalid token
    try {
      await verifyRefreshToken(db, user.id, "invalid-token");
      // If we get here, the test should fail
      expect(true).toBe(false);
    } catch (error) {
      expect((error as Error).message).toBe("Invalid refresh token");
    }
  });

  test("verifyRefreshToken should reject expired token", async () => {
    // Create a user with refresh token
    const { user, refreshToken } = await createUserWithRefreshToken();

    // Set the token to be expired
    await db
      .update(UserSchema)
      .set({
        refreshTokenExpiry: new Date(Date.now() - 86400000), // 1 day in the past
      })
      .where(eq(UserSchema.id, user.id));

    // Try to verify with expired token
    try {
      await verifyRefreshToken(db, user.id, refreshToken);
      // If we get here, the test should fail
      expect(true).toBe(false);
    } catch (error) {
      expect((error as Error).message).toBe("Refresh token has expired");
    }
  });

  test("invalidateRefreshToken should clear refresh token", async () => {
    // Create a user with refresh token
    const { user } = await createUserWithRefreshToken();

    // Invalidate the refresh token
    const result = await invalidateRefreshToken(db, user.id);

    // Check that the token was invalidated
    expect(result).toBe(true);

    // Verify the database was updated
    const updatedUser = await db
      .select()
      .from(UserSchema)
      .where(eq(UserSchema.id, user.id))
      .limit(1);

    expect(updatedUser[0].refreshToken == null).toBe(true);
    expect(updatedUser[0].refreshTokenExpiry == null).toBe(true);
  });
});
