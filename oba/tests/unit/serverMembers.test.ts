import { describe, expect, test, beforeEach, afterEach } from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  cleanupTestData,
  createTestServerWithRoles,
  assignRoleToUser,
} from "../helpers";
import {
  kickMember,
  banMember,
  unbanMember,
  changeUserRoles,
  getBannedUsers,
  getServerMembers,
  isUserBanned,
} from "../../utils/serverMembers";
import {
  ServerMembershipSchema,
  ServerBanSchema,
  UserRoles,
} from "../../db/schema";
import { eq, and } from "drizzle-orm";

describe("Server Member Management Utility Functions", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  // Kick Member Tests
  test("kickMember should remove a user from a server", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Add the member to the server
    await db.insert(ServerMembershipSchema).values({
      userId: member.id,
      serverId: server.id,
    });

    // Kick the member
    const success = await kickMember(
      db,
      server.id,
      member.id,
      owner.id, // Server owner has permission to kick
    );

    // Check that the operation was successful
    expect(success).toBe(true);

    // Verify that the member was removed from the server
    const membership = await db
      .select()
      .from(ServerMembershipSchema)
      .where(
        and(
          eq(ServerMembershipSchema.userId, member.id),
          eq(ServerMembershipSchema.serverId, server.id),
        ),
      );

    expect(membership.length).toBe(0);

    // Verify that the member's roles were removed
    const userRoles = await db
      .select()
      .from(UserRoles)
      .where(
        and(eq(UserRoles.userId, member.id), eq(UserRoles.serverId, server.id)),
      );

    expect(userRoles.length).toBe(0);
  });

  test("kickMember should not allow kicking the server owner", async () => {
    // Create a user
    const owner = await createTestUser("owner", "<EMAIL>");
    const moderator = await createTestUser("mod", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign moderator role to the mod user
    await assignRoleToUser(moderator.id, roles.moderator.id, server.id);

    // Add the moderator to the server
    await db.insert(ServerMembershipSchema).values({
      userId: moderator.id,
      serverId: server.id,
    });

    // Try to kick the owner (should fail)
    const success = await kickMember(
      db,
      server.id,
      owner.id,
      moderator.id, // Moderator tries to kick the owner
    );

    // Check that the operation failed
    expect(success).toBe(false);

    // Verify that the owner is still in the server
    const membership = await db
      .select()
      .from(ServerMembershipSchema)
      .where(
        and(
          eq(ServerMembershipSchema.userId, owner.id),
          eq(ServerMembershipSchema.serverId, server.id),
        ),
      );

    // The owner doesn't have an explicit membership record, so this should be empty
    expect(membership.length).toBe(0);
  });

  test("kickMember should require proper permissions", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member1 = await createTestUser("member1", "<EMAIL>");
    const member2 = await createTestUser("member2", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member roles
    await assignRoleToUser(member1.id, roles.member.id, server.id);
    await assignRoleToUser(member2.id, roles.member.id, server.id);

    // Add the members to the server
    await db.insert(ServerMembershipSchema).values([
      {
        userId: member1.id,
        serverId: server.id,
      },
      {
        userId: member2.id,
        serverId: server.id,
      },
    ]);

    // Try to kick member2 as member1 (should fail)
    const success = await kickMember(
      db,
      server.id,
      member2.id,
      member1.id, // Regular member tries to kick another member
    );

    // Check that the operation failed
    expect(success).toBe(false);

    // Verify that member2 is still in the server
    const membership = await db
      .select()
      .from(ServerMembershipSchema)
      .where(
        and(
          eq(ServerMembershipSchema.userId, member2.id),
          eq(ServerMembershipSchema.serverId, server.id),
        ),
      );

    expect(membership.length).toBe(1);
  });

  // Ban Member Tests
  test("banMember should ban a user from a server", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Add the member to the server
    await db.insert(ServerMembershipSchema).values({
      userId: member.id,
      serverId: server.id,
    });

    // Ban the member
    const success = await banMember(
      db,
      server.id,
      member.id,
      owner.id, // Server owner has permission to ban
      "Violated server rules",
    );

    // Check that the operation was successful
    expect(success).toBe(true);

    // Verify that the member was removed from the server
    const membership = await db
      .select()
      .from(ServerMembershipSchema)
      .where(
        and(
          eq(ServerMembershipSchema.userId, member.id),
          eq(ServerMembershipSchema.serverId, server.id),
        ),
      );

    expect(membership.length).toBe(0);

    // Verify that the member's roles were removed
    const userRoles = await db
      .select()
      .from(UserRoles)
      .where(
        and(eq(UserRoles.userId, member.id), eq(UserRoles.serverId, server.id)),
      );

    expect(userRoles.length).toBe(0);

    // Verify that a ban record was created
    const ban = await db
      .select()
      .from(ServerBanSchema)
      .where(
        and(
          eq(ServerBanSchema.userId, member.id),
          eq(ServerBanSchema.serverId, server.id),
        ),
      );

    expect(ban.length).toBe(1);
    expect(ban[0].reason).toBe("Violated server rules");
    expect(ban[0].bannedById).toBe(owner.id);
  });

  test("banMember should not allow banning the server owner", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const moderator = await createTestUser("mod", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign moderator role to the mod user
    await assignRoleToUser(moderator.id, roles.moderator.id, server.id);

    // Add the moderator to the server
    await db.insert(ServerMembershipSchema).values({
      userId: moderator.id,
      serverId: server.id,
    });

    // Try to ban the owner (should fail)
    const success = await banMember(
      db,
      server.id,
      owner.id,
      moderator.id, // Moderator tries to ban the owner
    );

    // Check that the operation failed
    expect(success).toBe(false);

    // Verify that no ban record was created
    const ban = await db
      .select()
      .from(ServerBanSchema)
      .where(
        and(
          eq(ServerBanSchema.userId, owner.id),
          eq(ServerBanSchema.serverId, server.id),
        ),
      );

    expect(ban.length).toBe(0);
  });

  // Unban Member Tests
  test("unbanMember should unban a user from a server", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server } = await createTestServerWithRoles(owner.id);

    // Ban the member
    await db.insert(ServerBanSchema).values({
      serverId: server.id,
      userId: member.id,
      bannedById: owner.id,
      reason: "Test ban",
    });

    // Unban the member
    const success = await unbanMember(
      db,
      server.id,
      member.id,
      owner.id, // Server owner has permission to unban
    );

    // Check that the operation was successful
    expect(success).toBe(true);

    // Verify that the ban record was removed
    const ban = await db
      .select()
      .from(ServerBanSchema)
      .where(
        and(
          eq(ServerBanSchema.userId, member.id),
          eq(ServerBanSchema.serverId, server.id),
        ),
      );

    expect(ban.length).toBe(0);
  });

  // Change User Roles Tests
  test("changeUserRoles should update a user's roles", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Add the member to the server
    await db.insert(ServerMembershipSchema).values({
      userId: member.id,
      serverId: server.id,
    });

    // Change the member's roles to moderator
    const success = await changeUserRoles(
      db,
      server.id,
      member.id,
      [roles.moderator.id],
      owner.id, // Server owner has permission to change roles
    );

    // Check that the operation was successful
    expect(success).toBe(true);

    // Verify that the member's roles were updated
    const userRoles = await db
      .select()
      .from(UserRoles)
      .where(
        and(eq(UserRoles.userId, member.id), eq(UserRoles.serverId, server.id)),
      );

    expect(userRoles.length).toBe(1);
    expect(userRoles[0].roleId).toBe(roles.moderator.id);
  });

  test("changeUserRoles should not allow changing the server owner's roles", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const moderator = await createTestUser("mod", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign moderator role to the mod user
    await assignRoleToUser(moderator.id, roles.moderator.id, server.id);

    // Add the moderator to the server
    await db.insert(ServerMembershipSchema).values({
      userId: moderator.id,
      serverId: server.id,
    });

    // Try to change the owner's roles (should fail)
    const success = await changeUserRoles(
      db,
      server.id,
      owner.id,
      [roles.member.id],
      moderator.id, // Moderator tries to change the owner's roles
    );

    // Check that the operation failed
    expect(success).toBe(false);
  });

  // Get Banned Users Tests
  test("getBannedUsers should return banned users for a server", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member1 = await createTestUser("member1", "<EMAIL>");
    const member2 = await createTestUser("member2", "<EMAIL>");

    // Create server with roles
    const { server } = await createTestServerWithRoles(owner.id);

    // Ban the members
    await db.insert(ServerBanSchema).values([
      {
        serverId: server.id,
        userId: member1.id,
        bannedById: owner.id,
        reason: "Test ban 1",
      },
      {
        serverId: server.id,
        userId: member2.id,
        bannedById: owner.id,
        reason: "Test ban 2",
      },
    ]);

    // Get banned users
    const bannedUsers = await getBannedUsers(
      db,
      server.id,
      owner.id, // Server owner has permission to view banned users
    );

    // Check that the banned users were returned
    expect(bannedUsers).not.toBeNull();
    expect(bannedUsers?.length).toBe(2);

    // Check the banned users' details
    const userIds = bannedUsers?.map((user) => user.userId);
    expect(userIds).toContain(member1.id);
    expect(userIds).toContain(member2.id);

    // Check the ban reasons
    const reasons = bannedUsers?.map((user) => user.reason);
    expect(reasons).toContain("Test ban 1");
    expect(reasons).toContain("Test ban 2");
  });

  // Is User Banned Tests
  test("isUserBanned should return true for banned users", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server } = await createTestServerWithRoles(owner.id);

    // Ban the member
    await db.insert(ServerBanSchema).values({
      serverId: server.id,
      userId: member.id,
      bannedById: owner.id,
      reason: "Test ban",
    });

    // Check if the user is banned
    const isBanned = await isUserBanned(db, server.id, member.id);

    // Check that the user is banned
    expect(isBanned).toBe(true);
  });

  test("isUserBanned should return false for non-banned users", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server } = await createTestServerWithRoles(owner.id);

    // Check if the user is banned
    const isBanned = await isUserBanned(db, server.id, member.id);

    // Check that the user is not banned
    expect(isBanned).toBe(false);
  });

  // Get Server Members Tests
  test("getServerMembers should return all members of a server", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member1 = await createTestUser("member1", "<EMAIL>");
    const member2 = await createTestUser("member2", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign roles to the members
    await assignRoleToUser(member1.id, roles.member.id, server.id);
    await assignRoleToUser(member2.id, roles.moderator.id, server.id);

    // Add the members to the server
    await db.insert(ServerMembershipSchema).values([
      {
        userId: member1.id,
        serverId: server.id,
      },
      {
        userId: member2.id,
        serverId: server.id,
      },
    ]);

    // Get server members
    const members = await getServerMembers(db, server.id);

    // Check that the members were returned
    expect(members.length).toBe(2);

    // Check the members' details
    const userIds = members.map((member) => member.userId);
    expect(userIds).toContain(member1.id);
    expect(userIds).toContain(member2.id);

    // Check the owner status
    const ownerMember = members.find((member) => member.isOwner);
    expect(ownerMember).toBeUndefined(); // Owner doesn't have a membership record
  });
});
