import { describe, expect, test, beforeEach, afterEach } from "bun:test";
import { db } from "../setup";
import { createTestUser, cleanupTestData } from "../helpers";
import {
  validateUser,
  updateUserProfile,
  verifyUserPassword,
} from "../../db/utils";

describe("User Utility Functions", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  test("validateUser should return user with valid credentials", async () => {
    // Create a test user
    const testUser = await createTestUser(
      "testuser",
      "<EMAIL>",
      "Password123!",
    );

    // Validate the user
    const user = await validateUser(db, {
      username: "testuser",
      password: "Password123!",
    });

    // Check that the user was validated
    expect(user).toBeDefined();
    expect(user.id).toBe(testUser.id);
    expect(user.username).toBe("testuser");
    expect(user.email).toBe("<EMAIL>");
    expect(user.password).toBe(""); // Password should be empty in the returned user
  });

  test("validateUser should throw error with invalid credentials", async () => {
    // Create a test user
    await createTestUser("testuser", "<EMAIL>", "Password123!");

    // Attempt to validate with wrong password
    try {
      await validateUser(db, {
        username: "testuser",
        password: "WrongPassword123!",
      });
      // If we get here, the test should fail
      expect(true).toBe(false);
    } catch (error) {
      expect((error as Error).message).toContain(
        "Invalid username or password",
      );
    }
  });

  test("verifyUserPassword should return true for correct password", async () => {
    // Create a test user
    const testUser = await createTestUser(
      "testuser",
      "<EMAIL>",
      "Password123!",
    );

    // Verify the password
    const isValid = await verifyUserPassword(db, testUser.id, "Password123!");

    // Check that the password was verified
    expect(isValid).toBe(true);
  });

  test("verifyUserPassword should return false for incorrect password", async () => {
    // Create a test user
    const testUser = await createTestUser(
      "testuser",
      "<EMAIL>",
      "Password123!",
    );

    // Verify with wrong password
    const isValid = await verifyUserPassword(
      db,
      testUser.id,
      "WrongPassword123!",
    );

    // Check that the password was not verified
    expect(isValid).toBe(false);
  });

  test("updateUserProfile should update user profile", async () => {
    // Create a test user
    const testUser = await createTestUser(
      "testuser",
      "<EMAIL>",
      "Password123!",
    );

    // Update the user profile
    const updatedUser = await updateUserProfile(db, testUser.id, {
      username: "newusername",
      email: "<EMAIL>",
      avatar: "https://example.com/new-avatar.png",
    });

    // Check that the user was updated
    expect(updatedUser).toBeDefined();
    expect(updatedUser.id).toBe(testUser.id);
    expect(updatedUser.username).toBe("newusername");
    expect(updatedUser.email).toBe("<EMAIL>");
    // The avatar URL might be HTML-encoded, so we'll check if it contains the expected URL
    expect(
      updatedUser.avatar && updatedUser.avatar.includes("example.com"),
    ).toBe(true);
  });

  test("updateUserProfile should throw error for duplicate username", async () => {
    // Create two test users
    const testUser1 = await createTestUser(
      "testuser1",
      "<EMAIL>",
      "Password123!",
    );
    await createTestUser("testuser2", "<EMAIL>", "Password123!");

    // Attempt to update user1 with user2's username
    try {
      await updateUserProfile(db, testUser1.id, {
        username: "testuser2",
      });
      // If we get here, the test should fail
      expect(true).toBe(false);
    } catch (error) {
      expect((error as Error).message).toBe("Username already taken");
    }
  });
});
