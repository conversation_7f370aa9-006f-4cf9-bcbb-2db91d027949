import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  beforeAll,
  afterAll,
} from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  createTestServer,
  createTestChannel,
  createTestMessage,
  cleanupTestData,
} from "../helpers";
import {
  createNewMessage,
  editExistingMessage,
  getMessageById,
  deleteMessage,
  retrieveLastNMessages,
} from "../../db/utils";

describe("Message Utility Functions", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  test("createNewMessage should create a new message", async () => {
    // Create test data
    const user = await createTestUser();
    const server = await createTestServer(user.id);
    const channel = await createTestChannel(server.id);

    // Create a new message
    const message = await createNewMessage(
      db,
      user.id,
      channel.id,
      "Test message content",
    );

    // Check that the message was created
    expect(message).toBeDefined();
    expect(message.userId).toBe(user.id);
    expect(message.channelId).toBe(channel.id);
    expect(message.content).toBe("Test message content");
  });

  test("getMessageById should retrieve a message", async () => {
    // Create test data
    const user = await createTestUser();
    const server = await createTestServer(user.id);
    const channel = await createTestChannel(server.id);
    const message = await createTestMessage(user.id, channel.id);

    // Retrieve the message
    const retrievedMessage = await getMessageById(db, message.id);

    // Check that the message was retrieved
    expect(retrievedMessage).toBeDefined();
    expect(retrievedMessage?.id).toBe(message.id);
    expect(retrievedMessage?.userId).toBe(user.id);
    expect(retrievedMessage?.channelId).toBe(channel.id);
    expect(retrievedMessage?.content).toBe("Test message content");
  });

  test("editExistingMessage should update a message", async () => {
    // Create test data
    const user = await createTestUser();
    const server = await createTestServer(user.id);
    const channel = await createTestChannel(server.id);
    const message = await createTestMessage(user.id, channel.id);

    // Edit the message
    const updatedMessage = await editExistingMessage(
      db,
      message.id,
      "Updated content",
    );

    // Check that the message was updated
    expect(updatedMessage).toBeDefined();
    expect(updatedMessage.id).toBe(message.id);
    expect(updatedMessage.content).toBe("Updated content");
    expect(updatedMessage.editedAt).toBeDefined();
  });

  test("deleteMessage should delete a message", async () => {
    // Create test data
    const user = await createTestUser();
    const server = await createTestServer(user.id);
    const channel = await createTestChannel(server.id);
    const message = await createTestMessage(user.id, channel.id);

    // Delete the message
    const deletedMessage = await deleteMessage(db, message.id);

    // Check that the message was deleted
    expect(deletedMessage).toBeDefined();
    expect(deletedMessage.id).toBe(message.id);

    // Verify the message is no longer in the database
    const retrievedMessage = await getMessageById(db, message.id);
    expect(retrievedMessage).toBeNull();
  });

  test("retrieveLastNMessages should retrieve messages for a channel", async () => {
    // Create test data
    const user = await createTestUser();
    const server = await createTestServer(user.id);
    const channel = await createTestChannel(server.id);

    // Create multiple messages
    await createTestMessage(user.id, channel.id, "Message 1");
    await createTestMessage(user.id, channel.id, "Message 2");
    await createTestMessage(user.id, channel.id, "Message 3");

    // Retrieve the messages
    const messages = await retrieveLastNMessages(db, channel.id, 10);

    // Check that the messages were retrieved
    expect(messages).toBeDefined();
    expect(messages.length).toBe(3);

    // Messages should be in reverse chronological order (newest first)
    expect(messages[0].message.content).toBe("Message 3");
    expect(messages[1].message.content).toBe("Message 2");
    expect(messages[2].message.content).toBe("Message 1");
  });
});
