import { describe, expect, test } from "bun:test";
import {
  parseVoiceMessage,
  createVoiceMessage,
  validateVoiceMetadata,
  createVoiceMetadata,
} from "../../utils/binaryProtocol";

describe("Binary Protocol Utilities", () => {
  test("createVoiceMetadata should create valid metadata", () => {
    const userId = "user123";
    const sequence = 42;

    const metadata = createVoiceMetadata(userId, sequence);

    expect(metadata.type).toBe("voice_data");
    expect(metadata.userId).toBe(userId);
    expect(metadata.sequence).toBe(sequence);
    expect(metadata.format).toBe("opus");
    expect(metadata.sampleRate).toBe(48000);
    expect(metadata.channels).toBe(1);
    expect(metadata.frameSize).toBe(960);
    expect(typeof metadata.timestamp).toBe("number");
  });

  test("validateVoiceMetadata should validate correct metadata", () => {
    const metadata = {
      type: "voice_data",
      userId: "user123",
      timestamp: Date.now(),
      sequence: 42,
      format: "opus",
      sampleRate: 48000,
      channels: 1,
      frameSize: 960,
    };

    expect(validateVoiceMetadata(metadata)).toBe(true);
  });

  test("validateVoiceMetadata should reject invalid metadata", () => {
    // Missing type
    expect(
      validateVoiceMetadata({
        userId: "user123",
        timestamp: Date.now(),
        sequence: 42,
      }),
    ).toBe(false);

    // Wrong type
    expect(
      validateVoiceMetadata({
        type: "wrong_type",
        userId: "user123",
        timestamp: Date.now(),
        sequence: 42,
      }),
    ).toBe(false);

    // Missing userId
    expect(
      validateVoiceMetadata({
        type: "voice_data",
        timestamp: Date.now(),
        sequence: 42,
      }),
    ).toBe(false);

    // Missing timestamp
    expect(
      validateVoiceMetadata({
        type: "voice_data",
        userId: "user123",
        sequence: 42,
      }),
    ).toBe(false);

    // Missing sequence
    expect(
      validateVoiceMetadata({
        type: "voice_data",
        userId: "user123",
        timestamp: Date.now(),
      }),
    ).toBe(false);

    // Not an object
    expect(validateVoiceMetadata("not an object")).toBe(false);
    expect(validateVoiceMetadata(null)).toBe(false);
    expect(validateVoiceMetadata(undefined)).toBe(false);
  });

  test("createVoiceMessage and parseVoiceMessage should be inverses", () => {
    const userId = "user123";
    const sequence = 42;
    const audioData = new Uint8Array([1, 2, 3, 4, 5]).buffer;

    // Create metadata
    const originalMetadata = createVoiceMetadata(userId, sequence);

    // Create binary message
    const binaryMessage = createVoiceMessage(originalMetadata, audioData);

    // Parse binary message
    const { metadata, audioData: parsedAudioData } =
      parseVoiceMessage(binaryMessage);

    // Check metadata
    expect(metadata.type).toBe(originalMetadata.type);
    expect(metadata.userId).toBe(originalMetadata.userId);
    expect(metadata.timestamp).toBe(originalMetadata.timestamp);
    expect(metadata.sequence).toBe(originalMetadata.sequence);
    expect(metadata.format).toBe(originalMetadata.format);
    expect(metadata.sampleRate).toBe(originalMetadata.sampleRate);
    expect(metadata.channels).toBe(originalMetadata.channels);
    expect(metadata.frameSize).toBe(originalMetadata.frameSize);

    // Check audio data
    const originalAudioArray = new Uint8Array(audioData);
    const parsedAudioArray = new Uint8Array(parsedAudioData);
    expect(parsedAudioArray.length).toBe(originalAudioArray.length);
    for (let i = 0; i < originalAudioArray.length; i++) {
      expect(parsedAudioArray[i]).toBe(originalAudioArray[i]);
    }
  });

  test("parseVoiceMessage should handle empty audio data", () => {
    const userId = "user123";
    const sequence = 42;
    const audioData = new ArrayBuffer(0);

    // Create metadata
    const originalMetadata = createVoiceMetadata(userId, sequence);

    // Create binary message
    const binaryMessage = createVoiceMessage(originalMetadata, audioData);

    // Parse binary message
    const { metadata, audioData: parsedAudioData } =
      parseVoiceMessage(binaryMessage);

    // Check metadata
    expect(metadata.type).toBe(originalMetadata.type);
    expect(metadata.userId).toBe(originalMetadata.userId);

    // Check audio data
    expect(parsedAudioData.byteLength).toBe(0);
  });
});
