import { describe, expect, test, beforeEach, afterEach } from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  cleanupTestData,
  createTestServerWithRoles,
  createTestChannel,
  createTestServer,
  assignRoleToUser,
  addAllowedRoleToChannel,
} from "../helpers";
import {
  hasServerPermission,
  hasChannelPermission,
  createDefaultRoles,
  getServerRoles,
  getUserRoles,
  createRole,
  updateRole,
  deleteRole,
} from "../../utils/permissions";
import {
  MANAGE_CHANNELS,
  SEND_MESSAGES,
  VIEW_CHANNEL,
  MANAGE_ROLES,
} from "../../constants/permissions";
import {
  ServerRoleSchema,
  UserRoles,
  ChannelPrivacySchema,
  ChannelAllowedRolesSchema,
} from "../../db/schema";
import { eq, and } from "drizzle-orm";

// Global cleanup after all tests
// afterAll(async () => {
//   await cleanupTestData();
// });

describe("Permission System Utility Functions", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  // Server Permission Tests
  test("hasServerPermission should return true for server owner", async () => {
    // Create a test user and server
    const user = await createTestUser("permuser", "<EMAIL>");
    const { server } = await createTestServerWithRoles(user.id);

    // Check if the user has a permission they don't explicitly have
    const hasPermission = await hasServerPermission(
      db,
      user.id,
      server.id,
      MANAGE_CHANNELS,
    );

    // Server owner should have all permissions
    expect(hasPermission).toBe(true);
  });

  test("hasServerPermission should return true for user with ADMINISTRATOR permission", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const admin = await createTestUser("admin", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign admin role to the admin user
    await assignRoleToUser(admin.id, roles.admin.id, server.id);

    // Check if the admin user has a permission they don't explicitly have
    const hasPermission = await hasServerPermission(
      db,
      admin.id,
      server.id,
      MANAGE_ROLES, // Not explicitly granted, but admin has ADMINISTRATOR
    );

    // User with ADMINISTRATOR should have all permissions
    expect(hasPermission).toBe(true);
  });

  test("hasServerPermission should return true for user with specific permission", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const moderator = await createTestUser("mod", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign moderator role to the mod user
    await assignRoleToUser(moderator.id, roles.moderator.id, server.id);

    // Check if the mod user has a permission they explicitly have
    const hasPermission = await hasServerPermission(
      db,
      moderator.id,
      server.id,
      MANAGE_CHANNELS, // Explicitly granted to moderator
    );

    // User should have the permission
    expect(hasPermission).toBe(true);
  });

  test("hasServerPermission should return false for user without specific permission", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Check if the member user has a permission they don't have
    const hasPermission = await hasServerPermission(
      db,
      member.id,
      server.id,
      MANAGE_CHANNELS, // Not granted to member
    );

    // User should not have the permission
    expect(hasPermission).toBe(false);
  });

  test("hasServerPermission should return false for user with no roles", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const noRole = await createTestUser("norole", "<EMAIL>");

    // Create server with roles
    const { server } = await createTestServerWithRoles(owner.id);

    // Check if the user with no roles has a permission
    const hasPermission = await hasServerPermission(
      db,
      noRole.id,
      server.id,
      SEND_MESSAGES,
    );

    // User with no roles should not have any permissions
    expect(hasPermission).toBe(false);
  });

  // Channel Permission Tests
  test("hasChannelPermission should return true for server owner", async () => {
    // Create a test user and server
    const user = await createTestUser("permuser", "<EMAIL>");
    const { server } = await createTestServerWithRoles(user.id);

    // Create a channel
    const channel = await createTestChannel(server.id);

    // Check if the server owner has permission in the channel
    const hasPermission = await hasChannelPermission(
      db,
      user.id,
      channel.id,
      server.id,
      MANAGE_CHANNELS,
    );

    // Server owner should have all permissions in all channels
    expect(hasPermission).toBe(true);
  });

  test("hasChannelPermission should return true for user with permission in public channel", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const moderator = await createTestUser("mod", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign moderator role to the mod user
    await assignRoleToUser(moderator.id, roles.moderator.id, server.id);

    // Create a public channel
    const channel = await createTestChannel(server.id);

    // Create privacy settings for the channel (public)
    await db.insert(ChannelPrivacySchema).values({
      channelId: channel.id,
      isPublic: true,
    });

    // Check if the mod user has permission in the channel
    const hasPermission = await hasChannelPermission(
      db,
      moderator.id,
      channel.id,
      server.id,
      MANAGE_CHANNELS,
    );

    // Moderator should have MANAGE_CHANNELS permission in public channels
    expect(hasPermission).toBe(true);
  });

  test("hasChannelPermission should return true for user with permission in private channel they have access to", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const moderator = await createTestUser("mod", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign moderator role to the mod user
    await assignRoleToUser(moderator.id, roles.moderator.id, server.id);

    // Create a private channel
    const channel = await createTestChannel(server.id);

    // Create privacy settings for the channel (private)
    await db.insert(ChannelPrivacySchema).values({
      channelId: channel.id,
      isPublic: false,
    });

    // Add the moderator role to the channel's allowed roles
    await addAllowedRoleToChannel(channel.id, roles.moderator.id);

    // Check if the mod user has permission in the channel
    const hasPermission = await hasChannelPermission(
      db,
      moderator.id,
      channel.id,
      server.id,
      MANAGE_CHANNELS,
    );

    // Moderator should have MANAGE_CHANNELS permission in private channels they have access to
    expect(hasPermission).toBe(true);
  });

  test("hasChannelPermission should return false for user without access to private channel", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Create a private channel
    const channel = await createTestChannel(server.id);

    // Create privacy settings for the channel (private)
    await db.insert(ChannelPrivacySchema).values({
      channelId: channel.id,
      isPublic: false,
    });

    // Add only the admin role to the channel's allowed roles
    await addAllowedRoleToChannel(channel.id, roles.admin.id);

    // Check if the member user has permission in the channel
    const hasPermission = await hasChannelPermission(
      db,
      member.id,
      channel.id,
      server.id,
      VIEW_CHANNEL,
    );

    // Member should not have access to the private channel
    expect(hasPermission).toBe(false);
  });

  test("hasChannelPermission should return false for user with access but without required permission", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Create a public channel
    const channel = await createTestChannel(server.id);

    // Create privacy settings for the channel (public)
    await db.insert(ChannelPrivacySchema).values({
      channelId: channel.id,
      isPublic: true,
    });

    // Check if the member user has a permission they don't have
    const hasPermission = await hasChannelPermission(
      db,
      member.id,
      channel.id,
      server.id,
      MANAGE_CHANNELS, // Not granted to member
    );

    // Member should not have MANAGE_CHANNELS permission
    expect(hasPermission).toBe(false);
  });

  // Default Roles Tests
  test("createDefaultRoles should create admin, moderator, and member roles", async () => {
    // Create a test user and server
    const user = await createTestUser("roleuser", "<EMAIL>");
    const server = await createTestServer(user.id);

    // Create default roles
    await createDefaultRoles(db, server.id, user.id);

    // Get the roles
    const roles = await db
      .select()
      .from(ServerRoleSchema)
      .where(eq(ServerRoleSchema.serverId, server.id));

    // Check that three roles were created
    expect(roles.length).toBe(3);

    // Check role names
    const roleNames = roles.map((role) => role.name);
    expect(roleNames).toContain("Admin");
    expect(roleNames).toContain("Moderator");
    expect(roleNames).toContain("Member");

    // Check that the owner was assigned the admin role
    const userRoles = await db
      .select()
      .from(UserRoles)
      .where(
        and(eq(UserRoles.userId, user.id), eq(UserRoles.serverId, server.id)),
      );

    expect(userRoles.length).toBe(1);

    // Find the admin role
    const adminRole = roles.find((role) => role.name === "Admin");
    expect(adminRole).toBeDefined();

    // Check that the owner was assigned the admin role
    if (adminRole) {
      expect(userRoles[0].roleId).toBe(adminRole.id);
    }
  });

  // Role Management Tests
  test("getServerRoles should return all roles for a server", async () => {
    // Create a test user and server with roles
    const user = await createTestUser("roleuser", "<EMAIL>");
    const { server } = await createTestServerWithRoles(user.id);

    // Get the roles
    const roles = await getServerRoles(db, server.id);

    // Check that three roles were returned
    expect(roles.length).toBe(3);

    // Check role names
    const roleNames = roles.map((role) => role.name);
    expect(roleNames).toContain("Admin");
    expect(roleNames).toContain("Moderator");
    expect(roleNames).toContain("Member");
  });

  test("getUserRoles should return roles for a user", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Get the owner's roles
    const ownerRoles = await getUserRoles(db, owner.id, server.id);

    // Check that the owner has the admin role
    expect(ownerRoles.length).toBe(1);
    expect(ownerRoles[0].name).toBe("Admin");

    // Get the member's roles
    const memberRoles = await getUserRoles(db, member.id, server.id);

    // Check that the member has the member role
    expect(memberRoles.length).toBe(1);
    expect(memberRoles[0].name).toBe("Member");
  });

  test("createRole should create a new role", async () => {
    // Create a test user and server
    const user = await createTestUser("roleuser", "<EMAIL>");
    const { server } = await createTestServerWithRoles(user.id);

    // Create a new role
    const role = await createRole(
      db,
      server.id,
      "Custom Role",
      SEND_MESSAGES | VIEW_CHANNEL,
    );

    // Check that the role was created
    expect(role).toBeDefined();
    expect(role?.name).toBe("Custom Role");
    expect(role?.serverId).toBe(server.id);
    expect(role?.permissions).toBe(SEND_MESSAGES | VIEW_CHANNEL);

    // Verify in the database
    const dbRole = await db
      .select()
      .from(ServerRoleSchema)
      .where(
        and(
          eq(ServerRoleSchema.id, role?.id),
          eq(ServerRoleSchema.serverId, server.id),
        ),
      )
      .limit(1);

    expect(dbRole.length).toBe(1);
    expect(dbRole[0].name).toBe("Custom Role");
    expect(dbRole[0].permissions).toBe(SEND_MESSAGES | VIEW_CHANNEL);
  });

  test("updateRole should update an existing role", async () => {
    // Create a test user and server with roles
    const user = await createTestUser("roleuser", "<EMAIL>");
    const { roles } = await createTestServerWithRoles(user.id);

    // Update the member role
    const updatedRole = await updateRole(db, roles.member.id, {
      name: "Updated Member",
      permissions: SEND_MESSAGES | VIEW_CHANNEL | MANAGE_CHANNELS,
    });

    // Check that the role was updated
    expect(updatedRole).toBeDefined();
    expect(updatedRole?.name).toBe("Updated Member");
    expect(updatedRole?.permissions).toBe(
      SEND_MESSAGES | VIEW_CHANNEL | MANAGE_CHANNELS,
    );

    // Verify in the database
    const dbRole = await db
      .select()
      .from(ServerRoleSchema)
      .where(eq(ServerRoleSchema.id, roles.member.id))
      .limit(1);

    expect(dbRole.length).toBe(1);
    expect(dbRole[0].name).toBe("Updated Member");
    expect(dbRole[0].permissions).toBe(
      SEND_MESSAGES | VIEW_CHANNEL | MANAGE_CHANNELS,
    );
  });

  test("deleteRole should delete a role and its assignments", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Create a channel and add the member role to its allowed roles
    const channel = await createTestChannel(server.id);
    await addAllowedRoleToChannel(channel.id, roles.member.id);

    // Delete the member role
    const success = await deleteRole(db, roles.member.id);

    // Check that the operation was successful
    expect(success).toBe(true);

    // Verify that the role was deleted
    const dbRole = await db
      .select()
      .from(ServerRoleSchema)
      .where(eq(ServerRoleSchema.id, roles.member.id));

    expect(dbRole.length).toBe(0);

    // Verify that user assignments were deleted
    const userRoles = await db
      .select()
      .from(UserRoles)
      .where(eq(UserRoles.roleId, roles.member.id));

    expect(userRoles.length).toBe(0);

    // Verify that channel allowed roles were deleted
    const allowedRoles = await db
      .select()
      .from(ChannelAllowedRolesSchema)
      .where(eq(ChannelAllowedRolesSchema.roleId, roles.member.id));

    expect(allowedRoles.length).toBe(0);
  });
});
