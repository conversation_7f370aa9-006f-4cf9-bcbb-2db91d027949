import { describe, test, expect, beforeEach, afterEach } from "bun:test";
import { WebSocketValidator } from "../../utils/websocket-validator";
import {
  MESSAGE_TYPE_DEFINITIONS,
  MessageSendSchema,
  AuthLoginSchema,
  HeartbeatSchema,
  VoiceJoinSchema,
  DirectMessageSendSchema,
  FriendRequestSendSchema,
} from "../../utils/websocket-schemas";
import { EventTypes } from "../../constants/eventTypes";
import {
  type IWebSocketMessage,
  type IMessageTypeDefinition,
  WebSocketErrorCode,
} from "../../types/websocket-standardization.types";
import { z } from "zod";

describe("Message Type Registry - Comprehensive Tests", () => {
  beforeEach(() => {
    WebSocketValidator.clearSchemas();
  });

  afterEach(() => {
    WebSocketValidator.clearSchemas();
  });

  describe("Schema Registration and Management", () => {
    test("should register multiple schemas without conflicts", () => {
      const schemas = [
        {
          type: "TYPE_A",
          definition: {
            type: "TYPE_A",
            category: "test",
            direction: "client_to_server" as const,
            requiresAuth: true,
            dataSchema: MessageSendSchema,
          },
        },
        {
          type: "TYPE_B",
          definition: {
            type: "TYPE_B",
            category: "test",
            direction: "server_to_client" as const,
            requiresAuth: false,
            dataSchema: AuthLoginSchema,
          },
        },
        {
          type: "TYPE_C",
          definition: {
            type: "TYPE_C",
            category: "test",
            direction: "bidirectional" as const,
            requiresAuth: true,
            dataSchema: HeartbeatSchema,
          },
        },
      ];

      schemas.forEach(({ type, definition }) => {
        WebSocketValidator.registerSchema(type, definition);
      });

      const registeredTypes = WebSocketValidator.getRegisteredTypes();
      expect(registeredTypes).toHaveLength(3);
      expect(registeredTypes).toContain("TYPE_A");
      expect(registeredTypes).toContain("TYPE_B");
      expect(registeredTypes).toContain("TYPE_C");
    });

    test("should overwrite existing schema when registering with same type", () => {
      const originalDefinition: IMessageTypeDefinition = {
        type: "OVERWRITE_TEST",
        category: "original",
        direction: "client_to_server",
        requiresAuth: false,
        description: "Original definition",
      };

      const newDefinition: IMessageTypeDefinition = {
        type: "OVERWRITE_TEST",
        category: "updated",
        direction: "server_to_client",
        requiresAuth: true,
        description: "Updated definition",
      };

      WebSocketValidator.registerSchema("OVERWRITE_TEST", originalDefinition);
      let definition = WebSocketValidator.getSchemaDefinition("OVERWRITE_TEST");
      expect(definition?.category).toBe("original");

      WebSocketValidator.registerSchema("OVERWRITE_TEST", newDefinition);
      definition = WebSocketValidator.getSchemaDefinition("OVERWRITE_TEST");
      expect(definition?.category).toBe("updated");
      expect(definition?.requiresAuth).toBe(true);
    });

    test("should handle numeric message types", () => {
      const definition: IMessageTypeDefinition = {
        type: 42,
        category: "numeric",
        direction: "client_to_server",
        requiresAuth: false,
      };

      WebSocketValidator.registerSchema(42, definition);

      expect(WebSocketValidator.isTypeRegistered(42)).toBe(true);
      expect(WebSocketValidator.getSchemaDefinition(42)).toEqual(definition);
    });

    test("should handle mixed string and numeric types", () => {
      WebSocketValidator.registerSchema("STRING_TYPE", {
        type: "STRING_TYPE",
        category: "string",
        direction: "client_to_server",
        requiresAuth: false,
      });

      WebSocketValidator.registerSchema(123, {
        type: 123,
        category: "numeric",
        direction: "server_to_client",
        requiresAuth: true,
      });

      const types = WebSocketValidator.getRegisteredTypes();
      expect(types).toContain("STRING_TYPE");
      expect(types).toContain(123);
      expect(types).toHaveLength(2);
    });
  });

  describe("Schema Validation with Different Data Types", () => {
    beforeEach(() => {
      // Register test schemas
      WebSocketValidator.registerSchema(EventTypes.MESSAGE_SEND, {
        type: EventTypes.MESSAGE_SEND,
        category: "message",
        direction: "client_to_server",
        requiresAuth: true,
        dataSchema: MessageSendSchema,
        maxSize: 4096,
      });

      WebSocketValidator.registerSchema(EventTypes.VOICE_JOIN, {
        type: EventTypes.VOICE_JOIN,
        category: "voice",
        direction: "client_to_server",
        requiresAuth: true,
        dataSchema: VoiceJoinSchema,
        maxSize: 512,
      });

      WebSocketValidator.registerSchema(EventTypes.DIRECT_MESSAGE_SEND, {
        type: EventTypes.DIRECT_MESSAGE_SEND,
        category: "direct_message",
        direction: "client_to_server",
        requiresAuth: true,
        dataSchema: DirectMessageSendSchema,
        maxSize: 4096,
      });
    });

    test("should validate message send data with all optional fields", () => {
      const fullMessageData = {
        content: "Hello, world!",
        channelId: "123e4567-e89b-12d3-a456-************",
        serverId: "123e4567-e89b-12d3-a456-************",
        replyToId: "123e4567-e89b-12d3-a456-426614174002",
        attachments: [
          {
            filename: "test.jpg",
            url: "https://example.com/test.jpg",
            size: 1024,
            mimeType: "image/jpeg",
          },
          {
            filename: "document.pdf",
            url: "https://example.com/document.pdf",
            size: 2048,
            mimeType: "application/pdf",
          },
        ],
      };

      const result = WebSocketValidator.validateEventData(
        EventTypes.MESSAGE_SEND,
        fullMessageData,
      );

      expect(result.isValid).toBe(true);
      expect(result.sanitizedData).toEqual(fullMessageData);
    });

    test("should validate voice join data with optional fields", () => {
      const voiceData = {
        channelId: "123e4567-e89b-12d3-a456-************",
        serverId: "123e4567-e89b-12d3-a456-************",
        muted: true,
        deafened: false,
      };

      const result = WebSocketValidator.validateEventData(
        EventTypes.VOICE_JOIN,
        voiceData,
      );

      expect(result.isValid).toBe(true);
      expect(result.sanitizedData).toEqual(voiceData);
    });

    test("should validate direct message with attachments", () => {
      const dmData = {
        content: "Private message",
        recipientId: "123e4567-e89b-12d3-a456-************",
        attachments: [
          {
            filename: "secret.txt",
            url: "https://example.com/secret.txt",
            size: 512,
            mimeType: "text/plain",
          },
        ],
      };

      const result = WebSocketValidator.validateEventData(
        EventTypes.DIRECT_MESSAGE_SEND,
        dmData,
      );

      expect(result.isValid).toBe(true);
      expect(result.sanitizedData).toEqual(dmData);
    });

    test("should reject message with invalid attachment data", () => {
      const invalidMessageData = {
        content: "Hello, world!",
        channelId: "123e4567-e89b-12d3-a456-************",
        serverId: "123e4567-e89b-12d3-a456-************",
        attachments: [
          {
            filename: "", // Empty filename
            url: "not-a-url", // Invalid URL
            size: -1, // Negative size
            mimeType: "image/jpeg",
          },
        ],
      };

      const result = WebSocketValidator.validateEventData(
        EventTypes.MESSAGE_SEND,
        invalidMessageData,
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors!.length).toBeGreaterThan(0);
    });
  });

  describe("Complex Schema Validation", () => {
    test("should validate nested object schemas", () => {
      const nestedSchema = z.object({
        user: z.object({
          id: z.string().uuid(),
          profile: z.object({
            name: z.string().min(1),
            settings: z.object({
              theme: z.enum(["light", "dark"]),
              notifications: z.boolean(),
            }),
          }),
        }),
        metadata: z.object({
          timestamp: z.string().datetime(),
          version: z.string(),
        }),
      });

      WebSocketValidator.registerSchema("NESTED_TEST", {
        type: "NESTED_TEST",
        category: "test",
        direction: "client_to_server",
        requiresAuth: true,
        dataSchema: nestedSchema,
      });

      const validNestedData = {
        user: {
          id: "123e4567-e89b-12d3-a456-************",
          profile: {
            name: "Test User",
            settings: {
              theme: "dark" as const,
              notifications: true,
            },
          },
        },
        metadata: {
          timestamp: new Date(),
          version: "1.0.0",
        },
      };

      const result = WebSocketValidator.validateEventData(
        "NESTED_TEST",
        validNestedData,
      );

      expect(result.isValid).toBe(true);
      expect(result.sanitizedData).toEqual(validNestedData);
    });

    test("should validate array schemas", () => {
      const arraySchema = z.object({
        items: z.array(
          z.object({
            id: z.string().uuid(),
            name: z.string().min(1),
            tags: z.array(z.string()).optional(),
          }),
        ),
        metadata: z.object({
          count: z.number().min(0),
          hasMore: z.boolean(),
        }),
      });

      WebSocketValidator.registerSchema("ARRAY_TEST", {
        type: "ARRAY_TEST",
        category: "test",
        direction: "client_to_server",
        requiresAuth: true,
        dataSchema: arraySchema,
      });

      const validArrayData = {
        items: [
          {
            id: "123e4567-e89b-12d3-a456-************",
            name: "Item 1",
            tags: ["tag1", "tag2"],
          },
          {
            id: "123e4567-e89b-12d3-a456-************",
            name: "Item 2",
          },
        ],
        metadata: {
          count: 2,
          hasMore: false,
        },
      };

      const result = WebSocketValidator.validateEventData(
        "ARRAY_TEST",
        validArrayData,
      );

      expect(result.isValid).toBe(true);
      expect(result.sanitizedData).toEqual(validArrayData);
    });

    test("should validate union schemas", () => {
      const unionSchema = z.object({
        action: z.enum(["create", "update", "delete"]),
        data: z.union([
          z.object({
            type: z.literal("user"),
            userId: z.string().uuid(),
            userData: z.object({
              name: z.string(),
              email: z.string().email(),
            }),
          }),
          z.object({
            type: z.literal("message"),
            messageId: z.string().uuid(),
            content: z.string(),
          }),
        ]),
      });

      WebSocketValidator.registerSchema("UNION_TEST", {
        type: "UNION_TEST",
        category: "test",
        direction: "client_to_server",
        requiresAuth: true,
        dataSchema: unionSchema,
      });

      const validUnionData1 = {
        action: "create" as const,
        data: {
          type: "user" as const,
          userId: "123e4567-e89b-12d3-a456-************",
          userData: {
            name: "Test User",
            email: "<EMAIL>",
          },
        },
      };

      const validUnionData2 = {
        action: "update" as const,
        data: {
          type: "message" as const,
          messageId: "123e4567-e89b-12d3-a456-************",
          content: "Updated message content",
        },
      };

      const result1 = WebSocketValidator.validateEventData(
        "UNION_TEST",
        validUnionData1,
      );
      const result2 = WebSocketValidator.validateEventData(
        "UNION_TEST",
        validUnionData2,
      );

      expect(result1.isValid).toBe(true);
      expect(result2.isValid).toBe(true);
    });
  });

  describe("Message Size Validation", () => {
    test("should enforce message size limits", () => {
      WebSocketValidator.registerSchema("SIZE_LIMITED", {
        type: "SIZE_LIMITED",
        category: "test",
        direction: "client_to_server",
        requiresAuth: false,
        maxSize: 500, // Reasonable limit for testing
      });

      const smallMessage: IWebSocketMessage = {
        type: "SIZE_LIMITED",
        data: { small: "data" },
        meta: {
          timestamp: new Date(),
          messageId: "test-id",
          version: "1.0.0",
          source: "client",
        },
      };

      const largeMessage: IWebSocketMessage = {
        type: "SIZE_LIMITED",
        data: { large: "x".repeat(1000) }, // This will make the message too large
        meta: {
          timestamp: new Date(),
          messageId: "test-id",
          version: "1.0.0",
          source: "client",
        },
      };

      const smallResult = WebSocketValidator.validate(smallMessage);
      const largeResult = WebSocketValidator.validate(largeMessage);

      expect(smallResult.isValid).toBe(true);
      expect(largeResult.isValid).toBe(false);
      expect(largeResult.errors).toBeDefined();
      expect(
        largeResult.errors!.some(
          (e) => e.code === WebSocketErrorCode.MESSAGE_TOO_LARGE,
        ),
      ).toBe(true);
    });

    test("should handle messages without size limits", () => {
      WebSocketValidator.registerSchema("NO_SIZE_LIMIT", {
        type: "NO_SIZE_LIMIT",
        category: "test",
        direction: "client_to_server",
        requiresAuth: false,
        // No maxSize specified
      });

      const largeMessage: IWebSocketMessage = {
        type: "NO_SIZE_LIMIT",
        data: { content: "x".repeat(10000) },
        meta: {
          timestamp: new Date(),
          messageId: "test-id",
          version: "1.0.0",
          source: "client",
        },
      };

      const result = WebSocketValidator.validate(largeMessage);
      expect(result.isValid).toBe(true);
    });
  });

  describe("Predefined Message Type Definitions", () => {
    test("should have all expected message types in registry", () => {
      const expectedTypes = [
        EventTypes.AUTHENTICATE,
        EventTypes.MESSAGE_SEND,
        EventTypes.MESSAGE_UPDATE,
        EventTypes.MESSAGE_DELETE,
        EventTypes.REACTION_ADD,
        EventTypes.CHANNEL_SUBSCRIBE,
        EventTypes.CHANNEL_UNSUBSCRIBE,
        EventTypes.CHANNEL_CREATE,
        EventTypes.VOICE_JOIN,
        EventTypes.VOICE_LEAVE,
        EventTypes.VOICE_STATE_UPDATE,
        EventTypes.DIRECT_MESSAGE_SEND,
        EventTypes.FRIEND_REQUEST_SEND,
        EventTypes.FRIEND_REQUEST_ACCEPT,
        EventTypes.FRIEND_REQUEST_DECLINE,
        EventTypes.SERVER_JOIN,
        EventTypes.SERVER_LEAVE,
        EventTypes.SERVER_CREATE,
        EventTypes.USER_STATUS_UPDATE,
        EventTypes.USER_UPDATE,
        EventTypes.PING,
      ];

      const registryTypes = Array.from(MESSAGE_TYPE_DEFINITIONS.keys());

      expectedTypes.forEach((type) => {
        expect(registryTypes).toContain(type);
      });
    });

    test("should have valid schema definitions for all message types", () => {
      for (const [type, definition] of MESSAGE_TYPE_DEFINITIONS) {
        expect(definition.type).toBe(type);
        expect(definition.category).toBeDefined();
        expect(definition.direction).toMatch(
          /^(client_to_server|server_to_client|bidirectional)$/,
        );
        expect(typeof definition.requiresAuth).toBe("boolean");

        if (definition.dataSchema) {
          expect(typeof definition.dataSchema.safeParse).toBe("function");
        }

        if (definition.maxSize) {
          expect(definition.maxSize).toBeGreaterThan(0);
        }
      }
    });

    test("should categorize message types correctly", () => {
      const categories = new Map<string, string[]>();

      for (const [type, definition] of MESSAGE_TYPE_DEFINITIONS) {
        if (!categories.has(definition.category)) {
          categories.set(definition.category, []);
        }
        categories.get(definition.category)!.push(type as string);
      }

      // Verify expected categories exist
      expect(categories.has("auth")).toBe(true);
      expect(categories.has("message")).toBe(true);
      expect(categories.has("voice")).toBe(true);
      expect(categories.has("channel")).toBe(true);
      expect(categories.has("server")).toBe(true);
      expect(categories.has("user")).toBe(true);
      expect(categories.has("system")).toBe(true);
      expect(categories.has("friend")).toBe(true);
      expect(categories.has("direct_message")).toBe(true);

      // Verify categories have expected types
      expect(categories.get("auth")).toContain(EventTypes.AUTHENTICATE);
      expect(categories.get("message")).toContain(EventTypes.MESSAGE_SEND);
      expect(categories.get("voice")).toContain(EventTypes.VOICE_JOIN);
      expect(categories.get("system")).toContain(EventTypes.PING);
    });
  });

  describe("Error Handling in Registry", () => {
    test("should handle registration with invalid schema", () => {
      const invalidDefinition = {
        type: "INVALID_SCHEMA",
        category: "test",
        direction: "client_to_server" as const,
        requiresAuth: false,
        dataSchema: "not a schema" as any,
      };

      expect(() =>
        WebSocketValidator.registerSchema("INVALID_SCHEMA", invalidDefinition),
      ).not.toThrow();

      // The validation should fail when trying to use the invalid schema
      const result = WebSocketValidator.validateEventData(
        "INVALID_SCHEMA",
        { test: "data" },
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
    });

    test("should handle validation with corrupted registry", () => {
      // Register a valid schema first
      WebSocketValidator.registerSchema("VALID_SCHEMA", {
        type: "VALID_SCHEMA",
        category: "test",
        direction: "client_to_server",
        requiresAuth: false,
        dataSchema: z.object({ test: z.string() }),
      });

      // Manually corrupt the schema (simulating memory corruption)
      const definition = WebSocketValidator.getSchemaDefinition("VALID_SCHEMA");
      if (definition) {
        (definition as any).dataSchema = null;
      }

      const result = WebSocketValidator.validateEventData(
        "VALID_SCHEMA",
        { test: "data" },
      );

      // Should handle gracefully
      expect(result.isValid).toBe(true); // No schema means no validation
    });
  });
});