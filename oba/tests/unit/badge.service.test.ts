import { describe, test, expect, beforeEach, mock } from "bun:test";
import { BadgeService } from "../../services/badge.service";
import {
  BadgeNotFoundError,
  BadgeAlreadyAssignedError,
  InsufficientPermissionsError,
  BadgeValidationError
} from "../../class/badge-errors";
import type {
  BadgeType,
  UserBadge,
  CreateBadgeTypeRequest,
  UpdateBadgeTypeRequest,
  BadgeTypeFilters,
  EvaluationResult
} from "../../types/badge.types";

// Mock database connection
const mockDb = {
  select: mock(() => ({ from: mock(() => ({ where: mock(() => ({ limit: mock(() => []) })) })) })),
  insert: mock(() => ({ values: mock(() => ({ returning: mock(() => []) })) })),
  update: mock(() => ({ set: mock(() => ({ where: mock(() => ({ returning: mock(() => []) })) })) })),
  delete: mock(() => ({ where: mock(() => ({ returning: mock(() => []) })) })),
  transaction: mock((callback) => callback(mockDb))
};

// Mock utility functions
const mockBadgeUtils = {
  createBadgeType: mock(),
  getBadgeTypeById: mock(),
  getBadgeTypes: mock(),
  updateBadgeType: mock(),
  deleteBadgeType: mock(),
  assignBadgeToUser: mock(),
  removeBadgeFromUser: mock(),
  getUserBadges: mock(),
  getUserStats: mock(),
  getBadgeStats: mock(),
  getBadgeLeaderboard: mock(),
  getAvailableBadgesForUser: mock(),
  getBadgeProgress: mock(),
  bulkAssignBadges: mock()
};

const mockBadgeEvaluation = {
  evaluateUserForAutomaticBadges: mock(),
  batchEvaluateUsers: mock(),
  evaluateAllUsersForAutomaticBadges: mock(),
  reevaluateBadgeTypeForAllUsers: mock()
};

const mockPermissions = {
  hasServerPermission: mock()
};

// Mock the imports
mock.module("../../db/utils/badge-utils", () => mockBadgeUtils);
mock.module("../../db/utils/badge-evaluation", () => mockBadgeEvaluation);
mock.module("../../utils/permissions", () => mockPermissions);

describe("BadgeService", () => {
  let badgeService: BadgeService;
  const mockUserId = "550e8400-e29b-41d4-a716-446655440000";
  const mockBadgeTypeId = "550e8400-e29b-41d4-a716-446655440001";
  const mockServerId = "550e8400-e29b-41d4-a716-446655440002";

  beforeEach(() => {
    badgeService = new BadgeService(mockDb as any);
    
    // Reset all mocks
    Object.values(mockBadgeUtils).forEach(mock => mock.mockReset());
    Object.values(mockBadgeEvaluation).forEach(mock => mock.mockReset());
    Object.values(mockPermissions).forEach(mock => mock.mockReset());
  });

  describe("Badge Type Management", () => {
    describe("createBadgeType", () => {
      const validBadgeData: CreateBadgeTypeRequest = {
        name: "Test Badge",
        description: "A test badge",
        category: "achievement",
        assignmentType: "manual"
      };

      test("should create badge type successfully", async () => {
        const expectedBadge: BadgeType = {
          id: mockBadgeTypeId,
          ...validBadgeData,
          color: "#000000",
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockBadgeUtils.getBadgeTypes.mockResolvedValue([]);
        mockBadgeUtils.createBadgeType.mockResolvedValue(expectedBadge);

        const result = await badgeService.createBadgeType(validBadgeData, mockUserId);

        expect(result).toEqual(expectedBadge);
        expect(mockBadgeUtils.createBadgeType).toHaveBeenCalledWith(mockDb, expect.objectContaining({
          name: validBadgeData.name,
          description: validBadgeData.description,
          category: validBadgeData.category,
          assignmentType: validBadgeData.assignmentType
        }));
      });

      test("should throw validation error for duplicate name", async () => {
        const existingBadge: BadgeType = {
          id: "550e8400-e29b-41d4-a716-446655440003",
          name: "Test Badge",
          description: "Existing badge",
          category: "achievement",
          assignmentType: "manual",
          color: "#000000",
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockBadgeUtils.getBadgeTypes.mockResolvedValue([existingBadge]);

        await expect(badgeService.createBadgeType(validBadgeData, mockUserId))
          .rejects.toThrow(BadgeValidationError);
      });

      test("should throw validation error for invalid data", async () => {
        const invalidData = {
          name: "", // Invalid: empty name
          description: "Test description",
          category: "achievement",
          assignmentType: "manual"
        } as CreateBadgeTypeRequest;

        await expect(badgeService.createBadgeType(invalidData, mockUserId))
          .rejects.toThrow();
      });

      test("should require criteria for automatic badges", async () => {
        const automaticBadgeWithoutCriteria: CreateBadgeTypeRequest = {
          name: "Auto Badge",
          description: "Automatic badge without criteria",
          category: "achievement",
          assignmentType: "automatic"
          // Missing criteria
        };

        await expect(badgeService.createBadgeType(automaticBadgeWithoutCriteria, mockUserId))
          .rejects.toThrow();
      });
    });

    describe("updateBadgeType", () => {
      const existingBadge: BadgeType = {
        id: mockBadgeTypeId,
        name: "Original Badge",
        description: "Original description",
        category: "achievement",
        assignmentType: "manual",
        color: "#000000",
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      test("should update badge type successfully", async () => {
        const updates: UpdateBadgeTypeRequest = {
          name: "Updated Badge",
          description: "Updated description"
        };

        const updatedBadge = { ...existingBadge, ...updates };

        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(existingBadge);
        mockBadgeUtils.getBadgeTypes.mockResolvedValue([]);
        mockBadgeUtils.updateBadgeType.mockResolvedValue(updatedBadge);

        const result = await badgeService.updateBadgeType(mockBadgeTypeId, updates, mockUserId);

        expect(result).toEqual(updatedBadge);
        expect(mockBadgeUtils.updateBadgeType).toHaveBeenCalledWith(mockDb, mockBadgeTypeId, updates);
      });

      test("should throw error for non-existent badge", async () => {
        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(null);

        await expect(badgeService.updateBadgeType(mockBadgeTypeId, {}, mockUserId))
          .rejects.toThrow(BadgeNotFoundError);
      });

      test("should prevent duplicate names when updating", async () => {
        const anotherBadge: BadgeType = {
          id: "550e8400-e29b-41d4-a716-446655440004",
          name: "Another Badge",
          description: "Another description",
          category: "achievement",
          assignmentType: "manual",
          color: "#000000",
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(existingBadge);
        mockBadgeUtils.getBadgeTypes.mockResolvedValue([anotherBadge]);

        await expect(badgeService.updateBadgeType(mockBadgeTypeId, { name: "Another Badge" }, mockUserId))
          .rejects.toThrow(BadgeValidationError);
      });
    });

    describe("deleteBadgeType", () => {
      test("should delete badge type successfully", async () => {
        const existingBadge: BadgeType = {
          id: mockBadgeTypeId,
          name: "Badge to Delete",
          description: "This badge will be deleted",
          category: "achievement",
          assignmentType: "manual",
          color: "#000000",
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(existingBadge);
        mockBadgeUtils.deleteBadgeType.mockResolvedValue(true);

        await badgeService.deleteBadgeType(mockBadgeTypeId, mockUserId);

        expect(mockBadgeUtils.deleteBadgeType).toHaveBeenCalledWith(mockDb, mockBadgeTypeId);
      });

      test("should throw error for non-existent badge", async () => {
        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(null);

        await expect(badgeService.deleteBadgeType(mockBadgeTypeId, mockUserId))
          .rejects.toThrow(BadgeNotFoundError);
      });
    });

    describe("getBadgeTypes", () => {
      test("should get badge types with filters", async () => {
        const filters: BadgeTypeFilters = {
          category: "achievement",
          isActive: true
        };

        const expectedBadges: BadgeType[] = [
          {
            id: "550e8400-e29b-41d4-a716-446655440005",
            name: "Badge 1",
            description: "First badge",
            category: "achievement",
            assignmentType: "manual",
            color: "#000000",
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ];

        mockBadgeUtils.getBadgeTypes.mockResolvedValue(expectedBadges);

        const result = await badgeService.getBadgeTypes(filters, 10, 0);

        expect(result).toEqual(expectedBadges);
        expect(mockBadgeUtils.getBadgeTypes).toHaveBeenCalledWith(mockDb, filters, 10, 0);
      });
    });
  });

  describe("User Badge Operations", () => {
    describe("assignBadge", () => {
      const activeBadge: BadgeType = {
        id: mockBadgeTypeId,
        name: "Test Badge",
        description: "A test badge",
        category: "achievement",
        assignmentType: "manual",
        color: "#000000",
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      test("should assign badge successfully", async () => {
        const expectedUserBadge: UserBadge = {
          id: "550e8400-e29b-41d4-a716-446655440006",
          userId: mockUserId,
          badgeTypeId: mockBadgeTypeId,
          assignedBy: mockUserId,
          assignedAt: new Date(),
          isVisible: true,
          badgeType: activeBadge
        };

        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(activeBadge);
        mockBadgeUtils.getUserBadges.mockResolvedValue([]);
        mockBadgeUtils.assignBadgeToUser.mockResolvedValue(expectedUserBadge);
        mockPermissions.hasServerPermission.mockResolvedValue(true);

        const result = await badgeService.assignBadge(mockUserId, mockBadgeTypeId, mockUserId, mockServerId);

        expect(result).toEqual(expectedUserBadge);
        expect(mockBadgeUtils.assignBadgeToUser).toHaveBeenCalledWith(mockDb, mockUserId, mockBadgeTypeId, mockUserId);
      });

      test("should throw error for non-existent badge", async () => {
        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(null);

        await expect(badgeService.assignBadge(mockUserId, mockBadgeTypeId, mockUserId))
          .rejects.toThrow(BadgeNotFoundError);
      });

      test("should throw error for inactive badge", async () => {
        const inactiveBadge = { ...activeBadge, isActive: false };
        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(inactiveBadge);

        await expect(badgeService.assignBadge(mockUserId, mockBadgeTypeId, mockUserId))
          .rejects.toThrow(BadgeValidationError);
      });

      test("should throw error for duplicate assignment", async () => {
        const existingUserBadge: UserBadge = {
          id: "550e8400-e29b-41d4-a716-446655440007",
          userId: mockUserId,
          badgeTypeId: mockBadgeTypeId,
          assignedAt: new Date(),
          isVisible: true
        };

        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(activeBadge);
        mockBadgeUtils.getUserBadges.mockResolvedValue([existingUserBadge]);

        await expect(badgeService.assignBadge(mockUserId, mockBadgeTypeId, mockUserId))
          .rejects.toThrow(BadgeAlreadyAssignedError);
      });
    });

    describe("removeBadge", () => {
      const activeBadge: BadgeType = {
        id: mockBadgeTypeId,
        name: "Test Badge",
        description: "A test badge",
        category: "achievement",
        assignmentType: "manual",
        color: "#000000",
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      test("should remove badge successfully", async () => {
        const existingUserBadge: UserBadge = {
          id: "550e8400-e29b-41d4-a716-446655440008",
          userId: mockUserId,
          badgeTypeId: mockBadgeTypeId,
          assignedAt: new Date(),
          isVisible: true
        };

        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(activeBadge);
        mockBadgeUtils.getUserBadges.mockResolvedValue([existingUserBadge]);
        mockBadgeUtils.removeBadgeFromUser.mockResolvedValue(true);
        mockPermissions.hasServerPermission.mockResolvedValue(true);

        await badgeService.removeBadge(mockUserId, mockBadgeTypeId, mockUserId, mockServerId);

        expect(mockBadgeUtils.removeBadgeFromUser).toHaveBeenCalledWith(mockDb, mockUserId, mockBadgeTypeId);
      });

      test("should throw error when user doesn't have badge", async () => {
        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(activeBadge);
        mockBadgeUtils.getUserBadges.mockResolvedValue([]);
        mockPermissions.hasServerPermission.mockResolvedValue(true);

        await expect(badgeService.removeBadge(mockUserId, mockBadgeTypeId, mockUserId, mockServerId))
          .rejects.toThrow(BadgeValidationError);
      });
    });

    describe("getUserBadges", () => {
      test("should get user badges", async () => {
        const expectedBadges: UserBadge[] = [
          {
            id: "550e8400-e29b-41d4-a716-446655440009",
            userId: mockUserId,
            badgeTypeId: "550e8400-e29b-41d4-a716-44665544000a",
            assignedAt: new Date(),
            isVisible: true
          }
        ];

        mockBadgeUtils.getUserBadges.mockResolvedValue(expectedBadges);

        const result = await badgeService.getUserBadges(mockUserId, true);

        expect(result).toEqual(expectedBadges);
        expect(mockBadgeUtils.getUserBadges).toHaveBeenCalledWith(mockDb, mockUserId, true);
      });
    });
  });

  describe("Automatic Badge Evaluation", () => {
    describe("evaluateUserBadges", () => {
      test("should evaluate user badges successfully", async () => {
        const expectedResult: EvaluationResult = {
          userId: mockUserId,
          newBadges: [],
          evaluatedBadges: ["badge-1", "badge-2"],
          errors: []
        };

        mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockResolvedValue(expectedResult);

        const result = await badgeService.evaluateUserBadges(mockUserId);

        expect(result).toEqual(expectedResult);
        expect(mockBadgeEvaluation.evaluateUserForAutomaticBadges).toHaveBeenCalledWith(mockDb, mockUserId);
      });

      test("should handle evaluation errors gracefully", async () => {
        mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockRejectedValue(new Error("Evaluation failed"));

        const result = await badgeService.evaluateUserBadges(mockUserId);

        expect(result.userId).toBe(mockUserId);
        expect(result.newBadges).toEqual([]);
        expect(result.errors).toContain("Evaluation failed: Evaluation failed");
      });
    });

    describe("batchEvaluateUsers", () => {
      test("should batch evaluate users", async () => {
        const userIds = ["550e8400-e29b-41d4-a716-44665544000b", "550e8400-e29b-41d4-a716-44665544000c"];
        const expectedResults: EvaluationResult[] = [
          {
            userId: "550e8400-e29b-41d4-a716-44665544000b",
            newBadges: [],
            evaluatedBadges: [],
            errors: []
          },
          {
            userId: "550e8400-e29b-41d4-a716-44665544000c",
            newBadges: [],
            evaluatedBadges: [],
            errors: []
          }
        ];

        mockBadgeEvaluation.batchEvaluateUsers.mockResolvedValue(expectedResults);

        const result = await badgeService.batchEvaluateUsers(userIds);

        expect(result).toEqual(expectedResults);
        expect(mockBadgeEvaluation.batchEvaluateUsers).toHaveBeenCalledWith(mockDb, userIds);
      });
    });
  });

  describe("Badge Statistics", () => {
    describe("getBadgeStats", () => {
      test("should get badge statistics", async () => {
        const expectedStats = {
          totalBadges: 10,
          totalAssignments: 50,
          categoryBreakdown: {
            achievement: 5,
            role: 3,
            special: 2,
            community: 0,
            milestone: 0
          },
          mostPopularBadges: []
        };

        mockBadgeUtils.getBadgeStats.mockResolvedValue(expectedStats);

        const result = await badgeService.getBadgeStats();

        expect(result).toEqual(expectedStats);
        expect(mockBadgeUtils.getBadgeStats).toHaveBeenCalledWith(mockDb);
      });
    });

    describe("getBadgeLeaderboard", () => {
      test("should get badge leaderboard", async () => {
        const expectedLeaderboard = [
          {
            userId: "550e8400-e29b-41d4-a716-44665544000d",
            username: "User One",
            badgeCount: 5,
            badges: []
          }
        ];

        mockBadgeUtils.getBadgeLeaderboard.mockResolvedValue(expectedLeaderboard);

        const result = await badgeService.getBadgeLeaderboard(10);

        expect(result).toEqual(expectedLeaderboard);
        expect(mockBadgeUtils.getBadgeLeaderboard).toHaveBeenCalledWith(mockDb, 10);
      });
    });
  });

  describe("Bulk Operations", () => {
    describe("bulkAssignBadges", () => {
      test("should bulk assign badges", async () => {
        const assignments = [
          { userId: "550e8400-e29b-41d4-a716-44665544000e", badgeTypeId: "550e8400-e29b-41d4-a716-44665544000f" },
          { userId: "550e8400-e29b-41d4-a716-446655440010", badgeTypeId: "550e8400-e29b-41d4-a716-446655440011" }
        ];

        const expectedResults: UserBadge[] = [
          {
            id: "550e8400-e29b-41d4-a716-446655440012",
            userId: "550e8400-e29b-41d4-a716-44665544000e",
            badgeTypeId: "550e8400-e29b-41d4-a716-44665544000f",
            assignedBy: mockUserId,
            assignedAt: new Date(),
            isVisible: true
          }
        ];

        mockBadgeUtils.bulkAssignBadges.mockResolvedValue(expectedResults);
        mockPermissions.hasServerPermission.mockResolvedValue(true);

        const result = await badgeService.bulkAssignBadges(assignments, mockUserId, mockServerId);

        expect(result).toEqual(expectedResults);
        expect(mockBadgeUtils.bulkAssignBadges).toHaveBeenCalledWith(
          mockDb,
          assignments.map(a => ({ ...a, assignedBy: mockUserId }))
        );
      });
    });
  });
});