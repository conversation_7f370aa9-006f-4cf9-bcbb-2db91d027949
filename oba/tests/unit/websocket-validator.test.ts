import { describe, test, expect, beforeEach, afterEach } from "bun:test";
import { WebSocketValidator } from "../../utils/websocket-validator";
import {
  MESSAGE_TYPE_DEFINITIONS,
  MessageSendSchema,
  AuthLoginSchema,
  HeartbeatSchema,
} from "../../utils/websocket-schemas";
import { EventTypes } from "../../constants/eventTypes";
import {
  type IWebSocketMessage,
  type IMessageTypeDefinition,
  WebSocketErrorCode,
} from "../../types/websocket-standardization.types";

describe("WebSocketValidator", () => {
  beforeEach(() => {
    // Clear any existing schemas before each test
    WebSocketValidator.clearSchemas();
  });

  afterEach(() => {
    // Clean up after each test
    WebSocketValidator.clearSchemas();
  });

  describe("Schema Registration", () => {
    test("should register a schema successfully", () => {
      const definition: IMessageTypeDefinition = {
        type: "TEST_MESSAGE",
        category: "test",
        direction: "client_to_server",
        requiresAuth: true,
        dataSchema: MessageSendSchema,
        description: "Test message type",
      };

      WebSocketValidator.registerSchema("TEST_MESSAGE", definition);
      
      expect(WebSocketValidator.isTypeRegistered("TEST_MESSAGE")).toBe(true);
      expect(WebSocketValidator.getSchemaDefinition("TEST_MESSAGE")).toEqual(definition);
    });

    test("should get all registered types", () => {
      WebSocketValidator.registerSchema("TYPE1", {
        type: "TYPE1",
        category: "test",
        direction: "client_to_server",
        requiresAuth: false,
      });
      
      WebSocketValidator.registerSchema("TYPE2", {
        type: "TYPE2",
        category: "test",
        direction: "server_to_client",
        requiresAuth: false,
      });

      const types = WebSocketValidator.getRegisteredTypes();
      expect(types).toContain("TYPE1");
      expect(types).toContain("TYPE2");
      expect(types.length).toBe(2);
    });

    test("should clear all schemas", () => {
      WebSocketValidator.registerSchema("TEST", {
        type: "TEST",
        category: "test",
        direction: "client_to_server",
        requiresAuth: false,
      });

      expect(WebSocketValidator.isTypeRegistered("TEST")).toBe(true);
      
      WebSocketValidator.clearSchemas();
      
      expect(WebSocketValidator.isTypeRegistered("TEST")).toBe(false);
      expect(WebSocketValidator.getRegisteredTypes().length).toBe(0);
    });
  });

  describe("Message Structure Validation", () => {
    test("should validate a properly structured message", () => {
      const validMessage: IWebSocketMessage = {
        type: "TEST_MESSAGE",
        data: { test: "data" },
        meta: {
          timestamp: new Date(),
          messageId: "test-message-id",
          version: "1.0.0",
          source: "client",
        },
      };

      const result = WebSocketValidator.validateMessageStructure(validMessage);
      expect(result.isValid).toBe(true);
      expect(result.errors).toBeUndefined();
    });

    test("should reject non-object messages", () => {
      const result = WebSocketValidator.validateMessageStructure("invalid");
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors![0].code).toBe(WebSocketErrorCode.INVALID_MESSAGE_FORMAT);
    });

    test("should reject messages without required fields", () => {
      const invalidMessage = {
        data: { test: "data" },
        // Missing type and meta
      };

      const result = WebSocketValidator.validateMessageStructure(invalidMessage);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors!.length).toBe(2); // Missing type and meta
      expect(result.errors!.some(e => e.field === "type")).toBe(true);
      expect(result.errors!.some(e => e.field === "meta")).toBe(true);
    });

    test("should validate meta structure", () => {
      const messageWithInvalidMeta = {
        type: "TEST",
        meta: {
          // Missing required fields
          timestamp: "invalid-timestamp",
          source: "invalid-source",
        },
      };

      const result = WebSocketValidator.validateMessageStructure(messageWithInvalidMeta);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors!.some(e => e.field === "meta.id")).toBe(true);
      expect(result.errors!.some(e => e.field === "meta.version")).toBe(true);
      expect(result.errors!.some(e => e.field === "meta.timestamp")).toBe(true);
      expect(result.errors!.some(e => e.field === "meta.source")).toBe(true);
    });

    test("should validate ISO 8601 timestamps", () => {
      const messageWithInvalidTimestamp = {
        type: "TEST",
        meta: {
          timestamp: "2023-13-45T25:70:90Z", // Invalid date
          messageId: "test-id",
          version: "1.0.0",
          source: "client",
        },
      };

      const result = WebSocketValidator.validateMessageStructure(messageWithInvalidTimestamp);
      
      expect(result.isValid).toBe(false);
      expect(result.errors!.some(e => 
        e.field === "meta.timestamp" && 
        e.code === WebSocketErrorCode.INVALID_DATA
      )).toBe(true);
    });
  });

  describe("Data Validation", () => {
    beforeEach(() => {
      // Register test schemas
      WebSocketValidator.registerSchema(EventTypes.MESSAGE_SEND, {
        type: EventTypes.MESSAGE_SEND,
        category: "message",
        direction: "client_to_server",
        requiresAuth: true,
        dataSchema: MessageSendSchema,
      });

      WebSocketValidator.registerSchema(EventTypes.AUTHENTICATE, {
        type: EventTypes.AUTHENTICATE,
        category: "auth",
        direction: "client_to_server",
        requiresAuth: false,
        dataSchema: AuthLoginSchema,
      });
    });

    test("should validate correct message data", () => {
      const validData = {
        content: "Hello, world!",
        channelId: "123e4567-e89b-12d3-a456-************",
        serverId: "123e4567-e89b-12d3-a456-************",
      };

      const result = WebSocketValidator.validateEventData(EventTypes.MESSAGE_SEND, validData);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData).toEqual(validData);
    });

    test("should reject invalid message data", () => {
      const invalidData = {
        content: "", // Empty content not allowed
        channelId: "invalid-uuid",
        serverId: "123e4567-e89b-12d3-a456-************",
      };

      const result = WebSocketValidator.validateEventData(EventTypes.MESSAGE_SEND, invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors!.length).toBeGreaterThan(0);
    });

    test("should handle missing data schema gracefully", () => {
      WebSocketValidator.registerSchema("NO_SCHEMA", {
        type: "NO_SCHEMA",
        category: "test",
        direction: "client_to_server",
        requiresAuth: false,
        // No dataSchema defined
      });

      const result = WebSocketValidator.validateEventData("NO_SCHEMA", { any: "data" });
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData).toEqual({ any: "data" });
    });

    test("should validate auth login data", () => {
      const validAuthData = {
        username: "testuser",
        password: "testpassword",
        rememberMe: true,
      };

      const result = WebSocketValidator.validateEventData(EventTypes.AUTHENTICATE, validAuthData);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData).toEqual(validAuthData);
    });

    test("should reject invalid auth login data", () => {
      const invalidAuthData = {
        username: "", // Empty username
        password: "testpassword",
      };

      const result = WebSocketValidator.validateEventData(EventTypes.AUTHENTICATE, invalidAuthData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
    });
  });

  describe("Complete Message Validation", () => {
    beforeEach(() => {
      // Register schemas for testing
      WebSocketValidator.registerSchema(EventTypes.MESSAGE_SEND, {
        type: EventTypes.MESSAGE_SEND,
        category: "message",
        direction: "client_to_server",
        requiresAuth: true,
        dataSchema: MessageSendSchema,
        maxSize: 1000,
      });

      WebSocketValidator.registerSchema(EventTypes.PING, {
        type: EventTypes.PING,
        category: "system",
        direction: "bidirectional",
        requiresAuth: false,
        dataSchema: HeartbeatSchema,
      });
    });

    test("should validate a complete valid message", () => {
      const validMessage: IWebSocketMessage = {
        type: EventTypes.PING,
        data: {
          timestamp: new Date(),
        },
        meta: {
          timestamp: new Date(),
          messageId: "test-message-id",
          version: "1.0.0",
          source: "client",
        },
      };

      const result = WebSocketValidator.validate(validMessage);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData).toBeDefined();
    });

    test("should reject message with unknown type", () => {
      const messageWithUnknownType: IWebSocketMessage = {
        type: "UNKNOWN_TYPE",
        data: { test: "data" },
        meta: {
          timestamp: new Date(),
          messageId: "test-message-id",
          version: "1.0.0",
          source: "client",
        },
      };

      const result = WebSocketValidator.validate(messageWithUnknownType);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors![0].code).toBe(WebSocketErrorCode.INVALID_MESSAGE_TYPE);
    });

    test("should validate message size limits", () => {
      const largeMessage: IWebSocketMessage = {
        type: EventTypes.MESSAGE_SEND,
        data: {
          content: "x".repeat(2000), // Very long content
          channelId: "123e4567-e89b-12d3-a456-************",
          serverId: "123e4567-e89b-12d3-a456-************",
        },
        meta: {
          timestamp: new Date(),
          messageId: "test-message-id",
          version: "1.0.0",
          source: "client",
        },
      };

      const result = WebSocketValidator.validate(largeMessage);
      
      // Should fail due to size limit (maxSize: 1000 in schema)
      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors!.some(e => e.code === WebSocketErrorCode.MESSAGE_TOO_LARGE)).toBe(true);
    });

    test("should validate target structure", () => {
      const messageWithInvalidTarget: IWebSocketMessage = {
        type: EventTypes.PING,
        data: {
          timestamp: new Date(),
        },
        meta: {
          timestamp: new Date(),
          messageId: "test-message-id",
          version: "1.0.0",
          source: "client",
        },
        target: {
          userId: "invalid-uuid", // Invalid UUID format
          channelId: "123e4567-e89b-12d3-a456-************",
        },
      };

      const result = WebSocketValidator.validate(messageWithInvalidTarget);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors!.some(e => 
        e.field === "target.userId" && 
        e.code === WebSocketErrorCode.INVALID_DATA
      )).toBe(true);
    });

    test("should validate correlation ID format", () => {
      const messageWithInvalidCorrelationId: IWebSocketMessage = {
        type: EventTypes.PING,
        data: {
          timestamp: new Date(),
        },
        meta: {
          timestamp: new Date(),
          messageId: "test-message-id",
          correlationId: "", // Empty correlation ID
          version: "1.0.0",
          source: "client",
        },
      };

      const result = WebSocketValidator.validate(messageWithInvalidCorrelationId);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors!.some(e => 
        e.field === "meta.correlationId" && 
        e.code === WebSocketErrorCode.INVALID_DATA
      )).toBe(true);
    });
  });

  describe("Error Handling", () => {
    test("should handle validation exceptions gracefully", () => {
      // Register a schema with invalid Zod schema to trigger an error
      const invalidDefinition: IMessageTypeDefinition = {
        type: "INVALID_SCHEMA",
        category: "test",
        direction: "client_to_server",
        requiresAuth: false,
        dataSchema: { safeParse: "not a function" } as any, // This will cause an error
      };

      WebSocketValidator.registerSchema("INVALID_SCHEMA", invalidDefinition);

      const message: IWebSocketMessage = {
        type: "INVALID_SCHEMA",
        data: { test: "data" },
        meta: {
          timestamp: new Date(),
          messageId: "test-message-id",
          version: "1.0.0",
          source: "client",
        },
      };

      const result = WebSocketValidator.validate(message);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors![0].code).toBe(WebSocketErrorCode.INTERNAL_ERROR);
    });

    test("should handle malformed input gracefully", () => {
      const result = WebSocketValidator.validate(null);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors![0].code).toBe(WebSocketErrorCode.INVALID_MESSAGE_FORMAT);
    });
  });

  describe("Integration with Message Schemas", () => {
    test("should work with predefined message type definitions", () => {
      // Register some predefined schemas
      for (const [type, definition] of MESSAGE_TYPE_DEFINITIONS) {
        WebSocketValidator.registerSchema(type, definition);
      }

      // Test MESSAGE_SEND validation
      const messageSendData = {
        content: "Hello, world!",
        channelId: "123e4567-e89b-12d3-a456-************",
        serverId: "123e4567-e89b-12d3-a456-************",
      };

      const result = WebSocketValidator.validateEventData(EventTypes.MESSAGE_SEND, messageSendData);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData).toEqual(messageSendData);
    });

    test("should validate all registered message types", () => {
      for (const [type, definition] of MESSAGE_TYPE_DEFINITIONS) {
        WebSocketValidator.registerSchema(type, definition);
      }

      const registeredTypes = WebSocketValidator.getRegisteredTypes();
      
      expect(registeredTypes.length).toBe(MESSAGE_TYPE_DEFINITIONS.size);
      
      for (const type of MESSAGE_TYPE_DEFINITIONS.keys()) {
        expect(WebSocketValidator.isTypeRegistered(type)).toBe(true);
      }
    });
  });
});