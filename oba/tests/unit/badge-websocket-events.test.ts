import { describe, test, expect, beforeEach, afterEach, mock, spyOn } from "bun:test";
import { BadgeWebSocketService } from "../../utils/badge-websocket";
import { WebSocketManager } from "../../manager/websocket.manager";
import { WebSocketUtils } from "../../utils/websocket-utils";
import { EventTypes } from "../../constants/eventTypes";
import type {
  BadgeAssignedPayload,
  BadgeRemovedPayload,
  BadgeProgressUpdatePayload,
  BadgeAssignedEvent,
  BadgeRemovedEvent,
  BadgeProgressUpdateEvent
} from "../../types/badge-websocket.types";
import type { UserBadge, BadgeType } from "../../types/badge.types";

// Mock WebSocket Manager
const mockWebSocketManager = {
  broadcastToUser: mock(),
  broadcast: mock(),
  getInstance: mock()
};

// Mock WebSocket Utils
const mockWebSocketUtils = {
  event: mock()
};

// Mock database utilities
const mockDbUtils = {
  getUserServerMemberships: mock()
};

// Mock the imports
mock.module("../../manager/websocket.manager", () => ({
  WebSocketManager: {
    getInstance: () => mockWebSocketManager
  }
}));

mock.module("../../utils/websocket-utils", () => ({
  WebSocketUtils: mockWebSocketUtils
}));

mock.module("../../db/utils", () => mockDbUtils);
mock.module("../../db", () => ({ db: {} }));

describe("Badge WebSocket Events Tests", () => {
  let badgeWebSocketService: BadgeWebSocketService;
  
  const mockUserId = "550e8400-e29b-41d4-a716-446655440000";
  const mockAssignerId = "550e8400-e29b-41d4-a716-446655440001";
  const mockBadgeTypeId = "550e8400-e29b-41d4-a716-446655440002";
  const mockServerId = "550e8400-e29b-41d4-a716-446655440003";

  const mockBadgeType: BadgeType = {
    id: mockBadgeTypeId,
    badgeId: "test-badge",
    name: "Test Badge",
    description: "A test badge for WebSocket testing",
    icon: "🏆",
    design: {
      shape: "circle",
      background: "#FFD700",
      colors: ["#FFD700", "#FFA500"]
    },
    criteria: {
      requirement: "Send 10 messages",
      tracked: "message_count",
      type: "message_count",
      threshold: 10
    },
    unlockType: "automatic",
    category: "achievement",
    displayOrder: 1,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const mockUserBadge: UserBadge = {
    id: "user-badge-1",
    userId: mockUserId,
    badgeTypeId: mockBadgeTypeId,
    assignedBy: mockAssignerId,
    assignedAt: new Date(),
    isVisible: true,
    badgeType: mockBadgeType
  };

  beforeEach(() => {
    badgeWebSocketService = new BadgeWebSocketService();
    
    // Reset all mocks
    Object.values(mockWebSocketManager).forEach(mock => mock.mockReset());
    Object.values(mockWebSocketUtils).forEach(mock => mock.mockReset());
    Object.values(mockDbUtils).forEach(mock => mock.mockReset());

    // Setup default mock returns
    mockWebSocketUtils.event.mockReturnValue({ type: "test", data: {} });
    mockDbUtils.getUserServerMemberships.mockResolvedValue([]);
  });

  afterEach(() => {
    // Clean up any test state
  });

  describe("Badge Assigned Events", () => {
    test("should broadcast badge assigned event to user", async () => {
      const payload: BadgeAssignedPayload = {
        userId: mockUserId,
        badge: mockUserBadge,
        isAutomatic: false,
        assignedBy: mockAssignerId
      };

      const expectedEvent: BadgeAssignedEvent = {
        type: "BADGE_ASSIGNED",
        userId: mockUserId,
        badge: mockUserBadge,
        isAutomatic: false,
        assignedBy: mockAssignerId,
        timestamp: expect.any(Date)
      };

      const mockMessage = { type: "BADGE_ASSIGNED", data: expectedEvent };
      mockWebSocketUtils.event.mockReturnValue(mockMessage);

      await badgeWebSocketService.broadcastBadgeAssigned(payload);

      expect(mockWebSocketUtils.event).toHaveBeenCalledWith(
        EventTypes.BADGE_ASSIGNED,
        expect.objectContaining({
          type: "BADGE_ASSIGNED",
          userId: mockUserId,
          badge: mockUserBadge,
          isAutomatic: false,
          assignedBy: mockAssignerId
        })
      );

      expect(mockWebSocketManager.broadcastToUser).toHaveBeenCalledWith(mockUserId, mockMessage);
      expect(mockWebSocketManager.broadcastToUser).toHaveBeenCalledWith(mockAssignerId, mockMessage);
    });

    test("should broadcast automatic badge assignment", async () => {
      const payload: BadgeAssignedPayload = {
        userId: mockUserId,
        badge: { ...mockUserBadge, assignedBy: undefined },
        isAutomatic: true,
        assignedBy: undefined
      };

      const mockMessage = { type: "BADGE_ASSIGNED", data: {} };
      mockWebSocketUtils.event.mockReturnValue(mockMessage);

      await badgeWebSocketService.broadcastBadgeAssigned(payload);

      expect(mockWebSocketManager.broadcastToUser).toHaveBeenCalledTimes(1);
      expect(mockWebSocketManager.broadcastToUser).toHaveBeenCalledWith(mockUserId, mockMessage);
    });

    test("should broadcast to user servers for profile updates", async () => {
      const payload: BadgeAssignedPayload = {
        userId: mockUserId,
        badge: mockUserBadge,
        isAutomatic: false,
        assignedBy: mockAssignerId
      };

      const mockServerMemberships = [
        { serverId: "server-1", userId: mockUserId },
        { serverId: "server-2", userId: mockUserId }
      ];

      mockDbUtils.getUserServerMemberships.mockResolvedValue(mockServerMemberships);

      const mockMessage = { type: "BADGE_ASSIGNED", data: {} };
      mockWebSocketUtils.event.mockReturnValue(mockMessage);

      await badgeWebSocketService.broadcastBadgeAssigned(payload);

      expect(mockDbUtils.getUserServerMemberships).toHaveBeenCalledWith(expect.anything(), mockUserId);
      expect(mockWebSocketManager.broadcast).toHaveBeenCalledTimes(2);
      expect(mockWebSocketManager.broadcast).toHaveBeenCalledWith(mockMessage, "server-1", undefined, mockUserId);
      expect(mockWebSocketManager.broadcast).toHaveBeenCalledWith(mockMessage, "server-2", undefined, mockUserId);
    });

    test("should handle errors gracefully during badge assigned broadcast", async () => {
      const payload: BadgeAssignedPayload = {
        userId: mockUserId,
        badge: mockUserBadge,
        isAutomatic: false,
        assignedBy: mockAssignerId
      };

      mockWebSocketManager.broadcastToUser.mockRejectedValue(new Error("WebSocket error"));

      // Should not throw error
      await expect(badgeWebSocketService.broadcastBadgeAssigned(payload)).resolves.toBeUndefined();
    });
  });

  describe("Badge Removed Events", () => {
    test("should broadcast badge removed event", async () => {
      const payload: BadgeRemovedPayload = {
        userId: mockUserId,
        badgeTypeId: mockBadgeTypeId,
        badgeType: mockBadgeType,
        removedBy: mockAssignerId
      };

      const expectedEvent: BadgeRemovedEvent = {
        type: "BADGE_REMOVED",
        userId: mockUserId,
        badgeTypeId: mockBadgeTypeId,
        badgeType: mockBadgeType,
        removedBy: mockAssignerId,
        timestamp: expect.any(Date)
      };

      const mockMessage = { type: "BADGE_REMOVED", data: expectedEvent };
      mockWebSocketUtils.event.mockReturnValue(mockMessage);

      await badgeWebSocketService.broadcastBadgeRemoved(payload);

      expect(mockWebSocketUtils.event).toHaveBeenCalledWith(
        EventTypes.BADGE_REMOVED,
        expect.objectContaining({
          type: "BADGE_REMOVED",
          userId: mockUserId,
          badgeTypeId: mockBadgeTypeId,
          badgeType: mockBadgeType,
          removedBy: mockAssignerId
        })
      );

      expect(mockWebSocketManager.broadcastToUser).toHaveBeenCalledWith(mockUserId, mockMessage);
      expect(mockWebSocketManager.broadcastToUser).toHaveBeenCalledWith(mockAssignerId, mockMessage);
    });

    test("should handle self-removal of badge", async () => {
      const payload: BadgeRemovedPayload = {
        userId: mockUserId,
        badgeTypeId: mockBadgeTypeId,
        badgeType: mockBadgeType,
        removedBy: mockUserId // Same user removing their own badge
      };

      const mockMessage = { type: "BADGE_REMOVED", data: {} };
      mockWebSocketUtils.event.mockReturnValue(mockMessage);

      await badgeWebSocketService.broadcastBadgeRemoved(payload);

      // Should only broadcast once to the user
      expect(mockWebSocketManager.broadcastToUser).toHaveBeenCalledTimes(1);
      expect(mockWebSocketManager.broadcastToUser).toHaveBeenCalledWith(mockUserId, mockMessage);
    });

    test("should handle errors gracefully during badge removed broadcast", async () => {
      const payload: BadgeRemovedPayload = {
        userId: mockUserId,
        badgeTypeId: mockBadgeTypeId,
        badgeType: mockBadgeType,
        removedBy: mockAssignerId
      };

      mockWebSocketManager.broadcastToUser.mockRejectedValue(new Error("WebSocket error"));

      // Should not throw error
      await expect(badgeWebSocketService.broadcastBadgeRemoved(payload)).resolves.toBeUndefined();
    });
  });

  describe("Badge Progress Update Events", () => {
    test("should broadcast badge progress update", async () => {
      const payload: BadgeProgressUpdatePayload = {
        userId: mockUserId,
        badgeTypeId: mockBadgeTypeId,
        badgeType: mockBadgeType,
        progress: 7,
        total: 10,
        progressPercentage: 70
      };

      const expectedEvent: BadgeProgressUpdateEvent = {
        type: "BADGE_PROGRESS_UPDATE",
        userId: mockUserId,
        badgeTypeId: mockBadgeTypeId,
        badgeType: mockBadgeType,
        progress: 7,
        total: 10,
        progressPercentage: 70,
        timestamp: expect.any(Date)
      };

      const mockMessage = { type: "BADGE_PROGRESS_UPDATE", data: expectedEvent };
      mockWebSocketUtils.event.mockReturnValue(mockMessage);

      await badgeWebSocketService.broadcastBadgeProgressUpdate(payload);

      expect(mockWebSocketUtils.event).toHaveBeenCalledWith(
        EventTypes.BADGE_PROGRESS_UPDATE,
        expect.objectContaining({
          type: "BADGE_PROGRESS_UPDATE",
          userId: mockUserId,
          badgeTypeId: mockBadgeTypeId,
          progress: 7,
          total: 10,
          progressPercentage: 70
        })
      );

      // Progress updates only go to the user themselves
      expect(mockWebSocketManager.broadcastToUser).toHaveBeenCalledTimes(1);
      expect(mockWebSocketManager.broadcastToUser).toHaveBeenCalledWith(mockUserId, mockMessage);
    });

    test("should handle errors gracefully during progress update broadcast", async () => {
      const payload: BadgeProgressUpdatePayload = {
        userId: mockUserId,
        badgeTypeId: mockBadgeTypeId,
        badgeType: mockBadgeType,
        progress: 5,
        total: 10,
        progressPercentage: 50
      };

      mockWebSocketManager.broadcastToUser.mockRejectedValue(new Error("WebSocket error"));

      // Should not throw error
      await expect(badgeWebSocketService.broadcastBadgeProgressUpdate(payload)).resolves.toBeUndefined();
    });
  });

  describe("Batch Operations", () => {
    test("should broadcast batch badge assignments", async () => {
      const assignments: BadgeAssignedPayload[] = [
        {
          userId: "user-1",
          badge: { ...mockUserBadge, userId: "user-1" },
          isAutomatic: true,
          assignedBy: undefined
        },
        {
          userId: "user-2",
          badge: { ...mockUserBadge, userId: "user-2" },
          isAutomatic: true,
          assignedBy: undefined
        },
        {
          userId: "user-3",
          badge: { ...mockUserBadge, userId: "user-3" },
          isAutomatic: false,
          assignedBy: mockAssignerId
        }
      ];

      const mockMessage = { type: "BADGE_ASSIGNED", data: {} };
      mockWebSocketUtils.event.mockReturnValue(mockMessage);

      // Mock the broadcastBadgeAssigned method to avoid actual WebSocket calls
      const broadcastSpy = spyOn(badgeWebSocketService, "broadcastBadgeAssigned");
      broadcastSpy.mockResolvedValue(undefined);

      await badgeWebSocketService.broadcastBatchBadgeAssigned(assignments);

      expect(broadcastSpy).toHaveBeenCalledTimes(3);
      expect(broadcastSpy).toHaveBeenCalledWith(assignments[0]);
      expect(broadcastSpy).toHaveBeenCalledWith(assignments[1]);
      expect(broadcastSpy).toHaveBeenCalledWith(assignments[2]);
    });

    test("should handle batch processing with delays", async () => {
      // Create a large batch to test batching logic
      const assignments: BadgeAssignedPayload[] = Array.from({ length: 25 }, (_, i) => ({
        userId: `user-${i}`,
        badge: { ...mockUserBadge, userId: `user-${i}` },
        isAutomatic: true,
        assignedBy: undefined
      }));

      const broadcastSpy = spyOn(badgeWebSocketService, "broadcastBadgeAssigned");
      broadcastSpy.mockResolvedValue(undefined);

      const startTime = Date.now();
      await badgeWebSocketService.broadcastBatchBadgeAssigned(assignments);
      const endTime = Date.now();

      expect(broadcastSpy).toHaveBeenCalledTimes(25);
      // Should have some delay due to batching (at least 200ms for 3 batches)
      expect(endTime - startTime).toBeGreaterThan(100);
    });

    test("should broadcast batch progress updates", async () => {
      const updates: BadgeProgressUpdatePayload[] = [
        {
          userId: "user-1",
          badgeTypeId: mockBadgeTypeId,
          badgeType: mockBadgeType,
          progress: 5,
          total: 10,
          progressPercentage: 50
        },
        {
          userId: "user-2",
          badgeTypeId: mockBadgeTypeId,
          badgeType: mockBadgeType,
          progress: 8,
          total: 10,
          progressPercentage: 80
        }
      ];

      const broadcastSpy = spyOn(badgeWebSocketService, "broadcastBadgeProgressUpdate");
      broadcastSpy.mockResolvedValue(undefined);

      await badgeWebSocketService.broadcastBatchProgressUpdates(updates);

      expect(broadcastSpy).toHaveBeenCalledTimes(2);
      expect(broadcastSpy).toHaveBeenCalledWith(updates[0]);
      expect(broadcastSpy).toHaveBeenCalledWith(updates[1]);
    });

    test("should handle errors in batch operations gracefully", async () => {
      const assignments: BadgeAssignedPayload[] = [
        {
          userId: "user-1",
          badge: { ...mockUserBadge, userId: "user-1" },
          isAutomatic: true,
          assignedBy: undefined
        }
      ];

      const broadcastSpy = spyOn(badgeWebSocketService, "broadcastBadgeAssigned");
      broadcastSpy.mockRejectedValue(new Error("Broadcast error"));

      // Should not throw error
      await expect(badgeWebSocketService.broadcastBatchBadgeAssigned(assignments)).resolves.toBeUndefined();
    });
  });

  describe("Badge Notifications", () => {
    test("should send badge notification to specific user", async () => {
      const notificationData = {
        badgeId: mockBadgeTypeId,
        badgeName: "Test Badge",
        message: "Congratulations on earning this badge!"
      };

      const mockMessage = { type: "BADGE_NOTIFICATION", data: {} };
      mockWebSocketUtils.event.mockReturnValue(mockMessage);

      await badgeWebSocketService.sendBadgeNotification(mockUserId, "achievement", notificationData);

      expect(mockWebSocketUtils.event).toHaveBeenCalledWith(
        "BADGE_NOTIFICATION",
        expect.objectContaining({
          type: "achievement",
          userId: mockUserId,
          data: notificationData,
          timestamp: expect.any(Date)
        })
      );

      expect(mockWebSocketManager.broadcastToUser).toHaveBeenCalledWith(mockUserId, mockMessage);
    });

    test("should handle different notification types", async () => {
      const notificationTypes = ["achievement", "milestone", "progress"] as const;
      
      for (const type of notificationTypes) {
        const mockMessage = { type: "BADGE_NOTIFICATION", data: {} };
        mockWebSocketUtils.event.mockReturnValue(mockMessage);

        await badgeWebSocketService.sendBadgeNotification(mockUserId, type, {});

        expect(mockWebSocketUtils.event).toHaveBeenCalledWith(
          "BADGE_NOTIFICATION",
          expect.objectContaining({
            type,
            userId: mockUserId
          })
        );
      }
    });

    test("should handle notification errors gracefully", async () => {
      mockWebSocketManager.broadcastToUser.mockRejectedValue(new Error("Notification error"));

      // Should not throw error
      await expect(
        badgeWebSocketService.sendBadgeNotification(mockUserId, "achievement", {})
      ).resolves.toBeUndefined();
    });
  });

  describe("Server Membership Broadcasting", () => {
    test("should handle server membership lookup errors", async () => {
      const payload: BadgeAssignedPayload = {
        userId: mockUserId,
        badge: mockUserBadge,
        isAutomatic: false,
        assignedBy: mockAssignerId
      };

      mockDbUtils.getUserServerMemberships.mockRejectedValue(new Error("Database error"));

      const mockMessage = { type: "BADGE_ASSIGNED", data: {} };
      mockWebSocketUtils.event.mockReturnValue(mockMessage);

      // Should not throw error even if server membership lookup fails
      await expect(badgeWebSocketService.broadcastBadgeAssigned(payload)).resolves.toBeUndefined();

      // Should still broadcast to the user directly
      expect(mockWebSocketManager.broadcastToUser).toHaveBeenCalledWith(mockUserId, mockMessage);
    });

    test("should handle empty server memberships", async () => {
      const payload: BadgeAssignedPayload = {
        userId: mockUserId,
        badge: mockUserBadge,
        isAutomatic: false,
        assignedBy: mockAssignerId
      };

      mockDbUtils.getUserServerMemberships.mockResolvedValue([]);

      const mockMessage = { type: "BADGE_ASSIGNED", data: {} };
      mockWebSocketUtils.event.mockReturnValue(mockMessage);

      await badgeWebSocketService.broadcastBadgeAssigned(payload);

      // Should not call broadcast for servers
      expect(mockWebSocketManager.broadcast).not.toHaveBeenCalled();
      // Should still broadcast to users
      expect(mockWebSocketManager.broadcastToUser).toHaveBeenCalledTimes(2);
    });
  });
});