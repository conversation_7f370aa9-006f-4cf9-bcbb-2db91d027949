import { describe, test, expect, beforeEach, mock } from "bun:test";
import { PerkService } from "../../services/perk.service";
import type { BadgeType } from "../../types/badge.types";

// Mock database
const mockDb = {
  select: mock(() => ({
    from: mock(() => ({
      where: mock(() => ({
        limit: mock(() => []),
        innerJoin: mock(() => ({
          where: mock(() => [])
        }))
      })),
      innerJoin: mock(() => ({
        where: mock(() => [])
      }))
    }))
  })),
  insert: mock(() => ({
    values: mock(() => ({
      returning: mock(() => [])
    }))
  })),
  update: mock(() => ({
    set: mock(() => ({
      where: mock(() => ({
        returning: mock(() => [])
      }))
    }))
  })),
  delete: mock(() => ({
    where: mock(() => ({}))
  }))
} as any;

// Mock badge WebSocket service
mock.module("../../utils/badge-websocket", () => ({
  badgeWebSocketService: {
    broadcastPerkAssigned: mock(() => Promise.resolve()),
    broadcastPerkRevoked: mock(() => Promise.resolve())
  }
}));

// Mock permissions
mock.module("../../utils/permissions", () => ({
  hasServerPermission: mock(() => Promise.resolve(true)),
  assignRoleToUser: mock(() => Promise.resolve(true)),
  removeRoleFromUser: mock(() => Promise.resolve(true)),
  getUserRoles: mock(() => Promise.resolve([]))
}));

describe("PerkService", () => {
  let perkService: PerkService;

  beforeEach(() => {
    perkService = new PerkService(mockDb);
  });

  describe("parseStringPerk", () => {
    test("should parse role assignment perk", () => {
      const perk = perkService['parseStringPerk']("role:Moderator", "test_id");
      
      expect(perk).toEqual({
        id: "test_id",
        type: "role_assignment",
        name: "Role: Moderator",
        description: "Assigns the Moderator role",
        value: "Moderator",
        isActive: true
      });
    });

    test("should parse permission grant perk", () => {
      const perk = perkService['parseStringPerk']("permission:MANAGE_MESSAGES", "test_id");
      
      expect(perk).toEqual({
        id: "test_id",
        type: "permission_grant",
        name: "Permission: MANAGE_MESSAGES",
        description: "Grants MANAGE_MESSAGES permission",
        permissions: ["MANAGE_MESSAGES"],
        isActive: true
      });
    });

    test("should parse cosmetic feature perk", () => {
      const perk = perkService['parseStringPerk']("cosmetic:special_badge_glow", "test_id");
      
      expect(perk).toEqual({
        id: "test_id",
        type: "cosmetic_feature",
        name: "Cosmetic: special_badge_glow",
        description: "Unlocks special_badge_glow cosmetic feature",
        value: "special_badge_glow",
        isActive: true
      });
    });

    test("should parse access privilege perk", () => {
      const perk = perkService['parseStringPerk']("access:vip_lounge", "test_id");
      
      expect(perk).toEqual({
        id: "test_id",
        type: "access_privilege",
        name: "Access: vip_lounge",
        description: "Grants access to vip_lounge",
        value: "vip_lounge",
        isActive: true
      });
    });

    test("should parse custom privilege perk", () => {
      const perk = perkService['parseStringPerk']("custom_feature", "test_id");
      
      expect(perk).toEqual({
        id: "test_id",
        type: "custom_privilege",
        name: "Custom: custom_feature",
        description: "Custom privilege: custom_feature",
        value: "custom_feature",
        isActive: true
      });
    });
  });

  describe("parseObjectPerk", () => {
    test("should parse object-based perk definition", () => {
      const perkObject = {
        id: "custom_id",
        type: "role_assignment",
        name: "VIP Role",
        description: "Assigns VIP role to user",
        value: "VIP",
        serverId: "server123",
        isActive: true,
        metadata: { priority: 1 }
      };

      const perk = perkService['parseObjectPerk'](perkObject, "fallback_id");
      
      expect(perk).toEqual(perkObject);
    });

    test("should use fallback values for missing properties", () => {
      const perkObject = {
        type: "cosmetic_feature"
      };

      const perk = perkService['parseObjectPerk'](perkObject, "fallback_id");
      
      expect(perk).toEqual({
        id: "fallback_id",
        type: "cosmetic_feature",
        name: "Unnamed Perk",
        description: "No description provided",
        value: undefined,
        serverId: undefined,
        roleId: undefined,
        permissions: undefined,
        isActive: true,
        metadata: undefined
      });
    });
  });

  describe("validatePerk", () => {
    test("should validate role assignment perk successfully", async () => {
      // Mock successful role lookup
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => [{ id: "role123", name: "Moderator" }])
        }))
      });

      const perk = {
        id: "test",
        type: "role_assignment" as const,
        name: "Test Role",
        description: "Test",
        value: "Moderator",
        isActive: true
      };

      const result = await perkService.validatePerk(perk, "user123", "server123");
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test("should fail validation for missing role", async () => {
      // Mock failed role lookup
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => [])
        }))
      });

      const perk = {
        id: "test",
        type: "role_assignment" as const,
        name: "Test Role",
        description: "Test",
        value: "NonexistentRole",
        isActive: true
      };

      const result = await perkService.validatePerk(perk, "user123", "server123");
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Role "NonexistentRole" not found in server');
    });

    test("should validate permission grant perk", async () => {
      const perk = {
        id: "test",
        type: "permission_grant" as const,
        name: "Test Permission",
        description: "Test",
        permissions: ["MANAGE_MESSAGES"],
        isActive: true
      };

      const result = await perkService.validatePerk(perk, "user123", "server123");
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test("should fail validation for permission grant without permissions", async () => {
      const perk = {
        id: "test",
        type: "permission_grant" as const,
        name: "Test Permission",
        description: "Test",
        isActive: true
      };

      const result = await perkService.validatePerk(perk, "user123", "server123");
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Permission grant perk requires permissions array");
    });
  });

  describe("assignPerksForBadge", () => {
    test("should assign perks for badge with string perks", async () => {
      const badgeType: BadgeType = {
        id: "badge123",
        badgeId: "test_badge",
        name: "Test Badge",
        description: "Test badge",
        icon: "🏆",
        design: { shape: "circle", background: "gold", colors: ["#FFD700"] },
        criteria: { requirement: "test", tracked: "manual" },
        perks: ["role:Moderator", "cosmetic:special_glow"],
        unlockType: "manual",
        displayOrder: 1,
        category: "achievement",
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Mock successful role assignment
      const { assignRoleToUser } = await import("../../utils/permissions");
      (assignRoleToUser as any).mockResolvedValue(true);

      const results = await perkService.assignPerksForBadge("user123", badgeType, "server123");
      
      expect(results).toHaveLength(2);
      expect(results[0].success).toBe(true);
      expect(results[0].perk?.type).toBe("role_assignment");
      expect(results[1].success).toBe(true);
      expect(results[1].perk?.type).toBe("cosmetic_feature");
    });

    test("should handle perk assignment failures gracefully", async () => {
      const badgeType: BadgeType = {
        id: "badge123",
        badgeId: "test_badge",
        name: "Test Badge",
        description: "Test badge",
        icon: "🏆",
        design: { shape: "circle", background: "gold", colors: ["#FFD700"] },
        criteria: { requirement: "test", tracked: "manual" },
        perks: ["role:NonexistentRole"],
        unlockType: "manual",
        displayOrder: 1,
        category: "achievement",
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Mock failed role lookup
      mockDb.select.mockReturnValue({
        from: mock(() => ({
          where: mock(() => [])
        }))
      });

      const results = await perkService.assignPerksForBadge("user123", badgeType, "server123");
      
      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(false);
      expect(results[0].error).toContain("Validation failed");
    });
  });

  describe("revokePerksForBadge", () => {
    test("should revoke perks for badge", async () => {
      const badgeType: BadgeType = {
        id: "badge123",
        badgeId: "test_badge",
        name: "Test Badge",
        description: "Test badge",
        icon: "🏆",
        design: { shape: "circle", background: "gold", colors: ["#FFD700"] },
        criteria: { requirement: "test", tracked: "manual" },
        perks: ["role:Moderator"],
        unlockType: "manual",
        displayOrder: 1,
        category: "achievement",
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Mock successful role removal
      const { removeRoleFromUser } = await import("../../utils/permissions");
      (removeRoleFromUser as any).mockResolvedValue(true);

      // Mock no other badges with same role
      mockDb.select.mockReturnValue({
        from: mock(() => ({
          innerJoin: mock(() => ({
            where: mock(() => [])
          }))
        }))
      });

      const results = await perkService.revokePerksForBadge("user123", badgeType, "server123");
      
      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(true);
    });
  });

  describe("getUserPerks", () => {
    test("should get all user perks", async () => {
      const mockUserBadges = [
        {
          userBadge: { id: "ub1", userId: "user123", badgeTypeId: "bt1" },
          badgeType: {
            id: "bt1",
            perks: ["role:Moderator", "cosmetic:glow"],
            name: "Test Badge"
          }
        }
      ];

      mockDb.select.mockReturnValue({
        from: mock(() => ({
          innerJoin: mock(() => ({
            where: mock(() => mockUserBadges)
          }))
        }))
      });

      const perks = await perkService.getUserPerks("user123", "server123");
      
      expect(perks).toHaveLength(2);
      expect(perks[0].type).toBe("role_assignment");
      expect(perks[1].type).toBe("cosmetic_feature");
    });
  });

  describe("getAvailablePerks", () => {
    test("should get all available perks", async () => {
      const mockBadgeTypes = [
        {
          id: "bt1",
          perks: ["role:VIP", "access:lounge"],
          name: "VIP Badge",
          isActive: true
        },
        {
          id: "bt2", 
          perks: ["cosmetic:sparkle"],
          name: "Sparkle Badge",
          isActive: true
        }
      ];

      mockDb.select.mockReturnValue({
        from: mock(() => ({
          where: mock(() => mockBadgeTypes)
        }))
      });

      const perks = await perkService.getAvailablePerks("server123");
      
      expect(perks).toHaveLength(3);
      
      const perkTypes = perks.map(p => p.type);
      expect(perkTypes).toContain("role_assignment");
      expect(perkTypes).toContain("access_privilege");
      expect(perkTypes).toContain("cosmetic_feature");
    });

    test("should remove duplicate perks", async () => {
      const mockBadgeTypes = [
        {
          id: "bt1",
          perks: ["role:VIP"],
          name: "VIP Badge 1",
          isActive: true
        },
        {
          id: "bt2",
          perks: ["role:VIP"], // Same perk as above
          name: "VIP Badge 2",
          isActive: true
        }
      ];

      mockDb.select.mockReturnValue({
        from: mock(() => ({
          where: mock(() => mockBadgeTypes)
        }))
      });

      const perks = await perkService.getAvailablePerks("server123");
      
      // Should only have one VIP role perk despite two badges having it
      expect(perks).toHaveLength(1);
      expect(perks[0].type).toBe("role_assignment");
      expect(perks[0].value).toBe("VIP");
    });
  });
});