import { describe, it, expect, mock } from "bun:test";
import { WebSocketUtils } from "../../utils/websocket-utils";
import { WebSocketErrorCode } from "../../types/websocket-standardization.types";
import type { ServerWebSocket } from "bun";
import type { CustomWebSocketData } from "../../types/websocket.types";

// Mock WebSocket
const createMockWebSocket = (
  userId: string = "test-user",
  readyState: number = WebSocket.OPEN,
): ServerWebSocket<CustomWebSocketData> => {
  const mockWs = {
    data: {
      userId,
      token: "test-token",
      isAlive: true,
    },
    send: mock(() => {}),
    readyState,
  } as any;

  return mockWs;
};

// Mock WebSocketManager
const createMockManager = () => {
  return {
    broadcastToUser: mock(() => {}),
    broadcast: mock(() => {}),
  } as any;
};

describe("WebSocketUtils", () => {
  describe("success", () => {
    it("should create a standardized success message", () => {
      const data = { test: "data" };
      const message = WebSocketUtils.success("TEST_TYPE", data);

      expect(message.success).toBe(true);
      expect(message.type).toBe("TEST_TYPE");
      expect(message.data).toEqual(data);
      expect(message.meta).toBeDefined();
      expect(message.meta.id).toBeDefined();
      expect(message.meta.timestamp).toBeDefined();
      expect(message.meta.version).toBe("1.0.0");
      expect(message.meta.source).toBe("server");
    });

    it("should include correlation ID when provided", () => {
      const correlationId = "test-correlation-id";
      const message = WebSocketUtils.success(
        "TEST_TYPE",
        {},
        { correlationId },
      );

      expect(message.meta.correlationId).toBe(correlationId);
    });

    it("should include target when provided", () => {
      const target = { userId: "test-user" };
      const message = WebSocketUtils.success("TEST_TYPE", {}, { target });

      expect(message.target).toEqual(target);
    });

    it("should include custom message when provided", () => {
      const customMessage = "Operation completed successfully";
      const message = WebSocketUtils.success(
        "TEST_TYPE",
        {},
        { message: customMessage },
      );

      expect(message.message).toBe(customMessage);
    });
  });

  describe("error", () => {
    it("should create a standardized error message", () => {
      const code = WebSocketErrorCode.INVALID_DATA;
      const errorMessage = "Invalid data provided";
      const message = WebSocketUtils.error(code, errorMessage);

      expect(message.success).toBe(false);
      expect(message.type).toBe("ERROR");
      expect(message.error.code).toBe(code);
      expect(message.error.message).toBe(errorMessage);
      expect(message.meta).toBeDefined();
      expect(message.meta.id).toBeDefined();
      expect(message.meta.timestamp).toBeDefined();
      expect(message.meta.version).toBe("1.0.0");
      expect(message.meta.source).toBe("server");
    });

    it("should include correlation ID when provided", () => {
      const correlationId = "test-correlation-id";
      const message = WebSocketUtils.error(
        WebSocketErrorCode.INVALID_DATA,
        "Test error",
        { correlationId },
      );

      expect(message.meta.correlationId).toBe(correlationId);
    });

    it("should include details when provided", () => {
      const details = { field: "username", expectedFormat: "string" };
      const message = WebSocketUtils.error(
        WebSocketErrorCode.INVALID_DATA,
        "Test error",
        { details },
      );

      expect(message.error.details).toEqual(details);
    });
  });

  describe("event", () => {
    it("should create a standardized event message", () => {
      const event = "USER_JOINED";
      const data = { userId: "test-user" };
      const message = WebSocketUtils.event(event, data);

      expect(message.type).toBe("EVENT");
      expect(message.event).toBe(event);
      expect(message.data).toEqual(data);
      expect(message.severity).toBe("info");
      expect(message.meta).toBeDefined();
      expect(message.meta.id).toBeDefined();
      expect(message.meta.timestamp).toBeDefined();
      expect(message.meta.version).toBe("1.0.0");
      expect(message.meta.source).toBe("server");
    });

    it("should include target when provided", () => {
      const target = { channelId: "test-channel" };
      const message = WebSocketUtils.event("TEST_EVENT", {}, { target });

      expect(message.target).toEqual(target);
    });

    it("should include category and severity when provided", () => {
      const options = {
        category: "user",
        severity: "warning" as const,
      };
      const message = WebSocketUtils.event("TEST_EVENT", {}, options);

      expect(message.category).toBe(options.category);
      expect(message.severity).toBe(options.severity);
    });
  });

  describe("send", () => {
    it("should send message to open WebSocket", () => {
      const ws = createMockWebSocket();
      const message = WebSocketUtils.success("TEST_TYPE", {});

      WebSocketUtils.send(ws, message);

      expect(ws.send).toHaveBeenCalledTimes(1);
      expect(ws.send).toHaveBeenCalledWith(JSON.stringify(message));
    });

    it("should not send message to closed WebSocket", () => {
      const ws = createMockWebSocket("test-user", WebSocket.CLOSED);
      const message = WebSocketUtils.success("TEST_TYPE", {});

      WebSocketUtils.send(ws, message);

      expect(ws.send).not.toHaveBeenCalled();
    });
  });

  describe("broadcast", () => {
    it("should broadcast message to all open WebSockets", () => {
      const ws1 = createMockWebSocket("user1");
      const ws2 = createMockWebSocket("user2");
      const sockets = new Set([ws1, ws2]);
      const message = WebSocketUtils.success("TEST_TYPE", {});

      WebSocketUtils.broadcast(sockets, message);

      expect(ws1.send).toHaveBeenCalledTimes(1);
      expect(ws2.send).toHaveBeenCalledTimes(1);
    });

    it("should exclude specified user from broadcast", () => {
      const ws1 = createMockWebSocket("user1");
      const ws2 = createMockWebSocket("user2");
      const sockets = new Set([ws1, ws2]);
      const message = WebSocketUtils.success("TEST_TYPE", {});

      WebSocketUtils.broadcast(sockets, message, { excludeUserId: "user1" });

      expect(ws1.send).not.toHaveBeenCalled();
      expect(ws2.send).toHaveBeenCalledTimes(1);
    });

    it("should apply custom filter when provided", () => {
      const ws1 = createMockWebSocket("user1");
      const ws2 = createMockWebSocket("user2");
      const sockets = new Set([ws1, ws2]);
      const message = WebSocketUtils.success("TEST_TYPE", {});

      WebSocketUtils.broadcast(sockets, message, {
        filter: (ws) => ws.data.userId === "user2",
      });

      expect(ws1.send).not.toHaveBeenCalled();
      expect(ws2.send).toHaveBeenCalledTimes(1);
    });

    it("should skip closed WebSockets", () => {
      const ws1 = createMockWebSocket("user1", WebSocket.CLOSED);
      const ws2 = createMockWebSocket("user2");
      const sockets = new Set([ws1, ws2]);
      const message = WebSocketUtils.success("TEST_TYPE", {});

      WebSocketUtils.broadcast(sockets, message);

      expect(ws1.send).not.toHaveBeenCalled();
      expect(ws2.send).toHaveBeenCalledTimes(1);
    });
  });

  describe("sendToUser", () => {
    it("should send message to specific user", () => {
      const manager = createMockManager();
      const message = WebSocketUtils.success("TEST_TYPE", {});
      const userId = "test-user";

      const result = WebSocketUtils.sendToUser(userId, message, manager);

      expect(result).toBe(true);
      expect(manager.broadcastToUser).toHaveBeenCalledTimes(1);
      expect(manager.broadcastToUser).toHaveBeenCalledWith(
        userId,
        JSON.stringify(message),
      );
    });

    it("should return false for undefined userId", () => {
      const manager = createMockManager();
      const message = WebSocketUtils.success("TEST_TYPE", {});

      const result = WebSocketUtils.sendToUser("", message, manager);

      expect(result).toBe(false);
      expect(manager.broadcastToUser).not.toHaveBeenCalled();
    });
  });

  describe("sendToChannel", () => {
    it("should send message to channel", () => {
      const manager = createMockManager();
      const message = WebSocketUtils.success("TEST_TYPE", {});
      const channelId = "test-channel";

      WebSocketUtils.sendToChannel(channelId, message, manager);

      expect(manager.broadcast).toHaveBeenCalledTimes(1);
      expect(manager.broadcast).toHaveBeenCalledWith(
        JSON.stringify(message),
        undefined,
        channelId,
        undefined,
      );
    });

    it("should include serverId and excludeUserId when provided", () => {
      const manager = createMockManager();
      const message = WebSocketUtils.success("TEST_TYPE", {});
      const channelId = "test-channel";
      const options = {
        serverId: "test-server",
        excludeUserId: "test-user",
      };

      WebSocketUtils.sendToChannel(channelId, message, manager, options);

      expect(manager.broadcast).toHaveBeenCalledWith(
        JSON.stringify(message),
        options.serverId,
        channelId,
        options.excludeUserId,
      );
    });

    it("should not send for undefined channelId", () => {
      const manager = createMockManager();
      const message = WebSocketUtils.success("TEST_TYPE", {});

      WebSocketUtils.sendToChannel("", message, manager);

      expect(manager.broadcast).not.toHaveBeenCalled();
    });
  });

  describe("serialize", () => {
    it("should serialize valid message to JSON", () => {
      const message = WebSocketUtils.success("TEST_TYPE", { test: "data" });
      const serialized = WebSocketUtils.serialize(message);

      expect(typeof serialized).toBe("string");
      expect(JSON.parse(serialized)).toEqual(message);
    });

    it("should throw error for message without metadata", () => {
      const invalidMessage = { type: "TEST" } as any;

      expect(() => WebSocketUtils.serialize(invalidMessage)).toThrow();
    });
  });

  describe("validate", () => {
    it("should validate correct message structure", () => {
      const message = WebSocketUtils.success("TEST_TYPE", {});
      const isValid = WebSocketUtils.validate(message);

      expect(isValid).toBe(true);
    });

    it("should reject message without type", () => {
      const message = {
        meta: { messageId: "test", timestamp: "test", version: "1.0.0" },
      };
      const isValid = WebSocketUtils.validate(message);

      expect(isValid).toBe(false);
    });

    it("should reject message without meta", () => {
      const message = { type: "TEST" };
      const isValid = WebSocketUtils.validate(message);

      expect(isValid).toBe(false);
    });

    it("should reject non-object input", () => {
      expect(WebSocketUtils.validate(null)).toBe(false);
      expect(WebSocketUtils.validate("string")).toBe(false);
      expect(WebSocketUtils.validate(123)).toBe(false);
    });
  });

  describe("Common error factory methods", () => {
    it("should create authentication required error", () => {
      const message = WebSocketUtils.authenticationRequired();

      expect(message.success).toBe(false);
      expect(message.error.code).toBe(WebSocketErrorCode.AUTH_REQUIRED);
      expect(message.error.message).toBe(
        "Authentication is required for this operation",
      );
    });

    it("should create permission denied error", () => {
      const permission = "MANAGE_CHANNELS";
      const message = WebSocketUtils.permissionDenied(permission);

      expect(message.success).toBe(false);
      expect(message.error.code).toBe(WebSocketErrorCode.PERMISSION_DENIED);
      expect(message.error.message).toBe(
        `Permission denied: ${permission} required`,
      );
      expect(message.error.details).toEqual({ permission });
    });

    it("should create not found error", () => {
      const resource = "Channel";
      const message = WebSocketUtils.notFound(resource);

      expect(message.success).toBe(false);
      expect(message.error.code).toBe(WebSocketErrorCode.RESOURCE_NOT_FOUND);
      expect(message.error.message).toBe(`${resource} not found`);
      expect(message.error.details).toEqual({ resource });
    });

    it("should create validation error", () => {
      const errors = [
        { field: "username", message: "Required", code: "REQUIRED" },
        { field: "email", message: "Invalid format", code: "INVALID_FORMAT" },
      ];
      const message = WebSocketUtils.validationError(errors);

      expect(message.success).toBe(false);
      expect(message.error.code).toBe(
        WebSocketErrorCode.SCHEMA_VALIDATION_FAILED,
      );
      expect(message.error.message).toBe("Validation failed");
      expect(message.error.details).toEqual({ validationErrors: errors });
    });

    it("should create rate limited error", () => {
      const retryAfter = 60;
      const message = WebSocketUtils.rateLimited(retryAfter);

      expect(message.success).toBe(false);
      expect(message.error.code).toBe(WebSocketErrorCode.RATE_LIMITED);
      expect(message.error.message).toBe("Rate limit exceeded");
      expect(message.error.details).toEqual({ retryAfter });
    });

    it("should create internal error", () => {
      const customMessage = "Database connection failed";
      const message = WebSocketUtils.internalError(customMessage);

      expect(message.success).toBe(false);
      expect(message.error.code).toBe(WebSocketErrorCode.INTERNAL_ERROR);
      expect(message.error.message).toBe(customMessage);
    });

    it("should create message too large error", () => {
      const currentSize = 2048;
      const maxSize = 1024;
      const message = WebSocketUtils.messageTooLarge(currentSize, maxSize);

      expect(message.success).toBe(false);
      expect(message.error.code).toBe(WebSocketErrorCode.MESSAGE_TOO_LARGE);
      expect(message.error.message).toBe(
        "Message size exceeds maximum allowed",
      );
      expect(message.error.details).toEqual({ currentSize, maxSize });
    });

    it("should create channel not found error", () => {
      const channelId = "channel-123";
      const message = WebSocketUtils.channelNotFound(channelId);

      expect(message.success).toBe(false);
      expect(message.error.code).toBe(WebSocketErrorCode.CHANNEL_NOT_FOUND);
      expect(message.error.message).toBe("Channel not found");
      expect(message.error.details).toEqual({ channelId });
    });

    it("should create server not found error", () => {
      const serverId = "server-123";
      const message = WebSocketUtils.serverNotFound(serverId);

      expect(message.success).toBe(false);
      expect(message.error.code).toBe(WebSocketErrorCode.SERVER_NOT_FOUND);
      expect(message.error.message).toBe("Server not found");
      expect(message.error.details).toEqual({ serverId });
    });

    it("should create user not found error", () => {
      const userId = "user-123";
      const message = WebSocketUtils.userNotFound(userId);

      expect(message.success).toBe(false);
      expect(message.error.code).toBe(WebSocketErrorCode.USER_NOT_FOUND);
      expect(message.error.message).toBe("User not found");
      expect(message.error.details).toEqual({ userId });
    });
  });
});
