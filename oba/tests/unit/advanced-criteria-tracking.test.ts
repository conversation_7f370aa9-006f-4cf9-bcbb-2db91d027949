import { describe, it, expect, beforeEach, mock } from "bun:test";
import { AdvancedCriteriaEngine } from "../../services/advanced-criteria-engine.service";
import { TimeBasedTrackingService } from "../../services/time-based-tracking.service";
import { GeographicDemographicTrackingService } from "../../services/geographic-demographic-tracking.service";
import { InvitationReferralTrackingService } from "../../services/invitation-referral-tracking.service";
import { FeedbackContributionTrackingService } from "../../services/feedback-contribution-tracking.service";
import { AdvancedBadgeTrackingService } from "../../services/advanced-badge-tracking.service";

// Mock database
const mockDb = {
  select: mock(() => ({
    from: mock(() => ({
      where: mock(() => ({
        limit: mock(() => Promise.resolve([])),
        groupBy: mock(() => Promise.resolve([])),
        orderBy: mock(() => Promise.resolve([]))
      })),
      limit: mock(() => Promise.resolve([])),
      groupBy: mock(() => Promise.resolve([])),
      orderBy: mock(() => Promise.resolve([]))
    })),
    from: mock(() => ({
      where: mock(() => Promise.resolve([])),
      limit: mock(() => Promise.resolve([])),
      groupBy: mock(() => Promise.resolve([])),
      orderBy: mock(() => Promise.resolve([]))
    }))
  }))
} as any;

describe("Advanced Criteria Engine", () => {
  let criteriaEngine: AdvancedCriteriaEngine;

  beforeEach(() => {
    criteriaEngine = new AdvancedCriteriaEngine(mockDb);
  });

  describe("Multi-condition criteria evaluation", () => {
    it("should evaluate AND conditions correctly", async () => {
      const criteria = {
        type: 'multi_condition' as const,
        description: 'Test AND criteria',
        operator: 'AND' as const,
        conditions: [
          { type: 'message_count' as const, threshold: 100, operator: 'gte' as const },
          { type: 'friend_count' as const, threshold: 10, operator: 'gte' as const }
        ]
      };

      // Mock user stats that meet criteria
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => ({
            limit: mock(() => Promise.resolve([{
              createdAt: new Date('2023-01-01'),
              lastActive: new Date()
            }]))
          }))
        }))
      });

      // Mock message count
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{ count: 150 }]))
        }))
      });

      // Mock server count
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{ count: 5 }]))
        }))
      });

      // Mock friend count
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{ count: 15 }]))
        }))
      });

      const result = await criteriaEngine.evaluateComplexCriteria('user123', criteria);

      expect(result.userId).toBe('user123');
      expect(result.meetsCriteria).toBe(true);
      expect(result.evaluatedConditions).toHaveLength(2);
      expect(result.progress).toBeGreaterThan(0);
    });

    it("should evaluate OR conditions correctly", async () => {
      const criteria = {
        type: 'multi_condition' as const,
        description: 'Test OR criteria',
        operator: 'OR' as const,
        conditions: [
          { type: 'message_count' as const, threshold: 1000, operator: 'gte' as const },
          { type: 'friend_count' as const, threshold: 5, operator: 'gte' as const }
        ]
      };

      // Mock user stats where only one condition is met
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => ({
            limit: mock(() => Promise.resolve([{
              createdAt: new Date('2023-01-01'),
              lastActive: new Date()
            }]))
          }))
        }))
      });

      // Mock message count (doesn't meet threshold)
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{ count: 50 }]))
        }))
      });

      // Mock server count
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{ count: 3 }]))
        }))
      });

      // Mock friend count (meets threshold)
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{ count: 10 }]))
        }))
      });

      const result = await criteriaEngine.evaluateComplexCriteria('user123', criteria);

      expect(result.meetsCriteria).toBe(true); // OR condition - one met is enough
      expect(result.evaluatedConditions).toHaveLength(2);
    });

    it("should handle weighted conditions", async () => {
      const criteria = {
        type: 'multi_condition' as const,
        description: 'Test weighted criteria',
        operator: 'WEIGHTED' as const,
        threshold: 0.7,
        conditions: [
          { type: 'message_count' as const, threshold: 100, operator: 'gte' as const, weight: 0.6 },
          { type: 'friend_count' as const, threshold: 10, operator: 'gte' as const, weight: 0.4 }
        ]
      };

      // Mock user stats
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => ({
            limit: mock(() => Promise.resolve([{
              createdAt: new Date('2023-01-01'),
              lastActive: new Date()
            }]))
          }))
        }))
      });

      // Mock message count (meets threshold)
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{ count: 150 }]))
        }))
      });

      // Mock server count
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{ count: 2 }]))
        }))
      });

      // Mock friend count (doesn't meet threshold)
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{ count: 5 }]))
        }))
      });

      const result = await criteriaEngine.evaluateComplexCriteria('user123', criteria);

      expect(result.evaluatedConditions).toHaveLength(2);
      expect(result.details).toHaveProperty('operator', 'WEIGHTED');
    });
  });

  describe("Time-based criteria evaluation", () => {
    it("should evaluate time-based criteria correctly", async () => {
      const criteria = {
        type: 'time_based' as const,
        description: 'Test time-based criteria',
        timeWindow: '30d',
        conditions: [
          { metric: 'messages_per_day' as const, threshold: 5 },
          { metric: 'active_days' as const, threshold: 20 }
        ],
        minimumConditions: 1
      };

      // Mock user stats
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => ({
            limit: mock(() => Promise.resolve([{
              createdAt: new Date('2023-01-01'),
              lastActive: new Date()
            }]))
          }))
        }))
      });

      const result = await criteriaEngine.evaluateComplexCriteria('user123', criteria);

      expect(result.userId).toBe('user123');
      expect(result.details).toHaveProperty('timeWindow', '30d');
    });
  });

  describe("Error handling", () => {
    it("should handle database errors gracefully", async () => {
      const criteria = {
        type: 'multi_condition' as const,
        description: 'Test error handling',
        operator: 'AND' as const,
        conditions: [
          { type: 'message_count' as const, threshold: 100, operator: 'gte' as const }
        ]
      };

      // Mock database error
      mockDb.select.mockImplementationOnce(() => {
        throw new Error("Database connection failed");
      });

      const result = await criteriaEngine.evaluateComplexCriteria('user123', criteria);

      expect(result.meetsCriteria).toBe(false);
      expect(result.details).toHaveProperty('error');
    });

    it("should handle invalid criteria types", async () => {
      const criteria = {
        type: 'invalid_type' as any,
        description: 'Invalid criteria type'
      };

      const result = await criteriaEngine.evaluateComplexCriteria('user123', criteria);

      expect(result.meetsCriteria).toBe(false);
      expect(result.details).toHaveProperty('error');
    });
  });
});

describe("Time-Based Tracking Service", () => {
  let timeTracker: TimeBasedTrackingService;

  beforeEach(() => {
    timeTracker = new TimeBasedTrackingService(mockDb);
  });

  describe("Engagement metrics tracking", () => {
    it("should track engagement metrics for a time window", async () => {
      const timeWindow = { value: 7, unit: 'days' as const, label: 'Last Week' };

      // Mock user data
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => ({
            limit: mock(() => Promise.resolve([{
              createdAt: new Date('2023-01-01'),
              lastActive: new Date()
            }]))
          }))
        }))
      });

      // Mock message metrics
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{
            totalMessages: 50,
            avgLength: 75,
            maxLength: 200,
            minLength: 10
          }]))
        }))
      });

      // Mock friendship metrics
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{ count: 2 }]))
        }))
      });

      // Mock direct message metrics
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{ count: 10 }]))
        }))
      });

      // Mock daily activity
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => ({
            groupBy: mock(() => Promise.resolve([
              { date: '2023-12-01', count: 5 },
              { date: '2023-12-02', count: 8 },
              { date: '2023-12-03', count: 3 }
            ]))
          }))
        }))
      });

      // Mock weekly activity
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => ({
            groupBy: mock(() => Promise.resolve([
              { week: '2023-12-01', count: 20 },
              { week: '2023-12-08', count: 30 }
            ]))
          }))
        }))
      });

      const result = await timeTracker.trackEngagementMetrics('user123', timeWindow);

      expect(result.userId).toBe('user123');
      expect(result.timeWindow).toEqual(timeWindow);
      expect(result.messageMetrics).toBeDefined();
      expect(result.socialMetrics).toBeDefined();
      expect(result.activityPatterns).toBeDefined();
      expect(result.consistencyMetrics).toBeDefined();
      expect(result.overallScore).toBeGreaterThanOrEqual(0);
      expect(result.overallScore).toBeLessThanOrEqual(1);
    });

    it("should calculate activity streaks correctly", async () => {
      // Mock daily activity data
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => ({
            groupBy: mock(() => ({
              orderBy: mock(() => Promise.resolve([
                { date: '2023-12-05', messageCount: 10 },
                { date: '2023-12-04', messageCount: 5 },
                { date: '2023-12-03', messageCount: 8 },
                { date: '2023-12-01', messageCount: 3 } // Gap on 12-02
              ]))
            }))
          }))
        }))
      });

      const result = await timeTracker.getActivityStreaks('user123', 30);

      expect(result.userId).toBe('user123');
      expect(result.currentStreak).toBeGreaterThanOrEqual(0);
      expect(result.longestStreak).toBeGreaterThanOrEqual(0);
      expect(result.totalActiveDays).toBe(4);
      expect(result.streakHistory).toBeDefined();
    });
  });

  describe("Activity pattern analysis", () => {
    it("should analyze activity patterns correctly", async () => {
      const timeWindow = { value: 30, unit: 'days' as const, label: 'Last Month' };

      // Mock hourly activity
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => ({
            groupBy: mock(() => ({
              orderBy: mock(() => Promise.resolve([
                { hour: 9, count: 15, avgLength: 50 },
                { hour: 14, count: 25, avgLength: 75 },
                { hour: 19, count: 20, avgLength: 60 }
              ]))
            }))
          }))
        }))
      });

      // Mock daily activity
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => ({
            groupBy: mock(() => ({
              orderBy: mock(() => Promise.resolve([
                { dayOfWeek: 1, count: 50, avgLength: 65 }, // Monday
                { dayOfWeek: 2, count: 45, avgLength: 70 }, // Tuesday
                { dayOfWeek: 6, count: 30, avgLength: 55 }  // Saturday
              ]))
            }))
          }))
        }))
      });

      const result = await timeTracker.trackActivityPatterns('user123', timeWindow);

      expect(result.userId).toBe('user123');
      expect(result.timeWindow).toEqual(timeWindow);
      expect(result.hourlyDistribution).toHaveLength(3);
      expect(result.dailyDistribution).toHaveLength(3);
      expect(result.peakActivityHours).toBeDefined();
      expect(result.peakActivityDays).toBeDefined();
      expect(result.activityPattern).toBeDefined();
    });
  });
});

describe("Geographic Demographic Tracking Service", () => {
  let geoTracker: GeographicDemographicTrackingService;

  beforeEach(() => {
    geoTracker = new GeographicDemographicTrackingService(mockDb);
  });

  describe("Geographic profile tracking", () => {
    it("should track user geographic profile", async () => {
      // Mock user data
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => ({
            limit: mock(() => Promise.resolve([{
              id: 'user123',
              username: 'testuser',
              createdAt: new Date('2023-01-01'),
              lastActive: new Date()
            }]))
          }))
        }))
      });

      // Mock hourly activity for timezone detection
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => ({
            groupBy: mock(() => Promise.resolve([
              { hour: 14, count: 20, avgLength: 50 },
              { hour: 15, count: 25, avgLength: 60 },
              { hour: 16, count: 18, avgLength: 55 }
            ]))
          }))
        }))
      });

      // Mock message stats for language patterns
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{
            totalMessages: 100,
            avgLength: 65,
            totalLength: 6500
          }]))
        }))
      });

      const result = await geoTracker.trackUserGeographicProfile('user123');

      expect(result.userId).toBe('user123');
      expect(result.username).toBe('testuser');
      expect(result.primaryRegion).toBeDefined();
      expect(result.regionalActivity).toBeDefined();
      expect(result.timeZoneActivity).toBeDefined();
      expect(result.languagePatterns).toBeDefined();
      expect(result.regionalConnections).toBeDefined();
      expect(result.demographicInsights).toBeDefined();
    });

    it("should determine regional badge eligibility", async () => {
      const regionalBadges = [
        {
          id: 'north_america_pioneer',
          name: 'North America Pioneer',
          description: 'Active contributor in North America',
          requirements: {
            region: 'North America',
            minimumActivity: 50,
            communityConnections: 10,
            accountAge: 90
          },
          category: 'regional'
        }
      ];

      // Mock geographic profile
      const mockProfile = {
        userId: 'user123',
        username: 'testuser',
        primaryRegion: 'North America',
        regionalActivity: [
          { region: 'North America', activityScore: 75, messageCount: 100, averageLength: 50, peakHours: [14, 15, 16] }
        ],
        timeZoneActivity: {
          activityByHour: [{ hour: 14, messageCount: 25 }],
          peakActivityHours: [14, 15, 16],
          likelyTimezone: 'America/New_York',
          confidence: 0.8
        },
        languagePatterns: [
          { language: 'en', confidence: 0.9, messageCount: 100, averageLength: 50, characteristics: ['balanced'] }
        ],
        regionalConnections: [
          { region: 'North America', connectionCount: 15, strength: 0.8, lastInteraction: new Date() }
        ],
        demographicInsights: {
          accountAge: 120,
          activityLevel: 'high' as const,
          regionalInfluence: 75,
          culturalBridge: false,
          communityRole: 'active_member'
        },
        lastUpdated: new Date()
      };

      // Mock the trackUserGeographicProfile method
      geoTracker.trackUserGeographicProfile = mock(() => Promise.resolve(mockProfile));

      const result = await geoTracker.getRegionalBadgeEligibility('user123', regionalBadges);

      expect(result).toHaveLength(1);
      expect(result[0].badgeId).toBe('north_america_pioneer');
      expect(result[0].isEligible).toBe(true);
      expect(result[0].progress).toBeGreaterThan(0);
    });
  });
});

describe("Invitation Referral Tracking Service", () => {
  let invitationTracker: InvitationReferralTrackingService;

  beforeEach(() => {
    invitationTracker = new InvitationReferralTrackingService(mockDb);
  });

  describe("Invitation metrics tracking", () => {
    it("should track user invitation metrics", async () => {
      // Mock invitation stats
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{
            totalInvites: 25,
            totalUses: 18,
            maxUses: 125,
            activeInvites: 7
          }]))
        }))
      });

      // Mock invites for referral tracking
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([
            { id: 'invite1', serverId: 'server1', uses: 3, createdAt: new Date() },
            { id: 'invite2', serverId: 'server2', uses: 2, createdAt: new Date() }
          ]))
        }))
      });

      const result = await invitationTracker.trackUserInvitationMetrics('user123');

      expect(result.userId).toBe('user123');
      expect(result.invitationStats).toBeDefined();
      expect(result.referralStats).toBeDefined();
      expect(result.networkGrowth).toBeDefined();
      expect(result.invitationQuality).toBeDefined();
      expect(result.communityImpact).toBeDefined();
      expect(result.overallScore).toBeGreaterThanOrEqual(0);
      expect(result.overallScore).toBeLessThanOrEqual(100);
      expect(result.achievements).toBeDefined();
    });

    it("should track referral chains", async () => {
      // Mock direct referrals
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([
            { id: 'invite1', serverId: 'server1', uses: 2, createdAt: new Date() }
          ]))
        }))
      });

      const result = await invitationTracker.trackReferralChains('user123', 2);

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe("Community building impact", () => {
    it("should track community building impact", async () => {
      const result = await invitationTracker.trackCommunityBuildingImpact('user123');

      expect(result.userId).toBe('user123');
      expect(result.directImpact).toBeDefined();
      expect(result.indirectImpact).toBeDefined();
      expect(result.retentionImpact).toBeDefined();
      expect(result.diversityImpact).toBeDefined();
      expect(result.engagementImpact).toBeDefined();
      expect(result.totalImpactScore).toBeGreaterThanOrEqual(0);
      expect(result.impactLevel).toMatch(/^(low|medium|high|exceptional)$/);
      expect(result.recognitionEligibility).toBeDefined();
    });
  });
});

describe("Feedback Contribution Tracking Service", () => {
  let contributionTracker: FeedbackContributionTrackingService;

  beforeEach(() => {
    contributionTracker = new FeedbackContributionTrackingService(mockDb);
  });

  describe("Contribution metrics tracking", () => {
    it("should track user contributions", async () => {
      // Mock message stats for contribution estimation
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{
            totalMessages: 200,
            avgLength: 85
          }]))
        }))
      });

      // Mock message count for community help
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{ count: 200 }]))
        }))
      });

      // Mock message stats for content contributions
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{
            totalMessages: 200,
            avgLength: 85
          }]))
        }))
      });

      const result = await contributionTracker.trackUserContributions('user123');

      expect(result.userId).toBe('user123');
      expect(result.feedbackContributions).toBeDefined();
      expect(result.communityHelp).toBeDefined();
      expect(result.contentContributions).toBeDefined();
      expect(result.moderationContributions).toBeDefined();
      expect(result.mentorshipContributions).toBeDefined();
      expect(result.innovationContributions).toBeDefined();
      expect(result.overallScore).toBeGreaterThanOrEqual(0);
      expect(result.overallScore).toBeLessThanOrEqual(100);
      expect(result.recognitionLevel).toMatch(/^(bronze|silver|gold|platinum|diamond)$/);
      expect(result.achievements).toBeDefined();
    });

    it("should track feedback quality", async () => {
      const timeWindow = {
        startDate: new Date('2023-11-01'),
        endDate: new Date('2023-11-30')
      };

      const result = await contributionTracker.trackFeedbackQuality('user123', timeWindow);

      expect(result.userId).toBe('user123');
      expect(result.timeWindow).toEqual(timeWindow);
      expect(result.feedbackVolume).toBeDefined();
      expect(result.feedbackImpact).toBeDefined();
      expect(result.feedbackCategories).toBeDefined();
      expect(result.responsePatterns).toBeDefined();
      expect(result.qualityTrends).toBeDefined();
      expect(result.overallQualityScore).toBeGreaterThanOrEqual(0);
      expect(result.overallQualityScore).toBeLessThanOrEqual(100);
      expect(result.recommendations).toBeDefined();
    });
  });

  describe("Community support tracking", () => {
    it("should track community support metrics", async () => {
      // Mock message count for helpful responses
      mockDb.select.mockReturnValueOnce({
        from: mock(() => ({
          where: mock(() => Promise.resolve([{ count: 150 }]))
        }))
      });

      const result = await contributionTracker.trackCommunitySupport('user123');

      expect(result.userId).toBe('user123');
      expect(result.helpfulResponses).toBeDefined();
      expect(result.problemSolving).toBeDefined();
      expect(result.newUserSupport).toBeDefined();
      expect(result.knowledgeSharing).toBeDefined();
      expect(result.conflictResolution).toBeDefined();
      expect(result.supportScore).toBeGreaterThanOrEqual(0);
      expect(result.supportScore).toBeLessThanOrEqual(100);
      expect(result.supportLevel).toMatch(/^(helper|supporter|champion|hero)$/);
      expect(result.impactMetrics).toBeDefined();
      expect(result.recognitionEligibility).toBeDefined();
    });
  });
});

describe("Advanced Badge Tracking Service", () => {
  let advancedTracker: AdvancedBadgeTrackingService;

  beforeEach(() => {
    advancedTracker = new AdvancedBadgeTrackingService(mockDb);
  });

  describe("Advanced badge evaluation", () => {
    it("should evaluate user for advanced badges", async () => {
      // Mock all the required database calls for the comprehensive evaluation
      
      // Mock user data for criteria engine
      mockDb.select.mockReturnValue({
        from: mock(() => ({
          where: mock(() => ({
            limit: mock(() => Promise.resolve([{
              createdAt: new Date('2023-01-01'),
              lastActive: new Date()
            }]))
          }))
        }))
      });

      const result = await advancedTracker.evaluateUserForAdvancedBadges('user123');

      expect(result.userId).toBe('user123');
      expect(result.evaluationDate).toBeInstanceOf(Date);
      expect(result.criteriaResults).toBeDefined();
      expect(result.engagementMetrics).toBeDefined();
      expect(result.geographicProfile).toBeDefined();
      expect(result.invitationMetrics).toBeDefined();
      expect(result.contributionMetrics).toBeDefined();
      expect(result.eligibleBadges).toBeDefined();
      expect(result.overallAdvancedScore).toBeGreaterThanOrEqual(0);
      expect(result.overallAdvancedScore).toBeLessThanOrEqual(100);
      expect(result.recommendations).toBeDefined();
    });

    it("should track advanced badge progress", async () => {
      const timeWindow = { value: 90, unit: 'days' as const, label: 'Last Quarter' };

      // Mock the evaluation method
      const mockEvaluation = {
        userId: 'user123',
        evaluationDate: new Date(),
        criteriaResults: [],
        engagementMetrics: [],
        geographicProfile: {} as any,
        invitationMetrics: {} as any,
        contributionMetrics: {} as any,
        eligibleBadges: [],
        overallAdvancedScore: 75,
        recommendations: []
      };

      advancedTracker.evaluateUserForAdvancedBadges = mock(() => Promise.resolve(mockEvaluation));

      const result = await advancedTracker.trackAdvancedBadgeProgress('user123', timeWindow);

      expect(result.userId).toBe('user123');
      expect(result.timeWindow).toEqual(timeWindow);
      expect(result.currentEvaluation).toBeDefined();
      expect(result.historicalTrends).toBeDefined();
      expect(result.progressPredictions).toBeDefined();
      expect(result.milestoneTracking).toBeDefined();
      expect(result.progressScore).toBeGreaterThanOrEqual(0);
      expect(result.nextMilestones).toBeDefined();
    });
  });

  describe("Badge recommendations", () => {
    it("should get advanced badge recommendations", async () => {
      // Mock the evaluation method
      const mockEvaluation = {
        userId: 'user123',
        evaluationDate: new Date(),
        criteriaResults: [],
        engagementMetrics: [],
        geographicProfile: {} as any,
        invitationMetrics: {} as any,
        contributionMetrics: {} as any,
        eligibleBadges: [
          {
            badgeId: 'test_badge',
            badgeName: 'Test Badge',
            category: 'test',
            eligibilityScore: 0.9,
            requirements: ['Test requirement'],
            progress: 0.9
          }
        ],
        overallAdvancedScore: 75,
        recommendations: []
      };

      advancedTracker.evaluateUserForAdvancedBadges = mock(() => Promise.resolve(mockEvaluation));

      const result = await advancedTracker.getAdvancedBadgeRecommendations('user123');

      expect(result.userId).toBe('user123');
      expect(result.nearCompletionBadges).toBeDefined();
      expect(result.strategicBadges).toBeDefined();
      expect(result.communityImpactBadges).toBeDefined();
      expect(result.personalGrowthBadges).toBeDefined();
      expect(result.priorityRecommendations).toBeDefined();
      expect(result.actionPlan).toBeDefined();
      expect(result.actionPlan.shortTerm).toBeDefined();
      expect(result.actionPlan.mediumTerm).toBeDefined();
      expect(result.actionPlan.longTerm).toBeDefined();
    });
  });

  describe("Community analysis", () => {
    it("should analyze community advanced badge patterns", async () => {
      const result = await advancedTracker.analyzeCommunityAdvancedBadgePatterns();

      expect(result.distributionAnalysis).toBeDefined();
      expect(result.trendAnalysis).toBeDefined();
      expect(result.achievementPatterns).toBeDefined();
      expect(result.communityHealth).toBeDefined();
      expect(result.insights).toBeDefined();
      expect(result.recommendations).toBeDefined();
    });
  });

  describe("Error handling", () => {
    it("should handle evaluation errors gracefully", async () => {
      // Mock database error
      mockDb.select.mockImplementationOnce(() => {
        throw new Error("Database connection failed");
      });

      await expect(advancedTracker.evaluateUserForAdvancedBadges('user123')).rejects.toThrow();
    });
  });
});

describe("Integration Tests", () => {
  describe("Cross-service data consistency", () => {
    it("should maintain consistent user data across all tracking services", async () => {
      const userId = 'user123';
      
      const criteriaEngine = new AdvancedCriteriaEngine(mockDb);
      const timeTracker = new TimeBasedTrackingService(mockDb);
      const geoTracker = new GeographicDemographicTrackingService(mockDb);
      const invitationTracker = new InvitationReferralTrackingService(mockDb);
      const contributionTracker = new FeedbackContributionTrackingService(mockDb);

      // Mock consistent user data
      const mockUserData = {
        id: userId,
        username: 'testuser',
        createdAt: new Date('2023-01-01'),
        lastActive: new Date()
      };

      mockDb.select.mockReturnValue({
        from: mock(() => ({
          where: mock(() => ({
            limit: mock(() => Promise.resolve([mockUserData]))
          }))
        }))
      });

      // Test that all services can handle the same user
      const timeWindow = { value: 30, unit: 'days' as const, label: 'Last Month' };
      
      const [
        geoProfile,
        invitationMetrics,
        contributionMetrics
      ] = await Promise.all([
        geoTracker.trackUserGeographicProfile(userId),
        invitationTracker.trackUserInvitationMetrics(userId),
        contributionTracker.trackUserContributions(userId)
      ]);

      expect(geoProfile.userId).toBe(userId);
      expect(invitationMetrics.userId).toBe(userId);
      expect(contributionMetrics.userId).toBe(userId);
    });
  });

  describe("Performance under load", () => {
    it("should handle multiple concurrent evaluations", async () => {
      const advancedTracker = new AdvancedBadgeTrackingService(mockDb);
      const userIds = ['user1', 'user2', 'user3', 'user4', 'user5'];

      // Mock database responses
      mockDb.select.mockReturnValue({
        from: mock(() => ({
          where: mock(() => ({
            limit: mock(() => Promise.resolve([{
              createdAt: new Date('2023-01-01'),
              lastActive: new Date()
            }]))
          }))
        }))
      });

      const startTime = Date.now();
      
      const evaluations = await Promise.all(
        userIds.map(userId => advancedTracker.evaluateUserForAdvancedBadges(userId))
      );

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      expect(evaluations).toHaveLength(5);
      expect(executionTime).toBeLessThan(5000); // Should complete within 5 seconds
      
      evaluations.forEach((evaluation, index) => {
        expect(evaluation.userId).toBe(userIds[index]);
      });
    });
  });
});