import { describe, it, expect, beforeEach, afterEach, mock } from 'bun:test';
import { BadgeCollectionAdminService } from '../../services/badge-collection-admin.service';
import { BadgeError } from '../../class/badge-errors';
import type { 
  CreateBadgeCollectionRequest,
  UpdateBadgeCollectionRequest,
  BulkBadgeAssignmentRequest,
  CollectionTestResult
} from '../../types/badge.types';

// Mock the database
const mockDb = {
  insert: mock(() => ({
    values: mock(() => ({
      returning: mock(() => Promise.resolve([{
        id: 'test-id',
        collectionId: 'test-collection',
        name: 'Test Collection',
        description: 'Test description',
        type: 'progressive',
        totalBadges: 0,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }]))
    }))
  })),
  update: mock(() => ({
    set: mock(() => ({
      where: mock(() => ({
        returning: mock(() => Promise.resolve([{
          id: 'test-id',
          name: 'Updated Collection',
          updatedAt: new Date()
        }]))
      }))
    }))
  })),
  delete: mock(() => ({
    where: mock(() => Promise.resolve())
  })),
  select: mock(() => ({
    from: mock(() => ({
      where: mock(() => Promise.resolve([{
        id: 'test-id',
        collectionId: 'test-collection',
        name: 'Test Collection'
      }])),
      orderBy: mock(() => Promise.resolve([]))
    }))
  })),
  transaction: mock((callback) => callback(mockDb))
};

// Mock the database import
mock.module('../../db/index', () => ({
  db: mockDb
}));

describe('BadgeCollectionAdminService Unit Tests', () => {
  let adminService: BadgeCollectionAdminService;

  beforeEach(() => {
    adminService = new BadgeCollectionAdminService();
    // Reset all mocks
    mock.restore();
  });

  describe('createCollection', () => {
    it('should create a new collection successfully', async () => {
      const collectionData: CreateBadgeCollectionRequest = {
        collectionId: 'test-collection',
        name: 'Test Collection',
        description: 'Test description',
        type: 'progressive'
      };

      const result = await adminService.createCollection(collectionData);

      expect(result).toBeDefined();
      expect(result.collectionId).toBe('test-collection');
      expect(result.name).toBe('Test Collection');
      expect(mockDb.insert).toHaveBeenCalled();
    });

    it('should handle duplicate collection ID error', async () => {
      const collectionData: CreateBadgeCollectionRequest = {
        collectionId: 'duplicate-collection',
        name: 'Duplicate Collection',
        description: 'This should fail',
        type: 'progressive'
      };

      // Mock database error for unique constraint
      mockDb.insert.mockImplementationOnce(() => ({
        values: mock(() => ({
          returning: mock(() => Promise.reject(new Error('unique constraint violation')))
        }))
      }));

      await expect(adminService.createCollection(collectionData))
        .rejects.toThrow(BadgeError);
    });

    it('should set default values correctly', async () => {
      const minimalData: CreateBadgeCollectionRequest = {
        collectionId: 'minimal-collection',
        name: 'Minimal Collection',
        description: 'Minimal description'
      };

      await adminService.createCollection(minimalData);

      expect(mockDb.insert).toHaveBeenCalled();
      // Verify that values method was called with defaults
      const insertCall = mockDb.insert.mock.calls[0];
      expect(insertCall).toBeDefined();
    });
  });

  describe('updateCollection', () => {
    it('should update collection successfully', async () => {
      const updates: UpdateBadgeCollectionRequest = {
        name: 'Updated Name',
        description: 'Updated description'
      };

      const result = await adminService.updateCollection('test-id', updates);

      expect(result).toBeDefined();
      expect(mockDb.update).toHaveBeenCalled();
    });

    it('should throw error for non-existent collection', async () => {
      mockDb.update.mockImplementationOnce(() => ({
        set: mock(() => ({
          where: mock(() => ({
            returning: mock(() => Promise.resolve([]))
          }))
        }))
      }));

      await expect(adminService.updateCollection('non-existent', { name: 'Updated' }))
        .rejects.toThrow('Collection not found');
    });
  });

  describe('deleteCollection', () => {
    it('should delete collection without badges', async () => {
      // Mock getCollectionById to return a collection
      const getByIdSpy = mock.spyOn(adminService, 'getCollectionById')
        .mockResolvedValue({
          id: 'test-id',
          collectionId: 'test-collection',
          name: 'Test Collection',
          description: 'Test',
          type: 'progressive',
          totalBadges: 0,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });

      // Mock select to return no badges
      mockDb.select.mockImplementationOnce(() => ({
        from: mock(() => ({
          where: mock(() => Promise.resolve([]))
        }))
      }));

      await adminService.deleteCollection('test-id');

      expect(getByIdSpy).toHaveBeenCalledWith('test-id');
      expect(mockDb.delete).toHaveBeenCalled();
    });

    it('should not delete collection with existing badges', async () => {
      // Mock getCollectionById to return a collection
      mock.spyOn(adminService, 'getCollectionById')
        .mockResolvedValue({
          id: 'test-id',
          collectionId: 'test-collection',
          name: 'Test Collection',
          description: 'Test',
          type: 'progressive',
          totalBadges: 2,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });

      // Mock select to return badges
      mockDb.select.mockImplementationOnce(() => ({
        from: mock(() => ({
          where: mock(() => Promise.resolve([
            { id: 'badge1', name: 'Badge 1' },
            { id: 'badge2', name: 'Badge 2' }
          ]))
        }))
      }));

      await expect(adminService.deleteCollection('test-id'))
        .rejects.toThrow('Cannot delete collection with existing badges');
    });
  });

  describe('reorderCollectionBadges', () => {
    it('should reorder badges successfully', async () => {
      // Mock getCollectionById
      mock.spyOn(adminService, 'getCollectionById')
        .mockResolvedValue({
          id: 'test-id',
          collectionId: 'test-collection',
          name: 'Test Collection',
          description: 'Test',
          type: 'progressive',
          totalBadges: 2,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });

      // Mock select to return badges
      mockDb.select.mockImplementationOnce(() => ({
        from: mock(() => ({
          where: mock(() => Promise.resolve([
            { id: 'badge1', badgeId: 'first-badge' },
            { id: 'badge2', badgeId: 'second-badge' }
          ]))
        }))
      }));

      const reorderData = [
        { badgeId: 'first-badge', displayOrder: 1 },
        { badgeId: 'second-badge', displayOrder: 0 }
      ];

      await adminService.reorderCollectionBadges('test-id', reorderData);

      expect(mockDb.transaction).toHaveBeenCalled();
    });

    it('should reject invalid badge IDs', async () => {
      // Mock getCollectionById
      mock.spyOn(adminService, 'getCollectionById')
        .mockResolvedValue({
          id: 'test-id',
          collectionId: 'test-collection',
          name: 'Test Collection',
          description: 'Test',
          type: 'progressive',
          totalBadges: 1,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });

      // Mock select to return badges
      mockDb.select.mockImplementationOnce(() => ({
        from: mock(() => ({
          where: mock(() => Promise.resolve([
            { id: 'badge1', badgeId: 'valid-badge' }
          ]))
        }))
      }));

      const reorderData = [
        { badgeId: 'invalid-badge', displayOrder: 0 }
      ];

      await expect(adminService.reorderCollectionBadges('test-id', reorderData))
        .rejects.toThrow('Some badges do not belong to this collection');
    });
  });

  describe('testCollection', () => {
    it('should return valid test result for well-configured collection', async () => {
      // Mock getCollectionById
      mock.spyOn(adminService, 'getCollectionById')
        .mockResolvedValue({
          id: 'test-id',
          collectionId: 'test-collection',
          name: 'Test Collection',
          description: 'Test',
          type: 'progressive',
          totalBadges: 2,
          isActive: true,
          completionReward: {
            badge: 'completion-badge',
            title: 'Master',
            perks: [],
            visual: 'crown',
            animation: 'sparkle'
          },
          createdAt: new Date(),
          updatedAt: new Date()
        });

      // Mock select to return well-ordered badges
      mockDb.select.mockImplementationOnce(() => ({
        from: mock(() => ({
          where: mock(() => ({
            orderBy: mock(() => Promise.resolve([
              {
                id: 'badge1',
                name: 'First Badge',
                displayOrder: 0,
                criteria: { requirement: 'test', tracked: 'messages' }
              },
              {
                id: 'badge2',
                name: 'Second Badge',
                displayOrder: 1,
                criteria: { requirement: 'test', tracked: 'servers' }
              }
            ]))
          }))
        }))
      }));

      const result = await adminService.testCollection('test-id');

      expect(result.isValid).toBe(true);
      expect(result.issues).toHaveLength(0);
      expect(result.badgeCount).toBe(2);
    });

    it('should detect collection issues', async () => {
      // Mock getCollectionById
      mock.spyOn(adminService, 'getCollectionById')
        .mockResolvedValue({
          id: 'test-id',
          collectionId: 'test-collection',
          name: 'Test Collection',
          description: 'Test',
          type: 'progressive',
          totalBadges: 1, // Mismatch with actual badge count
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });

      // Mock select to return no badges
      mockDb.select.mockImplementationOnce(() => ({
        from: mock(() => ({
          where: mock(() => ({
            orderBy: mock(() => Promise.resolve([]))
          }))
        }))
      }));

      const result = await adminService.testCollection('test-id');

      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('Collection has no badges defined');
      expect(result.issues).toContain('Collection totalBadges (1) doesn\'t match actual badge count (0)');
    });

    it('should detect duplicate display orders', async () => {
      // Mock getCollectionById
      mock.spyOn(adminService, 'getCollectionById')
        .mockResolvedValue({
          id: 'test-id',
          collectionId: 'test-collection',
          name: 'Test Collection',
          description: 'Test',
          type: 'progressive',
          totalBadges: 2,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });

      // Mock select to return badges with duplicate orders
      mockDb.select.mockImplementationOnce(() => ({
        from: mock(() => ({
          where: mock(() => ({
            orderBy: mock(() => Promise.resolve([
              {
                id: 'badge1',
                name: 'First Badge',
                displayOrder: 0,
                criteria: { requirement: 'test', tracked: 'messages' }
              },
              {
                id: 'badge2',
                name: 'Second Badge',
                displayOrder: 0, // Duplicate order
                criteria: { requirement: 'test', tracked: 'servers' }
              }
            ]))
          }))
        }))
      }));

      const result = await adminService.testCollection('test-id');

      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('Duplicate display orders found: 0');
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      mockDb.insert.mockImplementationOnce(() => {
        throw new Error('Database connection failed');
      });

      const collectionData: CreateBadgeCollectionRequest = {
        collectionId: 'test-collection',
        name: 'Test Collection',
        description: 'Test description',
        type: 'progressive'
      };

      await expect(adminService.createCollection(collectionData))
        .rejects.toThrow('Failed to create badge collection');
    });

    it('should validate input parameters', async () => {
      // Test with empty collection ID
      const invalidData = {
        collectionId: '',
        name: 'Test Collection',
        description: 'Test description',
        type: 'progressive' as const
      };

      // This would be caught by schema validation in the handler layer
      // but we can test the service behavior with invalid data
      await expect(adminService.createCollection(invalidData))
        .resolves.toBeDefined(); // Service doesn't validate, handler does
    });
  });
});