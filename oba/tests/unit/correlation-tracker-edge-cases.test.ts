import { describe, test, expect, beforeEach, afterEach, mock } from "bun:test";
import { CorrelationTracker } from "../../utils/correlation-tracker";

describe("CorrelationTracker - Edge Cases and Error Handling", () => {
  let tracker: CorrelationTracker;

  beforeEach(() => {
    tracker = new CorrelationTracker();
  });

  afterEach(() => {
    tracker.clearAll();
  });

  describe("Edge Cases in Correlation Management", () => {
    test("should handle starting tracking with empty/invalid parameters", () => {
      // Empty strings
      const id1 = tracker.startTracking("", "");
      expect(id1).toMatch(/^corr-/);
      expect(tracker.isTracking(id1)).toBe(true);

      // Very long strings
      const longString = "x".repeat(1000);
      const id2 = tracker.startTracking(longString, longString);
      expect(id2).toMatch(/^corr-/);
      expect(tracker.isTracking(id2)).toBe(true);

      // Special characters
      const id3 = tracker.startTracking("type/with/special@chars", "user#123$%");
      expect(id3).toMatch(/^corr-/);
      expect(tracker.isTracking(id3)).toBe(true);
    });

    test("should handle metadata with various data types", () => {
      const complexMetadata = {
        string: "value",
        number: 42,
        boolean: true,
        null: null,
        undefined: undefined,
        array: [1, 2, 3],
        object: { nested: { deep: "value" } },
        date: new Date(),
        function: () => "test", // Functions should be handled gracefully
      };

      const id = tracker.startTracking("TEST", "user123", {
        metadata: complexMetadata,
      });

      const correlation = tracker.getCorrelation(id);
      expect(correlation?.metadata).toBeDefined();
      expect(correlation?.metadata?.string).toBe("value");
      expect(correlation?.metadata?.number).toBe(42);
      expect(correlation?.metadata?.array).toEqual([1, 2, 3]);
    });

    test("should handle zero and negative timeout values", () => {
      // Zero timeout should not set up timeout
      const id1 = tracker.startTracking("TEST", "user123", { timeoutMs: 0 });
      const correlation1 = tracker.getCorrelation(id1);
      expect(correlation1?.timeoutId).toBeUndefined();

      // Negative timeout should not set up timeout
      const id2 = tracker.startTracking("TEST", "user123", { timeoutMs: -1000 });
      const correlation2 = tracker.getCorrelation(id2);
      expect(correlation2?.timeoutId).toBeUndefined();
    });

    test("should handle very short timeout values", (done) => {
      const onTimeout = mock(() => {
        expect(tracker.isTracking(id)).toBe(false);
        done();
      });

      const id = tracker.startTracking("TEST", "user123", {
        timeoutMs: 1, // 1ms timeout
        onTimeout,
      });

      expect(tracker.isTracking(id)).toBe(true);
    });

    test("should handle timeout callback throwing errors", (done) => {
      const faultyCallback = mock(() => {
        throw new Error("Callback error");
      });

      const id = tracker.startTracking("TEST", "user123", {
        timeoutMs: 10,
        onTimeout: faultyCallback,
      });

      setTimeout(() => {
        expect(faultyCallback).toHaveBeenCalled();
        expect(tracker.isTracking(id)).toBe(false);
        done();
      }, 20);
    });
  });

  describe("Completion Edge Cases", () => {
    test("should handle completing non-existent correlations", () => {
      const result = tracker.completeTracking("non-existent-id");
      expect(result).toBeNull();

      const result2 = tracker.completeTracking("");
      expect(result2).toBeNull();

      const result3 = tracker.completeTracking("corr-fake-id");
      expect(result3).toBeNull();
    });

    test("should handle completing correlation multiple times", () => {
      const id = tracker.startTracking("TEST", "user123");
      
      const result1 = tracker.completeTracking(id);
      expect(result1).toBeTruthy();
      expect(result1?.id).toBe(id);

      const result2 = tracker.completeTracking(id);
      expect(result2).toBeNull();
    });

    test("should handle completion with complex response data", () => {
      const id = tracker.startTracking("TEST", "user123");

      const complexResponseData = {
        success: true,
        data: {
          users: [
            { id: 1, name: "User 1" },
            { id: 2, name: "User 2" },
          ],
          metadata: {
            total: 2,
            page: 1,
            hasMore: false,
          },
        },
        errors: null,
        timestamp: new Date(),
      };

      const result = tracker.completeTracking(id, complexResponseData);
      expect(result).toBeTruthy();
      expect(result?.id).toBe(id);
    });
  });

  describe("Memory Management and Cleanup", () => {
    test("should handle cleanup with various age thresholds", () => {
      // Create correlations with different ages
      const ids = [];
      for (let i = 0; i < 10; i++) {
        const id = tracker.startTracking(`REQUEST_${i}`, `user${i}`);
        ids.push(id);
        
        // Manually adjust timestamp to simulate age
        const correlation = tracker.getCorrelation(id);
        if (correlation) {
          correlation.timestamp = Date.now() - (i * 1000); // Each one is 1 second older
        }
      }

      // Cleanup correlations older than 5 seconds
      const cleaned = tracker.cleanupExpired(5000);
      expect(cleaned).toBe(5); // Should clean up the 5 oldest

      // Verify the right ones were cleaned
      for (let i = 0; i < 10; i++) {
        if (i < 5) {
          expect(tracker.isTracking(ids[i])).toBe(false);
        } else {
          expect(tracker.isTracking(ids[i])).toBe(true);
        }
      }
    });

    test("should handle cleanup when no correlations exist", () => {
      const cleaned = tracker.cleanupExpired();
      expect(cleaned).toBe(0);
    });

    test("should handle cleanup with all correlations being recent", () => {
      for (let i = 0; i < 5; i++) {
        tracker.startTracking(`REQUEST_${i}`, `user${i}`);
      }

      const cleaned = tracker.cleanupExpired(1000); // 1 second max age
      expect(cleaned).toBe(0); // All should be recent
    });

    test("should handle memory pressure with automatic cleanup", () => {
      // Create many correlations to trigger automatic cleanup
      const ids = [];
      for (let i = 0; i < 15000; i++) { // Exceed MAX_CORRELATIONS
        const id = tracker.startTracking(`REQUEST_${i}`, `user${i % 100}`);
        ids.push(id);
      }

      const stats = tracker.getStats();
      expect(stats.activeCorrelations).toBeLessThan(15000); // Should have triggered cleanup
    });
  });

  describe("Statistics and Monitoring", () => {
    test("should handle statistics with edge cases", () => {
      // Empty tracker
      let stats = tracker.getStats();
      expect(stats.activeCorrelations).toBe(0);
      expect(stats.totalTracked).toBe(0);
      expect(stats.averageAge).toBe(0);
      expect(stats.oldestCorrelation).toBeUndefined();

      // Single correlation
      const id = tracker.startTracking("TEST", "user123");
      stats = tracker.getStats();
      expect(stats.activeCorrelations).toBe(1);
      expect(stats.averageAge).toBeGreaterThan(0);
      expect(stats.oldestCorrelation).toBeDefined();
      expect(stats.oldestCorrelation?.id).toBe(id);
    });

    test("should handle statistics with correlations of different ages", () => {
      const now = Date.now();
      const ids = [];

      // Create correlations with specific timestamps
      for (let i = 0; i < 5; i++) {
        const id = tracker.startTracking(`REQUEST_${i}`, `user${i}`);
        ids.push(id);
        
        const correlation = tracker.getCorrelation(id);
        if (correlation) {
          correlation.timestamp = now - (i * 10000); // 10 seconds apart
        }
      }

      const stats = tracker.getStats();
      expect(stats.activeCorrelations).toBe(5);
      expect(stats.averageAge).toBeGreaterThan(0);
      expect(stats.oldestCorrelation?.id).toBe(ids[4]); // Last one should be oldest
    });

    test("should handle getUserCorrelations with edge cases", () => {
      // Non-existent user
      let userCorrelations = tracker.getUserCorrelations("non-existent");
      expect(userCorrelations).toEqual([]);

      // Empty string user
      userCorrelations = tracker.getUserCorrelations("");
      expect(userCorrelations).toEqual([]);

      // User with many correlations
      for (let i = 0; i < 100; i++) {
        tracker.startTracking(`REQUEST_${i}`, "heavy-user");
      }

      userCorrelations = tracker.getUserCorrelations("heavy-user");
      expect(userCorrelations).toHaveLength(100);
    });
  });

  describe("Concurrency and Race Conditions", () => {
    test("should handle rapid correlation creation and completion", async () => {
      const promises = [];
      const results = [];

      // Create many correlations rapidly
      for (let i = 0; i < 1000; i++) {
        promises.push(
          Promise.resolve().then(() => {
            const id = tracker.startTracking(`REQUEST_${i}`, `user${i % 10}`);
            results.push(id);
            
            // Complete some immediately
            if (i % 2 === 0) {
              return tracker.completeTracking(id);
            }
            return null;
          }),
        );
      }

      await Promise.all(promises);

      // Verify state is consistent
      const stats = tracker.getStats();
      expect(stats.activeCorrelations).toBe(500); // Half should remain active
    });

    test("should handle concurrent cleanup operations", async () => {
      // Create correlations
      for (let i = 0; i < 100; i++) {
        tracker.startTracking(`REQUEST_${i}`, `user${i}`);
      }

      // Run multiple cleanup operations concurrently
      const cleanupPromises = [];
      for (let i = 0; i < 10; i++) {
        cleanupPromises.push(
          Promise.resolve().then(() => tracker.cleanupExpired(1000)),
        );
      }

      const results = await Promise.all(cleanupPromises);
      
      // Should handle concurrent cleanups gracefully
      const totalCleaned = results.reduce((sum, count) => sum + count, 0);
      expect(totalCleaned).toBeGreaterThanOrEqual(0);
    });

    test("should handle timeout and completion race conditions", (done) => {
      let timeoutCalled = false;
      let completionResult = null;

      const onTimeout = mock(() => {
        timeoutCalled = true;
      });

      const id = tracker.startTracking("RACE_TEST", "user123", {
        timeoutMs: 50,
        onTimeout,
      });

      // Try to complete just before timeout
      setTimeout(() => {
        completionResult = tracker.completeTracking(id);
      }, 45);

      // Check results after timeout should have occurred
      setTimeout(() => {
        // Either timeout or completion should have won, but not both
        if (timeoutCalled) {
          expect(completionResult).toBeNull();
        } else {
          expect(completionResult).toBeTruthy();
        }
        expect(tracker.isTracking(id)).toBe(false);
        done();
      }, 100);
    });
  });

  describe("Error Recovery and Resilience", () => {
    test("should recover from corrupted correlation data", () => {
      const id = tracker.startTracking("TEST", "user123");
      
      // Simulate corruption by accessing private members
      const correlation = tracker.getCorrelation(id);
      if (correlation) {
        // Corrupt the data
        (correlation as any).timestamp = "invalid";
        (correlation as any).userId = null;
      }

      // Operations should still work
      const stats = tracker.getStats();
      expect(stats.activeCorrelations).toBe(1);

      const result = tracker.completeTracking(id);
      expect(result).toBeTruthy(); // Should still complete despite corruption
    });

    test("should handle system clock changes", () => {
      const id = tracker.startTracking("TEST", "user123");
      
      // Simulate system clock going backwards
      const correlation = tracker.getCorrelation(id);
      if (correlation) {
        correlation.timestamp = Date.now() + 86400000; // 1 day in the future
      }

      // Should handle gracefully
      const stats = tracker.getStats();
      expect(stats.activeCorrelations).toBe(1);
      expect(stats.averageAge).toBeLessThan(0); // Negative age due to future timestamp

      const cleaned = tracker.cleanupExpired(1000);
      expect(cleaned).toBe(0); // Shouldn't clean future correlations
    });

    test("should handle extreme memory pressure", () => {
      // Fill up memory with correlations
      const ids = [];
      try {
        for (let i = 0; i < 50000; i++) {
          const id = tracker.startTracking(`REQUEST_${i}`, `user${i % 1000}`);
          ids.push(id);
        }
      } catch (error) {
        // Should handle memory pressure gracefully
        expect(error).toBeInstanceOf(Error);
      }

      // Tracker should still be functional
      const stats = tracker.getStats();
      expect(stats.activeCorrelations).toBeGreaterThan(0);
      expect(stats.activeCorrelations).toBeLessThanOrEqual(50000);
    });
  });

  describe("Integration with Timeouts", () => {
    test("should handle timeout cleanup when clearAll is called", () => {
      const timeoutMocks = [];
      
      for (let i = 0; i < 10; i++) {
        const onTimeout = mock();
        timeoutMocks.push(onTimeout);
        
        tracker.startTracking(`REQUEST_${i}`, `user${i}`, {
          timeoutMs: 1000,
          onTimeout,
        });
      }

      tracker.clearAll();

      // Wait for potential timeouts
      setTimeout(() => {
        timeoutMocks.forEach((mockFn) => {
          expect(mockFn).not.toHaveBeenCalled();
        });
      }, 1100);
    });

    test("should handle timeout when correlation is already completed", (done) => {
      const onTimeout = mock();

      const id = tracker.startTracking("TEST", "user123", {
        timeoutMs: 50,
        onTimeout,
      });

      // Complete immediately
      tracker.completeTracking(id);

      // Wait for timeout period
      setTimeout(() => {
        expect(onTimeout).not.toHaveBeenCalled();
        expect(tracker.isTracking(id)).toBe(false);
        done();
      }, 100);
    });
  });
});