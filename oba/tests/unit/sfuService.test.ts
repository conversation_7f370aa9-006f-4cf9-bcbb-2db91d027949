import { describe, expect, test, mock, spyOn } from "bun:test";
import { SFUService, SFUEventTypes } from "../../services/sfu.service";
import {
  createVoiceMetadata,
  createVoiceMessage,
} from "../../utils/binaryProtocol";

// Mock WebSocket
class MockWebSocket {
  readyState = WebSocket.OPEN;
  data = {
    userId: "user123",
    serverId: "server456",
    channelId: "channel789",
    user: {
      id: "user123",
      name: "Test User",
      avatar: "avatar_url",
      speaking: false,
      muted: false,
      deafened: false,
    },
    isAuthenticated: true,
    token: "token123",
    isAlive: true,
    type: "voice",
  };

  send = mock(() => {});
  close = mock(() => {});
}

// Mock VoiceWebSocketManager
class MockVoiceWebSocketManager {
  topicSubscriptions = new Map();

  constructor() {
    // Initialize with a test channel
    const channelKey = "server456:channel789";
    const ws1 = new MockWebSocket();
    const ws2 = new MockWebSocket();
    ws2.data.userId = "user456";

    this.topicSubscriptions.set(channelKey, new Set([ws1, ws2]));
  }

  add = mock((ws) => {
    const channelKey = `${ws.data.serverId}:${ws.data.channelId}`;
    if (!this.topicSubscriptions.has(channelKey)) {
      this.topicSubscriptions.set(channelKey, new Set());
    }
    this.topicSubscriptions.get(channelKey).add(ws);
  });

  remove = mock((ws) => {
    const channelKey = `${ws.data.serverId}:${ws.data.channelId}`;
    if (this.topicSubscriptions.has(channelKey)) {
      this.topicSubscriptions.get(channelKey).delete(ws);
    }
  });

  getSocketsInChannel = mock((channelKey) => {
    return this.topicSubscriptions.get(channelKey);
  });

  getSocketByUserIdAndTopic = mock((userId, channelKey) => {
    const sockets = this.topicSubscriptions.get(channelKey);
    if (!sockets) return null;

    for (const socket of sockets) {
      if (socket.data.userId === userId) {
        return socket;
      }
    }

    return null;
  });

  broadcast = mock((userId, serverId, channelId, data) => {});
}

describe("SFU Service", () => {
  test("handleJoin should add user to voice channel", async () => {
    // Create mocks
    const voiceWsManager = new MockVoiceWebSocketManager();
    const sfuService = new SFUService(voiceWsManager);
    const ws = new MockWebSocket();

    // Create join message
    const message = {
      type: SFUEventTypes.SFU_JOIN,
      data: {
        serverId: "server456",
        channelId: "channel789",
        userId: "user123",
        user: ws.data.user,
      },
    };

    // Call handleJoin
    await sfuService.handleJoin(ws, message);

    // Check that add was called
    expect(voiceWsManager.add).toHaveBeenCalledWith(ws);

    // Check that a response was sent
    expect(ws.send).toHaveBeenCalled();

    // Check that the response contains the correct data
    const sendCall = ws.send.mock.calls[0];
    const response = JSON.parse(sendCall[0]);
    expect(response.type).toBe(SFUEventTypes.SFU_JOIN);
    expect(response.data.success).toBe(true);
    expect(response.data.channelId).toBe("channel789");
    expect(response.data.serverId).toBe("server456");
  });

  test("handleLeave should remove user from voice channel", () => {
    // Create mocks
    const voiceWsManager = new MockVoiceWebSocketManager();
    const sfuService = new SFUService(voiceWsManager);
    const ws = new MockWebSocket();

    // Create leave message
    const message = {
      type: SFUEventTypes.SFU_LEAVE,
      data: {
        serverId: "server456",
        channelId: "channel789",
        userId: "user123",
      },
    };

    // Call handleLeave
    sfuService.handleLeave(ws, message);

    // Check that remove was called
    expect(voiceWsManager.remove).toHaveBeenCalledWith(ws);

    // Check that a broadcast was sent
    const getSocketsCall = voiceWsManager.getSocketsInChannel.mock.calls[0];
    expect(getSocketsCall[0]).toBe("server456:channel789");
  });

  test("handleVoiceData should forward voice data to other users", () => {
    // Create mocks
    const voiceWsManager = new MockVoiceWebSocketManager();
    const sfuService = new SFUService(voiceWsManager);
    const ws = new MockWebSocket();

    // Create voice data
    const userId = "user123";
    const sequence = 42;
    const audioData = new Uint8Array([1, 2, 3, 4, 5]).buffer;
    const metadata = createVoiceMetadata(userId, sequence);
    const binaryMessage = createVoiceMessage(metadata, audioData);

    // Spy on broadcastToChannel
    const broadcastSpy = spyOn(sfuService, "broadcastToChannel");

    // Call handleVoiceData
    sfuService.handleVoiceData(ws, binaryMessage);

    // Check that getSocketsInChannel was called
    const getSocketsCall = voiceWsManager.getSocketsInChannel.mock.calls[0];
    expect(getSocketsCall[0]).toBe("server456:channel789");

    // Check that speaking state was broadcast
    expect(broadcastSpy).toHaveBeenCalled();
    const broadcastCall = broadcastSpy.mock.calls[0];
    expect(broadcastCall[1].type).toBe(SFUEventTypes.SFU_SPEAKING_STATE);
    expect(broadcastCall[1].data.userId).toBe(userId);
    expect(broadcastCall[1].data.isSpeaking).toBe(true);
  });

  test("handleVoiceData should not forward voice data from muted users", () => {
    // Create mocks
    const voiceWsManager = new MockVoiceWebSocketManager();
    const sfuService = new SFUService(voiceWsManager);
    const ws = new MockWebSocket();

    // Set user as muted
    ws.data.user.muted = true;

    // Create voice data
    const userId = "user123";
    const sequence = 42;
    const audioData = new Uint8Array([1, 2, 3, 4, 5]).buffer;
    const metadata = createVoiceMetadata(userId, sequence);
    const binaryMessage = createVoiceMessage(metadata, audioData);

    // Add the user to clientConnections
    sfuService["clientConnections"].set(userId, {
      userId,
      peerConnection: null,
      audioTransceiver: null,
      dataChannel: null,
      serverId: "server456",
      channelId: "channel789",
      isMuted: true,
      isDeafened: false,
      isSpeaking: false,
      lastActivity: Date.now(),
      connectionState: "connected",
      streams: new Map(),
    });

    // Call handleVoiceData
    sfuService.handleVoiceData(ws, binaryMessage);

    // Check that getSocketsInChannel was not called (since user is muted)
    expect(voiceWsManager.getSocketsInChannel).not.toHaveBeenCalled();
  });
});
