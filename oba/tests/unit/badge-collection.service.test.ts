import { describe, it, expect, beforeEach, mock } from "bun:test";
import type {
  BadgeCollection,
  CreateBadgeCollectionRequest,
  UpdateBadgeCollectionRequest,
  BadgeCollectionFilters
} from "../../types/badge.types";

// Mock the badge errors
class BadgeNotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'BadgeNotFoundError';
  }
}

class BadgeValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'BadgeValidationError';
  }
}

class InsufficientPermissionsError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'InsufficientPermissionsError';
  }
}

// Mock validation functions
const mockValidation = {
  validateCreateBadgeCollection: mock((data: any) => data),
  validateUpdateBadgeCollection: mock((data: any) => data),
  validateBadgeCollectionFilters: mock((data: any) => data)
};

// Mock badge websocket service
const mockBadgeWebSocketService = {
  broadcastCollectionCompleted: mock(async () => {})
};

// Create a mock BadgeCollectionService class
class MockBadgeCollectionService {
  constructor(private db: any) {}

  async createCollection(data: CreateBadgeCollectionRequest, userId: string): Promise<BadgeCollection> {
    mockValidation.validateCreateBadgeCollection(data);
    
    if (data.collectionId === "existing-collection") {
      throw new BadgeValidationError(`Collection with ID "${data.collectionId}" already exists`);
    }

    return {
      id: "test-collection-id",
      collectionId: data.collectionId,
      name: data.name,
      description: data.description,
      type: data.type,
      totalBadges: 0,
      unlockedBy: data.unlockedBy,
      completionReward: data.completionReward,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  async updateCollection(collectionId: string, updates: UpdateBadgeCollectionRequest, userId: string): Promise<BadgeCollection> {
    mockValidation.validateUpdateBadgeCollection(updates);
    
    if (collectionId === "non-existent") {
      throw new BadgeNotFoundError(`Collection not found: ${collectionId}`);
    }

    return {
      id: "test-collection-id",
      collectionId,
      name: updates.name || "Test Collection",
      description: updates.description || "Test description",
      type: updates.type || "progressive",
      totalBadges: 3,
      unlockedBy: updates.unlockedBy,
      completionReward: updates.completionReward,
      isActive: updates.isActive !== undefined ? updates.isActive : true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  async deleteCollection(collectionId: string, userId: string): Promise<void> {
    if (collectionId === "non-existent") {
      throw new BadgeNotFoundError(`Collection not found: ${collectionId}`);
    }
    
    if (collectionId === "has-badges") {
      throw new BadgeValidationError("Cannot delete collection that contains badges. Remove badges first.");
    }
  }

  async getCollections(filters?: BadgeCollectionFilters, limit = 50, offset = 0): Promise<BadgeCollection[]> {
    if (filters) {
      mockValidation.validateBadgeCollectionFilters(filters);
    }

    return [{
      id: "test-collection-id",
      collectionId: "test-collection",
      name: "Test Collection",
      description: "A test collection",
      type: "progressive",
      totalBadges: 3,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }];
  }

  async getCollectionById(collectionId: string): Promise<BadgeCollection> {
    if (collectionId === "non-existent") {
      throw new BadgeNotFoundError(`Collection not found: ${collectionId}`);
    }

    return {
      id: "test-collection-id",
      collectionId,
      name: "Test Collection",
      description: "A test collection",
      type: "progressive",
      totalBadges: 3,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  async canUnlockNextBadge(userId: string, collectionId: string, displayOrder: number): Promise<boolean> {
    if (displayOrder === 0) return true;
    if (collectionId === "standalone-collection") return true;
    return false;
  }

  async isCollectionCompleted(userId: string, collectionId: string): Promise<boolean> {
    return collectionId === "completed-collection";
  }

  async assignBadgeWithCollectionCheck(userId: string, badgeTypeId: string, assignedBy?: string) {
    if (badgeTypeId === "invalid-badge") {
      return { success: false, error: "Badge type not found" };
    }
    
    if (badgeTypeId === "blocked-badge") {
      return { success: false, error: "Previous badges in collection must be earned first" };
    }

    return { success: true };
  }

  async updateCollectionBadgeCount(collectionId: string): Promise<void> {
    // Mock implementation
  }
}

describe("BadgeCollectionService Unit Tests", () => {
  let service: MockBadgeCollectionService;
  let mockCollection: BadgeCollection;

  beforeEach(() => {
    service = new MockBadgeCollectionService({} as any);
    
    mockCollection = {
      id: "test-collection-id",
      collectionId: "test-collection",
      name: "Test Collection",
      description: "A test collection",
      type: "progressive",
      totalBadges: 3,
      unlockedBy: "activity_and_time",
      completionReward: {
        badge: "completion-badge",
        title: "Collection Master",
        perks: ["special_role"],
        visual: "golden_crown",
        animation: "sparkle"
      },
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Reset all mocks
    Object.values(mockValidation).forEach(mockFn => {
      if (typeof mockFn === 'function') {
        mockFn.mockClear();
      }
    });
  });

  describe("Collection Management", () => {
    describe("createCollection", () => {
      it("should create a new collection successfully", async () => {
        const collectionData: CreateBadgeCollectionRequest = {
          collectionId: "new-collection",
          name: "New Collection",
          description: "A new test collection",
          type: "progressive",
          completionReward: {
            badge: "completion-badge",
            title: "Master",
            perks: ["role"],
            visual: "crown",
            animation: "glow"
          }
        };

        const result = await service.createCollection(collectionData, "user-id");

        expect(result).toBeDefined();
        expect(result.collectionId).toBe("new-collection");
        expect(result.name).toBe("New Collection");
        expect(mockValidation.validateCreateBadgeCollection).toHaveBeenCalledWith(collectionData);
      });

      it("should throw error for duplicate collection ID", async () => {
        const collectionData: CreateBadgeCollectionRequest = {
          collectionId: "existing-collection",
          name: "Existing Collection",
          description: "Already exists",
          type: "progressive"
        };

        await expect(
          service.createCollection(collectionData, "user-id")
        ).rejects.toThrow(BadgeValidationError);
      });
    });

    describe("updateCollection", () => {
      it("should update collection successfully", async () => {
        const updates: UpdateBadgeCollectionRequest = {
          name: "Updated Collection",
          description: "Updated description"
        };

        const result = await service.updateCollection(
          "test-collection",
          updates,
          "user-id"
        );

        expect(result.name).toBe("Updated Collection");
        expect(result.description).toBe("Updated description");
        expect(mockValidation.validateUpdateBadgeCollection).toHaveBeenCalledWith(updates);
      });

      it("should throw error for non-existent collection", async () => {
        const updates: UpdateBadgeCollectionRequest = {
          name: "Updated Collection"
        };

        await expect(
          service.updateCollection("non-existent", updates, "user-id")
        ).rejects.toThrow(BadgeNotFoundError);
      });
    });

    describe("deleteCollection", () => {
      it("should delete collection successfully when no badges exist", async () => {
        await service.deleteCollection("test-collection", "user-id");
        // Should not throw an error
      });

      it("should throw error when collection has badges", async () => {
        await expect(
          service.deleteCollection("has-badges", "user-id")
        ).rejects.toThrow(BadgeValidationError);
      });
    });

    describe("getCollections", () => {
      it("should get collections with filters", async () => {
        const filters: BadgeCollectionFilters = {
          type: "progressive",
          isActive: true,
          search: "test"
        };

        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.orderBy.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue(mockDb);
        mockDb.offset.mockReturnValue([mockCollection]);

        const result = await service.getCollections(filters, 10, 0);

        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
        expect(mockDb.select).toHaveBeenCalled();
      });

      it("should get collections without filters", async () => {
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.orderBy.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue(mockDb);
        mockDb.offset.mockReturnValue([mockCollection]);

        const result = await service.getCollections();

        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
      });
    });

    describe("getCollectionById", () => {
      it("should get collection by ID successfully", async () => {
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([mockCollection]);

        const result = await service.getCollectionById("test-collection");

        expect(result).toBeDefined();
        expect(result.collectionId).toBe("test-collection");
      });

      it("should throw error for non-existent collection", async () => {
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([]);

        await expect(
          service.getCollectionById("non-existent")
        ).rejects.toThrow(BadgeNotFoundError);
      });
    });
  });

  describe("Progress Tracking", () => {
    describe("getUserCollectionProgress", () => {
      it("should get user collection progress", async () => {
        const mockProgress = {
          id: "progress-id",
          userId: "user-id",
          collectionId: "collection-id",
          badgesEarned: 2,
          totalBadges: 3,
          isCompleted: false,
          completionRewardGranted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Mock getting collection
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([mockCollection]);

        // Mock getting progress
        mockDb.select.mockReturnValueOnce(mockDb);
        mockDb.from.mockReturnValueOnce(mockDb);
        mockDb.where.mockReturnValueOnce(mockDb);
        mockDb.limit.mockReturnValueOnce([mockProgress]);

        const result = await service.getUserCollectionProgress("user-id", "test-collection");

        expect(result).toBeDefined();
        expect(result?.badgesEarned).toBe(2);
        expect(result?.totalBadges).toBe(3);
      });

      it("should return null for no progress", async () => {
        // Mock getting collection
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([mockCollection]);

        // Mock no progress found
        mockDb.select.mockReturnValueOnce(mockDb);
        mockDb.from.mockReturnValueOnce(mockDb);
        mockDb.where.mockReturnValueOnce(mockDb);
        mockDb.limit.mockReturnValueOnce([]);

        const result = await service.getUserCollectionProgress("user-id", "test-collection");

        expect(result).toBeNull();
      });
    });

    describe("updateUserCollectionProgress", () => {
      it("should create new progress record", async () => {
        const mockNewProgress = {
          id: "new-progress-id",
          userId: "user-id",
          collectionId: "collection-id",
          badgesEarned: 1,
          totalBadges: 3,
          isCompleted: false,
          completionRewardGranted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Mock getting collection
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([mockCollection]);

        // Mock no existing progress
        mockDb.select.mockReturnValueOnce(mockDb);
        mockDb.from.mockReturnValueOnce(mockDb);
        mockDb.where.mockReturnValueOnce(mockDb);
        mockDb.limit.mockReturnValueOnce([]);

        // Mock creating new progress
        mockDb.insert.mockReturnValue(mockDb);
        mockDb.values.mockReturnValue(mockDb);
        mockDb.returning.mockReturnValue([mockNewProgress]);

        const result = await service.updateUserCollectionProgress(
          "user-id",
          "test-collection",
          true
        );

        expect(result).toBeDefined();
        expect(result.badgesEarned).toBe(1);
        expect(mockDb.insert).toHaveBeenCalled();
      });

      it("should update existing progress record", async () => {
        const mockExistingProgress = {
          id: "progress-id",
          userId: "user-id",
          collectionId: "collection-id",
          badgesEarned: 1,
          totalBadges: 3,
          isCompleted: false,
          completionDate: null,
          completionRewardGranted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const mockUpdatedProgress = {
          ...mockExistingProgress,
          badgesEarned: 2,
          updatedAt: new Date()
        };

        // Mock getting collection
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([mockCollection]);

        // Mock existing progress
        mockDb.select.mockReturnValueOnce(mockDb);
        mockDb.from.mockReturnValueOnce(mockDb);
        mockDb.where.mockReturnValueOnce(mockDb);
        mockDb.limit.mockReturnValueOnce([mockExistingProgress]);

        // Mock update operation
        mockDb.update.mockReturnValue(mockDb);
        mockDb.set.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.returning.mockReturnValue([mockUpdatedProgress]);

        const result = await service.updateUserCollectionProgress(
          "user-id",
          "test-collection",
          true
        );

        expect(result).toBeDefined();
        expect(result.badgesEarned).toBe(2);
        expect(mockDb.update).toHaveBeenCalled();
      });
    });
  });

  describe("Sequential Badge Unlocking", () => {
    describe("canUnlockNextBadge", () => {
      it("should allow unlocking first badge", async () => {
        // Mock getting collection
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([mockCollection]);

        const result = await service.canUnlockNextBadge("user-id", "test-collection", 0);

        expect(result).toBe(true);
      });

      it("should allow unlocking in standalone collection", async () => {
        const standaloneCollection = { ...mockCollection, type: "standalone" as const };

        // Mock getting collection
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([standaloneCollection]);

        const result = await service.canUnlockNextBadge("user-id", "test-collection", 2);

        expect(result).toBe(true);
      });
    });

    describe("getNextUnlockableBadge", () => {
      it("should return first unlockable badge", async () => {
        const mockBadge = {
          id: "badge-id",
          badgeId: "first-badge",
          name: "First Badge",
          displayOrder: 0,
          isActive: true
        };

        // Mock getting collection
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([mockCollection]);

        // Mock getting badges with progress
        mockDb.select.mockReturnValueOnce(mockDb);
        mockDb.from.mockReturnValueOnce(mockDb);
        mockDb.leftJoin.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.orderBy.mockReturnValue([
          { badge: mockBadge, userBadge: null }
        ]);

        const result = await service.getNextUnlockableBadge("user-id", "test-collection");

        expect(result).toBeDefined();
        expect(result?.name).toBe("First Badge");
      });

      it("should return null when no badges available", async () => {
        // Mock getting collection
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([mockCollection]);

        // Mock no badges available
        mockDb.select.mockReturnValueOnce(mockDb);
        mockDb.from.mockReturnValueOnce(mockDb);
        mockDb.leftJoin.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.orderBy.mockReturnValue([]);

        const result = await service.getNextUnlockableBadge("user-id", "test-collection");

        expect(result).toBeNull();
      });
    });
  });

  describe("Collection Completion", () => {
    describe("isCollectionCompleted", () => {
      it("should return true for completed collection", async () => {
        const completedProgress = {
          id: "progress-id",
          userId: "user-id",
          collectionId: "collection-id",
          badgesEarned: 3,
          totalBadges: 3,
          isCompleted: true,
          completionRewardGranted: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Mock getting collection
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([mockCollection]);

        // Mock completed progress
        mockDb.select.mockReturnValueOnce(mockDb);
        mockDb.from.mockReturnValueOnce(mockDb);
        mockDb.where.mockReturnValueOnce(mockDb);
        mockDb.limit.mockReturnValueOnce([completedProgress]);

        const result = await service.isCollectionCompleted("user-id", "test-collection");

        expect(result).toBe(true);
      });

      it("should return false for incomplete collection", async () => {
        // Mock getting collection
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([mockCollection]);

        // Mock no progress
        mockDb.select.mockReturnValueOnce(mockDb);
        mockDb.from.mockReturnValueOnce(mockDb);
        mockDb.where.mockReturnValueOnce(mockDb);
        mockDb.limit.mockReturnValueOnce([]);

        const result = await service.isCollectionCompleted("user-id", "test-collection");

        expect(result).toBe(false);
      });
    });
  });

  describe("Collection-based Badge Assignment", () => {
    describe("assignBadgeWithCollectionCheck", () => {
      it("should allow assignment when dependencies are met", async () => {
        const mockBadgeType = {
          id: "badge-id",
          collectionId: "collection-id",
          displayOrder: 0,
          isActive: true
        };

        // Mock getting badge type
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([mockBadgeType]);

        const result = await service.assignBadgeWithCollectionCheck(
          "user-id",
          "badge-id",
          "assigned-by"
        );

        expect(result.success).toBe(true);
      });

      it("should prevent assignment when dependencies not met", async () => {
        const mockBadgeType = {
          id: "badge-id",
          collectionId: "collection-id",
          displayOrder: 2,
          isActive: true
        };

        // Mock getting badge type
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([mockBadgeType]);

        // Mock collection (progressive)
        mockDb.select.mockReturnValueOnce(mockDb);
        mockDb.from.mockReturnValueOnce(mockDb);
        mockDb.where.mockReturnValueOnce(mockDb);
        mockDb.limit.mockReturnValueOnce([mockCollection]);

        const result = await service.assignBadgeWithCollectionCheck(
          "user-id",
          "badge-id",
          "assigned-by"
        );

        expect(result.success).toBe(false);
        expect(result.error).toContain("Previous badges in collection must be earned first");
      });

      it("should handle badge not found", async () => {
        // Mock badge not found
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([]);

        const result = await service.assignBadgeWithCollectionCheck(
          "user-id",
          "non-existent-badge",
          "assigned-by"
        );

        expect(result.success).toBe(false);
        expect(result.error).toBe("Badge type not found");
      });
    });
  });

  describe("Utility Methods", () => {
    describe("updateCollectionBadgeCount", () => {
      it("should update badge count successfully", async () => {
        // Mock getting collection
        mockDb.select.mockReturnValue(mockDb);
        mockDb.from.mockReturnValue(mockDb);
        mockDb.where.mockReturnValue(mockDb);
        mockDb.limit.mockReturnValue([mockCollection]);

        // Mock badge count
        mockDb.select.mockReturnValueOnce(mockDb);
        mockDb.from.mockReturnValueOnce(mockDb);
        mockDb.where.mockReturnValueOnce([{ count: 5 }]);

        // Mock update
        mockDb.update.mockReturnValue(mockDb);
        mockDb.set.mockReturnValue(mockDb);

        await service.updateCollectionBadgeCount("test-collection");

        expect(mockDb.update).toHaveBeenCalled();
      });
    });
  });
});