import { describe, it, expect, beforeEach, afterEach, mock, spyOn } from "bun:test";
import { WebSocketManager } from "../../manager/websocket.manager";
import { WebSocketUtils } from "../../utils/websocket-utils";
import { WebSocketErrorCode } from "../../types/websocket-standardization.types";
import type { ServerWebSocket } from "bun";
import type { CustomWebSocketData } from "../../types/websocket.types";

// Mock WebSocket
class MockWebSocket {
  public readyState = WebSocket.OPEN;
  public data: CustomWebSocketData;
  private messageQueue: string[] = [];

  constructor(data: CustomWebSocketData) {
    this.data = data;
  }

  send(message: string) {
    this.messageQueue.push(message);
  }

  getMessages() {
    return this.messageQueue;
  }

  clearMessages() {
    this.messageQueue = [];
  }

  close() {
    this.readyState = WebSocket.CLOSED;
  }
}

describe("Enhanced WebSocket Manager", () => {
  let manager: WebSocketManager;
  let mockWs: MockWebSocket;

  beforeEach(() => {
    // Get fresh instance for each test
    manager = WebSocketManager.getInstance();
    
    // Create mock WebSocket
    mockWs = new MockWebSocket({
      userId: "test-user-123",
      token: "test-token",
      isAlive: true,
      serverId: "test-server",
      channelId: "test-channel",
    });
  });

  afterEach(() => {
    // Clean up
    if (mockWs) {
      mockWs.close();
    }
  });

  describe("Standardized Message Handling", () => {
    it("should send standardized success messages", () => {
      const successMessage = WebSocketUtils.success("TEST_SUCCESS", {
        data: "test data",
      });

      manager.sendStandardMessage(mockWs as any, successMessage);

      const messages = mockWs.getMessages();
      expect(messages).toHaveLength(1);

      const sentMessage = JSON.parse(messages[0]);
      expect(sentMessage.success).toBe(true);
      expect(sentMessage.type).toBe("TEST_SUCCESS");
      expect(sentMessage.data.data).toBe("test data");
      expect(sentMessage.meta).toBeDefined();
      expect(sentMessage.meta.id).toBeDefined();
      expect(sentMessage.meta.timestamp).toBeDefined();
    });

    it("should send standardized error messages", () => {
      const errorMessage = WebSocketUtils.error(
        WebSocketErrorCode.PERMISSION_DENIED,
        "Access denied"
      );

      manager.sendStandardMessage(mockWs as any, errorMessage);

      const messages = mockWs.getMessages();
      expect(messages).toHaveLength(1);

      const sentMessage = JSON.parse(messages[0]);
      expect(sentMessage.success).toBe(false);
      expect(sentMessage.error.code).toBe(WebSocketErrorCode.PERMISSION_DENIED);
      expect(sentMessage.error.message).toBe("Access denied");
    });

    it("should validate messages when validation is enabled", () => {
      const config = manager.getConfig();
      expect(config.enableValidation).toBe(true);

      // Send invalid message (missing required meta fields)
      const invalidMessage = {
        type: "TEST",
        data: "test",
        meta: {}, // Missing required fields
      };

      manager.sendStandardMessage(mockWs as any, invalidMessage as any);

      const messages = mockWs.getMessages();
      expect(messages).toHaveLength(1);

      const sentMessage = JSON.parse(messages[0]);
      expect(sentMessage.success).toBe(false);
      expect(sentMessage.error.code).toBe(WebSocketErrorCode.SCHEMA_VALIDATION_FAILED);
    });
  });

  describe("Rate Limiting", () => {
    it("should allow requests within rate limits", () => {
      const result = manager.checkRateLimit(mockWs as any);
      expect(result).toBe(true);
    });

    it("should block requests when rate limit is exceeded", () => {
      const config = manager.getConfig();
      const maxRequests = config.rateLimits.default.maxRequests;

      // Exhaust rate limit
      for (let i = 0; i < maxRequests; i++) {
        manager.checkRateLimit(mockWs as any);
      }

      // Next request should be blocked
      const result = manager.checkRateLimit(mockWs as any);
      expect(result).toBe(false);
    });

    it("should send rate limit error when limit exceeded", () => {
      const config = manager.getConfig();
      const maxRequests = config.rateLimits.default.maxRequests;

      // Exhaust rate limit
      for (let i = 0; i < maxRequests; i++) {
        manager.checkRateLimit(mockWs as any);
      }

      // Try to send message when rate limited
      const testMessage = WebSocketUtils.success("TEST", { data: "test" });
      manager.sendStandardMessage(mockWs as any, testMessage);

      const messages = mockWs.getMessages();
      expect(messages).toHaveLength(1);

      const sentMessage = JSON.parse(messages[0]);
      expect(sentMessage.success).toBe(false);
      expect(sentMessage.error.code).toBe(WebSocketErrorCode.RATE_LIMITED);
    });
  });

  describe("Subscription Management", () => {
    it("should subscribe to topics", () => {
      manager.subscribe(mockWs as any, "test-topic");

      const messages = mockWs.getMessages();
      expect(messages).toHaveLength(1);

      const sentMessage = JSON.parse(messages[0]);
      expect(sentMessage.success).toBe(true);
      expect(sentMessage.type).toBe("SUBSCRIPTION_ADDED");
      expect(sentMessage.data.topic).toBe("test-topic");
    });

    it("should unsubscribe from topics", () => {
      // First subscribe
      manager.subscribe(mockWs as any, "test-topic");
      mockWs.clearMessages();

      // Then unsubscribe
      manager.unsubscribe(mockWs as any, "test-topic");

      const messages = mockWs.getMessages();
      expect(messages).toHaveLength(1);

      const sentMessage = JSON.parse(messages[0]);
      expect(sentMessage.success).toBe(true);
      expect(sentMessage.type).toBe("SUBSCRIPTION_REMOVED");
      expect(sentMessage.data.topic).toBe("test-topic");
    });

    it("should require authentication for subscription operations", () => {
      const unauthenticatedWs = new MockWebSocket({
        userId: "", // No user ID
        token: "",
        isAlive: true,
      });

      manager.subscribe(unauthenticatedWs as any, "test-topic");

      const messages = unauthenticatedWs.getMessages();
      expect(messages).toHaveLength(1);

      const sentMessage = JSON.parse(messages[0]);
      expect(sentMessage.success).toBe(false);
      expect(sentMessage.error.code).toBe(WebSocketErrorCode.AUTH_REQUIRED);
    });
  });

  describe("Heartbeat Management", () => {
    it("should handle heartbeat messages", () => {
      manager.handleHeartbeat(mockWs as any);

      const messages = mockWs.getMessages();
      expect(messages).toHaveLength(1);

      const sentMessage = JSON.parse(messages[0]);
      expect(sentMessage.type).toBe("EVENT");
      expect(sentMessage.event).toBe("HEARTBEAT_ACK");
      expect(sentMessage.data.timestamp).toBeDefined();
    });

    it("should start heartbeat monitoring", () => {
      const startSpy = spyOn(global, "setInterval");
      
      manager.startHeartbeat(mockWs as any);
      
      expect(startSpy).toHaveBeenCalled();
      
      // Clean up
      manager.stopHeartbeat(mockWs.data.userId);
    });

    it("should stop heartbeat monitoring", () => {
      const clearSpy = spyOn(global, "clearInterval");
      
      // Start then stop
      manager.startHeartbeat(mockWs as any);
      manager.stopHeartbeat(mockWs.data.userId);
      
      expect(clearSpy).toHaveBeenCalled();
    });
  });

  describe("Correlation Tracking", () => {
    it("should handle request-response correlation", async () => {
      const config = manager.getConfig();
      expect(config.enableCorrelation).toBe(true);

      const requestMessage = WebSocketUtils.success("TEST_REQUEST", {
        data: "test request",
      });

      // Send request (this would normally return a promise)
      const requestPromise = manager.sendRequest(mockWs as any, requestMessage, 1000);

      // Simulate response
      const responseMessage = WebSocketUtils.success("TEST_RESPONSE", {
        data: "test response",
      });
      responseMessage.meta.correlationId = requestMessage.meta.correlationId;

      // Handle the correlated response
      const handled = manager.handleCorrelatedResponse(mockWs as any, responseMessage);
      expect(handled).toBe(true);

      // The request promise should resolve
      const response = await requestPromise;
      expect(response.data.data).toBe("test response");
    });

    it("should timeout requests without responses", async () => {
      const requestMessage = WebSocketUtils.success("TEST_REQUEST", {
        data: "test request",
      });

      // Send request with short timeout
      const requestPromise = manager.sendRequest(mockWs as any, requestMessage, 100);

      // Should timeout
      await expect(requestPromise).rejects.toThrow("Request timeout after 100ms");
    });
  });

  describe("Enhanced Connection Management", () => {
    it("should initialize enhanced data on connection add", () => {
      manager.add(mockWs as any);

      // Check that enhanced data was created
      const metrics = manager.getMetrics();
      expect(metrics.activeConnections).toBeGreaterThan(0);
    });

    it("should clean up enhanced data on connection remove", () => {
      // Add connection first
      manager.add(mockWs as any);
      
      // Then remove it
      manager.remove(mockWs as any);

      // Enhanced cleanup should have been called
      // This is verified by checking that heartbeat was stopped
      // and subscriptions were cleaned up
    });

    it("should track connection metrics", () => {
      const initialMetrics = manager.getMetrics();
      
      manager.add(mockWs as any);
      
      const updatedMetrics = manager.getMetrics();
      expect(updatedMetrics.activeConnections).toBeGreaterThanOrEqual(
        initialMetrics.activeConnections
      );
    });
  });

  describe("Configuration Management", () => {
    it("should return current configuration", () => {
      const config = manager.getConfig();
      
      expect(config.messageVersion).toBe("1.0.0");
      expect(config.enableCorrelation).toBe(true);
      expect(config.enableValidation).toBe(true);
      expect(config.heartbeatInterval).toBe(30000);
    });

    it("should update configuration", () => {
      const newConfig = {
        heartbeatInterval: 60000,
        enableValidation: false,
      };

      manager.updateConfig(newConfig);
      
      const updatedConfig = manager.getConfig();
      expect(updatedConfig.heartbeatInterval).toBe(60000);
      expect(updatedConfig.enableValidation).toBe(false);
      expect(updatedConfig.messageVersion).toBe("1.0.0"); // Should remain unchanged
    });
  });

  describe("Metrics Collection", () => {
    it("should provide WebSocket metrics", () => {
      const metrics = manager.getMetrics();
      
      expect(metrics).toHaveProperty("messagesPerSecond");
      expect(metrics).toHaveProperty("activeConnections");
      expect(metrics).toHaveProperty("errorRate");
      expect(metrics).toHaveProperty("rateLimitHits");
      expect(metrics).toHaveProperty("correlationTimeouts");
      expect(metrics).toHaveProperty("validationFailures");
    });

    it("should track rate limit hits", () => {
      const initialMetrics = manager.getMetrics();
      const config = manager.getConfig();
      const maxRequests = config.rateLimits.default.maxRequests;

      // Exhaust rate limit to trigger rate limit hit
      for (let i = 0; i <= maxRequests; i++) {
        const testMessage = WebSocketUtils.success("TEST", { data: "test" });
        manager.sendStandardMessage(mockWs as any, testMessage);
      }

      const updatedMetrics = manager.getMetrics();
      expect(updatedMetrics.rateLimitHits).toBeGreaterThan(initialMetrics.rateLimitHits);
    });
  });
});