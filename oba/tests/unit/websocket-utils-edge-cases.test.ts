import { describe, it, expect, mock, beforeEach, afterEach } from "bun:test";
import { WebSocketUtils } from "../../utils/websocket-utils";
import { WebSocketErrorCode } from "../../types/websocket-standardization.types";
import type { ServerWebSocket } from "bun";
import type { CustomWebSocketData } from "../../types/websocket.types";

// Mock WebSocket
const createMockWebSocket = (
  userId: string = "test-user",
  readyState: number = WebSocket.OPEN,
): ServerWebSocket<CustomWebSocketData> => {
  const mockWs = {
    data: {
      userId,
      token: "test-token",
      isAlive: true,
    },
    send: mock(() => {}),
    readyState,
  } as any;

  return mockWs;
};

// Mock WebSocketManager
const createMockManager = () => {
  return {
    broadcastToUser: mock(() => {}),
    broadcast: mock(() => {}),
  } as any;
};

describe("WebSocketUtils - Edge Cases and Error Handling", () => {
  describe("Message Creation Edge Cases", () => {
    it("should handle null data in success message", () => {
      const message = WebSocketUtils.success("TEST_TYPE", null);

      expect(message.success).toBe(true);
      expect(message.type).toBe("TEST_TYPE");
      expect(message.data).toBeNull();
      expect(message.meta).toBeDefined();
    });

    it("should handle undefined data in success message", () => {
      const message = WebSocketUtils.success("TEST_TYPE", undefined);

      expect(message.success).toBe(true);
      expect(message.type).toBe("TEST_TYPE");
      expect(message.data).toBeUndefined();
      expect(message.meta).toBeDefined();
    });

    it("should handle complex nested data structures", () => {
      const complexData = {
        user: {
          id: "123",
          profile: {
            name: "Test User",
            settings: {
              theme: "dark",
              notifications: true,
            },
          },
        },
        metadata: {
          timestamp: new Date(),
          version: "1.0.0",
        },
        array: [1, 2, 3, { nested: "value" }],
      };

      const message = WebSocketUtils.success("COMPLEX_DATA", complexData);

      expect(message.success).toBe(true);
      expect(message.data).toEqual(complexData);
      expect(message.meta).toBeDefined();
    });

    it("should handle numeric message types", () => {
      const message = WebSocketUtils.success(42, { test: "data" });

      expect(message.success).toBe(true);
      expect(message.type).toBe(42);
      expect(message.meta).toBeDefined();
    });

    it("should handle empty string message types", () => {
      const message = WebSocketUtils.success("", { test: "data" });

      expect(message.success).toBe(true);
      expect(message.type).toBe("");
      expect(message.meta).toBeDefined();
    });

    it("should handle very long error messages", () => {
      const longMessage = "A".repeat(1000);
      const message = WebSocketUtils.error(
        WebSocketErrorCode.INTERNAL_ERROR,
        longMessage,
      );

      expect(message.success).toBe(false);
      expect(message.error.message).toBe(longMessage);
      expect(message.error.message.length).toBe(1000);
    });

    it("should handle special characters in error messages", () => {
      const specialMessage = "Error with special chars: 你好 🚀 <script>alert('xss')</script>";
      const message = WebSocketUtils.error(
        WebSocketErrorCode.INVALID_DATA,
        specialMessage,
      );

      expect(message.success).toBe(false);
      expect(message.error.message).toBe(specialMessage);
    });
  });

  describe("Serialization Edge Cases", () => {
    it("should handle circular references gracefully", () => {
      const circularObj: any = { name: "test" };
      circularObj.self = circularObj;

      const message = {
        type: "TEST",
        data: circularObj,
        meta: {
          messageId: "test-id",
          timestamp: new Date(),
          version: "1.0.0",
          source: "server" as const,
        },
      };

      expect(() => WebSocketUtils.serialize(message)).toThrow();
    });

    it("should handle very large messages", () => {
      const largeData = {
        content: "x".repeat(100000), // 100KB of data
        metadata: Array.from({ length: 1000 }, (_, i) => ({
          id: i,
          value: `item-${i}`,
        })),
      };

      const message = WebSocketUtils.success("LARGE_MESSAGE", largeData);
      const serialized = WebSocketUtils.serialize(message);

      expect(typeof serialized).toBe("string");
      expect(serialized.length).toBeGreaterThan(100000);
    });

    it("should handle messages with undefined values", () => {
      const dataWithUndefined = {
        defined: "value",
        undefined: undefined,
        null: null,
        empty: "",
      };

      const message = WebSocketUtils.success("UNDEFINED_TEST", dataWithUndefined);
      const serialized = WebSocketUtils.serialize(message);
      const parsed = JSON.parse(serialized);

      expect(parsed.data.defined).toBe("value");
      expect(parsed.data.null).toBeNull();
      expect(parsed.data.empty).toBe("");
      expect("undefined" in parsed.data).toBe(false); // undefined should be omitted
    });
  });

  describe("WebSocket Connection Edge Cases", () => {
    it("should handle WebSocket send throwing an error", () => {
      const ws = createMockWebSocket();
      ws.send = mock(() => {
        throw new Error("Network error");
      });

      const message = WebSocketUtils.success("TEST_TYPE", {});

      // Should not throw, but handle the error gracefully
      expect(() => WebSocketUtils.send(ws, message)).not.toThrow();
      expect(ws.send).toHaveBeenCalledTimes(1);
    });

    it("should handle WebSocket in CONNECTING state", () => {
      const ws = createMockWebSocket("test-user", WebSocket.CONNECTING);
      const message = WebSocketUtils.success("TEST_TYPE", {});

      WebSocketUtils.send(ws, message);

      expect(ws.send).not.toHaveBeenCalled();
    });

    it("should handle WebSocket in CLOSING state", () => {
      const ws = createMockWebSocket("test-user", WebSocket.CLOSING);
      const message = WebSocketUtils.success("TEST_TYPE", {});

      WebSocketUtils.send(ws, message);

      expect(ws.send).not.toHaveBeenCalled();
    });

    it("should handle broadcast with empty socket set", () => {
      const sockets = new Set<ServerWebSocket<CustomWebSocketData>>();
      const message = WebSocketUtils.success("TEST_TYPE", {});

      expect(() => WebSocketUtils.broadcast(sockets, message)).not.toThrow();
    });

    it("should handle broadcast with all closed sockets", () => {
      const ws1 = createMockWebSocket("user1", WebSocket.CLOSED);
      const ws2 = createMockWebSocket("user2", WebSocket.CLOSED);
      const sockets = new Set([ws1, ws2]);
      const message = WebSocketUtils.success("TEST_TYPE", {});

      WebSocketUtils.broadcast(sockets, message);

      expect(ws1.send).not.toHaveBeenCalled();
      expect(ws2.send).not.toHaveBeenCalled();
    });

    it("should handle broadcast with mixed socket states", () => {
      const ws1 = createMockWebSocket("user1", WebSocket.OPEN);
      const ws2 = createMockWebSocket("user2", WebSocket.CLOSED);
      const ws3 = createMockWebSocket("user3", WebSocket.CONNECTING);
      const ws4 = createMockWebSocket("user4", WebSocket.CLOSING);
      const sockets = new Set([ws1, ws2, ws3, ws4]);
      const message = WebSocketUtils.success("TEST_TYPE", {});

      WebSocketUtils.broadcast(sockets, message);

      expect(ws1.send).toHaveBeenCalledTimes(1);
      expect(ws2.send).not.toHaveBeenCalled();
      expect(ws3.send).not.toHaveBeenCalled();
      expect(ws4.send).not.toHaveBeenCalled();
    });

    it("should handle broadcast with filter that throws error", () => {
      const ws1 = createMockWebSocket("user1");
      const ws2 = createMockWebSocket("user2");
      const sockets = new Set([ws1, ws2]);
      const message = WebSocketUtils.success("TEST_TYPE", {});

      const faultyFilter = mock(() => {
        throw new Error("Filter error");
      });

      // Should not throw, but handle the error gracefully
      expect(() =>
        WebSocketUtils.broadcast(sockets, message, { filter: faultyFilter }),
      ).not.toThrow();
    });
  });

  describe("Manager Integration Edge Cases", () => {
    it("should handle manager throwing error in sendToUser", () => {
      const manager = {
        broadcastToUser: mock(() => {
          throw new Error("Manager error");
        }),
      } as any;

      const message = WebSocketUtils.success("TEST_TYPE", {});
      const result = WebSocketUtils.sendToUser("test-user", message, manager);

      expect(result).toBe(false);
      expect(manager.broadcastToUser).toHaveBeenCalledTimes(1);
    });

    it("should handle manager throwing error in sendToChannel", () => {
      const manager = {
        broadcast: mock(() => {
          throw new Error("Manager error");
        }),
      } as any;

      const message = WebSocketUtils.success("TEST_TYPE", {});

      // Should not throw, but handle the error gracefully
      expect(() =>
        WebSocketUtils.sendToChannel("test-channel", message, manager),
      ).not.toThrow();
      expect(manager.broadcast).toHaveBeenCalledTimes(1);
    });

    it("should handle null/undefined manager", () => {
      const message = WebSocketUtils.success("TEST_TYPE", {});

      // sendToUser returns false instead of throwing for null manager
      const result1 = WebSocketUtils.sendToUser("test-user", message, null as any);
      expect(result1).toBe(false);

      // sendToChannel should handle null manager gracefully (logs error but doesn't throw)
      expect(() =>
        WebSocketUtils.sendToChannel("test-channel", message, null as any),
      ).not.toThrow();
    });
  });

  describe("Validation Edge Cases", () => {
    it("should handle validation of messages with missing meta fields", () => {
      const invalidMessage = {
        type: "TEST",
        meta: {
          messageId: "test-id",
          // Missing timestamp, version, source
        },
      };

      const isValid = WebSocketUtils.validate(invalidMessage);
      expect(isValid).toBe(false);
    });

    it("should handle validation of messages with invalid meta types", () => {
      const invalidMessage = {
        type: "TEST",
        meta: {
          messageId: 123, // Should be string
          timestamp: new Date(), // Should be string
          version: null,
          source: "invalid",
        },
      };

      const isValid = WebSocketUtils.validate(invalidMessage);
      expect(isValid).toBe(false);
    });

    it("should handle validation of deeply nested objects", () => {
      const deeplyNested: any = { level: 0 };
      let current = deeplyNested;
      for (let i = 1; i < 100; i++) {
        current.next = { level: i };
        current = current.next;
      }

      const message = {
        type: "DEEP_NESTED",
        data: deeplyNested,
        meta: {
          messageId: "test-id",
          timestamp: new Date(),
          version: "1.0.0",
          source: "server",
        },
      };

      const isValid = WebSocketUtils.validate(message);
      expect(isValid).toBe(true);
    });
  });

  describe("Error Factory Edge Cases", () => {
    it("should handle creating errors with null/undefined parameters", () => {
      const message1 = WebSocketUtils.authenticationRequired(undefined);
      expect(message1.meta.correlationId).toBeUndefined();

      const message2 = WebSocketUtils.permissionDenied(null as any);
      expect(message2.error.message).toBe("Permission denied");

      const message3 = WebSocketUtils.notFound("");
      expect(message3.error.message).toBe(" not found");
    });

    it("should handle creating validation errors with empty array", () => {
      const message = WebSocketUtils.validationError([]);
      expect(message.error.details?.validationErrors).toEqual([]);
    });

    it("should handle creating validation errors with malformed error objects", () => {
      const malformedErrors = [
        { field: null, message: undefined, code: "" } as any,
        { field: "test", message: "error", code: null } as any,
      ];

      const message = WebSocketUtils.validationError(malformedErrors);
      expect(message.error.details?.validationErrors).toEqual(malformedErrors);
    });

    it("should handle rate limited error with negative retry time", () => {
      const message = WebSocketUtils.rateLimited(-1);
      expect(message.error.details?.retryAfter).toBe(-1);
    });

    it("should handle message too large with zero sizes", () => {
      const message = WebSocketUtils.messageTooLarge(0, 0);
      expect(message.error.details?.currentSize).toBe(0);
      expect(message.error.details?.maxSize).toBe(0);
    });
  });

  describe("Concurrency and Performance Edge Cases", () => {
    it("should handle rapid message creation", () => {
      const messages = [];
      const start = Date.now();

      for (let i = 0; i < 1000; i++) {
        messages.push(WebSocketUtils.success(`TYPE_${i}`, { index: i }));
      }

      const end = Date.now();
      const duration = end - start;

      expect(messages.length).toBe(1000);
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
      
      // Verify all messages have unique IDs
      const messageIds = messages.map(m => m.meta.id);
      const uniqueIds = new Set(messageIds);
      expect(uniqueIds.size).toBe(1000);
    });

    it("should handle concurrent broadcast operations", async () => {
      const ws1 = createMockWebSocket("user1");
      const ws2 = createMockWebSocket("user2");
      const sockets = new Set([ws1, ws2]);

      const promises = [];
      for (let i = 0; i < 100; i++) {
        promises.push(
          Promise.resolve().then(() => {
            const message = WebSocketUtils.success(`CONCURRENT_${i}`, { index: i });
            WebSocketUtils.broadcast(sockets, message);
          }),
        );
      }

      await Promise.all(promises);

      expect(ws1.send).toHaveBeenCalledTimes(100);
      expect(ws2.send).toHaveBeenCalledTimes(100);
    });
  });

  describe("Memory Management", () => {
    it("should not leak memory with large number of message creations", () => {
      // Create a large number of messages to test for memory leaks
      const messages = [];
      
      for (let i = 0; i < 10000; i++) {
        const message = WebSocketUtils.success(`TYPE_${i}`, {
          data: `content_${i}`,
          timestamp: new Date(),
        });
        
        // Only keep reference to every 1000th message to allow GC
        if (i % 1000 === 0) {
          messages.push(message);
        }
      }

      expect(messages.length).toBe(10);
      
      // Verify the kept messages are still valid
      messages.forEach((message, index) => {
        expect(message.success).toBe(true);
        expect(message.type).toBe(`TYPE_${index * 1000}`);
        expect(message.meta).toBeDefined();
      });
    });
  });
});