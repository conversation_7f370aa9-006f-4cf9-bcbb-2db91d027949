import { describe, it, expect, beforeEach } from "bun:test";
import { z } from "zod";
import {
  MessageTypeRegistry,
  MessageCategory,
  RateLimitGroup,
  MESSAGE_TYPE_REGISTRY,
  RATE_LIMIT_CONFIG,
  MessageSchemas,
  type IExtendedMessageTypeDefinition,
} from "../../utils/message-type-registry";
import { EventTypes } from "../../constants/eventTypes";
import { SEND_MESSAGES, MANAGE_MESSAGES, JOIN_VOICE } from "../../constants/permissions";

describe("MessageTypeRegistry", () => {
  describe("getMessageType", () => {
    it("should return message type definition for valid type", () => {
      const messageType = MessageTypeRegistry.getMessageType(EventTypes.MESSAGE_SEND);
      
      expect(messageType).toBeDefined();
      expect(messageType?.type).toBe(EventTypes.MESSAGE_SEND);
      expect(messageType?.category).toBe(MessageCategory.MESSAGE);
      expect(messageType?.requiresAuth).toBe(true);
      expect(messageType?.requiresPermission).toBe(SEND_MESSAGES);
    });

    it("should return undefined for invalid type", () => {
      const messageType = MessageTypeRegistry.getMessageType("INVALID_TYPE");
      expect(messageType).toBeUndefined();
    });
  });

  describe("hasMessageType", () => {
    it("should return true for existing message type", () => {
      expect(MessageTypeRegistry.hasMessageType(EventTypes.MESSAGE_SEND)).toBe(true);
    });

    it("should return false for non-existing message type", () => {
      expect(MessageTypeRegistry.hasMessageType("INVALID_TYPE")).toBe(false);
    });
  });

  describe("getMessageTypesByCategory", () => {
    it("should return all message types in MESSAGE category", () => {
      const messageTypes = MessageTypeRegistry.getMessageTypesByCategory(MessageCategory.MESSAGE);
      
      expect(messageTypes.length).toBeGreaterThan(0);
      expect(messageTypes.every(type => type.category === MessageCategory.MESSAGE)).toBe(true);
      
      const messageSendType = messageTypes.find(type => type.type === EventTypes.MESSAGE_SEND);
      expect(messageSendType).toBeDefined();
    });

    it("should return all message types in VOICE category", () => {
      const messageTypes = MessageTypeRegistry.getMessageTypesByCategory(MessageCategory.VOICE);
      
      expect(messageTypes.length).toBeGreaterThan(0);
      expect(messageTypes.every(type => type.category === MessageCategory.VOICE)).toBe(true);
    });

    it("should return empty array for non-existing category", () => {
      const messageTypes = MessageTypeRegistry.getMessageTypesByCategory("INVALID_CATEGORY" as MessageCategory);
      expect(messageTypes).toEqual([]);
    });
  });

  describe("getMessageTypesByPermission", () => {
    it("should return message types that require SEND_MESSAGES permission", () => {
      const messageTypes = MessageTypeRegistry.getMessageTypesByPermission(SEND_MESSAGES);
      
      expect(messageTypes.length).toBeGreaterThan(0);
      expect(messageTypes.every(type => type.requiresPermission === SEND_MESSAGES)).toBe(true);
    });

    it("should return message types that require MANAGE_MESSAGES permission", () => {
      const messageTypes = MessageTypeRegistry.getMessageTypesByPermission(MANAGE_MESSAGES);
      
      expect(messageTypes.length).toBeGreaterThan(0);
      expect(messageTypes.every(type => type.requiresPermission === MANAGE_MESSAGES)).toBe(true);
    });
  });

  describe("getMessageTypesByRateLimit", () => {
    it("should return message types in MESSAGING rate limit group", () => {
      const messageTypes = MessageTypeRegistry.getMessageTypesByRateLimit(RateLimitGroup.MESSAGING);
      
      expect(messageTypes.length).toBeGreaterThan(0);
      expect(messageTypes.every(type => type.rateLimitGroup === RateLimitGroup.MESSAGING)).toBe(true);
    });

    it("should return message types in VOICE rate limit group", () => {
      const messageTypes = MessageTypeRegistry.getMessageTypesByRateLimit(RateLimitGroup.VOICE);
      
      expect(messageTypes.length).toBeGreaterThan(0);
      expect(messageTypes.every(type => type.rateLimitGroup === RateLimitGroup.VOICE)).toBe(true);
    });
  });

  describe("getRateLimitConfig", () => {
    it("should return rate limit config for MESSAGE_SEND", () => {
      const config = MessageTypeRegistry.getRateLimitConfig(EventTypes.MESSAGE_SEND);
      
      expect(config).toBeDefined();
      expect(config?.maxRequests).toBe(RATE_LIMIT_CONFIG[RateLimitGroup.MESSAGING].maxRequests);
      expect(config?.windowMs).toBe(RATE_LIMIT_CONFIG[RateLimitGroup.MESSAGING].windowMs);
    });

    it("should return undefined for message type without rate limit group", () => {
      // First register a message type without rate limit group
      const testType: IExtendedMessageTypeDefinition = {
        type: "TEST_TYPE",
        category: MessageCategory.SYSTEM,
        direction: "server_to_client",
        requiresAuth: false,
        description: "Test message type",
      };
      
      MessageTypeRegistry.registerMessageType("TEST_TYPE", testType);
      
      const config = MessageTypeRegistry.getRateLimitConfig("TEST_TYPE");
      expect(config).toBeUndefined();
      
      // Clean up
      MessageTypeRegistry.unregisterMessageType("TEST_TYPE");
    });
  });

  describe("requiresAuth", () => {
    it("should return true for MESSAGE_SEND", () => {
      expect(MessageTypeRegistry.requiresAuth(EventTypes.MESSAGE_SEND)).toBe(true);
    });

    it("should return false for ERROR", () => {
      expect(MessageTypeRegistry.requiresAuth(EventTypes.ERROR)).toBe(false);
    });

    it("should return false for non-existing message type", () => {
      expect(MessageTypeRegistry.requiresAuth("INVALID_TYPE")).toBe(false);
    });
  });

  describe("getRequiredPermission", () => {
    it("should return SEND_MESSAGES for MESSAGE_SEND", () => {
      const permission = MessageTypeRegistry.getRequiredPermission(EventTypes.MESSAGE_SEND);
      expect(permission).toBe(SEND_MESSAGES);
    });

    it("should return undefined for message type without permission requirement", () => {
      const permission = MessageTypeRegistry.getRequiredPermission(EventTypes.ERROR);
      expect(permission).toBeUndefined();
    });
  });

  describe("getDataSchema", () => {
    it("should return schema for MESSAGE_SEND", () => {
      const schema = MessageTypeRegistry.getDataSchema(EventTypes.MESSAGE_SEND);
      
      expect(schema).toBeDefined();
      expect(schema).toBe(MessageSchemas.MessageSend);
    });

    it("should validate correct data with MESSAGE_SEND schema", () => {
      const schema = MessageTypeRegistry.getDataSchema(EventTypes.MESSAGE_SEND);
      
      const validData = {
        content: "Hello, world!",
        channelId: "123e4567-e89b-12d3-a456-************",
      };
      
      const result = schema?.safeParse(validData);
      expect(result?.success).toBe(true);
    });

    it("should reject invalid data with MESSAGE_SEND schema", () => {
      const schema = MessageTypeRegistry.getDataSchema(EventTypes.MESSAGE_SEND);
      
      const invalidData = {
        content: "", // Empty content should fail
        channelId: "invalid-uuid",
      };
      
      const result = schema?.safeParse(invalidData);
      expect(result?.success).toBe(false);
    });
  });

  describe("supportsCorrelation", () => {
    it("should return true for MESSAGE_SEND", () => {
      expect(MessageTypeRegistry.supportsCorrelation(EventTypes.MESSAGE_SEND)).toBe(true);
    });

    it("should return false for message type that doesn't support correlation", () => {
      // Register a test message type without correlation support
      const testType: IExtendedMessageTypeDefinition = {
        type: "TEST_NO_CORRELATION",
        category: MessageCategory.SYSTEM,
        direction: "server_to_client",
        requiresAuth: false,
        supportsCorrelation: false,
        description: "Test message type without correlation",
      };
      
      MessageTypeRegistry.registerMessageType("TEST_NO_CORRELATION", testType);
      
      expect(MessageTypeRegistry.supportsCorrelation("TEST_NO_CORRELATION")).toBe(false);
      
      // Clean up
      MessageTypeRegistry.unregisterMessageType("TEST_NO_CORRELATION");
    });
  });

  describe("getMaxSize", () => {
    it("should return max size for MESSAGE_SEND", () => {
      const maxSize = MessageTypeRegistry.getMaxSize(EventTypes.MESSAGE_SEND);
      expect(maxSize).toBe(4096);
    });

    it("should return larger max size for VOICE_DATA_SEND", () => {
      const maxSize = MessageTypeRegistry.getMaxSize(EventTypes.VOICE_DATA_SEND);
      expect(maxSize).toBe(65536);
    });
  });

  describe("getResponseType", () => {
    it("should return MESSAGE_SENT for MESSAGE_SEND", () => {
      const responseType = MessageTypeRegistry.getResponseType(EventTypes.MESSAGE_SEND);
      expect(responseType).toBe(EventTypes.MESSAGE_SENT);
    });

    it("should return undefined for message type without response", () => {
      const responseType = MessageTypeRegistry.getResponseType(EventTypes.ERROR);
      expect(responseType).toBeUndefined();
    });
  });

  describe("registerMessageType and unregisterMessageType", () => {
    const testType: IExtendedMessageTypeDefinition = {
      type: "TEST_REGISTER",
      category: MessageCategory.SYSTEM,
      direction: "bidirectional",
      requiresAuth: true,
      description: "Test registration",
    };

    it("should register a new message type", () => {
      MessageTypeRegistry.registerMessageType("TEST_REGISTER", testType);
      
      expect(MessageTypeRegistry.hasMessageType("TEST_REGISTER")).toBe(true);
      
      const registered = MessageTypeRegistry.getMessageType("TEST_REGISTER");
      expect(registered).toEqual(testType);
    });

    it("should unregister a message type", () => {
      MessageTypeRegistry.registerMessageType("TEST_REGISTER", testType);
      
      const result = MessageTypeRegistry.unregisterMessageType("TEST_REGISTER");
      expect(result).toBe(true);
      expect(MessageTypeRegistry.hasMessageType("TEST_REGISTER")).toBe(false);
    });

    it("should return false when unregistering non-existing type", () => {
      const result = MessageTypeRegistry.unregisterMessageType("NON_EXISTING");
      expect(result).toBe(false);
    });
  });

  describe("getMessageTypesByDirection", () => {
    it("should return client-to-server message types", () => {
      const messageTypes = MessageTypeRegistry.getClientToServerMessageTypes();
      
      expect(messageTypes.length).toBeGreaterThan(0);
      expect(messageTypes.every(type => type.direction === "client_to_server")).toBe(true);
      
      const messageSendType = messageTypes.find(type => type.type === EventTypes.MESSAGE_SEND);
      expect(messageSendType).toBeDefined();
    });

    it("should return server-to-client message types", () => {
      const messageTypes = MessageTypeRegistry.getServerToClientMessageTypes();
      
      expect(messageTypes.length).toBeGreaterThan(0);
      expect(messageTypes.every(type => type.direction === "server_to_client")).toBe(true);
      
      const messageSentType = messageTypes.find(type => type.type === EventTypes.MESSAGE_SENT);
      expect(messageSentType).toBeDefined();
    });

    it("should return bidirectional message types", () => {
      const messageTypes = MessageTypeRegistry.getBidirectionalMessageTypes();
      
      // There might not be any bidirectional types in the current registry
      expect(Array.isArray(messageTypes)).toBe(true);
      expect(messageTypes.every(type => type.direction === "bidirectional")).toBe(true);
    });
  });

  describe("validateMessageTypeDefinition", () => {
    it("should validate a correct message type definition", () => {
      const validDefinition: IExtendedMessageTypeDefinition = {
        type: "VALID_TYPE",
        category: MessageCategory.MESSAGE,
        direction: "client_to_server",
        requiresAuth: true,
        rateLimitGroup: RateLimitGroup.MESSAGING,
        description: "Valid message type",
      };
      
      expect(MessageTypeRegistry.validateMessageTypeDefinition(validDefinition)).toBe(true);
    });

    it("should reject definition without required fields", () => {
      const invalidDefinition = {
        category: MessageCategory.MESSAGE,
        direction: "client_to_server",
        requiresAuth: true,
      } as IExtendedMessageTypeDefinition;
      
      expect(MessageTypeRegistry.validateMessageTypeDefinition(invalidDefinition)).toBe(false);
    });

    it("should reject definition with invalid category", () => {
      const invalidDefinition: IExtendedMessageTypeDefinition = {
        type: "INVALID_TYPE",
        category: "INVALID_CATEGORY" as MessageCategory,
        direction: "client_to_server",
        requiresAuth: true,
        description: "Invalid category",
      };
      
      expect(MessageTypeRegistry.validateMessageTypeDefinition(invalidDefinition)).toBe(false);
    });

    it("should reject definition with invalid direction", () => {
      const invalidDefinition: IExtendedMessageTypeDefinition = {
        type: "INVALID_TYPE",
        category: MessageCategory.MESSAGE,
        direction: "invalid_direction" as any,
        requiresAuth: true,
        description: "Invalid direction",
      };
      
      expect(MessageTypeRegistry.validateMessageTypeDefinition(invalidDefinition)).toBe(false);
    });

    it("should reject definition with invalid rate limit group", () => {
      const invalidDefinition: IExtendedMessageTypeDefinition = {
        type: "INVALID_TYPE",
        category: MessageCategory.MESSAGE,
        direction: "client_to_server",
        requiresAuth: true,
        rateLimitGroup: "INVALID_GROUP" as RateLimitGroup,
        description: "Invalid rate limit group",
      };
      
      expect(MessageTypeRegistry.validateMessageTypeDefinition(invalidDefinition)).toBe(false);
    });
  });

  describe("Rate Limit Configuration", () => {
    it("should have valid rate limit config for all groups", () => {
      Object.values(RateLimitGroup).forEach(group => {
        const config = RATE_LIMIT_CONFIG[group];
        expect(config).toBeDefined();
        expect(config.maxRequests).toBeGreaterThan(0);
        expect(config.windowMs).toBeGreaterThan(0);
      });
    });

    it("should have appropriate limits for different groups", () => {
      // Messaging should have moderate limits
      expect(RATE_LIMIT_CONFIG[RateLimitGroup.MESSAGING].maxRequests).toBe(30);
      
      // Voice should have high limits for real-time data
      expect(RATE_LIMIT_CONFIG[RateLimitGroup.VOICE].maxRequests).toBe(1000);
      
      // Server management should have low limits
      expect(RATE_LIMIT_CONFIG[RateLimitGroup.SERVER_MANAGEMENT].maxRequests).toBe(10);
      
      // System should have high limits
      expect(RATE_LIMIT_CONFIG[RateLimitGroup.SYSTEM].maxRequests).toBe(1000);
    });
  });

  describe("Message Registry Completeness", () => {
    it("should have entries for all major event types", () => {
      const majorEvents = [
        EventTypes.MESSAGE_SEND,
        EventTypes.MESSAGE_SENT,
        EventTypes.VOICE_JOIN,
        EventTypes.SERVER_CREATE,
        EventTypes.CHANNEL_CREATE,
        EventTypes.ERROR,
      ];
      
      majorEvents.forEach(eventType => {
        expect(MessageTypeRegistry.hasMessageType(eventType)).toBe(true);
      });
    });

    it("should have consistent request-response pairs", () => {
      const requestTypes = MessageTypeRegistry.getClientToServerMessageTypes()
        .filter(type => type.responseType);
      
      requestTypes.forEach(requestType => {
        if (requestType.responseType) {
          expect(MessageTypeRegistry.hasMessageType(requestType.responseType)).toBe(true);
        }
      });
    });

    it("should have appropriate permissions for protected operations", () => {
      const protectedOperations = [
        EventTypes.MESSAGE_SEND,
        EventTypes.MESSAGE_DELETE,
        EventTypes.CHANNEL_CREATE,
        EventTypes.SERVER_UPDATE,
        EventTypes.VOICE_JOIN,
      ];
      
      protectedOperations.forEach(operation => {
        const messageType = MessageTypeRegistry.getMessageType(operation);
        expect(messageType?.requiresAuth).toBe(true);
        if (messageType?.requiresPermission) {
          expect(typeof messageType.requiresPermission).toBe("bigint");
        }
      });
    });
  });
});

describe("MessageSchemas", () => {
  describe("MessageSend schema", () => {
    it("should validate correct message send data", () => {
      const validData = {
        content: "Hello, world!",
        channelId: "123e4567-e89b-12d3-a456-************",
      };
      
      const result = MessageSchemas.MessageSend.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it("should reject empty content", () => {
      const invalidData = {
        content: "",
        channelId: "123e4567-e89b-12d3-a456-************",
      };
      
      const result = MessageSchemas.MessageSend.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it("should reject content that's too long", () => {
      const invalidData = {
        content: "a".repeat(2001), // Exceeds 2000 character limit
        channelId: "123e4567-e89b-12d3-a456-************",
      };
      
      const result = MessageSchemas.MessageSend.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it("should reject invalid UUID for channelId", () => {
      const invalidData = {
        content: "Hello, world!",
        channelId: "invalid-uuid",
      };
      
      const result = MessageSchemas.MessageSend.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it("should accept optional fields", () => {
      const validData = {
        content: "Hello, world!",
        channelId: "123e4567-e89b-12d3-a456-************",
        replyToId: "456e7890-e89b-12d3-a456-************",
        attachments: ["file1.jpg", "file2.png"],
      };
      
      const result = MessageSchemas.MessageSend.safeParse(validData);
      expect(result.success).toBe(true);
    });
  });

  describe("VoiceJoin schema", () => {
    it("should validate correct voice join data", () => {
      const validData = {
        channelId: "123e4567-e89b-12d3-a456-************",
      };
      
      const result = MessageSchemas.VoiceJoin.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it("should reject invalid UUID", () => {
      const invalidData = {
        channelId: "invalid-uuid",
      };
      
      const result = MessageSchemas.VoiceJoin.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe("ServerCreate schema", () => {
    it("should validate correct server create data", () => {
      const validData = {
        name: "My Server",
        description: "A great server for chatting",
        icon: "server-icon.png",
      };
      
      const result = MessageSchemas.ServerCreate.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it("should reject empty name", () => {
      const invalidData = {
        name: "",
        description: "A great server for chatting",
      };
      
      const result = MessageSchemas.ServerCreate.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it("should reject name that's too long", () => {
      const invalidData = {
        name: "a".repeat(101), // Exceeds 100 character limit
      };
      
      const result = MessageSchemas.ServerCreate.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it("should accept minimal data", () => {
      const validData = {
        name: "My Server",
      };
      
      const result = MessageSchemas.ServerCreate.safeParse(validData);
      expect(result.success).toBe(true);
    });
  });
});