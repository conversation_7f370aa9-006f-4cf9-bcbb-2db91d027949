import { describe, it, expect, beforeEach, mock } from "bun:test";
import type { ServerWebSocket } from "bun";
import { WebSocketMigrationUtils } from "../../utils/websocket-migration-utils";
import { WebSocketUtils } from "../../utils/websocket-utils";
import { WebSocketCompatibility } from "../../utils/websocket-compatibility";
import {
  type IWebSocketMessage,
  WebSocketErrorCode,
} from "../../types/websocket-standardization.types";
import type { CustomWebSocketData } from "../../types/websocket.types";

// Mock logger
const mockLogger = {
  debug: mock(() => {}),
  info: mock(() => {}),
  warn: mock(() => {}),
  error: mock(() => {}),
  createLogger: mock(() => mockLogger),
};

// Mock the logger service
mock.module("../../services/logger.service", () => ({
  logger: mockLogger,
}));

// Mock WebSocketCompatibility
mock.module("../../utils/websocket-compatibility", () => ({
  WebSocketCompatibility: {
    processIncomingMessage: mock((rawMessage: string, ws: any) => {
      try {
        const parsed = JSON.parse(rawMessage);
        if (parsed.meta) {
          return parsed; // Already standardized
        }
        // Convert legacy to standardized
        return {
          type: parsed.type,
          data: parsed.data,
          meta: {
            messageId: "converted-123",
            timestamp: new Date(),
            version: "1.0.0",
            source: "client",
          },
          target: { userId: ws.data.userId },
        };
      } catch {
        return null;
      }
    }),
    shouldUseLegacyFormat: mock((ws: any) => {
      return ws.data.legacyOnly === true;
    }),
    sendCompatibleMessage: mock(() => {}),
    convertLegacyMessage: mock((message: any) => ({
      type: message.type,
      data: message.data,
      meta: {
        messageId: "converted-456",
        timestamp: new Date(),
        version: "1.0.0",
        source: "server",
      },
    })),
  },
}));

describe("WebSocketMigrationUtils", () => {
  let mockWebSocket: ServerWebSocket<CustomWebSocketData>;
  let mockManager: any;

  beforeEach(() => {
    // Reset mocks
    mockLogger.debug.mockClear();
    mockLogger.info.mockClear();
    mockLogger.warn.mockClear();
    mockLogger.error.mockClear();

    // Create mock WebSocket
    mockWebSocket = {
      data: {
        userId: "test-user-123",
        token: "test-token",
        isAlive: true,
        type: "channel",
      },
      readyState: WebSocket.OPEN,
      send: mock(() => {}),
    } as any;

    // Create mock manager
    mockManager = {
      broadcast: mock(() => {}),
      broadcastToUser: mock(() => {}),
    };
  });

  describe("wrapLegacyHandler", () => {
    it("should wrap legacy handler and call it with extracted parameters", async () => {
      const mockLegacyHandler = mock(async (ws: any, sender: string, data: any) => {
        expect(sender).toBe("test-user-123");
        expect(data).toEqual({ content: "Hello World" });
      });

      const wrappedHandler = WebSocketMigrationUtils.wrapLegacyHandler(mockLegacyHandler, {
        handlerName: "TestHandler",
        enableLogging: true,
      });

      const testMessage: IWebSocketMessage = {
        type: "MESSAGE_SEND",
        data: { content: "Hello World" },
        meta: {
          messageId: "msg-123",
          timestamp: new Date(),
          version: "1.0.0",
          source: "client",
        },
        target: { userId: "test-user-123" },
      };

      await wrappedHandler(mockWebSocket, testMessage);

      expect(mockLegacyHandler).toHaveBeenCalledTimes(1);
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining("Processing message in wrapped legacy handler: TestHandler"),
        undefined,
        expect.any(Object),
      );
    });

    it("should handle legacy handler errors and send error response", async () => {
      const mockLegacyHandler = mock(async () => {
        throw new Error("Handler failed");
      });

      const wrappedHandler = WebSocketMigrationUtils.wrapLegacyHandler(mockLegacyHandler, {
        handlerName: "FailingHandler",
      });

      const testMessage: IWebSocketMessage = {
        type: "TEST",
        data: { test: true },
        meta: {
          messageId: "msg-456",
          timestamp: new Date(),
          version: "1.0.0",
          source: "client",
          correlationId: "corr-123",
        },
      };

      await wrappedHandler(mockWebSocket, testMessage);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining("Error in wrapped legacy handler: FailingHandler"),
        undefined,
        expect.any(Object),
      );
      expect(WebSocketCompatibility.sendCompatibleMessage).toHaveBeenCalled();
    });

    it("should validate input when requested", async () => {
      const mockLegacyHandler = mock(async () => {});

      const wrappedHandler = WebSocketMigrationUtils.wrapLegacyHandler(mockLegacyHandler, {
        validateInput: true,
      });

      // Test with invalid sender (empty string)
      const testMessage: IWebSocketMessage = {
        type: "TEST",
        data: { test: true },
        meta: {
          messageId: "msg-789",
          timestamp: new Date(),
          version: "1.0.0",
          source: "client",
        },
        target: { userId: "" }, // Invalid sender
      };

      await wrappedHandler(mockWebSocket, testMessage);

      expect(mockLegacyHandler).not.toHaveBeenCalled();
      expect(WebSocketCompatibility.sendCompatibleMessage).toHaveBeenCalled();
    });

    it("should disable logging when requested", async () => {
      const mockLegacyHandler = mock(async () => {});

      const wrappedHandler = WebSocketMigrationUtils.wrapLegacyHandler(mockLegacyHandler, {
        enableLogging: false,
      });

      const testMessage: IWebSocketMessage = {
        type: "TEST",
        data: { test: true },
        meta: {
          messageId: "msg-000",
          timestamp: new Date(),
          version: "1.0.0",
          source: "client",
        },
      };

      await wrappedHandler(mockWebSocket, testMessage);

      expect(mockLegacyHandler).toHaveBeenCalled();
      // Should not log processing messages when logging is disabled
      expect(mockLogger.debug).not.toHaveBeenCalledWith(
        expect.stringContaining("Processing message in wrapped legacy handler"),
        undefined,
        expect.any(Object),
      );
    });
  });

  describe("createDualFormatHandler", () => {
    it("should route to legacy handler for legacy format connections", async () => {
      const mockLegacyHandler = mock(async () => {});
      const mockStandardizedHandler = mock(async () => {});

      const dualHandler = WebSocketMigrationUtils.createDualFormatHandler(
        mockLegacyHandler,
        mockStandardizedHandler,
        { handlerName: "DualHandler" },
      );

      // Mock WebSocket that prefers legacy format
      const legacyWs = {
        ...mockWebSocket,
        data: { ...mockWebSocket.data, legacyOnly: true },
      } as any;

      const legacyMessage = JSON.stringify({
        type: "MESSAGE_SEND",
        data: { content: "Hello" },
        sender: "user123",
      });

      await dualHandler(legacyWs, legacyMessage);

      expect(mockLegacyHandler).toHaveBeenCalled();
      expect(mockStandardizedHandler).not.toHaveBeenCalled();
    });

    it("should route to standardized handler for standardized format connections", async () => {
      const mockLegacyHandler = mock(async () => {});
      const mockStandardizedHandler = mock(async () => {});

      const dualHandler = WebSocketMigrationUtils.createDualFormatHandler(
        mockLegacyHandler,
        mockStandardizedHandler,
        { preferStandardized: true },
      );

      const standardizedMessage = JSON.stringify({
        type: "MESSAGE_SEND",
        data: { content: "Hello" },
        meta: {
          messageId: "msg-123",
          timestamp: new Date(),
          version: "1.0.0",
          source: "client",
        },
      });

      await dualHandler(mockWebSocket, standardizedMessage);

      expect(mockStandardizedHandler).toHaveBeenCalled();
      expect(mockLegacyHandler).not.toHaveBeenCalled();
    });

    it("should handle processing errors gracefully", async () => {
      const mockLegacyHandler = mock(async () => {});
      const mockStandardizedHandler = mock(async () => {});

      const dualHandler = WebSocketMigrationUtils.createDualFormatHandler(
        mockLegacyHandler,
        mockStandardizedHandler,
      );

      // Invalid JSON message
      await dualHandler(mockWebSocket, "invalid json");

      expect(mockLegacyHandler).not.toHaveBeenCalled();
      expect(mockStandardizedHandler).not.toHaveBeenCalled();
    });
  });

  describe("migrateLegacyBroadcast", () => {
    it("should convert legacy message and broadcast to channel", () => {
      const legacyMessage = {
        type: "MESSAGE_SENT",
        data: { content: "Hello" },
        userId: "user123",
      };

      WebSocketMigrationUtils.migrateLegacyBroadcast(
        mockManager,
        legacyMessage,
        "server123",
        "channel456",
        "excludeUser789",
      );

      expect(WebSocketCompatibility.convertLegacyMessage).toHaveBeenCalledWith(
        legacyMessage,
        { source: "server" },
      );
      expect(mockLogger.debug).toHaveBeenCalledWith(
        "Successfully migrated legacy broadcast",
        undefined,
        expect.any(Object),
      );
    });

    it("should broadcast to entire server when no channel specified", () => {
      const legacyMessage = {
        type: "SERVER_UPDATE",
        data: { name: "New Server Name" },
      };

      WebSocketMigrationUtils.migrateLegacyBroadcast(
        mockManager,
        legacyMessage,
        "server123",
      );

      expect(mockManager.broadcast).toHaveBeenCalled();
    });

    it("should handle broadcast errors gracefully", () => {
      const legacyMessage = {
        type: "TEST",
        data: null,
      };

      // Mock convertLegacyMessage to throw error
      const originalConvert = WebSocketCompatibility.convertLegacyMessage;
      (WebSocketCompatibility.convertLegacyMessage as any).mockImplementationOnce(() => {
        throw new Error("Conversion failed");
      });

      WebSocketMigrationUtils.migrateLegacyBroadcast(
        mockManager,
        legacyMessage,
        "server123",
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        "Failed to migrate legacy broadcast",
        undefined,
        expect.any(Object),
      );

      // Restore original mock
      (WebSocketCompatibility.convertLegacyMessage as any).mockImplementation(originalConvert);
    });
  });

  describe("createMigrationWrapper", () => {
    it("should create wrapper with original manager methods", () => {
      const wrapper = WebSocketMigrationUtils.createMigrationWrapper(mockManager);

      expect(wrapper.broadcast).toBe(mockManager.broadcast);
      expect(wrapper.broadcastToUser).toBe(mockManager.broadcastToUser);
      expect(wrapper.broadcastCompatible).toBeDefined();
      expect(wrapper.sendToUserCompatible).toBeDefined();
    });

    it("should handle compatible broadcast with standardized message", () => {
      const wrapper = WebSocketMigrationUtils.createMigrationWrapper(mockManager);
      const standardizedMessage = WebSocketUtils.success("TEST", { data: "test" });

      wrapper.broadcastCompatible(standardizedMessage, "server123", "channel456");

      // Should not call convertLegacyMessage since it's already standardized
      expect(mockLogger.error).not.toHaveBeenCalled();
    });

    it("should handle compatible broadcast with legacy message", () => {
      const wrapper = WebSocketMigrationUtils.createMigrationWrapper(mockManager);
      const legacyMessage = {
        type: "MESSAGE_SENT",
        data: { content: "Hello" },
      };

      wrapper.broadcastCompatible(legacyMessage, "server123", "channel456");

      expect(WebSocketCompatibility.convertLegacyMessage).toHaveBeenCalled();
    });

    it("should handle sendToUserCompatible with standardized message", () => {
      const wrapper = WebSocketMigrationUtils.createMigrationWrapper(mockManager);
      const standardizedMessage = WebSocketUtils.success("TEST", { data: "test" });

      wrapper.sendToUserCompatible("user123", standardizedMessage);

      // Should not call convertLegacyMessage since it's already standardized
      expect(mockLogger.error).not.toHaveBeenCalled();
    });
  });

  describe("createMigrationHelpers", () => {
    it("should create helpers with all required methods", () => {
      const helpers = WebSocketMigrationUtils.createMigrationHelpers();

      expect(helpers.serializeMessage).toBeDefined();
      expect(helpers.createSuccessResponse).toBeDefined();
      expect(helpers.createErrorResponse).toBeDefined();
      expect(helpers.createEventMessage).toBeDefined();
    });

    it("should serialize standardized message correctly", () => {
      const helpers = WebSocketMigrationUtils.createMigrationHelpers();
      const standardizedMessage = WebSocketUtils.success("TEST", { data: "test" });

      const result = helpers.serializeMessage(standardizedMessage);

      expect(result).toBeDefined();
      expect(() => JSON.parse(result)).not.toThrow();
    });

    it("should serialize legacy message by converting first", () => {
      const helpers = WebSocketMigrationUtils.createMigrationHelpers();
      const legacyMessage = {
        type: "TEST",
        data: { content: "Hello" },
      };

      const result = helpers.serializeMessage(legacyMessage);

      expect(result).toBeDefined();
      expect(() => JSON.parse(result)).not.toThrow();
      expect(WebSocketCompatibility.convertLegacyMessage).toHaveBeenCalled();
    });

    it("should create success response", () => {
      const helpers = WebSocketMigrationUtils.createMigrationHelpers();

      const result = helpers.createSuccessResponse("TEST_SUCCESS", { data: "test" }, "corr-123");

      expect(result.type).toBe("TEST_SUCCESS");
      expect(result.success).toBe(true);
      expect(result.data).toEqual({ data: "test" });
      expect(result.meta.correlationId).toBe("corr-123");
    });

    it("should create error response", () => {
      const helpers = WebSocketMigrationUtils.createMigrationHelpers();

      const result = helpers.createErrorResponse("Test error", "TEST_ERROR", "corr-456");

      expect(result.type).toBe("ERROR");
      expect(result.success).toBe(false);
      expect(result.error.message).toBe("Test error");
      expect(result.meta.correlationId).toBe("corr-456");
    });

    it("should create event message", () => {
      const helpers = WebSocketMigrationUtils.createMigrationHelpers();

      const result = helpers.createEventMessage("USER_JOINED", { username: "john" }, {
        userId: "user123",
        channelId: "channel456",
      });

      expect(result.type).toBe("EVENT");
      expect(result.event).toBe("USER_JOINED");
      expect(result.data).toEqual({ username: "john" });
      expect(result.target?.userId).toBe("user123");
      expect(result.target?.channelId).toBe("channel456");
    });

    it("should handle serialization errors gracefully", () => {
      const helpers = WebSocketMigrationUtils.createMigrationHelpers();
      
      // Mock WebSocketUtils.serialize to throw error
      const originalSerialize = WebSocketUtils.serialize;
      (WebSocketUtils.serialize as any) = mock(() => {
        throw new Error("Serialization failed");
      });

      const result = helpers.serializeMessage({ type: "TEST" });

      expect(result).toBeDefined();
      expect(mockLogger.error).toHaveBeenCalled();

      // Restore original method
      (WebSocketUtils.serialize as any) = originalSerialize;
    });
  });

  describe("generateMigrationReport", () => {
    it("should generate report for handler not started migration", () => {
      const report = WebSocketMigrationUtils.generateMigrationReport(
        "TestHandler",
        100,
        0,
      );

      expect(report.handlerName).toBe("TestHandler");
      expect(report.totalCalls).toBe(100);
      expect(report.legacyPercentage).toBe(100);
      expect(report.standardizedPercentage).toBe(0);
      expect(report.migrationStatus).toBe("not_started");
      expect(report.recommendation).toContain("Begin migration");
    });

    it("should generate report for handler in progress migration", () => {
      const report = WebSocketMigrationUtils.generateMigrationReport(
        "TestHandler",
        60,
        40,
      );

      expect(report.handlerName).toBe("TestHandler");
      expect(report.totalCalls).toBe(100);
      expect(report.legacyPercentage).toBe(60);
      expect(report.standardizedPercentage).toBe(40);
      expect(report.migrationStatus).toBe("in_progress");
      expect(report.recommendation).toContain("Continue migration");
      expect(report.recommendation).toContain("60.0%");
    });

    it("should generate report for completed migration", () => {
      const report = WebSocketMigrationUtils.generateMigrationReport(
        "TestHandler",
        0,
        100,
      );

      expect(report.handlerName).toBe("TestHandler");
      expect(report.totalCalls).toBe(100);
      expect(report.legacyPercentage).toBe(0);
      expect(report.standardizedPercentage).toBe(100);
      expect(report.migrationStatus).toBe("complete");
      expect(report.recommendation).toContain("Migration complete");
    });

    it("should handle zero calls gracefully", () => {
      const report = WebSocketMigrationUtils.generateMigrationReport(
        "TestHandler",
        0,
        0,
      );

      expect(report.totalCalls).toBe(0);
      expect(report.legacyPercentage).toBe(0);
      expect(report.standardizedPercentage).toBe(0);
      expect(report.migrationStatus).toBe("not_started");
    });
  });
});