import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  afterAll,
} from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  cleanupTestData,
  createUserWithStatus,
  createUsersWithStatuses,
  createAcceptedFriendship,
} from "../helpers";
import {
  updateUserStatus,
  getUserStatus,
  getOnlineFriends,
  updateLastActive,
  setAllUsersOffline,
} from "../../db/utils";
import { UserSchema } from "../../db/schema";
import { eq } from "drizzle-orm";

describe("User Status Management Utility Functions", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  // Update User Status Tests
  test("updateUserStatus should update a user's status", async () => {
    // Create a test user
    const user = await createTestUser("statususer", "<EMAIL>");

    // Update the user's status
    const updatedUser = await updateUserStatus(
      db,
      user.id,
      "AWAY",
      "I am away",
    );

    // Check that the status was updated
    expect(updatedUser).toBeDefined();
    expect(updatedUser.id).toBe(user.id);
    expect(updatedUser.status).toBe("AWAY");
    expect(updatedUser.statusMessage).toBe("I am away");
    expect(updatedUser.lastActive).toBeDefined();

    // Verify in the database
    const dbUser = await db
      .select()
      .from(UserSchema)
      .where(eq(UserSchema.id, user.id))
      .limit(1);

    expect(dbUser.length).toBe(1);
    expect(dbUser[0].status).toBe("AWAY");
    expect(dbUser[0].statusMessage).toBe("I am away");
  });

  test("updateUserStatus should clear status message when not provided", async () => {
    // Create a test user with a status message
    const user = await createUserWithStatus(
      "statususer",
      "<EMAIL>",
      "ONLINE",
      "Initial message",
    );

    // Update the user's status without a message
    const updatedUser = await updateUserStatus(db, user.id, "BUSY");

    // Check that the status was updated and message cleared
    expect(updatedUser).toBeDefined();
    expect(updatedUser.status).toBe("BUSY");
    expect(updatedUser.statusMessage).toBeNull();

    // Verify in the database
    const dbUser = await db
      .select()
      .from(UserSchema)
      .where(eq(UserSchema.id, user.id))
      .limit(1);

    expect(dbUser[0].status).toBe("BUSY");
    expect(dbUser[0].statusMessage).toBeNull();
  });

  test("updateUserStatus should throw error for non-existent user", async () => {
    // Generate a random UUID that doesn't exist
    const nonExistentId = crypto.randomUUID();

    // Try to update a non-existent user
    try {
      await updateUserStatus(db, nonExistentId, "ONLINE");
      // If we get here, the test should fail
      expect(true).toBe(false);
    } catch (error) {
      expect((error as Error).message).toBe("User not found");
    }
  });

  // Get User Status Tests
  test("getUserStatus should return a user's status", async () => {
    // Create a test user with a specific status
    const user = await createUserWithStatus(
      "statususer",
      "<EMAIL>",
      "BUSY",
      "I am busy",
    );

    // Get the user's status
    const userStatus = await getUserStatus(db, user.id);

    // Check the returned status
    expect(userStatus).toBeDefined();
    expect(userStatus.id).toBe(user.id);
    expect(userStatus.username).toBe("statususer");
    expect(userStatus.status).toBe("BUSY");
    expect(userStatus.statusMessage).toBe("I am busy");
    expect(userStatus.lastActive).toBeDefined();
  });

  test("getUserStatus should throw error for non-existent user", async () => {
    // Generate a random UUID that doesn't exist
    const nonExistentId = crypto.randomUUID();

    // Try to get status of a non-existent user
    try {
      await getUserStatus(db, nonExistentId);
      // If we get here, the test should fail
      expect(true).toBe(false);
    } catch (error) {
      expect((error as Error).message).toBe("User not found");
    }
  });

  // Get Online Friends Tests
  test("getOnlineFriends should return online friends", async () => {
    // Create users with different statuses
    const { onlineUser, awayUser, busyUser, invisibleUser, offlineUser } =
      await createUsersWithStatuses();

    // Create a main user
    const mainUser = await createTestUser("mainuser", "<EMAIL>");

    // Create friendships with all users
    await createAcceptedFriendship(mainUser.id, onlineUser.id);
    await createAcceptedFriendship(mainUser.id, awayUser.id);
    await createAcceptedFriendship(mainUser.id, busyUser.id);
    await createAcceptedFriendship(mainUser.id, invisibleUser.id);
    await createAcceptedFriendship(mainUser.id, offlineUser.id);

    // Get online friends
    const onlineFriends = await getOnlineFriends(db, mainUser.id);

    // Check that only online, away, and busy users are returned (not invisible or offline)
    expect(onlineFriends).toBeDefined();
    expect(onlineFriends.length).toBe(3); // online, away, busy

    // Check that the correct users are returned
    const friendIds = onlineFriends.map((f) => f.friend.id);
    expect(friendIds).toContain(onlineUser.id);
    expect(friendIds).toContain(awayUser.id);
    expect(friendIds).toContain(busyUser.id);
    expect(friendIds).not.toContain(invisibleUser.id);
    expect(friendIds).not.toContain(offlineUser.id);

    // Check that the status information is included
    const onlineFriend = onlineFriends.find(
      (f) => f.friend.id === onlineUser.id,
    );
    expect(onlineFriend).toBeDefined();
    expect(onlineFriend?.friend.status).toBe("ONLINE");
    expect(onlineFriend?.friend.statusMessage).toBe("I am online");
  });

  test("getOnlineFriends should return empty array for user with no friends", async () => {
    // Create a user with no friends
    const user = await createTestUser("lonelyuser", "<EMAIL>");

    // Get online friends
    const onlineFriends = await getOnlineFriends(db, user.id);

    // Check that an empty array is returned
    expect(onlineFriends).toBeDefined();
    expect(onlineFriends.length).toBe(0);
  });

  // Update Last Active Tests
  test("updateLastActive should update a user's last active timestamp", async () => {
    // Create a test user
    const user = await createTestUser("activeuser", "<EMAIL>");

    // Update the last active timestamp
    const updatedUser = await updateLastActive(db, user.id);

    // Check that the timestamp was updated
    expect(updatedUser).toBeDefined();
    expect(updatedUser.id).toBe(user.id);
    expect(updatedUser.lastActive).toBeDefined();

    // Verify in the database
    const dbUser = await db
      .select()
      .from(UserSchema)
      .where(eq(UserSchema.id, user.id))
      .limit(1);

    expect(dbUser.length).toBe(1);
    expect(dbUser[0].lastActive).toBeDefined();
  });

  // Set All Users Offline Tests
  test("setAllUsersOffline should set all users to offline", async () => {
    // Create users with different statuses
    await createUsersWithStatuses();

    // Set all users to offline
    const result = await setAllUsersOffline(db);

    // Check that the operation was successful
    expect(result).toBe(true);

    // Verify in the database that all users are offline
    const users = await db.select().from(UserSchema);

    expect(users.length).toBeGreaterThan(0);

    // Check that all users are offline
    for (const user of users) {
      expect(user.status).toBe("OFFLINE");
    }
  });
});
