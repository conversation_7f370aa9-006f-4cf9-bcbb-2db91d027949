import { describe, test, expect, beforeEach, afterEach, mock } from "bun:test";
import { BadgeService } from "../../services/badge.service";
import { BadgeEvaluationService } from "../../services/badge-evaluation.service";
import {
  BadgeNotFoundError,
  BadgeAlreadyAssignedError,
  InsufficientPermissionsError,
  BadgeValidationError
} from "../../class/badge-errors";
import type {
  BadgeType,
  UserBadge,
  CreateBadgeTypeRequest,
  UpdateBadgeTypeRequest,
  BadgeTypeFilters,
  EvaluationResult,
  UserStats,
  BadgeCriteria,
  BadgeProgress,
  BadgeStats,
  BadgeLeaderboard
} from "../../types/badge.types";

// Mock database connection
const mockDb = {
  select: mock(() => ({ 
    from: mock(() => ({ 
      where: mock(() => ({ 
        limit: mock(() => []),
        orderBy: mock(() => [])
      })) 
    })) 
  })),
  insert: mock(() => ({ 
    values: mock(() => ({ 
      returning: mock(() => []),
      onConflictDoNothing: mock(() => ({ returning: mock(() => []) }))
    })) 
  })),
  update: mock(() => ({ 
    set: mock(() => ({ 
      where: mock(() => ({ 
        returning: mock(() => []) 
      })) 
    })) 
  })),
  delete: mock(() => ({ 
    where: mock(() => ({ 
      returning: mock(() => []) 
    })) 
  })),
  transaction: mock((callback) => callback(mockDb))
};

// Mock utility functions
const mockBadgeUtils = {
  createBadgeType: mock(),
  getBadgeTypeById: mock(),
  getBadgeTypes: mock(),
  updateBadgeType: mock(),
  deleteBadgeType: mock(),
  assignBadgeToUser: mock(),
  removeBadgeFromUser: mock(),
  getUserBadges: mock(),
  getUserStats: mock(),
  getBadgeStats: mock(),
  getBadgeLeaderboard: mock(),
  getAvailableBadgesForUser: mock(),
  getBadgeProgress: mock(),
  bulkAssignBadges: mock()
};

const mockBadgeEvaluation = {
  evaluateUserForAutomaticBadges: mock(),
  batchEvaluateUsers: mock(),
  evaluateAllUsersForAutomaticBadges: mock(),
  reevaluateBadgeTypeForAllUsers: mock(),
  getUsersNearBadgeCompletion: mock(),
  evaluateBadgeCriteria: mock()
};

const mockBadgeWebSocket = {
  broadcastBadgeAssigned: mock(),
  broadcastBadgeRemoved: mock(),
  broadcastBadgeProgressUpdate: mock(),
  broadcastBatchBadgeAssigned: mock(),
  broadcastBatchProgressUpdates: mock()
};

const mockPermissions = {
  hasServerPermission: mock()
};

// Mock the imports
mock.module("../../db/utils/badge-utils", () => mockBadgeUtils);
mock.module("../../db/utils/badge-evaluation", () => mockBadgeEvaluation);
mock.module("../../utils/badge-websocket", () => ({ badgeWebSocketService: mockBadgeWebSocket }));
mock.module("../../utils/permissions", () => mockPermissions);
mock.module("../../db", () => ({ db: mockDb }));
mock.module("../../db", () => ({ db: mockDb }));

describe("Badge System Comprehensive Tests", () => {
  let badgeService: BadgeService;
  let badgeEvaluationService: BadgeEvaluationService;
  
  const mockUserId = "550e8400-e29b-41d4-a716-446655440000";
  const mockBadgeTypeId = "550e8400-e29b-41d4-a716-446655440001";
  const mockServerId = "550e8400-e29b-41d4-a716-446655440002";
  const mockAdminId = "550e8400-e29b-41d4-a716-446655440003";

  beforeEach(() => {
    badgeService = new BadgeService(mockDb as any);
    badgeEvaluationService = new BadgeEvaluationService(mockDb as any);
    
    // Reset all mocks
    Object.values(mockBadgeUtils).forEach(mock => mock.mockReset());
    Object.values(mockBadgeEvaluation).forEach(mock => mock.mockReset());
    Object.values(mockBadgeWebSocket).forEach(mock => mock.mockReset());
    Object.values(mockPermissions).forEach(mock => mock.mockReset());
  });

  afterEach(() => {
    // Clean up any test state
  });

  describe("Badge Service - Core Functionality", () => {
    describe("Badge Type Management", () => {
      const validBadgeData: CreateBadgeTypeRequest = {
        name: "Test Badge",
        description: "A test badge for unit testing",
        iconUrl: "https://example.com/icon.png",
        color: "#FFD700",
        category: "achievement",
        assignmentType: "automatic",
        criteria: {
          type: "message_count",
          threshold: 10
        }
      };

      test("should create badge type with valid data", async () => {
        const expectedBadge: BadgeType = {
          id: mockBadgeTypeId,
          badgeId: "test-badge",
          name: validBadgeData.name,
          description: validBadgeData.description,
          icon: validBadgeData.iconUrl || "",
          design: {
            shape: "circle",
            background: validBadgeData.color,
            colors: [validBadgeData.color]
          },
          criteria: {
            requirement: "Send 10 messages",
            tracked: "message_count",
            type: "message_count",
            threshold: 10
          },
          unlockType: "automatic",
          category: validBadgeData.category,
          displayOrder: 1,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockBadgeUtils.getBadgeTypes.mockResolvedValue([]);
        mockBadgeUtils.createBadgeType.mockResolvedValue(expectedBadge);

        const result = await badgeService.createBadgeType(validBadgeData, mockAdminId);

        expect(result).toEqual(expectedBadge);
        expect(mockBadgeUtils.createBadgeType).toHaveBeenCalledWith(mockDb, expect.objectContaining({
          name: validBadgeData.name,
          description: validBadgeData.description,
          category: validBadgeData.category,
          assignmentType: validBadgeData.assignmentType
        }));
      });

      test("should prevent duplicate badge names", async () => {
        const existingBadge: BadgeType = {
          id: "existing-badge-id",
          badgeId: "existing-badge",
          name: "Test Badge",
          description: "Existing badge",
          icon: "https://example.com/existing.png",
          design: { shape: "square", background: "#000000", colors: ["#000000"] },
          criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 1 },
          unlockType: "manual",
          category: "achievement",
          displayOrder: 1,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockBadgeUtils.getBadgeTypes.mockResolvedValue([existingBadge]);

        await expect(badgeService.createBadgeType(validBadgeData, mockAdminId))
          .rejects.toThrow(BadgeValidationError);
      });

      test("should update badge type successfully", async () => {
        const existingBadge: BadgeType = {
          id: mockBadgeTypeId,
          badgeId: "test-badge",
          name: "Original Badge",
          description: "Original description",
          icon: "https://example.com/original.png",
          design: { shape: "circle", background: "#000000", colors: ["#000000"] },
          criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 1 },
          unlockType: "manual",
          category: "achievement",
          displayOrder: 1,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const updates: UpdateBadgeTypeRequest = {
          name: "Updated Badge",
          description: "Updated description"
        };

        const updatedBadge = { ...existingBadge, ...updates };

        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(existingBadge);
        mockBadgeUtils.getBadgeTypes.mockResolvedValue([]);
        mockBadgeUtils.updateBadgeType.mockResolvedValue(updatedBadge);

        const result = await badgeService.updateBadgeType(mockBadgeTypeId, updates, mockAdminId);

        expect(result).toEqual(updatedBadge);
        expect(mockBadgeUtils.updateBadgeType).toHaveBeenCalledWith(mockDb, mockBadgeTypeId, updates);
      });

      test("should delete badge type and cascade to user badges", async () => {
        const existingBadge: BadgeType = {
          id: mockBadgeTypeId,
          badgeId: "test-badge",
          name: "Badge to Delete",
          description: "This badge will be deleted",
          icon: "https://example.com/delete.png",
          design: { shape: "circle", background: "#000000", colors: ["#000000"] },
          criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 1 },
          unlockType: "manual",
          category: "achievement",
          displayOrder: 1,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(existingBadge);
        mockBadgeUtils.deleteBadgeType.mockResolvedValue(true);

        await badgeService.deleteBadgeType(mockBadgeTypeId, mockAdminId);

        expect(mockBadgeUtils.deleteBadgeType).toHaveBeenCalledWith(mockDb, mockBadgeTypeId);
      });

      test("should filter badge types correctly", async () => {
        const filters: BadgeTypeFilters = {
          category: "achievement",
          unlockType: "automatic",
          isActive: true
        };

        const expectedBadges: BadgeType[] = [
          {
            id: "badge-1",
            badgeId: "achievement-badge",
            name: "Achievement Badge",
            description: "An achievement badge",
            icon: "https://example.com/achievement.png",
            design: { shape: "circle", background: "#FFD700", colors: ["#FFD700"] },
            criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 10 },
            unlockType: "automatic",
            category: "achievement",
            displayOrder: 1,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ];

        mockBadgeUtils.getBadgeTypes.mockResolvedValue(expectedBadges);

        const result = await badgeService.getBadgeTypes(filters, 10, 0);

        expect(result).toEqual(expectedBadges);
        expect(mockBadgeUtils.getBadgeTypes).toHaveBeenCalled();
      });
    });

    describe("User Badge Operations", () => {
      const activeBadge: BadgeType = {
        id: mockBadgeTypeId,
        badgeId: "test-badge",
        name: "Test Badge",
        description: "A test badge",
        icon: "https://example.com/test.png",
        design: { shape: "circle", background: "#FFD700", colors: ["#FFD700"] },
        criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 10 },
        unlockType: "manual",
        category: "achievement",
        displayOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      test("should assign badge to user successfully", async () => {
        const expectedUserBadge: UserBadge = {
          id: "user-badge-1",
          userId: mockUserId,
          badgeTypeId: mockBadgeTypeId,
          assignedBy: mockAdminId,
          assignedAt: new Date(),
          isVisible: true,
          badgeType: activeBadge
        };

        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(activeBadge);
        mockBadgeUtils.getUserBadges.mockResolvedValue([]);
        mockBadgeUtils.assignBadgeToUser.mockResolvedValue(expectedUserBadge);
        mockPermissions.hasServerPermission.mockResolvedValue(true);
        mockBadgeWebSocket.broadcastBadgeAssigned.mockResolvedValue(undefined);

        const result = await badgeService.assignBadge(mockUserId, mockBadgeTypeId, mockAdminId, mockServerId);

        expect(result).toEqual(expectedUserBadge);
        expect(mockBadgeUtils.assignBadgeToUser).toHaveBeenCalled();
        expect(mockBadgeWebSocket.broadcastBadgeAssigned).toHaveBeenCalled();
      });

      test("should prevent duplicate badge assignments", async () => {
        const existingUserBadge: UserBadge = {
          id: "existing-user-badge",
          userId: mockUserId,
          badgeTypeId: mockBadgeTypeId,
          assignedAt: new Date(),
          isVisible: true
        };

        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(activeBadge);
        mockBadgeUtils.getUserBadges.mockResolvedValue([existingUserBadge]);

        await expect(badgeService.assignBadge(mockUserId, mockBadgeTypeId, mockAdminId))
          .rejects.toThrow(BadgeAlreadyAssignedError);
      });

      test("should remove badge from user successfully", async () => {
        const existingUserBadge: UserBadge = {
          id: "user-badge-to-remove",
          userId: mockUserId,
          badgeTypeId: mockBadgeTypeId,
          assignedAt: new Date(),
          isVisible: true
        };

        mockBadgeUtils.getBadgeTypeById.mockResolvedValue(activeBadge);
        mockBadgeUtils.getUserBadges.mockResolvedValue([existingUserBadge]);
        mockBadgeUtils.removeBadgeFromUser.mockResolvedValue(true);
        mockPermissions.hasServerPermission.mockResolvedValue(true);
        mockBadgeWebSocket.broadcastBadgeRemoved.mockResolvedValue(undefined);

        await badgeService.removeBadge(mockUserId, mockBadgeTypeId, mockAdminId, mockServerId);

        expect(mockBadgeUtils.removeBadgeFromUser).toHaveBeenCalledWith(mockDb, mockUserId, mockBadgeTypeId);
        expect(mockBadgeWebSocket.broadcastBadgeRemoved).toHaveBeenCalledWith({
          userId: mockUserId,
          badgeTypeId: mockBadgeTypeId,
          badgeType: activeBadge,
          removedBy: mockAdminId
        });
      });

      test("should get user badges with visibility filter", async () => {
        const userBadges: UserBadge[] = [
          {
            id: "visible-badge",
            userId: mockUserId,
            badgeTypeId: "badge-1",
            assignedAt: new Date(),
            isVisible: true
          },
          {
            id: "hidden-badge",
            userId: mockUserId,
            badgeTypeId: "badge-2",
            assignedAt: new Date(),
            isVisible: false
          }
        ];

        mockBadgeUtils.getUserBadges.mockResolvedValue(userBadges);

        const result = await badgeService.getUserBadges(mockUserId, true);

        expect(result).toEqual(userBadges);
        expect(mockBadgeUtils.getUserBadges).toHaveBeenCalledWith(mockDb, mockUserId, true);
      });

      test("should get available badges for user", async () => {
        const availableBadges: BadgeType[] = [
          {
            id: "available-badge-1",
            badgeId: "available-1",
            name: "Available Badge 1",
            description: "A badge the user can earn",
            icon: "https://example.com/available.png",
            design: { shape: "circle", background: "#00FF00", colors: ["#00FF00"] },
            criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 5 },
            unlockType: "automatic",
            category: "achievement",
            displayOrder: 1,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ];

        mockBadgeUtils.getAvailableBadgesForUser.mockResolvedValue(availableBadges);

        const result = await badgeService.getAvailableBadgesForUser(mockUserId);

        expect(result).toEqual(availableBadges);
        expect(mockBadgeUtils.getAvailableBadgesForUser).toHaveBeenCalledWith(mockDb, mockUserId);
      });
    });

    describe("Bulk Operations", () => {
      test("should bulk assign badges to multiple users", async () => {
        const assignments = [
          { userId: "550e8400-e29b-41d4-a716-446655440010", badgeTypeId: "550e8400-e29b-41d4-a716-446655440020" },
          { userId: "550e8400-e29b-41d4-a716-446655440011", badgeTypeId: "550e8400-e29b-41d4-a716-446655440020" },
          { userId: "550e8400-e29b-41d4-a716-446655440012", badgeTypeId: "550e8400-e29b-41d4-a716-446655440021" }
        ];

        const expectedResults: UserBadge[] = [
          {
            id: "bulk-badge-1",
            userId: "550e8400-e29b-41d4-a716-446655440010",
            badgeTypeId: "550e8400-e29b-41d4-a716-446655440020",
            assignedBy: mockAdminId,
            assignedAt: new Date(),
            isVisible: true
          },
          {
            id: "bulk-badge-2",
            userId: "550e8400-e29b-41d4-a716-446655440011",
            badgeTypeId: "550e8400-e29b-41d4-a716-446655440020",
            assignedBy: mockAdminId,
            assignedAt: new Date(),
            isVisible: true
          }
        ];

        mockBadgeUtils.bulkAssignBadges.mockResolvedValue(expectedResults);
        mockPermissions.hasServerPermission.mockResolvedValue(true);
        mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

        const result = await badgeService.bulkAssignBadges(assignments, mockAdminId, mockServerId);

        expect(result).toEqual(expectedResults);
        expect(mockBadgeUtils.bulkAssignBadges).toHaveBeenCalledWith(
          mockDb,
          assignments.map(a => ({ ...a, assignedBy: mockAdminId }))
        );
        expect(mockBadgeWebSocket.broadcastBatchBadgeAssigned).toHaveBeenCalled();
      });
    });
  });

  describe("Badge Evaluation Service", () => {
    const mockUserStats: UserStats = {
      messageCount: 100,
      serverCount: 5,
      friendCount: 10,
      daysActive: 30,
      accountAge: 60,
      lastActive: new Date(),
      invitesSent: 5,
      invitesAccepted: 3,
      feedbackSubmitted: 2,
      moderationActions: 0
    };

    describe("User Evaluation", () => {
      test("should evaluate user for automatic badges", async () => {
        const mockResult: EvaluationResult = {
          userId: mockUserId,
          newBadges: [],
          evaluatedBadges: ["badge-1", "badge-2"],
          collectionProgress: [],
          errors: []
        };

        mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockResolvedValue(mockResult);
        mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

        const result = await badgeEvaluationService.evaluateUser(mockUserId);

        expect(result).toEqual(mockResult);
        expect(mockBadgeEvaluation.evaluateUserForAutomaticBadges).toHaveBeenCalledWith(mockDb, mockUserId);
      });

      test("should handle evaluation errors gracefully", async () => {
        mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockRejectedValue(new Error("Database error"));

        const result = await badgeEvaluationService.evaluateUser(mockUserId);

        expect(result.userId).toBe(mockUserId);
        expect(result.newBadges).toEqual([]);
        expect(result.errors).toHaveLength(1);
        expect(result.errors[0]).toContain("Evaluation failed");
      });

      test("should batch evaluate multiple users", async () => {
        const userIds = ["user-1", "user-2", "user-3"];
        const mockResults: EvaluationResult[] = userIds.map(userId => ({
          userId,
          newBadges: [],
          evaluatedBadges: [],
          collectionProgress: [],
          errors: []
        }));

        mockBadgeEvaluation.batchEvaluateUsers.mockResolvedValue(mockResults);
        mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

        const result = await badgeEvaluationService.evaluateUsers(userIds);

        expect(result).toEqual(mockResults);
        expect(mockBadgeEvaluation.batchEvaluateUsers).toHaveBeenCalledWith(mockDb, userIds);
      });
    });

    describe("Criteria Evaluation", () => {
      test("should check message count criteria", async () => {
        const criteria: BadgeCriteria = {
          requirement: "Send 50 messages",
          tracked: "message_count",
          type: "message_count",
          threshold: 50
        };

        mockBadgeUtils.getUserStats.mockResolvedValue(mockUserStats);
        mockBadgeEvaluation.evaluateBadgeCriteria.mockResolvedValue(true);

        const result = await badgeEvaluationService.checkCriteria(mockUserId, criteria);

        expect(result).toBe(true);
        expect(mockBadgeUtils.getUserStats).toHaveBeenCalledWith(mockDb, mockUserId);
        expect(mockBadgeEvaluation.evaluateBadgeCriteria).toHaveBeenCalledWith(mockUserStats, criteria);
      });

      test("should check criteria for multiple users", async () => {
        const userIds = ["user-1", "user-2"];
        const criteria: BadgeCriteria = {
          requirement: "Send 50 messages",
          tracked: "message_count",
          type: "message_count",
          threshold: 50
        };

        mockBadgeUtils.getUserStats.mockResolvedValue(mockUserStats);
        mockBadgeEvaluation.evaluateBadgeCriteria
          .mockResolvedValueOnce(true)
          .mockResolvedValueOnce(false);

        const result = await badgeEvaluationService.checkCriteriaForUsers(userIds, criteria);

        expect(result.size).toBe(2);
        expect(result.get("user-1")).toBe(true);
        expect(result.get("user-2")).toBe(false);
      });
    });

    describe("Progress Tracking", () => {
      test("should get badge progress for user", async () => {
        const mockBadgeType: BadgeType = {
          id: "progress-badge",
          badgeId: "progress-test",
          name: "Progress Badge",
          description: "A badge to test progress",
          icon: "https://example.com/progress.png",
          design: { shape: "circle", background: "#0000FF", colors: ["#0000FF"] },
          criteria: {
            requirement: "Send 100 messages",
            tracked: "message_count",
            type: "message_count",
            threshold: 100
          },
          unlockType: "automatic",
          category: "achievement",
          displayOrder: 1,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockBadgeUtils.getUserBadges.mockResolvedValue([]);
        mockBadgeUtils.getBadgeTypes.mockResolvedValue([mockBadgeType]);
        mockBadgeUtils.getUserStats.mockResolvedValue(mockUserStats);

        const result = await badgeEvaluationService.getBadgeProgress(mockUserId);

        expect(result).toHaveLength(1);
        expect(result[0].badgeTypeId).toBe("progress-badge");
        expect(result[0].progress).toBe(100); // User has 100 messages, threshold is 100
        expect(result[0].total).toBe(100);
        expect(result[0].isEarned).toBe(false);
      });
    });
  });

  describe("Badge Statistics", () => {
    test("should get badge system statistics", async () => {
      const expectedStats: BadgeStats = {
        totalBadges: 25,
        totalAssignments: 150,
        categoryBreakdown: {
          achievement: 10,
          role: 5,
          special: 3,
          community: 4,
          milestone: 3
        },
        mostPopularBadges: []
      };

      mockBadgeUtils.getBadgeStats.mockResolvedValue(expectedStats);

      const result = await badgeService.getBadgeStats();

      expect(result).toEqual(expectedStats);
      expect(mockBadgeUtils.getBadgeStats).toHaveBeenCalledWith(mockDb);
    });

    test("should get badge leaderboard", async () => {
      const expectedLeaderboard: BadgeLeaderboard[] = [
        {
          userId: "top-user-1",
          username: "TopUser1",
          badgeCount: 15,
          badges: []
        },
        {
          userId: "top-user-2",
          username: "TopUser2",
          badgeCount: 12,
          badges: []
        }
      ];

      mockBadgeUtils.getBadgeLeaderboard.mockResolvedValue(expectedLeaderboard);

      const result = await badgeService.getBadgeLeaderboard(10);

      expect(result).toEqual(expectedLeaderboard);
      expect(mockBadgeUtils.getBadgeLeaderboard).toHaveBeenCalledWith(mockDb, 10);
    });
  });

  describe("Error Handling", () => {
    test("should handle badge not found errors", async () => {
      mockBadgeUtils.getBadgeTypeById.mockResolvedValue(null);

      await expect(badgeService.getBadgeTypeById(mockBadgeTypeId))
        .rejects.toThrow(BadgeNotFoundError);
    });

    test("should handle validation errors", async () => {
      const invalidBadgeData = {
        name: "",
        description: "",
        iconUrl: "",
        color: "",
        category: "achievement" as const,
        assignmentType: "automatic" as const,
        criteria: { type: "message_count" as const, threshold: -1 }
      };

      await expect(badgeService.createBadgeType(invalidBadgeData, mockAdminId))
        .rejects.toThrow();
    });

    test("should handle permission errors", async () => {
      const testBadge: BadgeType = {
        id: mockBadgeTypeId,
        badgeId: "test-badge",
        name: "Test Badge",
        description: "A test badge",
        icon: "https://example.com/test.png",
        design: { shape: "circle", background: "#FFD700", colors: ["#FFD700"] },
        criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 10 },
        unlockType: "manual",
        category: "achievement",
        displayOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // Change badge to manual assignment type to trigger permission check
      testBadge.assignmentType = "manual";
      
      mockBadgeUtils.getBadgeTypeById.mockResolvedValue(testBadge);
      mockBadgeUtils.getUserBadges.mockResolvedValue([]);
      
      // Mock hasServerPermission to return false for permission check
      const mockHasServerPermission = mock(() => Promise.resolve(false));
      mockPermissions.hasServerPermission = mockHasServerPermission;

      await expect(badgeService.assignBadge(mockUserId, mockBadgeTypeId, "unauthorized-user", mockServerId))
        .rejects.toThrow(InsufficientPermissionsError);
    });
  });
});