import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  afterAll,
} from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  cleanupTestData,
  createPendingFriendRequest,
  createAcceptedFriendship,
  createBlockedRelationship,
} from "../helpers";
import {
  sendFriendRequest,
  acceptFriendRequest,
  rejectFriendRequest,
  cancelFriendRequest,
  removeFriend,
  blockUser,
  unblockUser,
  getUserFriends,
  getPendingFriendRequests,
  getSentFriendRequests,
  getBlockedUsers,
  isUserBlocked,
} from "../../db/utils";
import { FriendshipSchema } from "../../db/schema";
import { eq, and, or } from "drizzle-orm";
import crypto from "crypto";

// Global cleanup after all tests
afterAll(async () => {
  await cleanupTestData();
});

describe("Friend Management Utility Functions", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  // Send Friend Request Tests
  test("sendFriendRequest should create a pending friend request", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Send a friend request
    const friendship = await sendFriendRequest(db, user1.id, user2.id);

    // Check that the request was created
    expect(friendship).toBeDefined();
    expect(friendship.userId).toBe(user1.id);
    expect(friendship.friendId).toBe(user2.id);
    expect(friendship.status).toBe("PENDING");
    expect(friendship.initiatedBy).toBe(user1.id);

    // Verify in the database
    const dbFriendship = await db
      .select()
      .from(FriendshipSchema)
      .where(
        and(
          eq(FriendshipSchema.userId, user1.id),
          eq(FriendshipSchema.friendId, user2.id),
        ),
      )
      .limit(1);

    expect(dbFriendship.length).toBe(1);
    expect(dbFriendship[0].status).toBe("PENDING");
  });

  test("sendFriendRequest should auto-accept if other user already sent a request", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a pending request from user2 to user1
    await createPendingFriendRequest(user2.id, user1.id);

    // Send a request from user1 to user2 (should auto-accept)
    const friendship = await sendFriendRequest(db, user1.id, user2.id);

    // Check that the request was auto-accepted
    expect(friendship).toBeDefined();
    expect(friendship.status).toBe("ACCEPTED");

    // Verify in the database
    const dbFriendship = await db
      .select()
      .from(FriendshipSchema)
      .where(
        or(
          and(
            eq(FriendshipSchema.userId, user1.id),
            eq(FriendshipSchema.friendId, user2.id),
          ),
          and(
            eq(FriendshipSchema.userId, user2.id),
            eq(FriendshipSchema.friendId, user1.id),
          ),
        ),
      )
      .limit(1);

    expect(dbFriendship.length).toBe(1);
    expect(dbFriendship[0].status).toBe("ACCEPTED");
  });

  test("sendFriendRequest should reject if users are already friends", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create an accepted friendship
    await createAcceptedFriendship(user1.id, user2.id);

    // Try to send a friend request
    try {
      await sendFriendRequest(db, user1.id, user2.id);
      // If we get here, the test should fail
      expect(true).toBe(false);
    } catch (error) {
      expect((error as Error).message).toBe("Already friends");
    }
  });

  test("sendFriendRequest should reject if request already sent", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a pending request
    await createPendingFriendRequest(user1.id, user2.id);

    // Try to send another request
    try {
      await sendFriendRequest(db, user1.id, user2.id);
      // If we get here, the test should fail
      expect(true).toBe(false);
    } catch (error) {
      expect((error as Error).message).toBe("Friend request already sent");
    }
  });

  test("sendFriendRequest should reject if user is blocked", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a blocked relationship
    await createBlockedRelationship(user1.id, user2.id);

    // Try to send a friend request
    try {
      await sendFriendRequest(db, user1.id, user2.id);
      // If we get here, the test should fail
      expect(true).toBe(false);
    } catch (error) {
      expect((error as Error).message).toBe("Cannot send friend request");
    }
  });

  // Accept Friend Request Tests
  test("acceptFriendRequest should accept a pending request", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a pending request
    const pendingRequest = await createPendingFriendRequest(user1.id, user2.id);

    // Accept the request
    const friendship = await acceptFriendRequest(
      db,
      user2.id,
      pendingRequest.id,
    );

    // Check that the request was accepted
    expect(friendship).toBeDefined();
    expect(friendship.status).toBe("ACCEPTED");
    expect(friendship.initiatedBy).toBe(user2.id);

    // Verify in the database
    const dbFriendship = await db
      .select()
      .from(FriendshipSchema)
      .where(eq(FriendshipSchema.id, pendingRequest.id))
      .limit(1);

    expect(dbFriendship.length).toBe(1);
    expect(dbFriendship[0].status).toBe("ACCEPTED");
  });

  test("acceptFriendRequest should reject if request not found", async () => {
    // Create a test user
    const user = await createTestUser("user1", "<EMAIL>");

    // Generate a valid UUID that doesn't exist in the database
    const nonExistentId = crypto.randomUUID();

    // Try to accept a non-existent request
    try {
      await acceptFriendRequest(db, user.id, nonExistentId);
      // If we get here, the test should fail
      expect(true).toBe(false);
    } catch (error) {
      expect((error as Error).message).toBe("Friend request not found");
    }
  });

  // Reject Friend Request Tests
  test("rejectFriendRequest should delete a pending request", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a pending request
    const pendingRequest = await createPendingFriendRequest(user1.id, user2.id);

    // Reject the request
    const deletedRequest = await rejectFriendRequest(
      db,
      user2.id,
      pendingRequest.id,
    );

    // Check that the request was deleted
    expect(deletedRequest).toBeDefined();
    expect(deletedRequest.id).toBe(pendingRequest.id);

    // Verify in the database
    const dbFriendship = await db
      .select()
      .from(FriendshipSchema)
      .where(eq(FriendshipSchema.id, pendingRequest.id))
      .limit(1);

    expect(dbFriendship.length).toBe(0);
  });

  // Cancel Friend Request Tests
  test("cancelFriendRequest should delete a pending request", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a pending request
    const pendingRequest = await createPendingFriendRequest(user1.id, user2.id);

    // Cancel the request
    const deletedRequest = await cancelFriendRequest(
      db,
      user1.id,
      pendingRequest.id,
    );

    // Check that the request was deleted
    expect(deletedRequest).toBeDefined();
    expect(deletedRequest.id).toBe(pendingRequest.id);

    // Verify in the database
    const dbFriendship = await db
      .select()
      .from(FriendshipSchema)
      .where(eq(FriendshipSchema.id, pendingRequest.id))
      .limit(1);

    expect(dbFriendship.length).toBe(0);
  });

  // Remove Friend Tests
  test("removeFriend should delete an accepted friendship", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create an accepted friendship
    const friendship = await createAcceptedFriendship(user1.id, user2.id);

    // Remove the friend
    const deletedFriendship = await removeFriend(db, user1.id, friendship.id);

    // Check that the friendship was deleted
    expect(deletedFriendship).toBeDefined();
    expect(deletedFriendship.id).toBe(friendship.id);

    // Verify in the database
    const dbFriendship = await db
      .select()
      .from(FriendshipSchema)
      .where(eq(FriendshipSchema.id, friendship.id))
      .limit(1);

    expect(dbFriendship.length).toBe(0);
  });

  // Block User Tests
  test("blockUser should create a blocked relationship", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Block the user
    const blockedRelationship = await blockUser(db, user1.id, user2.id);

    // Check that the relationship was created
    expect(blockedRelationship).toBeDefined();
    expect(blockedRelationship.userId).toBe(user1.id);
    expect(blockedRelationship.friendId).toBe(user2.id);
    expect(blockedRelationship.status).toBe("BLOCKED");
    expect(blockedRelationship.initiatedBy).toBe(user1.id);

    // Verify in the database
    const dbRelationship = await db
      .select()
      .from(FriendshipSchema)
      .where(
        and(
          eq(FriendshipSchema.userId, user1.id),
          eq(FriendshipSchema.friendId, user2.id),
        ),
      )
      .limit(1);

    expect(dbRelationship.length).toBe(1);
    expect(dbRelationship[0].status).toBe("BLOCKED");
  });

  test("blockUser should convert existing friendship to blocked", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create an accepted friendship
    await createAcceptedFriendship(user1.id, user2.id);

    // Block the user
    const blockedRelationship = await blockUser(db, user1.id, user2.id);

    // Check that the relationship was updated
    expect(blockedRelationship).toBeDefined();
    expect(blockedRelationship.status).toBe("BLOCKED");
    expect(blockedRelationship.initiatedBy).toBe(user1.id);

    // Verify in the database
    const dbRelationship = await db
      .select()
      .from(FriendshipSchema)
      .where(
        or(
          and(
            eq(FriendshipSchema.userId, user1.id),
            eq(FriendshipSchema.friendId, user2.id),
          ),
          and(
            eq(FriendshipSchema.userId, user2.id),
            eq(FriendshipSchema.friendId, user1.id),
          ),
        ),
      )
      .limit(1);

    expect(dbRelationship.length).toBe(1);
    expect(dbRelationship[0].status).toBe("BLOCKED");
  });

  // Unblock User Tests
  test("unblockUser should delete a blocked relationship", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a blocked relationship
    const blockedRelationship = await createBlockedRelationship(
      user1.id,
      user2.id,
    );

    // Unblock the user
    const deletedRelationship = await unblockUser(
      db,
      user1.id,
      blockedRelationship.id,
    );

    // Check that the relationship was deleted
    expect(deletedRelationship).toBeDefined();
    expect(deletedRelationship.id).toBe(blockedRelationship.id);

    // Verify in the database
    const dbRelationship = await db
      .select()
      .from(FriendshipSchema)
      .where(eq(FriendshipSchema.id, blockedRelationship.id))
      .limit(1);

    expect(dbRelationship.length).toBe(0);
  });

  // Get Friends Tests
  test("getUserFriends should return a user's friends", async () => {
    // Create three test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");
    const user3 = await createTestUser("user3", "<EMAIL>");

    // Create friendships
    await createAcceptedFriendship(user1.id, user2.id);
    await createAcceptedFriendship(user1.id, user3.id);

    // Get user1's friends
    const friends = await getUserFriends(db, user1.id);

    // Check that both friends are returned
    expect(friends).toBeDefined();
    expect(friends.length).toBe(2);

    // Check that the friend data is correct
    const friendIds = friends.map((f) => f.friend.id);
    expect(friendIds).toContain(user2.id);
    expect(friendIds).toContain(user3.id);
  });

  // Get Pending Requests Tests
  test("getPendingFriendRequests should return pending requests", async () => {
    // Create three test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");
    const user3 = await createTestUser("user3", "<EMAIL>");

    // Create pending requests to user1
    await createPendingFriendRequest(user2.id, user1.id);
    await createPendingFriendRequest(user3.id, user1.id);

    // Get user1's pending requests
    const pendingRequests = await getPendingFriendRequests(db, user1.id);

    // Check that both requests are returned
    expect(pendingRequests).toBeDefined();
    expect(pendingRequests.length).toBe(2);

    // Check that the requester data is correct
    const requesterIds = pendingRequests.map((r) => r.requester.id);
    expect(requesterIds).toContain(user2.id);
    expect(requesterIds).toContain(user3.id);
  });

  // Get Sent Requests Tests
  test("getSentFriendRequests should return sent requests", async () => {
    // Create three test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");
    const user3 = await createTestUser("user3", "<EMAIL>");

    // Create sent requests from user1
    await createPendingFriendRequest(user1.id, user2.id);
    await createPendingFriendRequest(user1.id, user3.id);

    // Get user1's sent requests
    const sentRequests = await getSentFriendRequests(db, user1.id);

    // Check that both requests are returned
    expect(sentRequests).toBeDefined();
    expect(sentRequests.length).toBe(2);

    // Check that the recipient data is correct
    const recipientIds = sentRequests.map((r) => r.recipient.id);
    expect(recipientIds).toContain(user2.id);
    expect(recipientIds).toContain(user3.id);
  });

  // Get Blocked Users Tests
  test("getBlockedUsers should return blocked users", async () => {
    // Create three test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");
    const user3 = await createTestUser("user3", "<EMAIL>");

    // Create blocked relationships
    await createBlockedRelationship(user1.id, user2.id);
    await createBlockedRelationship(user1.id, user3.id);

    // Get user1's blocked users
    const blockedUsers = await getBlockedUsers(db, user1.id);

    // Check that both blocked users are returned
    expect(blockedUsers).toBeDefined();
    expect(blockedUsers.length).toBe(2);

    // Check that the blocked user data is correct
    const blockedIds = blockedUsers.map((b) => b.blocked.id);
    expect(blockedIds).toContain(user2.id);
    expect(blockedIds).toContain(user3.id);
  });

  // Is User Blocked Tests
  test("isUserBlocked should return true if user is blocked", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a blocked relationship
    await createBlockedRelationship(user1.id, user2.id);

    // Check if user2 is blocked by user1
    const isBlocked = await isUserBlocked(db, user1.id, user2.id);

    // Should be true
    expect(isBlocked).toBe(true);
  });

  test("isUserBlocked should return false if user is not blocked", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Check if user2 is blocked by user1 (no relationship exists)
    const isBlocked = await isUserBlocked(db, user1.id, user2.id);

    // Should be false
    expect(isBlocked).toBe(false);
  });
});
