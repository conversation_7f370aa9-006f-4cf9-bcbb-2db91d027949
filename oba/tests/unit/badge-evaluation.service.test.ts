import { describe, it, expect, beforeEach, mock, spyOn } from "bun:test";
import { BadgeEvaluationService } from "../../services/badge-evaluation.service";
import type { 
  UserStats, 
  BadgeCriteria, 
  EvaluationResult,
  BadgeType,
  UserBadge,
  BadgeProgress
} from "../../types/badge.types";

// Mock the database and utility functions
const mockDb = {} as any;

// Mock the utility functions
mock.module("../../db/utils/badge-utils", () => ({
  getUserStats: mock(),
  assignBadgeToUser: mock(),
  getUserBadges: mock(),
  getBadgeTypes: mock(),
}));

mock.module("../../db/utils/badge-evaluation", () => ({
  evaluateUserForAutomaticBadges: mock(),
  batchEvaluateUsers: mock(),
  evaluateAllUsersForAutomaticBadges: mock(),
  reevaluateBadgeTypeForAllUsers: mock(),
  getUsersNearBadgeCompletion: mock(),
  evaluateBadgeCriteria: mock(),
}));

describe("BadgeEvaluationService", () => {
  let service: BadgeEvaluationService;
  let mockUserStats: UserStats;
  let mockBadgeType: BadgeType;
  let mockUserBadge: UserBadge;

  beforeEach(() => {
    service = new BadgeEvaluationService(mockDb);
    
    mockUserStats = {
      messageCount: 100,
      serverCount: 5,
      friendCount: 10,
      daysActive: 30,
      accountAge: 60,
      lastActive: new Date(),
    };

    mockBadgeType = {
      id: "badge-1",
      name: "Test Badge",
      description: "A test badge",
      color: "#000000",
      category: "achievement",
      isActive: true,
      assignmentType: "automatic",
      criteria: {
        type: "message_count",
        threshold: 50,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    mockUserBadge = {
      id: "user-badge-1",
      userId: "user-1",
      badgeTypeId: "badge-1",
      assignedAt: new Date(),
      isVisible: true,
      badgeType: mockBadgeType,
    };
  });

  describe("evaluateUser", () => {
    it("should evaluate a user for automatic badges", async () => {
      const mockResult: EvaluationResult = {
        userId: "user-1",
        newBadges: [mockUserBadge],
        evaluatedBadges: ["badge-1"],
        errors: [],
      };

      const { evaluateUserForAutomaticBadges } = await import("../../db/utils/badge-evaluation");
      (evaluateUserForAutomaticBadges as any).mockResolvedValue(mockResult);

      const result = await service.evaluateUser("user-1");

      expect(result).toEqual(mockResult);
      expect(evaluateUserForAutomaticBadges).toHaveBeenCalledWith(mockDb, "user-1");
    });

    it("should handle evaluation errors gracefully", async () => {
      const { evaluateUserForAutomaticBadges } = await import("../../db/utils/badge-evaluation");
      (evaluateUserForAutomaticBadges as any).mockRejectedValue(new Error("Database error"));

      const result = await service.evaluateUser("user-1");

      expect(result.userId).toBe("user-1");
      expect(result.newBadges).toEqual([]);
      expect(result.evaluatedBadges).toEqual([]);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain("Evaluation failed");
    });
  });

  describe("evaluateUsers", () => {
    it("should evaluate multiple users in batch", async () => {
      const mockResults: EvaluationResult[] = [
        {
          userId: "user-1",
          newBadges: [mockUserBadge],
          evaluatedBadges: ["badge-1"],
          errors: [],
        },
        {
          userId: "user-2",
          newBadges: [],
          evaluatedBadges: ["badge-1"],
          errors: [],
        },
      ];

      const { batchEvaluateUsers } = await import("../../db/utils/badge-evaluation");
      (batchEvaluateUsers as any).mockResolvedValue(mockResults);

      const result = await service.evaluateUsers(["user-1", "user-2"]);

      expect(result).toEqual(mockResults);
      expect(batchEvaluateUsers).toHaveBeenCalledWith(mockDb, ["user-1", "user-2"]);
    });

    it("should throw error when batch evaluation fails", async () => {
      const { batchEvaluateUsers } = await import("../../db/utils/badge-evaluation");
      (batchEvaluateUsers as any).mockRejectedValue(new Error("Batch error"));

      await expect(service.evaluateUsers(["user-1", "user-2"])).rejects.toThrow("Failed to batch evaluate users");
    });
  });

  describe("checkCriteria", () => {
    it("should check if user meets message count criteria", async () => {
      const { getUserStats } = await import("../../db/utils/badge-utils");
      const { evaluateBadgeCriteria } = await import("../../db/utils/badge-evaluation");
      
      (getUserStats as any).mockResolvedValue(mockUserStats);
      (evaluateBadgeCriteria as any).mockResolvedValue(true);

      const criteria: BadgeCriteria = {
        type: "message_count",
        threshold: 50,
      };

      const result = await service.checkCriteria("user-1", criteria);

      expect(result).toBe(true);
      expect(getUserStats).toHaveBeenCalledWith(mockDb, "user-1");
      expect(evaluateBadgeCriteria).toHaveBeenCalledWith(mockUserStats, criteria);
    });

    it("should return false when criteria check fails", async () => {
      const { getUserStats } = await import("../../db/utils/badge-utils");
      (getUserStats as any).mockRejectedValue(new Error("Stats error"));

      const criteria: BadgeCriteria = {
        type: "message_count",
        threshold: 50,
      };

      const result = await service.checkCriteria("user-1", criteria);

      expect(result).toBe(false);
    });
  });

  describe("checkCriteriaForUsers", () => {
    it("should check criteria for multiple users", async () => {
      const { getUserStats } = await import("../../db/utils/badge-utils");
      const { evaluateBadgeCriteria } = await import("../../db/utils/badge-evaluation");
      
      (getUserStats as any).mockResolvedValue(mockUserStats);
      (evaluateBadgeCriteria as any)
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(false);

      const criteria: BadgeCriteria = {
        type: "message_count",
        threshold: 50,
      };

      const result = await service.checkCriteriaForUsers(["user-1", "user-2"], criteria);

      expect(result.size).toBe(2);
      expect(result.get("user-1")).toBe(true);
      expect(result.get("user-2")).toBe(false);
    });

    it("should handle errors for individual users gracefully", async () => {
      const { getUserStats } = await import("../../db/utils/badge-utils");
      
      (getUserStats as any)
        .mockResolvedValueOnce(mockUserStats)
        .mockRejectedValueOnce(new Error("User error"));

      const { evaluateBadgeCriteria } = await import("../../db/utils/badge-evaluation");
      (evaluateBadgeCriteria as any).mockResolvedValue(true);

      const criteria: BadgeCriteria = {
        type: "message_count",
        threshold: 50,
      };

      const result = await service.checkCriteriaForUsers(["user-1", "user-2"], criteria);

      expect(result.size).toBe(2);
      expect(result.get("user-1")).toBe(true);
      expect(result.get("user-2")).toBe(false); // Should default to false on error
    });
  });

  describe("getUserStats", () => {
    it("should get user statistics", async () => {
      const { getUserStats } = await import("../../db/utils/badge-utils");
      (getUserStats as any).mockResolvedValue(mockUserStats);

      const result = await service.getUserStats("user-1");

      expect(result).toEqual(mockUserStats);
      expect(getUserStats).toHaveBeenCalledWith(mockDb, "user-1");
    });

    it("should throw error when getting stats fails", async () => {
      const { getUserStats } = await import("../../db/utils/badge-utils");
      (getUserStats as any).mockRejectedValue(new Error("Stats error"));

      await expect(service.getUserStats("user-1")).rejects.toThrow("Failed to get user statistics");
    });
  });

  describe("getUserStatsForUsers", () => {
    it("should get stats for multiple users", async () => {
      const { getUserStats } = await import("../../db/utils/badge-utils");
      (getUserStats as any).mockResolvedValue(mockUserStats);

      const result = await service.getUserStatsForUsers(["user-1", "user-2"]);

      expect(result.size).toBe(2);
      expect(result.get("user-1")).toEqual(mockUserStats);
      expect(result.get("user-2")).toEqual(mockUserStats);
    });

    it("should handle errors for individual users", async () => {
      const { getUserStats } = await import("../../db/utils/badge-utils");
      (getUserStats as any)
        .mockResolvedValueOnce(mockUserStats)
        .mockRejectedValueOnce(new Error("User error"));

      const result = await service.getUserStatsForUsers(["user-1", "user-2"]);

      expect(result.size).toBe(1);
      expect(result.get("user-1")).toEqual(mockUserStats);
      expect(result.has("user-2")).toBe(false);
    });
  });

  describe("getBadgeProgress", () => {
    it("should get badge progress for a user", async () => {
      const { getUserBadges, getBadgeTypes } = await import("../../db/utils/badge-utils");
      
      (getUserBadges as any).mockResolvedValue([]);
      (getBadgeTypes as any).mockResolvedValue([mockBadgeType]);

      // Mock getUserStats method
      const getUserStatsSpy = spyOn(service, "getUserStats");
      getUserStatsSpy.mockResolvedValue(mockUserStats);

      const result = await service.getBadgeProgress("user-1");

      expect(result).toHaveLength(1);
      expect(result[0].badgeTypeId).toBe("badge-1");
      expect(result[0].progress).toBe(50); // Progress is capped at threshold
      expect(result[0].total).toBe(50);
      expect(result[0].isEarned).toBe(false);
    });

    it("should mark badges as earned if user already has them", async () => {
      const { getUserBadges, getBadgeTypes } = await import("../../db/utils/badge-utils");
      
      (getUserBadges as any).mockResolvedValue([mockUserBadge]);
      (getBadgeTypes as any).mockResolvedValue([mockBadgeType]);

      const getUserStatsSpy = spyOn(service, "getUserStats");
      getUserStatsSpy.mockResolvedValue(mockUserStats);

      const result = await service.getBadgeProgress("user-1");

      expect(result).toHaveLength(1);
      expect(result[0].isEarned).toBe(true);
    });
  });

  describe("getUsersNearCompletion", () => {
    it("should get users near badge completion", async () => {
      const mockNearCompletion = [
        {
          userId: "user-1",
          username: "testuser",
          progress: 45,
          total: 50,
          progressPercentage: 0.9,
        },
      ];

      const { getUsersNearBadgeCompletion } = await import("../../db/utils/badge-evaluation");
      (getUsersNearBadgeCompletion as any).mockResolvedValue(mockNearCompletion);

      const result = await service.getUsersNearCompletion("badge-1", 0.8);

      expect(result).toEqual(mockNearCompletion);
      expect(getUsersNearBadgeCompletion).toHaveBeenCalledWith(mockDb, "badge-1", 0.8);
    });
  });

  describe("evaluateAllUsers", () => {
    it("should evaluate all users with specified batch size", async () => {
      const mockSummary = {
        totalUsers: 100,
        processedUsers: 100,
        totalNewBadges: 25,
        errors: [],
      };

      const { evaluateAllUsersForAutomaticBadges } = await import("../../db/utils/badge-evaluation");
      (evaluateAllUsersForAutomaticBadges as any).mockResolvedValue(mockSummary);

      const result = await service.evaluateAllUsers(25);

      expect(result).toEqual(mockSummary);
      expect(evaluateAllUsersForAutomaticBadges).toHaveBeenCalledWith(mockDb, 25);
    });
  });

  describe("reevaluateBadgeType", () => {
    it("should re-evaluate a badge type for all users", async () => {
      const mockSummary = {
        evaluatedUsers: 50,
        newAssignments: 10,
        errors: [],
      };

      const { reevaluateBadgeTypeForAllUsers } = await import("../../db/utils/badge-evaluation");
      (reevaluateBadgeTypeForAllUsers as any).mockResolvedValue(mockSummary);

      const result = await service.reevaluateBadgeType("badge-1");

      expect(result).toEqual(mockSummary);
      expect(reevaluateBadgeTypeForAllUsers).toHaveBeenCalledWith(mockDb, "badge-1");
    });
  });

  describe("evaluateUsersForBadgeType", () => {
    it("should evaluate specific users for a specific badge type", async () => {
      const { getBadgeTypes, assignBadgeToUser } = await import("../../db/utils/badge-utils");
      
      (getBadgeTypes as any).mockResolvedValue([mockBadgeType]);
      (assignBadgeToUser as any).mockResolvedValue(mockUserBadge);

      // Mock database query for existing badges
      const mockDbSelect = mock(() => ({
        from: mock(() => ({
          where: mock(() => []), // No existing badges
        })),
      }));
      mockDb.select = mockDbSelect;

      const checkCriteriaSpy = spyOn(service, "checkCriteria");
      checkCriteriaSpy.mockResolvedValue(true);

      const result = await service.evaluateUsersForBadgeType(["user-1", "user-2"], "badge-1");

      expect(result.evaluatedUsers).toBe(2);
      expect(result.newAssignments).toBe(2);
      expect(result.errors).toHaveLength(0);
    });

    it("should skip users who already have the badge", async () => {
      const { getBadgeTypes } = await import("../../db/utils/badge-utils");
      (getBadgeTypes as any).mockResolvedValue([mockBadgeType]);

      // Mock database query to return existing badge
      const mockDbSelect = mock(() => ({
        from: mock(() => ({
          where: mock(() => [{ userId: "user-1" }]), // user-1 already has badge
        })),
      }));
      mockDb.select = mockDbSelect;

      const checkCriteriaSpy = spyOn(service, "checkCriteria");
      checkCriteriaSpy.mockResolvedValue(true);

      const result = await service.evaluateUsersForBadgeType(["user-1", "user-2"], "badge-1");

      expect(result.evaluatedUsers).toBe(1); // Only user-2 evaluated
    });
  });

  describe("Custom criteria progress calculation", () => {
    it("should calculate progress for account age criteria", async () => {
      const customBadgeType: BadgeType = {
        ...mockBadgeType,
        criteria: {
          type: "custom",
          threshold: 1,
          conditions: {
            minAccountAge: 30,
          },
        },
      };

      const { getUserBadges, getBadgeTypes } = await import("../../db/utils/badge-utils");
      
      (getUserBadges as any).mockResolvedValue([]);
      (getBadgeTypes as any).mockResolvedValue([customBadgeType]);

      const getUserStatsSpy = spyOn(service, "getUserStats");
      getUserStatsSpy.mockResolvedValue({
        ...mockUserStats,
        accountAge: 60, // User has 60 days, requirement is 30
      });

      const result = await service.getBadgeProgress("user-1");

      expect(result).toHaveLength(1);
      expect(result[0].progress).toBe(1); // Should be capped at threshold (1)
      expect(result[0].total).toBe(1);
    });

    it("should calculate progress for combined requirements", async () => {
      const customBadgeType: BadgeType = {
        ...mockBadgeType,
        criteria: {
          type: "custom",
          threshold: 100,
          conditions: {
            combinedRequirements: {
              messages: 50,
              servers: 3,
              friends: 5,
            },
          },
        },
      };

      const { getUserBadges, getBadgeTypes } = await import("../../db/utils/badge-utils");
      
      (getUserBadges as any).mockResolvedValue([]);
      (getBadgeTypes as any).mockResolvedValue([customBadgeType]);

      const getUserStatsSpy = spyOn(service, "getUserStats");
      getUserStatsSpy.mockResolvedValue({
        ...mockUserStats,
        messageCount: 100, // Meets requirement
        serverCount: 5,    // Meets requirement
        friendCount: 3,    // Does not meet requirement (needs 5)
      });

      const result = await service.getBadgeProgress("user-1");

      expect(result).toHaveLength(1);
      // Should be 2/3 requirements met = 66.67% of threshold
      expect(result[0].progress).toBeCloseTo(66.67, 1);
      expect(result[0].total).toBe(100);
    });
  });
});