import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  afterAll,
} from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  cleanupTestData,
  createTestServer,
  createTestChannel,
  createTestChannelWithPrivacy,
  createTestRole,
  addAllowedRoleToChannel,
} from "../helpers";
import {
  createChannel,
  updateChannel,
  deleteChannel,
  getChannelDetails,
} from "../../db/utils";
import {
  ChannelSchema,
  ChannelPrivacySchema,
  ChannelAllowedRolesSchema,
  MessageSchema,
  MessageReactionSchema,
} from "../../db/schema";
import { eq } from "drizzle-orm";

describe("Channel Management Utility Functions", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  // Create Channel Tests
  test("createChannel should create a new channel", async () => {
    // Create a test user and server
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);

    // Create a channel
    const channel = await createChannel(db, server.id, "test-channel", {
      description: "A test channel",
      type: "TEXT",
      isPublic: true,
    });

    // Check that the channel was created
    expect(channel).toBeDefined();
    expect(channel.name).toBe("test-channel");
    expect(channel.serverId).toBe(server.id);
    expect(channel.type).toBe("TEXT");

    // Verify in the database
    const dbChannel = await db
      .select()
      .from(ChannelSchema)
      .where(eq(ChannelSchema.id, channel.id))
      .limit(1);

    expect(dbChannel.length).toBe(1);
    expect(dbChannel[0].name).toBe("test-channel");

    // Verify privacy settings
    const privacySettings = await db
      .select()
      .from(ChannelPrivacySchema)
      .where(eq(ChannelPrivacySchema.channelId, channel.id))
      .limit(1);

    expect(privacySettings.length).toBe(1);
    expect(privacySettings[0].isPublic).toBe(true);
  });

  test("createChannel should create a private channel with allowed roles", async () => {
    // Create a test user and server
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);

    // Create a role
    const role = await createTestRole(server.id);

    // Create a private channel with allowed roles
    const channel = await createChannel(db, server.id, "private-channel", {
      description: "A private channel",
      type: "TEXT",
      isPublic: false,
      allowedRoleIds: [role.id],
    });

    // Check that the channel was created
    expect(channel).toBeDefined();
    expect(channel.name).toBe("private-channel");

    // Verify privacy settings
    const privacySettings = await db
      .select()
      .from(ChannelPrivacySchema)
      .where(eq(ChannelPrivacySchema.channelId, channel.id))
      .limit(1);

    expect(privacySettings.length).toBe(1);
    expect(privacySettings[0].isPublic).toBe(false);

    // Verify allowed roles
    const allowedRoles = await db
      .select()
      .from(ChannelAllowedRolesSchema)
      .where(eq(ChannelAllowedRolesSchema.channelId, channel.id));

    expect(allowedRoles.length).toBe(1);
    expect(allowedRoles[0].roleId).toBe(role.id);
  });

  // Update Channel Tests
  test("updateChannel should update channel details", async () => {
    // Create a test user, server, and channel
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);
    const channel = await createTestChannel(server.id, "original-name", "TEXT");

    // Update the channel
    const updatedChannel = await updateChannel(db, channel.id, {
      name: "updated-name",
      description: "Updated description",
      type: "ANNOUNCEMENT",
    });

    // Check that the channel was updated
    expect(updatedChannel).toBeDefined();
    expect(updatedChannel?.name).toBe("updated-name");
    expect(updatedChannel?.description).toBe("Updated description");
    expect(updatedChannel?.type).toBe("ANNOUNCEMENT");

    // Verify in the database
    const dbChannel = await db
      .select()
      .from(ChannelSchema)
      .where(eq(ChannelSchema.id, channel.id))
      .limit(1);

    expect(dbChannel.length).toBe(1);
    expect(dbChannel[0].name).toBe("updated-name");
    expect(dbChannel[0].description).toBe("Updated description");
    expect(dbChannel[0].type).toBe("ANNOUNCEMENT");
  });

  test("updateChannel should update privacy settings", async () => {
    // Create a test user, server, and channel with privacy settings
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);
    const channel = await createTestChannelWithPrivacy(
      server.id,
      "public-channel",
      "TEXT",
      true,
    );

    // Update the channel to private
    const updatedChannel = await updateChannel(db, channel.id, {
      isPublic: false,
    });

    // Check that the channel was updated
    expect(updatedChannel).toBeDefined();

    // Verify privacy settings in the database
    const privacySettings = await db
      .select()
      .from(ChannelPrivacySchema)
      .where(eq(ChannelPrivacySchema.channelId, channel.id))
      .limit(1);

    expect(privacySettings.length).toBe(1);
    expect(privacySettings[0].isPublic).toBe(false);
  });

  test("updateChannel should update allowed roles", async () => {
    // Create a test user, server, and channel with privacy settings
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);
    const channel = await createTestChannelWithPrivacy(
      server.id,
      "private-channel",
      "TEXT",
      false,
    );

    // Create roles
    const role1 = await createTestRole(server.id, "Role 1");
    const role2 = await createTestRole(server.id, "Role 2");

    // Add role1 to the channel
    await addAllowedRoleToChannel(channel.id, role1.id);

    // Update the channel to use role2 instead
    const updatedChannel = await updateChannel(
      db,
      channel.id,
      {
        isPublic: false,
      },
      [role2.id],
    );

    // Check that the channel was updated
    expect(updatedChannel).toBeDefined();

    // Verify allowed roles in the database
    const allowedRoles = await db
      .select()
      .from(ChannelAllowedRolesSchema)
      .where(eq(ChannelAllowedRolesSchema.channelId, channel.id));

    expect(allowedRoles.length).toBe(1);
    expect(allowedRoles[0].roleId).toBe(role2.id);
  });

  test("updateChannel should return null for non-existent channel", async () => {
    // Generate a random UUID that doesn't exist
    const nonExistentId = crypto.randomUUID();

    // Try to update a non-existent channel
    const updatedChannel = await updateChannel(db, nonExistentId, {
      name: "updated-name",
    });

    // Check that null is returned
    expect(updatedChannel).toBeNull();
  });

  // Delete Channel Tests
  test("deleteChannel should delete a channel and all associated data", async () => {
    // Create a test user, server, and channel
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);
    const channel = await createTestChannelWithPrivacy(
      server.id,
      "delete-channel",
      "TEXT",
      true,
    );

    // Create a message in the channel
    const message = await db
      .insert(MessageSchema)
      .values({
        content: "Test message",
        userId: user.id,
        channelId: channel.id,
        serverId: server.id,
      })
      .returning();

    // Create a reaction to the message
    await db.insert(MessageReactionSchema).values({
      messageId: message[0].id,
      userId: user.id,
      emoji: "👍",
    });

    // Delete the channel
    const result = await deleteChannel(db, channel.id);

    // Check that the operation was successful
    expect(result).toBe(true);

    // Verify that the channel was deleted
    const dbChannel = await db
      .select()
      .from(ChannelSchema)
      .where(eq(ChannelSchema.id, channel.id));

    expect(dbChannel.length).toBe(0);

    // Verify that privacy settings were deleted
    const privacySettings = await db
      .select()
      .from(ChannelPrivacySchema)
      .where(eq(ChannelPrivacySchema.channelId, channel.id));

    expect(privacySettings.length).toBe(0);

    // Verify that messages were deleted
    const messages = await db
      .select()
      .from(MessageSchema)
      .where(eq(MessageSchema.channelId, channel.id));

    expect(messages.length).toBe(0);

    // Verify that reactions were deleted
    const reactions = await db
      .select()
      .from(MessageReactionSchema)
      .where(eq(MessageReactionSchema.messageId, message[0].id));

    expect(reactions.length).toBe(0);
  });

  test("deleteChannel should return false for non-existent channel", async () => {
    // Generate a random UUID that doesn't exist
    const nonExistentId = crypto.randomUUID();

    // Try to delete a non-existent channel
    const result = await deleteChannel(db, nonExistentId);

    // Check that false is returned
    expect(result).toBe(false);
  });

  // Get Channel Details Tests
  test("getChannelDetails should return channel details", async () => {
    // Create a test user, server, and channel with privacy settings
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);
    const channel = await createTestChannelWithPrivacy(
      server.id,
      "details-channel",
      "TEXT",
      false,
    );

    // Create a role and add it to the channel
    const role = await createTestRole(server.id);
    await addAllowedRoleToChannel(channel.id, role.id);

    // Get channel details
    const channelDetails = await getChannelDetails(db, channel.id);

    // Check that the details were returned
    expect(channelDetails).toBeDefined();
    expect(channelDetails?.id).toBe(channel.id);
    expect(channelDetails?.name).toBe("details-channel");
    expect(channelDetails?.type).toBe("TEXT");
    expect(channelDetails?.serverId).toBe(server.id);

    // Check privacy settings
    expect(channelDetails?.privacySettings).toBeDefined();
    expect(channelDetails?.privacySettings?.isPublic).toBe(false);

    // Check allowed roles
    expect(channelDetails?.allowedRoles).toBeDefined();
    expect(channelDetails?.allowedRoles?.length).toBeGreaterThan(0);
    expect(channelDetails?.allowedRoles?.[0].roleId).toBe(role.id);
  });

  test("getChannelDetails should return null for non-existent channel", async () => {
    // Generate a random UUID that doesn't exist
    const nonExistentId = crypto.randomUUID();

    // Try to get details of a non-existent channel
    const channelDetails = await getChannelDetails(db, nonExistentId);

    // Check that null is returned
    expect(channelDetails).toBeNull();
  });
});
