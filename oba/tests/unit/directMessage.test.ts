import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  afterAll,
} from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  createTestDirectMessage,
  cleanupTestData,
} from "../helpers";
import {
  createDirectMessage,
  editDirectMessage,
  deleteDirectMessage,
  getDirectMessageById,
  getDirectMessagesBetweenUsers,
  markDirectMessageAsRead,
  getUserDirectMessageContacts,
} from "../../db/utils";

describe("Direct Message Utility Functions", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  test("createDirectMessage should create a new direct message", async () => {
    // Create test users
    const sender = await createTestUser("sender", "<EMAIL>");
    const receiver = await createTestUser("receiver", "<EMAIL>");

    // Create a direct message
    const message = await createDirectMessage(
      db,
      sender.id,
      receiver.id,
      "Test direct message",
    );

    // Check that the message was created
    expect(message).toBeDefined();
    expect(message.senderId).toBe(sender.id);
    expect(message.receiverId).toBe(receiver.id);
    expect(message.content).toBe("Test direct message");
  });

  test("getDirectMessageById should retrieve a direct message", async () => {
    // Create test users
    const sender = await createTestUser("sender", "<EMAIL>");
    const receiver = await createTestUser("receiver", "<EMAIL>");

    // Create a direct message
    const message = await createTestDirectMessage(sender.id, receiver.id);

    // Retrieve the message
    const retrievedMessage = await getDirectMessageById(db, message.id);

    // Check that the message was retrieved
    expect(retrievedMessage).toBeDefined();
    expect(retrievedMessage?.id).toBe(message.id);
    expect(retrievedMessage?.senderId).toBe(sender.id);
    expect(retrievedMessage?.receiverId).toBe(receiver.id);
    expect(retrievedMessage?.content).toBe("Test direct message content");
  });

  test("editDirectMessage should update a direct message", async () => {
    // Create test users
    const sender = await createTestUser("sender", "<EMAIL>");
    const receiver = await createTestUser("receiver", "<EMAIL>");

    // Create a direct message
    const message = await createTestDirectMessage(sender.id, receiver.id);

    // Edit the message
    const updatedMessage = await editDirectMessage(
      db,
      message.id,
      "Updated direct message content",
    );

    // Check that the message was updated
    expect(updatedMessage).toBeDefined();
    expect(updatedMessage.id).toBe(message.id);
    expect(updatedMessage.content).toBe("Updated direct message content");
    expect(updatedMessage.editedAt).toBeDefined();
  });

  test("deleteDirectMessage should delete a direct message", async () => {
    // Create test users
    const sender = await createTestUser("sender", "<EMAIL>");
    const receiver = await createTestUser("receiver", "<EMAIL>");

    // Create a direct message
    const message = await createTestDirectMessage(sender.id, receiver.id);

    // Delete the message
    const deletedMessage = await deleteDirectMessage(db, message.id);

    // Check that the message was deleted
    expect(deletedMessage).toBeDefined();
    expect(deletedMessage.id).toBe(message.id);

    // Verify the message is no longer in the database
    const retrievedMessage = await getDirectMessageById(db, message.id);
    expect(retrievedMessage).toBeNull();
  });

  test("getDirectMessagesBetweenUsers should retrieve messages between users", async () => {
    // Create test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create multiple messages between the users
    await createTestDirectMessage(
      user1.id,
      user2.id,
      "Message from user1 to user2 - 1",
    );
    await createTestDirectMessage(
      user2.id,
      user1.id,
      "Message from user2 to user1",
    );
    await createTestDirectMessage(
      user1.id,
      user2.id,
      "Message from user1 to user2 - 2",
    );

    // Retrieve the messages
    const messages = await getDirectMessagesBetweenUsers(
      db,
      user1.id,
      user2.id,
      10,
    );

    // Check that the messages were retrieved
    expect(messages).toBeDefined();
    expect(messages.length).toBe(3);

    // Messages should be in reverse chronological order (newest first)
    expect(messages[0].message.content).toBe("Message from user1 to user2 - 2");
    expect(messages[1].message.content).toBe("Message from user2 to user1");
    expect(messages[2].message.content).toBe("Message from user1 to user2 - 1");
  });

  test("markDirectMessageAsRead should mark a message as read", async () => {
    // Create test users
    const sender = await createTestUser("sender", "<EMAIL>");
    const receiver = await createTestUser("receiver", "<EMAIL>");

    // Create a direct message
    const message = await createTestDirectMessage(sender.id, receiver.id);

    // Initially, readAt should be null
    expect(message.readAt).toBeNull();

    // Mark the message as read
    await markDirectMessageAsRead(db, message.id);

    // Retrieve the updated message
    const updatedMessage = await getDirectMessageById(db, message.id);

    // Check that the message was marked as read
    expect(updatedMessage).toBeDefined();
    expect(updatedMessage?.readAt).toBeDefined();
  });

  test("getUserDirectMessageContacts should retrieve user contacts", async () => {
    // Create test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");
    const user3 = await createTestUser("user3", "<EMAIL>");

    // Create messages between users
    await createTestDirectMessage(user1.id, user2.id);
    await createTestDirectMessage(user2.id, user1.id);
    await createTestDirectMessage(user1.id, user3.id);

    // Retrieve user1's contacts
    const contacts = await getUserDirectMessageContacts(db, user1.id);

    // Check that the contacts were retrieved
    expect(contacts).toBeDefined();
    expect(contacts.length).toBe(2);

    // Check that user2 and user3 are in the contacts
    const contactUsernames = contacts.map((contact) => contact.username);
    expect(contactUsernames).toContain("user2");
    expect(contactUsernames).toContain("user3");
  });
});
