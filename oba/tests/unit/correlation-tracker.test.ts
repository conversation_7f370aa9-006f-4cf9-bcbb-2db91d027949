import { describe, test, expect, beforeEach, afterEach, mock } from "bun:test";
import { CorrelationTracker } from "../../utils/correlation-tracker";

describe("CorrelationTracker", () => {
  let tracker: CorrelationTracker;

  beforeEach(() => {
    tracker = new CorrelationTracker();
  });

  afterEach(() => {
    tracker.clearAll();
  });

  describe("generateCorrelationId", () => {
    test("should generate unique correlation IDs", () => {
      const id1 = tracker.generateCorrelationId();
      const id2 = tracker.generateCorrelationId();

      expect(id1).toMatch(/^corr-/);
      expect(id2).toMatch(/^corr-/);
      expect(id1).not.toBe(id2);
    });
  });

  describe("startTracking", () => {
    test("should start tracking a correlation", () => {
      const correlationId = tracker.startTracking("TEST_REQUEST", "user123");

      expect(correlationId).toMatch(/^corr-/);
      expect(tracker.isTracking(correlationId)).toBe(true);

      const correlation = tracker.getCorrelation(correlationId);
      expect(correlation).toBeTruthy();
      expect(correlation?.requestType).toBe("TEST_REQUEST");
      expect(correlation?.userId).toBe("user123");
      expect(correlation?.timestamp).toBeTypeOf("number");
    });

    test("should include metadata when provided", () => {
      const metadata = { serverId: "server123", channelId: "channel456" };
      const correlationId = tracker.startTracking("TEST_REQUEST", "user123", {
        metadata,
      });

      const correlation = tracker.getCorrelation(correlationId);
      expect(correlation?.metadata).toEqual(metadata);
    });

    test("should set up timeout when specified", (done) => {
      const onTimeout = mock(() => {
        done();
      });

      tracker.startTracking("TEST_REQUEST", "user123", {
        timeoutMs: 10,
        onTimeout,
      });

      // Wait for timeout to trigger
      setTimeout(() => {
        expect(onTimeout).toHaveBeenCalled();
      }, 20);
    });
  });

  describe("completeTracking", () => {
    test("should complete tracking and return correlation data", () => {
      const correlationId = tracker.startTracking("TEST_REQUEST", "user123");

      const completedData = tracker.completeTracking(correlationId, {
        success: true,
      });

      expect(completedData).toBeTruthy();
      expect(completedData?.requestType).toBe("TEST_REQUEST");
      expect(completedData?.userId).toBe("user123");
      expect(tracker.isTracking(correlationId)).toBe(false);
    });

    test("should return null for non-existent correlation", () => {
      const result = tracker.completeTracking("non-existent-id");
      expect(result).toBeNull();
    });

    test("should clear timeout when completing", () => {
      const onTimeout = mock();
      const correlationId = tracker.startTracking("TEST_REQUEST", "user123", {
        timeoutMs: 1000,
        onTimeout,
      });

      tracker.completeTracking(correlationId);

      // Wait to ensure timeout doesn't fire
      setTimeout(() => {
        expect(onTimeout).not.toHaveBeenCalled();
      }, 1100);
    });
  });

  describe("getUserCorrelations", () => {
    test("should return correlations for specific user", () => {
      const user1Id = tracker.startTracking("REQUEST_1", "user1");
      const user2Id = tracker.startTracking("REQUEST_2", "user2");
      const user1Id2 = tracker.startTracking("REQUEST_3", "user1");

      const user1Correlations = tracker.getUserCorrelations("user1");
      const user2Correlations = tracker.getUserCorrelations("user2");

      expect(user1Correlations).toHaveLength(2);
      expect(user2Correlations).toHaveLength(1);

      const user1Ids = user1Correlations.map((c) => c.id);
      expect(user1Ids).toContain(user1Id);
      expect(user1Ids).toContain(user1Id2);
      expect(user1Ids).not.toContain(user2Id);
    });

    test("should return empty array for user with no correlations", () => {
      const correlations = tracker.getUserCorrelations("non-existent-user");
      expect(correlations).toEqual([]);
    });
  });

  describe("getStats", () => {
    test("should return correct statistics", () => {
      tracker.startTracking("REQUEST_1", "user1");
      tracker.startTracking("REQUEST_2", "user2");

      const stats = tracker.getStats();

      expect(stats.activeCorrelations).toBe(2);
      expect(stats.totalTracked).toBe(2);
      expect(stats.averageAge).toBeTypeOf("number");
      expect(stats.oldestCorrelation).toBeTruthy();
    });

    test("should return zero stats when no correlations", () => {
      const stats = tracker.getStats();

      expect(stats.activeCorrelations).toBe(0);
      expect(stats.totalTracked).toBe(0);
      expect(stats.averageAge).toBe(0);
      expect(stats.oldestCorrelation).toBeUndefined();
    });
  });

  describe("cleanupExpired", () => {
    test("should clean up expired correlations", (done) => {
      const correlationId = tracker.startTracking("TEST_REQUEST", "user123");

      // Manually set timestamp to make it appear old
      const correlation = tracker.getCorrelation(correlationId);
      if (correlation) {
        correlation.timestamp = Date.now() - 10000; // 10 seconds ago
      }

      const cleanedCount = tracker.cleanupExpired(5000); // 5 second max age

      expect(cleanedCount).toBe(1);
      expect(tracker.isTracking(correlationId)).toBe(false);
      done();
    });

    test("should not clean up recent correlations", () => {
      const correlationId = tracker.startTracking("TEST_REQUEST", "user123");

      const cleanedCount = tracker.cleanupExpired(5000); // 5 second max age

      expect(cleanedCount).toBe(0);
      expect(tracker.isTracking(correlationId)).toBe(true);
    });
  });

  describe("clearAll", () => {
    test("should clear all correlations", () => {
      tracker.startTracking("REQUEST_1", "user1");
      tracker.startTracking("REQUEST_2", "user2");

      expect(tracker.getStats().activeCorrelations).toBe(2);

      tracker.clearAll();

      expect(tracker.getStats().activeCorrelations).toBe(0);
    });
  });

  describe("memory management", () => {
    test("should handle large number of correlations", () => {
      // This test ensures the tracker can handle many correlations
      // without running into memory issues
      const correlationIds: string[] = [];

      for (let i = 0; i < 1000; i++) {
        const id = tracker.startTracking(`REQUEST_${i}`, `user${i % 10}`);
        correlationIds.push(id);
      }

      expect(tracker.getStats().activeCorrelations).toBe(1000);

      // Complete half of them
      for (let i = 0; i < 500; i++) {
        tracker.completeTracking(correlationIds[i]);
      }

      expect(tracker.getStats().activeCorrelations).toBe(500);
    });
  });
});
