import { describe, test, expect } from "bun:test";
import { ResponseUtils } from "../utils/response-utils";

/**
 * API Response Format Validation Tests
 *
 * These tests ensure that all API endpoints return responses in the standardized format
 * as defined in the OpenAPI specification.
 */

// Helper function to parse response body
async function parseResponseBody(response: Response): Promise<any> {
  const text = await response.text();
  return JSON.parse(text);
}

describe("API Response Format Validation", () => {
  describe("ResponseUtils", () => {
    test("success() should return standardized success response", async () => {
      const testData = { id: "123", name: "Test User" };
      const response = ResponseUtils.success(testData, {
        message: "Test successful",
      });

      expect(response.status).toBe(200);
      expect(response.headers.get("Content-Type")).toBe("application/json");
      expect(response.headers.get("X-Request-ID")).toBeDefined();

      // Parse response body
      const body = await parseResponseBody(response);

      expect(body.success).toBe(true);
      expect(body.data).toEqual(testData);
      expect(body.message).toBe("Test successful");
      expect(body.meta.timestamp).toBeDefined();
      expect(body.meta.requestId).toBeDefined();
    });

    test("error() should return standardized error response", async () => {
      const response = ResponseUtils.error("TEST_ERROR", "Test error message", {
        status: 400,
        details: { field: "username" },
      });

      expect(response.status).toBe(400);
      expect(response.headers.get("Content-Type")).toBe("application/json");
      expect(response.headers.get("X-Request-ID")).toBeDefined();

      const body = await parseResponseBody(response);

      expect(body.success).toBe(false);
      expect(body.error.code).toBe("TEST_ERROR");
      expect(body.error.message).toBe("Test error message");
      expect(body.error.details).toEqual({ field: "username" });
      expect(body.meta.timestamp).toBeDefined();
      expect(body.meta.requestId).toBeDefined();
    });

    test("paginated() should return standardized paginated response", async () => {
      const testData = [
        { id: "1", name: "Item 1" },
        { id: "2", name: "Item 2" },
      ];

      const response = ResponseUtils.paginated(testData, {
        page: 1,
        limit: 10,
        total: 25,
      });

      expect(response.status).toBe(200);

      const body = await parseResponseBody(response);

      expect(body.success).toBe(true);
      expect(body.data).toEqual(testData);
      expect(body.meta.pagination).toBeDefined();
      expect(body.meta.pagination.page).toBe(1);
      expect(body.meta.pagination.limit).toBe(10);
      expect(body.meta.pagination.total).toBe(25);
      expect(body.meta.pagination.totalPages).toBe(3);
      expect(body.meta.pagination.hasNext).toBe(true);
      expect(body.meta.pagination.hasPrev).toBe(false);
    });

    test("created() should return 201 status with standardized format", async () => {
      const testData = { id: "123", name: "New Resource" };
      const response = ResponseUtils.created(testData);

      expect(response.status).toBe(201);

      const body = await parseResponseBody(response);

      expect(body.success).toBe(true);
      expect(body.data).toEqual(testData);
      expect(body.message).toBe("Resource created successfully");
    });

    test("common error methods should return correct status codes", () => {
      expect(ResponseUtils.badRequest("Bad request").status).toBe(400);
      expect(ResponseUtils.unauthorized().status).toBe(401);
      expect(ResponseUtils.forbidden().status).toBe(403);
      expect(ResponseUtils.notFound("User").status).toBe(404);
      expect(ResponseUtils.conflict("Already exists").status).toBe(409);
      expect(ResponseUtils.tooManyRequests().status).toBe(429);
      expect(ResponseUtils.internalError().status).toBe(500);
    });

    test("validationError() should format validation errors correctly", async () => {
      const errors = [
        { field: "username", message: "Username is required" },
        { field: "email", message: "Invalid email format" },
      ];

      const response = ResponseUtils.validationError(errors);

      expect(response.status).toBe(400);

      const body = await parseResponseBody(response);

      expect(body.success).toBe(false);
      expect(body.error.code).toBe("VALIDATION_ERROR");
      expect(body.error.message).toBe("Validation failed");
      expect(body.error.details.errors).toEqual(errors);
    });
  });

  describe("Response Format Consistency", () => {
    test("all success responses should have required fields", async () => {
      const responses = [
        ResponseUtils.success({ test: "data" }),
        ResponseUtils.created({ test: "data" }),
        ResponseUtils.paginated([{ test: "data" }], {
          page: 1,
          limit: 10,
          total: 1,
        }),
      ];

      for (const response of responses) {
        const body = await parseResponseBody(response);

        expect(body).toHaveProperty("success", true);
        expect(body).toHaveProperty("data");
        expect(body).toHaveProperty("meta");
        expect(body.meta).toHaveProperty("timestamp");
        expect(body.meta).toHaveProperty("requestId");
      }
    });

    test("all error responses should have required fields", async () => {
      const responses = [
        ResponseUtils.badRequest("Bad request"),
        ResponseUtils.unauthorized(),
        ResponseUtils.forbidden(),
        ResponseUtils.notFound("Resource"),
        ResponseUtils.internalError(),
      ];

      for (const response of responses) {
        const body = await parseResponseBody(response);

        expect(body).toHaveProperty("success", false);
        expect(body).toHaveProperty("error");
        expect(body.error).toHaveProperty("code");
        expect(body.error).toHaveProperty("message");
        expect(body).toHaveProperty("meta");
        expect(body.meta).toHaveProperty("timestamp");
        expect(body.meta).toHaveProperty("requestId");
      }
    });

    test("timestamps should be valid ISO strings", async () => {
      const response = ResponseUtils.success({ test: "data" });
      const body = await parseResponseBody(response);

      const timestamp = new Date(body.meta.timestamp);
      expect(timestamp.toISOString()).toBe(body.meta.timestamp);
    });

    test("request IDs should be unique", async () => {
      const response1 = ResponseUtils.success({ test: "data1" });
      const response2 = ResponseUtils.success({ test: "data2" });

      const body1 = await parseResponseBody(response1);
      const body2 = await parseResponseBody(response2);

      expect(body1.meta.requestId).not.toBe(body2.meta.requestId);
    });
  });
});

/**
 * Integration tests for actual API endpoints
 * These would test real endpoints to ensure they follow the standard format
 */
describe("API Endpoint Response Format Integration", () => {
  // These tests would require a running server instance
  // and would test actual endpoints

  test.skip("POST /api/login should return standardized format", async () => {
    // Example of how to test actual endpoints
    const response = await fetch("http://localhost:3005/api/login", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        username: "berkormanli",
        password: "Bebesrdr423!",
      }),
    });
    const body = await response.json();
    expect(body.success).toBeDefined();
    console.log(JSON.stringify(body));
    expect(body.meta.timestamp).toBeDefined();
    expect(body.meta.requestId).toBeDefined();
    expect(response.headers.get("X-Request-ID")).toBeDefined();
  });

  test.skip("GET /api/getUserServerlist should return standardized format", async () => {
    // Similar integration test for server list endpoint
  });

  test.skip("Error endpoints should return standardized error format", async () => {
    // Test error responses from actual endpoints
  });
});

/**
 * OpenAPI Schema Validation Tests
 * These tests validate that responses match the OpenAPI specification
 */
describe("OpenAPI Schema Validation", () => {
  test.skip("responses should match OpenAPI schema definitions", () => {
    // This would require loading the OpenAPI spec and validating responses
    // against the defined schemas using a library like ajv or similar
  });
});

/**
 * Performance Tests
 * Ensure the standardized response format doesn't impact performance
 */
describe("Response Format Performance", () => {
  test("response creation should be fast", () => {
    const start = performance.now();

    for (let i = 0; i < 1000; i++) {
      ResponseUtils.success({ id: i, name: `Item ${i}` });
    }

    const end = performance.now();
    const duration = end - start;

    // Should create 1000 responses in less than 100ms
    expect(duration).toBeLessThan(100);
  });

  test("error response creation should be fast", () => {
    const start = performance.now();

    for (let i = 0; i < 1000; i++) {
      ResponseUtils.badRequest(`Error ${i}`);
    }

    const end = performance.now();
    const duration = end - start;

    // Should create 1000 error responses in less than 100ms
    expect(duration).toBeLessThan(100);
  });
});
