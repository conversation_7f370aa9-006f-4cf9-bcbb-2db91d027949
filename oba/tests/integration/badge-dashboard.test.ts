import { describe, it, expect, beforeAll, afterAll } from "bun:test";
import { db } from "../../db";
import { BadgeService } from "../../services/badge.service";
import type {
  CreateBadgeTypeRequest,
  BadgeProgress,
  UserStats
} from "../../types/badge.types";

describe("Badge Dashboard Integration Tests", () => {
  const badgeService = new BadgeService(db);
  const testUserId = "01234567-89ab-cdef-0123-456789abcdef";
  const testAdminId = "01234567-89ab-cdef-0123-456789abcde0";
  let createdBadgeTypeIds: string[] = [];

  beforeAll(async () => {
    // Create test badge types for dashboard testing
    const testBadgeTypes: CreateBadgeTypeRequest[] = [
      {
        badgeId: "dashboard-test-message",
        name: "Dashboard Message Test",
        description: "Test badge for message count",
        icon: "💬",
        design: {
          shape: "circle",
          background: "#4CAF50",
          colors: ["#4CAF50"]
        },
        criteria: {
          requirement: "Send 5 messages",
          tracked: "message_count",
          type: "message_count",
          threshold: 5
        },
        unlockType: "automatic",
        category: "milestone"
      },
      {
        badgeId: "dashboard-test-manual",
        name: "Dashboard Manual Test",
        description: "Test badge for manual assignment",
        icon: "🏆",
        design: {
          shape: "star",
          background: "#FF9800",
          colors: ["#FF9800"]
        },
        criteria: {
          requirement: "Manually assigned",
          tracked: "manual"
        },
        unlockType: "manual",
        category: "special"
      }
    ];

    for (const badgeType of testBadgeTypes) {
      try {
        const created = await badgeService.createBadgeType(badgeType, testAdminId);
        createdBadgeTypeIds.push(created.id);
      } catch (error) {
        console.warn(`Failed to create test badge: ${error.message}`);
      }
    }
  });

  afterAll(async () => {
    // Cleanup test data
    for (const badgeTypeId of createdBadgeTypeIds) {
      try {
        await badgeService.deleteBadgeType(badgeTypeId, testAdminId);
      } catch (error) {
        console.warn(`Failed to cleanup badge type: ${error.message}`);
      }
    }
  });

  describe("Badge Progress Tracking", () => {
    it("should calculate badge progress correctly", async () => {
      const progress = await badgeService.getBadgeProgress(testUserId);
      
      expect(progress).toBeInstanceOf(Array);
      
      // Check that progress items have required properties
      for (const item of progress) {
        expect(item).toHaveProperty("badgeTypeId");
        expect(item).toHaveProperty("badgeType");
        expect(item).toHaveProperty("progress");
        expect(item).toHaveProperty("total");
        expect(item).toHaveProperty("isEarned");
        expect(typeof item.progress).toBe("number");
        expect(typeof item.total).toBe("number");
        expect(typeof item.isEarned).toBe("boolean");
      }
    });

    it("should get progress for specific badge type", async () => {
      if (createdBadgeTypeIds.length > 0) {
        const specificProgress = await badgeService.getBadgeProgress(
          testUserId, 
          createdBadgeTypeIds[0]
        );
        
        expect(specificProgress).toHaveLength(1);
        expect(specificProgress[0].badgeTypeId).toBe(createdBadgeTypeIds[0]);
      }
    });

    it("should handle progress for different criteria types", async () => {
      const progress = await badgeService.getBadgeProgress(testUserId);
      
      // Find automatic badges and check their progress calculation
      const automaticBadges = progress.filter(p => 
        p.badgeType.unlockType === "automatic"
      );
      
      for (const badge of automaticBadges) {
        expect(badge.progress).toBeGreaterThanOrEqual(0);
        expect(badge.total).toBeGreaterThan(0);
        expect(badge.progress).toBeLessThanOrEqual(badge.total);
      }
    });
  });

  describe("User Statistics", () => {
    it("should calculate comprehensive user statistics", async () => {
      const userStats = await badgeService.getUserStats(testUserId);
      
      expect(userStats).toHaveProperty("messageCount");
      expect(userStats).toHaveProperty("serverCount");
      expect(userStats).toHaveProperty("friendCount");
      expect(userStats).toHaveProperty("daysActive");
      expect(userStats).toHaveProperty("accountAge");
      expect(userStats).toHaveProperty("lastActive");
      expect(userStats).toHaveProperty("invitesSent");
      expect(userStats).toHaveProperty("invitesAccepted");
      
      // Check data types
      expect(typeof userStats.messageCount).toBe("number");
      expect(typeof userStats.serverCount).toBe("number");
      expect(typeof userStats.friendCount).toBe("number");
      expect(typeof userStats.daysActive).toBe("number");
      expect(typeof userStats.accountAge).toBe("number");
      expect(userStats.lastActive).toBeInstanceOf(Date);
      
      // Check reasonable values
      expect(userStats.messageCount).toBeGreaterThanOrEqual(0);
      expect(userStats.serverCount).toBeGreaterThanOrEqual(0);
      expect(userStats.friendCount).toBeGreaterThanOrEqual(0);
      expect(userStats.daysActive).toBeGreaterThanOrEqual(1);
      expect(userStats.accountAge).toBeGreaterThanOrEqual(0);
    });
  });

  describe("Badge Dashboard", () => {
    it("should provide comprehensive dashboard data", async () => {
      const userBadges = await badgeService.getUserBadges(testUserId);
      const availableBadges = await badgeService.getAvailableBadgesForUser(testUserId);
      const progress = await badgeService.getBadgeProgress(testUserId);
      
      // Check that we get arrays
      expect(userBadges).toBeInstanceOf(Array);
      expect(availableBadges).toBeInstanceOf(Array);
      expect(progress).toBeInstanceOf(Array);
      
      // Check that available badges don't include earned badges
      const earnedBadgeIds = new Set(userBadges.map(b => b.badgeTypeId));
      const availableBadgeIds = new Set(availableBadges.map(b => b.id));
      
      // No overlap between earned and available
      for (const earnedId of earnedBadgeIds) {
        expect(availableBadgeIds.has(earnedId)).toBe(false);
      }
    });

    it("should handle badge visibility correctly", async () => {
      const allBadges = await badgeService.getUserBadges(testUserId, false);
      const visibleBadges = await badgeService.getUserBadges(testUserId, true);
      
      expect(visibleBadges.length).toBeLessThanOrEqual(allBadges.length);
      
      // All visible badges should have isVisible = true
      for (const badge of visibleBadges) {
        expect(badge.isVisible).toBe(true);
      }
    });
  });

  describe("Badge Visibility Controls", () => {
    it("should update badge visibility", async () => {
      // First, assign a test badge
      if (createdBadgeTypeIds.length > 0) {
        try {
          await badgeService.assignBadge(
            testUserId,
            createdBadgeTypeIds[1], // Use manual badge
            testAdminId
          );
          
          // Test visibility update
          await badgeService.updateBadgeVisibility(
            testUserId,
            createdBadgeTypeIds[1],
            false
          );
          
          // Verify visibility was updated
          const userBadges = await badgeService.getUserBadges(testUserId);
          const updatedBadge = userBadges.find(b => b.badgeTypeId === createdBadgeTypeIds[1]);
          
          if (updatedBadge) {
            expect(updatedBadge.isVisible).toBe(false);
          }
          
          // Restore visibility
          await badgeService.updateBadgeVisibility(
            testUserId,
            createdBadgeTypeIds[1],
            true
          );
          
        } catch (error) {
          // Badge might already be assigned or other issues
          console.warn("Badge visibility test skipped:", error.message);
        }
      }
    });
  });

  describe("Badge Evaluation", () => {
    it("should evaluate user badges and return results", async () => {
      const evaluationResult = await badgeService.evaluateUserBadges(testUserId);
      
      expect(evaluationResult).toHaveProperty("userId");
      expect(evaluationResult).toHaveProperty("newBadges");
      expect(evaluationResult).toHaveProperty("evaluatedBadges");
      expect(evaluationResult).toHaveProperty("errors");
      
      expect(evaluationResult.userId).toBe(testUserId);
      expect(evaluationResult.newBadges).toBeInstanceOf(Array);
      expect(evaluationResult.evaluatedBadges).toBeInstanceOf(Array);
      expect(evaluationResult.errors).toBeInstanceOf(Array);
    });
  });

  describe("Badge History and Filtering", () => {
    it("should provide badge earning history", async () => {
      const userBadges = await badgeService.getUserBadges(testUserId);
      
      // Check that badges are ordered by assignment date (newest first)
      for (let i = 1; i < userBadges.length; i++) {
        expect(userBadges[i-1].assignedAt.getTime())
          .toBeGreaterThanOrEqual(userBadges[i].assignedAt.getTime());
      }
      
      // Check that each badge has required history information
      for (const badge of userBadges) {
        expect(badge).toHaveProperty("assignedAt");
        expect(badge.assignedAt).toBeInstanceOf(Date);
        expect(badge).toHaveProperty("isVisible");
        expect(typeof badge.isVisible).toBe("boolean");
      }
    });
  });

  describe("Error Handling", () => {
    it("should handle invalid badge type ID gracefully", async () => {
      await expect(
        badgeService.getBadgeProgress(testUserId, "invalid-badge-id")
      ).rejects.toThrow();
    });

    it("should handle invalid user ID gracefully", async () => {
      await expect(
        badgeService.getUserStats("invalid-user-id")
      ).rejects.toThrow();
    });

    it("should handle visibility update for non-existent badge", async () => {
      await expect(
        badgeService.updateBadgeVisibility(
          testUserId,
          "non-existent-badge-id",
          true
        )
      ).rejects.toThrow();
    });
  });

  describe("Performance", () => {
    it("should calculate progress efficiently", async () => {
      const startTime = Date.now();
      
      await badgeService.getBadgeProgress(testUserId);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(5000); // 5 seconds
    });

    it("should handle dashboard data efficiently", async () => {
      const startTime = Date.now();
      
      await Promise.all([
        badgeService.getUserBadges(testUserId),
        badgeService.getAvailableBadgesForUser(testUserId),
        badgeService.getBadgeProgress(testUserId),
        badgeService.getUserStats(testUserId)
      ]);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time
      expect(duration).toBeLessThan(10000); // 10 seconds
    });
  });
});