import { describe, test, expect, beforeAll, afterAll, beforeEach } from "bun:test";
import { WebSocketUtils } from "../../utils/websocket-utils";
import { WebSocketValidator } from "../../utils/websocket-validator";
import { correlationTracker } from "../../utils/correlation-tracker";
import { createTestUser, cleanupTestData } from "../helpers";
import type { CustomWebSocketData } from "../../types/websocket-standardization.types";

// Performance testing mock WebSocket
class PerformanceMockWebSocket {
  public data: CustomWebSocketData;
  public readyState: number = WebSocket.OPEN;
  public sentMessages: string[] = [];
  public isOpen: boolean = true;
  public sendTimes: number[] = [];
  public processingTimes: number[] = [];

  constructor(data: Partial<CustomWebSocketData> = {}) {
    this.data = {
      userId: data.userId || "perf-test-user",
      serverId: data.serverId,
      channelId: data.channelId,
      type: data.type || "channel",
      ...data
    };
  }

  send(message: string): void {
    if (this.isOpen) {
      const sendTime = performance.now();
      this.sendTimes.push(sendTime);
      this.sentMessages.push(message);
    }
  }

  close(): void {
    this.isOpen = false;
    this.readyState = WebSocket.CLOSED;
  }

  getAverageSendTime(): number {
    if (this.sendTimes.length < 2) return 0;
    
    const intervals = [];
    for (let i = 1; i < this.sendTimes.length; i++) {
      intervals.push(this.sendTimes[i] - this.sendTimes[i - 1]);
    }
    
    return intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
  }

  getMessageThroughput(): number {
    if (this.sendTimes.length === 0) return 0;
    
    const totalTime = this.sendTimes[this.sendTimes.length - 1] - this.sendTimes[0];
    return (this.sentMessages.length / totalTime) * 1000; // messages per second
  }

  clearMetrics(): void {
    this.sentMessages = [];
    this.sendTimes = [];
    this.processingTimes = [];
  }
}

describe("WebSocket Performance and Scalability Tests", () => {
  let testUser: any;
  let mockWs: PerformanceMockWebSocket;

  beforeAll(async () => {
    testUser = await createTestUser("perfuser", "<EMAIL>");
  });

  afterAll(async () => {
    await cleanupTestData();
  });

  beforeEach(() => {
    mockWs = new PerformanceMockWebSocket({ userId: testUser.id });
  });

  describe("Message Processing Performance", () => {
    test("should handle high-frequency message creation", async () => {
      const messageCount = 10000;
      const startTime = performance.now();

      for (let i = 0; i < messageCount; i++) {
        const message = WebSocketUtils.success("PERFORMANCE_TEST", {
          messageId: i,
          content: `Performance test message ${i}`,
          timestamp: Date.now()
        });
        
        WebSocketUtils.send(mockWs as any, message);
      }

      const endTime = performance.now();
      const totalTime = endTime - startTime;
      const messagesPerSecond = (messageCount / totalTime) * 1000;

      console.log(`Created and sent ${messageCount} messages in ${totalTime.toFixed(2)}ms`);
      console.log(`Throughput: ${messagesPerSecond.toFixed(2)} messages/second`);

      expect(mockWs.sentMessages.length).toBe(messageCount);
      expect(messagesPerSecond).toBeGreaterThan(1000); // Should handle at least 1000 msg/sec
      expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds
    });

    test("should handle large message payloads efficiently", async () => {
      const payloadSizes = [1024, 10240, 102400, 512000]; // 1KB, 10KB, 100KB, 500KB
      const results: Array<{ size: number; time: number; throughput: number }> = [];

      for (const size of payloadSizes) {
        const largePayload = "x".repeat(size);
        const messageCount = 100;
        
        mockWs.clearMetrics();
        const startTime = performance.now();

        for (let i = 0; i < messageCount; i++) {
          const message = WebSocketUtils.success("LARGE_PAYLOAD_TEST", {
            id: i,
            payload: largePayload,
            size: size
          });
          
          WebSocketUtils.send(mockWs as any, message);
        }

        const endTime = performance.now();
        const totalTime = endTime - startTime;
        const throughput = (messageCount / totalTime) * 1000;

        results.push({ size, time: totalTime, throughput });

        console.log(`${size} byte payload: ${totalTime.toFixed(2)}ms, ${throughput.toFixed(2)} msg/sec`);
      }

      // Verify all messages were sent
      expect(mockWs.sentMessages.length).toBe(100);

      // Performance should degrade gracefully with larger payloads
      for (let i = 1; i < results.length; i++) {
        expect(results[i].time).toBeGreaterThanOrEqual(results[i - 1].time);
      }
    });

    test("should handle concurrent message validation efficiently", async () => {
      const validationCount = 5000;
      const startTime = performance.now();

      const validationPromises = [];
      
      for (let i = 0; i < validationCount; i++) {
        const message = WebSocketUtils.success("VALIDATION_TEST", {
          testId: i,
          data: `Test data ${i}`,
          timestamp: Date.now()
        });

        // Simulate concurrent validation
        const validationPromise = new Promise<boolean>((resolve) => {
          const result = WebSocketValidator.validate(message);
          resolve(result.isValid);
        });
        
        validationPromises.push(validationPromise);
      }

      const validationResults = await Promise.all(validationPromises);
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      const validationsPerSecond = (validationCount / totalTime) * 1000;

      console.log(`Validated ${validationCount} messages in ${totalTime.toFixed(2)}ms`);
      console.log(`Validation throughput: ${validationsPerSecond.toFixed(2)} validations/second`);

      // All validations should pass
      expect(validationResults.every(result => result === true)).toBe(true);
      expect(validationsPerSecond).toBeGreaterThan(500); // Should handle at least 500 validations/sec
    });
  });

  describe("Memory Usage and Cleanup", () => {
    test("should manage correlation tracking memory efficiently", async () => {
      const correlationCount = 1000;
      const correlationIds: string[] = [];

      // Create many correlations
      for (let i = 0; i < correlationCount; i++) {
        const correlationId = `perf-correlation-${i}-${Date.now()}`;
        correlationIds.push(correlationId);
        correlationTracker.startTracking(correlationId, 1000); // 1 second timeout
      }

      // Verify all correlations are being tracked
      const activeCorrelations = correlationIds.filter(id => 
        correlationTracker.isTracking(id)
      );
      expect(activeCorrelations.length).toBe(correlationCount);

      // Wait for cleanup
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Verify correlations were cleaned up
      const remainingCorrelations = correlationIds.filter(id => 
        correlationTracker.isTracking(id)
      );
      expect(remainingCorrelations.length).toBe(0);
    });

    test("should handle message buffer overflow gracefully", async () => {
      const bufferSize = 1000;
      const overflowCount = 1500;

      // Fill buffer beyond capacity
      for (let i = 0; i < overflowCount; i++) {
        const message = WebSocketUtils.success("BUFFER_TEST", {
          messageNumber: i,
          timestamp: Date.now()
        });
        
        WebSocketUtils.send(mockWs as any, message);
      }

      // Should handle overflow without crashing
      expect(mockWs.sentMessages.length).toBe(overflowCount);
      
      // Memory usage should be bounded (simulated check)
      const estimatedMemoryUsage = mockWs.sentMessages.length * 200; // ~200 bytes per message
      expect(estimatedMemoryUsage).toBeLessThan(1024 * 1024); // Less than 1MB
    });
  });

  describe("Concurrent Connection Handling", () => {
    test("should handle multiple concurrent connections", async () => {
      const connectionCount = 100;
      const messagesPerConnection = 50;
      const connections: PerformanceMockWebSocket[] = [];

      // Create multiple connections
      for (let i = 0; i < connectionCount; i++) {
        const connection = new PerformanceMockWebSocket({
          userId: `concurrent-user-${i}`,
          serverId: "test-server",
          channelId: "test-channel"
        });
        connections.push(connection);
      }

      const startTime = performance.now();

      // Send messages from all connections concurrently
      const sendPromises = connections.map(async (connection, connIndex) => {
        for (let msgIndex = 0; msgIndex < messagesPerConnection; msgIndex++) {
          const message = WebSocketUtils.success("CONCURRENT_TEST", {
            connectionId: connIndex,
            messageId: msgIndex,
            content: `Message ${msgIndex} from connection ${connIndex}`
          });
          
          WebSocketUtils.send(connection as any, message);
        }
      });

      await Promise.all(sendPromises);
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      const totalMessages = connectionCount * messagesPerConnection;
      const throughput = (totalMessages / totalTime) * 1000;

      console.log(`Handled ${connectionCount} concurrent connections`);
      console.log(`Sent ${totalMessages} total messages in ${totalTime.toFixed(2)}ms`);
      console.log(`Concurrent throughput: ${throughput.toFixed(2)} messages/second`);

      // Verify all messages were sent
      const totalSentMessages = connections.reduce((sum, conn) => sum + conn.sentMessages.length, 0);
      expect(totalSentMessages).toBe(totalMessages);
      expect(throughput).toBeGreaterThan(1000); // Should maintain good throughput

      // Cleanup
      connections.forEach(conn => conn.close());
    });

    test("should handle connection churn efficiently", async () => {
      const churnCycles = 50;
      const connectionsPerCycle = 20;
      const connectionLifetime = 100; // milliseconds

      const startTime = performance.now();

      for (let cycle = 0; cycle < churnCycles; cycle++) {
        const cycleConnections: PerformanceMockWebSocket[] = [];

        // Create connections
        for (let i = 0; i < connectionsPerCycle; i++) {
          const connection = new PerformanceMockWebSocket({
            userId: `churn-user-${cycle}-${i}`,
            serverId: "churn-server"
          });
          cycleConnections.push(connection);

          // Send a few messages
          for (let j = 0; j < 5; j++) {
            const message = WebSocketUtils.success("CHURN_TEST", {
              cycle,
              connection: i,
              message: j
            });
            WebSocketUtils.send(connection as any, message);
          }
        }

        // Wait for connection lifetime
        await new Promise(resolve => setTimeout(resolve, connectionLifetime));

        // Close all connections
        cycleConnections.forEach(conn => conn.close());
      }

      const endTime = performance.now();
      const totalTime = endTime - startTime;
      const totalConnections = churnCycles * connectionsPerCycle;
      const connectionsPerSecond = (totalConnections / totalTime) * 1000;

      console.log(`Handled ${totalConnections} connection churn in ${totalTime.toFixed(2)}ms`);
      console.log(`Connection churn rate: ${connectionsPerSecond.toFixed(2)} connections/second`);

      expect(connectionsPerSecond).toBeGreaterThan(10); // Should handle reasonable churn rate
    });
  });

  describe("Broadcast Performance", () => {
    test("should handle large-scale broadcasting efficiently", async () => {
      const recipientCount = 500;
      const broadcastCount = 10;
      const recipients: PerformanceMockWebSocket[] = [];

      // Create recipient connections
      for (let i = 0; i < recipientCount; i++) {
        const recipient = new PerformanceMockWebSocket({
          userId: `broadcast-recipient-${i}`,
          serverId: "broadcast-server",
          channelId: "broadcast-channel"
        });
        recipients.push(recipient);
      }

      const startTime = performance.now();

      // Perform broadcasts
      for (let i = 0; i < broadcastCount; i++) {
        const broadcastMessage = WebSocketUtils.event("BROADCAST_EVENT", {
          broadcastId: i,
          content: `Broadcast message ${i}`,
          timestamp: Date.now()
        });

        // Simulate broadcasting to all recipients
        recipients.forEach(recipient => {
          WebSocketUtils.send(recipient as any, broadcastMessage);
        });
      }

      const endTime = performance.now();
      const totalTime = endTime - startTime;
      const totalDeliveries = recipientCount * broadcastCount;
      const deliveriesPerSecond = (totalDeliveries / totalTime) * 1000;

      console.log(`Broadcast ${broadcastCount} messages to ${recipientCount} recipients`);
      console.log(`Total deliveries: ${totalDeliveries} in ${totalTime.toFixed(2)}ms`);
      console.log(`Broadcast throughput: ${deliveriesPerSecond.toFixed(2)} deliveries/second`);

      // Verify all recipients received all broadcasts
      recipients.forEach(recipient => {
        expect(recipient.sentMessages.length).toBe(broadcastCount);
      });

      expect(deliveriesPerSecond).toBeGreaterThan(5000); // Should handle high broadcast throughput

      // Cleanup
      recipients.forEach(recipient => recipient.close());
    });

    test("should handle selective broadcasting with filtering", async () => {
      const totalRecipients = 1000;
      const filteredRecipients = 250; // 25% should receive the message
      const recipients: PerformanceMockWebSocket[] = [];

      // Create recipients with different properties for filtering
      for (let i = 0; i < totalRecipients; i++) {
        const recipient = new PerformanceMockWebSocket({
          userId: `filtered-user-${i}`,
          serverId: "filter-server",
          channelId: i % 4 === 0 ? "vip-channel" : "regular-channel" // 25% in VIP channel
        });
        recipients.push(recipient);
      }

      const startTime = performance.now();

      const selectiveMessage = WebSocketUtils.event("SELECTIVE_BROADCAST", {
        content: "VIP only message",
        targetChannel: "vip-channel"
      });

      // Simulate selective broadcasting (only to VIP channel)
      let deliveryCount = 0;
      recipients.forEach(recipient => {
        if (recipient.data.channelId === "vip-channel") {
          WebSocketUtils.send(recipient as any, selectiveMessage);
          deliveryCount++;
        }
      });

      const endTime = performance.now();
      const totalTime = endTime - startTime;
      const filteringRate = (totalRecipients / totalTime) * 1000;

      console.log(`Filtered ${totalRecipients} recipients in ${totalTime.toFixed(2)}ms`);
      console.log(`Delivered to ${deliveryCount} recipients (${(deliveryCount/totalRecipients*100).toFixed(1)}%)`);
      console.log(`Filtering rate: ${filteringRate.toFixed(2)} recipients/second`);

      expect(deliveryCount).toBe(filteredRecipients);
      expect(filteringRate).toBeGreaterThan(10000); // Should filter very quickly

      // Cleanup
      recipients.forEach(recipient => recipient.close());
    });
  });

  describe("Serialization Performance", () => {
    test("should handle JSON serialization efficiently", async () => {
      const serializationCount = 10000;
      const complexData = {
        users: Array.from({ length: 100 }, (_, i) => ({
          id: `user-${i}`,
          name: `User ${i}`,
          metadata: {
            lastActive: Date.now(),
            preferences: { theme: "dark", notifications: true }
          }
        })),
        channels: Array.from({ length: 50 }, (_, i) => ({
          id: `channel-${i}`,
          name: `Channel ${i}`,
          members: Array.from({ length: 20 }, (_, j) => `user-${j}`)
        }))
      };

      const startTime = performance.now();

      for (let i = 0; i < serializationCount; i++) {
        const message = WebSocketUtils.success("COMPLEX_DATA", {
          ...complexData,
          requestId: i,
          timestamp: Date.now()
        });

        const serialized = WebSocketUtils.serialize(message);
        expect(typeof serialized).toBe("string");
      }

      const endTime = performance.now();
      const totalTime = endTime - startTime;
      const serializationsPerSecond = (serializationCount / totalTime) * 1000;

      console.log(`Serialized ${serializationCount} complex messages in ${totalTime.toFixed(2)}ms`);
      console.log(`Serialization rate: ${serializationsPerSecond.toFixed(2)} serializations/second`);

      expect(serializationsPerSecond).toBeGreaterThan(100); // Should handle reasonable serialization rate
    });

    test("should handle message compression efficiently", async () => {
      const messageCount = 1000;
      const repetitiveContent = "This is a repetitive message that should compress well. ".repeat(100);

      const startTime = performance.now();
      let totalOriginalSize = 0;
      let totalCompressedSize = 0;

      for (let i = 0; i < messageCount; i++) {
        const message = WebSocketUtils.success("COMPRESSION_TEST", {
          content: repetitiveContent,
          messageId: i
        });

        const serialized = WebSocketUtils.serialize(message);
        totalOriginalSize += serialized.length;

        // Simulate compression (in real implementation, this would use actual compression)
        const compressionRatio = 0.3; // Assume 70% compression
        const compressedSize = Math.floor(serialized.length * compressionRatio);
        totalCompressedSize += compressedSize;
      }

      const endTime = performance.now();
      const totalTime = endTime - startTime;
      const compressionRate = (messageCount / totalTime) * 1000;
      const compressionRatio = (totalCompressedSize / totalOriginalSize) * 100;

      console.log(`Processed ${messageCount} messages for compression in ${totalTime.toFixed(2)}ms`);
      console.log(`Compression rate: ${compressionRate.toFixed(2)} messages/second`);
      console.log(`Compression ratio: ${compressionRatio.toFixed(1)}%`);

      expect(compressionRate).toBeGreaterThan(500); // Should handle compression efficiently
      expect(compressionRatio).toBeLessThan(50); // Should achieve good compression
    });
  });
});