import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  afterAll,
} from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  cleanupTestData,
  createMockRequest,
  parseResponse<PERSON>son,
  createPendingFriendRequest,
  createAcceptedFriendship,
  createBlockedRelationship,
} from "../helpers";
import { FriendshipSchema } from "../../db/schema";
import { eq } from "drizzle-orm";

import {
  sendFriendRequestHandler,
  accept<PERSON>riendRequestHand<PERSON>,
  reject<PERSON>riend<PERSON>equest<PERSON>and<PERSON>,
  cancelFriendRequestHandler,
  remove<PERSON><PERSON>d<PERSON><PERSON><PERSON>,
  block<PERSON><PERSON><PERSON><PERSON><PERSON>,
  unblockUserHandler,
  getUserFriendsHandler,
  getPendingFriendRequestsHandler,
  getSentFriendRequestsHandler,
  getBlockedUsersHandler,
} from "../../handlers/friends";

// Global cleanup after all tests
afterAll(async () => {
  await cleanupTestData();
});

describe("Friend Management API Endpoints", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  // Send Friend Request Tests
  test("sendFriendRequestHandler should create a friend request", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a mock request
    const req = createMockRequest({
      userId: user1.id,
      friendId: user2.id,
    });

    // Call the handler
    const response = await sendFriendRequestHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.friendship).toBeDefined();
    expect(data.friendship.userId).toBe(user1.id);
    expect(data.friendship.friendId).toBe(user2.id);
    expect(data.friendship.status).toBe("PENDING");

    // Verify in the database
    const dbFriendship = await db
      .select()
      .from(FriendshipSchema)
      .where(eq(FriendshipSchema.id, data.friendship.id))
      .limit(1);

    expect(dbFriendship.length).toBe(1);
    expect(dbFriendship[0].status).toBe("PENDING");
  });

  test("sendFriendRequestHandler should auto-accept if other user already sent a request", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a pending request from user2 to user1
    await createPendingFriendRequest(user2.id, user1.id);

    // Create a mock request
    const req = createMockRequest({
      userId: user1.id,
      friendId: user2.id,
    });

    // Call the handler
    const response = await sendFriendRequestHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.friendship).toBeDefined();
    expect(data.friendship.status).toBe("ACCEPTED");
    expect(data.isAutoAccepted).toBe(true);
  });

  test("sendFriendRequestHandler should reject if users are already friends", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create an accepted friendship
    await createAcceptedFriendship(user1.id, user2.id);

    // Create a mock request
    const req = createMockRequest({
      userId: user1.id,
      friendId: user2.id,
    });

    // Call the handler
    const response = await sendFriendRequestHandler(req);

    // Check the response
    expect(response.status).toBe(400);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.error).toBe("Already friends with this user");
  });

  // Accept Friend Request Tests
  test("acceptFriendRequestHandler should accept a pending request", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a pending request
    const pendingRequest = await createPendingFriendRequest(user1.id, user2.id);

    // Create a mock request
    const req = createMockRequest({
      userId: user2.id,
      friendshipId: pendingRequest.id,
    });

    // Call the handler
    const response = await acceptFriendRequestHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.friendship).toBeDefined();
    expect(data.friendship.status).toBe("ACCEPTED");

    // Verify in the database
    const dbFriendship = await db
      .select()
      .from(FriendshipSchema)
      .where(eq(FriendshipSchema.id, pendingRequest.id))
      .limit(1);

    expect(dbFriendship.length).toBe(1);
    expect(dbFriendship[0].status).toBe("ACCEPTED");
  });

  // Reject Friend Request Tests
  test("rejectFriendRequestHandler should delete a pending request", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a pending request
    const pendingRequest = await createPendingFriendRequest(user1.id, user2.id);

    // Create a mock request
    const req = createMockRequest({
      userId: user2.id,
      friendshipId: pendingRequest.id,
    });

    // Call the handler
    const response = await rejectFriendRequestHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);

    // Verify in the database
    const dbFriendship = await db
      .select()
      .from(FriendshipSchema)
      .where(eq(FriendshipSchema.id, pendingRequest.id))
      .limit(1);

    expect(dbFriendship.length).toBe(0);
  });

  // Cancel Friend Request Tests
  test("cancelFriendRequestHandler should delete a pending request", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a pending request
    const pendingRequest = await createPendingFriendRequest(user1.id, user2.id);

    // Create a mock request
    const req = createMockRequest({
      userId: user1.id,
      friendshipId: pendingRequest.id,
    });

    // Call the handler
    const response = await cancelFriendRequestHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);

    // Verify in the database
    const dbFriendship = await db
      .select()
      .from(FriendshipSchema)
      .where(eq(FriendshipSchema.id, pendingRequest.id))
      .limit(1);

    expect(dbFriendship.length).toBe(0);
  });

  // Remove Friend Tests
  test("removeFriendHandler should delete an accepted friendship", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create an accepted friendship
    const friendship = await createAcceptedFriendship(user1.id, user2.id);

    // Create a mock request
    const req = createMockRequest({
      userId: user1.id,
      friendshipId: friendship.id,
    });

    // Call the handler
    const response = await removeFriendHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);

    // Verify in the database
    const dbFriendship = await db
      .select()
      .from(FriendshipSchema)
      .where(eq(FriendshipSchema.id, friendship.id))
      .limit(1);

    expect(dbFriendship.length).toBe(0);
  });

  // Block User Tests
  test("blockUserHandler should create a blocked relationship", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a mock request
    const req = createMockRequest({
      userId: user1.id,
      targetUserId: user2.id,
    });

    // Call the handler
    const response = await blockUserHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.friendship).toBeDefined();
    expect(data.friendship.status).toBe("BLOCKED");

    // Verify in the database
    const dbRelationship = await db
      .select()
      .from(FriendshipSchema)
      .where(eq(FriendshipSchema.id, data.friendship.id))
      .limit(1);

    expect(dbRelationship.length).toBe(1);
    expect(dbRelationship[0].status).toBe("BLOCKED");
  });

  // Unblock User Tests
  test("unblockUserHandler should delete a blocked relationship", async () => {
    // Create two test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create a blocked relationship
    const blockedRelationship = await createBlockedRelationship(
      user1.id,
      user2.id,
    );

    // Create a mock request
    const req = createMockRequest({
      userId: user1.id,
      friendshipId: blockedRelationship.id,
    });

    // Call the handler
    const response = await unblockUserHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);

    // Verify in the database
    const dbRelationship = await db
      .select()
      .from(FriendshipSchema)
      .where(eq(FriendshipSchema.id, blockedRelationship.id))
      .limit(1);

    expect(dbRelationship.length).toBe(0);
  });

  // Get Friends Tests
  test("getUserFriendsHandler should return a user's friends", async () => {
    // Create three test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");
    const user3 = await createTestUser("user3", "<EMAIL>");

    // Create friendships
    await createAcceptedFriendship(user1.id, user2.id);
    await createAcceptedFriendship(user1.id, user3.id);

    // Create a mock request
    const req = new Request(
      `http://localhost:3000/api/friends?userId=${user1.id}`,
    );

    // Call the handler
    const response = await getUserFriendsHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.friends).toBeDefined();
    expect(data.friends.length).toBe(2);

    // Check that the friend data is correct
    const friendIds = data.friends.map((f: any) => f.friend.id);
    expect(friendIds).toContain(user2.id);
    expect(friendIds).toContain(user3.id);
  });

  // Get Pending Requests Tests
  test("getPendingFriendRequestsHandler should return pending requests", async () => {
    // Create three test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");
    const user3 = await createTestUser("user3", "<EMAIL>");

    // Create pending requests to user1
    await createPendingFriendRequest(user2.id, user1.id);
    await createPendingFriendRequest(user3.id, user1.id);

    // Create a mock request
    const req = new Request(
      `http://localhost:3000/api/friends/pending?userId=${user1.id}`,
    );

    // Call the handler
    const response = await getPendingFriendRequestsHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.pendingRequests).toBeDefined();
    expect(data.pendingRequests.length).toBe(2);

    // Check that the requester data is correct
    const requesterIds = data.pendingRequests.map((r: any) => r.requester.id);
    expect(requesterIds).toContain(user2.id);
    expect(requesterIds).toContain(user3.id);
  });

  // Get Sent Requests Tests
  test("getSentFriendRequestsHandler should return sent requests", async () => {
    // Create three test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");
    const user3 = await createTestUser("user3", "<EMAIL>");

    // Create sent requests from user1
    await createPendingFriendRequest(user1.id, user2.id);
    await createPendingFriendRequest(user1.id, user3.id);

    // Create a mock request
    const req = new Request(
      `http://localhost:3000/api/friends/sent?userId=${user1.id}`,
    );

    // Call the handler
    const response = await getSentFriendRequestsHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.sentRequests).toBeDefined();
    expect(data.sentRequests.length).toBe(2);

    // Check that the recipient data is correct
    const recipientIds = data.sentRequests.map((r: any) => r.recipient.id);
    expect(recipientIds).toContain(user2.id);
    expect(recipientIds).toContain(user3.id);
  });

  // Get Blocked Users Tests
  test("getBlockedUsersHandler should return blocked users", async () => {
    // Create three test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");
    const user3 = await createTestUser("user3", "<EMAIL>");

    // Create blocked relationships
    await createBlockedRelationship(user1.id, user2.id);
    await createBlockedRelationship(user1.id, user3.id);

    // Create a mock request
    const req = new Request(
      `http://localhost:3000/api/friends/blocked?userId=${user1.id}`,
    );

    // Call the handler
    const response = await getBlockedUsersHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.blockedUsers).toBeDefined();
    expect(data.blockedUsers.length).toBe(2);

    // Check that the blocked user data is correct
    const blockedIds = data.blockedUsers.map((b: any) => b.blocked.id);
    expect(blockedIds).toContain(user2.id);
    expect(blockedIds).toContain(user3.id);
  });
});
