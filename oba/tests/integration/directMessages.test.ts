import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  afterAll,
} from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  createTestDirectMessage,
  cleanupTestData,
  createMockRequest,
  parseResponseJson,
} from "../helpers";
import {
  sendDirectMessageHandler,
  editDirectMessageHandler,
  deleteDirectMessageHandler,
  markDirectMessageAsReadHandler,
  getDirectMessagesHandler,
  getUserContactsHandler,
} from "../../handlers/directMessages";

describe("Direct Message Handlers", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  test("sendDirectMessageHandler should send a direct message", async () => {
    // Create test users
    const sender = await createTestUser("sender", "<EMAIL>");
    const receiver = await createTestUser("receiver", "<EMAIL>");

    // Create a mock request
    const req = createMockRequest({
      senderId: sender.id,
      receiverId: receiver.id,
      content: "Test direct message",
    });

    // Call the send direct message handler
    const response = await sendDirectMessageHandler(req);

    // Check the response
    expect(response.status).toBe(201);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.message).toBeDefined();
    expect(data.message.senderId).toBe(sender.id);
    expect(data.message.receiverId).toBe(receiver.id);
    expect(data.message.content).toBe("Test direct message");
  });

  test("editDirectMessageHandler should edit a direct message", async () => {
    // Create test users
    const sender = await createTestUser("sender", "<EMAIL>");
    const receiver = await createTestUser("receiver", "<EMAIL>");

    // Create a direct message
    const message = await createTestDirectMessage(sender.id, receiver.id);

    // Create a mock request
    const req = createMockRequest({
      messageId: message.id,
      senderId: sender.id,
      content: "Updated direct message",
    });

    // Call the edit direct message handler
    const response = await editDirectMessageHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.message).toBeDefined();
    expect(data.message.id).toBe(message.id);
    expect(data.message.content).toBe("Updated direct message");
  });

  test("editDirectMessageHandler should reject unauthorized edit", async () => {
    // Create test users
    const sender = await createTestUser("sender", "<EMAIL>");
    const receiver = await createTestUser("receiver", "<EMAIL>");

    // Create a direct message
    const message = await createTestDirectMessage(sender.id, receiver.id);

    // Create a mock request with a different user
    const req = createMockRequest({
      messageId: message.id,
      senderId: receiver.id, // Different user
      content: "Updated direct message",
    });

    // Call the edit direct message handler
    const response = await editDirectMessageHandler(req);

    // Check the response
    expect(response.status).toBe(403);

    // Parse the response text
    const text = await response.text();

    // Check the response text
    expect(text).toContain("Unauthorized to edit this message");
  });

  test("deleteDirectMessageHandler should delete a direct message", async () => {
    // Create test users
    const sender = await createTestUser("sender", "<EMAIL>");
    const receiver = await createTestUser("receiver", "<EMAIL>");

    // Create a direct message
    const message = await createTestDirectMessage(sender.id, receiver.id);

    // Create a mock request
    const req = createMockRequest({
      messageId: message.id,
      senderId: sender.id,
    });

    // Call the delete direct message handler
    const response = await deleteDirectMessageHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.messageId).toBe(message.id);
  });

  test("markDirectMessageAsReadHandler should mark a message as read", async () => {
    // Create test users
    const sender = await createTestUser("sender", "<EMAIL>");
    const receiver = await createTestUser("receiver", "<EMAIL>");

    // Create a direct message
    const message = await createTestDirectMessage(sender.id, receiver.id);

    // Create a mock request
    const req = createMockRequest({
      messageId: message.id,
      userId: receiver.id,
    });

    // Call the mark as read handler
    const response = await markDirectMessageAsReadHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.messageId).toBe(message.id);
  });

  test("getDirectMessagesHandler should retrieve direct messages", async () => {
    // Create test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");

    // Create multiple messages between the users
    await createTestDirectMessage(
      user1.id,
      user2.id,
      "Message from user1 to user2 - 1",
    );
    await createTestDirectMessage(
      user2.id,
      user1.id,
      "Message from user2 to user1",
    );
    await createTestDirectMessage(
      user1.id,
      user2.id,
      "Message from user1 to user2 - 2",
    );

    // Create a mock request
    const req = new Request(
      `http://localhost:3000/api/directMessages?userId1=${user1.id}&userId2=${user2.id}&limit=10`,
    );

    // Call the get direct messages handler
    const response = await getDirectMessagesHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.messages).toBeDefined();
    expect(data.messages.length).toBe(3);

    // Messages should be in reverse chronological order (newest first)
    expect(data.messages[0].message.content).toBe(
      "Message from user1 to user2 - 2",
    );
    expect(data.messages[1].message.content).toBe(
      "Message from user2 to user1",
    );
    expect(data.messages[2].message.content).toBe(
      "Message from user1 to user2 - 1",
    );
  });

  test("getUserContactsHandler should retrieve user contacts", async () => {
    // Create test users
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");
    const user3 = await createTestUser("user3", "<EMAIL>");

    // Create messages between users
    await createTestDirectMessage(user1.id, user2.id);
    await createTestDirectMessage(user2.id, user1.id);
    await createTestDirectMessage(user1.id, user3.id);

    // Create a mock request
    const req = new Request(
      `http://localhost:3000/api/directMessages/contacts?userId=${user1.id}`,
    );

    // Call the get user contacts handler
    const response = await getUserContactsHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.contacts).toBeDefined();
    expect(data.contacts.length).toBe(2);

    // Check that user2 and user3 are in the contacts
    const contactUsernames = data.contacts.map((contact) => contact.username);
    expect(contactUsernames).toContain("user2");
    expect(contactUsernames).toContain("user3");
  });
});
