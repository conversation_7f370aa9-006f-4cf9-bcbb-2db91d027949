import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  afterAll,
} from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  cleanupTestData,
  createMockRequest,
  parseResponse<PERSON>son,
  createTestServer,
  createTestChannel,
  createTestChannelWithPrivacy,
  createTestRole,
  addAllowedRoleToChannel,
} from "../helpers";
import {
  ChannelSchema,
  ChannelPrivacySchema,
  ChannelAllowedRolesSchema,
} from "../../db/schema";
import { eq } from "drizzle-orm";

import {
  create<PERSON>hannelHandler,
  update<PERSON><PERSON>nel<PERSON>and<PERSON>,
  deleteChannelHandler,
  getChannelDetailsHandler,
} from "../../handlers/channels";

// Global cleanup after all tests
afterAll(async () => {
  await cleanupTestData();
});

describe("Channel Management API Endpoints", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  // Create Channel Tests
  test("createChannel<PERSON><PERSON><PERSON> should create a new channel", async () => {
    // Create a test user and server
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);

    // Create a mock request
    const req = createMockRequest({
      serverId: server.id,
      name: "test-channel",
      description: "A test channel",
      type: "TEXT",
      isPublic: true,
    });

    // Call the handler
    const response = await createChannelHandler(req);

    // Check the response
    expect(response.status).toBe(201);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.channel).toBeDefined();
    expect(data.channel.name).toBe("test-channel");
    expect(data.channel.serverId).toBe(server.id);
    expect(data.channel.type).toBe("TEXT");

    // Verify in the database
    const dbChannel = await db
      .select()
      .from(ChannelSchema)
      .where(eq(ChannelSchema.id, data.channel.id))
      .limit(1);

    expect(dbChannel.length).toBe(1);
    expect(dbChannel[0].name).toBe("test-channel");

    // Verify privacy settings
    const privacySettings = await db
      .select()
      .from(ChannelPrivacySchema)
      .where(eq(ChannelPrivacySchema.channelId, data.channel.id))
      .limit(1);

    expect(privacySettings.length).toBe(1);
    expect(privacySettings[0].isPublic).toBe(true);
  });

  test("createChannelHandler should create a private channel with allowed roles", async () => {
    // Create a test user and server
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);

    // Create a role
    const role = await createTestRole(server.id);

    // Create a mock request
    const req = createMockRequest({
      serverId: server.id,
      name: "private-channel",
      description: "A private channel",
      type: "TEXT",
      isPublic: false,
      allowedRoleIds: [role.id],
    });

    // Call the handler
    const response = await createChannelHandler(req);

    // Check the response
    expect(response.status).toBe(201);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.channel).toBeDefined();
    expect(data.channel.name).toBe("private-channel");

    // Verify privacy settings
    const privacySettings = await db
      .select()
      .from(ChannelPrivacySchema)
      .where(eq(ChannelPrivacySchema.channelId, data.channel.id))
      .limit(1);

    expect(privacySettings.length).toBe(1);
    expect(privacySettings[0].isPublic).toBe(false);

    // Verify allowed roles
    const allowedRoles = await db
      .select()
      .from(ChannelAllowedRolesSchema)
      .where(eq(ChannelAllowedRolesSchema.channelId, data.channel.id));

    expect(allowedRoles.length).toBe(1);
    expect(allowedRoles[0].roleId).toBe(role.id);
  });

  test("createChannelHandler should reject invalid input", async () => {
    // Create a test user and server
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);

    // Create a mock request with invalid data (missing name)
    const req = createMockRequest({
      serverId: server.id,
      type: "TEXT",
    });

    // Call the handler
    const response = await createChannelHandler(req);

    // Check the response
    expect(response.status).toBe(400);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.errors).toBeDefined();
  });

  // Update Channel Tests
  test("updateChannelHandler should update channel details", async () => {
    // Create a test user, server, and channel
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);
    const channel = await createTestChannel(server.id, "original-name", "TEXT");

    // Create a mock request
    const req = createMockRequest({
      channelId: channel.id,
      serverId: server.id,
      name: "updated-name",
      description: "Updated description",
      type: "ANNOUNCEMENT",
    });

    // Call the handler
    const response = await updateChannelHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.channel).toBeDefined();
    expect(data.channel.name).toBe("updated-name");
    expect(data.channel.description).toBe("Updated description");
    expect(data.channel.type).toBe("ANNOUNCEMENT");

    // Verify in the database
    const dbChannel = await db
      .select()
      .from(ChannelSchema)
      .where(eq(ChannelSchema.id, channel.id))
      .limit(1);

    expect(dbChannel.length).toBe(1);
    expect(dbChannel[0].name).toBe("updated-name");
    expect(dbChannel[0].description).toBe("Updated description");
    expect(dbChannel[0].type).toBe("ANNOUNCEMENT");
  });

  test("updateChannelHandler should update privacy settings", async () => {
    // Create a test user, server, and channel with privacy settings
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);
    const channel = await createTestChannelWithPrivacy(
      server.id,
      "public-channel",
      "TEXT",
      true,
    );

    // Create a mock request
    const req = createMockRequest({
      channelId: channel.id,
      serverId: server.id,
      isPublic: false,
    });

    // Call the handler
    const response = await updateChannelHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);

    // Verify privacy settings in the database
    const privacySettings = await db
      .select()
      .from(ChannelPrivacySchema)
      .where(eq(ChannelPrivacySchema.channelId, channel.id))
      .limit(1);

    expect(privacySettings.length).toBe(1);
    expect(privacySettings[0].isPublic).toBe(false);
  });

  test("updateChannelHandler should update allowed roles", async () => {
    // Create a test user, server, and channel with privacy settings
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);
    const channel = await createTestChannelWithPrivacy(
      server.id,
      "private-channel",
      "TEXT",
      false,
    );

    // Create roles
    const role1 = await createTestRole(server.id, "Role 1");
    const role2 = await createTestRole(server.id, "Role 2");

    // Add role1 to the channel
    await addAllowedRoleToChannel(channel.id, role1.id);

    // Create a mock request to update to role2
    const req = createMockRequest({
      channelId: channel.id,
      serverId: server.id,
      isPublic: false,
      allowedRoleIds: [role2.id],
    });

    // Call the handler
    const response = await updateChannelHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);

    // Verify allowed roles in the database
    const allowedRoles = await db
      .select()
      .from(ChannelAllowedRolesSchema)
      .where(eq(ChannelAllowedRolesSchema.channelId, channel.id));

    expect(allowedRoles.length).toBe(1);
    expect(allowedRoles[0].roleId).toBe(role2.id);
  });

  test("updateChannelHandler should return 404 for non-existent channel", async () => {
    // Generate a random UUID that doesn't exist
    const nonExistentId = crypto.randomUUID();

    // Create a test user and server
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);

    // Create a mock request
    const req = createMockRequest({
      channelId: nonExistentId,
      serverId: server.id,
      name: "updated-name",
    });

    // Call the handler
    const response = await updateChannelHandler(req);

    // Check the response
    expect(response.status).toBe(404);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.error).toBe("Channel not found");
  });

  // Delete Channel Tests
  test("deleteChannelHandler should delete a channel", async () => {
    // Create a test user, server, and channel
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);
    const channel = await createTestChannel(
      server.id,
      "delete-channel",
      "TEXT",
    );

    // Create a mock request
    const req = createMockRequest({
      channelId: channel.id,
      serverId: server.id,
    });

    // Call the handler
    const response = await deleteChannelHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.channelId).toBe(channel.id);

    // Verify that the channel was deleted
    const dbChannel = await db
      .select()
      .from(ChannelSchema)
      .where(eq(ChannelSchema.id, channel.id));

    expect(dbChannel.length).toBe(0);
  });

  test("deleteChannelHandler should return 404 for non-existent channel", async () => {
    // Generate a random UUID that doesn't exist
    const nonExistentId = crypto.randomUUID();

    // Create a test user and server
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);

    // Create a mock request
    const req = createMockRequest({
      channelId: nonExistentId,
      serverId: server.id,
    });

    // Call the handler
    const response = await deleteChannelHandler(req);

    // Check the response
    expect(response.status).toBe(404);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.error).toBe("Channel not found");
  });

  // Get Channel Details Tests
  test("getChannelDetailsHandler should return channel details", async () => {
    // Create a test user, server, and channel
    const user = await createTestUser("channeluser", "<EMAIL>");
    const server = await createTestServer(user.id);
    const channel = await createTestChannel(
      server.id,
      "details-channel",
      "TEXT",
    );

    // Create a mock request
    const req = new Request(
      `http://localhost:3000/api/channels/details?channelId=${channel.id}`,
    );

    // Call the handler
    const response = await getChannelDetailsHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.channel).toBeDefined();
    expect(data.channel.id).toBe(channel.id);
    expect(data.channel.name).toBe("details-channel");
    expect(data.channel.type).toBe("TEXT");
    expect(data.channel.serverId).toBe(server.id);
  });

  test("getChannelDetailsHandler should return 404 for non-existent channel", async () => {
    // Generate a random UUID that doesn't exist
    const nonExistentId = crypto.randomUUID();

    // Create a mock request
    const req = new Request(
      `http://localhost:3000/api/channels/details?channelId=${nonExistentId}`,
    );

    // Call the handler
    const response = await getChannelDetailsHandler(req);

    // Check the response
    expect(response.status).toBe(404);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.error).toBe("Channel not found");
  });

  test("getChannelDetailsHandler should reject missing channel ID", async () => {
    // Create a mock request without channel ID
    const req = new Request("http://localhost:3000/api/channels/details");

    // Call the handler
    const response = await getChannelDetailsHandler(req);

    // Check the response
    expect(response.status).toBe(400);

    // Parse the response text
    const text = await response.text();

    // Check the response text
    expect(text).toBe("Channel ID is required");
  });
});
