import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  afterAll,
} from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  createTestServer,
  createTestChannel,
  createTestMessage,
  cleanupTestData,
  createMockRequest,
  parseResponseJson,
} from "../helpers";
import {
  editMessageHandler,
  deleteMessageHandler,
  getChannelMessagesHandler,
} from "../../handlers/messages";

describe("Message Handlers", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  test("editMessageHand<PERSON> should edit a message", async () => {
    // Create test data
    const user = await createTestUser();
    const server = await createTestServer(user.id);
    const channel = await createTestChannel(server.id);
    const message = await createTestMessage(user.id, channel.id);

    // Create a mock request
    const req = createMockRequest({
      messageId: message.id,
      userId: user.id,
      content: "Updated message content",
      channelId: channel.id,
      serverId: server.id,
    });

    // Call the edit message handler
    const response = await editMessageHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.message).toBeDefined();
    expect(data.message.id).toBe(message.id);
    expect(data.message.content).toBe("Updated message content");
  });

  test("editMessageHandler should reject unauthorized edit", async () => {
    // Create test data
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");
    const server = await createTestServer(user1.id);
    const channel = await createTestChannel(server.id);
    const message = await createTestMessage(user1.id, channel.id);

    // Create a mock request with a different user
    const req = createMockRequest({
      messageId: message.id,
      userId: user2.id, // Different user
      content: "Updated message content",
      channelId: channel.id,
      serverId: server.id,
    });

    // Call the edit message handler
    const response = await editMessageHandler(req);

    // Check the response
    expect(response.status).toBe(403);

    // Parse the response text
    const text = await response.text();

    // Check the response text
    expect(text).toContain("Unauthorized to edit this message");
  });

  test("deleteMessageHandler should delete a message", async () => {
    // Create test data
    const user = await createTestUser();
    const server = await createTestServer(user.id);
    const channel = await createTestChannel(server.id);
    const message = await createTestMessage(user.id, channel.id);

    // Create a mock request
    const req = createMockRequest({
      messageId: message.id,
      userId: user.id,
      channelId: channel.id,
      serverId: server.id,
    });

    // Call the delete message handler
    const response = await deleteMessageHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.messageId).toBe(message.id);
  });

  test("deleteMessageHandler should reject unauthorized delete", async () => {
    // Create test data
    const user1 = await createTestUser("user1", "<EMAIL>");
    const user2 = await createTestUser("user2", "<EMAIL>");
    const server = await createTestServer(user1.id);
    const channel = await createTestChannel(server.id);
    const message = await createTestMessage(user1.id, channel.id);

    // Create a mock request with a different user
    const req = createMockRequest({
      messageId: message.id,
      userId: user2.id, // Different user
      channelId: channel.id,
      serverId: server.id,
    });

    // Call the delete message handler
    const response = await deleteMessageHandler(req);

    // Check the response
    expect(response.status).toBe(403);

    // Parse the response text
    const text = await response.text();

    // Check the response text
    expect(text).toContain("Unauthorized to delete this message");
  });

  test("getChannelMessagesHandler should retrieve channel messages", async () => {
    // Create test data
    const user = await createTestUser();
    const server = await createTestServer(user.id);
    const channel = await createTestChannel(server.id);

    // Create multiple messages
    await createTestMessage(user.id, channel.id, "Message 1");
    await createTestMessage(user.id, channel.id, "Message 2");
    await createTestMessage(user.id, channel.id, "Message 3");

    // Create a mock request
    const req = new Request(
      `http://localhost:3000/api/messages?channelId=${channel.id}&limit=10`,
    );

    // Call the get channel messages handler
    const response = await getChannelMessagesHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.messages).toBeDefined();
    expect(data.messages.length).toBe(3);

    // Messages should be in reverse chronological order (newest first)
    expect(data.messages[0].message.content).toBe("Message 3");
    expect(data.messages[1].message.content).toBe("Message 2");
    expect(data.messages[2].message.content).toBe("Message 1");
  });
});
