import { describe, it, expect, beforeAll, afterAll, beforeEach } from "bun:test";
import { db } from "../../db";
import { BadgeCollectionService } from "../../services/badge-collection.service";
import { BadgeService } from "../../services/badge.service";
import {
  BadgeCollectionSchema,
  BadgeTypeSchema,
  UserBadgeSchema,
  UserCollectionProgressSchema,
  UserSchema
} from "../../db/schema";
import { eq } from "drizzle-orm";
import type {
  CreateBadgeCollectionRequest,
  CreateBadgeTypeRequest,
  BadgeCollection,
  BadgeType,
  UserCollectionProgress
} from "../../types/badge.types";

describe("Badge Collections Integration Tests", () => {
  let collectionService: BadgeCollectionService;
  let badgeService: BadgeService;
  let testUserId: string;
  let testCollection: BadgeCollection;
  let testBadges: BadgeType[] = [];

  beforeAll(async () => {
    collectionService = new BadgeCollectionService(db);
    badgeService = new BadgeService(db);

    // Create a test user
    const [testUser] = await db
      .insert(UserSchema)
      .values({
        username: "testuser_collections",
        email: "<EMAIL>",
        passwordHash: "test_hash",
        isVerified: true
      })
      .returning();
    
    testUserId = testUser.id;
  });

  afterAll(async () => {
    // Clean up test data
    await db.delete(UserBadgeSchema).where(eq(UserBadgeSchema.userId, testUserId));
    await db.delete(UserCollectionProgressSchema).where(eq(UserCollectionProgressSchema.userId, testUserId));
    
    for (const badge of testBadges) {
      await db.delete(BadgeTypeSchema).where(eq(BadgeTypeSchema.id, badge.id));
    }
    
    if (testCollection) {
      await db.delete(BadgeCollectionSchema).where(eq(BadgeCollectionSchema.id, testCollection.id));
    }
    
    await db.delete(UserSchema).where(eq(UserSchema.id, testUserId));
  });

  beforeEach(async () => {
    // Clean up any existing test data
    await db.delete(UserBadgeSchema).where(eq(UserBadgeSchema.userId, testUserId));
    await db.delete(UserCollectionProgressSchema).where(eq(UserCollectionProgressSchema.userId, testUserId));
  });

  describe("Collection Management", () => {
    it("should create a progressive badge collection", async () => {
      const collectionData: CreateBadgeCollectionRequest = {
        collectionId: "test-progressive-collection",
        name: "Test Progressive Collection",
        description: "A test collection for progressive badges",
        type: "progressive",
        unlockedBy: "activity_and_time",
        completionReward: {
          badge: "completion-master",
          title: "Collection Master",
          perks: ["special_role", "exclusive_access"],
          visual: "golden_crown",
          animation: "sparkle_effect"
        }
      };

      testCollection = await collectionService.createCollection(collectionData, testUserId);

      expect(testCollection).toBeDefined();
      expect(testCollection.collectionId).toBe("test-progressive-collection");
      expect(testCollection.name).toBe("Test Progressive Collection");
      expect(testCollection.type).toBe("progressive");
      expect(testCollection.completionReward).toBeDefined();
      expect(testCollection.completionReward?.badge).toBe("completion-master");
    });

    it("should get collection by ID", async () => {
      const retrievedCollection = await collectionService.getCollectionById(testCollection.collectionId);

      expect(retrievedCollection).toBeDefined();
      expect(retrievedCollection.id).toBe(testCollection.id);
      expect(retrievedCollection.name).toBe(testCollection.name);
    });

    it("should update collection", async () => {
      const updates = {
        name: "Updated Test Collection",
        description: "Updated description"
      };

      const updatedCollection = await collectionService.updateCollection(
        testCollection.collectionId,
        updates,
        testUserId
      );

      expect(updatedCollection.name).toBe("Updated Test Collection");
      expect(updatedCollection.description).toBe("Updated description");
    });

    it("should get all collections with filters", async () => {
      const collections = await collectionService.getCollections({
        type: "progressive",
        isActive: true
      });

      expect(collections).toBeDefined();
      expect(collections.length).toBeGreaterThan(0);
      expect(collections.some(c => c.id === testCollection.id)).toBe(true);
    });
  });

  describe("Progressive Badge System", () => {
    beforeEach(async () => {
      // Create test badges in the collection
      const badgeData: CreateBadgeTypeRequest[] = [
        {
          collectionId: testCollection.id,
          badgeId: "first-step",
          name: "First Step",
          description: "Complete your first action",
          icon: "🥇",
          design: {
            shape: "circle",
            background: "linear-gradient(45deg, #FFD700, #FFA500)",
            colors: ["#FFD700", "#FFA500"]
          },
          criteria: {
            requirement: "Send first message",
            tracked: "message_count",
            type: "message_count",
            threshold: 1
          },
          unlockType: "automatic",
          displayOrder: 0,
          category: "milestone"
        },
        {
          collectionId: testCollection.id,
          badgeId: "getting-started",
          name: "Getting Started",
          description: "You're on your way",
          icon: "🚀",
          design: {
            shape: "hexagon",
            background: "linear-gradient(45deg, #4CAF50, #8BC34A)",
            colors: ["#4CAF50", "#8BC34A"]
          },
          criteria: {
            requirement: "Send 10 messages",
            tracked: "message_count",
            type: "message_count",
            threshold: 10
          },
          unlockType: "automatic",
          displayOrder: 1,
          category: "milestone"
        },
        {
          collectionId: testCollection.id,
          badgeId: "experienced",
          name: "Experienced",
          description: "You know what you're doing",
          icon: "⭐",
          design: {
            shape: "star",
            background: "linear-gradient(45deg, #2196F3, #03DAC6)",
            colors: ["#2196F3", "#03DAC6"]
          },
          criteria: {
            requirement: "Send 100 messages",
            tracked: "message_count",
            type: "message_count",
            threshold: 100
          },
          unlockType: "automatic",
          displayOrder: 2,
          category: "milestone"
        }
      ];

      for (const data of badgeData) {
        const badge = await badgeService.createBadgeType(data, testUserId);
        testBadges.push(badge);
      }

      // Update collection badge count
      await collectionService.updateCollectionBadgeCount(testCollection.collectionId);
    });

    it("should allow unlocking first badge in sequence", async () => {
      const firstBadge = testBadges.find(b => b.displayOrder === 0);
      expect(firstBadge).toBeDefined();

      const canUnlock = await collectionService.canUnlockNextBadge(
        testUserId,
        testCollection.collectionId,
        0
      );

      expect(canUnlock).toBe(true);
    });

    it("should prevent unlocking second badge without first", async () => {
      const canUnlock = await collectionService.canUnlockNextBadge(
        testUserId,
        testCollection.collectionId,
        1
      );

      expect(canUnlock).toBe(false);
    });

    it("should get next unlockable badge", async () => {
      const nextBadge = await collectionService.getNextUnlockableBadge(
        testUserId,
        testCollection.collectionId
      );

      expect(nextBadge).toBeDefined();
      expect(nextBadge?.displayOrder).toBe(0);
      expect(nextBadge?.name).toBe("First Step");
    });

    it("should track collection progress when badge is assigned", async () => {
      const firstBadge = testBadges.find(b => b.displayOrder === 0);
      expect(firstBadge).toBeDefined();

      // Assign the first badge
      await badgeService.assignBadge(testUserId, firstBadge!.id, testUserId);

      // Check collection progress
      const progress = await collectionService.getUserCollectionProgress(
        testUserId,
        testCollection.collectionId
      );

      expect(progress).toBeDefined();
      expect(progress?.badgesEarned).toBe(1);
      expect(progress?.totalBadges).toBe(3);
      expect(progress?.isCompleted).toBe(false);
    });

    it("should allow unlocking second badge after first is earned", async () => {
      const firstBadge = testBadges.find(b => b.displayOrder === 0);
      const secondBadge = testBadges.find(b => b.displayOrder === 1);
      expect(firstBadge).toBeDefined();
      expect(secondBadge).toBeDefined();

      // Assign the first badge
      await badgeService.assignBadge(testUserId, firstBadge!.id, testUserId);

      // Check if second badge can be unlocked
      const canUnlock = await collectionService.canUnlockNextBadge(
        testUserId,
        testCollection.collectionId,
        1
      );

      expect(canUnlock).toBe(true);

      // Get next unlockable badge
      const nextBadge = await collectionService.getNextUnlockableBadge(
        testUserId,
        testCollection.collectionId
      );

      expect(nextBadge).toBeDefined();
      expect(nextBadge?.displayOrder).toBe(1);
      expect(nextBadge?.name).toBe("Getting Started");
    });

    it("should prevent badge assignment if collection dependencies not met", async () => {
      const secondBadge = testBadges.find(b => b.displayOrder === 1);
      expect(secondBadge).toBeDefined();

      // Try to assign second badge without first
      await expect(
        badgeService.assignBadge(testUserId, secondBadge!.id, testUserId)
      ).rejects.toThrow("Previous badges in collection must be earned first");
    });

    it("should complete collection when all badges are earned", async () => {
      // Assign all badges in sequence
      for (const badge of testBadges.sort((a, b) => a.displayOrder - b.displayOrder)) {
        await badgeService.assignBadge(testUserId, badge.id, testUserId);
      }

      // Check collection completion
      const isCompleted = await collectionService.isCollectionCompleted(
        testUserId,
        testCollection.collectionId
      );

      expect(isCompleted).toBe(true);

      // Check final progress
      const progress = await collectionService.getUserCollectionProgress(
        testUserId,
        testCollection.collectionId
      );

      expect(progress?.isCompleted).toBe(true);
      expect(progress?.badgesEarned).toBe(3);
      expect(progress?.completionDate).toBeDefined();
    });

    it("should get detailed collection progress", async () => {
      // Assign first badge
      const firstBadge = testBadges.find(b => b.displayOrder === 0);
      await badgeService.assignBadge(testUserId, firstBadge!.id, testUserId);

      const detailedProgress = await collectionService.getDetailedCollectionProgress(
        testUserId,
        testCollection.collectionId
      );

      expect(detailedProgress).toBeDefined();
      expect(detailedProgress.collection.id).toBe(testCollection.id);
      expect(detailedProgress.progress.badgesEarned).toBe(1);
      expect(detailedProgress.earnedBadges).toHaveLength(1);
      expect(detailedProgress.nextBadge).toBeDefined();
      expect(detailedProgress.nextBadge?.displayOrder).toBe(1);
      expect(detailedProgress.completionReward).toBeDefined();
    });

    it("should get all user collection progress", async () => {
      // Assign first badge to create progress
      const firstBadge = testBadges.find(b => b.displayOrder === 0);
      await badgeService.assignBadge(testUserId, firstBadge!.id, testUserId);

      const allProgress = await collectionService.getUserCollectionProgressAll(testUserId);

      expect(allProgress).toBeDefined();
      expect(allProgress.length).toBeGreaterThan(0);
      expect(allProgress.some(p => p.collectionId === testCollection.id)).toBe(true);
    });
  });

  describe("Standalone Collection System", () => {
    let standaloneCollection: BadgeCollection;
    let standaloneBadges: BadgeType[] = [];

    beforeEach(async () => {
      // Create standalone collection
      const collectionData: CreateBadgeCollectionRequest = {
        collectionId: "test-standalone-collection",
        name: "Test Standalone Collection",
        description: "A test collection for standalone badges",
        type: "standalone"
      };

      standaloneCollection = await collectionService.createCollection(collectionData, testUserId);

      // Create standalone badges
      const badgeData: CreateBadgeTypeRequest[] = [
        {
          collectionId: standaloneCollection.id,
          badgeId: "helper",
          name: "Helper",
          description: "Helped another user",
          icon: "🤝",
          design: {
            shape: "circle",
            background: "#4CAF50",
            colors: ["#4CAF50"]
          },
          criteria: {
            requirement: "Help another user",
            tracked: "manual",
            type: "custom"
          },
          unlockType: "manual",
          displayOrder: 0,
          category: "community"
        },
        {
          collectionId: standaloneCollection.id,
          badgeId: "contributor",
          name: "Contributor",
          description: "Made a valuable contribution",
          icon: "💡",
          design: {
            shape: "hexagon",
            background: "#FF9800",
            colors: ["#FF9800"]
          },
          criteria: {
            requirement: "Make a contribution",
            tracked: "manual",
            type: "custom"
          },
          unlockType: "manual",
          displayOrder: 1,
          category: "community"
        }
      ];

      for (const data of badgeData) {
        const badge = await badgeService.createBadgeType(data, testUserId);
        standaloneBadges.push(badge);
      }

      await collectionService.updateCollectionBadgeCount(standaloneCollection.collectionId);
    });

    afterEach(async () => {
      // Clean up standalone collection data
      for (const badge of standaloneBadges) {
        await db.delete(BadgeTypeSchema).where(eq(BadgeTypeSchema.id, badge.id));
      }
      await db.delete(BadgeCollectionSchema).where(eq(BadgeCollectionSchema.id, standaloneCollection.id));
      standaloneBadges = [];
    });

    it("should allow any badge to be unlocked in standalone collection", async () => {
      const secondBadge = standaloneBadges.find(b => b.displayOrder === 1);
      expect(secondBadge).toBeDefined();

      const canUnlock = await collectionService.canUnlockNextBadge(
        testUserId,
        standaloneCollection.collectionId,
        1
      );

      expect(canUnlock).toBe(true);
    });

    it("should allow assigning any badge in standalone collection", async () => {
      const secondBadge = standaloneBadges.find(b => b.displayOrder === 1);
      expect(secondBadge).toBeDefined();

      // Should be able to assign second badge without first
      const userBadge = await badgeService.assignBadge(testUserId, secondBadge!.id, testUserId);

      expect(userBadge).toBeDefined();
      expect(userBadge.badgeTypeId).toBe(secondBadge!.id);
    });

    it("should track progress in standalone collection", async () => {
      const firstBadge = standaloneBadges.find(b => b.displayOrder === 0);
      await badgeService.assignBadge(testUserId, firstBadge!.id, testUserId);

      const progress = await collectionService.getUserCollectionProgress(
        testUserId,
        standaloneCollection.collectionId
      );

      expect(progress).toBeDefined();
      expect(progress?.badgesEarned).toBe(1);
      expect(progress?.totalBadges).toBe(2);
      expect(progress?.isCompleted).toBe(false);
    });
  });

  describe("Collection Completion and Rewards", () => {
    it("should handle collection completion reward", async () => {
      // Assign all badges to complete collection
      for (const badge of testBadges.sort((a, b) => a.displayOrder - b.displayOrder)) {
        await badgeService.assignBadge(testUserId, badge.id, testUserId);
      }

      const progress = await collectionService.getUserCollectionProgress(
        testUserId,
        testCollection.collectionId
      );

      expect(progress?.isCompleted).toBe(true);
      expect(progress?.completionRewardGranted).toBe(true);
    });

    it("should update collection badge count when badges are added", async () => {
      const updatedCollection = await collectionService.getCollectionById(testCollection.collectionId);
      expect(updatedCollection.totalBadges).toBe(testBadges.length);
    });
  });

  describe("Error Handling", () => {
    it("should throw error for non-existent collection", async () => {
      await expect(
        collectionService.getCollectionById("non-existent-collection")
      ).rejects.toThrow("Collection not found");
    });

    it("should throw error when trying to delete collection with badges", async () => {
      await expect(
        collectionService.deleteCollection(testCollection.collectionId, testUserId)
      ).rejects.toThrow("Cannot delete collection that contains badges");
    });

    it("should handle collection dependency check failure gracefully", async () => {
      const thirdBadge = testBadges.find(b => b.displayOrder === 2);
      expect(thirdBadge).toBeDefined();

      const result = await collectionService.assignBadgeWithCollectionCheck(
        testUserId,
        thirdBadge!.id,
        testUserId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain("Previous badges in collection must be earned first");
    });
  });
});