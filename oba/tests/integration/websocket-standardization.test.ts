import {
  describe,
  test,
  expect,
  beforeAll,
  afterAll,
  beforeEach,
  afterEach,
} from "bun:test";
import { WebSocketUtils } from "../../utils/websocket-utils";
import { WebSocketValidator } from "../../utils/websocket-validator";
import { WebSocketCompatibility } from "../../utils/websocket-compatibility";
import { WebSocketManager } from "../../manager/websocket.manager";
import { correlationTracker } from "../../utils/correlation-tracker";
import { MessageTypeRegistry } from "../../utils/message-type-registry";
import { createTestUser, cleanupTestData } from "../helpers";
import type { CustomWebSocketData } from "../../types/websocket.types";
import { WebSocketErrorCode } from "../../types/websocket-standardization.types";

// Mock WebSocket implementation for testing
class MockWebSocket {
  public data: CustomWebSocketData;
  public readyState: number = WebSocket.OPEN;
  public sentMessages: string[] = [];
  public isOpen: boolean = true;

  constructor(data: Partial<CustomWebSocketData> = {}) {
    this.data = {
      userId: data.userId || "test-user-id",
      serverId: data.serverId,
      channelId: data.channelId,
      type: data.type || "channel",
      ...data,
    };
  }

  send(message: string): void {
    if (this.isOpen) {
      this.sentMessages.push(message);
    }
  }

  close(): void {
    this.isOpen = false;
    this.readyState = WebSocket.CLOSED;
  }

  getLastMessage(): any {
    const lastMessage = this.sentMessages[this.sentMessages.length - 1];
    return lastMessage ? JSON.parse(lastMessage) : null;
  }

  getAllMessages(): any[] {
    return this.sentMessages.map((msg) => JSON.parse(msg));
  }

  clearMessages(): void {
    this.sentMessages = [];
  }
}

describe("WebSocket Standardization Integration Tests", () => {
  let testUser: any;
  let mockWs: MockWebSocket;
  let _wsManager: WebSocketManager;

  beforeAll(async () => {
    // Create test user
    testUser = await createTestUser("wstest", "<EMAIL>");
    wsManager = WebSocketManager.getInstance();
  });

  afterAll(async () => {
    await cleanupTestData();
  });

  beforeEach(() => {
    mockWs = new MockWebSocket({ userId: testUser.id });
  });

  afterEach(() => {
    mockWs.close();
  });

  describe("End-to-End Message Flow Tests", () => {
    test("should handle complete message flow with correlation", async () => {
      // Create a request message with correlation
      const requestMessage = WebSocketUtils.success(
        "MESSAGE_SEND",
        {
          content: "Hello, World!",
          channelId: "test-channel-id",
        },
        {
          correlationId: "test-correlation-123",
        },
      );

      // Simulate sending the message
      WebSocketUtils.send(mockWs as any, requestMessage);

      // Verify message was sent
      const sentMessage = mockWs.getLastMessage();
      expect(sentMessage).toBeDefined();
      expect(sentMessage.success).toBe(true);
      expect(sentMessage.data.content).toBe("Hello, World!");
      expect(sentMessage.meta.correlationId).toBe("test-correlation-123");
      expect(sentMessage.meta.id).toBeDefined();
      expect(sentMessage.meta.timestamp).toBeDefined();
    });

    test("should handle error responses with proper formatting", async () => {
      const errorMessage = WebSocketUtils.error(
        WebSocketErrorCode.SCHEMA_VALIDATION_FAILED,
        "Invalid message format",
        {
          correlationId: "error-correlation-456",
          details: { field: "content", expectedType: "string" },
        },
      );

      WebSocketUtils.send(mockWs as any, errorMessage);

      const sentMessage = mockWs.getLastMessage();
      expect(sentMessage).toBeDefined();
      expect(sentMessage.success).toBe(false);
      expect(sentMessage.error.code).toBe(
        WebSocketErrorCode.SCHEMA_VALIDATION_FAILED,
      );
      expect(sentMessage.error.message).toBe("Invalid message format");
      expect(sentMessage.meta.correlationId).toBe("error-correlation-456");
    });

    test("should handle event broadcasting", async () => {
      const eventMessage = WebSocketUtils.event("USER_JOINED", {
        userId: testUser.id,
        username: testUser.username,
        timestamp: new Date(),
      });

      WebSocketUtils.send(mockWs as any, eventMessage);

      const sentMessage = mockWs.getLastMessage();
      expect(sentMessage).toBeDefined();
      expect(sentMessage.event).toBe("USER_JOINED");
      expect(sentMessage.data.userId).toBe(testUser.id);
      expect(sentMessage.meta.source).toBe("server");
    });

    test("should validate message structure", async () => {
      const validMessage = WebSocketUtils.success("MESSAGE_SEND", {
        test: true,
      });
      const result = WebSocketValidator.validateMessageStructure(validMessage);

      expect(result.isValid).toBe(true);
      expect(result.errors).toBeUndefined();
    });

    test("should reject invalid message structure", async () => {
      const invalidMessage = { invalid: "structure" };
      const result =
        WebSocketValidator.validateMessageStructure(invalidMessage);

      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors!.length).toBeGreaterThan(0);
    });
  });

  describe("Backward Compatibility Tests", () => {
    test("should detect legacy message format", () => {
      const legacyMessage = {
        type: "MESSAGE_SEND",
        data: { content: "Legacy message" },
        timestamp: new Date(),
      };

      const isLegacy = WebSocketCompatibility.isLegacyMessage(legacyMessage);
      expect(isLegacy).toBe(true);
    });

    test("should convert legacy message to standardized format", () => {
      const legacyMessage = {
        type: "MESSAGE_SEND",
        data: { content: "Legacy message" },
        timestamp: new Date(),
      };

      const standardMessage =
        WebSocketCompatibility.convertLegacyMessage(legacyMessage);

      expect(standardMessage.meta).toBeDefined();
      expect(standardMessage.meta.id).toBeDefined();
      expect(standardMessage.meta.timestamp).toBeDefined();
      expect(standardMessage.meta.version).toBeDefined();
      expect(standardMessage.data).toEqual(legacyMessage.data);
    });

    test("should convert standardized message to legacy format", () => {
      const standardMessage = WebSocketUtils.success("MESSAGE_SEND", {
        content: "Standard message",
      });

      const legacyMessage =
        WebSocketCompatibility.convertToLegacyFormat(standardMessage);

      expect(legacyMessage.type).toBeDefined();
      expect(legacyMessage.data).toEqual(standardMessage.data);
      expect(legacyMessage.timestamp).toBeDefined();
      expect(legacyMessage.meta).toBeUndefined(); // Legacy format shouldn't have meta
    });

    test("should handle dual format support", async () => {
      // Test with legacy format
      const legacyMessage = {
        type: "MESSAGE_SEND",
        data: { content: "Legacy test" },
      };

      const isLegacy = WebSocketCompatibility.isLegacyMessage(legacyMessage);
      expect(isLegacy).toBe(true);

      // Test with standard format
      const standardMessage = WebSocketUtils.success("MESSAGE_SEND", {
        content: "Standard test",
      });

      const isStandard =
        !WebSocketCompatibility.isLegacyMessage(standardMessage);
      expect(isStandard).toBe(true);
    });
  });

  describe("Rate Limiting and Connection Management Tests", () => {
    test("should track rate limiting per user", async () => {
      const userId = testUser.id;

      // Simulate multiple rapid messages
      for (let i = 0; i < 5; i++) {
        const message = WebSocketUtils.success("TEST_MESSAGE", { count: i });
        WebSocketUtils.send(mockWs as any, message);
      }

      // Verify all messages were sent (within rate limit)
      expect(mockWs.sentMessages.length).toBe(5);
    });

    test("should handle connection lifecycle events", async () => {
      // Simulate connection establishment
      const connectEvent = WebSocketUtils.event("CONNECTION_ESTABLISHED", {
        userId: testUser.id,
        timestamp: new Date(),
      });

      WebSocketUtils.send(mockWs as any, connectEvent);

      const sentMessage = mockWs.getLastMessage();
      expect(sentMessage.event).toBe("CONNECTION_ESTABLISHED");
      expect(sentMessage.data.userId).toBe(testUser.id);
    });

    test("should handle heartbeat messages", async () => {
      const heartbeatMessage = WebSocketUtils.event("HEARTBEAT", {
        timestamp: new Date(),
      });

      WebSocketUtils.send(mockWs as any, heartbeatMessage);

      const sentMessage = mockWs.getLastMessage();
      expect(sentMessage.event).toBe("HEARTBEAT");
      expect(sentMessage.data.timestamp).toBeDefined();
    });

    test("should manage subscriptions", async () => {
      const subscribeMessage = WebSocketUtils.success("CHANNEL_SUBSCRIBE", {
        channelId: "test-channel-123",
        serverId: "test-server-456",
      });

      WebSocketUtils.send(mockWs as any, subscribeMessage);

      const sentMessage = mockWs.getLastMessage();
      expect(sentMessage.success).toBe(true);
      expect(sentMessage.data.channelId).toBe("test-channel-123");
    });
  });

  describe("Performance and Scalability Tests", () => {
    test("should handle high message throughput", async () => {
      const startTime = Date.now();
      const messageCount = 1000;

      // Send many messages rapidly
      for (let i = 0; i < messageCount; i++) {
        const message = WebSocketUtils.success("PERFORMANCE_TEST", {
          messageNumber: i,
          timestamp: Date.now(),
        });
        WebSocketUtils.send(mockWs as any, message);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Verify all messages were processed
      expect(mockWs.sentMessages.length).toBe(messageCount);

      // Performance assertion (should process 1000 messages in under 1 second)
      expect(duration).toBeLessThan(1000);

      console.log(`Processed ${messageCount} messages in ${duration}ms`);
    });

    test("should handle large message payloads", async () => {
      // Create a large payload (but within limits)
      const largeData = {
        content: "x".repeat(10000), // 10KB string
        metadata: Array.from({ length: 100 }, (_, i) => ({
          id: i,
          value: `data-${i}`,
          timestamp: Date.now(),
        })),
      };

      const message = WebSocketUtils.success("LARGE_MESSAGE", largeData);
      WebSocketUtils.send(mockWs as any, message);

      const sentMessage = mockWs.getLastMessage();
      expect(sentMessage.success).toBe(true);
      expect(sentMessage.data.content.length).toBe(10000);
      expect(sentMessage.data.metadata.length).toBe(100);
    });

    test("should handle concurrent connections", async () => {
      const connectionCount = 50;
      const mockConnections: MockWebSocket[] = [];

      // Create multiple mock connections
      for (let i = 0; i < connectionCount; i++) {
        const mockConnection = new MockWebSocket({
          userId: `user-${i}`,
          serverId: "test-server",
          channelId: "test-channel",
        });
        mockConnections.push(mockConnection);
      }

      // Send messages to all connections
      const broadcastMessage = WebSocketUtils.event("BROADCAST_TEST", {
        message: "Hello to all connections",
        timestamp: new Date(),
      });

      mockConnections.forEach((conn) => {
        WebSocketUtils.send(conn as any, broadcastMessage);
      });

      // Verify all connections received the message
      mockConnections.forEach((conn) => {
        const lastMessage = conn.getLastMessage();
        expect(lastMessage.event).toBe("BROADCAST_TEST");
        expect(lastMessage.data.message).toBe("Hello to all connections");
      });

      // Cleanup
      mockConnections.forEach((conn) => conn.close());
    });

    test("should handle correlation tracking at scale", async () => {
      const correlationCount = 100;
      const correlationIds: string[] = [];

      // Create multiple correlated messages
      for (let i = 0; i < correlationCount; i++) {
        const correlationId = `correlation-${i}-${Date.now()}`;
        correlationIds.push(correlationId);

        const message = WebSocketUtils.success(
          "CORRELATION_TEST",
          {
            testId: i,
          },
          {
            correlationId,
          },
        );

        WebSocketUtils.send(mockWs as any, message);
      }

      // Verify all messages have unique correlation IDs
      const sentMessages = mockWs.getAllMessages();
      expect(sentMessages.length).toBe(correlationCount);

      const receivedCorrelationIds = sentMessages.map(
        (msg) => msg.meta.correlationId,
      );
      const uniqueCorrelationIds = new Set(receivedCorrelationIds);

      expect(uniqueCorrelationIds.size).toBe(correlationCount);
    });

    test("should handle message validation at scale", async () => {
      const validationCount = 500;
      let validCount = 0;
      let invalidCount = 0;

      for (let i = 0; i < validationCount; i++) {
        let message: any;

        // Create mix of valid and invalid messages
        if (i % 2 === 0) {
          message = WebSocketUtils.success("VALID_MESSAGE", { data: i });
        } else {
          message = { invalid: "structure", data: i }; // Invalid structure
        }

        const result = WebSocketValidator.validateMessageStructure(message);
        if (result.isValid) {
          validCount++;
        } else {
          invalidCount++;
        }
      }

      expect(validCount).toBe(Math.floor(validationCount / 2));
      expect(invalidCount).toBe(Math.ceil(validationCount / 2));
      expect(validCount + invalidCount).toBe(validationCount);
    });
  });

  describe("Error Handling and Recovery Tests", () => {
    test("should handle malformed JSON gracefully", async () => {
      const malformedMessage = '{"invalid": json}';

      // This would normally cause JSON.parse to throw
      expect(() => {
        try {
          JSON.parse(malformedMessage);
        } catch (error) {
          // Simulate how the system should handle this
          const errorResponse = WebSocketUtils.error(
            WebSocketErrorCode.INVALID_MESSAGE_FORMAT,
            "Malformed JSON in message",
          );
          WebSocketUtils.send(mockWs as any, errorResponse);

          const sentMessage = mockWs.getLastMessage();
          expect(sentMessage.success).toBe(false);
          expect(sentMessage.error.code).toBe(
            WebSocketErrorCode.INVALID_MESSAGE_FORMAT,
          );
        }
      }).not.toThrow();
    });

    test("should handle connection drops gracefully", async () => {
      // Simulate connection drop
      mockWs.close();

      // Try to send message to closed connection
      const message = WebSocketUtils.success("TEST_MESSAGE", { test: true });

      // Should not throw error when sending to closed connection
      expect(() => {
        WebSocketUtils.send(mockWs as any, message);
      }).not.toThrow();

      // Message should not be added to closed connection
      expect(mockWs.sentMessages.length).toBe(0);
    });

    test("should handle correlation timeouts", async () => {
      const correlationId = "timeout-test-123";

      // Start correlation tracking
      correlationTracker.startTracking(correlationId, "100"); // 100ms timeout

      // Wait for timeout
      await new Promise((resolve) => setTimeout(resolve, 150));

      // Check if correlation was cleaned up
      const isTracking = correlationTracker.isTracking(correlationId);
      expect(isTracking).toBe(false);
    });

    test("should handle message size limits", async () => {
      // Create oversized message (simulate 2MB payload)
      const oversizedData = "x".repeat(2 * 1024 * 1024);

      const message = WebSocketUtils.success("OVERSIZED_MESSAGE", {
        content: oversizedData,
      });

      // In a real implementation, this would be rejected
      // For testing, we simulate the error response
      const errorResponse = WebSocketUtils.error(
        WebSocketErrorCode.MESSAGE_TOO_LARGE,
        "Message exceeds size limit",
        {
          details: {
            maxSize: 1024 * 1024,
            actualSize: oversizedData.length,
          },
        },
      );

      WebSocketUtils.send(mockWs as any, errorResponse);

      const sentMessage = mockWs.getLastMessage();
      expect(sentMessage.success).toBe(false);
      expect(sentMessage.error.code).toBe(WebSocketErrorCode.MESSAGE_TOO_LARGE);
      expect(sentMessage.error.details.maxSize).toBe(1024 * 1024);
    });
  });

  describe("Message Type Registry Integration", () => {
    test("should validate message types against registry", () => {
      // Test with registered message type
      const registeredType = "MESSAGE_SEND";
      const metadata = MessageTypeRegistry.getMessageType(registeredType);
      expect(metadata).toBeDefined();
    });

    test("should handle unregistered message types", () => {
      const unregisteredType = "UNKNOWN_MESSAGE_TYPE";
      const metadata = MessageTypeRegistry.getMessageType(unregisteredType);
      expect(metadata).toBeUndefined();
    });

    test("should get message type metadata", () => {
      const messageType = "MESSAGE_SEND";
      const metadata = MessageTypeRegistry.getMessageType(messageType);

      expect(metadata).toBeDefined();
      expect(metadata?.category).toBeDefined();
      expect(metadata?.requiresAuth).toBeDefined();
    });
  });

  describe("WebSocket Utilities Integration", () => {
    test("should serialize and deserialize messages correctly", () => {
      const originalMessage = WebSocketUtils.success("TEST_MESSAGE", {
        content: "Test content",
        timestamp: new Date(),
      });

      const serialized = WebSocketUtils.serialize(originalMessage);
      expect(typeof serialized).toBe("string");

      const deserialized = JSON.parse(serialized);
      expect(deserialized.success).toBe(originalMessage.success);
      expect(deserialized.data).toEqual(originalMessage.data);
      expect(deserialized.meta.id).toBe(originalMessage.meta.id);
    });

    test("should handle broadcast filtering", async () => {
      const connections = [
        new MockWebSocket({ userId: "user1" }),
        new MockWebSocket({ userId: "user2" }),
        new MockWebSocket({ userId: "user3" }),
      ];

      const message = WebSocketUtils.event("BROADCAST_TEST", {
        content: "Broadcast message",
      });

      // Simulate broadcast with exclusion
      connections.forEach((conn, index) => {
        if (index !== 1) {
          // Exclude user2
          WebSocketUtils.send(conn as any, message);
        }
      });

      // Verify user1 and user3 received message, user2 didn't
      expect(connections[0].sentMessages.length).toBe(1);
      expect(connections[1].sentMessages.length).toBe(0);
      expect(connections[2].sentMessages.length).toBe(1);

      connections.forEach((conn) => conn.close());
    });
  });
});
