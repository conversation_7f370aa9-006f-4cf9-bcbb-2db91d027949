import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  afterAll,
} from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  cleanupTestData,
  createMockRequest,
  parseResponseJson,
  createTestServer,
  createTestChannel,
  createTestCategory,
} from "../helpers";
import { ChannelSchema, ChannelCategorySchema } from "../../db/schema";
import { eq } from "drizzle-orm";

import {
  createCategoryHand<PERSON>,
  updateCategoryHandler,
  deleteCategoryHandler,
  getServerCategoriesHandler,
  getCategoryDetailsHandler,
  getCategoryChannelsHandler,
} from "../../handlers/categories";

import {
  createChannelHandler,
  updateChannelHandler,
} from "../../handlers/channels";

// Global cleanup after all tests
afterAll(async () => {
  await cleanupTestData();
});

describe("Channel Categories API", () => {
  let testUser: any;
  let testServer: any;
  let testCategory: any;
  let testChannel: any;

  beforeEach(async () => {
    // Create test user and server for each test
    testUser = await createTestUser(db);
    testServer = await createTestServer(db, testUser.id);
    testCategory = await createTestCategory(db, testServer.id, "Test Category");
    testChannel = await createTestChannel(db, testServer.id, "Test Channel");
  });

  afterEach(async () => {
    // Clean up after each test
    await db
      .delete(ChannelSchema)
      .where(eq(ChannelSchema.serverId, testServer.id));
    await db
      .delete(ChannelCategorySchema)
      .where(eq(ChannelCategorySchema.serverId, testServer.id));
  });

  test("should create a new category", async () => {
    const req = createMockRequest({
      method: "POST",
      body: {
        serverId: testServer.id,
        name: "New Category",
        description: "A test category",
        position: 1,
        userId: testUser.id,
      },
    });

    const response = await createCategoryHandler(req);
    expect(response.status).toBe(200);

    const responseData = await parseResponseJson(response);
    expect(responseData.success).toBe(true);
    expect(responseData.category.name).toBe("New Category");
    expect(responseData.category.description).toBe("A test category");
    expect(responseData.category.position).toBe(1);
    expect(responseData.category.serverId).toBe(testServer.id);
  });

  test("should update an existing category", async () => {
    const req = createMockRequest({
      method: "PUT",
      body: {
        categoryId: testCategory.id,
        serverId: testServer.id,
        name: "Updated Category",
        description: "Updated description",
        position: 2,
        userId: testUser.id,
      },
    });

    const response = await updateCategoryHandler(req);
    expect(response.status).toBe(200);

    const responseData = await parseResponseJson(response);
    expect(responseData.success).toBe(true);
    expect(responseData.category.name).toBe("Updated Category");
    expect(responseData.category.description).toBe("Updated description");
    expect(responseData.category.position).toBe(2);
  });

  test("should delete a category", async () => {
    const req = createMockRequest({
      method: "DELETE",
      body: {
        categoryId: testCategory.id,
        serverId: testServer.id,
        userId: testUser.id,
      },
    });

    const response = await deleteCategoryHandler(req);
    expect(response.status).toBe(200);

    const responseData = await parseResponseJson(response);
    expect(responseData.success).toBe(true);
    expect(responseData.categoryId).toBe(testCategory.id);

    // Verify the category was deleted
    const categories = await db
      .select()
      .from(ChannelCategorySchema)
      .where(eq(ChannelCategorySchema.id, testCategory.id));

    expect(categories.length).toBe(0);
  });

  test("should get server categories", async () => {
    // Create a second category to test listing
    await createTestCategory(
      db,
      testServer.id,
      "Second Category",
      "Another category",
      2,
    );

    const req = createMockRequest({
      method: "GET",
      url: `/api/categories/server?serverId=${testServer.id}`,
    });

    const response = await getServerCategoriesHandler(req);
    expect(response.status).toBe(200);

    const responseData = await parseResponseJson(response);
    expect(responseData.success).toBe(true);
    expect(responseData.categories.length).toBe(2);
    expect(responseData.categories[0].name).toBe("Test Category");
    expect(responseData.categories[1].name).toBe("Second Category");
  });

  test("should get category details", async () => {
    const req = createMockRequest({
      method: "GET",
      url: `/api/categories/details?categoryId=${testCategory.id}`,
    });

    const response = await getCategoryDetailsHandler(req);
    expect(response.status).toBe(200);

    const responseData = await parseResponseJson(response);
    expect(responseData.success).toBe(true);
    expect(responseData.category.id).toBe(testCategory.id);
    expect(responseData.category.name).toBe("Test Category");
  });

  test("should create a channel in a category", async () => {
    const req = createMockRequest({
      method: "POST",
      body: {
        serverId: testServer.id,
        name: "Categorized Channel",
        description: "A channel in a category",
        type: "TEXT",
        categoryId: testCategory.id,
        position: 1,
        userId: testUser.id,
      },
    });

    const response = await createChannelHandler(req);
    expect(response.status).toBe(200);

    const responseData = await parseResponseJson(response);
    expect(responseData.success).toBe(true);
    expect(responseData.channel.name).toBe("Categorized Channel");
    expect(responseData.channel.categoryId).toBe(testCategory.id);
    expect(responseData.channel.position).toBe(1);
  });

  test("should update a channel to move it to a category", async () => {
    const req = createMockRequest({
      method: "PUT",
      body: {
        channelId: testChannel.id,
        serverId: testServer.id,
        categoryId: testCategory.id,
        position: 2,
        userId: testUser.id,
      },
    });

    const response = await updateChannelHandler(req);
    expect(response.status).toBe(200);

    const responseData = await parseResponseJson(response);
    expect(responseData.success).toBe(true);
    expect(responseData.channel.categoryId).toBe(testCategory.id);
    expect(responseData.channel.position).toBe(2);
  });

  test("should get channels in a category", async () => {
    // First, create a channel in the category
    await db
      .update(ChannelSchema)
      .set({ categoryId: testCategory.id })
      .where(eq(ChannelSchema.id, testChannel.id));

    const req = createMockRequest({
      method: "GET",
      url: `/api/categories/channels?categoryId=${testCategory.id}`,
    });

    const response = await getCategoryChannelsHandler(req);
    expect(response.status).toBe(200);

    const responseData = await parseResponseJson(response);
    expect(responseData.success).toBe(true);
    expect(responseData.channels.length).toBe(1);
    expect(responseData.channels[0].id).toBe(testChannel.id);
  });
});
