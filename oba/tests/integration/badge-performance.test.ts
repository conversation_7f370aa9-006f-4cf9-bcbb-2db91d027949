import { describe, test, expect, beforeEach, afterEach, mock } from "bun:test";
import { BadgeService } from "../../services/badge.service";
import { BadgeEvaluationService } from "../../services/badge-evaluation.service";
import type {
  BadgeType,
  UserBadge,
  EvaluationResult,
  UserStats,
  BadgeCriteria
} from "../../types/badge.types";

// Mock database with performance simulation
const mockDb = {
  select: mock(() => ({ 
    from: mock(() => ({ 
      where: mock(() => ({ 
        limit: mock(() => []),
        orderBy: mock(() => [])
      })) 
    })) 
  })),
  insert: mock(() => ({ 
    values: mock(() => ({ 
      returning: mock(() => []),
      onConflictDoNothing: mock(() => ({ returning: mock(() => []) }))
    })) 
  })),
  update: mock(() => ({ 
    set: mock(() => ({ 
      where: mock(() => ({ 
        returning: mock(() => []) 
      })) 
    })) 
  })),
  delete: mock(() => ({ 
    where: mock(() => ({ 
      returning: mock(() => []) 
    })) 
  })),
  transaction: mock((callback) => callback(mockDb))
};

// Mock utility functions with performance simulation
const mockBadgeUtils = {
  createBadgeType: mock(),
  getBadgeTypeById: mock(),
  getBadgeTypes: mock(),
  updateBadgeType: mock(),
  deleteBadgeType: mock(),
  assignBadgeToUser: mock(),
  removeBadgeFromUser: mock(),
  getUserBadges: mock(),
  getUserStats: mock(),
  getBadgeStats: mock(),
  getBadgeLeaderboard: mock(),
  getAvailableBadgesForUser: mock(),
  getBadgeProgress: mock(),
  bulkAssignBadges: mock()
};

const mockBadgeEvaluation = {
  evaluateUserForAutomaticBadges: mock(),
  batchEvaluateUsers: mock(),
  evaluateAllUsersForAutomaticBadges: mock(),
  reevaluateBadgeTypeForAllUsers: mock(),
  getUsersNearBadgeCompletion: mock(),
  evaluateBadgeCriteria: mock()
};

const mockBadgeWebSocket = {
  broadcastBadgeAssigned: mock(),
  broadcastBadgeRemoved: mock(),
  broadcastBatchBadgeAssigned: mock(),
  broadcastBatchProgressUpdates: mock()
};

const mockPermissions = {
  hasServerPermission: mock()
};

// Mock the imports
mock.module("../../db/utils/badge-utils", () => mockBadgeUtils);
mock.module("../../db/utils/badge-evaluation", () => mockBadgeEvaluation);
mock.module("../../utils/badge-websocket", () => ({ badgeWebSocketService: mockBadgeWebSocket }));
mock.module("../../utils/permissions", () => mockPermissions);
mock.module("../../db", () => ({ db: mockDb }));

describe("Badge System Performance Tests", () => {
  let badgeService: BadgeService;
  let badgeEvaluationService: BadgeEvaluationService;
  
  const mockAdminId = "550e8400-e29b-41d4-a716-446655440000";

  beforeEach(() => {
    badgeService = new BadgeService(mockDb as any);
    badgeEvaluationService = new BadgeEvaluationService(mockDb as any);
    
    // Reset all mocks
    Object.values(mockBadgeUtils).forEach(mock => mock.mockReset());
    Object.values(mockBadgeEvaluation).forEach(mock => mock.mockReset());
    Object.values(mockBadgeWebSocket).forEach(mock => mock.mockReset());
    Object.values(mockPermissions).forEach(mock => mock.mockReset());

    // Setup default mock behaviors
    mockPermissions.hasServerPermission.mockResolvedValue(true);
    mockBadgeWebSocket.broadcastBadgeAssigned.mockResolvedValue(undefined);
    mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);
    mockBadgeWebSocket.broadcastBatchProgressUpdates.mockResolvedValue(undefined);
  });

  afterEach(() => {
    // Clean up any test state
  });

  // Helper function to generate mock user data
  function generateMockUsers(count: number): string[] {
    return Array.from({ length: count }, (_, i) => `user-${i.toString().padStart(6, '0')}`);
  }

  // Helper function to generate mock user stats
  function generateMockUserStats(userId: string): UserStats {
    const hash = userId.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    
    return {
      messageCount: Math.abs(hash % 1000),
      serverCount: Math.abs(hash % 10),
      friendCount: Math.abs(hash % 50),
      daysActive: Math.abs(hash % 365),
      accountAge: Math.abs(hash % 1000),
      lastActive: new Date(Date.now() - Math.abs(hash % ********)),
      invitesSent: Math.abs(hash % 20),
      invitesAccepted: Math.abs(hash % 15),
      feedbackSubmitted: Math.abs(hash % 5),
      moderationActions: Math.abs(hash % 3)
    };
  }

  // Helper function to generate mock badge types
  function generateMockBadgeTypes(count: number): BadgeType[] {
    return Array.from({ length: count }, (_, i) => ({
      id: `badge-${i}`,
      badgeId: `badge-${i}`,
      name: `Badge ${i}`,
      description: `Description for badge ${i}`,
      icon: "🏆",
      design: {
        shape: "circle",
        background: `#${Math.floor(Math.random() * ********).toString(16)}`,
        colors: [`#${Math.floor(Math.random() * ********).toString(16)}`]
      },
      criteria: {
        requirement: `Requirement ${i}`,
        tracked: "message_count",
        type: "message_count" as const,
        threshold: (i + 1) * 10
      },
      unlockType: "automatic" as const,
      category: "achievement" as const,
      displayOrder: i,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }));
  }

  describe("Large Scale User Evaluation", () => {
    test("should handle evaluation of 1000 users efficiently", async () => {
      const userCount = 1000;
      const userIds = generateMockUsers(userCount);
      
      // Mock batch evaluation with simulated processing time
      mockBadgeEvaluation.batchEvaluateUsers.mockImplementation(async (db, users) => {
        // Simulate processing time (1ms per user)
        await new Promise(resolve => setTimeout(resolve, users.length));
        
        return users.map((userId: string): EvaluationResult => ({
          userId,
          newBadges: Math.random() > 0.8 ? [{
            id: `new-badge-${userId}`,
            userId,
            badgeTypeId: "common-badge",
            assignedAt: new Date(),
            isVisible: true
          }] : [],
          evaluatedBadges: ["badge-1", "badge-2"],
          collectionProgress: [],
          errors: []
        }));
      });

      const startTime = Date.now();
      const results = await badgeEvaluationService.evaluateUsers(userIds);
      const endTime = Date.now();

      expect(results).toHaveLength(userCount);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
      expect(mockBadgeEvaluation.batchEvaluateUsers).toHaveBeenCalledWith(mockDb, userIds);
    });

    test("should handle evaluation of 10000 users with batching", async () => {
      const userCount = 10000;
      const userIds = generateMockUsers(userCount);
      
      // Mock all users evaluation with batching
      mockBadgeEvaluation.evaluateAllUsersForAutomaticBadges.mockImplementation(async (db, batchSize = 50) => {
        const totalBatches = Math.ceil(userCount / batchSize);
        let processedUsers = 0;
        let totalNewBadges = 0;
        
        // Simulate batch processing
        for (let i = 0; i < totalBatches; i++) {
          const batchUsers = Math.min(batchSize, userCount - processedUsers);
          processedUsers += batchUsers;
          totalNewBadges += Math.floor(batchUsers * 0.1); // 10% get new badges
          
          // Small delay to simulate database operations
          await new Promise(resolve => setTimeout(resolve, 1));
        }
        
        return {
          totalUsers: userCount,
          processedUsers,
          totalNewBadges,
          errors: []
        };
      });

      const startTime = Date.now();
      const result = await badgeEvaluationService.evaluateAllUsers(100); // Batch size of 100
      const endTime = Date.now();

      expect(result.totalUsers).toBe(userCount);
      expect(result.processedUsers).toBe(userCount);
      expect(result.totalNewBadges).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(10000); // Should complete within 10 seconds
    });

    test("should efficiently check criteria for large user sets", async () => {
      const userCount = 5000;
      const userIds = generateMockUsers(userCount);
      
      const criteria: BadgeCriteria = {
        requirement: "Send 100 messages",
        tracked: "message_count",
        type: "message_count",
        threshold: 100
      };

      // Mock user stats retrieval with caching simulation
      const userStatsCache = new Map<string, UserStats>();
      mockBadgeUtils.getUserStats.mockImplementation(async (db, userId) => {
        if (userStatsCache.has(userId)) {
          return userStatsCache.get(userId);
        }
        
        const stats = generateMockUserStats(userId);
        userStatsCache.set(userId, stats);
        return stats;
      });

      // Mock criteria evaluation
      mockBadgeEvaluation.evaluateBadgeCriteria.mockImplementation(async (stats, criteria) => {
        return stats.messageCount >= (criteria.threshold || 0);
      });

      const startTime = Date.now();
      const results = await badgeEvaluationService.checkCriteriaForUsers(userIds, criteria);
      const endTime = Date.now();

      expect(results.size).toBe(userCount);
      expect(endTime - startTime).toBeLessThan(3000); // Should complete within 3 seconds
      
      // Verify that some users meet the criteria
      const meetsCriteriaCount = Array.from(results.values()).filter(Boolean).length;
      expect(meetsCriteriaCount).toBeGreaterThan(0);
    });
  });

  describe("Bulk Operations Performance", () => {
    test("should handle bulk badge assignments efficiently", async () => {
      const assignmentCount = 2000;
      const assignments = Array.from({ length: assignmentCount }, (_, i) => ({
        userId: `user-${i}`,
        badgeTypeId: `badge-${i % 10}` // 10 different badge types
      }));

      // Mock bulk assignment with batching
      mockBadgeUtils.bulkAssignBadges.mockImplementation(async (db, assignments) => {
        // Simulate processing time (0.5ms per assignment)
        await new Promise(resolve => setTimeout(resolve, assignments.length * 0.5));
        
        return assignments.map((assignment, index) => ({
          id: `bulk-badge-${index}`,
          userId: assignment.userId,
          badgeTypeId: assignment.badgeTypeId,
          assignedBy: assignment.assignedBy,
          assignedAt: new Date(),
          isVisible: true
        }));
      });

      const startTime = Date.now();
      const results = await badgeService.bulkAssignBadges(assignments, mockAdminId);
      const endTime = Date.now();

      expect(results).toHaveLength(assignmentCount);
      expect(endTime - startTime).toBeLessThan(3000); // Should complete within 3 seconds
      expect(mockBadgeUtils.bulkAssignBadges).toHaveBeenCalledWith(
        mockDb,
        assignments.map(a => ({ ...a, assignedBy: mockAdminId }))
      );
    });

    test("should handle batch WebSocket notifications efficiently", async () => {
      const notificationCount = 1000;
      const assignments = Array.from({ length: notificationCount }, (_, i) => ({
        userId: `user-${i}`,
        badge: {
          id: `badge-${i}`,
          userId: `user-${i}`,
          badgeTypeId: "common-badge",
          assignedAt: new Date(),
          isVisible: true
        } as UserBadge,
        isAutomatic: true,
        assignedBy: undefined
      }));

      // Mock batch WebSocket broadcasting with batching
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockImplementation(async (assignments) => {
        const batchSize = 10;
        const batches = Math.ceil(assignments.length / batchSize);
        
        for (let i = 0; i < batches; i++) {
          // Simulate batch processing delay
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      });

      const startTime = Date.now();
      await mockBadgeWebSocket.broadcastBatchBadgeAssigned(assignments);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(2000); // Should complete within 2 seconds
      expect(mockBadgeWebSocket.broadcastBatchBadgeAssigned).toHaveBeenCalledWith(assignments);
    });
  });

  describe("Database Query Performance", () => {
    test("should efficiently query badge types with filters", async () => {
      const badgeCount = 500;
      const mockBadgeTypes = generateMockBadgeTypes(badgeCount);

      // Mock database query with indexing simulation
      mockBadgeUtils.getBadgeTypes.mockImplementation(async (db, filters, limit, offset) => {
        // Simulate index lookup time
        await new Promise(resolve => setTimeout(resolve, 10));
        
        let filteredBadges = mockBadgeTypes;
        
        if (filters?.category) {
          filteredBadges = filteredBadges.filter(badge => badge.category === filters.category);
        }
        
        if (filters?.unlockType) {
          filteredBadges = filteredBadges.filter(badge => badge.unlockType === filters.unlockType);
        }
        
        if (filters?.isActive !== undefined) {
          filteredBadges = filteredBadges.filter(badge => badge.isActive === filters.isActive);
        }
        
        if (offset) {
          filteredBadges = filteredBadges.slice(offset);
        }
        
        if (limit) {
          filteredBadges = filteredBadges.slice(0, limit);
        }
        
        return filteredBadges;
      });

      const startTime = Date.now();
      const results = await badgeService.getBadgeTypes(
        { category: "achievement", unlockType: "automatic", isActive: true },
        50,
        0
      );
      const endTime = Date.now();

      expect(results).toBeDefined();
      expect(results.length).toBeLessThanOrEqual(50);
      expect(endTime - startTime).toBeLessThan(100); // Should complete within 100ms
    });

    test("should efficiently retrieve user badges with joins", async () => {
      const userCount = 1000;
      const userIds = generateMockUsers(userCount);

      // Mock user badge retrieval with join simulation
      mockBadgeUtils.getUserBadges.mockImplementation(async (db, userId, visibleOnly) => {
        // Simulate join query time
        await new Promise(resolve => setTimeout(resolve, 2));
        
        const badgeCount = Math.floor(Math.random() * 10) + 1;
        return Array.from({ length: badgeCount }, (_, i) => ({
          id: `user-badge-${userId}-${i}`,
          userId,
          badgeTypeId: `badge-${i}`,
          assignedAt: new Date(),
          isVisible: visibleOnly ? true : Math.random() > 0.2,
          badgeType: {
            id: `badge-${i}`,
            badgeId: `badge-${i}`,
            name: `Badge ${i}`,
            description: `Description ${i}`,
            icon: "🏆",
            design: { shape: "circle", background: "#FFD700", colors: ["#FFD700"] },
            criteria: { requirement: "Test", tracked: "test", type: "message_count" as const, threshold: 10 },
            unlockType: "automatic" as const,
            category: "achievement" as const,
            displayOrder: i,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        }));
      });

      const startTime = Date.now();
      const promises = userIds.slice(0, 100).map(userId => 
        badgeService.getUserBadges(userId, true)
      );
      const results = await Promise.all(promises);
      const endTime = Date.now();

      expect(results).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });
  });

  describe("Memory Usage Optimization", () => {
    test("should handle large result sets without memory issues", async () => {
      const userCount = 5000;
      const userIds = generateMockUsers(userCount);

      // Mock memory-efficient batch processing
      mockBadgeEvaluation.batchEvaluateUsers.mockImplementation(async (db, users) => {
        // Process in smaller chunks to simulate memory management
        const chunkSize = 100;
        const results: EvaluationResult[] = [];
        
        for (let i = 0; i < users.length; i += chunkSize) {
          const chunk = users.slice(i, i + chunkSize);
          const chunkResults = chunk.map((userId: string) => ({
            userId,
            newBadges: [],
            evaluatedBadges: ["badge-1"],
            collectionProgress: [],
            errors: []
          }));
          
          results.push(...chunkResults);
          
          // Simulate garbage collection pause
          if (i % 1000 === 0) {
            await new Promise(resolve => setTimeout(resolve, 1));
          }
        }
        
        return results;
      });

      const startTime = Date.now();
      const results = await badgeEvaluationService.evaluateUsers(userIds);
      const endTime = Date.now();

      expect(results).toHaveLength(userCount);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
      
      // Verify memory usage is reasonable (this is a mock test)
      expect(results.every(result => result.userId)).toBe(true);
    });

    test("should efficiently stream large datasets", async () => {
      const badgeCount = 10000;
      
      // Mock streaming badge retrieval
      mockBadgeUtils.getBadgeTypes.mockImplementation(async (db, filters, limit = 100, offset = 0) => {
        // Simulate streaming with pagination
        await new Promise(resolve => setTimeout(resolve, 5));
        
        const startIndex = offset;
        const endIndex = Math.min(startIndex + limit, badgeCount);
        
        return Array.from({ length: endIndex - startIndex }, (_, i) => ({
          id: `badge-${startIndex + i}`,
          badgeId: `badge-${startIndex + i}`,
          name: `Badge ${startIndex + i}`,
          description: `Description ${startIndex + i}`,
          icon: "🏆",
          design: { shape: "circle", background: "#FFD700", colors: ["#FFD700"] },
          criteria: { requirement: "Test", tracked: "test", type: "message_count" as const, threshold: 10 },
          unlockType: "automatic" as const,
          category: "achievement" as const,
          displayOrder: startIndex + i,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }));
      });

      const pageSize = 100;
      const totalPages = Math.ceil(badgeCount / pageSize);
      let totalRetrieved = 0;

      const startTime = Date.now();
      
      for (let page = 0; page < totalPages; page++) {
        const results = await badgeService.getBadgeTypes(
          { isActive: true },
          pageSize,
          page * pageSize
        );
        totalRetrieved += results.length;
        
        // Simulate processing each page
        expect(results.length).toBeLessThanOrEqual(pageSize);
      }
      
      const endTime = Date.now();

      expect(totalRetrieved).toBe(badgeCount);
      expect(endTime - startTime).toBeLessThan(3000); // Should complete within 3 seconds
    });
  });

  describe("Concurrent Operations Performance", () => {
    test("should handle concurrent badge evaluations efficiently", async () => {
      const concurrentUsers = 100;
      const userIds = generateMockUsers(concurrentUsers);

      // Mock individual user evaluation
      mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockImplementation(async (db, userId) => {
        // Simulate evaluation time with some variance
        await new Promise(resolve => setTimeout(resolve, Math.random() * 50 + 10));
        
        return {
          userId,
          newBadges: Math.random() > 0.7 ? [{
            id: `new-badge-${userId}`,
            userId,
            badgeTypeId: "concurrent-badge",
            assignedAt: new Date(),
            isVisible: true
          }] : [],
          evaluatedBadges: ["badge-1", "badge-2"],
          collectionProgress: [],
          errors: []
        };
      });

      const startTime = Date.now();
      
      // Run evaluations concurrently
      const promises = userIds.map(userId => badgeEvaluationService.evaluateUser(userId));
      const results = await Promise.all(promises);
      
      const endTime = Date.now();

      expect(results).toHaveLength(concurrentUsers);
      expect(endTime - startTime).toBeLessThan(2000); // Should complete within 2 seconds due to concurrency
      
      // Verify all evaluations completed successfully
      expect(results.every(result => result.userId)).toBe(true);
    });

    test("should handle concurrent badge assignments without conflicts", async () => {
      const concurrentAssignments = 50;
      const assignments = Array.from({ length: concurrentAssignments }, (_, i) => ({
        userId: `user-${i}`,
        badgeTypeId: "concurrent-badge"
      }));

      const mockBadgeType: BadgeType = {
        id: "concurrent-badge",
        badgeId: "concurrent-badge",
        name: "Concurrent Badge",
        description: "Badge for concurrent testing",
        icon: "⚡",
        design: { shape: "circle", background: "#00FF00", colors: ["#00FF00"] },
        criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 1 },
        unlockType: "manual",
        category: "achievement",
        displayOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Mock concurrent badge assignments
      mockBadgeUtils.getBadgeTypeById.mockResolvedValue(mockBadgeType);
      mockBadgeUtils.getUserBadges.mockResolvedValue([]);
      mockBadgeUtils.assignBadgeToUser.mockImplementation(async (db, userId, badgeTypeId, assignedBy) => {
        // Simulate assignment time with some variance
        await new Promise(resolve => setTimeout(resolve, Math.random() * 20 + 5));
        
        return {
          id: `concurrent-assignment-${userId}`,
          userId,
          badgeTypeId,
          assignedBy,
          assignedAt: new Date(),
          isVisible: true,
          badgeType: mockBadgeType
        };
      });

      const startTime = Date.now();
      
      // Run assignments concurrently
      const promises = assignments.map(assignment => 
        badgeService.assignBadge(assignment.userId, assignment.badgeTypeId, mockAdminId)
      );
      const results = await Promise.all(promises);
      
      const endTime = Date.now();

      expect(results).toHaveLength(concurrentAssignments);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second due to concurrency
      
      // Verify all assignments completed successfully
      expect(results.every(result => result.userId)).toBe(true);
    });
  });
});