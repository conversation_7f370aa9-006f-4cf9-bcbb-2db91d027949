import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { db } from '../../db/index';
import { badgeCollections, badgeTypes, userBadges, users } from '../../db/schema';
import { BadgeCollectionAdminService } from '../../services/badge-collection-admin.service';
import type { 
  CreateBadgeCollectionRequest, 
  UpdateBadgeCollectionRequest,
  BulkBadgeAssignmentRequest 
} from '../../types/badge.types';

describe('Badge Collection Admin Integration Tests', () => {
  const adminService = new BadgeCollectionAdminService();
  let testCollectionId: string;
  let testBadgeIds: string[] = [];
  let testUserIds: string[] = [];

  beforeEach(async () => {
    // Clean up existing test data
    await db.delete(userBadges);
    await db.delete(badgeTypes);
    await db.delete(badgeCollections);
    await db.delete(users);

    // Create test users
    const testUsers = await db.insert(users).values([
      {
        id: crypto.randomUUID(),
        username: 'testuser1',
        email: '<EMAIL>',
        passwordHash: 'hash1'
      },
      {
        id: crypto.randomUUID(),
        username: 'testuser2',
        email: '<EMAIL>',
        passwordHash: 'hash2'
      }
    ]).returning();

    testUserIds = testUsers.map(u => u.id);

    // Create test collection
    const collectionData: CreateBadgeCollectionRequest = {
      collectionId: 'test-collection',
      name: 'Test Collection',
      description: 'A test badge collection',
      type: 'progressive',
      unlockedBy: 'activity_and_time',
      completionReward: {
        badge: 'completion-master',
        title: 'Collection Master',
        perks: ['special_role'],
        visual: 'golden_crown',
        animation: 'sparkle'
      }
    };

    const collection = await adminService.createCollection(collectionData);
    testCollectionId = collection.id;

    // Create test badges in the collection
    const badgeData = [
      {
        collectionId: testCollectionId,
        badgeId: 'first-badge',
        name: 'First Badge',
        description: 'The first badge in the collection',
        icon: '🥇',
        design: {
          shape: 'circle',
          background: 'gold',
          colors: ['#FFD700', '#FFA500']
        },
        criteria: {
          requirement: 'Send first message',
          tracked: 'message_count',
          type: 'message_count' as const,
          threshold: 1
        },
        unlockType: 'automatic' as const,
        displayOrder: 0,
        category: 'milestone' as const,
        isActive: true
      },
      {
        collectionId: testCollectionId,
        badgeId: 'second-badge',
        name: 'Second Badge',
        description: 'The second badge in the collection',
        icon: '🥈',
        design: {
          shape: 'circle',
          background: 'silver',
          colors: ['#C0C0C0', '#A0A0A0']
        },
        criteria: {
          requirement: 'Send 10 messages',
          tracked: 'message_count',
          type: 'message_count' as const,
          threshold: 10
        },
        unlockType: 'automatic' as const,
        displayOrder: 1,
        category: 'milestone' as const,
        isActive: true
      }
    ];

    const badges = await db.insert(badgeTypes).values(badgeData).returning();
    testBadgeIds = badges.map(b => b.id);
  });

  afterEach(async () => {
    // Clean up test data
    await db.delete(userBadges);
    await db.delete(badgeTypes);
    await db.delete(badgeCollections);
    await db.delete(users);
  });

  describe('Collection CRUD Operations', () => {
    it('should create a new badge collection', async () => {
      const collectionData: CreateBadgeCollectionRequest = {
        collectionId: 'new-collection',
        name: 'New Collection',
        description: 'A new test collection',
        type: 'progressive'
      };

      const collection = await adminService.createCollection(collectionData);

      expect(collection).toBeDefined();
      expect(collection.collectionId).toBe('new-collection');
      expect(collection.name).toBe('New Collection');
      expect(collection.type).toBe('progressive');
      expect(collection.isActive).toBe(true);
    });

    it('should update an existing collection', async () => {
      const updates: UpdateBadgeCollectionRequest = {
        name: 'Updated Collection Name',
        description: 'Updated description',
        isActive: false
      };

      const updatedCollection = await adminService.updateCollection(testCollectionId, updates);

      expect(updatedCollection.name).toBe('Updated Collection Name');
      expect(updatedCollection.description).toBe('Updated description');
      expect(updatedCollection.isActive).toBe(false);
    });

    it('should get collection by ID', async () => {
      const collection = await adminService.getCollectionById(testCollectionId);

      expect(collection).toBeDefined();
      expect(collection.id).toBe(testCollectionId);
      expect(collection.name).toBe('Test Collection');
    });

    it('should get all collections with filters', async () => {
      const collections = await adminService.getCollections({
        isActive: true,
        type: 'progressive'
      });

      expect(collections).toHaveLength(1);
      expect(collections[0].id).toBe(testCollectionId);
    });

    it('should delete a collection without badges', async () => {
      // Remove badges first
      await db.delete(badgeTypes).where(db.eq(badgeTypes.collectionId, testCollectionId));

      await expect(adminService.deleteCollection(testCollectionId)).resolves.not.toThrow();

      // Verify collection is deleted
      await expect(adminService.getCollectionById(testCollectionId))
        .rejects.toThrow('Collection not found');
    });

    it('should not delete a collection with existing badges', async () => {
      await expect(adminService.deleteCollection(testCollectionId))
        .rejects.toThrow('Cannot delete collection with existing badges');
    });
  });

  describe('Badge Reordering', () => {
    it('should reorder badges within a collection', async () => {
      const badges = await db.select()
        .from(badgeTypes)
        .where(db.eq(badgeTypes.collectionId, testCollectionId))
        .orderBy(db.asc(badgeTypes.displayOrder));

      const reorderData = [
        { badgeId: badges[1].badgeId, displayOrder: 0 },
        { badgeId: badges[0].badgeId, displayOrder: 1 }
      ];

      await adminService.reorderCollectionBadges(testCollectionId, reorderData);

      // Verify new order
      const reorderedBadges = await db.select()
        .from(badgeTypes)
        .where(db.eq(badgeTypes.collectionId, testCollectionId))
        .orderBy(db.asc(badgeTypes.displayOrder));

      expect(reorderedBadges[0].badgeId).toBe(badges[1].badgeId);
      expect(reorderedBadges[1].badgeId).toBe(badges[0].badgeId);
    });

    it('should reject reordering with invalid badge IDs', async () => {
      const reorderData = [
        { badgeId: 'invalid-badge', displayOrder: 0 }
      ];

      await expect(adminService.reorderCollectionBadges(testCollectionId, reorderData))
        .rejects.toThrow('Some badges do not belong to this collection');
    });
  });

  describe('Bulk Badge Assignment', () => {
    it('should bulk assign all badges in a collection to users', async () => {
      const assignmentData: BulkBadgeAssignmentRequest = {
        collectionId: testCollectionId,
        userIds: testUserIds,
        assignedBy: 'admin-user-id'
      };

      const results = await adminService.bulkAssignCollectionBadges(assignmentData);

      expect(results.successful).toBe(2);
      expect(results.failed).toHaveLength(0);

      // Verify badges were assigned
      const userBadgeCount = await db.select({ count: db.count() })
        .from(userBadges)
        .where(db.eq(userBadges.collectionId, testCollectionId));

      expect(userBadgeCount[0].count).toBe(4); // 2 users × 2 badges
    });

    it('should bulk assign specific badges to users', async () => {
      const badges = await db.select()
        .from(badgeTypes)
        .where(db.eq(badgeTypes.collectionId, testCollectionId));

      const assignmentData: BulkBadgeAssignmentRequest = {
        collectionId: testCollectionId,
        userIds: [testUserIds[0]],
        badgeIds: [badges[0].badgeId],
        assignedBy: 'admin-user-id'
      };

      const results = await adminService.bulkAssignCollectionBadges(assignmentData);

      expect(results.successful).toBe(1);
      expect(results.failed).toHaveLength(0);

      // Verify only one badge was assigned
      const userBadgeCount = await db.select({ count: db.count() })
        .from(userBadges)
        .where(db.eq(userBadges.collectionId, testCollectionId));

      expect(userBadgeCount[0].count).toBe(1);
    });
  });

  describe('Collection Analytics', () => {
    beforeEach(async () => {
      // Assign some badges for analytics testing
      await db.insert(userBadges).values([
        {
          userId: testUserIds[0],
          badgeTypeId: testBadgeIds[0],
          collectionId: testCollectionId,
          assignedAt: new Date(),
          isVisible: true
        },
        {
          userId: testUserIds[1],
          badgeTypeId: testBadgeIds[0],
          collectionId: testCollectionId,
          assignedAt: new Date(),
          isVisible: true
        }
      ]);
    });

    it('should get collection analytics', async () => {
      const analytics = await adminService.getCollectionAnalytics(testCollectionId);

      expect(analytics).toBeDefined();
      expect(analytics.collection.id).toBe(testCollectionId);
      expect(analytics.totalBadges).toBe(2);
      expect(analytics.badgeDistribution).toHaveLength(2);
      expect(analytics.badgeDistribution[0].assignmentCount).toBe(2);
      expect(analytics.badgeDistribution[1].assignmentCount).toBe(0);
    });

    it('should get collection progress report', async () => {
      const report = await adminService.getCollectionProgressReport(testCollectionId);

      expect(report).toBeDefined();
      expect(report.collection.id).toBe(testCollectionId);
      expect(report.userProgress).toBeDefined();
    });
  });

  describe('Collection Testing', () => {
    it('should test collection configuration and return valid result', async () => {
      const testResult = await adminService.testCollection(testCollectionId);

      expect(testResult).toBeDefined();
      expect(testResult.collectionId).toBe(testCollectionId);
      expect(testResult.isValid).toBe(true);
      expect(testResult.badgeCount).toBe(2);
      expect(testResult.issues).toHaveLength(0);
    });

    it('should detect issues in collection configuration', async () => {
      // Create a collection with no badges
      const emptyCollection = await adminService.createCollection({
        collectionId: 'empty-collection',
        name: 'Empty Collection',
        description: 'Collection with no badges',
        type: 'progressive'
      });

      const testResult = await adminService.testCollection(emptyCollection.id);

      expect(testResult.isValid).toBe(false);
      expect(testResult.issues).toContain('Collection has no badges defined');
    });
  });

  describe('Collection Preview', () => {
    it('should preview collection with badges and sample progress', async () => {
      const preview = await adminService.previewCollection(testCollectionId);

      expect(preview).toBeDefined();
      expect(preview.collection.id).toBe(testCollectionId);
      expect(preview.badges).toHaveLength(2);
      expect(preview.badges[0].displayOrder).toBe(0);
      expect(preview.badges[1].displayOrder).toBe(1);
      expect(preview.sampleUserProgress).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle duplicate collection ID creation', async () => {
      const duplicateData: CreateBadgeCollectionRequest = {
        collectionId: 'test-collection', // Same as existing
        name: 'Duplicate Collection',
        description: 'This should fail',
        type: 'progressive'
      };

      await expect(adminService.createCollection(duplicateData))
        .rejects.toThrow('Collection with ID \'test-collection\' already exists');
    });

    it('should handle non-existent collection operations', async () => {
      const nonExistentId = crypto.randomUUID();

      await expect(adminService.getCollectionById(nonExistentId))
        .rejects.toThrow('Collection not found');

      await expect(adminService.updateCollection(nonExistentId, { name: 'Updated' }))
        .rejects.toThrow('Collection not found');

      await expect(adminService.deleteCollection(nonExistentId))
        .rejects.toThrow('Collection not found');
    });
  });
});