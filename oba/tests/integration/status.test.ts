import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  afterAll,
} from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  cleanupTestData,
  createMockRequest,
  parseResponse<PERSON>son,
  createUserWithStatus,
  createUsersWithStatuses,
  createAcceptedFriendship,
} from "../helpers";
import { UserSchema } from "../../db/schema";
import { eq } from "drizzle-orm";

import {
  updateUserStatusHandler,
  getUserStatusHandler,
  getOnlineFriendsHandler,
  updateLastActiveHandler,
} from "../../handlers/status";

// Global cleanup after all tests
afterAll(async () => {
  await cleanupTestData();
});

describe("User Status API Endpoints", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  // Update User Status Tests
  test("updateUserStatusHandler should update a user's status", async () => {
    // Create a test user
    const user = await createTestUser("statususer", "<EMAIL>");

    // Create a mock request
    const req = createMockRequest({
      userId: user.id,
      status: "AWAY",
      statusMessage: "I am away",
    });

    // Call the handler
    const response = await updateUserStatusHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.user).toBeDefined();
    expect(data.user.id).toBe(user.id);
    expect(data.user.status).toBe("AWAY");
    expect(data.user.statusMessage).toBe("I am away");

    // Verify in the database
    const dbUser = await db
      .select()
      .from(UserSchema)
      .where(eq(UserSchema.id, user.id))
      .limit(1);

    expect(dbUser.length).toBe(1);
    expect(dbUser[0].status).toBe("AWAY");
    expect(dbUser[0].statusMessage).toBe("I am away");
  });

  test("updateUserStatusHandler should handle missing status message", async () => {
    // Create a test user
    const user = await createTestUser("statususer", "<EMAIL>");

    // Create a mock request without status message
    const req = createMockRequest({
      userId: user.id,
      status: "BUSY",
    });

    // Call the handler
    const response = await updateUserStatusHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.user.status).toBe("BUSY");
    expect(data.user.statusMessage).toBeNull();
  });

  test("updateUserStatusHandler should reject invalid user ID", async () => {
    // Create a mock request with invalid user ID
    const req = createMockRequest({
      userId: "invalid-id",
      status: "ONLINE",
    });

    // Call the handler
    const response = await updateUserStatusHandler(req);

    // Check the response
    expect(response.status).toBe(400);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.errors).toBeDefined();
  });

  test("updateUserStatusHandler should reject invalid status", async () => {
    // Create a test user
    const user = await createTestUser("statususer", "<EMAIL>");

    // Create a mock request with invalid status
    const req = createMockRequest({
      userId: user.id,
      status: "INVALID_STATUS",
    });

    // Call the handler
    const response = await updateUserStatusHandler(req);

    // Check the response
    expect(response.status).toBe(400);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.errors).toBeDefined();
  });

  // Get User Status Tests
  test("getUserStatusHandler should return a user's status", async () => {
    // Create a test user with a specific status
    const user = await createUserWithStatus(
      "statususer",
      "<EMAIL>",
      "BUSY",
      "I am busy",
    );

    // Create a mock request
    const req = new Request(
      `http://localhost:3000/api/status?userId=${user.id}`,
    );

    // Call the handler
    const response = await getUserStatusHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.user).toBeDefined();
    expect(data.user.id).toBe(user.id);
    expect(data.user.username).toBe("statususer");
    expect(data.user.status).toBe("BUSY");
    expect(data.user.statusMessage).toBe("I am busy");
  });

  test("getUserStatusHandler should reject missing user ID", async () => {
    // Create a mock request without user ID
    const req = new Request("http://localhost:3000/api/status");

    // Call the handler
    const response = await getUserStatusHandler(req);

    // Check the response
    expect(response.status).toBe(400);

    // Parse the response text
    const text = await response.text();

    // Check the response text
    expect(text).toBe("User ID is required");
  });

  test("getUserStatusHandler should reject non-existent user", async () => {
    // Generate a random UUID that doesn't exist
    const nonExistentId = crypto.randomUUID();

    // Create a mock request with non-existent user ID
    const req = new Request(
      `http://localhost:3000/api/status?userId=${nonExistentId}`,
    );

    // Call the handler
    const response = await getUserStatusHandler(req);

    // Check the response
    expect(response.status).toBe(404);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.error).toBe("User not found");
  });

  // Get Online Friends Tests
  test("getOnlineFriendsHandler should return online friends", async () => {
    // Create users with different statuses
    const { onlineUser, awayUser, busyUser, invisibleUser, offlineUser } =
      await createUsersWithStatuses();

    // Create a main user
    const mainUser = await createTestUser("mainuser", "<EMAIL>");

    // Create friendships with all users
    await createAcceptedFriendship(mainUser.id, onlineUser.id);
    await createAcceptedFriendship(mainUser.id, awayUser.id);
    await createAcceptedFriendship(mainUser.id, busyUser.id);
    await createAcceptedFriendship(mainUser.id, invisibleUser.id);
    await createAcceptedFriendship(mainUser.id, offlineUser.id);

    // Create a mock request
    const req = new Request(
      `http://localhost:3000/api/status/friends/online?userId=${mainUser.id}`,
    );

    // Call the handler
    const response = await getOnlineFriendsHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.friends).toBeDefined();
    expect(data.friends.length).toBe(3); // online, away, busy

    // Check that the correct users are returned
    const friendIds = data.friends.map((f: any) => f.friend.id);
    expect(friendIds).toContain(onlineUser.id);
    expect(friendIds).toContain(awayUser.id);
    expect(friendIds).toContain(busyUser.id);
    expect(friendIds).not.toContain(invisibleUser.id);
    expect(friendIds).not.toContain(offlineUser.id);
  });

  test("getOnlineFriendsHandler should reject missing user ID", async () => {
    // Create a mock request without user ID
    const req = new Request("http://localhost:3000/api/status/friends/online");

    // Call the handler
    const response = await getOnlineFriendsHandler(req);

    // Check the response
    expect(response.status).toBe(400);

    // Parse the response text
    const text = await response.text();

    // Check the response text
    expect(text).toBe("User ID is required");
  });

  // Update Last Active Tests
  test("updateLastActiveHandler should update a user's last active timestamp", async () => {
    // Create a test user
    const user = await createTestUser("activeuser", "<EMAIL>");

    // Create a mock request
    const req = createMockRequest({
      userId: user.id,
    });

    // Call the handler
    const response = await updateLastActiveHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.user).toBeDefined();
    expect(data.user.id).toBe(user.id);
    expect(data.user.lastActive).toBeDefined();

    // Verify in the database
    const dbUser = await db
      .select()
      .from(UserSchema)
      .where(eq(UserSchema.id, user.id))
      .limit(1);

    expect(dbUser.length).toBe(1);
    expect(dbUser[0].lastActive).toBeDefined();
  });

  test("updateLastActiveHandler should reject missing user ID", async () => {
    // Create a mock request without user ID
    const req = createMockRequest({});

    // Call the handler
    const response = await updateLastActiveHandler(req);

    // Check the response
    expect(response.status).toBe(400);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.errors).toBeDefined();
  });
});
