import { describe, test, expect, beforeEach, afterEach } from "bun:test";
import { db } from "../../db";
import { 
  UserSchema, 
  BadgeTypeSchema, 
  UserBadgeSchema,
  ServerSchema,
  ServerRoleSchema,
  UserRoles
} from "../../db/schema";
import { eq } from "drizzle-orm";

// Test server setup
const TEST_PORT = 3001;
let testServer: any;

// Test data
const testUser = {
  id: "test-user-123",
  username: "testuser",
  email: "<EMAIL>",
  password: "hashedpassword"
};

const testServer = {
  id: "test-server-123",
  name: "Test Server",
  ownerId: testUser.id
};

const testRole = {
  id: "test-role-123",
  serverId: testServer.id,
  name: "Moderator",
  permissions: BigInt(16) // MANAGE_ROLES permission
};

const testBadgeType = {
  id: "test-badge-123",
  badgeId: "moderator_badge",
  name: "Moderator Badge",
  description: "Badge for moderators",
  icon: "🛡️",
  design: JSON.stringify({ shape: "shield", background: "blue", colors: ["#0066CC"] }),
  criteria: JSON.stringify({ requirement: "manual", tracked: "manual" }),
  perks: JSON.stringify(["role:Moderator", "cosmetic:mod_glow", "access:mod_lounge"]),
  unlockType: "manual",
  displayOrder: 1,
  category: "role",
  isActive: true
};

describe("Perk API Integration Tests", () => {
  beforeEach(async () => {
    // Start test server
    const { default: app } = await import("../../index");
    testServer = Bun.serve({
      port: TEST_PORT,
      fetch: app.fetch
    });

    // Clean up test data
    await db.delete(UserBadgeSchema).where(eq(UserBadgeSchema.userId, testUser.id));
    await db.delete(BadgeTypeSchema).where(eq(BadgeTypeSchema.id, testBadgeType.id));
    await db.delete(UserRoles).where(eq(UserRoles.userId, testUser.id));
    await db.delete(ServerRoleSchema).where(eq(ServerRoleSchema.id, testRole.id));
    await db.delete(ServerSchema).where(eq(ServerSchema.id, testServer.id));
    await db.delete(UserSchema).where(eq(UserSchema.id, testUser.id));

    // Insert test data
    await db.insert(UserSchema).values(testUser);
    await db.insert(ServerSchema).values(testServer);
    await db.insert(ServerRoleSchema).values(testRole);
    await db.insert(BadgeTypeSchema).values(testBadgeType);
  });

  afterEach(async () => {
    // Clean up test data
    await db.delete(UserBadgeSchema).where(eq(UserBadgeSchema.userId, testUser.id));
    await db.delete(BadgeTypeSchema).where(eq(BadgeTypeSchema.id, testBadgeType.id));
    await db.delete(UserRoles).where(eq(UserRoles.userId, testUser.id));
    await db.delete(ServerRoleSchema).where(eq(ServerRoleSchema.id, testRole.id));
    await db.delete(ServerSchema).where(eq(ServerSchema.id, testServer.id));
    await db.delete(UserSchema).where(eq(UserSchema.id, testUser.id));

    // Stop test server
    if (testServer) {
      testServer.stop();
    }
  });

  describe("GET /api/users/:userId/perks", () => {
    test("should get user perks successfully", async () => {
      // Assign badge to user first
      await db.insert(UserBadgeSchema).values({
        userId: testUser.id,
        badgeTypeId: testBadgeType.id,
        assignedBy: testUser.id
      });

      const response = await fetch(`http://localhost:${TEST_PORT}/api/users/${testUser.id}/perks?serverId=${testServer.id}`);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.perks).toBeArray();
      expect(data.perks.length).toBeGreaterThan(0);
      
      // Check that perks include the expected types
      const perkTypes = data.perks.map((p: any) => p.type);
      expect(perkTypes).toContain("role_assignment");
      expect(perkTypes).toContain("cosmetic_feature");
      expect(perkTypes).toContain("access_privilege");
    });

    test("should return empty array for user with no badges", async () => {
      const response = await fetch(`http://localhost:${TEST_PORT}/api/users/${testUser.id}/perks`);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.perks).toBeArray();
      expect(data.perks).toHaveLength(0);
    });

    test("should return 400 for invalid user ID", async () => {
      const response = await fetch(`http://localhost:${TEST_PORT}/api/users/invalid-id/perks`);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
    });
  });

  describe("GET /api/perks/available", () => {
    test("should get available perks successfully", async () => {
      const response = await fetch(`http://localhost:${TEST_PORT}/api/perks/available?serverId=${testServer.id}`);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.perks).toBeArray();
      expect(data.perks.length).toBeGreaterThan(0);
      
      // Check that perks include the expected types from our test badge
      const perkTypes = data.perks.map((p: any) => p.type);
      expect(perkTypes).toContain("role_assignment");
      expect(perkTypes).toContain("cosmetic_feature");
      expect(perkTypes).toContain("access_privilege");
    });

    test("should get available perks without server filter", async () => {
      const response = await fetch(`http://localhost:${TEST_PORT}/api/perks/available`);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.perks).toBeArray();
    });
  });

  describe("GET /api/badges/:badgeTypeId/perks/validate", () => {
    test("should validate badge perks successfully", async () => {
      const response = await fetch(`http://localhost:${TEST_PORT}/api/badges/${testBadgeType.id}/perks/validate?serverId=${testServer.id}`);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.validation).toBeObject();
      expect(data.validation.isValid).toBeBoolean();
      expect(data.validation.errors).toBeArray();
      expect(data.validation.warnings).toBeArray();
    });

    test("should return 404 for non-existent badge", async () => {
      const response = await fetch(`http://localhost:${TEST_PORT}/api/badges/non-existent-id/perks/validate`);
      const data = await response.json();

      expect(response.status).toBe(400); // Will be 400 due to UUID validation
      expect(data.success).toBe(false);
    });
  });

  describe("GET /api/perks/display", () => {
    test("should get perk display information", async () => {
      const response = await fetch(`http://localhost:${TEST_PORT}/api/perks/display?serverId=${testServer.id}&userId=${testUser.id}`);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.display).toBeObject();
      expect(data.display.availablePerks).toBeArray();
      expect(data.display.userPerks).toBeArray();
      expect(data.display.perksByType).toBeObject();
      expect(data.display.summary).toBeObject();
      expect(data.display.summary.totalAvailable).toBeNumber();
      expect(data.display.summary.userPerksCount).toBeNumber();
      expect(data.display.summary.typeBreakdown).toBeArray();
    });

    test("should get display info without user filter", async () => {
      const response = await fetch(`http://localhost:${TEST_PORT}/api/perks/display?serverId=${testServer.id}`);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.display.userPerks).toHaveLength(0);
    });
  });

  describe("POST /api/perks/assign", () => {
    test("should manually assign perks successfully", async () => {
      const requestBody = {
        userId: testUser.id,
        badgeTypeId: testBadgeType.id,
        requestedBy: testUser.id,
        serverId: testServer.id
      };

      const response = await fetch(`http://localhost:${TEST_PORT}/api/perks/assign`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.results).toBeObject();
      expect(data.results.total).toBeNumber();
      expect(data.results.successful).toBeNumber();
      expect(data.results.failed).toBeNumber();
      expect(data.results.assignments).toBeArray();
    });

    test("should return 400 for invalid request body", async () => {
      const response = await fetch(`http://localhost:${TEST_PORT}/api/perks/assign`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          userId: "invalid-id"
        })
      });

      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
    });

    test("should handle form data requests", async () => {
      const formData = new FormData();
      formData.append("userId", testUser.id);
      formData.append("badgeTypeId", testBadgeType.id);
      formData.append("requestedBy", testUser.id);
      formData.append("serverId", testServer.id);

      const response = await fetch(`http://localhost:${TEST_PORT}/api/perks/assign`, {
        method: "POST",
        body: formData
      });

      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });
  });

  describe("POST /api/perks/revoke", () => {
    test("should manually revoke perks successfully", async () => {
      // First assign a badge to have perks to revoke
      await db.insert(UserBadgeSchema).values({
        userId: testUser.id,
        badgeTypeId: testBadgeType.id,
        assignedBy: testUser.id
      });

      const requestBody = {
        userId: testUser.id,
        badgeTypeId: testBadgeType.id,
        requestedBy: testUser.id,
        serverId: testServer.id
      };

      const response = await fetch(`http://localhost:${TEST_PORT}/api/perks/revoke`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.results).toBeObject();
      expect(data.results.total).toBeNumber();
      expect(data.results.successful).toBeNumber();
      expect(data.results.failed).toBeNumber();
      expect(data.results.revocations).toBeArray();
    });

    test("should return 400 for invalid request body", async () => {
      const response = await fetch(`http://localhost:${TEST_PORT}/api/perks/revoke`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          userId: "invalid-id"
        })
      });

      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
    });
  });

  describe("Error Handling", () => {
    test("should handle unsupported content type", async () => {
      const response = await fetch(`http://localhost:${TEST_PORT}/api/perks/assign`, {
        method: "POST",
        headers: {
          "Content-Type": "text/plain"
        },
        body: "invalid body"
      });

      const data = await response.json();

      expect(response.status).toBe(415);
      expect(data.success).toBe(false);
    });

    test("should handle malformed JSON", async () => {
      const response = await fetch(`http://localhost:${TEST_PORT}/api/perks/assign`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: "invalid json"
      });

      expect(response.status).toBe(400);
    });
  });

  describe("Permission Validation", () => {
    test("should validate permissions for perk assignment", async () => {
      // Create a user without permissions
      const unprivilegedUser = {
        id: "unprivileged-user-123",
        username: "unprivileged",
        email: "<EMAIL>",
        password: "hashedpassword"
      };

      await db.insert(UserSchema).values(unprivilegedUser);

      const requestBody = {
        userId: testUser.id,
        badgeTypeId: testBadgeType.id,
        requestedBy: unprivilegedUser.id,
        serverId: testServer.id
      };

      const response = await fetch(`http://localhost:${TEST_PORT}/api/perks/assign`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody)
      });

      // Clean up
      await db.delete(UserSchema).where(eq(UserSchema.id, unprivilegedUser.id));

      // Note: This test might pass if permission validation is not fully implemented
      // In a real scenario, this should return 403 Forbidden
      expect([200, 403]).toContain(response.status);
    });
  });
});