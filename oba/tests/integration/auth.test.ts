import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  afterAll,
} from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  cleanupTestData,
  createMockRequest,
  parseResponse<PERSON>son,
  createUserWithVerificationToken,
  createUserWithRefreshToken,
  extractCookies,
} from "../helpers";
import {
  login<PERSON><PERSON><PERSON>,
  registerHandler,
  updateUserPro<PERSON>le<PERSON><PERSON>ler,
  verify<PERSON>mail<PERSON><PERSON><PERSON>,
  refreshToken<PERSON><PERSON><PERSON>,
  logoutHandler,
} from "../../handlers/auth";

// Global cleanup after all tests
afterAll(async () => {
  await cleanupTestData();
});

describe("Authentication Handlers", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  test("registerHandler should register a new user", async () => {
    // Create a mock request
    const req = createMockRequest({
      username: "newuser",
      email: "<EMAIL>",
      password: "Password123!",
    });

    // Call the register handler
    const response = await registerHandler(req);

    // Check the response
    expect(response.status).toBe(201);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.user).toBeDefined();
    expect(data.user.username).toBe("newuser");
    expect(data.user.email).toBe("<EMAIL>");
    expect(data.token).toBeDefined();
  });

  test("registerHandler should reject duplicate username", async () => {
    // Create a test user
    await createTestUser(
      "existinguser",
      "<EMAIL>",
      "Password123!",
    );

    // Create a mock request with the same username
    const req = createMockRequest({
      username: "existinguser",
      email: "<EMAIL>",
      password: "Password123!",
    });

    // Call the register handler
    const response = await registerHandler(req);

    // Check the response
    expect(response.status).toBe(400);

    // Parse the response text
    const text = await response.text();

    // Check the response text
    expect(text).toContain("Username already exists");
  });

  test("loginHandler should authenticate a valid user", async () => {
    // Create a test user
    await createTestUser("testuser", "<EMAIL>", "Password123!");

    // Create a mock request
    const req = createMockRequest({
      username: "testuser",
      password: "Password123!",
    });

    // Call the login handler
    const response = await loginHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.user).toBeDefined();
    expect(data.user.username).toBe("testuser");
    expect(data.user.email).toBe("<EMAIL>");
    expect(data.token).toBeDefined();
  });

  test("loginHandler should reject invalid credentials", async () => {
    // Create a test user
    await createTestUser("testuser", "<EMAIL>", "Password123!");

    // Create a mock request with wrong password
    const req = createMockRequest({
      username: "testuser",
      password: "WrongPassword123!",
    });

    // Call the login handler
    const response = await loginHandler(req);

    // Check the response
    expect(response.status).toBe(400);

    // Parse the response text
    const text = await response.text();

    // Check the response text
    expect(text).toContain("Invalid username or password");
  });

  test("updateUserProfileHandler should update user profile", async () => {
    // Create a test user
    const user = await createTestUser(
      "testuser",
      "<EMAIL>",
      "Password123!",
    );

    // Create a mock request
    const req = createMockRequest({
      userId: user.id,
      username: "updateduser",
      email: "<EMAIL>",
      avatar: "https://example.com/new-avatar.png",
    });

    // Call the update profile handler
    const response = await updateUserProfileHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.user).toBeDefined();
    expect(data.user.username).toBe("updateduser");
    expect(data.user.email).toBe("<EMAIL>");
    expect(data.user.avatar).toBe("https://example.com/new-avatar.png");
  });

  test("updateUserProfileHandler should update password with verification", async () => {
    // Create a test user
    const user = await createTestUser(
      "testuser",
      "<EMAIL>",
      "Password123!",
    );

    // Create a mock request
    const req = createMockRequest({
      userId: user.id,
      currentPassword: "Password123!",
      newPassword: "NewPassword123!",
    });

    // Call the update profile handler
    const response = await updateUserProfileHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);

    // Create a login request with the new password
    const loginReq = createMockRequest({
      username: "testuser",
      password: "NewPassword123!",
    });

    // Call the login handler
    const loginResponse = await loginHandler(loginReq);

    // Check the login response
    expect(loginResponse.status).toBe(200);
  });

  test("updateUserProfileHandler should reject password update with wrong current password", async () => {
    // Create a test user
    const user = await createTestUser(
      "testuser",
      "<EMAIL>",
      "Password123!",
    );

    // Create a mock request with wrong current password
    const req = createMockRequest({
      userId: user.id,
      currentPassword: "WrongPassword123!",
      newPassword: "NewPassword123!",
    });

    // Call the update profile handler
    const response = await updateUserProfileHandler(req);

    // Check the response
    expect(response.status).toBe(401);

    // Parse the response text
    const text = await response.text();

    // Check the response text
    expect(text).toContain("Current password is incorrect");
  });
});

describe("Email Verification Handlers", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  test("verifyEmailHandler should verify a user email", async () => {
    // Create a user with verification token
    const { user, token } = await createUserWithVerificationToken();

    // Create a mock request
    const url = `http://localhost:3000/api/verify-email?userId=${user.id}&token=${token}`;
    const req = new Request(url);

    // Call the verify email handler
    const response = await verifyEmailHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.message).toBe("Email verified successfully");
    expect(data.user).toBeDefined();
    expect(data.user.id).toBe(user.id);
    expect(data.user.isEmailVerified).toBe(true);
  });

  test("verifyEmailHandler should reject invalid token", async () => {
    // Create a user with verification token
    const { user } = await createUserWithVerificationToken();

    // Create a mock request with invalid token
    const url = `http://localhost:3000/api/verify-email?userId=${user.id}&token=invalid-token`;
    const req = new Request(url);

    // Call the verify email handler
    const response = await verifyEmailHandler(req);

    // Check the response
    expect(response.status).toBe(400);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.error).toBe("Invalid or expired verification token");
  });
});

describe("Refresh Token Handlers", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  test("refreshTokenHandler should issue a new access token", async () => {
    // Create a user with refresh token
    const { user, refreshToken } = await createUserWithRefreshToken();

    // Create a mock request
    const req = createMockRequest({
      userId: user.id,
      refreshToken,
    });

    // Call the refresh token handler
    const response = await refreshTokenHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.token).toBeDefined();
    expect(data.expiresIn).toBe(900); // 15 minutes in seconds

    // Check the cookies
    const cookies = extractCookies(response);
    expect(cookies.accessToken).toBeDefined();
  });

  test("refreshTokenHandler should reject invalid token", async () => {
    // Create a user with refresh token
    const { user } = await createUserWithRefreshToken();

    // Create a mock request with invalid token
    const req = createMockRequest({
      userId: user.id,
      refreshToken: "invalid-token",
    });

    // Call the refresh token handler
    const response = await refreshTokenHandler(req);

    // Check the response
    expect(response.status).toBe(401);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.error).toBe("Invalid or expired refresh token");

    // Check the cookies (should clear the refresh token)
    const cookies = extractCookies(response);
    expect(cookies.refreshToken).toBeDefined();
    // In a real test, we would check that the cookie is set to expire immediately
  });

  test("logoutHandler should invalidate refresh token", async () => {
    // Create a user with refresh token
    const { user } = await createUserWithRefreshToken();

    // Create a mock request
    const req = createMockRequest({
      userId: user.id,
    });

    // Call the logout handler
    const response = await logoutHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.message).toBe("Logged out successfully");

    // Check the cookies (should clear both tokens)
    const cookies = extractCookies(response);
    expect(cookies.accessToken).toBeDefined();
    expect(cookies.refreshToken).toBeDefined();
    // In a real test, we would check that the cookies are set to expire immediately
  });
});
