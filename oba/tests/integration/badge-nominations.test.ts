import { describe, it, expect, beforeEach, afterEach } from "bun:test";
import { db } from "../../db/index";
import { 
  BadgeNominationSchema, 
  BadgeTypeSchema, 
  UserSchema,
  UserBadgeSchema,
  BadgeCollectionSchema 
} from "../../db/schema";
import { eq, and } from "drizzle-orm";

const BASE_URL = "http://localhost:3000";

// Test data
const testUser1 = {
  id: "test-user-1",
  username: "nominator",
  email: "<EMAIL>",
  password: "hashedpassword",
};

const testUser2 = {
  id: "test-user-2", 
  username: "nominee",
  email: "<EMAIL>",
  password: "hashedpassword",
};

const testAdmin = {
  id: "test-admin",
  username: "admin",
  email: "<EMAIL>", 
  password: "hashedpassword",
  role: "admin",
};

const testBadgeType = {
  id: "test-badge-type",
  badgeId: "community-helper",
  name: "Community Helper",
  description: "Helps other community members",
  icon: "🤝",
  design: {
    shape: "circle",
    background: "blue",
    colors: ["#0066cc", "#ffffff"],
  },
  criteria: {
    requirement: "Peer nominations",
    tracked: "nominations",
    threshold: 3,
  },
  unlockType: "peer_voted",
  category: "community",
  displayOrder: 1,
  isActive: true,
};

describe("Badge Nominations API Integration Tests", () => {
  let nominatorToken: string;
  let nomineeToken: string;
  let adminToken: string;

  beforeEach(async () => {
    // Clean up test data
    await db.delete(BadgeNominationSchema);
    await db.delete(UserBadgeSchema);
    await db.delete(BadgeTypeSchema);
    await db.delete(UserSchema);

    // Insert test users
    await db.insert(UserSchema).values([testUser1, testUser2, testAdmin]);

    // Insert test badge type
    await db.insert(BadgeTypeSchema).values(testBadgeType);

    // Get auth tokens (mock implementation)
    nominatorToken = "mock-nominator-token";
    nomineeToken = "mock-nominee-token";
    adminToken = "mock-admin-token";
  });

  afterEach(async () => {
    // Clean up test data
    await db.delete(BadgeNominationSchema);
    await db.delete(UserBadgeSchema);
    await db.delete(BadgeTypeSchema);
    await db.delete(UserSchema);
  });

  describe("POST /api/badges/nominations", () => {
    it("should successfully submit a nomination", async () => {
      const nominationData = {
        badgeTypeId: testBadgeType.id,
        nomineeUserId: testUser2.id,
        nominationReason: "Great community member who helps everyone",
      };

      const response = await fetch(`${BASE_URL}/api/badges/nominations`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${nominatorToken}`,
        },
        body: JSON.stringify(nominationData),
      });

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.success).toBe(true);
      expect(result.data.nomination).toBeDefined();
      expect(result.data.nomination.badgeTypeId).toBe(testBadgeType.id);
      expect(result.data.nomination.nomineeUserId).toBe(testUser2.id);
      expect(result.data.nomination.status).toBe("pending");

      // Verify nomination was created in database
      const [nomination] = await db
        .select()
        .from(BadgeNominationSchema)
        .where(eq(BadgeNominationSchema.badgeTypeId, testBadgeType.id));

      expect(nomination).toBeDefined();
      expect(nomination.nomineeUserId).toBe(testUser2.id);
    });

    it("should reject self-nomination", async () => {
      const nominationData = {
        badgeTypeId: testBadgeType.id,
        nomineeUserId: testUser1.id, // Same as nominator
        nominationReason: "Self nomination",
      };

      const response = await fetch(`${BASE_URL}/api/badges/nominations`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${nominatorToken}`,
        },
        body: JSON.stringify(nominationData),
      });

      expect(response.status).toBe(400);
      
      const result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain("Cannot nominate yourself");
    });

    it("should reject duplicate nomination", async () => {
      // First nomination
      await db.insert(BadgeNominationSchema).values({
        badgeTypeId: testBadgeType.id,
        nomineeUserId: testUser2.id,
        nominatorUserId: testUser1.id,
        status: "pending",
      });

      const nominationData = {
        badgeTypeId: testBadgeType.id,
        nomineeUserId: testUser2.id,
        nominationReason: "Duplicate nomination",
      };

      const response = await fetch(`${BASE_URL}/api/badges/nominations`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${nominatorToken}`,
        },
        body: JSON.stringify(nominationData),
      });

      expect(response.status).toBe(400);
      
      const result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain("Nomination already exists");
    });
  });

  describe("GET /api/badges/nominations/received", () => {
    beforeEach(async () => {
      // Insert test nominations
      await db.insert(BadgeNominationSchema).values([
        {
          badgeTypeId: testBadgeType.id,
          nomineeUserId: testUser2.id,
          nominatorUserId: testUser1.id,
          status: "pending",
          nominationReason: "Great helper",
        },
        {
          badgeTypeId: testBadgeType.id,
          nomineeUserId: testUser2.id,
          nominatorUserId: testAdmin.id,
          status: "approved",
          nominationReason: "Excellent community member",
        },
      ]);
    });

    it("should return nominations received by user", async () => {
      const response = await fetch(`${BASE_URL}/api/badges/nominations/received`, {
        headers: {
          "Authorization": `Bearer ${nomineeToken}`,
        },
      });

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.success).toBe(true);
      expect(result.data.nominations).toHaveLength(2);
      expect(result.data.count).toBe(2);
    });

    it("should filter nominations by status", async () => {
      const response = await fetch(`${BASE_URL}/api/badges/nominations/received?status=pending`, {
        headers: {
          "Authorization": `Bearer ${nomineeToken}`,
        },
      });

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.success).toBe(true);
      expect(result.data.nominations).toHaveLength(1);
      expect(result.data.nominations[0].status).toBe("pending");
    });
  });

  describe("GET /api/badges/nominations/submitted", () => {
    beforeEach(async () => {
      // Insert test nominations
      await db.insert(BadgeNominationSchema).values([
        {
          badgeTypeId: testBadgeType.id,
          nomineeUserId: testUser2.id,
          nominatorUserId: testUser1.id,
          status: "pending",
          nominationReason: "Great helper",
        },
      ]);
    });

    it("should return nominations submitted by user", async () => {
      const response = await fetch(`${BASE_URL}/api/badges/nominations/submitted`, {
        headers: {
          "Authorization": `Bearer ${nominatorToken}`,
        },
      });

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.success).toBe(true);
      expect(result.data.nominations).toHaveLength(1);
      expect(result.data.nominations[0].nominatorUserId).toBe(testUser1.id);
    });
  });

  describe("GET /api/badges/nominations/stats/:badgeTypeId", () => {
    beforeEach(async () => {
      // Insert test nominations with different statuses
      await db.insert(BadgeNominationSchema).values([
        {
          badgeTypeId: testBadgeType.id,
          nomineeUserId: testUser2.id,
          nominatorUserId: testUser1.id,
          status: "pending",
        },
        {
          badgeTypeId: testBadgeType.id,
          nomineeUserId: testUser1.id,
          nominatorUserId: testUser2.id,
          status: "approved",
        },
        {
          badgeTypeId: testBadgeType.id,
          nomineeUserId: testUser1.id,
          nominatorUserId: testAdmin.id,
          status: "rejected",
        },
      ]);
    });

    it("should return nomination statistics", async () => {
      const response = await fetch(`${BASE_URL}/api/badges/nominations/stats/${testBadgeType.id}`, {
        headers: {
          "Authorization": `Bearer ${nominatorToken}`,
        },
      });

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.success).toBe(true);
      expect(result.data.stats.totalNominations).toBe(3);
      expect(result.data.stats.pendingNominations).toBe(1);
      expect(result.data.stats.approvedNominations).toBe(1);
      expect(result.data.stats.rejectedNominations).toBe(1);
    });
  });

  describe("POST /api/badges/nominations/:nominationId/approve", () => {
    let nominationId: string;

    beforeEach(async () => {
      // Insert test nomination
      const [nomination] = await db.insert(BadgeNominationSchema).values({
        badgeTypeId: testBadgeType.id,
        nomineeUserId: testUser2.id,
        nominatorUserId: testUser1.id,
        status: "pending",
        nominationReason: "Great community member",
      }).returning();

      nominationId = nomination.id;
    });

    it("should approve nomination and assign badge (admin only)", async () => {
      const response = await fetch(`${BASE_URL}/api/badges/nominations/${nominationId}/approve`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${adminToken}`,
        },
      });

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.success).toBe(true);
      expect(result.data.userBadge).toBeDefined();
      expect(result.data.message).toContain("approved");

      // Verify nomination status updated
      const [updatedNomination] = await db
        .select()
        .from(BadgeNominationSchema)
        .where(eq(BadgeNominationSchema.id, nominationId));

      expect(updatedNomination.status).toBe("approved");
      expect(updatedNomination.processedAt).toBeDefined();

      // Verify badge was assigned
      const [userBadge] = await db
        .select()
        .from(UserBadgeSchema)
        .where(
          and(
            eq(UserBadgeSchema.userId, testUser2.id),
            eq(UserBadgeSchema.badgeTypeId, testBadgeType.id)
          )
        );

      expect(userBadge).toBeDefined();
    });

    it("should reject non-admin approval", async () => {
      const response = await fetch(`${BASE_URL}/api/badges/nominations/${nominationId}/approve`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${nominatorToken}`,
        },
      });

      expect(response.status).toBe(403);
      
      const result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain("Admin permissions required");
    });
  });

  describe("POST /api/badges/nominations/:nominationId/reject", () => {
    let nominationId: string;

    beforeEach(async () => {
      // Insert test nomination
      const [nomination] = await db.insert(BadgeNominationSchema).values({
        badgeTypeId: testBadgeType.id,
        nomineeUserId: testUser2.id,
        nominatorUserId: testUser1.id,
        status: "pending",
        nominationReason: "Test rejection",
      }).returning();

      nominationId = nomination.id;
    });

    it("should reject nomination (admin only)", async () => {
      const response = await fetch(`${BASE_URL}/api/badges/nominations/${nominationId}/reject`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${adminToken}`,
        },
      });

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.success).toBe(true);
      expect(result.data.message).toContain("rejected");

      // Verify nomination status updated
      const [updatedNomination] = await db
        .select()
        .from(BadgeNominationSchema)
        .where(eq(BadgeNominationSchema.id, nominationId));

      expect(updatedNomination.status).toBe("rejected");
      expect(updatedNomination.processedAt).toBeDefined();
    });
  });

  describe("Auto-approval threshold", () => {
    it("should auto-approve when nomination threshold is met", async () => {
      // Create multiple nominations for the same user/badge (threshold = 3)
      const nominations = [
        {
          badgeTypeId: testBadgeType.id,
          nomineeUserId: testUser2.id,
          nominatorUserId: testUser1.id,
          status: "pending",
        },
        {
          badgeTypeId: testBadgeType.id,
          nomineeUserId: testUser2.id,
          nominatorUserId: testAdmin.id,
          status: "pending",
        },
      ];

      await db.insert(BadgeNominationSchema).values(nominations);

      // Submit the third nomination that should trigger auto-approval
      const thirdNomination = {
        badgeTypeId: testBadgeType.id,
        nomineeUserId: testUser2.id,
        nominationReason: "Third nomination - should trigger auto-approval",
      };

      // Create a third user to nominate
      const testUser3 = {
        id: "test-user-3",
        username: "nominator2",
        email: "<EMAIL>",
        password: "hashedpassword",
      };

      await db.insert(UserSchema).values(testUser3);

      const response = await fetch(`${BASE_URL}/api/badges/nominations`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer mock-user3-token`,
        },
        body: JSON.stringify(thirdNomination),
      });

      expect(response.status).toBe(200);

      // Verify badge was auto-assigned
      const [userBadge] = await db
        .select()
        .from(UserBadgeSchema)
        .where(
          and(
            eq(UserBadgeSchema.userId, testUser2.id),
            eq(UserBadgeSchema.badgeTypeId, testBadgeType.id)
          )
        );

      expect(userBadge).toBeDefined();
      expect(userBadge.assignedBy).toBe("system");

      // Verify all nominations were marked as approved
      const approvedNominations = await db
        .select()
        .from(BadgeNominationSchema)
        .where(
          and(
            eq(BadgeNominationSchema.badgeTypeId, testBadgeType.id),
            eq(BadgeNominationSchema.nomineeUserId, testUser2.id),
            eq(BadgeNominationSchema.status, "approved")
          )
        );

      expect(approvedNominations).toHaveLength(3);
    });
  });
});