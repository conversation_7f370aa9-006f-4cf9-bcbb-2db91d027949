import { describe, it, expect, beforeEach, afterEach } from "bun:test";
import { BadgeVisualRenderer } from "../../utils/badge-visual-renderer";
import { BadgeDisplayComponents } from "../../utils/badge-display-components";
import { BadgeAccessibility } from "../../utils/badge-accessibility";
import { BadgeType, UserBadge, BadgeCollection, UserCollectionProgress } from "../../types/badge.types";

describe("Badge Visual System Integration", () => {
  let mockBadgeTypes: BadgeType[];
  let mockUserBadges: UserBadge[];
  let mockCollection: BadgeCollection;
  let mockProgress: UserCollectionProgress;

  beforeEach(() => {
    // Create mock badge types with different designs
    mockBadgeTypes = [
      {
        id: "badge-1",
        collectionId: "collection-1",
        badgeId: "first-message",
        name: "First Message",
        title: "Communication Starter",
        description: "Sent your first message",
        icon: "💬",
        tooltip: "Welcome to the community!",
        design: {
          shape: "circle",
          background: "#4CAF50",
          colors: ["#4CAF50", "#2E7D32"],
          gradient: "linear",
          pattern: "dots",
          elements: ["border"]
        },
        criteria: { requirement: "Send first message", tracked: "message_count", type: "message_count", threshold: 1 },
        perks: ["Chat color"],
        unlockType: "automatic",
        animation: "pulse",
        displayOrder: 1,
        category: "milestone",
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "badge-2",
        collectionId: "collection-1",
        badgeId: "active-member",
        name: "Active Member",
        title: "Community Regular",
        description: "Active for 30 days",
        icon: "⭐",
        tooltip: "You're a valued community member!",
        design: {
          shape: "star",
          background: "#FF9800",
          colors: ["#FF9800", "#F57C00"],
          gradient: "radial",
          elements: ["glow", "sparkles"]
        },
        criteria: { requirement: "Active for 30 days", tracked: "days_active", type: "days_active", threshold: 30 },
        perks: ["Priority support", "Special role"],
        unlockType: "automatic",
        animation: "glow",
        displayOrder: 2,
        category: "achievement",
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "badge-3",
        collectionId: null,
        badgeId: "moderator",
        name: "Moderator",
        title: "Community Guardian",
        description: "Trusted community moderator",
        icon: "🛡️",
        tooltip: "Keeping the community safe and friendly",
        design: {
          shape: "shield",
          background: "#9C27B0",
          colors: ["#9C27B0", "#6A1B9A"],
          gradient: "linear",
          elements: ["border", "glow"]
        },
        criteria: { requirement: "Manual assignment", tracked: "none" },
        perks: ["Moderation tools", "Special badge", "Priority access"],
        unlockType: "manual",
        animation: "none",
        displayOrder: 0,
        category: "role",
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // Create mock user badges
    mockUserBadges = [
      {
        id: "user-badge-1",
        userId: "user-123",
        badgeTypeId: "badge-1",
        collectionId: "collection-1",
        assignedAt: new Date(),
        isVisible: true,
        badgeType: mockBadgeTypes[0]
      },
      {
        id: "user-badge-2",
        userId: "user-123",
        badgeTypeId: "badge-2",
        collectionId: "collection-1",
        assignedAt: new Date(),
        isVisible: true,
        badgeType: mockBadgeTypes[1]
      },
      {
        id: "user-badge-3",
        userId: "user-123",
        badgeTypeId: "badge-3",
        collectionId: null,
        assignedBy: "admin-456",
        assignedAt: new Date(),
        isVisible: true,
        badgeType: mockBadgeTypes[2]
      }
    ];

    // Create mock collection
    mockCollection = {
      id: "collection-1",
      collectionId: "communication-journey",
      name: "Communication Journey",
      description: "Your path to becoming a communication expert",
      type: "progressive",
      totalBadges: 5,
      unlockedBy: "activity_and_engagement",
      completionReward: {
        badge: "Communication Master",
        title: "Master Communicator",
        perks: ["Golden chat color", "VIP access"],
        visual: "Golden crown with communication symbols",
        animation: "royal-glow"
      },
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Create mock progress
    mockProgress = {
      id: "progress-1",
      userId: "user-123",
      collectionId: "collection-1",
      badgesEarned: 2,
      totalBadges: 5,
      isCompleted: false,
      completionRewardGranted: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  });

  describe("End-to-End Badge Rendering", () => {
    it("should render complete badge system with all features", () => {
      // Test complete rendering pipeline
      const badges = mockUserBadges.map(userBadge => {
        if (!userBadge.badgeType) return null;

        // Render SVG
        const renderResult = BadgeVisualRenderer.renderBadgeSVG(userBadge.badgeType, {
          size: 'large',
          animation: true,
          accessibility: true
        });

        // Generate accessibility attributes
        const accessibilityResult = BadgeAccessibility.generateAccessibilityAttributes(
          userBadge.badgeType,
          {
            includeAriaLabels: true,
            includeDescriptions: true,
            includeKeyboardNavigation: true,
            includeHighContrast: true,
            includeReducedMotion: true
          }
        );

        return {
          userBadge,
          renderResult,
          accessibilityResult
        };
      }).filter(Boolean);

      // Verify all badges rendered successfully
      expect(badges).toHaveLength(3);
      
      badges.forEach(badge => {
        expect(badge!.renderResult.svg).toContain('<svg');
        expect(badge!.renderResult.accessibility.alt).toBeTruthy();
        expect(badge!.accessibilityResult.attributes.role).toBeTruthy();
      });
    });

    it("should create responsive badge grid with all accessibility features", () => {
      const result = BadgeDisplayComponents.createBadgeGrid(mockUserBadges, {
        maxVisible: 10,
        showTooltips: true,
        responsive: true,
        groupByCollection: true,
        showProgress: true,
        accessibility: true,
        theme: 'auto'
      });

      // Verify HTML structure
      expect(result.html).toContain('class="badge-grid');
      expect(result.html).toContain('role="list"');
      expect(result.html).toContain('aria-label');

      // Verify CSS includes responsive breakpoints
      expect(result.css).toContain('@media');
      expect(result.css).toContain('grid-template-columns');

      // Verify JavaScript for interactivity
      expect(result.javascript).toBeDefined();
      expect(result.javascript).toContain('tooltip');

      // Verify accessibility information
      expect(result.accessibility.totalBadges).toBe(3);
      expect(result.accessibility.visibleBadges).toBe(3);
      expect(result.accessibility.ariaLabel).toContain('3 badges');
    });

    it("should create collection progress display with visual indicators", () => {
      const earnedBadges = mockUserBadges.filter(badge => badge.collectionId === "collection-1");
      
      const result = BadgeDisplayComponents.createCollectionProgressDisplay(
        mockCollection,
        mockProgress,
        earnedBadges
      );

      // Verify structure
      expect(result.html).toContain('collection-progress');
      expect(result.html).toContain('Communication Journey');
      expect(result.html).toContain('2/5');

      // Verify progress visualization
      expect(result.html).toContain('progress-bar');
      expect(result.html).toContain('width: 40%'); // 2/5 = 40%

      // Verify badge slots
      expect(result.html).toContain('badge-slot earned');
      expect(result.html).toContain('badge-slot unearned');

      // Verify CSS styling
      expect(result.css).toContain('.badge-slots');
      expect(result.css).toContain('grid-template-columns');
      expect(result.css).toContain('@media'); // Responsive design
    });
  });

  describe("Accessibility Integration", () => {
    it("should validate complete system accessibility", () => {
      const validationResults = mockBadgeTypes.map(badge => 
        BadgeAccessibility.validateAccessibilityCompliance(badge)
      );

      // Check that validation runs for all badges
      validationResults.forEach(result => {
        expect(typeof result.isCompliant).toBe('boolean');
        expect(Array.isArray(result.issues)).toBe(true);
      });
    });

    it("should generate accessible tooltips for all badges", () => {
      const tooltips = mockBadgeTypes.map(badge => 
        BadgeAccessibility.generateAccessibleTooltip(badge)
      );

      tooltips.forEach(tooltip => {
        expect(tooltip.html).toContain('role="tooltip"');
        expect(tooltip.html).toContain('aria-hidden="true"');
        expect(tooltip.css).toContain('.badge-tooltip');
        expect(tooltip.javascript).toContain('showTooltip');
      });
    });

    it("should analyze color contrast for all badge designs", () => {
      const contrastResults = mockBadgeTypes.map(badge => {
        const design = typeof badge.design === 'string' 
          ? JSON.parse(badge.design) 
          : badge.design;
        
        if (design.colors && design.colors.length >= 2) {
          return BadgeAccessibility.analyzeColorContrast(
            design.colors[0],
            design.colors[1]
          );
        }
        return null;
      }).filter(Boolean);

      // Verify contrast analysis
      contrastResults.forEach(result => {
        expect(result!.ratio).toBeGreaterThan(0);
        expect(['AA', 'AAA', 'FAIL']).toContain(result!.level);
        expect(typeof result!.isAccessible).toBe('boolean');
      });
    });

    it("should support keyboard navigation across badge grid", () => {
      const result = BadgeDisplayComponents.createBadgeGrid(mockUserBadges, {
        showTooltips: true,
        accessibility: true
      });

      // Verify tooltip functionality is included
      expect(result.javascript).toContain('mouseenter');
      expect(result.javascript).toContain('focus');
      expect(result.javascript).toContain('aria-describedby');
    });
  });

  describe("Responsive Design Integration", () => {
    it("should adapt to different screen sizes", () => {
      const result = BadgeDisplayComponents.createBadgeGrid(mockUserBadges, {
        responsive: true
      });

      // Verify responsive breakpoints
      expect(result.css).toContain('@media (max-width: 768px)'); // Mobile
      expect(result.css).toContain('@media (max-width: 1200px)'); // Tablet

      // Verify grid adaptations
      expect(result.css).toContain('grid-template-columns: repeat(3, 1fr)'); // Mobile columns
      expect(result.css).toContain('grid-template-columns: repeat(4, 1fr)'); // Tablet columns
      expect(result.css).toContain('grid-template-columns: repeat(5, 1fr)'); // Desktop columns
    });

    it("should create compact display for mobile profiles", () => {
      const result = BadgeDisplayComponents.createCompactBadgeList(mockUserBadges, 3);

      // Verify compact structure
      expect(result.html).toContain('badge-list-compact');
      expect(result.css).toContain('flex');
      expect(result.css).toContain('gap: 4px');

      // Verify hover effects
      expect(result.css).toContain(':hover');
      expect(result.css).toContain('transform: scale(1.1)');
    });
  });

  describe("Animation and Visual Effects", () => {
    it("should render different animation types correctly", () => {
      const animationTypes = ['pulse', 'glow', 'bounce', 'rotate', 'shake', 'fade'];
      
      animationTypes.forEach(animation => {
        const badge = { ...mockBadgeTypes[0], animation };
        const result = BadgeVisualRenderer.renderBadgeSVG(badge, { animation: true });

        expect(result.css).toBeDefined();
        expect(result.css).toContain('@keyframes');
        expect(result.css).toContain(`badge-animation-${animation}`);
      });
    });

    it("should respect reduced motion preferences", () => {
      const result = BadgeAccessibility.generateAccessibilityAttributes(mockBadgeTypes[0], {
        includeReducedMotion: true
      });

      expect(result.css).toContain('@media (prefers-reduced-motion: reduce)');
      expect(result.css).toContain('animation: none !important');
    });

    it("should provide static fallbacks for animations", () => {
      mockBadgeTypes.forEach(badge => {
        const motionPrefs = BadgeAccessibility.getMotionPreferences(badge);
        
        expect(motionPrefs.respectReducedMotion).toBe(true);
        
        if (badge.animation && badge.animation !== 'none') {
          expect(motionPrefs.alternativeAnimations.length).toBeGreaterThan(0);
          expect(motionPrefs.staticFallback).toBeTruthy();
        }
      });
    });
  });

  describe("Theme Integration", () => {
    it("should support light and dark themes", () => {
      const lightResult = BadgeDisplayComponents.createBadgeGrid(mockUserBadges, {
        theme: 'light'
      });
      
      const darkResult = BadgeDisplayComponents.createBadgeGrid(mockUserBadges, {
        theme: 'dark'
      });

      // Verify theme classes
      expect(lightResult.html).toContain('class="badge-grid light"');
      expect(darkResult.html).toContain('class="badge-grid dark"');

      // Verify different color schemes
      expect(lightResult.css).toContain('background: #ffffff');
      expect(darkResult.css).toContain('background: #2a2a2a');
    });

    it("should adapt to system theme preferences", () => {
      const result = BadgeDisplayComponents.createBadgeGrid(mockUserBadges, {
        theme: 'auto'
      });

      expect(result.html).toContain('class="badge-grid auto"');
    });
  });

  describe("Performance and Optimization", () => {
    it("should handle large numbers of badges efficiently", () => {
      // Create many badges
      const manyBadges = Array.from({ length: 100 }, (_, i) => ({
        ...mockUserBadges[0],
        id: `badge-${i}`,
        badgeTypeId: `type-${i}`
      }));

      const startTime = Date.now();
      
      const result = BadgeDisplayComponents.createBadgeGrid(manyBadges, {
        maxVisible: 20
      });
      
      const endTime = Date.now();
      const renderTime = endTime - startTime;

      // Should render quickly (under 100ms for 100 badges)
      expect(renderTime).toBeLessThan(100);
      
      // Should limit visible badges
      expect(result.accessibility.visibleBadges).toBe(20);
      expect(result.accessibility.hiddenBadges).toBe(80);
    });

    it("should generate efficient CSS without duplication", () => {
      const result = BadgeDisplayComponents.createBadgeGrid(mockUserBadges);

      // CSS should not have excessive duplication
      const cssLines = result.css.split('\n');
      const uniqueSelectors = new Set();
      
      cssLines.forEach(line => {
        const trimmed = line.trim();
        if (trimmed.includes('{')) {
          const selector = trimmed.split('{')[0].trim();
          if (selector) {
            uniqueSelectors.add(selector);
          }
        }
      });

      // Should have reasonable number of unique selectors
      expect(uniqueSelectors.size).toBeLessThan(50);
    });
  });

  describe("Error Handling and Edge Cases", () => {
    it("should handle badges without badge types gracefully", () => {
      const badgesWithoutTypes = [
        { ...mockUserBadges[0], badgeType: undefined }
      ];

      const result = BadgeDisplayComponents.createBadgeGrid(badgesWithoutTypes);

      expect(result.html).toBeTruthy();
      // The badge grid should handle missing badge types gracefully
      expect(result.accessibility.visibleBadges).toBeGreaterThanOrEqual(0);
    });

    it("should handle invalid badge designs gracefully", () => {
      const invalidBadge = {
        ...mockBadgeTypes[0],
        design: {
          shape: "",
          background: "",
          colors: []
        }
      };

      const validation = BadgeVisualRenderer.validateBadgeDesign(invalidBadge.design);
      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);

      // Should still attempt to render with fallbacks
      const result = BadgeVisualRenderer.renderBadgeSVG(invalidBadge);
      expect(result.svg).toContain('<svg');
    });

    it("should handle empty badge collections", () => {
      const emptyProgress = { ...mockProgress, badgesEarned: 0, totalBadges: 0 };
      
      const result = BadgeDisplayComponents.createCollectionProgressDisplay(
        mockCollection,
        emptyProgress,
        []
      );

      expect(result.html).toContain('collection-progress');
      expect(result.html).toContain('0/0');
    });
  });

  describe("Localization Integration", () => {
    it("should support multiple languages", () => {
      const languages = ['en', 'es', 'fr'];
      
      languages.forEach(lang => {
        const result = BadgeAccessibility.generateAccessibilityAttributes(mockBadgeTypes[0], {
          language: lang
        });

        expect(result.attributes['aria-label']).toBeTruthy();
        expect(result.screenReaderText).toBeTruthy();
      });
    });

    it("should generate localized tooltips", () => {
      const englishTooltip = BadgeAccessibility.generateAccessibleTooltip(mockBadgeTypes[0], 'en');
      const spanishTooltip = BadgeAccessibility.generateAccessibleTooltip(mockBadgeTypes[0], 'es');

      expect(englishTooltip.html).toContain('Benefits:');
      expect(spanishTooltip.html).toContain('Beneficios:');
    });
  });
});