import { describe, test, expect, beforeEach, afterEach, mock } from "bun:test";
import { 
  getBadgeTypesHandler,
  createBadgeTypeHandler,
  updateBadgeTypeHandler,
  deleteBadgeTypeHandler,
  getUserBadgesHandler,
  assignBadgeToUserHandler,
  removeBadgeFromUserHandler,
  getAvailableBadgesHandler,
  evaluateUserBadgesHandler,
  getBadgeStatsHandler,
  getBadgeLeaderboardHandler
} from "../../handlers/badges";
import { ResponseUtils } from "../../utils/response-utils";
import type {
  BadgeType,
  UserBadge,
  BadgeStats,
  BadgeLeaderboard,
  EvaluationResult
} from "../../types/badge.types";

// Mock the badge service
const mockBadgeService = {
  createBadgeType: mock(),
  updateBadgeType: mock(),
  deleteBadgeType: mock(),
  getBadgeTypes: mock(),
  getBadgeTypeById: mock(),
  assignBadge: mock(),
  removeBadge: mock(),
  getUserBadges: mock(),
  getAvailableBadgesForUser: mock(),
  evaluateUserBadges: mock(),
  getBadgeStats: mock(),
  getBadgeLeaderboard: mock()
};

// Mock the service module
mock.module("../../services/badge.service", () => ({
  BadgeService: class {
    constructor() {
      return mockBadgeService;
    }
  }
}));

// Mock the database
mock.module("../../db", () => ({ db: {} }));

describe("Badge API Integration Tests", () => {
  const mockUserId = "550e8400-e29b-41d4-a716-446655440000";
  const mockBadgeTypeId = "550e8400-e29b-41d4-a716-446655440001";
  const mockAdminId = "550e8400-e29b-41d4-a716-446655440002";

  beforeEach(() => {
    // Reset all mocks
    Object.values(mockBadgeService).forEach(mock => mock.mockReset());
  });

  afterEach(() => {
    // Clean up any test state
  });

  describe("Badge Type Management Endpoints", () => {
    describe("GET /api/badges/types", () => {
      test("should return badge types with default filters", async () => {
        const mockBadgeTypes: BadgeType[] = [
          {
            id: "badge-1",
            badgeId: "first-message",
            name: "First Message",
            description: "Sent your first message",
            icon: "💬",
            design: {
              shape: "circle",
              background: "#4CAF50",
              colors: ["#4CAF50", "#45a049"]
            },
            criteria: {
              requirement: "Send your first message",
              tracked: "message_count",
              type: "message_count",
              threshold: 1
            },
            unlockType: "automatic",
            category: "milestone",
            displayOrder: 1,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ];

        mockBadgeService.getBadgeTypes.mockResolvedValue(mockBadgeTypes);

        const request = new Request("http://localhost/api/badges/types", {
          method: "GET"
        });

        const response = await getBadgeTypesHandler(request);
        const responseData = await response.json();

        expect(response.status).toBe(200);
        expect(responseData.success).toBe(true);
        expect(responseData.data).toEqual(mockBadgeTypes);
        expect(mockBadgeService.getBadgeTypes).toHaveBeenCalledWith(undefined, undefined, undefined);
      });

      test("should return badge types with filters", async () => {
        const mockBadgeTypes: BadgeType[] = [];
        mockBadgeService.getBadgeTypes.mockResolvedValue(mockBadgeTypes);

        const request = new Request("http://localhost/api/badges/types?category=achievement&unlockType=automatic&limit=5&offset=10", {
          method: "GET"
        });

        const response = await getBadgeTypesHandler(request);
        const responseData = await response.json();

        expect(response.status).toBe(200);
        expect(responseData.success).toBe(true);
        expect(mockBadgeService.getBadgeTypes).toHaveBeenCalledWith(
          { category: "achievement", unlockType: "automatic" },
          5,
          10
        );
      });

      test("should handle validation errors for invalid filters", async () => {
        const request = new Request("http://localhost/api/badges/types?category=invalid&unlockType=invalid", {
          method: "GET"
        });

        const response = await getBadgeTypesHandler(request);
        const responseData = await response.json();

        expect(response.status).toBe(400);
        expect(responseData.success).toBe(false);
        expect(responseData.error).toBeDefined();
      });

      test("should reject non-GET requests", async () => {
        const request = new Request("http://localhost/api/badges/types", {
          method: "POST"
        });

        const response = await getBadgeTypesHandler(request);

        expect(response.status).toBe(405);
      });
    });

    describe("POST /api/badges/types/create", () => {
      test("should create badge type successfully", async () => {
        const badgeData = {
          badgeId: "test-badge",
          name: "Test Badge",
          description: "A test badge",
          icon: "🏆",
          design: {
            shape: "circle",
            background: "#FFD700",
            colors: ["#FFD700", "#FFA500"]
          },
          criteria: {
            requirement: "Send 10 messages",
            tracked: "message_count",
            type: "message_count",
            threshold: 10
          },
          unlockType: "automatic",
          category: "achievement"
        };

        const expectedBadge: BadgeType = {
          id: mockBadgeTypeId,
          ...badgeData,
          displayOrder: 1,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockBadgeService.createBadgeType.mockResolvedValue(expectedBadge);

        const request = new Request("http://localhost/api/badges/types/create", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(badgeData)
        });

        // Mock authenticated user
        (request as any).user = { userId: mockAdminId };

        const response = await createBadgeTypeHandler(request);
        const responseData = await response.json();

        expect(response.status).toBe(201);
        expect(responseData.success).toBe(true);
        expect(responseData.data).toEqual(expectedBadge);
        expect(mockBadgeService.createBadgeType).toHaveBeenCalledWith(
          expect.objectContaining(badgeData),
          mockAdminId
        );
      });

      test("should handle validation errors", async () => {
        const invalidData = {
          name: "", // Invalid: empty name
          description: "Test description",
          category: "achievement",
          unlockType: "automatic"
        };

        const request = new Request("http://localhost/api/badges/types/create", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(invalidData)
        });

        (request as any).user = { userId: mockAdminId };

        const response = await createBadgeTypeHandler(request);
        const responseData = await response.json();

        expect(response.status).toBe(400);
        expect(responseData.success).toBe(false);
        expect(responseData.error).toBeDefined();
      });

      test("should reject non-POST requests", async () => {
        const request = new Request("http://localhost/api/badges/types/create", {
          method: "GET"
        });

        const response = await createBadgeTypeHandler(request);

        expect(response.status).toBe(405);
      });
    });

    describe("PUT /api/badges/types/:id/update", () => {
      test("should update badge type successfully", async () => {
        const updates = {
          name: "Updated Badge Name",
          description: "Updated description"
        };

        const updatedBadge: BadgeType = {
          id: mockBadgeTypeId,
          badgeId: "test-badge",
          name: "Updated Badge Name",
          description: "Updated description",
          icon: "🏆",
          design: {
            shape: "circle",
            background: "#FFD700",
            colors: ["#FFD700"]
          },
          criteria: {
            requirement: "Test",
            tracked: "test",
            type: "message_count",
            threshold: 10
          },
          unlockType: "automatic",
          category: "achievement",
          displayOrder: 1,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockBadgeService.updateBadgeType.mockResolvedValue(updatedBadge);

        const request = new Request(`http://localhost/api/badges/types/${mockBadgeTypeId}/update`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(updates)
        });

        (request as any).user = { userId: mockAdminId };

        const response = await updateBadgeTypeHandler(request, { id: mockBadgeTypeId });
        const responseData = await response.json();

        expect(response.status).toBe(200);
        expect(responseData.success).toBe(true);
        expect(responseData.data).toEqual(updatedBadge);
        expect(mockBadgeService.updateBadgeType).toHaveBeenCalledWith(
          mockBadgeTypeId,
          expect.objectContaining(updates),
          mockAdminId
        );
      });

      test("should handle missing badge ID", async () => {
        const request = new Request("http://localhost/api/badges/types/update", {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ name: "Updated" })
        });

        (request as any).user = { userId: mockAdminId };

        const response = await updateBadgeTypeHandler(request);
        const responseData = await response.json();

        expect(response.status).toBe(400);
        expect(responseData.success).toBe(false);
        expect(responseData.error).toContain("Badge type ID is required");
      });
    });

    describe("DELETE /api/badges/types/:id/delete", () => {
      test("should delete badge type successfully", async () => {
        mockBadgeService.deleteBadgeType.mockResolvedValue(undefined);

        const request = new Request(`http://localhost/api/badges/types/${mockBadgeTypeId}/delete`, {
          method: "DELETE"
        });

        (request as any).user = { userId: mockAdminId };

        const response = await deleteBadgeTypeHandler(request, { id: mockBadgeTypeId });
        const responseData = await response.json();

        expect(response.status).toBe(200);
        expect(responseData.success).toBe(true);
        expect(responseData.data).toBe(null);
        expect(mockBadgeService.deleteBadgeType).toHaveBeenCalledWith(mockBadgeTypeId, mockAdminId);
      });

      test("should handle missing badge ID", async () => {
        const request = new Request("http://localhost/api/badges/types/delete", {
          method: "DELETE"
        });

        (request as any).user = { userId: mockAdminId };

        const response = await deleteBadgeTypeHandler(request);
        const responseData = await response.json();

        expect(response.status).toBe(400);
        expect(responseData.success).toBe(false);
        expect(responseData.error).toContain("Badge type ID is required");
      });
    });
  });

  describe("User Badge Management Endpoints", () => {
    describe("GET /api/users/:userId/badges", () => {
      test("should get user badges successfully", async () => {
        const mockUserBadges: UserBadge[] = [
          {
            id: "user-badge-1",
            userId: mockUserId,
            badgeTypeId: "badge-1",
            assignedAt: new Date(),
            isVisible: true,
            badgeType: {
              id: "badge-1",
              badgeId: "first-message",
              name: "First Message",
              description: "Sent first message",
              icon: "💬",
              design: { shape: "circle", background: "#4CAF50", colors: ["#4CAF50"] },
              criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 1 },
              unlockType: "automatic",
              category: "milestone",
              displayOrder: 1,
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            }
          }
        ];

        mockBadgeService.getUserBadges.mockResolvedValue(mockUserBadges);

        const request = new Request(`http://localhost/api/users/${mockUserId}/badges`, {
          method: "GET"
        });

        const response = await getUserBadgesHandler(request, { userId: mockUserId });
        const responseData = await response.json();

        expect(response.status).toBe(200);
        expect(responseData.success).toBe(true);
        expect(responseData.data).toEqual(mockUserBadges);
        expect(mockBadgeService.getUserBadges).toHaveBeenCalledWith(mockUserId, false);
      });

      test("should filter visible badges only", async () => {
        const mockUserBadges: UserBadge[] = [];
        mockBadgeService.getUserBadges.mockResolvedValue(mockUserBadges);

        const request = new Request(`http://localhost/api/users/${mockUserId}/badges?visibleOnly=true`, {
          method: "GET"
        });

        const response = await getUserBadgesHandler(request, { userId: mockUserId });

        expect(response.status).toBe(200);
        expect(mockBadgeService.getUserBadges).toHaveBeenCalledWith(mockUserId, true);
      });

      test("should handle missing user ID", async () => {
        const request = new Request("http://localhost/api/users/badges", {
          method: "GET"
        });

        const response = await getUserBadgesHandler(request);
        const responseData = await response.json();

        expect(response.status).toBe(400);
        expect(responseData.success).toBe(false);
        expect(responseData.error).toContain("User ID is required");
      });
    });

    describe("POST /api/users/:userId/badges/assign", () => {
      test("should assign badge to user successfully", async () => {
        const assignmentData = {
          badgeTypeId: mockBadgeTypeId
        };

        const expectedUserBadge: UserBadge = {
          id: "new-user-badge",
          userId: mockUserId,
          badgeTypeId: mockBadgeTypeId,
          assignedBy: mockAdminId,
          assignedAt: new Date(),
          isVisible: true
        };

        mockBadgeService.assignBadge.mockResolvedValue(expectedUserBadge);

        const request = new Request(`http://localhost/api/users/${mockUserId}/badges/assign`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(assignmentData)
        });

        (request as any).user = { userId: mockAdminId };

        const response = await assignBadgeToUserHandler(request, { userId: mockUserId });
        const responseData = await response.json();

        expect(response.status).toBe(201);
        expect(responseData.success).toBe(true);
        expect(responseData.data).toEqual(expectedUserBadge);
        expect(mockBadgeService.assignBadge).toHaveBeenCalledWith(
          mockUserId,
          mockBadgeTypeId,
          mockAdminId,
          undefined
        );
      });

      test("should handle server-specific badge assignment", async () => {
        const assignmentData = {
          badgeTypeId: mockBadgeTypeId
        };

        const expectedUserBadge: UserBadge = {
          id: "server-badge",
          userId: mockUserId,
          badgeTypeId: mockBadgeTypeId,
          assignedBy: mockAdminId,
          assignedAt: new Date(),
          isVisible: true
        };

        mockBadgeService.assignBadge.mockResolvedValue(expectedUserBadge);

        const serverId = "server-123";
        const request = new Request(`http://localhost/api/users/${mockUserId}/badges/assign?serverId=${serverId}`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(assignmentData)
        });

        (request as any).user = { userId: mockAdminId };

        const response = await assignBadgeToUserHandler(request, { userId: mockUserId });

        expect(response.status).toBe(201);
        expect(mockBadgeService.assignBadge).toHaveBeenCalledWith(
          mockUserId,
          mockBadgeTypeId,
          mockAdminId,
          serverId
        );
      });

      test("should handle validation errors", async () => {
        const invalidData = {
          badgeTypeId: "invalid-uuid"
        };

        const request = new Request(`http://localhost/api/users/${mockUserId}/badges/assign`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(invalidData)
        });

        (request as any).user = { userId: mockAdminId };

        const response = await assignBadgeToUserHandler(request, { userId: mockUserId });
        const responseData = await response.json();

        expect(response.status).toBe(400);
        expect(responseData.success).toBe(false);
        expect(responseData.error).toBeDefined();
      });
    });

    describe("DELETE /api/users/:userId/badges/:badgeId/remove", () => {
      test("should remove badge from user successfully", async () => {
        mockBadgeService.removeBadge.mockResolvedValue(undefined);

        const request = new Request(`http://localhost/api/users/${mockUserId}/badges/${mockBadgeTypeId}/remove`, {
          method: "DELETE"
        });

        (request as any).user = { userId: mockAdminId };

        const response = await removeBadgeFromUserHandler(request, { 
          userId: mockUserId, 
          badgeId: mockBadgeTypeId 
        });
        const responseData = await response.json();

        expect(response.status).toBe(200);
        expect(responseData.success).toBe(true);
        expect(responseData.data).toBe(null);
        expect(mockBadgeService.removeBadge).toHaveBeenCalledWith(
          mockUserId,
          mockBadgeTypeId,
          mockAdminId,
          undefined
        );
      });

      test("should handle missing parameters", async () => {
        const request = new Request("http://localhost/api/users/badges/remove", {
          method: "DELETE"
        });

        (request as any).user = { userId: mockAdminId };

        const response = await removeBadgeFromUserHandler(request);
        const responseData = await response.json();

        expect(response.status).toBe(400);
        expect(responseData.success).toBe(false);
        expect(responseData.error).toContain("User ID is required");
      });
    });
  });

  describe("Badge Information Endpoints", () => {
    describe("GET /api/badges/available", () => {
      test("should get available badges for current user", async () => {
        const mockAvailableBadges: BadgeType[] = [
          {
            id: "available-badge-1",
            badgeId: "server-creator",
            name: "Server Creator",
            description: "Created your first server",
            icon: "🏗️",
            design: { shape: "hexagon", background: "#9C27B0", colors: ["#9C27B0"] },
            criteria: { requirement: "Create a server", tracked: "server_count", type: "server_count", threshold: 1 },
            unlockType: "automatic",
            category: "achievement",
            displayOrder: 1,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ];

        mockBadgeService.getAvailableBadgesForUser.mockResolvedValue(mockAvailableBadges);

        const request = new Request("http://localhost/api/badges/available", {
          method: "GET"
        });

        (request as any).user = { userId: mockUserId };

        const response = await getAvailableBadgesHandler(request);
        const responseData = await response.json();

        expect(response.status).toBe(200);
        expect(responseData.success).toBe(true);
        expect(responseData.data).toEqual(mockAvailableBadges);
        expect(mockBadgeService.getAvailableBadgesForUser).toHaveBeenCalledWith(mockUserId);
      });
    });

    describe("POST /api/badges/evaluate/:userId", () => {
      test("should trigger badge evaluation for user", async () => {
        const mockEvaluationResult: EvaluationResult = {
          userId: mockUserId,
          newBadges: [],
          evaluatedBadges: ["badge-1", "badge-2"],
          collectionProgress: [],
          errors: []
        };

        mockBadgeService.evaluateUserBadges.mockResolvedValue(mockEvaluationResult);

        const request = new Request(`http://localhost/api/badges/evaluate/${mockUserId}`, {
          method: "POST"
        });

        const response = await evaluateUserBadgesHandler(request, { userId: mockUserId });
        const responseData = await response.json();

        expect(response.status).toBe(200);
        expect(responseData.success).toBe(true);
        expect(responseData.data).toEqual(mockEvaluationResult);
        expect(mockBadgeService.evaluateUserBadges).toHaveBeenCalledWith(mockUserId);
      });

      test("should handle missing user ID", async () => {
        const request = new Request("http://localhost/api/badges/evaluate", {
          method: "POST"
        });

        const response = await evaluateUserBadgesHandler(request);
        const responseData = await response.json();

        expect(response.status).toBe(400);
        expect(responseData.success).toBe(false);
        expect(responseData.error).toContain("User ID is required");
      });
    });

    describe("GET /api/badges/stats", () => {
      test("should get badge statistics", async () => {
        const mockStats: BadgeStats = {
          totalBadges: 50,
          totalAssignments: 250,
          categoryBreakdown: {
            achievement: 20,
            role: 10,
            special: 8,
            community: 7,
            milestone: 5
          },
          mostPopularBadges: []
        };

        mockBadgeService.getBadgeStats.mockResolvedValue(mockStats);

        const request = new Request("http://localhost/api/badges/stats", {
          method: "GET"
        });

        const response = await getBadgeStatsHandler(request);
        const responseData = await response.json();

        expect(response.status).toBe(200);
        expect(responseData.success).toBe(true);
        expect(responseData.data).toEqual(mockStats);
        expect(mockBadgeService.getBadgeStats).toHaveBeenCalled();
      });
    });

    describe("GET /api/badges/leaderboard", () => {
      test("should get badge leaderboard with default limit", async () => {
        const mockLeaderboard: BadgeLeaderboard[] = [
          {
            userId: "top-user-1",
            username: "TopUser1",
            badgeCount: 25,
            badges: []
          },
          {
            userId: "top-user-2",
            username: "TopUser2",
            badgeCount: 20,
            badges: []
          }
        ];

        mockBadgeService.getBadgeLeaderboard.mockResolvedValue(mockLeaderboard);

        const request = new Request("http://localhost/api/badges/leaderboard", {
          method: "GET"
        });

        const response = await getBadgeLeaderboardHandler(request);
        const responseData = await response.json();

        expect(response.status).toBe(200);
        expect(responseData.success).toBe(true);
        expect(responseData.data).toEqual(mockLeaderboard);
        expect(mockBadgeService.getBadgeLeaderboard).toHaveBeenCalledWith(10);
      });

      test("should get badge leaderboard with custom limit", async () => {
        const mockLeaderboard: BadgeLeaderboard[] = [];
        mockBadgeService.getBadgeLeaderboard.mockResolvedValue(mockLeaderboard);

        const request = new Request("http://localhost/api/badges/leaderboard?limit=25", {
          method: "GET"
        });

        const response = await getBadgeLeaderboardHandler(request);

        expect(response.status).toBe(200);
        expect(mockBadgeService.getBadgeLeaderboard).toHaveBeenCalledWith(25);
      });
    });
  });

  describe("Error Handling", () => {
    test("should handle service errors gracefully", async () => {
      mockBadgeService.getBadgeTypes.mockRejectedValue(new Error("Database connection failed"));

      const request = new Request("http://localhost/api/badges/types", {
        method: "GET"
      });

      const response = await getBadgeTypesHandler(request);
      const responseData = await response.json();

      expect(response.status).toBe(500);
      expect(responseData.success).toBe(false);
      expect(responseData.error).toContain("Failed to retrieve badge types");
    });

    test("should handle authentication errors", async () => {
      const request = new Request("http://localhost/api/badges/types/create", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({})
      });

      // No user authentication

      const response = await createBadgeTypeHandler(request);
      const responseData = await response.json();

      expect(response.status).toBe(500);
      expect(responseData.success).toBe(false);
    });
  });
});