import { describe, test, expect, beforeAll, afterAll, beforeEach, afterEach } from "bun:test";
import { WebSocketUtils } from "../../utils/websocket-utils";
import { WebSocketManager } from "../../manager/websocket.manager";
import { createTestUser, cleanupTestData } from "../helpers";
import type { CustomWebSocketData } from "../../types/websocket-standardization.types";

// Mock WebSocket for rate limiting tests
class RateLimitMockWebSocket {
  public data: CustomWebSocketData;
  public readyState: number = WebSocket.OPEN;
  public sentMessages: string[] = [];
  public isOpen: boolean = true;
  public rateLimitHits: number = 0;

  constructor(data: Partial<CustomWebSocketData> = {}) {
    this.data = {
      userId: data.userId || "rate-limit-test-user",
      serverId: data.serverId,
      channelId: data.channelId,
      type: data.type || "channel",
      ...data
    };
  }

  send(message: string): void {
    if (this.isOpen) {
      const parsedMessage = JSON.parse(message);
      
      // Check if this is a rate limit error
      if (parsedMessage.success === false && parsedMessage.error?.code === "RATE_LIMITED") {
        this.rateLimitHits++;
      }
      
      this.sentMessages.push(message);
    }
  }

  close(): void {
    this.isOpen = false;
    this.readyState = WebSocket.CLOSED;
  }

  getLastMessage(): any {
    const lastMessage = this.sentMessages[this.sentMessages.length - 1];
    return lastMessage ? JSON.parse(lastMessage) : null;
  }

  getAllMessages(): any[] {
    return this.sentMessages.map(msg => JSON.parse(msg));
  }

  clearMessages(): void {
    this.sentMessages = [];
    this.rateLimitHits = 0;
  }
}

describe("WebSocket Rate Limiting Integration Tests", () => {
  let testUser: any;
  let mockWs: RateLimitMockWebSocket;
  let wsManager: WebSocketManager;

  beforeAll(async () => {
    testUser = await createTestUser("ratelimituser", "<EMAIL>");
    wsManager = WebSocketManager.getInstance();
  });

  afterAll(async () => {
    await cleanupTestData();
  });

  beforeEach(() => {
    mockWs = new RateLimitMockWebSocket({ userId: testUser.id });
  });

  afterEach(() => {
    mockWs.close();
  });

  describe("Message Rate Limiting", () => {
    test("should allow messages within rate limit", async () => {
      const messagesWithinLimit = 10;
      
      for (let i = 0; i < messagesWithinLimit; i++) {
        const message = WebSocketUtils.success("MESSAGE_SEND", {
          content: `Message ${i}`,
          channelId: "test-channel"
        });
        
        WebSocketUtils.send(mockWs as any, message);
      }

      // All messages should be sent successfully
      expect(mockWs.sentMessages.length).toBe(messagesWithinLimit);
      expect(mockWs.rateLimitHits).toBe(0);
    });

    test("should enforce rate limits for excessive messaging", async () => {
      const excessiveMessageCount = 150; // Exceeds typical rate limit
      
      for (let i = 0; i < excessiveMessageCount; i++) {
        const message = WebSocketUtils.success("MESSAGE_SEND", {
          content: `Spam message ${i}`,
          channelId: "test-channel"
        });
        
        // Simulate rate limiting check
        if (i > 100) { // Simulate rate limit after 100 messages
          const rateLimitError = WebSocketUtils.rateLimited(60); // 60 seconds retry
          WebSocketUtils.send(mockWs as any, rateLimitError);
        } else {
          WebSocketUtils.send(mockWs as any, message);
        }
      }

      // Should have some rate limit hits
      expect(mockWs.rateLimitHits).toBeGreaterThan(0);
      expect(mockWs.sentMessages.length).toBe(excessiveMessageCount);
    });

    test("should handle different rate limits for different message types", async () => {
      const messageTypes = [
        { type: "MESSAGE_SEND", limit: 50 },
        { type: "VOICE_DATA", limit: 200 },
        { type: "HEARTBEAT", limit: 1000 }
      ];

      for (const msgType of messageTypes) {
        mockWs.clearMessages();
        
        // Send messages up to the limit
        for (let i = 0; i < msgType.limit + 10; i++) {
          const message = WebSocketUtils.success(msgType.type, {
            data: `${msgType.type} data ${i}`
          });
          
          // Simulate different rate limits
          if (i >= msgType.limit) {
            const rateLimitError = WebSocketUtils.rateLimited(30);
            WebSocketUtils.send(mockWs as any, rateLimitError);
          } else {
            WebSocketUtils.send(mockWs as any, message);
          }
        }

        // Should have rate limit hits for messages over the limit
        if (msgType.limit < msgType.limit + 10) {
          expect(mockWs.rateLimitHits).toBeGreaterThan(0);
        }
      }
    });

    test("should reset rate limits after time window", async () => {
      // Send messages to hit rate limit
      for (let i = 0; i < 60; i++) {
        const message = WebSocketUtils.success("MESSAGE_SEND", {
          content: `Message ${i}`
        });
        WebSocketUtils.send(mockWs as any, message);
      }

      // Simulate rate limit hit
      const rateLimitError = WebSocketUtils.rateLimited(1); // 1 second retry
      WebSocketUtils.send(mockWs as any, rateLimitError);
      
      expect(mockWs.rateLimitHits).toBe(1);

      // Wait for rate limit window to reset (simulate)
      await new Promise(resolve => setTimeout(resolve, 1100));

      // Clear previous messages and try again
      mockWs.clearMessages();

      // Should be able to send messages again
      const newMessage = WebSocketUtils.success("MESSAGE_SEND", {
        content: "Message after reset"
      });
      WebSocketUtils.send(mockWs as any, newMessage);

      expect(mockWs.sentMessages.length).toBe(1);
      expect(mockWs.rateLimitHits).toBe(0);
    });
  });

  describe("Connection-based Rate Limiting", () => {
    test("should limit connections per user", async () => {
      const maxConnections = 5;
      const connections: RateLimitMockWebSocket[] = [];

      // Create multiple connections for the same user
      for (let i = 0; i < maxConnections + 3; i++) {
        const connection = new RateLimitMockWebSocket({
          userId: testUser.id,
          serverId: "test-server",
          channelId: `channel-${i}`
        });
        connections.push(connection);

        // Simulate connection limit check
        if (i >= maxConnections) {
          const limitError = WebSocketUtils.error("TOO_MANY_CONNECTIONS", 
            `Maximum ${maxConnections} connections allowed per user`);
          WebSocketUtils.send(connection as any, limitError);
        } else {
          const connectSuccess = WebSocketUtils.success("CONNECTION_ESTABLISHED", {
            connectionId: `conn-${i}`
          });
          WebSocketUtils.send(connection as any, connectSuccess);
        }
      }

      // Check that excess connections received error messages
      const excessConnections = connections.slice(maxConnections);
      excessConnections.forEach(conn => {
        const lastMessage = conn.getLastMessage();
        expect(lastMessage.success).toBe(false);
        expect(lastMessage.error.code).toBe("TOO_MANY_CONNECTIONS");
      });

      // Cleanup
      connections.forEach(conn => conn.close());
    });

    test("should handle rate limiting per IP address", async () => {
      const connectionsPerIP = [
        { ip: "***********", count: 10 },
        { ip: "***********", count: 15 },
        { ip: "***********", count: 5 }
      ];

      const maxConnectionsPerIP = 12;

      for (const ipGroup of connectionsPerIP) {
        const connections: RateLimitMockWebSocket[] = [];

        for (let i = 0; i < ipGroup.count; i++) {
          const connection = new RateLimitMockWebSocket({
            userId: `user-${ipGroup.ip}-${i}`,
            serverId: "test-server"
          });
          connections.push(connection);

          // Simulate IP-based rate limiting
          if (i >= maxConnectionsPerIP) {
            const ipLimitError = WebSocketUtils.error("IP_RATE_LIMITED", 
              `Too many connections from IP ${ipGroup.ip}`);
            WebSocketUtils.send(connection as any, ipLimitError);
          } else {
            const connectSuccess = WebSocketUtils.success("CONNECTION_ESTABLISHED", {
              ip: ipGroup.ip,
              connectionNumber: i
            });
            WebSocketUtils.send(connection as any, connectSuccess);
          }
        }

        // Verify rate limiting was applied correctly
        if (ipGroup.count > maxConnectionsPerIP) {
          const excessConnections = connections.slice(maxConnectionsPerIP);
          excessConnections.forEach(conn => {
            const lastMessage = conn.getLastMessage();
            expect(lastMessage.success).toBe(false);
            expect(lastMessage.error.code).toBe("IP_RATE_LIMITED");
          });
        }

        connections.forEach(conn => conn.close());
      }
    });
  });

  describe("Burst and Sustained Rate Limiting", () => {
    test("should handle burst traffic patterns", async () => {
      const burstSize = 20;
      const burstLimit = 15;
      
      // Send burst of messages
      const burstStart = Date.now();
      for (let i = 0; i < burstSize; i++) {
        const message = WebSocketUtils.success("MESSAGE_SEND", {
          content: `Burst message ${i}`,
          timestamp: Date.now()
        });

        // Simulate burst rate limiting
        if (i >= burstLimit) {
          const burstLimitError = WebSocketUtils.error("BURST_RATE_LIMITED", 
            "Too many messages in short time period");
          WebSocketUtils.send(mockWs as any, burstLimitError);
        } else {
          WebSocketUtils.send(mockWs as any, message);
        }
      }
      const burstEnd = Date.now();

      // Verify burst was detected and limited
      expect(mockWs.rateLimitHits).toBeGreaterThan(0);
      expect(burstEnd - burstStart).toBeLessThan(1000); // Burst completed quickly
    });

    test("should handle sustained traffic patterns", async () => {
      const sustainedDuration = 5000; // 5 seconds
      const messagesPerSecond = 10;
      const sustainedLimit = 8; // Lower than messagesPerSecond
      
      const startTime = Date.now();
      let messageCount = 0;
      let rateLimitCount = 0;

      // Simulate sustained traffic
      const interval = setInterval(() => {
        if (Date.now() - startTime >= sustainedDuration) {
          clearInterval(interval);
          return;
        }

        for (let i = 0; i < messagesPerSecond; i++) {
          messageCount++;
          
          const message = WebSocketUtils.success("MESSAGE_SEND", {
            content: `Sustained message ${messageCount}`,
            timestamp: Date.now()
          });

          // Apply sustained rate limiting
          if (i >= sustainedLimit) {
            rateLimitCount++;
            const sustainedLimitError = WebSocketUtils.error("SUSTAINED_RATE_LIMITED", 
              "Sustained rate limit exceeded");
            WebSocketUtils.send(mockWs as any, sustainedLimitError);
          } else {
            WebSocketUtils.send(mockWs as any, message);
          }
        }
      }, 1000);

      // Wait for sustained test to complete
      await new Promise(resolve => setTimeout(resolve, sustainedDuration + 500));

      // Verify sustained rate limiting was applied
      expect(rateLimitCount).toBeGreaterThan(0);
      expect(messageCount).toBeGreaterThan(sustainedLimit * 4); // At least 4 seconds worth
    });
  });

  describe("Rate Limit Recovery and Backoff", () => {
    test("should implement exponential backoff for repeated violations", async () => {
      const violations = [1, 2, 4, 8, 16]; // Exponential backoff seconds
      
      for (let i = 0; i < violations.length; i++) {
        // Simulate rate limit violation
        const backoffTime = violations[i];
        const rateLimitError = WebSocketUtils.rateLimited(backoffTime);
        WebSocketUtils.send(mockWs as any, rateLimitError);

        const lastMessage = mockWs.getLastMessage();
        expect(lastMessage.error.details?.retryAfter).toBe(backoffTime);
      }

      expect(mockWs.rateLimitHits).toBe(violations.length);
    });

    test("should allow gradual recovery after rate limit", async () => {
      // Hit rate limit
      const rateLimitError = WebSocketUtils.rateLimited(2);
      WebSocketUtils.send(mockWs as any, rateLimitError);
      
      expect(mockWs.rateLimitHits).toBe(1);

      // Wait for recovery period
      await new Promise(resolve => setTimeout(resolve, 2100));

      // Clear messages and try gradual recovery
      mockWs.clearMessages();

      // Should allow limited messages initially
      const recoveryMessage = WebSocketUtils.success("MESSAGE_SEND", {
        content: "Recovery test message"
      });
      WebSocketUtils.send(mockWs as any, recoveryMessage);

      expect(mockWs.sentMessages.length).toBe(1);
      expect(mockWs.rateLimitHits).toBe(0); // Reset after clearing
    });
  });

  describe("Rate Limiting Metrics and Monitoring", () => {
    test("should track rate limiting metrics", async () => {
      const testMetrics = {
        totalRequests: 0,
        rateLimitedRequests: 0,
        averageRequestsPerSecond: 0
      };

      // Simulate requests with some being rate limited
      for (let i = 0; i < 100; i++) {
        testMetrics.totalRequests++;
        
        const message = WebSocketUtils.success("MESSAGE_SEND", {
          content: `Metrics test ${i}`
        });

        // Simulate rate limiting every 10th message
        if (i % 10 === 9) {
          testMetrics.rateLimitedRequests++;
          const rateLimitError = WebSocketUtils.rateLimited(1);
          WebSocketUtils.send(mockWs as any, rateLimitError);
        } else {
          WebSocketUtils.send(mockWs as any, message);
        }
      }

      // Calculate metrics
      const rateLimitPercentage = (testMetrics.rateLimitedRequests / testMetrics.totalRequests) * 100;
      
      expect(testMetrics.totalRequests).toBe(100);
      expect(testMetrics.rateLimitedRequests).toBe(10);
      expect(rateLimitPercentage).toBe(10);
      expect(mockWs.rateLimitHits).toBe(10);
    });

    test("should provide rate limit status information", async () => {
      // Send rate limit status message
      const statusMessage = WebSocketUtils.success("RATE_LIMIT_STATUS", {
        userId: testUser.id,
        currentTokens: 45,
        maxTokens: 100,
        refillRate: 10, // tokens per minute
        nextRefill: Date.now() + 60000,
        isLimited: false
      });

      WebSocketUtils.send(mockWs as any, statusMessage);

      const lastMessage = mockWs.getLastMessage();
      expect(lastMessage.success).toBe(true);
      expect(lastMessage.data.currentTokens).toBe(45);
      expect(lastMessage.data.maxTokens).toBe(100);
      expect(lastMessage.data.isLimited).toBe(false);
    });
  });
});