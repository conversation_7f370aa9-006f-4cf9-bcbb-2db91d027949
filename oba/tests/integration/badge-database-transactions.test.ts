import { describe, test, expect, beforeEach, afterEach, mock } from "bun:test";
import { BadgeService } from "../../services/badge.service";
import { BadgeEvaluationService } from "../../services/badge-evaluation.service";
import {
  BadgeNotFoundError,
  BadgeAlreadyAssignedError,
  BadgeValidationError
} from "../../class/badge-errors";
import type {
  BadgeType,
  UserBadge,
  CreateBadgeTypeRequest,
  EvaluationResult
} from "../../types/badge.types";

// Mock database with transaction support
const mockTransaction = mock();
const mockDb = {
  select: mock(() => ({ 
    from: mock(() => ({ 
      where: mock(() => ({ 
        limit: mock(() => []),
        orderBy: mock(() => [])
      })) 
    })) 
  })),
  insert: mock(() => ({ 
    values: mock(() => ({ 
      returning: mock(() => []),
      onConflictDoNothing: mock(() => ({ returning: mock(() => []) }))
    })) 
  })),
  update: mock(() => ({ 
    set: mock(() => ({ 
      where: mock(() => ({ 
        returning: mock(() => []) 
      })) 
    })) 
  })),
  delete: mock(() => ({ 
    where: mock(() => ({ 
      returning: mock(() => []) 
    })) 
  })),
  transaction: mockTransaction
};

// Mock utility functions with transaction awareness
const mockBadgeUtils = {
  createBadgeType: mock(),
  getBadgeTypeById: mock(),
  getBadgeTypes: mock(),
  updateBadgeType: mock(),
  deleteBadgeType: mock(),
  assignBadgeToUser: mock(),
  removeBadgeFromUser: mock(),
  getUserBadges: mock(),
  getUserStats: mock(),
  getBadgeStats: mock(),
  getBadgeLeaderboard: mock(),
  getAvailableBadgesForUser: mock(),
  getBadgeProgress: mock(),
  bulkAssignBadges: mock()
};

const mockBadgeEvaluation = {
  evaluateUserForAutomaticBadges: mock(),
  batchEvaluateUsers: mock(),
  evaluateAllUsersForAutomaticBadges: mock(),
  reevaluateBadgeTypeForAllUsers: mock()
};

const mockBadgeWebSocket = {
  broadcastBadgeAssigned: mock(),
  broadcastBadgeRemoved: mock(),
  broadcastBatchBadgeAssigned: mock()
};

const mockPermissions = {
  hasServerPermission: mock()
};

// Mock the imports
mock.module("../../db/utils/badge-utils", () => mockBadgeUtils);
mock.module("../../db/utils/badge-evaluation", () => mockBadgeEvaluation);
mock.module("../../utils/badge-websocket", () => ({ badgeWebSocketService: mockBadgeWebSocket }));
mock.module("../../utils/permissions", () => mockPermissions);
mock.module("../../db", () => ({ db: mockDb }));

describe("Badge Database Transaction Tests", () => {
  let badgeService: BadgeService;
  let badgeEvaluationService: BadgeEvaluationService;
  
  const mockUserId = "550e8400-e29b-41d4-a716-446655440000";
  const mockBadgeTypeId = "550e8400-e29b-41d4-a716-446655440001";
  const mockAdminId = "550e8400-e29b-41d4-a716-446655440002";

  beforeEach(() => {
    badgeService = new BadgeService(mockDb as any);
    badgeEvaluationService = new BadgeEvaluationService(mockDb as any);
    
    // Reset all mocks
    Object.values(mockBadgeUtils).forEach(mock => mock.mockReset());
    Object.values(mockBadgeEvaluation).forEach(mock => mock.mockReset());
    Object.values(mockBadgeWebSocket).forEach(mock => mock.mockReset());
    Object.values(mockPermissions).forEach(mock => mock.mockReset());
    mockTransaction.mockReset();

    // Setup default transaction behavior
    mockTransaction.mockImplementation((callback) => callback(mockDb));
  });

  afterEach(() => {
    // Clean up any test state
  });

  describe("Badge Type Creation Transactions", () => {
    test("should create badge type within transaction", async () => {
      const badgeData: CreateBadgeTypeRequest = {
        badgeId: "test-badge",
        name: "Test Badge",
        description: "A test badge",
        icon: "🏆",
        design: {
          shape: "circle",
          background: "#FFD700",
          colors: ["#FFD700"]
        },
        criteria: {
          requirement: "Send 10 messages",
          tracked: "message_count",
          type: "message_count",
          threshold: 10
        },
        unlockType: "automatic",
        category: "achievement",
        displayOrder: 1
      };

      const expectedBadge: BadgeType = {
        id: mockBadgeTypeId,
        ...badgeData,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockBadgeUtils.getBadgeTypes.mockResolvedValue([]);
      mockBadgeUtils.createBadgeType.mockResolvedValue(expectedBadge);

      const result = await badgeService.createBadgeType(badgeData, mockAdminId);

      expect(result).toEqual(expectedBadge);
      expect(mockBadgeUtils.createBadgeType).toHaveBeenCalledWith(mockDb, expect.objectContaining(badgeData));
    });

    test("should rollback transaction on badge creation failure", async () => {
      const badgeData: CreateBadgeTypeRequest = {
        badgeId: "failing-badge",
        name: "Failing Badge",
        description: "This badge will fail to create",
        icon: "❌",
        design: {
          shape: "circle",
          background: "#FF0000",
          colors: ["#FF0000"]
        },
        criteria: {
          requirement: "Fail",
          tracked: "fail",
          type: "message_count",
          threshold: 1
        },
        unlockType: "manual",
        category: "achievement",
        displayOrder: 1
      };

      mockBadgeUtils.getBadgeTypes.mockResolvedValue([]);
      mockBadgeUtils.createBadgeType.mockRejectedValue(new Error("Database constraint violation"));

      // Transaction should be called but callback should throw
      mockTransaction.mockImplementation((callback) => {
        return callback(mockDb).catch((error: Error) => {
          // Simulate transaction rollback
          throw error;
        });
      });

      await expect(badgeService.createBadgeType(badgeData, mockAdminId))
        .rejects.toThrow(BadgeValidationError);

      expect(mockTransaction).toHaveBeenCalled();
    });

    test("should handle duplicate name check within transaction", async () => {
      const badgeData: CreateBadgeTypeRequest = {
        badgeId: "duplicate-badge",
        name: "Duplicate Badge",
        description: "This badge has a duplicate name",
        icon: "🔄",
        design: {
          shape: "circle",
          background: "#FFA500",
          colors: ["#FFA500"]
        },
        criteria: {
          requirement: "Test",
          tracked: "test",
          type: "message_count",
          threshold: 1
        },
        unlockType: "manual",
        category: "achievement",
        displayOrder: 1
      };

      const existingBadge: BadgeType = {
        id: "existing-badge-id",
        badgeId: "existing-badge",
        name: "Duplicate Badge",
        description: "Existing badge with same name",
        icon: "🏆",
        design: { shape: "circle", background: "#000", colors: ["#000"] },
        criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 1 },
        unlockType: "manual",
        category: "achievement",
        displayOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockBadgeUtils.getBadgeTypes.mockResolvedValue([existingBadge]);

      await expect(badgeService.createBadgeType(badgeData, mockAdminId))
        .rejects.toThrow(BadgeValidationError);

      // Transaction should not proceed to create the badge
      expect(mockBadgeUtils.createBadgeType).not.toHaveBeenCalled();
    });
  });

  describe("Badge Assignment Transactions", () => {
    const activeBadge: BadgeType = {
      id: mockBadgeTypeId,
      badgeId: "test-badge",
      name: "Test Badge",
      description: "A test badge",
      icon: "🏆",
      design: { shape: "circle", background: "#FFD700", colors: ["#FFD700"] },
      criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 10 },
      unlockType: "manual",
      category: "achievement",
      displayOrder: 1,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    test("should assign badge within transaction", async () => {
      const expectedUserBadge: UserBadge = {
        id: "user-badge-1",
        userId: mockUserId,
        badgeTypeId: mockBadgeTypeId,
        assignedBy: mockAdminId,
        assignedAt: new Date(),
        isVisible: true,
        badgeType: activeBadge
      };

      mockBadgeUtils.getBadgeTypeById.mockResolvedValue(activeBadge);
      mockBadgeUtils.getUserBadges.mockResolvedValue([]);
      mockBadgeUtils.assignBadgeToUser.mockResolvedValue(expectedUserBadge);
      mockPermissions.hasServerPermission.mockResolvedValue(true);
      mockBadgeWebSocket.broadcastBadgeAssigned.mockResolvedValue(undefined);

      const result = await badgeService.assignBadge(mockUserId, mockBadgeTypeId, mockAdminId);

      expect(result).toEqual(expectedUserBadge);
      expect(mockBadgeUtils.assignBadgeToUser).toHaveBeenCalledWith(mockDb, mockUserId, mockBadgeTypeId, mockAdminId);
    });

    test("should rollback transaction on duplicate badge assignment", async () => {
      const existingUserBadge: UserBadge = {
        id: "existing-user-badge",
        userId: mockUserId,
        badgeTypeId: mockBadgeTypeId,
        assignedAt: new Date(),
        isVisible: true
      };

      mockBadgeUtils.getBadgeTypeById.mockResolvedValue(activeBadge);
      mockBadgeUtils.getUserBadges.mockResolvedValue([existingUserBadge]);

      await expect(badgeService.assignBadge(mockUserId, mockBadgeTypeId, mockAdminId))
        .rejects.toThrow(BadgeAlreadyAssignedError);

      // Should not proceed to assign the badge
      expect(mockBadgeUtils.assignBadgeToUser).not.toHaveBeenCalled();
    });

    test("should handle transaction failure during badge assignment", async () => {
      mockBadgeUtils.getBadgeTypeById.mockResolvedValue(activeBadge);
      mockBadgeUtils.getUserBadges.mockResolvedValue([]);
      mockBadgeUtils.assignBadgeToUser.mockRejectedValue(new Error("Database constraint violation"));
      mockPermissions.hasServerPermission.mockResolvedValue(true);

      // Simulate transaction rollback
      mockTransaction.mockImplementation((callback) => {
        return callback(mockDb).catch((error: Error) => {
          throw error;
        });
      });

      await expect(badgeService.assignBadge(mockUserId, mockBadgeTypeId, mockAdminId))
        .rejects.toThrow(BadgeValidationError);
    });
  });

  describe("Bulk Badge Assignment Transactions", () => {
    test("should perform bulk assignments within single transaction", async () => {
      const assignments = [
        { userId: "user-1", badgeTypeId: "badge-1" },
        { userId: "user-2", badgeTypeId: "badge-1" },
        { userId: "user-3", badgeTypeId: "badge-2" }
      ];

      const expectedResults: UserBadge[] = [
        {
          id: "bulk-badge-1",
          userId: "user-1",
          badgeTypeId: "badge-1",
          assignedBy: mockAdminId,
          assignedAt: new Date(),
          isVisible: true
        },
        {
          id: "bulk-badge-2",
          userId: "user-2",
          badgeTypeId: "badge-1",
          assignedBy: mockAdminId,
          assignedAt: new Date(),
          isVisible: true
        }
      ];

      mockBadgeUtils.bulkAssignBadges.mockResolvedValue(expectedResults);
      mockPermissions.hasServerPermission.mockResolvedValue(true);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const result = await badgeService.bulkAssignBadges(assignments, mockAdminId);

      expect(result).toEqual(expectedResults);
      expect(mockBadgeUtils.bulkAssignBadges).toHaveBeenCalledWith(
        mockDb,
        assignments.map(a => ({ ...a, assignedBy: mockAdminId }))
      );
    });

    test("should rollback entire bulk operation on partial failure", async () => {
      const assignments = [
        { userId: "user-1", badgeTypeId: "badge-1" },
        { userId: "user-2", badgeTypeId: "invalid-badge" }, // This will fail
        { userId: "user-3", badgeTypeId: "badge-2" }
      ];

      mockBadgeUtils.bulkAssignBadges.mockRejectedValue(new Error("Invalid badge type"));
      mockPermissions.hasServerPermission.mockResolvedValue(true);

      // Simulate transaction rollback
      mockTransaction.mockImplementation((callback) => {
        return callback(mockDb).catch((error: Error) => {
          throw error;
        });
      });

      await expect(badgeService.bulkAssignBadges(assignments, mockAdminId))
        .rejects.toThrow(BadgeValidationError);

      // WebSocket broadcast should not be called if transaction fails
      expect(mockBadgeWebSocket.broadcastBatchBadgeAssigned).not.toHaveBeenCalled();
    });

    test("should handle partial success in bulk operations", async () => {
      const assignments = [
        { userId: "user-1", badgeTypeId: "badge-1" },
        { userId: "user-2", badgeTypeId: "badge-1" },
        { userId: "user-3", badgeTypeId: "badge-2" }
      ];

      // Only first two assignments succeed
      const partialResults: UserBadge[] = [
        {
          id: "bulk-badge-1",
          userId: "user-1",
          badgeTypeId: "badge-1",
          assignedBy: mockAdminId,
          assignedAt: new Date(),
          isVisible: true
        },
        {
          id: "bulk-badge-2",
          userId: "user-2",
          badgeTypeId: "badge-1",
          assignedBy: mockAdminId,
          assignedAt: new Date(),
          isVisible: true
        }
      ];

      mockBadgeUtils.bulkAssignBadges.mockResolvedValue(partialResults);
      mockPermissions.hasServerPermission.mockResolvedValue(true);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const result = await badgeService.bulkAssignBadges(assignments, mockAdminId);

      expect(result).toEqual(partialResults);
      expect(result.length).toBe(2); // Only successful assignments returned
    });
  });

  describe("Badge Evaluation Transactions", () => {
    test("should evaluate user badges within transaction", async () => {
      const mockResult: EvaluationResult = {
        userId: mockUserId,
        newBadges: [],
        evaluatedBadges: ["badge-1", "badge-2"],
        collectionProgress: [],
        errors: []
      };

      mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockResolvedValue(mockResult);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const result = await badgeEvaluationService.evaluateUser(mockUserId);

      expect(result).toEqual(mockResult);
      expect(mockBadgeEvaluation.evaluateUserForAutomaticBadges).toHaveBeenCalledWith(mockDb, mockUserId);
    });

    test("should handle transaction failure during batch evaluation", async () => {
      const userIds = ["user-1", "user-2", "user-3"];

      mockBadgeEvaluation.batchEvaluateUsers.mockRejectedValue(new Error("Database deadlock"));

      await expect(badgeEvaluationService.evaluateUsers(userIds))
        .rejects.toThrow("Failed to batch evaluate users");
    });

    test("should maintain data consistency during evaluation", async () => {
      const mockResult: EvaluationResult = {
        userId: mockUserId,
        newBadges: [
          {
            id: "new-badge-1",
            userId: mockUserId,
            badgeTypeId: "auto-badge-1",
            assignedAt: new Date(),
            isVisible: true
          }
        ],
        evaluatedBadges: ["auto-badge-1"],
        collectionProgress: [],
        errors: []
      };

      mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockResolvedValue(mockResult);
      mockBadgeWebSocket.broadcastBatchBadgeAssigned.mockResolvedValue(undefined);

      const result = await badgeEvaluationService.evaluateUser(mockUserId);

      expect(result.newBadges.length).toBe(1);
      expect(result.errors.length).toBe(0);

      // WebSocket broadcast should be called for new badges
      expect(mockBadgeWebSocket.broadcastBatchBadgeAssigned).toHaveBeenCalledWith([
        {
          userId: mockUserId,
          badge: mockResult.newBadges[0],
          isAutomatic: true,
          assignedBy: undefined
        }
      ]);
    });
  });

  describe("Badge Deletion Transactions", () => {
    test("should delete badge type and cascade user badges within transaction", async () => {
      const existingBadge: BadgeType = {
        id: mockBadgeTypeId,
        badgeId: "badge-to-delete",
        name: "Badge to Delete",
        description: "This badge will be deleted",
        icon: "🗑️",
        design: { shape: "circle", background: "#FF0000", colors: ["#FF0000"] },
        criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 1 },
        unlockType: "manual",
        category: "achievement",
        displayOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockBadgeUtils.getBadgeTypeById.mockResolvedValue(existingBadge);
      mockBadgeUtils.deleteBadgeType.mockResolvedValue(true);

      await badgeService.deleteBadgeType(mockBadgeTypeId, mockAdminId);

      expect(mockBadgeUtils.deleteBadgeType).toHaveBeenCalledWith(mockDb, mockBadgeTypeId);
    });

    test("should rollback transaction if badge deletion fails", async () => {
      const existingBadge: BadgeType = {
        id: mockBadgeTypeId,
        badgeId: "protected-badge",
        name: "Protected Badge",
        description: "This badge cannot be deleted",
        icon: "🔒",
        design: { shape: "circle", background: "#FFA500", colors: ["#FFA500"] },
        criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 1 },
        unlockType: "manual",
        category: "achievement",
        displayOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockBadgeUtils.getBadgeTypeById.mockResolvedValue(existingBadge);
      mockBadgeUtils.deleteBadgeType.mockRejectedValue(new Error("Foreign key constraint violation"));

      // Simulate transaction rollback
      mockTransaction.mockImplementation((callback) => {
        return callback(mockDb).catch((error: Error) => {
          throw error;
        });
      });

      await expect(badgeService.deleteBadgeType(mockBadgeTypeId, mockAdminId))
        .rejects.toThrow(BadgeValidationError);
    });
  });

  describe("Concurrent Transaction Handling", () => {
    test("should handle concurrent badge assignments gracefully", async () => {
      const activeBadge: BadgeType = {
        id: mockBadgeTypeId,
        badgeId: "concurrent-badge",
        name: "Concurrent Badge",
        description: "Badge for testing concurrent assignments",
        icon: "⚡",
        design: { shape: "circle", background: "#00FF00", colors: ["#00FF00"] },
        criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 1 },
        unlockType: "manual",
        category: "achievement",
        displayOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // First assignment succeeds
      mockBadgeUtils.getBadgeTypeById.mockResolvedValue(activeBadge);
      mockBadgeUtils.getUserBadges.mockResolvedValueOnce([]);
      mockBadgeUtils.assignBadgeToUser.mockResolvedValueOnce({
        id: "concurrent-badge-1",
        userId: mockUserId,
        badgeTypeId: mockBadgeTypeId,
        assignedBy: mockAdminId,
        assignedAt: new Date(),
        isVisible: true
      });
      mockPermissions.hasServerPermission.mockResolvedValue(true);
      mockBadgeWebSocket.broadcastBadgeAssigned.mockResolvedValue(undefined);

      // Second concurrent assignment should fail due to duplicate
      mockBadgeUtils.getUserBadges.mockResolvedValueOnce([{
        id: "existing-badge",
        userId: mockUserId,
        badgeTypeId: mockBadgeTypeId,
        assignedAt: new Date(),
        isVisible: true
      }]);

      // First assignment should succeed
      const result1 = await badgeService.assignBadge(mockUserId, mockBadgeTypeId, mockAdminId);
      expect(result1).toBeDefined();

      // Second assignment should fail
      await expect(badgeService.assignBadge(mockUserId, mockBadgeTypeId, mockAdminId))
        .rejects.toThrow(BadgeAlreadyAssignedError);
    });

    test("should handle database deadlocks during transactions", async () => {
      const badgeData: CreateBadgeTypeRequest = {
        badgeId: "deadlock-badge",
        name: "Deadlock Badge",
        description: "Badge that causes deadlock",
        icon: "🔄",
        design: { shape: "circle", background: "#FF00FF", colors: ["#FF00FF"] },
        criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 1 },
        unlockType: "manual",
        category: "achievement",
        displayOrder: 1
      };

      mockBadgeUtils.getBadgeTypes.mockResolvedValue([]);
      mockBadgeUtils.createBadgeType.mockRejectedValue(new Error("deadlock detected"));

      // Simulate transaction retry logic
      mockTransaction.mockImplementation((callback) => {
        return callback(mockDb).catch((error: Error) => {
          if (error.message.includes("deadlock")) {
            // In a real implementation, you might retry the transaction
            throw new BadgeValidationError("Transaction failed due to deadlock");
          }
          throw error;
        });
      });

      await expect(badgeService.createBadgeType(badgeData, mockAdminId))
        .rejects.toThrow(BadgeValidationError);
    });
  });

  describe("Transaction Isolation Levels", () => {
    test("should maintain read consistency during badge evaluation", async () => {
      const mockResult: EvaluationResult = {
        userId: mockUserId,
        newBadges: [],
        evaluatedBadges: [],
        collectionProgress: [],
        errors: []
      };

      // Simulate consistent read during evaluation
      mockBadgeEvaluation.evaluateUserForAutomaticBadges.mockImplementation(async (db, userId) => {
        // Simulate reading user stats and badge types within transaction
        return mockResult;
      });

      const result = await badgeEvaluationService.evaluateUser(mockUserId);

      expect(result).toEqual(mockResult);
      expect(mockBadgeEvaluation.evaluateUserForAutomaticBadges).toHaveBeenCalledWith(mockDb, mockUserId);
    });

    test("should prevent phantom reads during badge queries", async () => {
      const initialBadges: BadgeType[] = [
        {
          id: "badge-1",
          badgeId: "initial-badge",
          name: "Initial Badge",
          description: "Initially available badge",
          icon: "1️⃣",
          design: { shape: "circle", background: "#0000FF", colors: ["#0000FF"] },
          criteria: { requirement: "Test", tracked: "test", type: "message_count", threshold: 1 },
          unlockType: "automatic",
          category: "achievement",
          displayOrder: 1,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      mockBadgeUtils.getBadgeTypes.mockResolvedValue(initialBadges);

      const result = await badgeService.getBadgeTypes({ unlockType: "automatic" });

      expect(result).toEqual(initialBadges);
      expect(result.length).toBe(1);
    });
  });
});