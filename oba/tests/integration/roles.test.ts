import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  afterAll,
} from "bun:test";
import { db } from "../setup";
import {
  createTestUser,
  cleanupTestData,
  createMockRequest,
  parseResponse<PERSON>son,
  createTestServerWithRoles,
  assignRoleToUser,
} from "../helpers";
import { ServerRoleSchema, UserRoles } from "../../db/schema";
import { eq, and } from "drizzle-orm";

import {
  createR<PERSON><PERSON>and<PERSON>,
  update<PERSON><PERSON><PERSON>andler,
  deleteRoleHandler,
  getServer<PERSON><PERSON>s<PERSON>andler,
  getUser<PERSON><PERSON>sHandler,
  assignR<PERSON>Handler,
  remove<PERSON><PERSON><PERSON>and<PERSON>,
} from "../../handlers/roles";

// Global cleanup after all tests
afterAll(async () => {
  await cleanupTestData();
});

describe("Role Management API Endpoints", () => {
  // Clean up before each test
  beforeEach(async () => {
    await cleanupTestData();
  });

  // Clean up after each test
  afterEach(async () => {
    await cleanupTestData();
  });

  // Create Role Tests
  test("create<PERSON><PERSON><PERSON><PERSON><PERSON> should create a new role", async () => {
    // Create a test user and server with roles
    const user = await createTestUser("roleuser", "<EMAIL>");
    const { server } = await createTestServerWithRoles(user.id);

    // Create a mock request
    const req = createMockRequest({
      serverId: server.id,
      name: "Custom Role",
      permissions: ["SEND_MESSAGES", "VIEW_CHANNEL"],
      userId: user.id, // Server owner has permission to create roles
    });

    // Call the handler
    const response = await createRoleHandler(req);

    // Check the response
    expect(response.status).toBe(201);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.role).toBeDefined();
    expect(data.role.name).toBe("Custom Role");
    expect(data.role.serverId).toBe(server.id);
    expect(data.role.permissions).toContain("SEND_MESSAGES");
    expect(data.role.permissions).toContain("VIEW_CHANNEL");

    // Verify in the database
    const dbRole = await db
      .select()
      .from(ServerRoleSchema)
      .where(eq(ServerRoleSchema.id, data.role.id))
      .limit(1);

    expect(dbRole.length).toBe(1);
    expect(dbRole[0].name).toBe("Custom Role");
  });

  test("createRoleHandler should reject request without permission", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Create a mock request
    const req = createMockRequest({
      serverId: server.id,
      name: "Custom Role",
      permissions: ["SEND_MESSAGES", "VIEW_CHANNEL"],
      userId: member.id, // Member doesn't have permission to create roles
    });

    // Call the handler
    const response = await createRoleHandler(req);

    // Check the response
    expect(response.status).toBe(403);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.error).toBe("You do not have permission to manage roles");
  });

  // Update Role Tests
  test("updateRoleHandler should update an existing role", async () => {
    // Create a test user and server with roles
    const user = await createTestUser("roleuser", "<EMAIL>");
    const { server, roles } = await createTestServerWithRoles(user.id);

    // Create a mock request
    const req = createMockRequest({
      roleId: roles.member.id,
      serverId: server.id,
      name: "Updated Member",
      permissions: ["SEND_MESSAGES", "VIEW_CHANNEL", "MANAGE_CHANNELS"],
      userId: user.id, // Server owner has permission to update roles
    });

    // Call the handler
    const response = await updateRoleHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.role).toBeDefined();
    expect(data.role.name).toBe("Updated Member");
    expect(data.role.permissions).toContain("SEND_MESSAGES");
    expect(data.role.permissions).toContain("VIEW_CHANNEL");
    expect(data.role.permissions).toContain("MANAGE_CHANNELS");

    // Verify in the database
    const dbRole = await db
      .select()
      .from(ServerRoleSchema)
      .where(eq(ServerRoleSchema.id, roles.member.id))
      .limit(1);

    expect(dbRole.length).toBe(1);
    expect(dbRole[0].name).toBe("Updated Member");
  });

  test("updateRoleHandler should reject request without permission", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Create a mock request
    const req = createMockRequest({
      roleId: roles.member.id,
      serverId: server.id,
      name: "Updated Member",
      permissions: ["SEND_MESSAGES", "VIEW_CHANNEL", "MANAGE_CHANNELS"],
      userId: member.id, // Member doesn't have permission to update roles
    });

    // Call the handler
    const response = await updateRoleHandler(req);

    // Check the response
    expect(response.status).toBe(403);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.error).toBe("You do not have permission to manage roles");
  });

  // Delete Role Tests
  test("deleteRoleHandler should delete a role", async () => {
    // Create a test user and server with roles
    const user = await createTestUser("roleuser", "<EMAIL>");
    const { server, roles } = await createTestServerWithRoles(user.id);

    // Create a mock request
    const req = createMockRequest({
      roleId: roles.member.id,
      serverId: server.id,
      userId: user.id, // Server owner has permission to delete roles
    });

    // Call the handler
    const response = await deleteRoleHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.roleId).toBe(roles.member.id);

    // Verify in the database
    const dbRole = await db
      .select()
      .from(ServerRoleSchema)
      .where(eq(ServerRoleSchema.id, roles.member.id));

    expect(dbRole.length).toBe(0);
  });

  test("deleteRoleHandler should reject request without permission", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Create a mock request
    const req = createMockRequest({
      roleId: roles.member.id,
      serverId: server.id,
      userId: member.id, // Member doesn't have permission to delete roles
    });

    // Call the handler
    const response = await deleteRoleHandler(req);

    // Check the response
    expect(response.status).toBe(403);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.error).toBe("You do not have permission to manage roles");
  });

  // Get Server Roles Tests
  test("getServerRolesHandler should return all roles for a server", async () => {
    // Create a test user and server with roles
    const user = await createTestUser("roleuser", "<EMAIL>");
    const { server } = await createTestServerWithRoles(user.id);

    // Create a mock request
    const req = new Request(
      `http://localhost:3000/api/roles/server?serverId=${server.id}&userId=${user.id}`,
    );

    // Call the handler
    const response = await getServerRolesHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.roles).toBeDefined();
    expect(data.roles.length).toBe(3); // Admin, Moderator, Member

    // Check role names
    const roleNames = data.roles.map((role: any) => role.name);
    expect(roleNames).toContain("Admin");
    expect(roleNames).toContain("Moderator");
    expect(roleNames).toContain("Member");
  });

  test("getServerRolesHandler should reject request without permission", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Create a mock request
    const req = new Request(
      `http://localhost:3000/api/roles/server?serverId=${server.id}&userId=${member.id}`,
    );

    // Call the handler
    const response = await getServerRolesHandler(req);

    // Check the response
    expect(response.status).toBe(403);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.error).toBe("You do not have permission to view all roles");
  });

  // Get User Roles Tests
  test("getUserRolesHandler should return roles for a user", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Create a mock request for the owner to view the member's roles
    const req = new Request(
      `http://localhost:3000/api/roles/user?userId=${member.id}&serverId=${server.id}&requesterId=${owner.id}`,
    );

    // Call the handler
    const response = await getUserRolesHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.roles).toBeDefined();
    expect(data.roles.length).toBe(1);
    expect(data.roles[0].name).toBe("Member");
  });

  test("getUserRolesHandler should allow users to view their own roles", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Create a mock request for the member to view their own roles
    const req = new Request(
      `http://localhost:3000/api/roles/user?userId=${member.id}&serverId=${server.id}&requesterId=${member.id}`,
    );

    // Call the handler
    const response = await getUserRolesHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.roles).toBeDefined();
    expect(data.roles.length).toBe(1);
    expect(data.roles[0].name).toBe("Member");
  });

  test("getUserRolesHandler should reject request to view another user's roles without permission", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member1 = await createTestUser("member1", "<EMAIL>");
    const member2 = await createTestUser("member2", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member roles
    await assignRoleToUser(member1.id, roles.member.id, server.id);
    await assignRoleToUser(member2.id, roles.member.id, server.id);

    // Create a mock request for member1 to view member2's roles
    const req = new Request(
      `http://localhost:3000/api/roles/user?userId=${member2.id}&serverId=${server.id}&requesterId=${member1.id}`,
    );

    // Call the handler
    const response = await getUserRolesHandler(req);

    // Check the response
    expect(response.status).toBe(403);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.error).toBe(
      "You do not have permission to view this user's roles",
    );
  });

  // Assign Role Tests
  test("assignRoleHandler should assign a role to a user", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Create a mock request
    const req = createMockRequest({
      userId: member.id,
      roleId: roles.moderator.id,
      serverId: server.id,
      assignerId: owner.id, // Server owner has permission to assign roles
    });

    // Call the handler
    const response = await assignRoleHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.userId).toBe(member.id);
    expect(data.roleId).toBe(roles.moderator.id);

    // Verify in the database
    const userRoles = await db
      .select()
      .from(UserRoles)
      .where(
        and(
          eq(UserRoles.userId, member.id),
          eq(UserRoles.roleId, roles.moderator.id),
          eq(UserRoles.serverId, server.id),
        ),
      );

    expect(userRoles.length).toBe(1);
  });

  test("assignRoleHandler should reject request without permission", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member1 = await createTestUser("member1", "<EMAIL>");
    const member2 = await createTestUser("member2", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member roles
    await assignRoleToUser(member1.id, roles.member.id, server.id);

    // Create a mock request
    const req = createMockRequest({
      userId: member2.id,
      roleId: roles.moderator.id,
      serverId: server.id,
      assignerId: member1.id, // Member doesn't have permission to assign roles
    });

    // Call the handler
    const response = await assignRoleHandler(req);

    // Check the response
    expect(response.status).toBe(403);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.error).toBe("You do not have permission to assign roles");
  });

  // Remove Role Tests
  test("removeRoleHandler should remove a role from a user", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member = await createTestUser("member", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member role to the member user
    await assignRoleToUser(member.id, roles.member.id, server.id);

    // Create a mock request
    const req = createMockRequest({
      userId: member.id,
      roleId: roles.member.id,
      serverId: server.id,
      removerId: owner.id, // Server owner has permission to remove roles
    });

    // Call the handler
    const response = await removeRoleHandler(req);

    // Check the response
    expect(response.status).toBe(200);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.success).toBe(true);
    expect(data.userId).toBe(member.id);
    expect(data.roleId).toBe(roles.member.id);

    // Verify in the database
    const userRoles = await db
      .select()
      .from(UserRoles)
      .where(
        and(
          eq(UserRoles.userId, member.id),
          eq(UserRoles.roleId, roles.member.id),
          eq(UserRoles.serverId, server.id),
        ),
      );

    expect(userRoles.length).toBe(0);
  });

  test("removeRoleHandler should reject request without permission", async () => {
    // Create users
    const owner = await createTestUser("owner", "<EMAIL>");
    const member1 = await createTestUser("member1", "<EMAIL>");
    const member2 = await createTestUser("member2", "<EMAIL>");

    // Create server with roles
    const { server, roles } = await createTestServerWithRoles(owner.id);

    // Assign member roles
    await assignRoleToUser(member1.id, roles.member.id, server.id);
    await assignRoleToUser(member2.id, roles.member.id, server.id);

    // Create a mock request
    const req = createMockRequest({
      userId: member2.id,
      roleId: roles.member.id,
      serverId: server.id,
      removerId: member1.id, // Member doesn't have permission to remove roles
    });

    // Call the handler
    const response = await removeRoleHandler(req);

    // Check the response
    expect(response.status).toBe(403);

    // Parse the response JSON
    const data = await parseResponseJson(response);

    // Check the response data
    expect(data.error).toBe("You do not have permission to remove roles");
  });
});
