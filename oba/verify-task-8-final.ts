#!/usr/bin/env bun

/**
 * Task 8 Implementation Verification
 * 
 * This script verifies that badges have been successfully integrated into:
 * 1. User profile API responses (auth handlers)
 * 2. WebSocket user presence events
 * 3. Badge response utilities are working
 */

import { readFile } from "fs/promises";

async function verifyImplementation() {
  console.log("🔍 Verifying Task 8: Badge Integration Implementation");
  console.log("=" .repeat(60));

  let verificationsPassed = 0;
  let totalVerifications = 0;

  // 1. Verify auth.ts includes badge imports and usage
  console.log("\n📋 1. Checking auth.ts for badge integration...");
  totalVerifications++;
  try {
    const authContent = await readFile("handlers/auth.ts", "utf-8");
    
    const checks = {
      importsBadgeUtils: authContent.includes("getUserBadgesForResponse"),
      loginIncludesBadges: authContent.includes("badges: userBadges") && authContent.includes("loginHandler"),
      registerIncludesBadges: authContent.includes("badges: userBadges") && authContent.includes("registerHandler"),
      profileUpdateIncludesBadges: authContent.includes("badges: userBadges") && authContent.includes("updateUserProfileHandler"),
      getUserDetailsIncludesBadges: authContent.includes("badges: userBadges") && authContent.includes("getUserDetailsHandler")
    };

    console.log("   ✓ Badge utility import:", checks.importsBadgeUtils ? "✅" : "❌");
    console.log("   ✓ Login handler includes badges:", checks.loginIncludesBadges ? "✅" : "❌");
    console.log("   ✓ Register handler includes badges:", checks.registerIncludesBadges ? "✅" : "❌");
    console.log("   ✓ Profile update includes badges:", checks.profileUpdateIncludesBadges ? "✅" : "❌");
    console.log("   ✓ Get user details includes badges:", checks.getUserDetailsIncludesBadges ? "✅" : "❌");

    if (Object.values(checks).every(Boolean)) {
      console.log("✅ Auth handler badge integration: VERIFIED");
      verificationsPassed++;
    } else {
      console.log("❌ Auth handler badge integration: INCOMPLETE");
    }
  } catch (error) {
    console.log("❌ Error checking auth.ts:", error.message);
  }

  // 2. Verify WebSocket manager includes badge integration
  console.log("\n📡 2. Checking WebSocket manager for badge integration...");
  totalVerifications++;
  try {
    const wsContent = await readFile("manager/websocket.manager.ts", "utf-8");
    
    const checks = {
      importsBadgeUtils: wsContent.includes("getUserBadgesForWebSocket"),
      statusUpdateIncludesBadges: wsContent.includes("badges: userBadges") && wsContent.includes("handleUserStatusUpdate"),
      connectIncludesBadges: wsContent.includes("badges: userBadges") && wsContent.includes("handleUserConnect"),
      disconnectIncludesBadges: wsContent.includes("badges: userBadges") && wsContent.includes("handleUserDisconnect")
    };

    console.log("   ✓ Badge utility import:", checks.importsBadgeUtils ? "✅" : "❌");
    console.log("   ✓ Status update includes badges:", checks.statusUpdateIncludesBadges ? "✅" : "❌");
    console.log("   ✓ User connect includes badges:", checks.connectIncludesBadges ? "✅" : "❌");
    console.log("   ✓ User disconnect includes badges:", checks.disconnectIncludesBadges ? "✅" : "❌");

    if (Object.values(checks).every(Boolean)) {
      console.log("✅ WebSocket badge integration: VERIFIED");
      verificationsPassed++;
    } else {
      console.log("❌ WebSocket badge integration: INCOMPLETE");
    }
  } catch (error) {
    console.log("❌ Error checking websocket.manager.ts:", error.message);
  }

  // 3. Verify badge response utilities exist and are properly structured
  console.log("\n🛠️  3. Checking badge response utilities...");
  totalVerifications++;
  try {
    const badgeUtilsContent = await readFile("utils/badge-response-utils.ts", "utf-8");
    
    const checks = {
      hasFormattedBadgeData: badgeUtilsContent.includes("FormattedBadgeData"),
      hasBadgeSummary: badgeUtilsContent.includes("BadgeSummary"),
      hasGetUserBadgesForResponse: badgeUtilsContent.includes("getUserBadgesForResponse"),
      hasGetUserBadgesForWebSocket: badgeUtilsContent.includes("getUserBadgesForWebSocket"),
      hasDisplayLimit: badgeUtilsContent.includes("MAX_VISIBLE_BADGES"),
      hasFormatting: badgeUtilsContent.includes("formatBadgeForResponse")
    };

    console.log("   ✓ FormattedBadgeData interface:", checks.hasFormattedBadgeData ? "✅" : "❌");
    console.log("   ✓ BadgeSummary interface:", checks.hasBadgeSummary ? "✅" : "❌");
    console.log("   ✓ getUserBadgesForResponse function:", checks.hasGetUserBadgesForResponse ? "✅" : "❌");
    console.log("   ✓ getUserBadgesForWebSocket function:", checks.hasGetUserBadgesForWebSocket ? "✅" : "❌");
    console.log("   ✓ Display limit configuration:", checks.hasDisplayLimit ? "✅" : "❌");
    console.log("   ✓ Badge formatting functions:", checks.hasFormatting ? "✅" : "❌");

    if (Object.values(checks).every(Boolean)) {
      console.log("✅ Badge response utilities: VERIFIED");
      verificationsPassed++;
    } else {
      console.log("❌ Badge response utilities: INCOMPLETE");
    }
  } catch (error) {
    console.log("❌ Error checking badge-response-utils.ts:", error.message);
  }

  // 4. Check that badge display limit is properly configured
  console.log("\n🔢 4. Checking badge display limit configuration...");
  totalVerifications++;
  try {
    const badgeUtilsContent = await readFile("utils/badge-response-utils.ts", "utf-8");
    
    const checks = {
      hasMaxVisibleBadges: badgeUtilsContent.includes("MAX_VISIBLE_BADGES: 5"),
      hasDisplayLogic: badgeUtilsContent.includes("slice(0, BADGE_DISPLAY_CONFIG.MAX_VISIBLE_BADGES)"),
      hasMoreFlag: badgeUtilsContent.includes("hasMore: visibleBadges.length > BADGE_DISPLAY_CONFIG.MAX_VISIBLE_BADGES")
    };

    console.log("   ✓ Max visible badges set to 5:", checks.hasMaxVisibleBadges ? "✅" : "❌");
    console.log("   ✓ Display limit logic:", checks.hasDisplayLogic ? "✅" : "❌");
    console.log("   ✓ 'Has more' flag logic:", checks.hasMoreFlag ? "✅" : "❌");

    if (Object.values(checks).every(Boolean)) {
      console.log("✅ Badge display limit: VERIFIED");
      verificationsPassed++;
    } else {
      console.log("❌ Badge display limit: INCOMPLETE");
    }
  } catch (error) {
    console.log("❌ Error checking badge display limit:", error.message);
  }

  // 5. Verify consistent badge data formatting
  console.log("\n📝 5. Checking badge data formatting consistency...");
  totalVerifications++;
  try {
    const badgeUtilsContent = await readFile("utils/badge-response-utils.ts", "utf-8");
    
    const checks = {
      hasFullResponseFormat: badgeUtilsContent.includes("id: string") && 
                            badgeUtilsContent.includes("name: string") && 
                            badgeUtilsContent.includes("description: string") &&
                            badgeUtilsContent.includes("category: string") &&
                            badgeUtilsContent.includes("assignedAt: Date"),
      hasLightweightFormat: badgeUtilsContent.includes("LightweightBadgeData"),
      hasColorHandling: badgeUtilsContent.includes("DEFAULT_BADGE_COLOR"),
      hasSorting: badgeUtilsContent.includes("sort") && badgeUtilsContent.includes("assignedAt")
    };

    console.log("   ✓ Full response format:", checks.hasFullResponseFormat ? "✅" : "❌");
    console.log("   ✓ Lightweight WebSocket format:", checks.hasLightweightFormat ? "✅" : "❌");
    console.log("   ✓ Color handling:", checks.hasColorHandling ? "✅" : "❌");
    console.log("   ✓ Badge sorting by date:", checks.hasSorting ? "✅" : "❌");

    if (Object.values(checks).every(Boolean)) {
      console.log("✅ Badge data formatting: VERIFIED");
      verificationsPassed++;
    } else {
      console.log("❌ Badge data formatting: INCOMPLETE");
    }
  } catch (error) {
    console.log("❌ Error checking badge formatting:", error.message);
  }

  // Summary
  console.log("\n" + "=".repeat(60));
  console.log(`📊 Implementation Verification: ${verificationsPassed}/${totalVerifications} checks passed`);
  
  if (verificationsPassed === totalVerifications) {
    console.log("\n🎉 SUCCESS: Task 8 implementation is complete!");
    console.log("\n✅ Verified implementations:");
    console.log("   • User profile API responses include badge information");
    console.log("   • User authentication responses include badge data");
    console.log("   • WebSocket user presence events include badge information");
    console.log("   • Badge data formatting is consistent across API responses");
    console.log("   • Badge display limit logic (show first 5 badges with total count)");
    console.log("\n🔧 Integration points:");
    console.log("   • loginHandler - includes user badges in response");
    console.log("   • registerHandler - includes user badges in response");
    console.log("   • updateUserProfileHandler - includes user badges in response");
    console.log("   • getUserDetailsHandler - includes user badges in response");
    console.log("   • WebSocket status events - include badge information");
    console.log("   • Badge response utilities - handle formatting and limits");
    
    return true;
  } else {
    console.log(`\n❌ INCOMPLETE: ${totalVerifications - verificationsPassed} checks failed`);
    console.log("Please review the implementation to ensure all requirements are met.");
    return false;
  }
}

// Main execution
async function main() {
  try {
    const success = await verifyImplementation();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error("💥 Verification failed:", error);
    process.exit(1);
  }
}

if (import.meta.main) {
  main();
}