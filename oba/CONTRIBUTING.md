# Contributing to <PERSON><PERSON> Backend

First off, thank you for considering contributing to <PERSON><PERSON>! It's people like you that make open source projects such a great experience. Every contribution is appreciated, from reporting a bug to implementing a new feature.

This document provides a comprehensive guide for developers who want to contribute to the project. Please read it carefully to ensure a smooth and effective development process.

## Table of Contents

- [Project Overview](#project-overview)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
  - [Environment Configuration](#environment-configuration)
  - [Running the Application](#running-the-application)
  - [Running Tests](#running-tests)
- [Project Structure](#project-structure)
- [Architecture](#architecture)
  - [Routing](#routing)
  - [Database](#database)
  - [Real-time Communication](#real-time-communication)
- [Development Workflow](#development-workflow)
  - [Adding a New Feature](#adding-a-new-feature)
  - [Coding Style](#coding-style)
  - [Branching Strategy](#branching-strategy)
- [Database Migrations](#database-migrations)
- [Key Areas for Contribution](#key-areas-for-contribution)

## Project Overview

Oba is the backend service for a real-time communication platform, similar to Discord or Slack. It provides a RESTful API for core functionalities and uses WebSockets for real-time features like messaging and user status updates. The project is built with Bun, TypeScript, and Drizzle ORM.

## Getting Started

### Prerequisites

- [Bun](https://bun.sh/) (v1.1.0 or higher)
- [Docker](https://www.docker.com/) and [Docker Compose](https://docs.docker.com/compose/)
- A PostgreSQL database

### Installation

1.  **Clone the repository:**

    ```bash
    git clone https://github.com/your-username/oba-backend.git
    cd oba-backend
    ```

2.  **Install dependencies:**

    ```bash
    bun install
    ```

### Environment Configuration

The project uses `.env` files for environment-specific configurations. You'll need to create a `.env.dev` file for local development.

1.  **Create a `.env.dev` file:**

    ```bash
    cp .env.example .env.dev
    ```

    _Note: An `.env.example` file should be created to show the required environment variables._

2.  **Update the variables** in `.env.dev` with your local database credentials and other settings.

### Running the Application

- **For local development:**

  ```bash
  bun run dev
  ```

  This will start the server in watch mode, so it will automatically restart when you make changes to the code.

- **Using Docker:**

  ```bash
  docker-compose up --build
  ```

### Running Tests

The project has both unit and integration tests. You can run them using the following commands:

- **Run all tests:**

  ```bash
  bun run test
  ```

- **Run unit tests:**

  ```bash
  bun run test:unit
  ```

- **Run integration tests:**

  ```bash
  bun run test:integration
  ```

## Project Structure

- `class/`: Core classes like `Router` and `RouteHandler`.
- `constants/`: Application-wide constants.
- `db/`: Database schema, connection, and utilities.
- `drizzle/`: Database migration files.
- `handlers/`: Business logic for API endpoints.
- `manager/`: WebSocket connection management.
- `middlewares/`: Middleware functions (auth, logging, etc.).
- `services/`: External services (logging, WebRTC, etc.).
- `tests/`: Unit and integration tests.
- `types/`: TypeScript type definitions.
- `utils/`: Utility functions.

## Architecture

### Routing

Routing is handled by the custom `Router` class in `class/router.ts`. Routes are defined in `index.ts` and associated with a `RouteHandler` (`PublicRouteHandler` or `ProtectedRouteHandler`).

- `PublicRouteHandler`: For routes that don't require authentication.
- `ProtectedRouteHandler`: For routes that require authentication. It automatically applies the `authMiddleware`.

### Database

The project uses [Drizzle ORM](https://orm.drizzle.team/) to interact with the PostgreSQL database. The database schema is defined in `db/schema.ts`. All database queries should be placed in the `db/utils/` directory.

### Real-time Communication

Real-time features are implemented using WebSockets. The `WebSocketManager` in `manager/websocket.manager.ts` handles WebSocket connections, messages, and state.

## Development Workflow

### Adding a New Feature

1.  **Create a new handler function** in the appropriate file in the `handlers/` directory.
2.  **Add a new route** in `index.ts` that points to your new handler.
3.  **Write unit and integration tests** for the new feature.
4.  **Update the documentation** if necessary.

### Coding Style

The project uses ESLint for linting and Prettier for code formatting. Please run the following commands before committing your changes:

```bash
bun run lint
bun run format
```

### Branching Strategy

- `main`: This branch is for the latest stable release.
- `develop`: This is the main development branch. All new features should be branched off of `develop`.
- **Feature branches:** Create a new branch for each new feature (e.g., `feature/add-user-profiles`).

## Database Migrations

When you make changes to the database schema in `db/schema.ts`, you need to create a new migration file.

1.  **Generate the migration:**

    ```bash
    bun run drizzle-kit generate
    ```

2.  **Apply the migration:**

    ```bash
    bun run drizzle-kit migrate
    ```

## Key Areas for Contribution

We are actively looking for contributions in the following areas:

- **Security:** Implementing authentication for all sensitive endpoints.
- **Refactoring:** Breaking down the `index.ts` file into smaller, more manageable modules.
- **API Documentation:** Creating comprehensive API documentation using a tool like Swagger or OpenAPI.
- **CI/CD:** Setting up a CI/CD pipeline to automate testing and deployment.
- **New Features:** Implementing new features from our roadmap.

Thank you for your interest in contributing to Oba! We look forward to your pull requests.
