/**
 * Simple test script for badge collections functionality
 * This tests the core collection features without complex database setup
 */

import { db } from "./db";
import { BadgeCollectionService } from "./services/badge-collection.service";
import { BadgeService } from "./services/badge.service";
import type {
  CreateBadgeCollectionRequest,
  CreateBadgeTypeRequest
} from "./types/badge.types";

async function testBadgeCollections() {
  console.log("🧪 Testing Badge Collections System...\n");

  const collectionService = new BadgeCollectionService(db);
  const badgeService = new BadgeService(db);

  // Generate a valid UUID for test user
  const testUserId = "01983871-62aa-7592-9a84-6fa545dcfb34"; // Valid UUID format

  try {
    // Test 1: Create a progressive collection
    console.log("1️⃣ Creating progressive collection...");
    const timestamp = Date.now();
    const collectionData: CreateBadgeCollectionRequest = {
      collectionId: `test-progressive-journey-${timestamp}`,
      name: "Progressive Journey",
      description: "A journey through progressive badge unlocking",
      type: "progressive",
      unlockedBy: "activity_and_time",
      completionReward: {
        badge: "journey-master",
        title: "Journey Master",
        perks: ["special_role", "exclusive_access"],
        visual: "golden_trophy",
        animation: "victory_sparkle"
      }
    };

    const collection = await collectionService.createCollection(collectionData, "test-admin");
    console.log("✅ Collection created:", collection.name);

    // Test 2: Create badges in the collection
    console.log("\n2️⃣ Creating badges in collection...");
    const badgeData: CreateBadgeTypeRequest[] = [
      {
        collectionId: collection.id,
        badgeId: "first-step",
        name: `First Step ${timestamp}`,
        description: "Take your first step",
        icon: "👶",
        design: {
          shape: "circle",
          background: "#FFD700",
          colors: ["#FFD700", "#FFA500"]
        },
        criteria: {
          requirement: "Complete first action",
          tracked: "manual",
          type: "custom",
          conditions: { manual: true }
        },
        unlockType: "manual",
        displayOrder: 0,
        category: "milestone"
      },
      {
        collectionId: collection.id,
        badgeId: "getting-started",
        name: `Getting Started ${timestamp}`,
        description: "You're making progress",
        icon: "🚀",
        design: {
          shape: "hexagon",
          background: "#4CAF50",
          colors: ["#4CAF50", "#8BC34A"]
        },
        criteria: {
          requirement: "Complete second action",
          tracked: "manual",
          type: "custom",
          conditions: { manual: true }
        },
        unlockType: "manual",
        displayOrder: 1,
        category: "milestone"
      },
      {
        collectionId: collection.id,
        badgeId: "experienced",
        name: `Experienced ${timestamp}`,
        description: "You're becoming an expert",
        icon: "⭐",
        design: {
          shape: "star",
          background: "#2196F3",
          colors: ["#2196F3", "#03DAC6"]
        },
        criteria: {
          requirement: "Complete advanced action",
          tracked: "manual",
          type: "custom",
          conditions: { manual: true }
        },
        unlockType: "manual",
        displayOrder: 2,
        category: "milestone"
      }
    ];

    const badges = [];
    for (const data of badgeData) {
      const badge = await badgeService.createBadgeType(data, "test-admin");
      badges.push(badge);
      console.log(`✅ Badge created: ${badge.name} (Order: ${badge.displayOrder})`);
      console.log(`   Collection ID: ${badge.collectionId}`);
    }

    // Update collection badge count
    await collectionService.updateCollectionBadgeCount(collection.collectionId);
    console.log("✅ Collection badge count updated");
    
    // Check updated collection
    const updatedCollection = await collectionService.getCollectionById(collection.collectionId);
    console.log(`✅ Collection now has ${updatedCollection.totalBadges} badges`);

    // Test 3: Test sequential unlocking
    console.log("\n3️⃣ Testing sequential badge unlocking...");
    
    // Check if first badge can be unlocked
    const canUnlockFirst = await collectionService.canUnlockNextBadge(
      testUserId,
      collection.collectionId,
      0
    );
    console.log(`✅ Can unlock first badge: ${canUnlockFirst}`);

    // Check if second badge can be unlocked (should be false)
    const canUnlockSecond = await collectionService.canUnlockNextBadge(
      testUserId,
      collection.collectionId,
      1
    );
    console.log(`✅ Can unlock second badge without first: ${canUnlockSecond}`);

    // Get next unlockable badge
    const nextBadge = await collectionService.getNextUnlockableBadge(
      testUserId,
      collection.collectionId
    );
    console.log(`✅ Next unlockable badge: ${nextBadge?.name || "None"}`);

    // Test 4: Test collection dependency checking
    console.log("\n4️⃣ Testing collection dependency checking...");
    
    // Try to assign second badge without first (should fail)
    const secondBadge = badges.find(b => b.displayOrder === 1);
    if (secondBadge) {
      const dependencyCheck = await collectionService.assignBadgeWithCollectionCheck(
        testUserId,
        secondBadge.id,
        "test-admin"
      );
      console.log(`✅ Dependency check for second badge: ${dependencyCheck.success ? "PASSED" : "FAILED"}`);
      if (!dependencyCheck.success) {
        console.log(`   Error: ${dependencyCheck.error}`);
      }
    }

    // Test 5: Test standalone collection
    console.log("\n5️⃣ Testing standalone collection...");
    const standaloneData: CreateBadgeCollectionRequest = {
      collectionId: `test-standalone-collection-${timestamp}`,
      name: "Standalone Collection",
      description: "A collection where badges can be earned independently",
      type: "standalone"
    };

    const standaloneCollection = await collectionService.createCollection(standaloneData, "test-admin");
    console.log("✅ Standalone collection created:", standaloneCollection.name);

    // Create a badge in standalone collection
    const standaloneBadgeData: CreateBadgeTypeRequest = {
      collectionId: standaloneCollection.id,
      badgeId: "independent-badge",
      name: `Independent Badge ${timestamp}`,
      description: "Can be earned independently",
      icon: "🎯",
      design: {
        shape: "diamond",
        background: "#9C27B0",
        colors: ["#9C27B0"]
      },
      criteria: {
        requirement: "Independent achievement",
        tracked: "manual",
        type: "custom",
        conditions: { manual: true }
      },
      unlockType: "manual",
      displayOrder: 5, // High order to test independence
      category: "achievement"
    };

    const standaloneBadge = await badgeService.createBadgeType(standaloneBadgeData, "test-admin");
    console.log(`✅ Standalone badge created: ${standaloneBadge.name}`);

    // Test if high-order badge can be unlocked in standalone collection
    const canUnlockStandalone = await collectionService.canUnlockNextBadge(
      testUserId,
      standaloneCollection.collectionId,
      5
    );
    console.log(`✅ Can unlock high-order badge in standalone: ${canUnlockStandalone}`);

    // Test 6: Get collections with filters
    console.log("\n6️⃣ Testing collection filtering...");
    const progressiveCollections = await collectionService.getCollections({
      type: "progressive",
      isActive: true
    });
    console.log(`✅ Found ${progressiveCollections.length} progressive collections`);

    const standaloneCollections = await collectionService.getCollections({
      type: "standalone",
      isActive: true
    });
    console.log(`✅ Found ${standaloneCollections.length} standalone collections`);

    // Test 7: Get detailed collection progress
    console.log("\n7️⃣ Testing detailed collection progress...");
    const detailedProgress = await collectionService.getDetailedCollectionProgress(
      testUserId,
      collection.collectionId
    );
    console.log(`✅ Collection progress: ${detailedProgress.progress.badgesEarned}/${detailedProgress.progress.totalBadges}`);
    console.log(`✅ Next badge: ${detailedProgress.nextBadge?.name || "None"}`);
    console.log(`✅ Completion reward: ${detailedProgress.completionReward?.title || "None"}`);

    console.log("\n🎉 All badge collection tests completed successfully!");

    // Cleanup
    console.log("\n🧹 Cleaning up test data...");
    
    // Note: In a real scenario, you'd want to clean up the test data
    // For this demo, we'll leave it as the cleanup would require
    // additional database operations
    
    console.log("✅ Test completed!");

  } catch (error) {
    console.error("❌ Test failed:", error);
    throw error;
  }
}

// Run the test
if (import.meta.main) {
  testBadgeCollections()
    .then(() => {
      console.log("\n✨ Badge Collections test suite completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n💥 Badge Collections test suite failed:", error);
      process.exit(1);
    });
}

export { testBadgeCollections };