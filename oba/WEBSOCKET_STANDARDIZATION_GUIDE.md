# WebSocket Standardization Guide

This comprehensive guide covers the WebSocket standardization system implemented in the OBA platform. It provides everything you need to understand, implement, and maintain standardized WebSocket communications.

## Table of Contents

1. [Overview](#overview)
2. [Quick Start](#quick-start)
3. [Configuration](#configuration)
4. [API Reference](#api-reference)
5. [Migration Guide](#migration-guide)
6. [Best Practices](#best-practices)
7. [Examples](#examples)
8. [Troubleshooting](#troubleshooting)
9. [Performance Considerations](#performance-considerations)
10. [Security Guidelines](#security-guidelines)

## Overview

The WebSocket standardization system provides:

- **Consistent Message Format**: All WebSocket messages follow a standardized structure
- **Type Safety**: Full TypeScript support with interfaces and validation
- **Error Handling**: Standardized error responses with proper error codes
- **Correlation Tracking**: Request-response pattern support with correlation IDs
- **Validation**: Schema-based message validation with Zod
- **Backward Compatibility**: Gradual migration support for existing implementations
- **Performance Optimization**: Efficient broadcasting and message handling
- **Debugging Support**: Comprehensive logging and tracing capabilities

### Key Benefits

- **Maintainability**: Consistent patterns across all WebSocket handlers
- **Reliability**: Built-in error handling and validation
- **Developer Experience**: Type safety and clear API documentation
- **Scalability**: Optimized for high-throughput scenarios
- **Debugging**: Enhanced logging and correlation tracking

## Quick Start

### 1. Basic Setup

```typescript
import { WebSocketUtils } from './utils/websocket-utils';
import { loadWebSocketConfig } from './config/websocket.config';

// Load configuration
const wsConfig = loadWebSocketConfig();

// Create success message
const message = WebSocketUtils.success('USER_CREATED', {
  userId: '123',
  username: 'john_doe'
});

// Send message
WebSocketUtils.send(websocket, message);
```

### 2. Error Handling

```typescript
// Authentication error
const authError = WebSocketUtils.authenticationRequired();
WebSocketUtils.send(websocket, authError);

// Custom error
const customError = WebSocketUtils.error('VALIDATION_FAILED', 'Invalid input', {
  field: 'email',
  details: { expectedFormat: '<EMAIL>' }
});
WebSocketUtils.send(websocket, customError);
```

### 3. Broadcasting

```typescript
// Broadcast to channel
const eventMessage = WebSocketUtils.event('NEW_MESSAGE', {
  messageId: '456',
  content: 'Hello everyone!'
});

WebSocketUtils.sendToChannel('channel-123', eventMessage, websocketManager);
```

## Configuration

### Environment Variables

Configure WebSocket behavior using environment variables:

```bash
# Core settings
WS_ENABLE_STANDARDIZATION=true
WS_ENABLE_LEGACY_SUPPORT=true
WS_MESSAGE_VERSION=1.0.0

# Connection settings
WS_HEARTBEAT_INTERVAL=30000
WS_CONNECTION_TIMEOUT=60000
WS_MAX_CONNECTIONS=10000

# Message settings
WS_MAX_MESSAGE_SIZE=1048576
WS_ENABLE_COMPRESSION=true

# Validation settings
WS_ENABLE_VALIDATION=true
WS_STRICT_VALIDATION=false

# Logging settings
WS_LOG_LEVEL=info
WS_ENABLE_MESSAGE_LOGGING=false
WS_ENABLE_PERFORMANCE_METRICS=true

# Migration settings
WS_MIGRATION_MODE=gradual
WS_LEGACY_FORMAT_DETECTION=true
```

### Configuration Schema

```typescript
import { loadWebSocketConfig, validateWebSocketConfig } from './config/websocket.config';

// Load with environment overrides
const config = loadWebSocketConfig();

// Validate custom configuration
const validation = validateWebSocketConfig({
  enableStandardization: true,
  rateLimits: {
    messaging: { maxRequests: 100, windowMs: 60000 }
  }
});

if (!validation.isValid) {
  console.error('Configuration errors:', validation.errors);
}
```

## API Reference

### WebSocketUtils Class

The main utility class for creating and sending standardized messages.

#### Message Creation

```typescript
// Success messages
WebSocketUtils.success<T>(type: string, data: T, options?: SuccessOptions)

// Error messages
WebSocketUtils.error(code: string, message: string, options?: ErrorOptions)

// Event messages
WebSocketUtils.event<T>(event: string, data: T, options?: EventOptions)
```

#### Message Sending

```typescript
// Send to specific WebSocket
WebSocketUtils.send(ws: ServerWebSocket, message: IWebSocketMessage)

// Broadcast to multiple WebSockets
WebSocketUtils.broadcast(sockets: Set<ServerWebSocket>, message: IWebSocketMessage, options?: BroadcastOptions)

// Send to specific user
WebSocketUtils.sendToUser(userId: string, message: IWebSocketMessage, manager: WebSocketManager)

// Send to channel
WebSocketUtils.sendToChannel(channelId: string, message: IWebSocketMessage, manager: WebSocketManager, options?: ChannelOptions)
```

#### Common Error Responses

```typescript
WebSocketUtils.authenticationRequired()
WebSocketUtils.permissionDenied(permission?: string)
WebSocketUtils.notFound(resource: string)
WebSocketUtils.validationError(errors: ValidationError[])
WebSocketUtils.rateLimited(retryAfter?: number)
WebSocketUtils.internalError(message?: string)
```

For complete API documentation, see [WebSocketUtils API Documentation](./docs/WEBSOCKET_UTILS_API.md).

## Migration Guide

### Migration Strategies

1. **Gradual Migration** (Recommended)
   - Enable standardization with legacy support
   - Migrate handlers incrementally
   - Test thoroughly at each step

2. **Forced Migration**
   - For new projects or complete rewrites
   - Disable legacy support immediately
   - Implement all handlers with new format

### Step-by-Step Migration

1. **Enable Standardization**
   ```typescript
   const config = {
     enableStandardization: true,
     enableLegacySupport: true,
     migrationMode: 'gradual'
   };
   ```

2. **Update Message Creation**
   ```typescript
   // Before
   ws.send(JSON.stringify({ type: 'SUCCESS', data: result }));
   
   // After
   const message = WebSocketUtils.success('SUCCESS', result);
   WebSocketUtils.send(ws, message);
   ```

3. **Update Error Handling**
   ```typescript
   // Before
   ws.send(JSON.stringify({ error: 'Not found' }));
   
   // After
   const error = WebSocketUtils.notFound('user');
   WebSocketUtils.send(ws, error);
   ```

For detailed migration instructions, see [WebSocket Migration Guide](./WEBSOCKET_MIGRATION_GUIDE.md).

## Best Practices

### 1. Always Use Correlation IDs

```typescript
// Good
const message = WebSocketUtils.success('RESPONSE', data, {
  correlationId: request.meta.correlationId
});

// Bad
const message = WebSocketUtils.success('RESPONSE', data);
```

### 2. Implement Proper Error Handling

```typescript
try {
  await processMessage(data);
} catch (error) {
  if (error instanceof ValidationError) {
    const validationError = WebSocketUtils.validationError(error.errors);
    WebSocketUtils.send(ws, validationError);
  } else {
    const internalError = WebSocketUtils.internalError();
    WebSocketUtils.send(ws, internalError);
  }
}
```

### 3. Use Type Safety

```typescript
interface MessageData {
  content: string;
  channelId: string;
}

const message = WebSocketUtils.success<MessageData>('MESSAGE_SENT', {
  content: 'Hello',
  channelId: 'channel-123'
});
```

### 4. Validate Messages

```typescript
const validation = WebSocketValidator.validate(incomingMessage);
if (!validation.isValid) {
  const error = WebSocketUtils.validationError(validation.errors!);
  WebSocketUtils.send(ws, error);
  return;
}
```

### 5. Handle Connection Lifecycle

```typescript
// Connection setup
ws.data = {
  subscriptions: new Set(),
  correlationMap: new Map(),
  lastHeartbeat: Date.now()
};

// Connection cleanup
CorrelationTracker.cleanup(ws);
```

For comprehensive best practices with examples, see [WebSocket Best Practices Examples](./examples/websocket-best-practices-example.ts).

## Examples

### Authentication Flow

```typescript
websocket.message = async (ws, message) => {
  const data = JSON.parse(message.toString());
  
  if (data.type === 'AUTH_REQUEST') {
    try {
      const user = await authenticateUser(data.token);
      const success = WebSocketUtils.success('AUTH_SUCCESS', {
        userId: user.id,
        username: user.username
      }, { correlationId: data.meta?.correlationId });
      
      WebSocketUtils.send(ws, success);
    } catch (error) {
      const authError = WebSocketUtils.error('AUTH_FAILED', 'Invalid credentials', {
        correlationId: data.meta?.correlationId
      });
      WebSocketUtils.send(ws, authError);
    }
  }
};
```

### Message Broadcasting

```typescript
async function broadcastMessage(channelId: string, messageData: any) {
  const eventMessage = WebSocketUtils.event('NEW_MESSAGE', {
    messageId: messageData.id,
    content: messageData.content,
    authorId: messageData.authorId,
    timestamp: messageData.createdAt
  });
  
  WebSocketUtils.sendToChannel(channelId, eventMessage, websocketManager, {
    excludeUserId: messageData.authorId
  });
}
```

### Request-Response Pattern

```typescript
import { CorrelationTracker } from './utils/correlation-tracker';

async function requestUserInfo(ws: ServerWebSocket, userId: string): Promise<UserInfo> {
  const correlationId = CorrelationTracker.generateId();
  
  const request = WebSocketUtils.success('GET_USER_INFO', { userId }, { correlationId });
  WebSocketUtils.send(ws, request);
  
  const response = await CorrelationTracker.waitForResponse(ws, correlationId, 5000);
  return response.data as UserInfo;
}
```

## Troubleshooting

### Common Issues

1. **Message Format Errors**
   - Enable debug logging: `WS_LOG_LEVEL=debug`
   - Check message structure against interfaces
   - Validate with WebSocketValidator

2. **Correlation Timeouts**
   - Increase timeout values
   - Check response handling
   - Verify correlation ID matching

3. **Validation Failures**
   - Review Zod schemas
   - Check data types and required fields
   - Enable strict validation for debugging

4. **Performance Issues**
   - Use message batching for high frequency
   - Implement connection pooling
   - Monitor memory usage with correlation maps

### Debug Configuration

```typescript
const debugConfig = {
  logLevel: 'debug',
  enableMessageLogging: true,
  enableTracing: true,
  enablePerformanceMetrics: true
};
```

### Logging Examples

```typescript
// Enable detailed logging
console.log('WebSocket message:', {
  type: message.type,
  correlationId: message.meta.correlationId,
  timestamp: message.meta.timestamp,
  userId: ws.data.userId
});
```

## Performance Considerations

### Message Batching

```typescript
// Batch multiple messages for efficiency
const batchMessage = WebSocketUtils.success('MESSAGE_BATCH', {
  messages: [message1, message2, message3],
  count: 3
});
```

### Connection Pooling

```typescript
// Reuse connections efficiently
const connectionPool = new Map<string, ServerWebSocket>();
```

### Memory Management

```typescript
// Clean up correlation maps regularly
setInterval(() => {
  CorrelationTracker.cleanupExpired();
}, 300000); // 5 minutes
```

### Broadcasting Optimization

```typescript
// Pre-serialize messages for large broadcasts
const serializedMessage = JSON.stringify(message);
sockets.forEach(ws => ws.send(serializedMessage));
```

## Security Guidelines

### Message Validation

```typescript
// Always validate incoming messages
const validation = WebSocketValidator.validate(message);
if (!validation.isValid) {
  // Handle validation error
  return;
}
```

### Authentication Checks

```typescript
// Check authentication for protected operations
if (requiresAuth(messageType) && !ws.data.userId) {
  const authError = WebSocketUtils.authenticationRequired();
  WebSocketUtils.send(ws, authError);
  return;
}
```

### Permission Verification

```typescript
// Verify permissions before processing
if (!hasPermission(ws.data.userId, requiredPermission)) {
  const permError = WebSocketUtils.permissionDenied(requiredPermission);
  WebSocketUtils.send(ws, permError);
  return;
}
```

### Rate Limiting

```typescript
// Implement rate limiting
if (!checkRateLimit(ws.data.userId, messageType)) {
  const rateLimitError = WebSocketUtils.rateLimited(60);
  WebSocketUtils.send(ws, rateLimitError);
  return;
}
```

### Data Sanitization

```typescript
// Sanitize user input
const sanitizedData = sanitizeInput(messageData);
```

## Additional Resources

- [WebSocketUtils API Documentation](./docs/WEBSOCKET_UTILS_API.md)
- [Migration Guide](./WEBSOCKET_MIGRATION_GUIDE.md)
- [Best Practices Examples](./examples/websocket-best-practices-example.ts)
- [Configuration Reference](./config/websocket.config.ts)
- [Type Definitions](./types/websocket-standardization.types.ts)

## Support

For questions, issues, or contributions:

1. Check the troubleshooting section
2. Review the examples and documentation
3. Enable debug logging for detailed information
4. Consult the API reference for method signatures

The WebSocket standardization system is designed to be robust, scalable, and developer-friendly. Following this guide will help you implement reliable WebSocket communications in your OBA application.