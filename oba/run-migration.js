// Run the migration to add the preferred_status column
import { db } from "./db";

async function runMigration() {
  console.log("Running migration to add preferred_status column...");

  try {
    // Check if the column already exists
    const result = await db.execute(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_schema = 'base_schema' 
      AND table_name = 'users' 
      AND column_name = 'preferred_status'
    `);

    if (result.length > 0) {
      console.log("Column preferred_status already exists, skipping migration");
      return;
    }

    // Add the preferred_status column
    await db.execute(`
      ALTER TABLE "base_schema"."users" 
      ADD COLUMN "preferred_status" "user_status" DEFAULT 'ONLINE'
    `);

    // Update existing users to have their current status as preferred status (if not OFFLINE)
    await db.execute(`
      UPDATE "base_schema"."users" 
      SET "preferred_status" = "status" 
      WHERE "status" != 'OFFLINE'
    `);

    console.log("Migration completed successfully");
  } catch (error) {
    console.error("Error running migration:", error);
  } finally {
    process.exit(0);
  }
}

runMigration();
