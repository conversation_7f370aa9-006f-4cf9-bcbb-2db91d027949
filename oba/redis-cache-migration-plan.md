# Redis-Backed Cache Migration Plan

_Single-Bun → Multi-Bun Ready_

> Goal: introduce Redis caching today **without breaking the single-instance setup**, yet guarantee zero rewrites when you later add more Bun containers behind a load balancer.

---

## 0. Quick Win Checklist

- [ ] Add `src/cache.ts` (abstraction).
- [ ] Add `ioredis` to `package.json`.
- [ ] Replace every ad-hoc `Map` / in-memory cache with `cache.set|get|del`.
- [ ] Add `src/events.ts` for pub/sub invalidations.
- [ ] Document LOCAL vs SHARED state.

---

## 1. Abstraction Layer

| File            | Purpose                                                                                                    |
| --------------- | ---------------------------------------------------------------------------------------------------------- |
| `src/cache.ts`  | Single source of truth for all caching. Never import `redis` or `Map` elsewhere.                           |
| `src/events.ts` | Tiny pub/sub wrapper (Redis). Used for cross-instance invalidations even when you still have one instance. |

---

## 2. Key Naming Convention

```
<service>:<entity>:<id>[:<sub>]
chat:room:42:messages:page3
user:123:profile
```

---

## 3. Cache Scope Decision Table

| Data                           | Example          | Scope  | Storage           |
| ------------------------------ | ---------------- | ------ | ----------------- |
| Short-lived “typing” indicator | `typing:room:42` | LOCAL  | in-memory `Map`   |
| Auth token → user object       | `user:123:token` | SHARED | Redis             |
| Paginated message list         | `room:42:page3`  | SHARED | Redis             |
| WebSocket object               | `ws:abc123`      | LOCAL  | in-memory `Map`   |
| Cron leader lock               | `leader:cron`    | SHARED | Redis `SET NX EX` |

---

## 4. Code Changes per Endpoint

| HTTP/WebSocket Route            | Current         | Change                                        |
| ------------------------------- | --------------- | --------------------------------------------- |
| `GET /rooms/:id/messages?page=` | DB query        | `cached(key, () => db.query(...), 60)`        |
| `POST /rooms/:id/messages`      | insert + return | after insert `events.emit('invalidate', key)` |
| `GET /users/:id/profile`        | DB query        | `cached('user:<id>:profile', () => ..., 300)` |
| Presence “last seen”            | in-memory map   | `ZADD presence <timestamp> <userId>` in Redis |

---

## 5. Pub/Sub Invalidations

1. Producer (after write):
   ```ts
   await events.emit("invalidate", { keys: ["room:42:page3", "room:42:meta"] });
   ```
2. Consumer:
   ```ts
   events.on("invalidate", ({ keys }) => keys.forEach((k) => cache.del(k)));
   ```

---

## 6. Local Dev & CI

- `docker-compose.yml` snippet:
  ```yaml
  redis:
    image: redis:7-alpine
    ports: ["6379:6379"]
  ```
- `.env.example`:
  ```
  REDIS_URL=redis://localhost:6379
  ```
- CI step:
  ```sh
  redis-cli monitor > /tmp/redis.log &
  bun test --timeout 10000
  ```

---

## 7. Scale-Out Day (no code change)

1. Duplicate container.
2. Load balancer with sticky sessions _or_ use pub/sub to fan-out events.
3. Increase Redis memory/cluster if needed.

---

## 8. Files to Create / Modify

```
src/
 ├─ cache.ts        (new)
 ├─ events.ts       (new)
 ├─ routes/*.ts     (replace inline caches)
 └─ index.ts        (import events once to start listener)
.env.example        (add REDIS_URL)
docker-compose.yml  (add redis service)
package.json        (add ioredis, @types/ioredis)
```

---

## 9. Testing Script (quick sanity)

```sh
# terminal 1
docker-compose up redis
# terminal 2
bun run dev
# terminal 3
curl -X POST localhost:3000/rooms/1/messages -d '{"text":"hi"}'
curl localhost:3000/rooms/1/messages  # should be cached
redis-cli FLUSHALL
curl localhost:3000/rooms/1/messages  # should still work (cache miss)
```

---

## 10. Rollback Plan

- Commit before refactor.
- If Redis fails, abstraction returns `null` and code falls back to DB query (graceful degradation).
- Simply remove `REDIS_URL` env → abstraction will skip Redis.
