/**
 * WebSocket Best Practices Examples
 * 
 * This file demonstrates best practices for using the WebSocket standardization
 * system in the OBA platform. These examples show proper patterns for common
 * WebSocket operations.
 */

import { ServerWebSocket } from 'bun';
import { WebSocketUtils } from '../utils/websocket-utils';
import { WebSocketValidator } from '../utils/websocket-validator';
import { CorrelationTracker } from '../utils/correlation-tracker';
import { WebSocketManager } from '../manager/websocket.manager';
import type {
  IWebSocketMessage,
  CustomWebSocketData,
  IEnhancedWebSocketData
} from '../types/websocket-standardization.types';

// ============================================================================
// EXAMPLE 1: Proper Message Handler Structure
// ============================================================================

/**
 * Best Practice: Comprehensive message handler with proper error handling,
 * validation, authentication, and rate limiting checks.
 */
export async function bestPracticeMessageHandler(
  ws: ServerWebSocket<IEnhancedWebSocketData>,
  rawMessage: string | Buffer
) {
  try {
    // Step 1: Parse message safely
    let parsedMessage: any;
    try {
      parsedMessage = JSON.parse(rawMessage.toString());
    } catch (parseError) {
      const errorMessage = WebSocketUtils.error(
        'INVALID_JSON',
        'Message must be valid JSON'
      );
      WebSocketUtils.send(ws, errorMessage);
      return;
    }

    // Step 2: Validate message structure
    const validation = WebSocketValidator.validate(parsedMessage);
    if (!validation.isValid) {
      const validationError = WebSocketUtils.validationError(validation.errors!);
      WebSocketUtils.send(ws, validationError);
      return;
    }

    // Step 3: Check authentication if required
    if (requiresAuthentication(parsedMessage.type) && !ws.data.userId) {
      const authError = WebSocketUtils.authenticationRequired();
      WebSocketUtils.send(ws, authError);
      return;
    }

    // Step 4: Check permissions
    const requiredPermission = getRequiredPermission(parsedMessage.type);
    if (requiredPermission && !hasPermission(ws.data.userId, requiredPermission)) {
      const permError = WebSocketUtils.permissionDenied(requiredPermission);
      WebSocketUtils.send(ws, permError);
      return;
    }

    // Step 5: Check rate limiting
    if (!checkRateLimit(ws, parsedMessage.type)) {
      const rateLimitError = WebSocketUtils.rateLimited(60);
      WebSocketUtils.send(ws, rateLimitError);
      return;
    }

    // Step 6: Process the message
    await processValidatedMessage(ws, validation.sanitizedData);

  } catch (error) {
    console.error('Message handler error:', error);

    // Always send a standardized error response
    const internalError = WebSocketUtils.internalError(
      process.env.NODE_ENV === 'development' ? error.message : undefined
    );
    WebSocketUtils.send(ws, internalError);
  }
}

// ============================================================================
// EXAMPLE 2: Request-Response Pattern with Correlation
// ============================================================================

/**
 * Best Practice: Implementing request-response patterns with proper
 * correlation tracking and timeout handling.
 */
export class RequestResponseExample {
  private websocketManager: WebSocketManager;

  constructor(websocketManager: WebSocketManager) {
    this.websocketManager = websocketManager;
  }

  /**
   * Send a request and wait for a correlated response
   */
  async sendRequestWithResponse<TRequest, TResponse>(
    ws: ServerWebSocket<IEnhancedWebSocketData>,
    type: string,
    requestData: TRequest,
    timeoutMs: number = 30000
  ): Promise<TResponse> {
    // Generate correlation ID
    const correlationId = CorrelationTracker.generateId();

    // Create request message with correlation
    const requestMessage = WebSocketUtils.success(type, requestData, {
      correlationId
    });

    // Set up response tracking
    const responsePromise = CorrelationTracker.waitForResponse<TResponse>(
      ws,
      correlationId,
      timeoutMs
    );

    // Send the request
    WebSocketUtils.send(ws, requestMessage);

    try {
      // Wait for response
      const response = await responsePromise;
      return response.data as TResponse;
    } catch (error) {
      if (error.message === 'CORRELATION_TIMEOUT') {
        throw new Error(`Request ${type} timed out after ${timeoutMs}ms`);
      }
      throw error;
    }
  }

  /**
   * Handle incoming response and match with correlation
   */
  handleResponse(ws: ServerWebSocket<IEnhancedWebSocketData>, message: IWebSocketMessage) {
    if (message.meta.correlationId) {
      CorrelationTracker.handleResponse(ws, message);
    }
  }
}

// ============================================================================
// EXAMPLE 3: Efficient Broadcasting Patterns
// ============================================================================

/**
 * Best Practice: Efficient message broadcasting with proper filtering
 * and error handling.
 */
export class BroadcastingExample {
  private websocketManager: WebSocketManager;

  constructor(websocketManager: WebSocketManager) {
    this.websocketManager = websocketManager;
  }

  /**
   * Broadcast server-wide announcement
   */
  async broadcastServerAnnouncement(
    serverId: string,
    announcement: {
      title: string;
      message: string;
      priority: 'low' | 'medium' | 'high';
    }
  ) {
    const eventMessage = WebSocketUtils.event('SERVER_ANNOUNCEMENT', {
      serverId,
      ...announcement,
      timestamp: new Date().toISOString()
    });

    // Get all connections for the server
    const serverConnections = this.websocketManager.getServerConnections(serverId);

    // Broadcast with error handling
    try {
      WebSocketUtils.broadcast(serverConnections, eventMessage);

      // Log successful broadcast
      console.log(`Broadcasted announcement to ${serverConnections.size} connections`);
    } catch (error) {
      console.error('Failed to broadcast announcement:', error);
    }
  }

  /**
   * Broadcast to channel with user filtering
   */
  async broadcastToChannelWithFiltering(
    channelId: string,
    message: any,
    options: {
      excludeUserIds?: string[];
      requirePermission?: string;
      onlyOnlineUsers?: boolean;
    } = {}
  ) {
    const eventMessage = WebSocketUtils.event('CHANNEL_EVENT', {
      channelId,
      ...message,
      timestamp: new Date().toISOString()
    });

    // Custom filter function
    const filter = (ws: ServerWebSocket<IEnhancedWebSocketData>) => {
      // Exclude specific users
      if (options.excludeUserIds?.includes(ws.data.userId)) {
        return false;
      }

      // Check permission requirement
      if (options.requirePermission &&
        !hasPermission(ws.data.userId, options.requirePermission)) {
        return false;
      }

      // Check online status
      if (options.onlyOnlineUsers && ws.data.status !== 'online') {
        return false;
      }

      return true;
    };

    // Send to channel with filtering
    WebSocketUtils.sendToChannel(channelId, eventMessage, this.websocketManager, {
      filter
    });
  }
}

// ============================================================================
// EXAMPLE 4: Error Handling Patterns
// ============================================================================

/**
 * Best Practice: Comprehensive error handling with appropriate error types
 * and user-friendly messages.
 */
export class ErrorHandlingExample {

  /**
   * Handle different types of errors appropriately
   */
  async handleMessageWithProperErrors(
    ws: ServerWebSocket<IEnhancedWebSocketData>,
    messageData: any
  ) {
    try {
      // Simulate various operations that might fail
      await this.processMessage(messageData);

    } catch (error) {
      // Handle specific error types
      if (error instanceof ValidationError) {
        const validationError = WebSocketUtils.validationError(
          error.errors.map(err => ({
            field: err.field,
            message: err.message,
            code: err.code
          }))
        );
        WebSocketUtils.send(ws, validationError);

      } else if (error instanceof PermissionError) {
        const permError = WebSocketUtils.permissionDenied(error.requiredPermission);
        WebSocketUtils.send(ws, permError);

      } else if (error instanceof NotFoundError) {
        const notFoundError = WebSocketUtils.notFound(error.resourceType);
        WebSocketUtils.send(ws, notFoundError);

      } else if (error instanceof RateLimitError) {
        const rateLimitError = WebSocketUtils.rateLimited(error.retryAfter);
        WebSocketUtils.send(ws, rateLimitError);

      } else {
        // Generic internal error for unexpected errors
        const internalError = WebSocketUtils.internalError(
          process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred'
        );
        WebSocketUtils.send(ws, internalError);

        // Log the error for debugging
        console.error('Unexpected WebSocket error:', {
          error: error.message,
          stack: error.stack,
          userId: ws.data.userId,
          messageType: messageData.type
        });
      }
    }
  }

  private async processMessage(messageData: any): Promise<void> {
    // Simulate message processing that might throw various errors
    throw new ValidationError([
      { field: 'content', message: 'Content is required', code: 'REQUIRED' }
    ]);
  }
}

// ============================================================================
// EXAMPLE 5: Connection Lifecycle Management
// ============================================================================

/**
 * Best Practice: Proper connection lifecycle management with cleanup
 * and state management.
 */
export class ConnectionLifecycleExample {
  private websocketManager: WebSocketManager;

  constructor(websocketManager: WebSocketManager) {
    this.websocketManager = websocketManager;
  }

  /**
   * Handle new connection with proper setup
   */
  async handleNewConnection(ws: ServerWebSocket<IEnhancedWebSocketData>) {
    try {
      // Initialize connection data
      ws.data = {
        ...ws.data,
        subscriptions: new Set(),
        lastHeartbeat: Date.now(),
        messageCount: 0,
        rateLimitTokens: 100,
        correlationMap: new Map()
      };

      // Send welcome message
      const welcomeMessage = WebSocketUtils.success('CONNECTION_ESTABLISHED', {
        connectionId: ws.data.connectionId,
        serverTime: new Date().toISOString(),
        features: ['standardized-messages', 'correlation-tracking', 'rate-limiting']
      });

      WebSocketUtils.send(ws, welcomeMessage);

      // Set up heartbeat
      this.setupHeartbeat(ws);

      console.log(`New WebSocket connection established: ${ws.data.connectionId}`);

    } catch (error) {
      console.error('Failed to setup new connection:', error);
      ws.close(1011, 'Connection setup failed');
    }
  }

  /**
   * Handle connection close with proper cleanup
   */
  async handleConnectionClose(ws: ServerWebSocket<IEnhancedWebSocketData>) {
    try {
      // Clean up subscriptions
      if (ws.data.subscriptions) {
        for (const subscription of ws.data.subscriptions) {
          this.websocketManager.unsubscribe(ws, subscription);
        }
      }

      // Clean up correlation tracking
      if (ws.data.correlationMap) {
        CorrelationTracker.cleanup(ws);
      }

      // Notify other users if this was an authenticated connection
      if (ws.data.userId) {
        await this.notifyUserDisconnected(ws.data.userId);
      }

      console.log(`WebSocket connection closed: ${ws.data.connectionId}`);

    } catch (error) {
      console.error('Error during connection cleanup:', error);
    }
  }

  /**
   * Set up heartbeat mechanism
   */
  private setupHeartbeat(ws: ServerWebSocket<IEnhancedWebSocketData>) {
    const heartbeatInterval = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        const pingMessage = WebSocketUtils.success('PING', {
          timestamp: Date.now()
        });
        WebSocketUtils.send(ws, pingMessage);

        // Check if last heartbeat was too long ago
        const timeSinceLastHeartbeat = Date.now() - ws.data.lastHeartbeat;
        if (timeSinceLastHeartbeat > 60000) { // 1 minute
          console.warn(`Connection ${ws.data.connectionId} missed heartbeat`);
          ws.close(1001, 'Heartbeat timeout');
        }
      } else {
        clearInterval(heartbeatInterval);
      }
    }, 30000); // 30 seconds

    // Store interval reference for cleanup
    ws.data.heartbeatInterval = heartbeatInterval;
  }

  private async notifyUserDisconnected(userId: string) {
    const disconnectEvent = WebSocketUtils.event('USER_DISCONNECTED', {
      userId,
      timestamp: new Date().toISOString()
    });

    // Notify friends and servers
    await this.websocketManager.broadcastToUserFriends(userId, disconnectEvent);
    await this.websocketManager.broadcastToUserServers(userId, disconnectEvent);
  }
}

// ============================================================================
// EXAMPLE 6: Performance Optimization Patterns
// ============================================================================

/**
 * Best Practice: Performance optimization techniques for high-throughput
 * WebSocket operations.
 */
export class PerformanceOptimizationExample {
  private messageQueue: Map<string, IWebSocketMessage[]> = new Map();
  private batchTimer: NodeJS.Timeout | null = null;

  /**
   * Batch messages for efficient sending
   */
  queueMessage(userId: string, message: IWebSocketMessage) {
    if (!this.messageQueue.has(userId)) {
      this.messageQueue.set(userId, []);
    }

    this.messageQueue.get(userId)!.push(message);

    // Set up batch processing if not already scheduled
    if (!this.batchTimer) {
      this.batchTimer = setTimeout(() => {
        this.processBatchedMessages();
      }, 100); // 100ms batch window
    }
  }

  /**
   * Process batched messages efficiently
   */
  private processBatchedMessages() {
    for (const [userId, messages] of this.messageQueue.entries()) {
      if (messages.length > 0) {
        // Create batch message
        const batchMessage = WebSocketUtils.success('MESSAGE_BATCH', {
          messages,
          count: messages.length
        });

        // Send batch to user
        WebSocketUtils.sendToUser(userId, batchMessage, this.websocketManager);
      }
    }

    // Clear queue and timer
    this.messageQueue.clear();
    this.batchTimer = null;
  }

  /**
   * Efficient message filtering for large broadcasts
   */
  async efficientBroadcast(
    targetSockets: Set<ServerWebSocket<IEnhancedWebSocketData>>,
    message: IWebSocketMessage,
    filterCriteria: {
      requiredPermission?: string;
      excludeUserIds?: Set<string>;
      onlyActiveUsers?: boolean;
    }
  ) {
    // Pre-serialize message for efficiency
    const serializedMessage = JSON.stringify(message);

    // Use parallel processing for large sets
    const socketArray = Array.from(targetSockets);
    const batchSize = 100;

    for (let i = 0; i < socketArray.length; i += batchSize) {
      const batch = socketArray.slice(i, i + batchSize);

      // Process batch in parallel
      await Promise.all(
        batch.map(async (ws) => {
          if (this.shouldSendToSocket(ws, filterCriteria)) {
            try {
              ws.send(serializedMessage);
            } catch (error) {
              console.error(`Failed to send to socket ${ws.data.connectionId}:`, error);
            }
          }
        })
      );
    }
  }

  private shouldSendToSocket(
    ws: ServerWebSocket<IEnhancedWebSocketData>,
    criteria: any
  ): boolean {
    if (criteria.excludeUserIds?.has(ws.data.userId)) {
      return false;
    }

    if (criteria.requiredPermission &&
      !hasPermission(ws.data.userId, criteria.requiredPermission)) {
      return false;
    }

    if (criteria.onlyActiveUsers && ws.data.status !== 'online') {
      return false;
    }

    return true;
  }
}

// ============================================================================
// Helper Functions and Types
// ============================================================================

// Custom error types for better error handling
class ValidationError extends Error {
  constructor(public errors: Array<{ field: string, message: string, code?: string }>) {
    super('Validation failed');
    this.name = 'ValidationError';
  }
}

class PermissionError extends Error {
  constructor(public requiredPermission: string) {
    super(`Permission required: ${requiredPermission}`);
    this.name = 'PermissionError';
  }
}

class NotFoundError extends Error {
  constructor(public resourceType: string) {
    super(`${resourceType} not found`);
    this.name = 'NotFoundError';
  }
}

class RateLimitError extends Error {
  constructor(public retryAfter: number) {
    super('Rate limit exceeded');
    this.name = 'RateLimitError';
  }
}

// Helper functions (these would be implemented elsewhere in your app)
function requiresAuthentication(messageType: string): boolean {
  const publicTypes = ['PING', 'AUTH_REQUEST'];
  return !publicTypes.includes(messageType);
}

function getRequiredPermission(messageType: string): string | null {
  const permissionMap: Record<string, string> = {
    'MESSAGE_SEND': 'SEND_MESSAGES',
    'CHANNEL_CREATE': 'MANAGE_CHANNELS',
    'USER_BAN': 'BAN_MEMBERS'
  };
  return permissionMap[messageType] || null;
}

function hasPermission(userId: string, permission: string): boolean {
  // Implementation would check user permissions
  return true; // Placeholder
}

function checkRateLimit(ws: ServerWebSocket<IEnhancedWebSocketData>, messageType: string): boolean {
  // Implementation would check rate limits
  return true; // Placeholder
}

async function processValidatedMessage(
  ws: ServerWebSocket<IEnhancedWebSocketData>,
  message: any
): Promise<void> {
  // Implementation would route to appropriate handler
  console.log('Processing message:', message.type);
}

/**
 * Usage Examples:
 * 
 * // 1. Set up message handler
 * websocket.message = bestPracticeMessageHandler;
 * 
 * // 2. Use request-response pattern
 * const requestResponse = new RequestResponseExample(websocketManager);
 * const response = await requestResponse.sendRequestWithResponse(ws, 'GET_USER_INFO', { userId: '123' });
 * 
 * // 3. Efficient broadcasting
 * const broadcaster = new BroadcastingExample(websocketManager);
 * await broadcaster.broadcastServerAnnouncement('server-123', {
 *   title: 'Maintenance',
 *   message: 'Server will be down for 10 minutes',
 *   priority: 'high'
 * });
 * 
 * // 4. Connection lifecycle
 * const lifecycle = new ConnectionLifecycleExample(websocketManager);
 * websocket.open = (ws) => lifecycle.handleNewConnection(ws);
 * websocket.close = (ws) => lifecycle.handleConnectionClose(ws);
 */