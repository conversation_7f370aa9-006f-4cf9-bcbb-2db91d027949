/**
 * WebSocket Backward Compatibility Example
 * 
 * This example demonstrates how to use the WebSocket backward compatibility layer
 * to support both legacy and standardized message formats during migration.
 */

import type { ServerWebSocket } from "bun";
import { WebSocketCompatibility } from "../utils/websocket-compatibility";
import { WebSocketMigrationUtils } from "../utils/websocket-migration-utils";
import { WebSocketUtils } from "../utils/websocket-utils";
import { WebSocketManager } from "../manager/websocket.manager";
import type { CustomWebSocketData } from "../types/websocket.types";
import type { IWebSocketMessage } from "../types/websocket-standardization.types";

// Example 1: Detecting and Converting Legacy Messages
export function exampleMessageDetectionAndConversion() {
  console.log("=== Example 1: Message Detection and Conversion ===");

  // Legacy message format (old)
  const legacyMessage = {
    type: "MESSAGE_SEND",
    data: { content: "Hello World", channelId: "ch123" },
    sender: "user456",
    timestamp: "2024-01-01T12:00:00Z",
  };

  // Standardized message format (new)
  const standardizedMessage = WebSocketUtils.success("MESSAGE_SENT", {
    messageId: "msg789",
    content: "Hello World",
    channelId: "ch123",
  });

  // Detect message formats
  console.log("Is legacy message:", WebSocketCompatibility.isLegacyMessage(legacyMessage));
  console.log("Is standardized message:", WebSocketCompatibility.isLegacyMessage(standardizedMessage));

  // Convert legacy to standardized
  const convertedMessage = WebSocketCompatibility.convertLegacyMessage(legacyMessage, {
    userId: "user456",
    source: "client",
  });
  console.log("Converted message:", JSON.stringify(convertedMessage, null, 2));

  // Convert standardized to legacy
  const legacyFormat = WebSocketCompatibility.convertToLegacyFormat(standardizedMessage);
  console.log("Legacy format:", JSON.stringify(legacyFormat, null, 2));
}

// Example 2: Migrating Existing Handlers
export function exampleHandlerMigration() {
  console.log("\n=== Example 2: Handler Migration ===");

  // Original legacy handler
  const legacyMessageHandler = async (
    ws: ServerWebSocket<CustomWebSocketData>,
    sender: string,
    data: { content: string; channelId: string },
  ) => {
    console.log(`Legacy handler: ${sender} sent "${data.content}" to ${data.channelId}`);
    
    // Legacy response (old way)
    const response = {
      type: "MESSAGE_SENT",
      data: { messageId: "msg123", success: true },
      timestamp: new Date(),
    };
    
    ws.send(JSON.stringify(response));
  };

  // Wrap legacy handler to support standardized format
  const wrappedHandler = WebSocketMigrationUtils.wrapLegacyHandler(legacyMessageHandler, {
    handlerName: "MessageHandler",
    enableLogging: true,
    validateInput: true,
  });

  // New standardized handler
  const standardizedHandler = async (
    ws: ServerWebSocket<CustomWebSocketData>,
    message: IWebSocketMessage<{ content: string; channelId: string }>,
  ) => {
    console.log(`Standardized handler: Processing message ${message.meta.id}`);
    
    // Standardized response (new way)
    const response = WebSocketUtils.success("MESSAGE_SENT", {
      messageId: "msg456",
      success: true,
    }, {
      correlationId: message.meta.correlationId,
    });
    
    WebSocketCompatibility.sendCompatibleMessage(ws, response);
  };

  // Create dual-format handler that supports both
  const dualFormatHandler = WebSocketMigrationUtils.createDualFormatHandler(
    legacyMessageHandler,
    standardizedHandler,
    {
      handlerName: "MessageHandler",
      preferStandardized: true,
    },
  );

  console.log("Handlers created successfully");
}

// Example 3: Compatible Message Sending
export function exampleCompatibleMessaging() {
  console.log("\n=== Example 3: Compatible Message Sending ===");

  // Mock WebSocket connections with different capabilities
  const legacyWebSocket = {
    data: {
      userId: "legacy-user",
      token: "token123",
      isAlive: true,
      type: "channel",
      legacyOnly: true, // This connection only supports legacy format
    },
    readyState: WebSocket.OPEN,
    send: (message: string) => {
      console.log("Legacy WS received:", message);
    },
  } as any;

  const modernWebSocket = {
    data: {
      userId: "modern-user",
      token: "token456",
      isAlive: true,
      type: "channel",
      supportsStandardizedFormat: true, // This connection supports standardized format
    },
    readyState: WebSocket.OPEN,
    send: (message: string) => {
      console.log("Modern WS received:", message);
    },
  } as any;

  // Create a standardized message
  const message = WebSocketUtils.success("USER_JOINED", {
    userId: "new-user",
    username: "john_doe",
    channelId: "general",
  });

  // Send to both connections - the compatibility layer will handle format conversion
  console.log("Sending to legacy connection:");
  WebSocketCompatibility.sendCompatibleMessage(legacyWebSocket, message);

  console.log("Sending to modern connection:");
  WebSocketCompatibility.sendCompatibleMessage(modernWebSocket, message);
}

// Example 4: Processing Incoming Messages
export function exampleIncomingMessageProcessing() {
  console.log("\n=== Example 4: Processing Incoming Messages ===");

  const mockWebSocket = {
    data: {
      userId: "test-user",
      token: "token789",
      isAlive: true,
      type: "channel",
    },
  } as any;

  // Legacy message from client
  const legacyRawMessage = JSON.stringify({
    type: "MESSAGE_SEND",
    data: { content: "Hello from legacy client" },
    sender: "legacy-user",
  });

  // Standardized message from client
  const standardizedRawMessage = JSON.stringify({
    type: "MESSAGE_SEND",
    data: { content: "Hello from modern client" },
    meta: {
      messageId: "msg-modern-123",
      timestamp: new Date(),
      version: "1.0.0",
      source: "client",
    },
  });

  // Process both message types
  console.log("Processing legacy message:");
  const processedLegacy = WebSocketCompatibility.processIncomingMessage(
    legacyRawMessage,
    mockWebSocket,
  );
  console.log("Result:", processedLegacy?.type, processedLegacy?.meta?.messageId);

  console.log("Processing standardized message:");
  const processedStandardized = WebSocketCompatibility.processIncomingMessage(
    standardizedRawMessage,
    mockWebSocket,
  );
  console.log("Result:", processedStandardized?.type, processedStandardized?.meta?.messageId);
}

// Example 5: Migration Utilities
export function exampleMigrationUtilities() {
  console.log("\n=== Example 5: Migration Utilities ===");

  // Create migration helpers
  const helpers = WebSocketMigrationUtils.createMigrationHelpers();

  // Create standardized responses using helpers
  const successResponse = helpers.createSuccessResponse("OPERATION_COMPLETE", {
    result: "success",
    data: { id: 123 },
  }, "correlation-456");

  const errorResponse = helpers.createErrorResponse(
    "Operation failed",
    "OPERATION_ERROR",
    "correlation-789",
  );

  const eventMessage = helpers.createEventMessage("USER_STATUS_CHANGED", {
    userId: "user123",
    status: "online",
  }, {
    serverId: "server456",
  });

  console.log("Success response:", successResponse.type);
  console.log("Error response:", errorResponse.error.message);
  console.log("Event message:", eventMessage.event);

  // Serialize messages
  const serializedSuccess = helpers.serializeMessage(successResponse);
  const serializedError = helpers.serializeMessage(errorResponse);

  console.log("Serialized success length:", serializedSuccess.length);
  console.log("Serialized error length:", serializedError.length);
}

// Example 6: Migration Progress Tracking
export function exampleMigrationTracking() {
  console.log("\n=== Example 6: Migration Progress Tracking ===");

  // Simulate handler usage statistics
  const handlers = [
    { name: "AuthHandler", legacy: 0, standardized: 150 },
    { name: "MessageHandler", legacy: 75, standardized: 125 },
    { name: "VoiceHandler", legacy: 200, standardized: 0 },
    { name: "ChannelHandler", legacy: 50, standardized: 50 },
  ];

  console.log("Migration Progress Report:");
  console.log("========================");

  handlers.forEach(handler => {
    const report = WebSocketMigrationUtils.generateMigrationReport(
      handler.name,
      handler.legacy,
      handler.standardized,
    );

    console.log(`\n${report.handlerName}:`);
    console.log(`  Total Calls: ${report.totalCalls}`);
    console.log(`  Legacy: ${report.legacyPercentage}%`);
    console.log(`  Standardized: ${report.standardizedPercentage}%`);
    console.log(`  Status: ${report.migrationStatus}`);
    console.log(`  Recommendation: ${report.recommendation}`);
  });
}

// Example 7: Enhanced Manager with Migration Support
export function exampleEnhancedManager() {
  console.log("\n=== Example 7: Enhanced Manager ===");

  // Get WebSocket manager instance
  const manager = WebSocketManager.getInstance();

  // Create migration wrapper
  const enhancedManager = WebSocketMigrationUtils.createMigrationWrapper(manager);

  // Use enhanced methods that support both formats
  const legacyMessage = {
    type: "SERVER_ANNOUNCEMENT",
    data: { message: "Server maintenance in 10 minutes" },
  };

  const standardizedMessage = WebSocketUtils.event("SERVER_ANNOUNCEMENT", {
    message: "Server maintenance in 10 minutes",
    severity: "warning",
  });

  // Both calls will work regardless of message format
  console.log("Broadcasting legacy message...");
  enhancedManager.broadcastCompatible(legacyMessage, "server123");

  console.log("Broadcasting standardized message...");
  enhancedManager.broadcastCompatible(standardizedMessage, "server123");

  console.log("Sending to user with legacy format...");
  enhancedManager.sendToUserCompatible("user456", legacyMessage);

  console.log("Sending to user with standardized format...");
  enhancedManager.sendToUserCompatible("user456", standardizedMessage);
}

// Example 8: Error Handling and Fallbacks
export function exampleErrorHandling() {
  console.log("\n=== Example 8: Error Handling ===");

  // Test with invalid messages
  const invalidMessages = [
    null,
    undefined,
    "invalid json",
    '{"type": null}',
    '{"incomplete": true',
  ];

  const mockWebSocket = {
    data: { userId: "test-user", token: "token", isAlive: true, type: "test" },
    readyState: WebSocket.CLOSED, // Closed connection
    send: () => {},
  } as any;

  invalidMessages.forEach((msg, index) => {
    console.log(`\nTesting invalid message ${index + 1}:`);
    
    try {
      const result = WebSocketCompatibility.processIncomingMessage(
        msg as string,
        mockWebSocket,
      );
      console.log("Result:", result ? "Processed" : "Failed gracefully");
    } catch (error) {
      console.log("Error handled:", error instanceof Error ? error.message : "Unknown error");
    }
  });

  // Test sending to closed connection
  const testMessage = WebSocketUtils.success("TEST", { data: "test" });
  console.log("\nTesting send to closed connection:");
  WebSocketCompatibility.sendCompatibleMessage(mockWebSocket, testMessage);
  console.log("Handled gracefully");
}

// Run all examples
export function runAllExamples() {
  console.log("WebSocket Backward Compatibility Examples");
  console.log("========================================");

  exampleMessageDetectionAndConversion();
  exampleHandlerMigration();
  exampleCompatibleMessaging();
  exampleIncomingMessageProcessing();
  exampleMigrationUtilities();
  exampleMigrationTracking();
  exampleEnhancedManager();
  exampleErrorHandling();

  console.log("\n=== All Examples Completed ===");
}

// Export for use in other files
export {
  WebSocketCompatibility,
  WebSocketMigrationUtils,
  WebSocketUtils,
};

// Run examples if this file is executed directly
if (import.meta.main) {
  runAllExamples();
}