/**
 * Example demonstrating how to integrate correlation tracking into WebSocket handlers
 * This shows the pattern for implementing request-response correlation tracking
 */

import { WebSocketUtils } from "../utils/websocket-utils";
import { correlationTracker } from "../utils/correlation-tracker";
import type { ServerWebSocket } from "bun";
import type { CustomWebSocketData } from "../types/websocket.types";

/**
 * Example of how to handle a WebSocket message with correlation tracking
 */
export class CorrelationTrackingExample {
  /**
   * Example: Handle a server creation request with correlation tracking
   */
  async handleServerCreateWithCorrelation(
    ws: ServerWebSocket<CustomWebSocketData>,
    message: any,
  ) {
    const { type, sender, data } = message;
    const { userId } = ws.data;

    // 1. Extract or generate correlation ID
    const incomingCorrelationId = message?.meta?.correlationId;

    // 2. Start correlation tracking for this request
    const correlationId =
      incomingCorrelationId ||
      correlationTracker.startTracking("SERVER_CREATE", userId, {
        metadata: {
          serverId: data.serverId,
          serverName: data.name,
          requestData: data,
        },
        timeoutMs: 30000, // 30 second timeout
        onTimeout: (corrId, corrData) => {
          console.warn(`Server creation request timed out`, {
            correlationId: corrId,
            userId: corrData.userId,
            duration: Date.now() - corrData.timestamp,
          });

          // Send timeout error to client
          const timeoutError = WebSocketUtils.error(
            "REQUEST_TIMEOUT",
            "Server creation request timed out",
            { correlationId: corrId },
          );
          WebSocketUtils.send(ws, timeoutError);
        },
      });

    try {
      // 3. Process the request (simulate server creation)
      console.log(
        `Processing server creation with correlation ID: ${correlationId}`,
      );

      // Simulate async operation
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Simulate server creation result
      const newServer = {
        id: "server-123",
        name: data.name,
        ownerId: userId,
        createdAt: new Date().toISOString(),
      };

      // 4. Send success response with correlation tracking
      const successResponse = WebSocketUtils.success(
        "SERVER_CREATED",
        {
          serverId: newServer.id,
          serverName: newServer.name,
          ownerId: newServer.ownerId,
          createdAt: newServer.createdAt,
        },
        { correlationId },
      );

      WebSocketUtils.send(ws, successResponse);

      // 5. Broadcast event to other users with correlation tracking
      const serverCreatedEvent = WebSocketUtils.event(
        "SERVER_CREATED_BROADCAST",
        {
          serverId: newServer.id,
          serverName: newServer.name,
          createdBy: userId,
          timestamp: new Date(),
        },
        {
          category: "server",
          severity: "info",
        },
      );

      // Link the broadcast event to the original request
      serverCreatedEvent.meta.correlationId = correlationId;

      // Broadcast to other users (excluding the creator)
      this.broadcastToOthers(
        WebSocketUtils.serialize(serverCreatedEvent),
        userId,
      );

      // 6. Complete correlation tracking
      correlationTracker.completeTracking(correlationId, {
        success: true,
        serverId: newServer.id,
        responseTime: Date.now(),
      });

      console.log(
        `Server creation completed successfully with correlation ID: ${correlationId}`,
      );
    } catch (error) {
      // 7. Handle errors with correlation tracking
      console.error(
        `Server creation failed with correlation ID: ${correlationId}`,
        error,
      );

      const errorResponse = WebSocketUtils.error(
        "SERVER_CREATION_FAILED",
        `Failed to create server: ${error instanceof Error ? error.message : "Unknown error"}`,
        {
          correlationId,
          errorDetails: error instanceof Error ? error.stack : undefined,
        },
      );

      WebSocketUtils.send(ws, errorResponse);

      // Complete correlation tracking with error
      correlationTracker.completeTracking(correlationId, {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        responseTime: Date.now(),
      });
    }
  }

  /**
   * Example: Handle a message send request with correlation tracking
   */
  async handleMessageSendWithCorrelation(
    ws: ServerWebSocket<CustomWebSocketData>,
    message: any,
  ) {
    const { type, sender, data } = message;
    const { userId } = ws.data;

    // Extract or generate correlation ID
    const correlationId =
      message?.meta?.correlationId ||
      correlationTracker.startTracking("MESSAGE_SEND", userId, {
        metadata: {
          channelId: data.channelId,
          serverId: data.serverId,
          messageContent: data.content?.substring(0, 50), // First 50 chars for logging
        },
        timeoutMs: 15000, // 15 second timeout for messages
      });

    try {
      // Process message sending
      console.log(
        `Processing message send with correlation ID: ${correlationId}`,
      );

      // Simulate message creation
      const newMessage = {
        id: "msg-456",
        content: data.content,
        authorId: userId,
        channelId: data.channelId,
        serverId: data.serverId,
        createdAt: new Date().toISOString(),
      };

      // Send success response to sender
      const successResponse = WebSocketUtils.success(
        "MESSAGE_SENT",
        {
          messageId: newMessage.id,
          channelId: newMessage.channelId,
          serverId: newMessage.serverId,
          timestamp: newMessage.createdAt,
        },
        { correlationId },
      );

      WebSocketUtils.send(ws, successResponse);

      // Broadcast message to channel members
      const messageEvent = WebSocketUtils.event(
        "MESSAGE_RECEIVED",
        {
          messageId: newMessage.id,
          content: newMessage.content,
          authorId: newMessage.authorId,
          channelId: newMessage.channelId,
          serverId: newMessage.serverId,
          timestamp: newMessage.createdAt,
        },
        {
          category: "message",
          severity: "info",
        },
      );

      // Link to original request
      messageEvent.meta.correlationId = correlationId;

      // Broadcast to channel members (excluding sender)
      this.broadcastToChannel(
        WebSocketUtils.serialize(messageEvent),
        data.channelId,
        data.serverId,
        userId, // exclude sender
      );

      // Complete correlation tracking
      correlationTracker.completeTracking(correlationId, {
        success: true,
        messageId: newMessage.id,
        channelId: newMessage.channelId,
      });
    } catch (error) {
      console.error(
        `Message send failed with correlation ID: ${correlationId}`,
        error,
      );

      const errorResponse = WebSocketUtils.error(
        "MESSAGE_SEND_FAILED",
        `Failed to send message: ${error instanceof Error ? error.message : "Unknown error"}`,
        { correlationId },
      );

      WebSocketUtils.send(ws, errorResponse);

      correlationTracker.completeTracking(correlationId, {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * Example: Handle a request that requires multiple async operations
   */
  async handleComplexOperationWithCorrelation(
    ws: ServerWebSocket<CustomWebSocketData>,
    message: any,
  ) {
    const { userId } = ws.data;
    const { data } = message;

    // Start main correlation tracking
    const mainCorrelationId = correlationTracker.startTracking(
      "COMPLEX_OPERATION",
      userId,
      {
        metadata: { operationType: data.type },
        timeoutMs: 60000, // 1 minute timeout for complex operations
      },
    );

    try {
      // Step 1: Validate permissions
      const permissionCorrelationId = correlationTracker.startTracking(
        "PERMISSION_CHECK",
        userId,
        {
          metadata: { parentCorrelation: mainCorrelationId },
          timeoutMs: 5000,
        },
      );

      console.log(
        `Checking permissions with correlation ID: ${permissionCorrelationId}`,
      );
      await this.simulatePermissionCheck(userId, data.serverId);

      correlationTracker.completeTracking(permissionCorrelationId, {
        success: true,
      });

      // Step 2: Process data
      const processingCorrelationId = correlationTracker.startTracking(
        "DATA_PROCESSING",
        userId,
        {
          metadata: { parentCorrelation: mainCorrelationId },
          timeoutMs: 30000,
        },
      );

      console.log(
        `Processing data with correlation ID: ${processingCorrelationId}`,
      );
      const processedData = await this.simulateDataProcessing(data);

      correlationTracker.completeTracking(processingCorrelationId, {
        success: true,
        processedItems: processedData.length,
      });

      // Step 3: Save results
      const saveCorrelationId = correlationTracker.startTracking(
        "DATA_SAVE",
        userId,
        {
          metadata: { parentCorrelation: mainCorrelationId },
          timeoutMs: 10000,
        },
      );

      console.log(`Saving results with correlation ID: ${saveCorrelationId}`);
      const savedResult = await this.simulateDataSave(processedData);

      correlationTracker.completeTracking(saveCorrelationId, {
        success: true,
        savedId: savedResult.id,
      });

      // Send final success response
      const successResponse = WebSocketUtils.success(
        "COMPLEX_OPERATION_COMPLETED",
        {
          resultId: savedResult.id,
          processedItems: processedData.length,
          completedAt: new Date().toISOString(),
        },
        { correlationId: mainCorrelationId },
      );

      WebSocketUtils.send(ws, successResponse);

      // Complete main correlation tracking
      correlationTracker.completeTracking(mainCorrelationId, {
        success: true,
        resultId: savedResult.id,
        stepsCompleted: 3,
      });
    } catch (error) {
      console.error(
        `Complex operation failed with correlation ID: ${mainCorrelationId}`,
        error,
      );

      const errorResponse = WebSocketUtils.error(
        "COMPLEX_OPERATION_FAILED",
        `Operation failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        { correlationId: mainCorrelationId },
      );

      WebSocketUtils.send(ws, errorResponse);

      correlationTracker.completeTracking(mainCorrelationId, {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * Example: Get correlation statistics for monitoring
   */
  getCorrelationStats() {
    const stats = correlationTracker.getStats();

    console.log("Correlation Tracking Statistics:", {
      activeCorrelations: stats.activeCorrelations,
      averageAge: `${Math.round(stats.averageAge / 1000)}s`,
      oldestCorrelation: stats.oldestCorrelation
        ? {
            id: stats.oldestCorrelation.id,
            type: stats.oldestCorrelation.requestType,
            age: `${Math.round((Date.now() - stats.oldestCorrelation.timestamp) / 1000)}s`,
          }
        : null,
    });

    return stats;
  }

  /**
   * Example: Clean up expired correlations (should be called periodically)
   */
  cleanupExpiredCorrelations() {
    const cleanedCount = correlationTracker.cleanupExpired(5 * 60 * 1000); // 5 minutes

    if (cleanedCount > 0) {
      console.log(`Cleaned up ${cleanedCount} expired correlations`);
    }

    return cleanedCount;
  }

  // Helper methods for simulation
  private async simulatePermissionCheck(
    userId: string,
    serverId: string,
  ): Promise<boolean> {
    await new Promise((resolve) => setTimeout(resolve, 50));
    return true;
  }

  private async simulateDataProcessing(data: any): Promise<any[]> {
    await new Promise((resolve) => setTimeout(resolve, 200));
    return [{ processed: true, data }];
  }

  private async simulateDataSave(data: any[]): Promise<{ id: string }> {
    await new Promise((resolve) => setTimeout(resolve, 100));
    return { id: "saved-123" };
  }

  private broadcastToOthers(message: string, excludeUserId: string): void {
    // Implementation would broadcast to other users
    console.log(
      `Broadcasting to others (excluding ${excludeUserId}):`,
      message,
    );
  }

  private broadcastToChannel(
    message: string,
    channelId: string,
    serverId: string,
    excludeUserId?: string,
  ): void {
    // Implementation would broadcast to channel members
    console.log(
      `Broadcasting to channel ${channelId} in server ${serverId}:`,
      message,
    );
  }
}

/**
 * Example usage and testing
 */
export function demonstrateCorrelationTracking() {
  const example = new CorrelationTrackingExample();

  console.log("=== Correlation Tracking Demonstration ===");

  // Show initial stats
  console.log("\nInitial correlation stats:");
  example.getCorrelationStats();

  // Simulate some operations
  console.log("\nSimulating WebSocket operations with correlation tracking...");

  // This would normally be called from actual WebSocket message handlers
  // example.handleServerCreateWithCorrelation(ws, message);
  // example.handleMessageSendWithCorrelation(ws, message);

  console.log("\nCorrelation tracking demonstration complete!");
}

// Export for use in other files
export { correlationTracker };
