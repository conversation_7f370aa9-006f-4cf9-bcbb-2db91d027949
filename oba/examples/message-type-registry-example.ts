/**
 * Message Type Registry Usage Examples
 * 
 * This file demonstrates how to use the MessageTypeRegistry system
 * for WebSocket message standardization.
 */

import {
  MessageTypeRegistry,
  MessageCategory,
  RateLimitGroup,
  MessageSchemas,
  type IExtendedMessageTypeDefinition,
} from "../utils/message-type-registry";
import { EventTypes } from "../constants/eventTypes";
import { SEND_MESSAGES, MANAGE_CHANNELS } from "../constants/permissions";

console.log("=== Message Type Registry Examples ===\n");

// Example 1: Getting message type information
console.log("1. Getting Message Type Information:");
const messageSendType = MessageTypeRegistry.getMessageType(EventTypes.MESSAGE_SEND);
if (messageSendType) {
  console.log(`- Type: ${messageSendType.type}`);
  console.log(`- Category: ${messageSendType.category}`);
  console.log(`- Direction: ${messageSendType.direction}`);
  console.log(`- Requires Auth: ${messageSendType.requiresAuth}`);
  console.log(`- Permission: ${messageSendType.permissionName || 'None'}`);
  console.log(`- Rate Limit Group: ${messageSendType.rateLimitGroup}`);
  console.log(`- Max Size: ${messageSendType.maxSize || 'No limit'} bytes`);
  console.log(`- Supports Correlation: ${messageSendType.supportsCorrelation}`);
  console.log(`- Response Type: ${messageSendType.responseType || 'None'}`);
}
console.log();

// Example 2: Checking authentication requirements
console.log("2. Authentication Requirements:");
const authRequiredTypes = [
  EventTypes.MESSAGE_SEND,
  EventTypes.VOICE_JOIN,
  EventTypes.SERVER_CREATE,
  EventTypes.ERROR,
];

authRequiredTypes.forEach(type => {
  const requiresAuth = MessageTypeRegistry.requiresAuth(type);
  console.log(`- ${type}: ${requiresAuth ? 'Auth Required' : 'No Auth Required'}`);
});
console.log();

// Example 3: Getting message types by category
console.log("3. Message Types by Category:");
const messageTypes = MessageTypeRegistry.getMessageTypesByCategory(MessageCategory.MESSAGE);
console.log(`- MESSAGE category has ${messageTypes.length} types:`);
messageTypes.forEach(type => {
  console.log(`  * ${type.type} (${type.direction})`);
});
console.log();

// Example 4: Rate limiting information
console.log("4. Rate Limiting Information:");
const rateLimitTypes = [
  EventTypes.MESSAGE_SEND,
  EventTypes.VOICE_DATA_SEND,
  EventTypes.SERVER_CREATE,
  EventTypes.CHANNEL_CREATE,
];

rateLimitTypes.forEach(type => {
  const config = MessageTypeRegistry.getRateLimitConfig(type);
  if (config) {
    console.log(`- ${type}: ${config.maxRequests} requests per ${config.windowMs / 1000}s`);
  } else {
    console.log(`- ${type}: No rate limit configured`);
  }
});
console.log();

// Example 5: Permission checking
console.log("5. Permission Requirements:");
const permissionTypes = MessageTypeRegistry.getMessageTypesByPermission(SEND_MESSAGES);
console.log(`- Types requiring SEND_MESSAGES permission (${permissionTypes.length}):`);
permissionTypes.forEach(type => {
  console.log(`  * ${type.type}`);
});
console.log();

// Example 6: Data validation
console.log("6. Data Validation Examples:");

// Valid message data
const validMessageData = {
  content: "Hello, world!",
  channelId: "123e4567-e89b-12d3-a456-************",
  replyToId: "456e7890-e89b-12d3-a456-************",
};

const messageSchema = MessageTypeRegistry.getDataSchema(EventTypes.MESSAGE_SEND);
if (messageSchema) {
  const validResult = messageSchema.safeParse(validMessageData);
  console.log(`- Valid message data: ${validResult.success ? 'PASS' : 'FAIL'}`);
  
  // Invalid message data
  const invalidMessageData = {
    content: "", // Empty content should fail
    channelId: "invalid-uuid",
  };
  
  const invalidResult = messageSchema.safeParse(invalidMessageData);
  console.log(`- Invalid message data: ${invalidResult.success ? 'PASS' : 'FAIL'}`);
  if (!invalidResult.success) {
    console.log(`  Errors: ${invalidResult.error.issues.map(i => i.message).join(', ')}`);
  }
}
console.log();

// Example 7: Request-Response patterns
console.log("7. Request-Response Patterns:");
const clientToServerTypes = MessageTypeRegistry.getClientToServerMessageTypes()
  .filter(type => type.responseType);

console.log(`- Request types with responses (${clientToServerTypes.length}):`);
clientToServerTypes.slice(0, 5).forEach(type => {
  console.log(`  * ${type.type} → ${type.responseType}`);
});
console.log();

// Example 8: Message targeting capabilities
console.log("8. Message Targeting Capabilities:");
const targetingExamples = [
  EventTypes.MESSAGE_SEND,
  EventTypes.VOICE_JOIN,
  EventTypes.SERVER_UPDATED,
  EventTypes.USER_UPDATED,
];

targetingExamples.forEach(type => {
  const messageType = MessageTypeRegistry.getMessageType(type);
  if (messageType) {
    const capabilities = [];
    if (messageType.supportsChannelTargeting) capabilities.push('Channel');
    if (messageType.supportsUserTargeting) capabilities.push('User');
    if (messageType.supportsServerBroadcast) capabilities.push('Server Broadcast');
    
    console.log(`- ${type}: ${capabilities.length > 0 ? capabilities.join(', ') : 'No targeting'}`);
  }
});
console.log();

// Example 9: Custom message type registration
console.log("9. Custom Message Type Registration:");

const customMessageType: IExtendedMessageTypeDefinition = {
  type: "CUSTOM_NOTIFICATION",
  category: MessageCategory.SYSTEM,
  direction: "server_to_client",
  requiresAuth: false,
  rateLimitGroup: RateLimitGroup.SYSTEM,
  supportsCorrelation: false,
  description: "Custom notification message",
  supportsUserTargeting: true,
  supportsServerBroadcast: true,
};

// Register the custom type
MessageTypeRegistry.registerMessageType("CUSTOM_NOTIFICATION", customMessageType);
console.log("- Registered custom message type: CUSTOM_NOTIFICATION");

// Verify registration
const isRegistered = MessageTypeRegistry.hasMessageType("CUSTOM_NOTIFICATION");
console.log(`- Custom type exists: ${isRegistered}`);

// Get the registered type
const retrievedType = MessageTypeRegistry.getMessageType("CUSTOM_NOTIFICATION");
console.log(`- Retrieved type description: ${retrievedType?.description}`);

// Clean up
MessageTypeRegistry.unregisterMessageType("CUSTOM_NOTIFICATION");
console.log("- Unregistered custom message type");
console.log();

// Example 10: Registry statistics
console.log("10. Registry Statistics:");
const allTypes = MessageTypeRegistry.getAllMessageTypes();
const clientToServer = MessageTypeRegistry.getClientToServerMessageTypes();
const serverToClient = MessageTypeRegistry.getServerToClientMessageTypes();
const bidirectional = MessageTypeRegistry.getBidirectionalMessageTypes();

console.log(`- Total message types: ${allTypes.size}`);
console.log(`- Client to Server: ${clientToServer.length}`);
console.log(`- Server to Client: ${serverToClient.length}`);
console.log(`- Bidirectional: ${bidirectional.length}`);

// Count by category
const categoryCounts = new Map<MessageCategory, number>();
Object.values(MessageCategory).forEach(category => {
  const count = MessageTypeRegistry.getMessageTypesByCategory(category).length;
  if (count > 0) {
    categoryCounts.set(category, count);
  }
});

console.log("- By Category:");
categoryCounts.forEach((count, category) => {
  console.log(`  * ${category}: ${count}`);
});
console.log();

// Example 11: Validation helper
console.log("11. Message Type Definition Validation:");

const validDefinition: IExtendedMessageTypeDefinition = {
  type: "VALID_TEST",
  category: MessageCategory.SYSTEM,
  direction: "bidirectional",
  requiresAuth: true,
  rateLimitGroup: RateLimitGroup.SYSTEM,
  description: "Valid test message",
};

const invalidDefinition = {
  type: "INVALID_TEST",
  category: "INVALID_CATEGORY",
  direction: "invalid_direction",
  requiresAuth: true,
} as IExtendedMessageTypeDefinition;

console.log(`- Valid definition: ${MessageTypeRegistry.validateMessageTypeDefinition(validDefinition) ? 'PASS' : 'FAIL'}`);
console.log(`- Invalid definition: ${MessageTypeRegistry.validateMessageTypeDefinition(invalidDefinition) ? 'PASS' : 'FAIL'}`);
console.log();

console.log("=== Examples Complete ===");

// Export for potential use in other files
export {
  validMessageData,
  customMessageType,
};