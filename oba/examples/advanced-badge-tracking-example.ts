/**
 * Advanced Badge Tracking System - Usage Examples
 * 
 * This file demonstrates how to use the advanced criteria and tracking systems
 * for complex badge requirements including multi-conditions, time-based tracking,
 * geographic/demographic analysis, invitation/referral tracking, and contribution tracking.
 */

import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { AdvancedCriteriaEngine } from "../services/advanced-criteria-engine.service";
import { TimeBasedTrackingService } from "../services/time-based-tracking.service";
import { GeographicDemographicTrackingService } from "../services/geographic-demographic-tracking.service";
import { InvitationReferralTrackingService } from "../services/invitation-referral-tracking.service";
import { FeedbackContributionTrackingService } from "../services/feedback-contribution-tracking.service";
import { AdvancedBadgeTrackingService } from "../services/advanced-badge-tracking.service";

// Database setup (example)
const connectionString = process.env.DATABASE_URL || "postgresql://localhost:5432/oba";
const client = postgres(connectionString);
const db = drizzle(client);

/**
 * Example 1: Complex Multi-Condition Badge Criteria
 * Demonstrates evaluating badges with AND/OR/WEIGHTED logic
 */
async function exampleComplexCriteria() {
  console.log("=== Complex Multi-Condition Badge Criteria ===");
  
  const criteriaEngine = new AdvancedCriteriaEngine(db);
  const userId = "user123";

  // Define a complex badge with multiple conditions
  const communityLeaderCriteria = {
    type: 'multi_condition' as const,
    description: 'Community Leader Badge - requires high activity AND social engagement',
    operator: 'AND' as const,
    conditions: [
      { type: 'message_count' as const, threshold: 1000, operator: 'gte' as const },
      { type: 'friend_count' as const, threshold: 50, operator: 'gte' as const },
      { type: 'account_age' as const, threshold: 365, operator: 'gte' as const },
      { type: 'invites_sent' as const, threshold: 20, operator: 'gte' as const }
    ]
  };

  try {
    const result = await criteriaEngine.evaluateComplexCriteria(userId, communityLeaderCriteria);
    
    console.log(`User ${userId} Community Leader Badge Evaluation:`);
    console.log(`- Meets Criteria: ${result.meetsCriteria}`);
    console.log(`- Progress: ${(result.progress * 100).toFixed(1)}%`);
    console.log(`- Conditions Evaluated: ${result.evaluatedConditions.length}`);
    
    result.evaluatedConditions.forEach((condition, index) => {
      console.log(`  ${index + 1}. ${condition.condition}: ${condition.currentValue}/${condition.threshold} (${condition.met ? '✓' : '✗'})`);
    });
  } catch (error) {
    console.error("Error evaluating complex criteria:", error);
  }
}

/**
 * Example 2: Time-Based Engagement Tracking
 * Demonstrates tracking user engagement patterns over time
 */
async function exampleTimeBasedTracking() {
  console.log("\n=== Time-Based Engagement Tracking ===");
  
  const timeTracker = new TimeBasedTrackingService(db);
  const userId = "user123";

  // Track engagement over different time windows
  const timeWindows = [
    { value: 7, unit: 'days' as const, label: 'Last Week' },
    { value: 30, unit: 'days' as const, label: 'Last Month' },
    { value: 90, unit: 'days' as const, label: 'Last Quarter' }
  ];

  try {
    for (const window of timeWindows) {
      const metrics = await timeTracker.trackEngagementMetrics(userId, window);
      
      console.log(`\n${window.label} Engagement Metrics:`);
      console.log(`- Overall Score: ${(metrics.overallScore * 100).toFixed(1)}%`);
      console.log(`- Messages: ${metrics.messageMetrics.totalMessages} (${metrics.messageMetrics.messagesPerDay.toFixed(1)}/day)`);
      console.log(`- Activity Rate: ${(metrics.activityPatterns.activityRate * 100).toFixed(1)}%`);
      console.log(`- Consistency Score: ${(metrics.consistencyMetrics.consistencyScore * 100).toFixed(1)}%`);
    }

    // Get activity streaks
    const streaks = await timeTracker.getActivityStreaks(userId);
    console.log(`\nActivity Streaks:`);
    console.log(`- Current Streak: ${streaks.currentStreak} days`);
    console.log(`- Longest Streak: ${streaks.longestStreak} days`);
    console.log(`- Total Active Days: ${streaks.totalActiveDays}`);

    // Analyze activity patterns
    const patterns = await timeTracker.trackActivityPatterns(userId, timeWindows[1]);
    console.log(`\nActivity Patterns:`);
    console.log(`- Peak Hours: ${patterns.peakActivityHours.join(', ')}`);
    console.log(`- Activity Pattern: ${patterns.activityPattern}`);
    
  } catch (error) {
    console.error("Error tracking time-based metrics:", error);
  }
}

/**
 * Example 3: Geographic and Demographic Analysis
 * Demonstrates regional badge eligibility and demographic insights
 */
async function exampleGeographicTracking() {
  console.log("\n=== Geographic and Demographic Analysis ===");
  
  const geoTracker = new GeographicDemographicTrackingService(db);
  const userId = "user123";

  try {
    // Track user's geographic profile
    const profile = await geoTracker.trackUserGeographicProfile(userId);
    
    console.log(`Geographic Profile for ${profile.username}:`);
    console.log(`- Primary Region: ${profile.primaryRegion}`);
    console.log(`- Likely Timezone: ${profile.timeZoneActivity.likelyTimezone}`);
    console.log(`- Regional Activity: ${profile.regionalActivity.length} regions`);
    console.log(`- Language Patterns: ${profile.languagePatterns.map(l => l.language).join(', ')}`);
    console.log(`- Community Role: ${profile.demographicInsights.communityRole}`);

    // Check regional badge eligibility
    const regionalBadges = [
      {
        id: 'north_america_pioneer',
        name: 'North America Pioneer',
        description: 'Early adopter and active contributor in North America',
        requirements: {
          region: 'North America',
          minimumActivity: 50,
          communityConnections: 10,
          accountAge: 90
        },
        category: 'regional'
      },
      {
        id: 'global_connector',
        name: 'Global Connector',
        description: 'Bridges communities across multiple regions',
        requirements: {
          communityConnections: 25,
          accountAge: 180,
          culturalBridge: true
        },
        category: 'global'
      }
    ];

    const eligibility = await geoTracker.getRegionalBadgeEligibility(userId, regionalBadges);
    
    console.log(`\nRegional Badge Eligibility:`);
    eligibility.forEach(badge => {
      console.log(`- ${badge.badge.name}: ${badge.isEligible ? '✓ Eligible' : '✗ Not Eligible'} (${(badge.progress * 100).toFixed(1)}%)`);
      console.log(`  Reason: ${badge.eligibilityReason}`);
    });

    // Get regional community leaders
    const leaders = await geoTracker.getRegionalCommunityLeaders(profile.primaryRegion, 5);
    console.log(`\nTop Regional Leaders in ${profile.primaryRegion}:`);
    leaders.forEach((leader, index) => {
      console.log(`${index + 1}. ${leader.username} (Score: ${leader.leadershipScore.toFixed(1)})`);
    });
    
  } catch (error) {
    console.error("Error tracking geographic data:", error);
  }
}

/**
 * Example 4: Invitation and Referral Tracking
 * Demonstrates community building and network growth analysis
 */
async function exampleInvitationTracking() {
  console.log("\n=== Invitation and Referral Tracking ===");
  
  const invitationTracker = new InvitationReferralTrackingService(db);
  const userId = "user123";

  try {
    // Track comprehensive invitation metrics
    const metrics = await invitationTracker.trackUserInvitationMetrics(userId);
    
    console.log(`Invitation Metrics for User ${userId}:`);
    console.log(`- Overall Score: ${metrics.overallScore.toFixed(1)}/100`);
    console.log(`- Invites Sent: ${metrics.invitationStats.totalInvitesSent}`);
    console.log(`- Success Rate: ${(metrics.invitationStats.successRate * 100).toFixed(1)}%`);
    console.log(`- Direct Referrals: ${metrics.referralStats.directReferrals}`);
    console.log(`- Network Size: ${metrics.referralStats.networkSize}`);
    console.log(`- Retention Rate: ${(metrics.referralStats.referralRetentionRate * 100).toFixed(1)}%`);

    // Track referral chains
    const chains = await invitationTracker.trackReferralChains(userId, 3);
    console.log(`\nReferral Chain Analysis:`);
    console.log(`- Total Chains: ${chains.length}`);
    
    chains.slice(0, 3).forEach((chain, index) => {
      console.log(`  Chain ${index + 1}: Depth ${chain.depth}, Active: ${chain.isActive}, Engagement: ${(chain.engagementScore * 100).toFixed(1)}%`);
    });

    // Community building impact
    const impact = await invitationTracker.trackCommunityBuildingImpact(userId);
    console.log(`\nCommunity Building Impact:`);
    console.log(`- Impact Level: ${impact.impactLevel}`);
    console.log(`- Total Impact Score: ${impact.totalImpactScore.toFixed(1)}`);
    console.log(`- Users Referred: ${impact.directImpact.usersReferred}`);
    console.log(`- Network Multiplier: ${impact.indirectImpact.networkMultiplierEffect.toFixed(2)}x`);

    // Recognition eligibility
    const eligibility = impact.recognitionEligibility;
    console.log(`\nRecognition Eligibility:`);
    console.log(`- Community Builder: ${eligibility.communityBuilder ? '✓' : '✗'}`);
    console.log(`- Network Expander: ${eligibility.networkExpander ? '✓' : '✗'}`);
    console.log(`- Retention Champion: ${eligibility.retentionChampion ? '✓' : '✗'}`);
    console.log(`- Invitation Master: ${eligibility.invitationMaster ? '✓' : '✗'}`);
    
  } catch (error) {
    console.error("Error tracking invitation metrics:", error);
  }
}

/**
 * Example 5: Feedback and Contribution Tracking
 * Demonstrates recognition badge eligibility based on contributions
 */
async function exampleContributionTracking() {
  console.log("\n=== Feedback and Contribution Tracking ===");
  
  const contributionTracker = new FeedbackContributionTrackingService(db);
  const userId = "user123";

  try {
    // Track comprehensive contributions
    const contributions = await contributionTracker.trackUserContributions(userId);
    
    console.log(`Contribution Metrics for User ${userId}:`);
    console.log(`- Overall Score: ${contributions.overallScore.toFixed(1)}/100`);
    console.log(`- Recognition Level: ${contributions.recognitionLevel}`);
    console.log(`- Achievements: ${contributions.achievements.length}`);

    console.log(`\nContribution Breakdown:`);
    console.log(`- Feedback Quality: ${contributions.feedbackContributions.qualityScore.toFixed(1)}/100`);
    console.log(`  • Total Feedback: ${contributions.feedbackContributions.totalFeedbackSubmitted}`);
    console.log(`  • Implemented: ${contributions.feedbackContributions.implementedSuggestions}`);
    
    console.log(`- Community Help: ${contributions.communityHelp.helpfulnessScore.toFixed(1)}/100`);
    console.log(`  • Problems Solved: ${contributions.communityHelp.problemsSolved}`);
    console.log(`  • New Users Helped: ${contributions.communityHelp.newUsersHelped}`);
    
    console.log(`- Content Creation: ${contributions.contentContributions.creativeScore.toFixed(1)}/100`);
    console.log(`  • Original Content: ${contributions.contentContributions.originalContent}`);
    console.log(`  • Resources Shared: ${contributions.contentContributions.sharedResources}`);

    console.log(`- Mentorship: ${contributions.mentorshipContributions.impactScore.toFixed(1)}/100`);
    console.log(`  • Users Mentored: ${contributions.mentorshipContributions.usersMentored}`);
    console.log(`  • Success Rate: ${(contributions.mentorshipContributions.successfulMentorships / Math.max(contributions.mentorshipContributions.usersMentored, 1) * 100).toFixed(1)}%`);

    // Track community support specifically
    const support = await contributionTracker.trackCommunitySupport(userId);
    console.log(`\nCommunity Support Analysis:`);
    console.log(`- Support Level: ${support.supportLevel}`);
    console.log(`- Support Score: ${support.supportScore.toFixed(1)}/100`);
    console.log(`- Users Impacted: ${support.impactMetrics.usersImpacted}`);

    // Get contribution leaderboard
    const leaderboard = await contributionTracker.getContributionLeaderboard('overall', undefined, 10);
    console.log(`\nTop Contributors (Overall):`);
    leaderboard.slice(0, 5).forEach(entry => {
      console.log(`${entry.rank}. ${entry.username} - Score: ${entry.categoryScore.toFixed(1)} (${entry.recognitionLevel})`);
    });
    
  } catch (error) {
    console.error("Error tracking contributions:", error);
  }
}

/**
 * Example 6: Comprehensive Advanced Badge Evaluation
 * Demonstrates the integrated advanced badge tracking system
 */
async function exampleAdvancedBadgeEvaluation() {
  console.log("\n=== Comprehensive Advanced Badge Evaluation ===");
  
  const advancedTracker = new AdvancedBadgeTrackingService(db);
  const userId = "user123";

  try {
    // Comprehensive evaluation
    const evaluation = await advancedTracker.evaluateUserForAdvancedBadges(userId);
    
    console.log(`Advanced Badge Evaluation for User ${userId}:`);
    console.log(`- Overall Advanced Score: ${evaluation.overallAdvancedScore.toFixed(1)}/100`);
    console.log(`- Eligible Badges: ${evaluation.eligibleBadges.length}`);
    console.log(`- Criteria Results: ${evaluation.criteriaResults.length}`);
    console.log(`- Engagement Periods: ${evaluation.engagementMetrics.length}`);

    console.log(`\nEligible Advanced Badges:`);
    evaluation.eligibleBadges.forEach(badge => {
      console.log(`- ${badge.badgeName} (${badge.category})`);
      console.log(`  Eligibility: ${(badge.eligibilityScore * 100).toFixed(1)}%`);
      console.log(`  Progress: ${(badge.progress * 100).toFixed(1)}%`);
      console.log(`  Requirements: ${badge.requirements.join(', ')}`);
    });

    // Get personalized recommendations
    const recommendations = await advancedTracker.getAdvancedBadgeRecommendations(userId);
    
    console.log(`\nPersonalized Badge Recommendations:`);
    console.log(`- Near Completion: ${recommendations.nearCompletionBadges.length} badges`);
    console.log(`- Strategic: ${recommendations.strategicBadges.length} badges`);
    console.log(`- Community Impact: ${recommendations.communityImpactBadges.length} badges`);

    console.log(`\nPriority Recommendations:`);
    recommendations.priorityRecommendations.slice(0, 3).forEach((rec, index) => {
      console.log(`${index + 1}. ${rec.badgeName} (${rec.category})`);
      console.log(`   Priority: ${rec.priority}, Effort: ${rec.estimatedEffort}, Impact: ${rec.potentialImpact}`);
      console.log(`   Actions: ${rec.actionItems.join(', ')}`);
    });

    // Track progress over time
    const timeWindow = { value: 90, unit: 'days' as const, label: 'Last Quarter' };
    const progress = await advancedTracker.trackAdvancedBadgeProgress(userId, timeWindow);
    
    console.log(`\nProgress Tracking (${timeWindow.label}):`);
    console.log(`- Progress Score: ${progress.progressScore.toFixed(1)}/100`);
    console.log(`- Trend: ${progress.historicalTrends.trendDirection}`);
    console.log(`- Consistency: ${(progress.historicalTrends.consistencyScore * 100).toFixed(1)}%`);
    console.log(`- Next Milestones: ${progress.nextMilestones.length}`);

    progress.nextMilestones.forEach((milestone, index) => {
      console.log(`  ${index + 1}. ${milestone.milestone.name} (${milestone.priority} priority)`);
      console.log(`     Progress: ${(milestone.milestone.progress * 100).toFixed(1)}%`);
      console.log(`     Actions: ${milestone.actionItems.join(', ')}`);
    });
    
  } catch (error) {
    console.error("Error in advanced badge evaluation:", error);
  }
}

/**
 * Example 7: Community-Wide Analysis
 * Demonstrates platform-wide badge pattern analysis
 */
async function exampleCommunityAnalysis() {
  console.log("\n=== Community-Wide Badge Analysis ===");
  
  const advancedTracker = new AdvancedBadgeTrackingService(db);

  try {
    const analysis = await advancedTracker.analyzeCommunityAdvancedBadgePatterns();
    
    console.log(`Community Advanced Badge Analysis:`);
    console.log(`- Total Advanced Badges: ${analysis.distributionAnalysis.totalAdvancedBadges}`);
    console.log(`- Monthly Growth: ${(analysis.trendAnalysis.monthlyGrowth * 100).toFixed(1)}%`);
    console.log(`- Badge Holder Retention: ${(analysis.communityHealth.advancedBadgeHolderRetention * 100).toFixed(1)}%`);

    console.log(`\nBadge Distribution by Category:`);
    Object.entries(analysis.distributionAnalysis.badgesByCategory).forEach(([category, count]) => {
      console.log(`- ${category}: ${count} badges`);
    });

    console.log(`\nCategory Trends:`);
    Object.entries(analysis.trendAnalysis.categoryTrends).forEach(([category, trend]) => {
      console.log(`- ${category}: ${trend}`);
    });

    console.log(`\nCommunity Health Metrics:`);
    console.log(`- Leadership Diversity: ${(analysis.communityHealth.communityLeadershipDiversity * 100).toFixed(1)}%`);
    console.log(`- New User Advancement: ${(analysis.communityHealth.newUserAdvancementRate * 100).toFixed(1)}%`);
    console.log(`- Engagement Quality: ${(analysis.communityHealth.overallEngagementQuality * 100).toFixed(1)}%`);

    console.log(`\nKey Insights:`);
    analysis.insights.forEach((insight, index) => {
      console.log(`${index + 1}. ${insight}`);
    });

    console.log(`\nRecommendations:`);
    analysis.recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`);
    });
    
  } catch (error) {
    console.error("Error in community analysis:", error);
  }
}

/**
 * Main execution function
 */
async function runExamples() {
  console.log("🏆 Advanced Badge Tracking System - Examples\n");
  
  try {
    await exampleComplexCriteria();
    await exampleTimeBasedTracking();
    await exampleGeographicTracking();
    await exampleInvitationTracking();
    await exampleContributionTracking();
    await exampleAdvancedBadgeEvaluation();
    await exampleCommunityAnalysis();
    
    console.log("\n✅ All examples completed successfully!");
    
  } catch (error) {
    console.error("❌ Error running examples:", error);
  } finally {
    // Close database connection
    await client.end();
  }
}

/**
 * Utility function to demonstrate badge criteria creation
 */
function createExampleBadgeCriteria() {
  console.log("\n=== Example Badge Criteria Definitions ===");
  
  const badgeCriteria = {
    // Simple multi-condition badge
    communityContributor: {
      type: 'multi_condition' as const,
      description: 'Community Contributor - active participation',
      operator: 'AND' as const,
      conditions: [
        { type: 'message_count' as const, threshold: 500, operator: 'gte' as const },
        { type: 'friend_count' as const, threshold: 25, operator: 'gte' as const },
        { type: 'account_age' as const, threshold: 180, operator: 'gte' as const }
      ]
    },

    // Time-based consistency badge
    consistentEngager: {
      type: 'time_based' as const,
      description: 'Consistent Engager - regular activity over time',
      timeWindow: '60d',
      conditions: [
        { metric: 'messages_per_day' as const, threshold: 3 },
        { metric: 'active_days' as const, threshold: 45 },
        { metric: 'engagement_consistency' as const, threshold: 0.6 }
      ],
      minimumConditions: 2
    },

    // Social network badge
    networkBuilder: {
      type: 'social_network' as const,
      description: 'Network Builder - strong social connections',
      metrics: [
        { type: 'friend_network_size' as const, threshold: 75 },
        { type: 'network_influence' as const, threshold: 40 },
        { type: 'referral_success' as const, threshold: 15 }
      ],
      minimumMetrics: 2
    },

    // Engagement pattern badge
    communityHelper: {
      type: 'engagement_pattern' as const,
      description: 'Community Helper - helpful interaction patterns',
      patterns: [
        { type: 'interaction_diversity' as const, threshold: 4 },
        { type: 'response_rate' as const, threshold: 0.7 },
        { type: 'community_helper' as const, threshold: 25 }
      ],
      minimumPatterns: 2
    },

    // Achievement chain badge
    badgeCollector: {
      type: 'achievement_chain' as const,
      description: 'Badge Collector - progression through badge system',
      chain: [
        { type: 'prerequisite_badges' as const, badgeIds: ['first_message', 'first_friend', 'first_server'] },
        { type: 'badge_category_count' as const, categories: ['social', 'activity'], threshold: 3 },
        { type: 'time_between_badges' as const, threshold: 30 }
      ],
      minimumRequirements: 2
    },

    // Conditional threshold badge
    adaptiveAchiever: {
      type: 'conditional_threshold' as const,
      description: 'Adaptive Achiever - dynamic requirements based on user profile',
      metric: 'message_count' as const,
      baseThreshold: 1000,
      conditions: [
        { type: 'account_age_modifier' as const, accountAgeThreshold: 365, thresholdModifier: -200 },
        { type: 'activity_level_modifier' as const, activityThreshold: 500, thresholdModifier: -100 },
        { type: 'social_modifier' as const, socialThreshold: 50, thresholdModifier: -150 }
      ]
    }
  };

  console.log("Example badge criteria created:");
  Object.entries(badgeCriteria).forEach(([name, criteria]) => {
    console.log(`- ${name}: ${criteria.description}`);
  });

  return badgeCriteria;
}

// Run examples if this file is executed directly
if (import.meta.main) {
  createExampleBadgeCriteria();
  runExamples();
}

export {
  exampleComplexCriteria,
  exampleTimeBasedTracking,
  exampleGeographicTracking,
  exampleInvitationTracking,
  exampleContributionTracking,
  exampleAdvancedBadgeEvaluation,
  exampleCommunityAnalysis,
  createExampleBadgeCriteria
};