import { WebSocketValidator } from "../utils/websocket-validator";
import { MESSAGE_TYPE_DEFINITIONS } from "../utils/websocket-schemas";
import { EventTypes } from "@kurultai/oba-types";
import {
  type IWebSocketMessage,
  WebSocketErrorCode,
} from "../types/websocket-standardization.types";

/**
 * Example demonstrating how to use the WebSocket validation system
 */

// Initialize the validator with predefined schemas
console.log("Initializing WebSocket validation system...");

// Register all predefined message type definitions
for (const [type, definition] of MESSAGE_TYPE_DEFINITIONS) {
  WebSocketValidator.registerSchema(type, definition);
}

console.log(`Registered ${WebSocketValidator.getRegisteredTypes().length} message types`);

// Example 1: Valid message validation
console.log("\n=== Example 1: Valid Message ===");

const validMessage: IWebSocketMessage = {
  type: EventTypes.MESSAGE_SEND,
  data: {
    content: "Hello, world!",
    channelId: "123e4567-e89b-12d3-a456-426614174000",
    serverId: "123e4567-e89b-12d3-a456-************",
  },
  meta: {
    timestamp: new Date(),
    messageId: "msg-12345",
    version: "1.0.0",
    source: "client",
  },
};

const validResult = WebSocketValidator.validate(validMessage);
console.log("Validation result:", validResult);

// Example 2: Invalid message structure
console.log("\n=== Example 2: Invalid Message Structure ===");

const invalidStructureMessage = {
  // Missing type and meta
  data: { content: "Hello" },
};

const structureResult = WebSocketValidator.validate(invalidStructureMessage);
console.log("Validation result:", structureResult);

// Example 3: Invalid message data
console.log("\n=== Example 3: Invalid Message Data ===");

const invalidDataMessage: IWebSocketMessage = {
  type: EventTypes.MESSAGE_SEND,
  data: {
    content: "", // Empty content not allowed
    channelId: "invalid-uuid", // Invalid UUID format
    serverId: "123e4567-e89b-12d3-a456-************",
  },
  meta: {
    timestamp: new Date(),
    messageId: "msg-12345",
    version: "1.0.0",
    source: "client",
  },
};

const dataResult = WebSocketValidator.validate(invalidDataMessage);
console.log("Validation result:", dataResult);

// Example 4: Unknown message type
console.log("\n=== Example 4: Unknown Message Type ===");

const unknownTypeMessage: IWebSocketMessage = {
  type: 999, // Unknown message type (not in EventTypes enum)
  data: { test: "data" },
  meta: {
    timestamp: new Date(),
    messageId: "msg-12345",
    version: "1.0.0",
    source: "client",
  },
};

const unknownResult = WebSocketValidator.validate(unknownTypeMessage);
console.log("Validation result:", unknownResult);

// Example 5: Message size validation
console.log("\n=== Example 5: Message Size Validation ===");

const largeMessage: IWebSocketMessage = {
  type: EventTypes.MESSAGE_SEND,
  data: {
    content: "x".repeat(3000), // Very long content
    channelId: "123e4567-e89b-12d3-a456-426614174000",
    serverId: "123e4567-e89b-12d3-a456-************",
  },
  meta: {
    timestamp: new Date(),
    messageId: "msg-12345",
    version: "1.0.0",
    source: "client",
  },
};

const sizeResult = WebSocketValidator.validate(largeMessage);
console.log("Validation result:", sizeResult);

// Example 6: Heartbeat message validation
console.log("\n=== Example 6: Heartbeat Message ===");

const heartbeatMessage: IWebSocketMessage = {
  type: EventTypes.PING,
  data: {
    timestamp: new Date(),
  },
  meta: {
    timestamp: new Date(),
    messageId: "heartbeat-12345",
    version: "1.0.0",
    source: "client",
  },
};

const heartbeatResult = WebSocketValidator.validate(heartbeatMessage);
console.log("Validation result:", heartbeatResult);

// Example 7: Auth login message validation
console.log("\n=== Example 7: Auth Login Message ===");

const authMessage: IWebSocketMessage = {
  type: EventTypes.AUTHENTICATE,
  data: {
    username: "testuser",
    password: "testpassword",
    rememberMe: true,
  },
  meta: {
    timestamp: new Date(),
    messageId: "auth-12345",
    version: "1.0.0",
    source: "client",
  },
};

const authResult = WebSocketValidator.validate(authMessage);
console.log("Validation result:", authResult);

// Example 8: Data-only validation
console.log("\n=== Example 8: Data-Only Validation ===");

const messageData = {
  content: "Test message",
  channelId: "123e4567-e89b-12d3-a456-426614174000",
  serverId: "123e4567-e89b-12d3-a456-************",
};

const dataOnlyResult = WebSocketValidator.validateEventData(EventTypes.MESSAGE_SEND, messageData);
console.log("Data validation result:", dataOnlyResult);

// Example 9: Schema information
console.log("\n=== Example 9: Schema Information ===");

const messageSendDefinition = WebSocketValidator.getSchemaDefinition(EventTypes.MESSAGE_SEND);
console.log("MESSAGE_SEND definition:", {
  type: messageSendDefinition?.type,
  category: messageSendDefinition?.category,
  requiresAuth: messageSendDefinition?.requiresAuth,
  requiresPermission: messageSendDefinition?.requiresPermission,
  maxSize: messageSendDefinition?.maxSize,
});

console.log("\nAll registered types:", WebSocketValidator.getRegisteredTypes());

console.log("\n=== Validation System Demo Complete ===");