/**
 * Enhanced WebSocket Manager Example
 * 
 * This example demonstrates the enhanced WebSocket manager features:
 * - Standardized message handling
 * - Subscription management
 * - Rate limiting
 * - Heartbeat management
 * - Correlation tracking
 */

import { WebSocketManager } from "../manager/websocket.manager";
import { WebSocketUtils } from "../utils/websocket-utils";
import { WebSocketErrorCode } from "../types/websocket-standardization.types";
import type { ServerWebSocket } from "bun";
import type { CustomWebSocketData } from "../types/websocket.types";

// Example usage of the enhanced WebSocket manager
export class EnhancedWebSocketManagerExample {
  private manager: WebSocketManager;

  constructor() {
    this.manager = WebSocketManager.getInstance();
  }

  /**
   * Example: Sending standardized messages
   */
  async demonstrateStandardizedMessages(ws: ServerWebSocket<CustomWebSocketData>) {
    console.log("=== Standardized Message Handling ===");

    // Send a success message
    const successMessage = WebSocketUtils.success("USER_PROFILE_UPDATED", {
      userId: ws.data.userId,
      username: "john_doe",
      avatar: "https://example.com/avatar.jpg",
    });
    this.manager.sendStandardMessage(ws, successMessage);

    // Send an error message
    const errorMessage = WebSocketUtils.error(
      WebSocketErrorCode.PERMISSION_DENIED,
      "You don't have permission to access this resource",
      {
        details: {
          resource: "admin_panel",
          requiredPermission: "ADMIN_ACCESS",
        },
      }
    );
    this.manager.sendStandardMessage(ws, errorMessage);

    // Send an event message
    const eventMessage = WebSocketUtils.event("NEW_MESSAGE", {
      messageId: "msg_123",
      channelId: "channel_456",
      content: "Hello, world!",
      author: {
        userId: "user_789",
        username: "alice",
      },
    });
    this.manager.sendStandardMessage(ws, eventMessage);
  }

  /**
   * Example: Subscription management
   */
  async demonstrateSubscriptionManagement(ws: ServerWebSocket<CustomWebSocketData>) {
    console.log("=== Subscription Management ===");

    // Subscribe to various topics
    this.manager.subscribe(ws, "server:123:general");
    this.manager.subscribe(ws, "user:notifications");
    this.manager.subscribe(ws, "voice:channel:456");

    // Later, unsubscribe from a topic
    setTimeout(() => {
      this.manager.unsubscribe(ws, "voice:channel:456");
    }, 5000);
  }

  /**
   * Example: Rate limiting demonstration
   */
  async demonstrateRateLimiting(ws: ServerWebSocket<CustomWebSocketData>) {
    console.log("=== Rate Limiting ===");

    // Check current rate limit status
    const canSend = this.manager.checkRateLimit(ws);
    console.log(`Can send message: ${canSend}`);

    // Simulate rapid message sending to trigger rate limiting
    for (let i = 0; i < 105; i++) { // Exceed the default limit of 100
      const testMessage = WebSocketUtils.success("TEST_MESSAGE", {
        messageNumber: i + 1,
        timestamp: new Date(),
      });
      
      this.manager.sendStandardMessage(ws, testMessage);
      
      // After the rate limit is exceeded, subsequent messages will be blocked
      if (i > 100) {
        console.log(`Message ${i + 1}: Rate limited`);
      }
    }
  }

  /**
   * Example: Heartbeat management
   */
  async demonstrateHeartbeat(ws: ServerWebSocket<CustomWebSocketData>) {
    console.log("=== Heartbeat Management ===");

    // Start heartbeat monitoring
    this.manager.startHeartbeat(ws);
    console.log("Heartbeat monitoring started");

    // Simulate heartbeat response
    setTimeout(() => {
      this.manager.handleHeartbeat(ws);
      console.log("Heartbeat handled");
    }, 2000);

    // Stop heartbeat after 10 seconds
    setTimeout(() => {
      this.manager.stopHeartbeat(ws.data.userId);
      console.log("Heartbeat monitoring stopped");
    }, 10000);
  }

  /**
   * Example: Request-response correlation
   */
  async demonstrateCorrelation(ws: ServerWebSocket<CustomWebSocketData>) {
    console.log("=== Correlation Tracking ===");

    try {
      // Create a request message
      const requestMessage = WebSocketUtils.success("GET_USER_PROFILE", {
        userId: "user_123",
      });

      // Send request and wait for response
      console.log("Sending request with correlation tracking...");
      const responsePromise = this.manager.sendRequest(ws, requestMessage, 5000);

      // Simulate a response after 2 seconds
      setTimeout(() => {
        const responseMessage = WebSocketUtils.success("USER_PROFILE_DATA", {
          userId: "user_123",
          username: "john_doe",
          email: "<EMAIL>",
          avatar: "https://example.com/avatar.jpg",
        });
        
        // Set the correlation ID to match the request
        responseMessage.meta.correlationId = requestMessage.meta.correlationId;
        
        // Handle the correlated response
        this.manager.handleCorrelatedResponse(ws, responseMessage);
      }, 2000);

      // Wait for the response
      const response = await responsePromise;
      console.log("Received correlated response:", response.data);
    } catch (error) {
      console.error("Correlation error:", error);
    }
  }

  /**
   * Example: Configuration management
   */
  demonstrateConfiguration() {
    console.log("=== Configuration Management ===");

    // Get current configuration
    const config = this.manager.getConfig();
    console.log("Current configuration:", {
      messageVersion: config.messageVersion,
      heartbeatInterval: config.heartbeatInterval,
      enableCorrelation: config.enableCorrelation,
      enableValidation: config.enableValidation,
    });

    // Update configuration
    this.manager.updateConfig({
      heartbeatInterval: 45000, // 45 seconds
      rateLimits: {
        ...config.rateLimits,
        messaging: { maxRequests: 30, windowMs: 60000 }, // Stricter limit for messaging
      },
    });

    console.log("Configuration updated");
  }

  /**
   * Example: Metrics monitoring
   */
  demonstrateMetrics() {
    console.log("=== Metrics Monitoring ===");

    const metrics = this.manager.getMetrics();
    console.log("Current metrics:", {
      activeConnections: metrics.activeConnections,
      messagesPerSecond: metrics.messagesPerSecond,
      rateLimitHits: metrics.rateLimitHits,
      correlationTimeouts: metrics.correlationTimeouts,
      validationFailures: metrics.validationFailures,
    });

    // Set up periodic metrics logging
    setInterval(() => {
      const currentMetrics = this.manager.getMetrics();
      console.log("Metrics update:", {
        activeConnections: currentMetrics.activeConnections,
        rateLimitHits: currentMetrics.rateLimitHits,
        errorRate: currentMetrics.errorRate,
      });
    }, 30000); // Every 30 seconds
  }

  /**
   * Example: Enhanced connection lifecycle
   */
  async demonstrateConnectionLifecycle(ws: ServerWebSocket<CustomWebSocketData>) {
    console.log("=== Enhanced Connection Lifecycle ===");

    // Add connection with enhanced features
    this.manager.add(ws);
    console.log("Connection added with enhanced features");

    // Simulate some activity
    await this.demonstrateStandardizedMessages(ws);
    await this.demonstrateSubscriptionManagement(ws);

    // Remove connection with enhanced cleanup
    setTimeout(() => {
      this.manager.remove(ws);
      console.log("Connection removed with enhanced cleanup");
    }, 15000);
  }

  /**
   * Run all examples
   */
  async runAllExamples(ws: ServerWebSocket<CustomWebSocketData>) {
    console.log("🚀 Starting Enhanced WebSocket Manager Examples");
    console.log("================================================");

    try {
      await this.demonstrateStandardizedMessages(ws);
      await new Promise(resolve => setTimeout(resolve, 1000));

      await this.demonstrateSubscriptionManagement(ws);
      await new Promise(resolve => setTimeout(resolve, 1000));

      await this.demonstrateRateLimiting(ws);
      await new Promise(resolve => setTimeout(resolve, 1000));

      await this.demonstrateHeartbeat(ws);
      await new Promise(resolve => setTimeout(resolve, 1000));

      await this.demonstrateCorrelation(ws);
      await new Promise(resolve => setTimeout(resolve, 1000));

      this.demonstrateConfiguration();
      await new Promise(resolve => setTimeout(resolve, 1000));

      this.demonstrateMetrics();
      await new Promise(resolve => setTimeout(resolve, 1000));

      await this.demonstrateConnectionLifecycle(ws);

      console.log("✅ All examples completed successfully!");
    } catch (error) {
      console.error("❌ Example error:", error);
    }
  }
}

// Usage example:
// const example = new EnhancedWebSocketManagerExample();
// example.runAllExamples(websocketConnection);