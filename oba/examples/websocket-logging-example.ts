/**
 * WebSocket Logging and Debugging Example
 * 
 * This example demonstrates how to use the comprehensive logging and debugging
 * utilities for WebSocket standardization.
 */

import type { ServerWebSocket } from "bun";
import type { CustomWebSocketData } from "../types/websocket.types";
import { WebSocketUtils } from "../utils/websocket-utils";
import { webSocketLogger } from "../utils/websocket-logger";
import { webSocketDebugger } from "../utils/websocket-debug";
import { webSocketMetrics } from "../utils/websocket-metrics";
import { correlationTracker } from "../utils/correlation-tracker";

/**
 * Example: Basic message logging with correlation tracking
 */
export function exampleBasicLogging(ws: ServerWebSocket<CustomWebSocketData>) {
  console.log("=== Basic WebSocket Logging Example ===");

  // Log connection event
  webSocketLogger.logConnection("connect", ws, {
    userAgent: "Example Client",
    ipAddress: "127.0.0.1",
  });

  // Simulate receiving a message
  const inboundMessage = {
    type: "MESSAGE_SEND",
    data: { content: "Hello, world!", channelId: "channel-123" },
    meta: {
      messageId: "msg-123",
      timestamp: new Date(),
      version: "1.0.0",
      source: "client" as const,
    },
  };

  // Log inbound message
  webSocketLogger.logMessage("inbound", ws, inboundMessage);

  console.log("Basic logging example completed");
}