/**
 * Badge Visual System Example
 * 
 * This example demonstrates how to use the enhanced badge visual system
 * with SVG rendering, animations, responsive design, and accessibility features.
 */

import { BadgeVisualRenderer } from "../utils/badge-visual-renderer";
import { BadgeDisplayComponents } from "../utils/badge-display-components";
import { BadgeAccessibility } from "../utils/badge-accessibility";
import { BadgeType, UserBadge, BadgeCollection, UserCollectionProgress } from "../types/badge.types";

// Example badge types with rich visual designs
const exampleBadgeTypes: BadgeType[] = [
  {
    id: "badge-1",
    collectionId: "communication-collection",
    badgeId: "first-message",
    name: "First Message",
    title: "Communication Starter",
    description: "Sent your first message in the community",
    icon: "💬",
    tooltip: "Welcome to the community! This badge celebrates your first contribution to our discussions.",
    design: {
      shape: "circle",
      background: "#4CAF50",
      colors: ["#4CAF50", "#2E7D32", "#1B5E20"],
      gradient: "linear",
      pattern: "dots",
      elements: ["border", "glow"]
    },
    criteria: {
      requirement: "Send your first message",
      tracked: "message_count",
      type: "message_count",
      threshold: 1
    },
    perks: ["Special chat color", "Welcome badge visibility"],
    unlockType: "automatic",
    visualDescription: "Green circular badge with chat bubble icon and subtle glow effect",
    animation: "pulse",
    displayOrder: 1,
    category: "milestone",
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "badge-2",
    collectionId: "communication-collection",
    badgeId: "active-member",
    name: "Active Member",
    title: "Community Regular",
    description: "Active in the community for 30 consecutive days",
    icon: "⭐",
    tooltip: "You're a valued community member! Your consistent participation makes our community stronger.",
    design: {
      shape: "star",
      background: "#FF9800",
      colors: ["#FF9800", "#F57C00", "#E65100"],
      gradient: "radial",
      pattern: "sparkles",
      elements: ["glow", "sparkles"]
    },
    criteria: {
      requirement: "Be active for 30 consecutive days",
      tracked: "days_active",
      type: "days_active",
      threshold: 30
    },
    perks: ["Priority support", "Special role color", "Early access to features"],
    unlockType: "automatic",
    visualDescription: "Orange star-shaped badge with sparkle effects and radial gradient",
    animation: "glow",
    displayOrder: 2,
    category: "achievement",
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "badge-3",
    collectionId: null,
    badgeId: "moderator",
    name: "Moderator",
    title: "Community Guardian",
    description: "Trusted community moderator with special privileges",
    icon: "🛡️",
    tooltip: "This badge identifies trusted community moderators who help keep our space safe and welcoming.",
    design: {
      shape: "shield",
      background: "#9C27B0",
      colors: ["#9C27B0", "#6A1B9A", "#4A148C"],
      gradient: "linear",
      pattern: "stripes",
      elements: ["border", "glow"]
    },
    criteria: {
      requirement: "Manually assigned by administrators",
      tracked: "none"
    },
    perks: ["Moderation tools", "Special badge prominence", "Priority access", "Exclusive channels"],
    unlockType: "manual",
    visualDescription: "Purple shield-shaped badge with diagonal stripes and strong border",
    animation: "none",
    displayOrder: 0,
    category: "role",
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "badge-4",
    collectionId: "communication-collection",
    badgeId: "conversation-master",
    name: "Conversation Master",
    title: "Discussion Leader",
    description: "Started 50 meaningful conversations",
    icon: "🎯",
    tooltip: "Your ability to spark engaging discussions is remarkable! Keep leading great conversations.",
    design: {
      shape: "hexagon",
      background: "#2196F3",
      colors: ["#2196F3", "#1976D2", "#0D47A1"],
      gradient: "linear",
      elements: ["border"]
    },
    criteria: {
      requirement: "Start 50 conversations with 5+ replies each",
      tracked: "conversations_started",
      type: "complex",
      conditions: {
        conversations_started: 50,
        min_replies_per_conversation: 5
      }
    },
    perks: ["Conversation starter badge", "Featured discussions", "Topic suggestion privileges"],
    unlockType: "automatic",
    visualDescription: "Blue hexagonal badge with target icon representing precision in conversation starting",
    animation: "bounce",
    displayOrder: 3,
    category: "achievement",
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Example user badges
const exampleUserBadges: UserBadge[] = [
  {
    id: "user-badge-1",
    userId: "user-123",
    badgeTypeId: "badge-1",
    collectionId: "communication-collection",
    assignedAt: new Date("2024-01-15"),
    isVisible: true,
    progressData: { messages_sent: 1 },
    perksGranted: ["Special chat color"],
    badgeType: exampleBadgeTypes[0]
  },
  {
    id: "user-badge-2",
    userId: "user-123",
    badgeTypeId: "badge-2",
    collectionId: "communication-collection",
    assignedAt: new Date("2024-02-14"),
    isVisible: true,
    progressData: { days_active: 30 },
    perksGranted: ["Priority support", "Special role color"],
    badgeType: exampleBadgeTypes[1]
  },
  {
    id: "user-badge-3",
    userId: "user-123",
    badgeTypeId: "badge-3",
    collectionId: null,
    assignedBy: "admin-456",
    assignedAt: new Date("2024-03-01"),
    isVisible: true,
    perksGranted: ["Moderation tools", "Special badge prominence"],
    badgeType: exampleBadgeTypes[2]
  }
];

// Example collection
const exampleCollection: BadgeCollection = {
  id: "communication-collection",
  collectionId: "communication-journey",
  name: "Communication Journey",
  description: "Master the art of community communication through this progressive badge collection",
  type: "progressive",
  totalBadges: 5,
  unlockedBy: "activity_and_engagement",
  completionReward: {
    badge: "Communication Master",
    title: "Master Communicator",
    perks: ["Golden chat color", "VIP access", "Exclusive events"],
    visual: "Golden crown with communication symbols and royal effects",
    animation: "royal-glow"
  },
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
};

// Example collection progress
const exampleProgress: UserCollectionProgress = {
  id: "progress-1",
  userId: "user-123",
  collectionId: "communication-collection",
  badgesEarned: 3,
  totalBadges: 5,
  isCompleted: false,
  completionRewardGranted: false,
  createdAt: new Date(),
  updatedAt: new Date()
};

/**
 * Example 1: Basic Badge SVG Rendering
 */
export function renderBasicBadgeExample() {
  console.log("=== Basic Badge SVG Rendering ===");
  
  const badge = exampleBadgeTypes[0]; // First Message badge
  
  // Render badge with different sizes and options
  const sizes: Array<'small' | 'medium' | 'large' | 'xl'> = ['small', 'medium', 'large', 'xl'];
  
  sizes.forEach(size => {
    const result = BadgeVisualRenderer.renderBadgeSVG(badge, {
      size,
      animation: true,
      accessibility: true
    });
    
    console.log(`\n${size.toUpperCase()} Badge:`);
    console.log("SVG:", result.svg.substring(0, 200) + "...");
    console.log("Has Animation CSS:", !!result.css);
    console.log("Accessibility Alt:", result.accessibility.alt);
  });
}

/**
 * Example 2: Badge Design Validation
 */
export function validateBadgeDesignsExample() {
  console.log("\n=== Badge Design Validation ===");
  
  exampleBadgeTypes.forEach(badge => {
    const design = typeof badge.design === 'string' 
      ? JSON.parse(badge.design) 
      : badge.design;
    
    const validation = BadgeVisualRenderer.validateBadgeDesign(design);
    
    console.log(`\nBadge: ${badge.name}`);
    console.log("Valid Design:", validation.isValid);
    if (!validation.isValid) {
      console.log("Errors:", validation.errors);
    }
  });
}

/**
 * Example 3: Responsive Badge Grid
 */
export function createResponsiveBadgeGridExample() {
  console.log("\n=== Responsive Badge Grid ===");
  
  const result = BadgeDisplayComponents.createBadgeGrid(exampleUserBadges, {
    maxVisible: 10,
    showTooltips: true,
    responsive: true,
    groupByCollection: true,
    accessibility: true,
    theme: 'auto'
  });
  
  console.log("HTML Length:", result.html.length);
  console.log("CSS Length:", result.css.length);
  console.log("Has JavaScript:", !!result.javascript);
  console.log("Accessibility Info:", result.accessibility);
  
  // Show responsive breakpoints in CSS
  const mobileBreakpoint = result.css.includes('@media (max-width: 768px)');
  const tabletBreakpoint = result.css.includes('@media (max-width: 1200px)');
  
  console.log("Mobile Responsive:", mobileBreakpoint);
  console.log("Tablet Responsive:", tabletBreakpoint);
}

/**
 * Example 4: Compact Badge List for Profile Cards
 */
export function createCompactBadgeListExample() {
  console.log("\n=== Compact Badge List ===");
  
  // Create more badges to test the "more" indicator
  const manyBadges = [
    ...exampleUserBadges,
    ...Array.from({ length: 5 }, (_, i) => ({
      ...exampleUserBadges[0],
      id: `extra-badge-${i}`,
      badgeTypeId: `extra-type-${i}`
    }))
  ];
  
  const result = BadgeDisplayComponents.createCompactBadgeList(manyBadges, 3);
  
  console.log("Compact List HTML Length:", result.html.length);
  console.log("Shows More Indicator:", result.html.includes('more-indicator'));
  console.log("Accessibility:", result.accessibility);
}

/**
 * Example 5: Collection Progress Display
 */
export function createCollectionProgressExample() {
  console.log("\n=== Collection Progress Display ===");
  
  const earnedBadges = exampleUserBadges.filter(badge => 
    badge.collectionId === "communication-collection"
  );
  
  const result = BadgeDisplayComponents.createCollectionProgressDisplay(
    exampleCollection,
    exampleProgress,
    earnedBadges
  );
  
  console.log("Progress Display HTML Length:", result.html.length);
  console.log("Shows Progress Bar:", result.html.includes('progress-bar'));
  console.log("Shows Badge Slots:", result.html.includes('badge-slot'));
  console.log("Collection Complete:", result.html.includes('Collection Complete'));
}

/**
 * Example 6: Accessibility Features
 */
export function demonstrateAccessibilityExample() {
  console.log("\n=== Accessibility Features ===");
  
  exampleBadgeTypes.forEach(badge => {
    // Generate accessibility attributes
    const accessibility = BadgeAccessibility.generateAccessibilityAttributes(badge, {
      includeAriaLabels: true,
      includeDescriptions: true,
      includeKeyboardNavigation: true,
      includeHighContrast: true,
      includeReducedMotion: true,
      language: 'en'
    });
    
    console.log(`\nBadge: ${badge.name}`);
    console.log("ARIA Label:", accessibility.attributes['aria-label']);
    console.log("Has Keyboard Support:", accessibility.javascript.includes('keydown'));
    console.log("Has High Contrast CSS:", accessibility.css.includes('prefers-contrast'));
    console.log("Has Reduced Motion CSS:", accessibility.css.includes('prefers-reduced-motion'));
    
    // Validate accessibility compliance
    const validation = BadgeAccessibility.validateAccessibilityCompliance(badge);
    console.log("Accessibility Compliant:", validation.isCompliant);
    if (!validation.isCompliant) {
      console.log("Issues:", validation.issues);
    }
    if (validation.recommendations.length > 0) {
      console.log("Recommendations:", validation.recommendations.slice(0, 2)); // Show first 2
    }
  });
}

/**
 * Example 7: Color Contrast Analysis
 */
export function analyzeColorContrastExample() {
  console.log("\n=== Color Contrast Analysis ===");
  
  exampleBadgeTypes.forEach(badge => {
    const design = typeof badge.design === 'string' 
      ? JSON.parse(badge.design) 
      : badge.design;
    
    if (design.colors && design.colors.length >= 2) {
      const contrast = BadgeAccessibility.analyzeColorContrast(
        design.colors[0],
        design.colors[1],
        16 // Font size
      );
      
      console.log(`\nBadge: ${badge.name}`);
      console.log(`Colors: ${design.colors[0]} on ${design.colors[1]}`);
      console.log(`Contrast Ratio: ${contrast.ratio.toFixed(2)}:1`);
      console.log(`WCAG Level: ${contrast.level}`);
      console.log(`Accessible: ${contrast.isAccessible}`);
      
      if (contrast.suggestions) {
        console.log("Suggestions:", contrast.suggestions[0]); // Show first suggestion
      }
    }
  });
}

/**
 * Example 8: Accessible Tooltips
 */
export function createAccessibleTooltipsExample() {
  console.log("\n=== Accessible Tooltips ===");
  
  const badge = exampleBadgeTypes[1]; // Active Member badge
  
  // Generate tooltips in different languages
  const languages = ['en', 'es', 'fr'];
  
  languages.forEach(lang => {
    const tooltip = BadgeAccessibility.generateAccessibleTooltip(badge, lang);
    
    console.log(`\nLanguage: ${lang.toUpperCase()}`);
    console.log("Tooltip HTML Length:", tooltip.html.length);
    console.log("Has Role Tooltip:", tooltip.html.includes('role="tooltip"'));
    console.log("Has ARIA Hidden:", tooltip.html.includes('aria-hidden="true"'));
    console.log("Has JavaScript:", tooltip.javascript.includes('showTooltip'));
  });
}

/**
 * Example 9: Badge Preview System
 */
export function createBadgePreviewSystemExample() {
  console.log("\n=== Badge Preview System ===");
  
  const previewSystem = BadgeDisplayComponents.createBadgePreviewSystem();
  
  console.log("Preview System HTML Length:", previewSystem.html.length);
  console.log("Has Controls:", previewSystem.html.includes('preview-controls'));
  console.log("Has Shape Selector:", previewSystem.html.includes('shape-select'));
  console.log("Has Color Input:", previewSystem.html.includes('color-input'));
  console.log("Has Animation Selector:", previewSystem.html.includes('animation-select'));
  console.log("Has JavaScript:", previewSystem.javascript.includes('updatePreview'));
  
  // Test design preview generation
  const testDesign = {
    shape: "star",
    background: "#FF5722",
    colors: ["#FF5722", "#D84315"],
    gradient: "radial",
    elements: ["glow"]
  };
  
  const preview = BadgeVisualRenderer.generateBadgePreview(testDesign, {
    size: 'large',
    animation: false
  });
  
  console.log("\nTest Preview Generated:", preview.svg.includes('<svg'));
  console.log("Preview Accessibility:", preview.accessibility.alt);
}

/**
 * Example 10: Animation System
 */
export function demonstrateAnimationSystemExample() {
  console.log("\n=== Animation System ===");
  
  const animationTypes = ['pulse', 'glow', 'bounce', 'rotate', 'shake', 'fade', 'none'];
  const badge = exampleBadgeTypes[0];
  
  animationTypes.forEach(animation => {
    const animatedBadge = { ...badge, animation };
    
    const result = BadgeVisualRenderer.renderBadgeSVG(animatedBadge, {
      animation: animation !== 'none',
      size: 'medium'
    });
    
    console.log(`\nAnimation: ${animation}`);
    console.log("Has CSS Animation:", !!result.css);
    console.log("Animation Class:", result.svg.includes(`badge-animation-${animation}`));
    
    if (result.css) {
      console.log("Has Keyframes:", result.css.includes('@keyframes'));
    }
    
    // Check motion preferences
    const motionPrefs = BadgeAccessibility.getMotionPreferences(animatedBadge);
    console.log("Respects Reduced Motion:", motionPrefs.respectReducedMotion);
    console.log("Has Alternatives:", motionPrefs.alternativeAnimations.length > 0);
  });
}

/**
 * Run all examples
 */
export function runAllExamples() {
  console.log("🎨 Badge Visual System Examples");
  console.log("================================");
  
  try {
    renderBasicBadgeExample();
    validateBadgeDesignsExample();
    createResponsiveBadgeGridExample();
    createCompactBadgeListExample();
    createCollectionProgressExample();
    demonstrateAccessibilityExample();
    analyzeColorContrastExample();
    createAccessibleTooltipsExample();
    createBadgePreviewSystemExample();
    demonstrateAnimationSystemExample();
    
    console.log("\n✅ All examples completed successfully!");
    
  } catch (error) {
    console.error("\n❌ Error running examples:", error);
  }
}

// Export individual examples for selective testing
export {
  exampleBadgeTypes,
  exampleUserBadges,
  exampleCollection,
  exampleProgress
};

// Run examples if this file is executed directly
if (import.meta.main) {
  runAllExamples();
}