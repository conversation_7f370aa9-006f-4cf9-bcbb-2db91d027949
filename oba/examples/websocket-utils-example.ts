/**
 * WebSocketUtils Usage Examples
 *
 * This file demonstrates how to use the WebSocketUtils class for standardized
 * WebSocket message creation and sending.
 */

import { WebSocketUtils } from "../utils/websocket-utils";
import { WebSocketErrorCode } from "../types/websocket-standardization.types";
import type { ServerWebSocket } from "bun";
import type { CustomWebSocketData } from "../types/websocket.types";
import type { WebSocketManager } from "../manager/websocket.manager";

// Example: Creating success messages
function createSuccessMessage() {
  // Basic success message
  const basicSuccess = WebSocketUtils.success("MESSAGE_SENT", {
    messageId: "msg-123",
    content: "Hello, world!",
    timestamp: new Date(),
  });

  // Success message with correlation ID for request-response pattern
  const correlatedSuccess = WebSocketUtils.success(
    "USER_UPDATED",
    {
      userId: "user-123",
      username: "john_doe",
    },
    {
      correlationId: "req-456",
      message: "User profile updated successfully",
    },
  );

  // Success message with targeting information
  const targetedSuccess = WebSocketUtils.success(
    "CHANNEL_MESSAGE",
    {
      content: "Welcome to the channel!",
      author: "system",
    },
    {
      target: {
        channelId: "channel-789",
        serverId: "server-456",
      },
    },
  );

  return { basicSuccess, correlatedSuccess, targetedSuccess };
}

// Example: Creating error messages
function createErrorMessages() {
  // Basic error message
  const basicError = WebSocketUtils.error(
    WebSocketErrorCode.PERMISSION_DENIED,
    "You don't have permission to perform this action",
  );

  // Error with detailed information
  const detailedError = WebSocketUtils.error(
    WebSocketErrorCode.INVALID_DATA,
    "Validation failed",
    {
      correlationId: "req-789",
      details: {
        field: "username",
        expectedFormat: "alphanumeric, 3-20 characters",
        receivedValue: "ab",
      },
    },
  );

  // Using convenience error methods
  const authError = WebSocketUtils.authenticationRequired("req-123");
  const notFoundError = WebSocketUtils.channelNotFound("channel-404");
  const rateLimitError = WebSocketUtils.rateLimited(60); // retry after 60 seconds

  return {
    basicError,
    detailedError,
    authError,
    notFoundError,
    rateLimitError,
  };
}

// Example: Creating event messages
function createEventMessages() {
  // Basic event message
  const userJoinedEvent = WebSocketUtils.event("USER_JOINED", {
    userId: "user-123",
    username: "john_doe",
    timestamp: new Date(),
  });

  // Event with category and severity
  const systemEvent = WebSocketUtils.event(
    "SYSTEM_MAINTENANCE",
    {
      message: "Server will restart in 5 minutes",
      scheduledTime: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
    },
    {
      category: "system",
      severity: "warning",
      target: {
        serverId: "server-123",
      },
    },
  );

  return { userJoinedEvent, systemEvent };
}

// Example: Sending messages to individual WebSocket
function sendToWebSocket(ws: ServerWebSocket<CustomWebSocketData>) {
  // Send a success message
  const successMessage = WebSocketUtils.success("PING_RESPONSE", {
    timestamp: new Date(),
    latency: 42,
  });

  WebSocketUtils.send(ws, successMessage);

  // Send an error message
  const errorMessage = WebSocketUtils.permissionDenied("MANAGE_CHANNELS");
  WebSocketUtils.send(ws, errorMessage);
}

// Example: Broadcasting to multiple WebSockets
function broadcastToSockets(
  sockets: Set<ServerWebSocket<CustomWebSocketData>>,
) {
  // Broadcast to all sockets
  const announcement = WebSocketUtils.event("SERVER_ANNOUNCEMENT", {
    title: "New Feature Available",
    message: "Check out our new voice channels!",
    timestamp: new Date(),
  });

  WebSocketUtils.broadcast(sockets, announcement);

  // Broadcast with exclusions and filters
  const privateMessage = WebSocketUtils.success("PRIVATE_UPDATE", {
    data: "sensitive information",
  });

  WebSocketUtils.broadcast(sockets, privateMessage, {
    excludeUserId: "user-123", // Don't send to this user
    filter: (ws) => ws.data.user?.role === "admin", // Only send to admins
  });
}

// Example: Sending to specific users and channels
function sendToUsersAndChannels(manager: WebSocketManager) {
  // Send to a specific user
  const directMessage = WebSocketUtils.success("DIRECT_MESSAGE", {
    from: "user-456",
    content: "Hey, how are you?",
    timestamp: new Date(),
  });

  const sent = WebSocketUtils.sendToUser("user-123", directMessage, manager);
  if (!sent) {
    console.log("User is not online");
  }

  // Send to a channel
  const channelMessage = WebSocketUtils.event("NEW_MESSAGE", {
    messageId: "msg-789",
    content: "Hello everyone!",
    author: {
      id: "user-456",
      username: "jane_doe",
    },
  });

  WebSocketUtils.sendToChannel("channel-123", channelMessage, manager, {
    serverId: "server-456",
    excludeUserId: "user-456", // Don't echo back to sender
  });
}

// Example: Message validation and serialization
function validateAndSerializeMessages() {
  // Create a valid message
  const validMessage = WebSocketUtils.success("TEST", { data: "test" });

  // Validate the message structure
  const isValid = WebSocketUtils.validate(validMessage);
  console.log("Message is valid:", isValid);

  // Serialize the message
  try {
    const serialized = WebSocketUtils.serialize(validMessage);
    console.log("Serialized message:", serialized);

    // Parse it back
    const parsed = JSON.parse(serialized);
    console.log("Message ID:", parsed.meta.id);
  } catch (error) {
    console.error("Serialization failed:", error);
  }

  // Try to validate an invalid message
  const invalidMessage = { type: "TEST" }; // Missing meta
  const isInvalid = WebSocketUtils.validate(invalidMessage);
  console.log("Invalid message is valid:", isInvalid); // Should be false
}

// Example: Error handling patterns
function errorHandlingExamples() {
  // Handle authentication errors
  function handleAuthError(correlationId?: string) {
    return WebSocketUtils.authenticationRequired(correlationId);
  }

  // Handle validation errors
  function handleValidationErrors(
    errors: Array<{ field: string; message: string; code: string }>,
  ) {
    return WebSocketUtils.validationError(errors);
  }

  // Handle resource not found errors
  function handleNotFound(resourceType: string, resourceId: string) {
    if (resourceType === "channel") {
      return WebSocketUtils.channelNotFound(resourceId);
    } else if (resourceType === "server") {
      return WebSocketUtils.serverNotFound(resourceId);
    } else if (resourceType === "user") {
      return WebSocketUtils.userNotFound(resourceId);
    } else {
      return WebSocketUtils.notFound(resourceType);
    }
  }

  // Handle rate limiting
  function handleRateLimit(retryAfterSeconds: number) {
    return WebSocketUtils.rateLimited(retryAfterSeconds);
  }

  return {
    handleAuthError,
    handleValidationErrors,
    handleNotFound,
    handleRateLimit,
  };
}

// Export examples for use in other files
export {
  createSuccessMessage,
  createErrorMessages,
  createEventMessages,
  sendToWebSocket,
  broadcastToSockets,
  sendToUsersAndChannels,
  validateAndSerializeMessages,
  errorHandlingExamples,
};
