// --- Server ---

import type { ServerWebSocket } from "bun";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ProtectedRouteH<PERSON><PERSON>,
  WebSocketRouteHandler,
} from "./class/routeHandler.ts";
import Router from "./class/router";
import authMiddleware from "./middlewares/authMiddleware";
import loggerMiddleware from "./middlewares/loggerMiddleware";
import { type WebSocketMessage } from "./types.ts";
import { EventTypes } from "@kurultai/oba-types";
import { WebSocketManager } from "./manager/websocket.manager.ts";
import { VoiceWebSocketManager } from "./manager/websocket.debug.ts";
import { corsMiddleware } from "./middlewares/corsMiddleware.ts";
import { registerAuthRoutes } from "./routes/authRoutes.ts";
import { registerDirectMessageRoutes } from "./routes/directMessageRoutes.ts";
import { registerMessageRoutes } from "./routes/messageRoutes.ts";
import { registerServerRoutes } from "./routes/serverRoutes.ts";
import { registerReactionRoutes } from "./routes/reactionRoutes.ts";
import { registerFriendRoutes } from "./routes/friendRoutes.ts";
import { registerStatusRoutes } from "./routes/statusRoutes.ts";
import { registerCategoryRoutes } from "./routes/categoryRoutes.ts";
import { registerBulkOperationRoutes } from "./routes/bulkOperationRoutes.ts";
import { registerRoleRoutes } from "./routes/roleRoutes.ts";
import { registerMemberRoutes } from "./routes/memberRoutes.ts";
import { registerUtilityRoutes } from "./routes/utilityRoutes.ts";
import { registerBadgeRoutes } from "./routes/badgeRoutes.ts";
import { registerBadgeAdminRoutes } from "./routes/badgeAdminRoutes.ts";
import { registerBadgeNominationRoutes } from "./routes/badgeNominationRoutes.ts";
import { setupPerkRoutes } from "./routes/perkRoutes.ts";

const router = new Router();
registerAuthRoutes(router);
registerDirectMessageRoutes(router);
registerMessageRoutes(router);
registerServerRoutes(router);
registerReactionRoutes(router);
registerFriendRoutes(router);
registerStatusRoutes(router);
registerCategoryRoutes(router);
registerBulkOperationRoutes(router);
registerRoleRoutes(router);
registerMemberRoutes(router);
registerUtilityRoutes(router);
registerBadgeRoutes(router);
registerBadgeAdminRoutes(router);
registerBadgeNominationRoutes(router);
setupPerkRoutes(router);
registerMessageRoutes(router);

// Public route
router.add(
  "/",
  new PublicRouteHandler(() => new Response("Welcome!"), [loggerMiddleware]),
);

// Another public route
router.add(
  "/about",
  new PublicRouteHandler(() => new Response("About us"), [loggerMiddleware]),
);

// Protected route
router.add(
  "/profile",
  new ProtectedRouteHandler(
    (req: Request) => {
      const user = req.headers.get("X-Authenticated-User");
      return new Response(`Profile page. Welcome, ${user}!`);
    },
    [loggerMiddleware, authMiddleware], // You can add middlewares in the constructor
  ),
);

//Dynamic route
router.add(
  "/users/:id",
  new PublicRouteHandler(
    (req: Request, params?: Record<string, string>) => {
      const userId = params?.id;
      return new Response(`User profile for user ID: ${userId}`);
    },
    [loggerMiddleware], // Middleware for dynamic route
  ),
);

// --- Bun Server ---

// Create a logger instance for the main application
const appLogger = logger.createLogger("App");

// Get singleton instances of WebSocket managers
const wsManager = WebSocketManager.getInstance();
const voiceWsManager = VoiceWebSocketManager.getInstance();

// --- WebRTC Services ---
const webRTCSignalingService = new WebRTCSignalingServiceDebug(voiceWsManager);
const sfuService = new SFUServiceDebug(voiceWsManager);

appLogger.info("Application services initialized");

// Import shared types from the types file
import type { User, CustomWebSocketData } from "./types/websocket.types";
import { logger } from "./services/logger.service.ts";
import { WebRTCSignalingServiceDebug } from "./services/webrtc.service.debug.ts";
import { SFUServiceDebug } from "./services/sfu.service.debug.ts";
import { handleBinaryVoiceData } from "./handlers/binaryVoiceHandler.ts";
import {
  createDirectMessage,
  deleteDirectMessage,
  editDirectMessage,
  getDirectMessageById,
} from "./db/utils.ts";
import { db } from "./db/index.ts";
import {
  addCORSHeaders,
  createCORSHandler,
  type CORSOptions,
} from "./utils/cors.ts";
import { WebSocketUtils } from "./utils/websocket-utils.ts";
import { WebSocketErrorCode } from "./types/websocket-standardization.types.ts";

// Use a WeakMap to track connection status
const connectionStatus = new WeakMap<
  ServerWebSocket<CustomWebSocketData>,
  { isAlive: boolean }
>();

const corsHeaders = {
  "Access-Control-Allow-Origin": "*", // Adjust as needed for security
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

const PING_INTERVAL = 60000; // 60 seconds

// Change this later on for more robust approach.
function authenticateUser() {
  return true;
}

const stunTurnConfig = {
  iceServers: [
    {
      urls: [
        "stun:stun.l.google.com:19302", // Public STUN server (Google)
      ],
    },
    {
      urls: [
        // Domain ayarları yapıldıktan sonra çalışacak.
        "turn:coolify.berkormanli.dev:3478", // TURN server - check port!
      ],
      credential: "mypassword",
      username: "myuser",
    },
  ],
};

const corsOptions: CORSOptions = {
  origin: process.env.FRONTEND_ORIGIN || "http://localhost:5173",
  credentials: process.env.CORS_CREDENTIALS === "true",
  allowedHeaders: [
    "content-type",
    "Content-Type",
    "authorization",
    "Authorization",
    "x-authenticated-user",
    "X-Authenticated-User",
    "x-new-access-token",
    "x-token-refreshed",
    "x-requested-with",
    "x-request-id",
  ],
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
};

const corsHandler = createCORSHandler(corsOptions);

const server = Bun.serve({
  port: 3005,
  websocket: {
    async open(ws: ServerWebSocket<CustomWebSocketData>) {
      //console.log(ws)
      // Use WebSocketUtils for standardized auth required message
      const authRequiredMessage = WebSocketUtils.authenticationRequired();
      WebSocketUtils.send(ws, authRequiredMessage);

      // Initialize connection status
      connectionStatus.set(ws, { isAlive: true });

      //wsManager.add(ws);
      //console.log("WebSocket opened", ws.data);
      // You might want to send a welcome message or initial data here
      //ws.send(JSON.stringify({ type: "WELCOME", data: "Connected!" }));
      // Ping-pong logic
      //ws.data.isAlive = true;
      //connectionStatus.set(ws, { isAlive: true });
    },
    ping(ws) {
      // When server sends ping
      //console.log(`Ping sent to user ${ws.data.userId}`);
    },
    pong(ws) {
      // When server receives pong
      //console.log(`Pong received from user ${ws.data.userId}`);
      const status = connectionStatus.get(ws);
      if (status) {
        status.isAlive = true;
      }
    },
    message: async function (
      ws: ServerWebSocket<CustomWebSocketData>,
      rawMessage,
    ): Promise<void> {
      const { isAuthenticated, userId, token, isAlive } = ws.data;

      if (!isAuthenticated) {
        if (typeof rawMessage === "string") {
          const message = JSON.parse(rawMessage);

          // Extract correlation ID from the message for request-response tracking
          const correlationId =
            message.meta?.correlationId || message.correlationId;

          // Use WebSocketManager's enhanced authentication
          try {
            const authData = {
              userId: message.data.userId || message.data.user?.userId,
              deviceId: message.data.deviceId || "default-device",
              token: message.data.token,
              refreshToken: message.data.refreshToken || "",
              expiresIn: message.data.expiresIn || 3600,
              serverId: message.data.serverId,
              channelId: message.data.channelId,
              type: message.data.type,
            };

            const authResult = await wsManager.handleAuthentication(
              ws,
              authData,
            );

            if (authResult) {
              ws.data.isAuthenticated = true;
              ws.data.userId = authData.userId;
              ws.data.deviceId = authData.deviceId;
              ws.data.token = authData.token;
              connectionStatus.set(ws, { isAlive: true });
              // Set common data for all connection types
              ws.data.user = message.data.user;
              ws.data.userId = authData.userId;

              // Send authentication success response using WebSocketUtils with correlation tracking
              const authSuccessMessage = WebSocketUtils.success(
                "AUTH_SUCCESS",
                {
                  stunTurnConfig: stunTurnConfig, // Send STUN/TURN config to client on auth success
                  userId: authData.userId,
                  deviceId: authData.deviceId,
                  connectionType: message.data.type,
                },
                {
                  message: "Authentication successful",
                  correlationId: correlationId, // Include correlation ID for request-response tracking
                },
              );
              WebSocketUtils.send(ws, authSuccessMessage);

              // Handle different connection types
              if (message.data.type === "voice") {
                // Voice connection
                ws.data.type = "voice";
                ws.data.channelId = message.data.channelId;
                ws.data.serverId = message.data.serverId;

                // Handle connection (will update user status to ONLINE if authenticated)
                await wsManager.handleConnection(ws);
                voiceWsManager.add(ws);

                // If this is a reconnection and we have channel context, auto-subscribe
                if (message.data.serverId && message.data.channelId) {
                  appLogger.debug(
                    `Auto-subscribing voice connection to channel ${message.data.channelId} in server ${message.data.serverId}`,
                  );
                  // Voice connections are automatically handled by voiceWsManager.add()
                }

                appLogger.debug(
                  `Voice WebSocket connection added for user ${ws.data.userId}`,
                );
              } else if (message.data.type === "private") {
                // Private connection for direct messages and friend events
                ws.data.type = "private";

                // Handle connection (will update user status to ONLINE if authenticated)
                await wsManager.handleConnection(ws);
                wsManager.addPrivateConnection(ws);
                appLogger.debug(
                  `Private WebSocket connection added for user ${ws.data.userId}`,
                );
              } else {
                // Regular channel connection
                ws.data.type = "channel";
                ws.data.channelId = message.data.channelId;
                ws.data.serverId = message.data.serverId;

                // Handle connection (will update user status to ONLINE if authenticated)
                await wsManager.handleConnection(ws);
                wsManager.add(ws);

                // If this is a reconnection and we have channel context, auto-subscribe
                if (message.data.serverId && message.data.channelId) {
                  appLogger.debug(
                    `Auto-subscribing to channel ${message.data.channelId} in server ${message.data.serverId} after reconnection`,
                  );
                  try {
                    await wsManager.subscribeChannel(
                      ws,
                      message.data.userId,
                      message.data.serverId,
                      message.data.channelId,
                    );
                    appLogger.debug(
                      `Successfully auto-subscribed to channel after reconnection`,
                    );
                  } catch (error) {
                    appLogger.error(
                      `Failed to auto-subscribe to channel after reconnection:`,
                      undefined,
                      error,
                    );
                  }
                }

                appLogger.debug(
                  `Channel WebSocket connection added for user ${ws.data.userId}`,
                );
              }
            } else {
              // Use WebSocketUtils for standardized authentication failure with correlation tracking
              const authFailureMessage = WebSocketUtils.error(
                WebSocketErrorCode.AUTH_FAILED,
                "Authentication failed",
                { correlationId: correlationId },
              );
              WebSocketUtils.send(ws, authFailureMessage);
              ws.close(); // Close the connection
            }
          } catch (error) {
            appLogger.error("Authentication error:", undefined, error);
            // Use WebSocketUtils for standardized authentication error with correlation tracking
            const authErrorMessage = WebSocketUtils.error(
              WebSocketErrorCode.AUTH_FAILED,
              "Authentication error occurred",
              { correlationId: correlationId },
            );
            WebSocketUtils.send(ws, authErrorMessage);
            ws.close();
          }
        } else {
          // Use WebSocketUtils for standardized authentication error
          const authErrorMessage = WebSocketUtils.error(
            WebSocketErrorCode.INVALID_MESSAGE_FORMAT,
            "Authentication message must be a string",
          );
          WebSocketUtils.send(ws, authErrorMessage);
          ws.close();
        }
      } else if (
        rawMessage instanceof Blob ||
        rawMessage instanceof ArrayBuffer
      ) {
        appLogger.debug(
          `Received binary voice data from user ${ws.data.userId}`,
        );

        if (!ws.data.serverId || !ws.data.channelId) {
          appLogger.error(
            `Missing serverId or channelId for voice data from user ${ws.data.userId}`,
          );
          return;
        }

        try {
          // Convert Blob to ArrayBuffer if needed
          const arrayBuffer =
            rawMessage instanceof Blob
              ? await rawMessage.arrayBuffer()
              : rawMessage;
          appLogger.debug(
            `Binary voice data size: ${arrayBuffer.byteLength} bytes`,
          );

          // Set the WebSocket type to 'voice' if not already set
          if (!ws.data.type) {
            ws.data.type = "voice";
            appLogger.debug(
              `Set WebSocket type to 'voice' for user ${ws.data.userId}`,
            );
          }

          // Handle voice data through SFU service
          appLogger.debug(`Processing voice data for user ${ws.data.userId}`);
          await handleBinaryVoiceData(
            voiceWsManager,
            ws.data.userId,
            ws.data.serverId,
            ws.data.channelId,
            arrayBuffer,
          );
          appLogger.debug(`Voice data processed for user ${ws.data.userId}`);
        } catch (error) {
          appLogger.error(
            `Error processing binary voice data from user ${ws.data.userId}:`,
            undefined,
            error,
          );
        }
      } else if (rawMessage instanceof Buffer) {
        if (!ws.data.serverId || !ws.data.channelId || !ws.data.user) return;

        const arrayBufferLike = rawMessage.buffer;
        const slicedBuffer = arrayBufferLike.slice(
          rawMessage.byteOffset,
          rawMessage.byteOffset + rawMessage.byteLength,
        );

        if (slicedBuffer instanceof ArrayBuffer) {
          voiceWsManager.broadcast(
            ws.data.userId,
            ws.data.serverId,
            ws.data.channelId,
            slicedBuffer,
          );
        } else {
          // Handle SharedArrayBuffer case (if needed for your application)
          console.error("Unexpected SharedArrayBuffer encountered!");
          // Option 1: Convert to ArrayBuffer (if data doesn't need to be shared)
          const arrayBuffer = new ArrayBuffer(slicedBuffer.byteLength);
          new Uint8Array(arrayBuffer).set(new Uint8Array(slicedBuffer));
          voiceWsManager.broadcast(
            ws.data.userId,
            ws.data.serverId,
            ws.data.channelId,
            arrayBuffer,
          );

          // Option 2: Throw an error or use a different communication mechanism
          // throw new Error("SharedArrayBuffer not supported for broadcast.");
        }
      } else {
        console.log("Raw message received:", rawMessage);
        console.log("Raw message type:", typeof rawMessage);
        console.log("Raw message length:", rawMessage.length);

        const message = JSON.parse(rawMessage);
        console.log("Parsed message:", JSON.stringify(message, null, 2));
        console.log("Message keys:", Object.keys(message));

        const user = message.sender || message.userId || ws.data.userId;
        console.log("Extracted user:", user);
        console.log("WebSocket data:", JSON.stringify(ws.data, null, 2));

        // Validate message before processing
        const validation = await wsManager.validateMessage(ws, message);
        console.log("Message validation result:", validation);

        if (!validation.isValid) {
          console.log("Message validation failed:", validation.error);
          ws.send(
            JSON.stringify({
              type: "ERROR",
              code: validation.error!.code,
              message: validation.error!.message,
            }),
          );
          return;
        }

        // Handle different message types
        switch (message.type) {
          case "ping":
            ws.send(
              JSON.stringify({
                type: "pong",
              }),
            );
            break;
          case EventTypes.MESSAGE_SEND:
            const data = {
              avatar: message.avatar,
              username: message.username,
              message: message.data,
              serverId: message.serverId,
              channelId: message.channelId,
            };
            wsManager.handleMessageSend(user, data);
            break;
          case EventTypes.MESSAGE_UPDATE:
            wsManager.handleMessageUpdate(user, {
              messageId: message.messageId,
              newContent: message.content,
              serverId: message.serverId,
              channelId: message.channelId,
            });
            break;
          case EventTypes.MESSAGE_DELETE:
            wsManager.handleMessageDelete(user, {
              messageId: message.messageId,
              serverId: message.serverId,
              channelId: message.channelId,
            });
            break;
          case EventTypes.DIRECT_MESSAGE_HISTORY_REQUEST: // Use string literal since EventTypes may not have this constant yet
            console.log(
              "Direct message history request received:",
              JSON.stringify(message),
              user,
            );
            if (message.userId === user) return;
            // Handle direct message history request
            const dmHistoryData = {
              otherUserId: message.data.otherUserId,
              limit: message.limit,
            };

            // Call the handler method
            wsManager.handleDirectMessageHistory(ws, user, dmHistoryData);
            break;
          case EventTypes.FRIEND_LIST_REQUEST:
            wsManager.handleFriendListRequest(ws, user);
            break;
          case EventTypes.FRIEND_REQUESTS_REQUEST:
            wsManager.handleFriendRequestsRequest(ws, user);
            break;
          case EventTypes.USER_BLOCK: // EventTypes.USER_BLOCK
            wsManager.handleUserBlock(user, {
              targetUserId: message.data.targetUserId,
            });
            break;
          case EventTypes.USER_UNBLOCK: // EventTypes.USER_UNBLOCK
            wsManager.handleUserUnblock(user, {
              friendshipId: message.data.friendshipId,
            });
            break;
          case EventTypes.CHANNEL_UPDATE: // EventTypes.CHANNEL_UPDATE
            wsManager.handleChannelUpdate(user, {
              channelId: message.channelId,
              name: message.name,
              description: message.description,
              type: message.type,
              serverId: message.serverId,
            });
            break;
          case EventTypes.CHANNEL_DELETE: // EventTypes.CHANNEL_DELETE
            wsManager.handleChannelDelete(user, {
              channelId: message.channelId,
              serverId: message.serverId,
            });
            break;
          // Direct message events
          case EventTypes.DIRECT_MESSAGE_SEND:
            // Handle direct message sending
            appLogger.debug(
              `Direct message from ${user} to ${message.recipientId}: ${message.content}`,
            );
            const dmData = {
              senderId: user,
              receiverId: message.recipientId,
              content: message.content,
              attachments: message.attachments,
            };
            // Call the utility function directly
            createDirectMessage(
              db,
              dmData.senderId,
              dmData.receiverId,
              dmData.content,
              dmData.attachments,
            )
              .then((newMessage) => {
                // Prepare message data for broadcasting
                const directMessageData = {
                  messageId: newMessage.id,
                  senderId: newMessage.senderId,
                  receiverId: newMessage.receiverId,
                  content: newMessage.content,
                  attachments: newMessage.attachments,
                  createdAt: newMessage.createdAt,
                  timestamp: new Date(),
                };

                // Use WebSocketUtils for standardized event broadcasting to receiver
                const receivedEvent = WebSocketUtils.event("DIRECT_MESSAGE_RECEIVED", directMessageData);
                wsManager.broadcastToUser(
                  dmData.receiverId,
                  WebSocketUtils.serialize(receivedEvent),
                );

                // Use WebSocketUtils for standardized event broadcasting to sender
                const sentEvent = WebSocketUtils.event("DIRECT_MESSAGE_SENT", directMessageData);
                wsManager.broadcastToUser(
                  dmData.senderId,
                  WebSocketUtils.serialize(sentEvent),
                );

                appLogger.debug("Direct message sent and broadcast successfully", undefined, {
                  messageId: newMessage.id,
                });
              })
              .catch((error) => {
                appLogger.error("Error sending direct message:", undefined, error);
                // Use WebSocketUtils for standardized error response
                const errorMessage = WebSocketUtils.internalError(
                  `Failed to send direct message: ${error instanceof Error ? error.message : "Unknown error"}`,
                );
                WebSocketUtils.send(ws, errorMessage);
              });
            break;
          case EventTypes.DIRECT_MESSAGE_UPDATE:
            // Handle direct message editing
            const dmUpdateData = {
              messageId: message.messageId,
              senderId: user,
              content: message.content,
            };
            // Get the message to verify ownership
            getDirectMessageById(db, dmUpdateData.messageId)
              .then((existingMessage) => {
                if (!existingMessage) {
                  appLogger.error(
                    `Direct message ${dmUpdateData.messageId} not found`,
                  );
                  // Use WebSocketUtils for standardized error response
                  const errorMessage = WebSocketUtils.notFound("Direct message");
                  WebSocketUtils.send(ws, errorMessage);
                  return;
                }

                // Verify the user is the message sender
                if (existingMessage.senderId !== dmUpdateData.senderId) {
                  appLogger.error(
                    `User ${dmUpdateData.senderId} is not authorized to edit message ${dmUpdateData.messageId}`,
                  );
                  // Use WebSocketUtils for standardized error response
                  const errorMessage = WebSocketUtils.permissionDenied("MESSAGE_EDIT");
                  WebSocketUtils.send(ws, errorMessage);
                  return;
                }

                // Update the message
                editDirectMessage(
                  db,
                  dmUpdateData.messageId,
                  dmUpdateData.content,
                )
                  .then((updatedMessage) => {
                    // Prepare update data for broadcasting
                    const updateData = {
                      messageId: updatedMessage.id,
                      content: updatedMessage.content,
                      editedAt: updatedMessage.editedAt,
                      senderId: updatedMessage.senderId,
                      receiverId: updatedMessage.receiverId,
                      timestamp: new Date(),
                    };

                    // Use WebSocketUtils for standardized event broadcasting
                    const updateEvent = WebSocketUtils.event("DIRECT_MESSAGE_UPDATED", updateData);

                    if (updatedMessage.receiverId) {
                      wsManager.broadcastToUser(
                        updatedMessage.receiverId,
                        WebSocketUtils.serialize(updateEvent),
                      );
                    }

                    // Also send to sender for confirmation
                    wsManager.broadcastToUser(
                      updatedMessage.senderId,
                      WebSocketUtils.serialize(updateEvent),
                    );

                    appLogger.debug("Direct message updated and broadcast successfully", undefined, {
                      messageId: updatedMessage.id,
                    });
                  })
                  .catch((error) => {
                    appLogger.error("Error updating direct message:", undefined, error);
                    // Use WebSocketUtils for standardized error response
                    const errorMessage = WebSocketUtils.internalError(
                      `Failed to update direct message: ${error instanceof Error ? error.message : "Unknown error"}`,
                    );
                    WebSocketUtils.send(ws, errorMessage);
                  });
              })
              .catch((error) => {
                appLogger.error("Error getting direct message:", undefined, error);
                // Use WebSocketUtils for standardized error response
                const errorMessage = WebSocketUtils.internalError(
                  `Failed to retrieve direct message: ${error instanceof Error ? error.message : "Unknown error"}`,
                );
                WebSocketUtils.send(ws, errorMessage);
              });
            break;
          case EventTypes.DIRECT_MESSAGE_DELETE:
            // Handle direct message deletion
            const dmDeleteData = {
              messageId: message.messageId,
              senderId: user,
            };
            // Get the message to verify ownership
            getDirectMessageById(db, dmDeleteData.messageId)
              .then((existingMessage) => {
                if (!existingMessage) {
                  appLogger.error(
                    `Direct message ${dmDeleteData.messageId} not found`,
                  );
                  // Use WebSocketUtils for standardized error response
                  const errorMessage = WebSocketUtils.notFound("Direct message");
                  WebSocketUtils.send(ws, errorMessage);
                  return;
                }

                // Verify the user is the message sender
                if (existingMessage.senderId !== dmDeleteData.senderId) {
                  appLogger.error(
                    `User ${dmDeleteData.senderId} is not authorized to delete message ${dmDeleteData.messageId}`,
                  );
                  // Use WebSocketUtils for standardized error response
                  const errorMessage = WebSocketUtils.permissionDenied("MESSAGE_DELETE");
                  WebSocketUtils.send(ws, errorMessage);
                  return;
                }

                // Delete the message
                deleteDirectMessage(db, dmDeleteData.messageId)
                  .then((deletedMessage) => {
                    // Prepare delete data for broadcasting
                    const deleteData = {
                      messageId: deletedMessage.id,
                      senderId: deletedMessage.senderId,
                      receiverId: deletedMessage.receiverId,
                      timestamp: new Date(),
                    };

                    // Use WebSocketUtils for standardized event broadcasting
                    const deleteEvent = WebSocketUtils.event("DIRECT_MESSAGE_DELETED", deleteData);

                    if (deletedMessage.receiverId) {
                      wsManager.broadcastToUser(
                        deletedMessage.receiverId,
                        WebSocketUtils.serialize(deleteEvent),
                      );
                    }

                    // Also send to sender for confirmation
                    wsManager.broadcastToUser(
                      deletedMessage.senderId,
                      WebSocketUtils.serialize(deleteEvent),
                    );

                    appLogger.debug("Direct message deleted and broadcast successfully", undefined, {
                      messageId: deletedMessage.id,
                    });
                  })
                  .catch((error) => {
                    appLogger.error("Error deleting direct message:", undefined, error);
                    // Use WebSocketUtils for standardized error response
                    const errorMessage = WebSocketUtils.internalError(
                      `Failed to delete direct message: ${error instanceof Error ? error.message : "Unknown error"}`,
                    );
                    WebSocketUtils.send(ws, errorMessage);
                  });
              })
              .catch((error) => {
                appLogger.error("Error getting direct message:", undefined, error);
                // Use WebSocketUtils for standardized error response
                const errorMessage = WebSocketUtils.internalError(
                  `Failed to retrieve direct message: ${error instanceof Error ? error.message : "Unknown error"}`,
                );
                WebSocketUtils.send(ws, errorMessage);
              });
            break;
          case EventTypes.SERVER_CREATE:
            wsManager.createServer(ws, user, message.data);
            break;
          case EventTypes.CHANNEL_CREATE:
            wsManager.createChannel(ws, user, message.data);
            break;
          case EventTypes.CATEGORY_CREATE:
            wsManager.handleCategoryCreate(ws, user, message.data);
            break;
          case EventTypes.SERVER_JOIN:
            wsManager.joinServer(ws, user, message.data);
            break;
          case EventTypes.SERVER_VIEW: // Use string literal for now
            wsManager.handleServerView(ws, user, message.data);
            break;
          case EventTypes.CHANNEL_SUBSCRIBE:
            console.log(
              "CHANNEL_SUBSCRIBE message received:",
              JSON.stringify(message),
            );

            // Extract serverId and channelId from either top-level or nested data
            const serverId = message.serverId || message.data?.serverId;
            const channelId = message.channelId || message.data?.channelId;

            console.log("Extracted parameters:", { user, serverId, channelId });
            console.log("Message structure analysis:", {
              hasTopLevelServerId: !!message.serverId,
              hasTopLevelChannelId: !!message.channelId,
              hasDataObject: !!message.data,
              hasNestedServerId: !!message.data?.serverId,
              hasNestedChannelId: !!message.data?.channelId,
              dataObjectKeys: message.data ? Object.keys(message.data) : null,
            });

            if (!serverId) {
              console.error(
                "Missing serverId or channelId in CHANNEL_SUBSCRIBE message",
              );
              ws.send(
                JSON.stringify({
                  type: "ERROR",
                  code: "MISSING_PARAMETERS",
                  message:
                    "Missing serverId or channelId in channel subscription request",
                }),
              );
              return;
            }

            wsManager.subscribeChannel(ws, user, serverId, channelId);
            break;
          case EventTypes.VOICE_DATA_SEND:
            try {
              // Extract correlation ID for request-response tracking
              const correlationId = message.meta?.correlationId || message.correlationId;

              wsManager.handleAudio(user, message.data);

              // Send success response with correlation tracking if requested
              if (correlationId) {
                const successMessage = WebSocketUtils.success("VOICE_DATA_SENT", {
                  userId: user,
                  dataSize: message.data?.length || 0,
                }, {
                  correlationId: correlationId,
                });
                WebSocketUtils.send(ws, successMessage);
              }
            } catch (error) {
              appLogger.error("Error handling voice data send:", undefined, error);
              const errorMessage = WebSocketUtils.internalError(
                `Failed to send voice data: ${error instanceof Error ? error.message : "Unknown error"}`,
              );
              WebSocketUtils.send(ws, errorMessage);
            }
            break;
          case EventTypes.CHANNELS_REORDER:
            wsManager.handleChannelsReorder(user, message.data);
            break;
          case EventTypes.CATEGORIES_REORDER:
            wsManager.handleCategoriesReorder(user, message.data);
            break;
          case EventTypes.CHANNELS_MOVE:
            wsManager.handleChannelsMove(user, message.data);
            break;
          case EventTypes.SERVER_RESTRUCTURE:
            wsManager.handleServerRestructure(ws, user, message.data);
            break;
          // Server Management
          case EventTypes.SERVER_UPDATE:
            wsManager.handleServerUpdate(user, message.data);
            break;
          case EventTypes.SERVER_DELETE:
            wsManager.handleServerDelete(user, message.data);
            break;
          case EventTypes.SERVER_LEAVE:
            wsManager.handleServerLeave(user, message.data);
            break;
          // Channel Management
          case EventTypes.CHANNEL_UNSUBSCRIBE:
            wsManager.handleChannelUnsubscribe(ws, user, message.data);
            break;
          case EventTypes.CHANNEL_ADDED_TO_CATEGORY:
            wsManager.handleChannelAddToCategory(user, message.data);
            break;
          // case EventTypes.CHANNEL_REMOVED_FROM_CATEGORY:
          //   wsManager.handleChannelRemoveFromCategory(user, message.data);
          //   break;
          // Category Management
          case EventTypes.CATEGORY_UPDATE:
            wsManager.handleCategoryUpdate(user, message.data);
            break;
          case EventTypes.CATEGORY_DELETE:
            wsManager.handleCategoryDelete(user, message.data);
            break;
          // Role Management
          case EventTypes.ROLE_CREATE:
            wsManager.handleRoleCreate(user, message.data);
            break;
          case EventTypes.ROLE_UPDATE:
            wsManager.handleRoleUpdate(user, message.data);
            break;
          case EventTypes.ROLE_DELETE:
            wsManager.handleRoleDelete(user, message.data);
            break;
          case EventTypes.ROLE_ASSIGN:
            wsManager.handleRoleAssign(user, message.data);
            break;
          case EventTypes.ROLE_REMOVE:
            wsManager.handleRoleRemove(user, message.data);
            break;
          // Member Management
          case EventTypes.MEMBER_KICK:
            wsManager.handleMemberKick(user, message.data);
            break;
          case EventTypes.MEMBER_BAN:
            wsManager.handleMemberBan(user, message.data);
            break;
          case EventTypes.MEMBER_UNBAN:
            wsManager.handleMemberUnban(user, message.data);
            break;
          // Voice Channel Features
          case EventTypes.VOICE_JOIN:
            try {
              // Extract correlation ID for request-response tracking
              const correlationId = message.meta?.correlationId || message.correlationId;

              await wsManager.handleVoiceJoin(user, message.data);

              // Send success response with correlation tracking
              if (correlationId) {
                const successMessage = WebSocketUtils.success("VOICE_JOIN_SUCCESS", {
                  serverId: message.data.serverId,
                  channelId: message.data.channelId,
                  userId: user,
                }, {
                  correlationId: correlationId,
                });
                WebSocketUtils.send(ws, successMessage);
              }
            } catch (error) {
              appLogger.error("Error handling voice join:", undefined, error);
              const errorMessage = WebSocketUtils.internalError(
                `Failed to join voice channel: ${error instanceof Error ? error.message : "Unknown error"}`,
              );
              WebSocketUtils.send(ws, errorMessage);
            }
            break;
          case EventTypes.VOICE_LEAVE:
            try {
              // Extract correlation ID for request-response tracking
              const correlationId = message.meta?.correlationId || message.correlationId;

              await wsManager.handleVoiceLeave(user, message.data);

              // Send success response with correlation tracking
              if (correlationId) {
                const successMessage = WebSocketUtils.success("VOICE_LEAVE_SUCCESS", {
                  serverId: message.data.serverId,
                  channelId: message.data.channelId,
                  userId: user,
                }, {
                  correlationId: correlationId,
                });
                WebSocketUtils.send(ws, successMessage);
              }
            } catch (error) {
              appLogger.error("Error handling voice leave:", undefined, error);
              const errorMessage = WebSocketUtils.internalError(
                `Failed to leave voice channel: ${error instanceof Error ? error.message : "Unknown error"}`,
              );
              WebSocketUtils.send(ws, errorMessage);
            }
            break;
          case EventTypes.VOICE_MUTE_TOGGLE:
            try {
              // Extract correlation ID for request-response tracking
              const correlationId = message.meta?.correlationId || message.correlationId;

              await wsManager.handleVoiceMuteToggle(user, message.data);

              // Send success response with correlation tracking
              if (correlationId) {
                const successMessage = WebSocketUtils.success("VOICE_MUTE_TOGGLE_SUCCESS", {
                  serverId: message.data.serverId,
                  channelId: message.data.channelId,
                  userId: user,
                }, {
                  correlationId: correlationId,
                });
                WebSocketUtils.send(ws, successMessage);
              }
            } catch (error) {
              appLogger.error("Error handling voice mute toggle:", undefined, error);
              const errorMessage = WebSocketUtils.internalError(
                `Failed to toggle mute status: ${error instanceof Error ? error.message : "Unknown error"}`,
              );
              WebSocketUtils.send(ws, errorMessage);
            }
            break;
          case EventTypes.VOICE_DEAFEN_TOGGLE:
            try {
              // Extract correlation ID for request-response tracking
              const correlationId = message.meta?.correlationId || message.correlationId;

              await wsManager.handleVoiceDeafenToggle(user, message.data);

              // Send success response with correlation tracking
              if (correlationId) {
                const successMessage = WebSocketUtils.success("VOICE_DEAFEN_TOGGLE_SUCCESS", {
                  serverId: message.data.serverId,
                  channelId: message.data.channelId,
                  userId: user,
                }, {
                  correlationId: correlationId,
                });
                WebSocketUtils.send(ws, successMessage);
              }
            } catch (error) {
              appLogger.error("Error handling voice deafen toggle:", undefined, error);
              const errorMessage = WebSocketUtils.internalError(
                `Failed to toggle deafen status: ${error instanceof Error ? error.message : "Unknown error"}`,
              );
              WebSocketUtils.send(ws, errorMessage);
            }
            break;
          // User Management
          case EventTypes.USER_UPDATE:
            wsManager.handleUserUpdate(user, message.data);
            break;
          case EventTypes.USER_STATUS_UPDATE: // EventTypes.USER_STATUS_UPDATE
            wsManager.handleUserStatusUpdate(user, {
              status: message.status,
              statusMessage: message.statusMessage,
            });
            break;
          // Message Management
          case EventTypes.MESSAGE_TYPING_START:
            wsManager.handleMessageTypingStart(user, message.data);
            break;
          case EventTypes.MESSAGE_TYPING_STOP:
            wsManager.handleMessageTypingEnd(user, message.data);
            break;
          // Friend Management
          case EventTypes.FRIEND_REQUEST: // EventTypes.FRIEND_REQUEST_SEND
            wsManager.handleFriendRequest(user, {
              friendId: message.data.username,
            });
            break;
          case EventTypes.FRIEND_REQUEST_ACCEPT: // EventTypes.FRIEND_REQUEST_ACCEPT
            wsManager.handleFriendRequestAccept(user, {
              requestId: message.data.requestId,
            });
            break;
          case EventTypes.FRIEND_REQUEST_REJECT: // EventTypes.FRIEND_REQUEST_REJECT
            wsManager.handleFriendRequestReject(user, {
              requestId: message.data.requestId,
            });
            break;
          case EventTypes.FRIEND_REQUEST_CANCEL: // EventTypes.FRIEND_REQUEST_CANCEL
            wsManager.handleFriendRequestCancel(user, {
              requestId: message.data.requestId,
            });
            break;
          case EventTypes.FRIEND_REMOVE: // EventTypes.FRIEND_REMOVE
            wsManager.handleFriendRemove(user, {
              friendshipId: message.data.friendshipId,
            });
            break;
          // Reaction Management
          case EventTypes.MESSAGE_REACTION_ADD:
            wsManager.handleMessageReactionAdd(user, {
              messageId: message.messageId,
              emoji: message.emoji,
              serverId: message.serverId,
              channelId: message.channelId,
            });
            break;
          case EventTypes.MESSAGE_REACTION_REMOVE:
            wsManager.handleMessageReactionRemove(user, {
              messageId: message.messageId,
              emoji: message.emoji,
              serverId: message.serverId,
              channelId: message.channelId,
            });
            break;
          case EventTypes.MESSAGE_REACTION_UPDATE:
            wsManager.handleMessageReactionUpdate(user, {
              messageId: message.messageId,
              emoji: message.emoji,
              serverId: message.serverId,
              channelId: message.channelId,
            });
            break;
          // WebRTC Signaling Handlers
          //case WebRTCEventTypes.WEBRTC_OFFER: // or EventTypes.WEBRTC_OFFER if you modified @kurultai/oba-types
          case "webrtc_offer": // Fallback if you can't modify EventTypes
            webRTCSignalingService.handleOffer(ws, message);
            break;
          //case WebRTCEventTypes.WEBRTC_ANSWER: // or EventTypes.WEBRTC_ANSWER
          case "webrtc_answer":
            webRTCSignalingService.handleAnswer(ws, message);
            break;
          //case WebRTCEventTypes.WEBRTC_ICE_CANDIDATE: // or EventTypes.WEBRTC_ICE_CANDIDATE
          case "webrtc_ice_candidate":
            webRTCSignalingService.handleIceCandidate(ws, message);
            break;
          case "webrtc_join_room":
            webRTCSignalingService.handleJoin(ws, message);
            break;
          case "webrtc_leave_room":
            webRTCSignalingService.handleLeave(ws, message);
            break;
          //case WebRTCEventTypes.WEBRTC_MUTE_TOGGLE:
          case "webrtc_mute_toggle":
            webRTCSignalingService.handleMuteToggle(ws, message);
            break;
          //case WebRTCEventTypes.WEBRTC_DEAFEN_TOGGLE:
          case "webrtc_deafen_toggle":
            webRTCSignalingService.handleDeafenToggle(ws, message);
            break;
          case "webrtc_speaking_start":
          case "webrtc_speaking_end":
            webRTCSignalingService.handleSpeakingState(ws, message);
            break;
          // SFU Handlers
          //case SFUEventTypes.SFU_JOIN:
          case "sfu_join":
            sfuService.handleJoin(ws, message);
            break;
          //case SFUEventTypes.SFU_LEAVE:
          case "sfu_leave":
            sfuService.handleLeave(ws, message);
            break;
          //case SFUEventTypes.SFU_OFFER:
          case "sfu_offer":
            sfuService.handleOffer(ws, message);
            break;
          //case SFUEventTypes.SFU_ICE_CANDIDATE:
          case "sfu_ice_candidate":
            sfuService.handleIceCandidate(ws, message);
            break;
          //case SFUEventTypes.SFU_MUTE_TOGGLE:
          case "sfu_mute_toggle":
            sfuService.handleMuteToggle(ws, message);
            break;
          //case SFUEventTypes.SFU_DEAFEN_TOGGLE:
          case "sfu_deafen_toggle":
            sfuService.handleDeafenToggle(ws, message);
            break;
          //case SFUEventTypes.SFU_SPEAKING_STATE:
          case "sfu_speaking_state":
            sfuService.handleSpeakingState(ws, message);
            break;
          default:
            console.log(message);
            console.error("Unknown message type:", message.type);
        }
      }

      //const userId = (ws.data as any).userId;
      //console.log("Received message:", rawMessage/* , "from user:", userId */);
      // Parse the incoming message (assuming it's JSON)
      /*       let message: WebSocketMessage;
      try {
        message = JSON.parse(rawMessage.toString()); // Assuming the message is a string
      } catch (error) {
        console.error("Invalid message format:", rawMessage);
        return;
      }
      // !!!!!For test purposes only!!!!!
      const user = message.sender || message.userId;
      // Handle different message types
      switch (message.type) {
        case EventTypes.MESSAGE_SEND:
          wsManager.handleMessageSend(user, message.data);
          break;
        case EventTypes.SERVER_CREATE:
          wsManager.createServer(ws, user, message.data);
          break;
        case EventTypes.CHANNEL_CREATE:
          wsManager.createChannel(ws, user, message.data);
          break;
        case EventTypes.SERVER_JOIN:
          wsManager.joinServer(ws, user, message.data);
          break;
        case EventTypes.VOICE_DATA_SEND:
          try {
            // Extract correlation ID for request-response tracking
            const correlationId = message.meta?.correlationId || message.correlationId;
            
            wsManager.handleAudio(user, message.data);
            
            // Send success response with correlation tracking if requested
            if (correlationId) {
              const successMessage = WebSocketUtils.success("VOICE_DATA_SENT", {
                userId: user,
                dataSize: message.data?.length || 0,
              }, {
                correlationId: correlationId,
              });
              WebSocketUtils.send(ws, successMessage);
            }
          } catch (error) {
            appLogger.error("Error handling voice data send:", undefined, error);
            const errorMessage = WebSocketUtils.internalError(
              `Failed to send voice data: ${error instanceof Error ? error.message : "Unknown error"}`,
            );
            WebSocketUtils.send(ws, errorMessage);
          }
          break;
        default:
          console.error("Unknown message type:", message.type);
      } */
    },
    async close(
      ws: ServerWebSocket<CustomWebSocketData>,
      code: number,
      reason: string,
    ) {
      const { userId } = ws.data;
      //console.log(`WebSocket closed for user ${userId} with code ${code}, reason: ${reason}`);

      // Handle disconnection (will update user status to OFFLINE)
      await wsManager.handleDisconnection(ws);

      // Remove from connection tracking
      connectionStatus.delete(ws); // Remove from connection status map
    },
  }, // handlers
  async fetch(req, server) {
    const url = new URL(req.url);
    const origin = req.headers.get("origin");
    const matchedRoute = router.match(req);

    if (
      //req.headers.get("Upgrade") === "websocket" &&
      server.upgrade<CustomWebSocketData>(req, {
        data: {
          // Extract userId, other relevant data from the request, and add type
          userId: url.searchParams.get("userId") || "",
          token: url.searchParams.get("token") || "",
          isAuthenticated: false,
          isAlive: true,
        },
      })
    ) {
      return; // Do not return a Response in case of WebSocket upgrade
    }

    // Handle OPTIONS requests for CORS preflight
    // if (req.method === "OPTIONS") {
    //   //console.log(req)
    //   return new Response(null, {
    //     status: 204,
    //     headers: corsHeaders,
    //   });
    // }
    // Handle CORS preflight
    const corsResponse = corsHandler(req);
    if (corsResponse) {
      return corsResponse;
    }

    if (matchedRoute) {
      try {
        let response = matchedRoute.handler.handle(
          req,
          matchedRoute.params,
          server,
        );

        if (response instanceof Promise) {
          response = await response;
        }

        if (response) {
          let res: Response;

          // Check if the handler already returned a Response object
          if (response instanceof Response) {
            res = response; // Use it directly
            // Add CORS headers if they are not present already
            for (const [key, value] of Object.entries(corsHeaders)) {
              if (!res.headers.has(key)) {
                res.headers.set(key, value);
              }
            }
          } else {
            // Create a new Response, handling body and headers
            res = new Response(response, {
              headers: {
                ...corsHeaders, // Apply CORS headers
              },
            });
          }

          //return res;

          // Add CORS headers to all responses
          return addCORSHeaders(response, corsOptions, origin);
        }
      } catch (error) {
        // ... (error handling)
      }

      return;
    }

    return new Response("Not Found", { status: 404, headers: corsHeaders });
  },
  error(error) {
    return new Response(`<pre>${error}\n${error.stack}</pre>`, {
      headers: {
        "Content-Type": "text/html",
        ...corsHeaders,
      },
    });
  },
});

appLogger.info(`Server listening on http://localhost:3000`);

// Set up periodic ping/pong (heartbeat)
setInterval(() => {
  // Get all WebSocket connections from all topics
  const allSockets: ServerWebSocket<CustomWebSocketData>[] = [];

  // Collect all sockets from all topics
  for (const socketSet of wsManager.topicSubscriptions.values()) {
    for (const socket of socketSet) {
      allSockets.push(socket);
    }
  }

  // Check each socket
  for (const ws of allSockets) {
    const status = connectionStatus.get(ws);
    if (status && status.isAlive === false) {
      appLogger.debug(
        `Terminating connection due to inactivity: ${ws.data.userId}`,
      );
      ws.close();
      continue;
    }

    if (status) {
      status.isAlive = false;
    }
    ws.ping();
  }
}, PING_INTERVAL);

// WebRTC signaling is handled directly in the message switch statement
