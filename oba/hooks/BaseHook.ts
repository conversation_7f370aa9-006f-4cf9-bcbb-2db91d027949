import { readFile } from "fs/promises";
import { existsSync } from "fs";
import { logger } from "../services/logger.service";
import type {
  Hook,
  HookContext,
  HookR<PERSON>ult,
  HookTrigger,
  HookPriority,
  HookSeverity,
  QuickFix,
} from "./types";

export abstract class <PERSON>Hook implements Hook {
  public abstract readonly id: string;
  public abstract readonly name: string;
  public abstract readonly description: string;
  public abstract readonly triggers: HookTrigger[];
  public abstract readonly priority: HookPriority;
  public enabled: boolean = true;

  protected logger = logger.createLogger(`Hook:${this.constructor.name}`);

  abstract execute(context: HookContext): Promise<HookResult>;

  /**
   * Create a successful hook result
   */
  protected createSuccessResult(
    message: string,
    quickFixes?: QuickFix[],
  ): HookResult {
    return {
      success: true,
      message,
      quickFixes,
      severity: "success" as HookSeverity,
    };
  }

  /**
   * Create an error hook result
   */
  protected createErrorResult(
    message: string,
    quickFixes?: QuickFix[],
  ): HookResult {
    return {
      success: false,
      message,
      quickFixes,
      severity: "error" as HookSeverity,
    };
  }

  /**
   * Create a warning hook result
   */
  protected createWarningResult(
    message: string,
    quickFixes?: QuickFix[],
  ): HookResult {
    return {
      success: true,
      message,
      quickFixes,
      severity: "warning" as HookSeverity,
    };
  }

  /**
   * Create an info hook result
   */
  protected createInfoResult(
    message: string,
    quickFixes?: QuickFix[],
  ): HookResult {
    return {
      success: true,
      message,
      quickFixes,
      severity: "info" as HookSeverity,
    };
  }

  /**
   * Check if a file exists
   */
  protected async fileExists(filePath: string): Promise<boolean> {
    return existsSync(filePath);
  }

  /**
   * Read file content
   */
  protected async readFileContent(filePath: string): Promise<string> {
    try {
      return await readFile(filePath, "utf-8");
    } catch (error) {
      this.logger.error(`Failed to read file ${filePath}:`, undefined, error);
      throw error;
    }
  }

  /**
   * Get line number for a text match in content
   */
  protected getLineNumber(content: string, searchText: string): number {
    const lines = content.split("\n");
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes(searchText)) {
        return i + 1;
      }
    }
    return 1;
  }

  /**
   * Run a shell command and return the result
   */
  protected async runCommand(
    command: string,
    cwd?: string,
  ): Promise<{ stdout: string; stderr: string; exitCode: number }> {
    const { spawn } = await import("child_process");

    return new Promise((resolve, reject) => {
      const [cmd, ...args] = command.split(" ");
      const child = spawn(cmd, args, {
        cwd: cwd || process.cwd(),
        stdio: "pipe",
      });

      let stdout = "";
      let stderr = "";

      child.stdout?.on("data", (data) => {
        stdout += data.toString();
      });

      child.stderr?.on("data", (data) => {
        stderr += data.toString();
      });

      child.on("close", (code) => {
        resolve({
          stdout: stdout.trim(),
          stderr: stderr.trim(),
          exitCode: code || 0,
        });
      });

      child.on("error", (error) => {
        reject(error);
      });
    });
  }

  /**
   * Show a notification to the user
   */
  protected async showNotification(
    message: string,
    type: "info" | "success" | "warning" | "error" = "info",
  ): Promise<void> {
    // This would integrate with the IDE's notification system
    // For now, we'll just log it
    this.logger.info(`[${type.toUpperCase()}] ${message}`);
  }

  /**
   * Create a quick fix that runs a command
   */
  protected createCommandQuickFix(
    description: string,
    command: string,
    cwd?: string,
  ): QuickFix {
    return {
      description,
      action: async () => {
        try {
          await this.runCommand(command, cwd);
          await this.showNotification(
            `Successfully executed: ${description}`,
            "success",
          );
        } catch (error) {
          await this.showNotification(
            `Failed to execute: ${description}`,
            "error",
          );
          throw error;
        }
      },
    };
  }

  /**
   * Create a quick fix that writes content to a file
   */
  protected createFileWriteQuickFix(
    description: string,
    filePath: string,
    content: string,
  ): QuickFix {
    return {
      description,
      action: async () => {
        try {
          const { writeFile } = await import("fs/promises");
          await writeFile(filePath, content, "utf-8");
          await this.showNotification(
            `Successfully updated: ${filePath}`,
            "success",
          );
        } catch (error) {
          await this.showNotification(`Failed to update: ${filePath}`, "error");
          throw error;
        }
      },
    };
  }

  /**
   * Format multiple messages into a single result message
   */
  protected formatMessages(messages: string[]): string {
    if (messages.length === 0) return "";
    if (messages.length === 1) return messages[0];

    return messages.map((msg, index) => `${index + 1}. ${msg}`).join("\n");
  }

  /**
   * Debounce function execution
   */
  protected debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;

    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }
}
