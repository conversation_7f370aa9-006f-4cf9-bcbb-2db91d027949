{"hooks": {"eslint-check": {"enabled": true, "priority": "high", "triggers": ["file_save"], "patterns": ["**/*.ts", "**/*.js"], "options": {"autoFix": true, "showWarnings": true, "configFile": ".eslintrc.json"}}, "typescript-check": {"enabled": true, "priority": "high", "triggers": ["file_save"], "patterns": ["**/*.ts"], "options": {"strict": true, "showErrors": true, "configFile": "tsconfig.json"}}, "prettier-format": {"enabled": true, "priority": "medium", "triggers": ["file_save"], "patterns": ["**/*.ts", "**/*.js", "**/*.json"], "options": {"autoFormat": false, "configFile": ".prettier<PERSON>"}}, "smart-test-runner": {"enabled": true, "priority": "medium", "triggers": ["file_save"], "patterns": ["oba/handlers/**/*.ts", "oba/db/**/*.ts", "oba/tests/**/*.test.ts"], "options": {"runOnSave": true, "showCoverage": true, "parallelExecution": true, "testTimeout": 30000}}, "schema-validation": {"enabled": true, "priority": "high", "triggers": ["file_save"], "patterns": ["oba/db/schema.ts"], "options": {"autoGenerateMigration": false, "validateRelationships": true, "checkBreakingChanges": true}}, "api-documentation": {"enabled": true, "priority": "low", "triggers": ["file_save"], "patterns": ["oba/handlers/**/*.ts", "oba/routes/**/*.ts"], "options": {"autoUpdate": false, "generateOpenAPI": true, "validateEndpoints": true}}, "security-scanner": {"enabled": true, "priority": "high", "triggers": ["file_save"], "patterns": ["oba/handlers/**/*.ts", "oba/middlewares/**/*.ts"], "options": {"checkSQLInjection": true, "checkAuthentication": true, "checkInputValidation": true, "severity": "warning"}}, "websocket-protocol": {"enabled": true, "priority": "medium", "triggers": ["file_save"], "patterns": ["oba/manager/**/*.ts", "oba/handlers/**/*.ts"], "options": {"validateEventTypes": true, "checkConsistency": true, "updateDefinitions": false}}}, "global": {"maxConcurrentHooks": 3, "timeoutMs": 30000, "showNotifications": true, "logLevel": "info"}}