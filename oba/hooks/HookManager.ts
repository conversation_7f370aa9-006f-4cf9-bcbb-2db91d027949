import { EventEmitter } from "events";
import { minimatch } from "minimatch";
import { logger } from "../services/logger.service";
import type {
  Hook,
  HookEvent,
  HookResult,
  HookStatus,
  HookExecution,
  HookManagerConfig,
  EventType,
  HookPriority,
} from "./types";

export class HookManager extends EventEmitter {
  private hooks: Map<string, Hook> = new Map();
  private executions: Map<string, HookExecution> = new Map();
  private config: HookManagerConfig;
  private hookLogger = logger.createLogger("HookManager");

  constructor(config: HookManagerConfig) {
    super();
    this.config = config;
  }

  /**
   * Register a hook with the manager
   */
  registerHook(hook: Hook): void {
    this.hookLogger.info(`Registering hook: ${hook.id}`);
    this.hooks.set(hook.id, hook);
    this.emit("hookRegistered", hook);
  }

  /**
   * Unregister a hook from the manager
   */
  unregisterHook(hookId: string): void {
    this.hookLogger.info(`Unregistering hook: ${hookId}`);
    this.hooks.delete(hookId);
    this.executions.delete(hookId);
    this.emit("hookUnregistered", hookId);
  }

  /**
   * Execute all applicable hooks for an event
   */
  async executeHooks(event: HookEvent): Promise<HookResult[]> {
    const applicableHooks = this.getApplicableHooks(event);

    if (applicableHooks.length === 0) {
      this.hookLogger.debug(
        `No applicable hooks for event: ${event.type} on ${event.filePath}`,
      );
      return [];
    }

    this.hookLogger.info(
      `Executing ${applicableHooks.length} hooks for ${event.filePath}`,
    );

    // Sort hooks by priority
    const sortedHooks = this.sortHooksByPriority(applicableHooks);

    // Execute hooks with concurrency limit
    const results = await this.executeHooksWithConcurrency(sortedHooks, event);

    this.emit("hooksExecuted", { event, results });
    return results;
  }

  /**
   * Get the current status of a hook
   */
  getHookStatus(hookId: string): HookStatus {
    const execution = this.executions.get(hookId);
    return execution?.status || HookStatus.IDLE;
  }

  /**
   * Get all registered hooks
   */
  getRegisteredHooks(): Hook[] {
    return Array.from(this.hooks.values());
  }

  /**
   * Get hook execution history
   */
  getHookExecutions(): HookExecution[] {
    return Array.from(this.executions.values());
  }

  /**
   * Update hook configuration
   */
  updateConfig(config: Partial<HookManagerConfig>): void {
    this.config = { ...this.config, ...config };
    this.emit("configUpdated", this.config);
  }

  /**
   * Enable or disable a specific hook
   */
  setHookEnabled(hookId: string, enabled: boolean): void {
    const hook = this.hooks.get(hookId);
    if (hook) {
      hook.enabled = enabled;
      this.hookLogger.info(
        `Hook ${hookId} ${enabled ? "enabled" : "disabled"}`,
      );
      this.emit("hookToggled", { hookId, enabled });
    }
  }

  /**
   * Cancel a running hook execution
   */
  cancelHookExecution(hookId: string): void {
    const execution = this.executions.get(hookId);
    if (execution && execution.status === HookStatus.RUNNING) {
      execution.status = HookStatus.ERROR;
      execution.endTime = new Date();
      execution.error = new Error("Hook execution cancelled by user");
      this.hookLogger.info(`Cancelled hook execution: ${hookId}`);
      this.emit("hookCancelled", hookId);
    }
  }

  private getApplicableHooks(event: HookEvent): Hook[] {
    return Array.from(this.hooks.values()).filter((hook) => {
      if (!hook.enabled) return false;

      return hook.triggers.some((trigger) => {
        if (trigger.event !== event.type) return false;
        return minimatch(event.filePath, trigger.pattern);
      });
    });
  }

  private sortHooksByPriority(hooks: Hook[]): Hook[] {
    const priorityOrder = {
      [HookPriority.CRITICAL]: 0,
      [HookPriority.HIGH]: 1,
      [HookPriority.MEDIUM]: 2,
      [HookPriority.LOW]: 3,
    };

    return hooks.sort(
      (a, b) => priorityOrder[a.priority] - priorityOrder[b.priority],
    );
  }

  private async executeHooksWithConcurrency(
    hooks: Hook[],
    event: HookEvent,
  ): Promise<HookResult[]> {
    const maxConcurrent = this.config.global.maxConcurrentHooks;
    const results: HookResult[] = [];

    // Execute hooks in batches based on concurrency limit
    for (let i = 0; i < hooks.length; i += maxConcurrent) {
      const batch = hooks.slice(i, i + maxConcurrent);
      const batchPromises = batch.map((hook) => this.executeHook(hook, event));
      const batchResults = await Promise.allSettled(batchPromises);

      batchResults.forEach((result, index) => {
        if (result.status === "fulfilled") {
          results.push(result.value);
        } else {
          // Handle rejected promises
          const hook = batch[index];
          results.push({
            success: false,
            message: `Hook execution failed: ${result.reason?.message || "Unknown error"}`,
            severity: "error" as const,
          });
          this.hookLogger.error(
            `Hook ${hook.id} execution failed:`,
            undefined,
            result.reason,
          );
        }
      });
    }

    return results;
  }

  private async executeHook(hook: Hook, event: HookEvent): Promise<HookResult> {
    const execution: HookExecution = {
      hookId: hook.id,
      status: HookStatus.RUNNING,
      startTime: new Date(),
    };

    this.executions.set(hook.id, execution);
    this.emit("hookStarted", { hookId: hook.id, execution });

    try {
      this.hookLogger.debug(`Executing hook: ${hook.id}`);

      // Create hook context
      const context = {
        filePath: event.filePath,
        fileContent: event.fileContent,
        event,
        metadata: event.metadata,
      };

      // Execute hook with timeout
      const result = await this.executeWithTimeout(
        hook.execute(context),
        this.config.global.timeoutMs,
      );

      // Update execution record
      execution.status = result.success ? HookStatus.SUCCESS : HookStatus.ERROR;
      execution.endTime = new Date();
      execution.result = result;
      result.executionTime =
        execution.endTime.getTime() - execution.startTime.getTime();

      this.hookLogger.debug(
        `Hook ${hook.id} completed in ${result.executionTime}ms`,
      );
      this.emit("hookCompleted", { hookId: hook.id, execution, result });

      return result;
    } catch (error) {
      execution.status = HookStatus.ERROR;
      execution.endTime = new Date();
      execution.error = error as Error;

      const errorResult: HookResult = {
        success: false,
        message: `Hook execution failed: ${(error as Error).message}`,
        severity: "error" as const,
        executionTime:
          execution.endTime.getTime() - execution.startTime.getTime(),
      };

      this.hookLogger.error(`Hook ${hook.id} failed:`, undefined, error);
      this.emit("hookFailed", { hookId: hook.id, execution, error });

      return errorResult;
    }
  }

  private async executeWithTimeout<T>(
    promise: Promise<T>,
    timeoutMs: number,
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Hook execution timed out after ${timeoutMs}ms`));
      }, timeoutMs);

      promise
        .then((result) => {
          clearTimeout(timeout);
          resolve(result);
        })
        .catch((error) => {
          clearTimeout(timeout);
          reject(error);
        });
    });
  }
}
