// Hook system types and interfaces

export enum EventType {
  FILE_SAVE = "file_save",
  PRE_COMMIT = "pre_commit",
  TEST_RUN = "test_run",
  SCHEMA_CHANGE = "schema_change",
  API_CHANGE = "api_change",
}

export enum HookPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical",
}

export enum HookSeverity {
  INFO = "info",
  SUCCESS = "success",
  WARNING = "warning",
  ERROR = "error",
}

export interface HookTrigger {
  event: EventType;
  pattern: string;
}

export interface HookContext {
  filePath: string;
  fileContent?: string;
  event: HookEvent;
  metadata?: Record<string, any>;
}

export interface HookEvent {
  type: EventType;
  filePath: string;
  fileContent?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface QuickFix {
  description: string;
  action: () => Promise<void> | void;
}

export interface HookResult {
  success: boolean;
  message: string;
  quickFixes?: QuickFix[];
  severity: HookSeverity;
  executionTime?: number;
  metadata?: Record<string, any>;
}

export interface Hook {
  id: string;
  name: string;
  description: string;
  triggers: HookTrigger[];
  priority: HookPriority;
  enabled: boolean;
  execute(context: HookContext): Promise<HookResult>;
}

export interface HookConfig {
  enabled: boolean;
  priority: HookPriority;
  triggers: string[];
  patterns: string[];
  options: Record<string, any>;
}

export interface HookManagerConfig {
  hooks: Record<string, HookConfig>;
  global: {
    maxConcurrentHooks: number;
    timeoutMs: number;
    showNotifications: boolean;
    logLevel: "debug" | "info" | "warn" | "error";
  };
}

export enum HookStatus {
  IDLE = "idle",
  RUNNING = "running",
  SUCCESS = "success",
  ERROR = "error",
  TIMEOUT = "timeout",
}

export interface HookExecution {
  hookId: string;
  status: HookStatus;
  startTime: Date;
  endTime?: Date;
  result?: HookResult;
  error?: Error;
}

export interface SecurityIssue {
  type: string;
  message: string;
  line: number;
  suggestion: string;
  severity: HookSeverity;
}

export interface TestResult {
  success: boolean;
  passed: number;
  failed: number;
  skipped: number;
  coverage?: number;
  failures: Array<{
    test: string;
    error: string;
  }>;
}

export interface APIEndpoint {
  path: string;
  method: string;
  handler: string;
  parameters?: Array<{
    name: string;
    type: string;
    required: boolean;
  }>;
  responses?: Array<{
    status: number;
    description: string;
  }>;
}
