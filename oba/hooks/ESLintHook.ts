import { ESLint } from "eslint";
import { BaseHook } from "./BaseHook";
import type {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Trigger,
  HookPriority,
  EventType,
  QuickFix,
} from "./types";

export class E<PERSON>intHook extends BaseHook {
  public readonly id = "eslint-check";
  public readonly name = "ESLint Code Quality Check";
  public readonly description =
    "Runs ESLint to check code quality and style issues";
  public readonly priority: HookPriority = "high";
  public readonly triggers: HookTrigger[] = [
    { event: "file_save" as EventType, pattern: "**/*.ts" },
    { event: "file_save" as EventType, pattern: "**/*.js" },
  ];

  private eslint: ESLint | null = null;

  async execute(context: HookContext): Promise<HookResult> {
    try {
      this.logger.debug(`Running ESLint check on ${context.filePath}`);

      // Initialize ESLint if not already done
      if (!this.eslint) {
        await this.initializeESLint();
      }

      if (!this.eslint) {
        return this.createErrorResult("Failed to initialize ESLint");
      }

      // Check if file should be linted
      const isPathIgnored = await this.eslint.isPathIgnored(context.filePath);
      if (isPathIgnored) {
        return this.createInfoResult(
          `File ${context.filePath} is ignored by ESLint`,
        );
      }

      // Run ESLint on the file
      const results = await this.eslint.lintFiles([context.filePath]);

      if (results.length === 0) {
        return this.createInfoResult("No ESLint results returned");
      }

      const result = results[0];
      const { errorCount, warningCount, messages } = result;

      // Generate quick fixes
      const quickFixes = await this.generateQuickFixes(result);

      // Format the result message
      const message = this.formatESLintResult(result);

      if (errorCount > 0) {
        return this.createErrorResult(message, quickFixes);
      } else if (warningCount > 0) {
        return this.createWarningResult(message, quickFixes);
      } else {
        return this.createSuccessResult("No ESLint issues found");
      }
    } catch (error) {
      this.logger.error("ESLint hook execution failed:", undefined, error);
      return this.createErrorResult(
        `ESLint execution failed: ${(error as Error).message}`,
      );
    }
  }

  private async initializeESLint(): Promise<void> {
    try {
      this.eslint = new ESLint({
        useEslintrc: true,
        fix: false, // We'll handle fixes separately
      });
      this.logger.debug("ESLint initialized successfully");
    } catch (error) {
      this.logger.error("Failed to initialize ESLint:", undefined, error);
      throw error;
    }
  }

  private formatESLintResult(result: ESLint.LintResult): string {
    const { errorCount, warningCount, messages } = result;

    if (errorCount === 0 && warningCount === 0) {
      return "No ESLint issues found";
    }

    const summary = [];
    if (errorCount > 0) {
      summary.push(`${errorCount} error${errorCount > 1 ? "s" : ""}`);
    }
    if (warningCount > 0) {
      summary.push(`${warningCount} warning${warningCount > 1 ? "s" : ""}`);
    }

    const summaryText = `Found ${summary.join(" and ")}`;

    // Format individual messages
    const formattedMessages = messages
      .slice(0, 5) // Limit to first 5 messages to avoid overwhelming output
      .map((msg) => {
        const severity = msg.severity === 2 ? "error" : "warning";
        return `  ${msg.line}:${msg.column} ${severity} ${msg.message} (${msg.ruleId || "unknown"})`;
      })
      .join("\n");

    let fullMessage = summaryText;
    if (formattedMessages) {
      fullMessage += "\n\n" + formattedMessages;
    }

    if (messages.length > 5) {
      fullMessage += `\n  ... and ${messages.length - 5} more issues`;
    }

    return fullMessage;
  }

  private async generateQuickFixes(
    result: ESLint.LintResult,
  ): Promise<QuickFix[]> {
    const quickFixes: QuickFix[] = [];

    // Check if there are fixable issues
    const fixableCount = result.messages.filter((msg) => msg.fix).length;

    if (fixableCount > 0) {
      quickFixes.push({
        description: `Auto-fix ${fixableCount} ESLint issue${fixableCount > 1 ? "s" : ""}`,
        action: async () => {
          await this.autoFixFile(result.filePath);
        },
      });
    }

    // Add quick fix for running ESLint with --fix flag
    if (result.errorCount > 0 || result.warningCount > 0) {
      quickFixes.push(
        this.createCommandQuickFix(
          "Run ESLint with --fix flag",
          `bun run lint --fix ${result.filePath}`,
        ),
      );
    }

    // Add quick fix for specific common rules
    const commonRules = this.getCommonRuleFixes(result.messages);
    quickFixes.push(...commonRules);

    return quickFixes;
  }

  private async autoFixFile(filePath: string): Promise<void> {
    try {
      if (!this.eslint) {
        throw new Error("ESLint not initialized");
      }

      // Create a new ESLint instance with fix enabled
      const fixingESLint = new ESLint({
        useEslintrc: true,
        fix: true,
      });

      const results = await fixingESLint.lintFiles([filePath]);

      // Apply fixes
      await ESLint.outputFixes(results);

      await this.showNotification(
        `Auto-fixed ESLint issues in ${filePath}`,
        "success",
      );
    } catch (error) {
      this.logger.error("Failed to auto-fix ESLint issues:", undefined, error);
      throw error;
    }
  }

  private getCommonRuleFixes(messages: ESLint.LintMessage[]): QuickFix[] {
    const quickFixes: QuickFix[] = [];
    const ruleCount = new Map<string, number>();

    // Count occurrences of each rule
    messages.forEach((msg) => {
      if (msg.ruleId) {
        ruleCount.set(msg.ruleId, (ruleCount.get(msg.ruleId) || 0) + 1);
      }
    });

    // Generate fixes for common rules
    ruleCount.forEach((count, ruleId) => {
      const fix = this.getQuickFixForRule(ruleId, count);
      if (fix) {
        quickFixes.push(fix);
      }
    });

    return quickFixes;
  }

  private getQuickFixForRule(ruleId: string, count: number): QuickFix | null {
    switch (ruleId) {
      case "@typescript-eslint/no-unused-vars":
        return {
          description: `Remove ${count} unused variable${count > 1 ? "s" : ""}`,
          action: async () => {
            await this.showNotification(
              "Please manually remove unused variables",
              "info",
            );
          },
        };

      case "no-console":
        return {
          description: `Remove ${count} console statement${count > 1 ? "s" : ""}`,
          action: async () => {
            await this.showNotification(
              "Please manually remove console statements",
              "info",
            );
          },
        };

      case "@typescript-eslint/explicit-function-return-type":
        return {
          description: `Add return types to ${count} function${count > 1 ? "s" : ""}`,
          action: async () => {
            await this.showNotification(
              "Please manually add return types to functions",
              "info",
            );
          },
        };

      default:
        return null;
    }
  }
}
