import type { <PERSON>Handler } from "./routeHandler";

class Router {
  routes: Record<
    string,
    {
      handler: RouteHandler;
      params: Record<string, string>;
    }
  >;

  constructor() {
    this.routes = {};
  }

  add(route: string, handler: RouteHandler): Router {
    this.routes[route] = { handler, params: {} };
    return this;
  }

  match(
    req: Request,
  ): { handler: RouteHandler; params: Record<string, string> } | null {
    const url = new URL(req.url);
    let matchedRoute = null;

    // Exact match first
    for (const route in this.routes) {
      if (route === url.pathname) {
        matchedRoute = this.routes[route];
        break;
      }
    }

    // Dynamic routes
    if (!matchedRoute) {
      for (const route in this.routes) {
        if (route.includes(":")) {
          const [pattern, paramName] = route.split(":");
          const regex = new RegExp(`^${pattern.replace(/\/$/, "")}/(.+)`);
          const match = url.pathname.match(regex);

          if (match) {
            matchedRoute = this.routes[route];
            matchedRoute.params = { [paramName]: match[1] };
            break;
          }
        }
      }
    }

    return matchedRoute;
  }
}

export default Router;
