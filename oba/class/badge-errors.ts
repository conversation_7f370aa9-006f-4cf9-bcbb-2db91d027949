/**
 * Badge-specific error classes extending base Error class
 * These errors provide specific error codes and messages for badge operations
 */

export class BadgeError extends <PERSON>rror {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly details?: Record<string, any>;

  constructor(message: string, code: string, statusCode: number = 400, details?: Record<string, any>) {
    super(message);
    this.name = 'BadgeError';
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
    
    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, BadgeError);
    }
  }
}

/**
 * Thrown when a badge type is not found
 */
export class BadgeNotFoundError extends BadgeError {
  constructor(badgeId: string) {
    super(
      `Badge not found: ${badgeId}`,
      'BADGE_NOT_FOUND',
      404,
      { badgeId }
    );
    this.name = 'BadgeNotFoundError';
  }
}

/**
 * Thrown when a user already has a specific badge
 */
export class BadgeAlreadyAssignedError extends BadgeError {
  constructor(userId: string, badgeTypeId: string) {
    super(
      'Badge already assigned to user',
      'BADGE_ALREADY_ASSIGNED',
      409,
      { userId, badgeTypeId }
    );
    this.name = 'BadgeAlreadyAssignedError';
  }
}

/**
 * Thrown when user lacks permissions for badge operations
 */
export class InsufficientPermissionsError extends BadgeError {
  constructor(operation: string = 'badge operation') {
    super(
      `Insufficient permissions for ${operation}`,
      'INSUFFICIENT_PERMISSIONS',
      403,
      { operation }
    );
    this.name = 'InsufficientPermissionsError';
  }
}

/**
 * Thrown when badge validation fails
 */
export class BadgeValidationError extends BadgeError {
  constructor(message: string, validationErrors?: Record<string, string[]>) {
    super(
      message,
      'BADGE_VALIDATION_ERROR',
      400,
      { validationErrors }
    );
    this.name = 'BadgeValidationError';
  }
}

/**
 * Thrown when badge criteria evaluation fails
 */
export class BadgeEvaluationError extends BadgeError {
  constructor(userId: string, badgeTypeId: string, reason: string) {
    super(
      `Badge evaluation failed for user ${userId}: ${reason}`,
      'BADGE_EVALUATION_ERROR',
      500,
      { userId, badgeTypeId, reason }
    );
    this.name = 'BadgeEvaluationError';
  }
}

/**
 * Thrown when badge assignment fails due to business logic constraints
 */
export class BadgeAssignmentError extends BadgeError {
  constructor(message: string, userId: string, badgeTypeId: string) {
    super(
      message,
      'BADGE_ASSIGNMENT_ERROR',
      400,
      { userId, badgeTypeId }
    );
    this.name = 'BadgeAssignmentError';
  }
}

/**
 * Thrown when badge removal fails
 */
export class BadgeRemovalError extends BadgeError {
  constructor(userId: string, badgeTypeId: string, reason: string) {
    super(
      `Failed to remove badge from user ${userId}: ${reason}`,
      'BADGE_REMOVAL_ERROR',
      400,
      { userId, badgeTypeId, reason }
    );
    this.name = 'BadgeRemovalError';
  }
}

/**
 * Thrown when badge type creation fails due to duplicate name
 */
export class DuplicateBadgeNameError extends BadgeError {
  constructor(badgeName: string) {
    super(
      `Badge with name '${badgeName}' already exists`,
      'DUPLICATE_BADGE_NAME',
      409,
      { badgeName }
    );
    this.name = 'DuplicateBadgeNameError';
  }
}

/**
 * Thrown when badge type is inactive and cannot be assigned
 */
export class InactiveBadgeError extends BadgeError {
  constructor(badgeTypeId: string) {
    super(
      'Cannot assign inactive badge type',
      'INACTIVE_BADGE_ERROR',
      400,
      { badgeTypeId }
    );
    this.name = 'InactiveBadgeError';
  }
}

/**
 * Thrown when user statistics cannot be calculated
 */
export class UserStatsError extends BadgeError {
  constructor(userId: string, reason: string) {
    super(
      `Failed to calculate user statistics for ${userId}: ${reason}`,
      'USER_STATS_ERROR',
      500,
      { userId, reason }
    );
    this.name = 'UserStatsError';
  }
}

/**
 * Thrown when badge criteria is invalid or malformed
 */
export class InvalidBadgeCriteriaError extends BadgeError {
  constructor(criteriaType: string, reason: string) {
    super(
      `Invalid badge criteria for type '${criteriaType}': ${reason}`,
      'INVALID_BADGE_CRITERIA',
      400,
      { criteriaType, reason }
    );
    this.name = 'InvalidBadgeCriteriaError';
  }
}

/**
 * Thrown when badge operation exceeds rate limits
 */
export class BadgeRateLimitError extends BadgeError {
  constructor(operation: string, retryAfter: number) {
    super(
      `Rate limit exceeded for ${operation}. Try again in ${retryAfter} seconds`,
      'BADGE_RATE_LIMIT',
      429,
      { operation, retryAfter }
    );
    this.name = 'BadgeRateLimitError';
  }
}

/**
 * Thrown when a badge collection is not found
 */
export class BadgeCollectionNotFoundError extends BadgeError {
  constructor(collectionId: string) {
    super(
      `Badge collection not found: ${collectionId}`,
      'BADGE_COLLECTION_NOT_FOUND',
      404,
      { collectionId }
    );
    this.name = 'BadgeCollectionNotFoundError';
  }
}

/**
 * Thrown when a badge collection already exists with the same ID
 */
export class DuplicateCollectionIdError extends BadgeError {
  constructor(collectionId: string) {
    super(
      `Badge collection with ID '${collectionId}' already exists`,
      'DUPLICATE_COLLECTION_ID',
      409,
      { collectionId }
    );
    this.name = 'DuplicateCollectionIdError';
  }
}

/**
 * Thrown when trying to assign a badge that violates collection order
 */
export class BadgeCollectionOrderError extends BadgeError {
  constructor(badgeId: string, collectionId: string, requiredPreviousBadge: string) {
    super(
      `Badge '${badgeId}' cannot be assigned before '${requiredPreviousBadge}' in collection '${collectionId}'`,
      'BADGE_COLLECTION_ORDER_ERROR',
      400,
      { badgeId, collectionId, requiredPreviousBadge }
    );
    this.name = 'BadgeCollectionOrderError';
  }
}

/**
 * Thrown when badge design validation fails
 */
export class BadgeDesignValidationError extends BadgeError {
  constructor(designErrors: Record<string, string[]>) {
    super(
      'Badge design validation failed',
      'BADGE_DESIGN_VALIDATION_ERROR',
      400,
      { designErrors }
    );
    this.name = 'BadgeDesignValidationError';
  }
}

/**
 * Thrown when badge nomination fails
 */
export class BadgeNominationError extends BadgeError {
  constructor(message: string, nomineeUserId: string, badgeTypeId: string) {
    super(
      message,
      'BADGE_NOMINATION_ERROR',
      400,
      { nomineeUserId, badgeTypeId }
    );
    this.name = 'BadgeNominationError';
  }
}

/**
 * Thrown when nomination is not found
 */
export class NominationNotFoundError extends BadgeError {
  constructor(nominationId: string) {
    super(
      `Badge nomination not found: ${nominationId}`,
      'NOMINATION_NOT_FOUND',
      404,
      { nominationId }
    );
    this.name = 'NominationNotFoundError';
  }
}

/**
 * Thrown when user tries to nominate themselves
 */
export class SelfNominationError extends BadgeError {
  constructor(userId: string) {
    super(
      'Users cannot nominate themselves for badges',
      'SELF_NOMINATION_ERROR',
      400,
      { userId }
    );
    this.name = 'SelfNominationError';
  }
}

/**
 * Thrown when nomination already exists
 */
export class DuplicateNominationError extends BadgeError {
  constructor(nominatorUserId: string, nomineeUserId: string, badgeTypeId: string) {
    super(
      'Nomination already exists for this user and badge',
      'DUPLICATE_NOMINATION',
      409,
      { nominatorUserId, nomineeUserId, badgeTypeId }
    );
    this.name = 'DuplicateNominationError';
  }
}

/**
 * Thrown when collection completion reward fails
 */
export class CollectionRewardError extends BadgeError {
  constructor(userId: string, collectionId: string, reason: string) {
    super(
      `Failed to grant collection completion reward for user ${userId}: ${reason}`,
      'COLLECTION_REWARD_ERROR',
      500,
      { userId, collectionId, reason }
    );
    this.name = 'CollectionRewardError';
  }
}

/**
 * Thrown when badge perks cannot be granted
 */
export class BadgePerksError extends BadgeError {
  constructor(userId: string, badgeTypeId: string, perks: string[], reason: string) {
    super(
      `Failed to grant badge perks for user ${userId}: ${reason}`,
      'BADGE_PERKS_ERROR',
      500,
      { userId, badgeTypeId, perks, reason }
    );
    this.name = 'BadgePerksError';
  }
}

/**
 * Utility function to check if an error is a badge-related error
 */
export function isBadgeError(error: any): error is BadgeError {
  return error instanceof BadgeError;
}

/**
 * Utility function to format badge errors for API responses
 */
export function formatBadgeError(error: BadgeError) {
  return {
    error: {
      code: error.code,
      message: error.message,
      details: error.details,
    },
    timestamp: new Date().toISOString(),
  };
}

/**
 * Error codes enum for easy reference
 */
export const BADGE_ERROR_CODES = {
  BADGE_NOT_FOUND: 'BADGE_NOT_FOUND',
  BADGE_ALREADY_ASSIGNED: 'BADGE_ALREADY_ASSIGNED',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  BADGE_VALIDATION_ERROR: 'BADGE_VALIDATION_ERROR',
  BADGE_EVALUATION_ERROR: 'BADGE_EVALUATION_ERROR',
  BADGE_ASSIGNMENT_ERROR: 'BADGE_ASSIGNMENT_ERROR',
  BADGE_REMOVAL_ERROR: 'BADGE_REMOVAL_ERROR',
  DUPLICATE_BADGE_NAME: 'DUPLICATE_BADGE_NAME',
  INACTIVE_BADGE_ERROR: 'INACTIVE_BADGE_ERROR',
  USER_STATS_ERROR: 'USER_STATS_ERROR',
  INVALID_BADGE_CRITERIA: 'INVALID_BADGE_CRITERIA',
  BADGE_RATE_LIMIT: 'BADGE_RATE_LIMIT',
  BADGE_COLLECTION_NOT_FOUND: 'BADGE_COLLECTION_NOT_FOUND',
  DUPLICATE_COLLECTION_ID: 'DUPLICATE_COLLECTION_ID',
  BADGE_COLLECTION_ORDER_ERROR: 'BADGE_COLLECTION_ORDER_ERROR',
  BADGE_DESIGN_VALIDATION_ERROR: 'BADGE_DESIGN_VALIDATION_ERROR',
  BADGE_NOMINATION_ERROR: 'BADGE_NOMINATION_ERROR',
  NOMINATION_NOT_FOUND: 'NOMINATION_NOT_FOUND',
  SELF_NOMINATION_ERROR: 'SELF_NOMINATION_ERROR',
  DUPLICATE_NOMINATION: 'DUPLICATE_NOMINATION',
  COLLECTION_REWARD_ERROR: 'COLLECTION_REWARD_ERROR',
  BADGE_PERKS_ERROR: 'BADGE_PERKS_ERROR',
} as const;

export type BadgeErrorCode = typeof BADGE_ERROR_CODES[keyof typeof BADGE_ERROR_CODES];