import type { Server, ServerWebSocket, WebSocketHandler } from "bun";
import authMiddleware from "../middlewares/authMiddleware";
import type { Middleware, RouteHandlerFunction } from "../types";
import { applyMiddleware } from "../utils";
import authWithRefreshMiddleware from "../middlewares/authWithRefreshMiddleware";

abstract class RouteHandler {
  middlewares: Middleware[];

  constructor(middlewares: Middleware[] = []) {
    this.middlewares = middlewares;
  }

  abstract handle(
    req: Request,
    params?: Record<string, string>,
    server?: Server,
  ): Response | Promise<Response>;

  protected applyMiddleware(
    req: Request,
    handler: RouteHandlerFunction,
  ): Response | Promise<Response> {
    return applyMiddleware(this.middlewares, handler)(req);
  }

  protected parseQueryParams(req: Request): Record<string, string> {
    const url = new URL(req.url);
    const queryParams: Record<string, string> = {};
    url.searchParams.forEach((value, key) => {
      queryParams[key] = value;
    });
    return queryParams;
  }
}

class PublicRouteHandler extends RouteHandler {
  handler: RouteHandlerFunction;

  constructor(handler: RouteHandlerFunction, middlewares: Middleware[] = []) {
    super(middlewares);
    this.handler = handler;
  }

  handle(
    req: Request,
    params?: Record<string, string>,
    server?: Server,
  ): Response | Promise<Response> {
    const queryParams = this.parseQueryParams(req);
    const mergedParams = { ...params, ...queryParams };
    return this.applyMiddleware(req, () => this.handler(req, mergedParams));
  }
}

class ProtectedRouteHandler extends RouteHandler {
  handler: RouteHandlerFunction;

  constructor(
    handler: RouteHandlerFunction,
    middlewares: Middleware[] = [authWithRefreshMiddleware],
  ) {
    super(middlewares);
    this.handler = handler;
  }

  handle(
    req: Request,
    params?: Record<string, string>,
    server?: Server,
  ): Response | Promise<Response> {
    const queryParams = this.parseQueryParams(req);
    const mergedParams = { ...params, ...queryParams };
    return this.applyMiddleware(req, () => this.handler(req, mergedParams));
  }
}

class WebSocketRouteHandler extends RouteHandler {
  handler: WebSocketHandler;
  sockets: Set<ServerWebSocket<unknown>>;

  constructor(handler: WebSocketHandler, middlewares: Middleware[] = []) {
    super(middlewares);
    this.handler = handler;
    this.sockets = new Set();
  }

  handle(
    req: Request,
    params?: Record<string, string>,
    server?: Server,
  ): Response | Promise<Response> {
    if (
      server &&
      req.headers.get("Connection") === "Upgrade" &&
      req.headers.get("Upgrade") === "websocket"
    ) {
      const queryParams = this.parseQueryParams(req);
      const mergedParams = { ...params, ...queryParams };
      const success = server.upgrade(req, {
        data: {
          params: mergedParams,
        },
        ...this.handler,
      });

      if (success) {
        return undefined; // Let Bun handle the WebSocket
      } else {
        return new Response("WebSocket upgrade failed", { status: 400 });
      }
    }

    return new Response("Not a WebSocket upgrade request", { status: 400 });
  }
}

export {
  RouteHandler,
  PublicRouteHandler,
  ProtectedRouteHandler,
  WebSocketRouteHandler,
};
