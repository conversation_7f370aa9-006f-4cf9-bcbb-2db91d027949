// Custom error class for authentication errors
export class AuthenticationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AuthenticationError";
  }
}

// Badge system error classes
export class BadgeError extends Error {
  public readonly code: string;
  
  constructor(message: string, code: string) {
    super(message);
    this.name = "BadgeError";
    this.code = code;
  }
}

export class BadgeNotFoundError extends BadgeError {
  constructor(badgeId: string) {
    super(`Badge not found: ${badgeId}`, "BADGE_NOT_FOUND");
    this.name = "BadgeNotFoundError";
  }
}

export class BadgeTypeNotFoundError extends BadgeError {
  constructor(badgeTypeId: string) {
    super(`Badge type not found: ${badgeTypeId}`, "BADGE_TYPE_NOT_FOUND");
    this.name = "BadgeTypeNotFoundError";
  }
}

export class BadgeAlreadyAssignedError extends BadgeError {
  constructor(userId: string, badgeTypeId: string) {
    super(`Badge already assigned to user ${userId}`, "BADGE_ALREADY_ASSIGNED");
    this.name = "BadgeAlreadyAssignedError";
  }
}

export class BadgeNotAssignedError extends BadgeError {
  constructor(userId: string, badgeTypeId: string) {
    super(`Badge not assigned to user ${userId}`, "BADGE_NOT_ASSIGNED");
    this.name = "BadgeNotAssignedError";
  }
}

export class InsufficientPermissionsError extends BadgeError {
  constructor(action: string = "badge operation") {
    super(`Insufficient permissions for ${action}`, "INSUFFICIENT_PERMISSIONS");
    this.name = "InsufficientPermissionsError";
  }
}

export class BadgeValidationError extends BadgeError {
  public readonly validationErrors: string[];
  
  constructor(message: string, validationErrors: string[] = []) {
    super(message, "BADGE_VALIDATION_ERROR");
    this.name = "BadgeValidationError";
    this.validationErrors = validationErrors;
  }
}

export class BadgeCriteriaError extends BadgeError {
  constructor(message: string) {
    super(message, "BADGE_CRITERIA_ERROR");
    this.name = "BadgeCriteriaError";
  }
}

export class BadgeEvaluationError extends BadgeError {
  constructor(message: string, userId?: string) {
    const errorMessage = userId 
      ? `Badge evaluation failed for user ${userId}: ${message}`
      : `Badge evaluation failed: ${message}`;
    super(errorMessage, "BADGE_EVALUATION_ERROR");
    this.name = "BadgeEvaluationError";
  }
}

export class BadgeAssignmentError extends BadgeError {
  constructor(message: string) {
    super(message, "BADGE_ASSIGNMENT_ERROR");
    this.name = "BadgeAssignmentError";
  }
}

export class BadgeTypeInactiveError extends BadgeError {
  constructor(badgeTypeId: string) {
    super(`Badge type ${badgeTypeId} is inactive and cannot be assigned`, "BADGE_TYPE_INACTIVE");
    this.name = "BadgeTypeInactiveError";
  }
}

export class BadgeNameConflictError extends BadgeError {
  constructor(badgeName: string) {
    super(`Badge name '${badgeName}' already exists`, "BADGE_NAME_CONFLICT");
    this.name = "BadgeNameConflictError";
  }
}
