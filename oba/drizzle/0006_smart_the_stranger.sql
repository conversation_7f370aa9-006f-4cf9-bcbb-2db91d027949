CREATE TYPE "public"."collection_type" AS ENUM('progressive', 'standalone');--> statement-breakpoint
CREATE TYPE "public"."unlock_type" AS ENUM('automatic', 'manual', 'peer_voted', 'manual_invitation');--> statement-breakpoint
CREATE TABLE "base_schema"."badge_collections" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"collection_id" text NOT NULL,
	"name" text NOT NULL,
	"description" text NOT NULL,
	"type" "collection_type" DEFAULT 'progressive',
	"total_badges" integer DEFAULT 0,
	"unlocked_by" text,
	"completion_reward" text,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "badge_collections_collection_id_unique" UNIQUE("collection_id")
);
--> statement-breakpoint
CREATE TABLE "base_schema"."badge_nominations" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"badge_type_id" uuid NOT NULL,
	"nominee_user_id" uuid NOT NULL,
	"nominator_user_id" uuid NOT NULL,
	"nomination_reason" text,
	"status" text DEFAULT 'pending',
	"created_at" timestamp DEFAULT now(),
	"processed_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "base_schema"."user_collection_progress" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"user_id" uuid NOT NULL,
	"collection_id" uuid NOT NULL,
	"badges_earned" integer DEFAULT 0,
	"total_badges" integer DEFAULT 0,
	"is_completed" boolean DEFAULT false,
	"completion_date" timestamp,
	"completion_reward_granted" boolean DEFAULT false,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" DROP CONSTRAINT "badge_types_name_unique";--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" ALTER COLUMN "criteria" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" ADD COLUMN "collection_id" uuid;--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" ADD COLUMN "badge_id" text NOT NULL;--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" ADD COLUMN "title" text;--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" ADD COLUMN "icon" text DEFAULT '🏆';--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" ADD COLUMN "tooltip" text;--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" ADD COLUMN "design" text NOT NULL;--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" ADD COLUMN "perks" text;--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" ADD COLUMN "unlock_type" "unlock_type" DEFAULT 'automatic';--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" ADD COLUMN "visual_description" text;--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" ADD COLUMN "animation" text;--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" ADD COLUMN "display_order" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "base_schema"."user_badges" ADD COLUMN "collection_id" uuid;--> statement-breakpoint
ALTER TABLE "base_schema"."user_badges" ADD COLUMN "progress_data" text;--> statement-breakpoint
ALTER TABLE "base_schema"."user_badges" ADD COLUMN "perks_granted" text;--> statement-breakpoint
ALTER TABLE "base_schema"."badge_nominations" ADD CONSTRAINT "badge_nominations_badge_type_id_badge_types_id_fk" FOREIGN KEY ("badge_type_id") REFERENCES "base_schema"."badge_types"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."badge_nominations" ADD CONSTRAINT "badge_nominations_nominee_user_id_users_id_fk" FOREIGN KEY ("nominee_user_id") REFERENCES "base_schema"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."badge_nominations" ADD CONSTRAINT "badge_nominations_nominator_user_id_users_id_fk" FOREIGN KEY ("nominator_user_id") REFERENCES "base_schema"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."user_collection_progress" ADD CONSTRAINT "user_collection_progress_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."user_collection_progress" ADD CONSTRAINT "user_collection_progress_collection_id_badge_collections_id_fk" FOREIGN KEY ("collection_id") REFERENCES "base_schema"."badge_collections"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE UNIQUE INDEX "nomination_idx" ON "base_schema"."badge_nominations" USING btree ("badge_type_id","nominee_user_id","nominator_user_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_collection_idx" ON "base_schema"."user_collection_progress" USING btree ("user_id","collection_id");--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" ADD CONSTRAINT "badge_types_collection_id_badge_collections_id_fk" FOREIGN KEY ("collection_id") REFERENCES "base_schema"."badge_collections"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."user_badges" ADD CONSTRAINT "user_badges_collection_id_badge_collections_id_fk" FOREIGN KEY ("collection_id") REFERENCES "base_schema"."badge_collections"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
CREATE UNIQUE INDEX "collection_badge_idx" ON "base_schema"."badge_types" USING btree ("collection_id","badge_id");--> statement-breakpoint
CREATE UNIQUE INDEX "collection_order_idx" ON "base_schema"."badge_types" USING btree ("collection_id","display_order");--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" DROP COLUMN "icon_url";--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" DROP COLUMN "color";--> statement-breakpoint
ALTER TABLE "base_schema"."badge_types" DROP COLUMN "assignment_type";