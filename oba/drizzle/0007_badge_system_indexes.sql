-- Additional indexes for optimal badge system query performance

-- Index for user badge queries by user_id (most common query)
CREATE INDEX IF NOT EXISTS "user_badges_user_id_idx" ON "base_schema"."user_badges" ("user_id");

-- Index for badge type queries by collection_id
CREATE INDEX IF NOT EXISTS "badge_types_collection_id_idx" ON "base_schema"."badge_types" ("collection_id");

-- Index for badge type queries by category and unlock_type
CREATE INDEX IF NOT EXISTS "badge_types_category_unlock_type_idx" ON "base_schema"."badge_types" ("category", "unlock_type");

-- Index for badge type queries by is_active status
CREATE INDEX IF NOT EXISTS "badge_types_is_active_idx" ON "base_schema"."badge_types" ("is_active");

-- Index for user collection progress queries by user_id
CREATE INDEX IF NOT EXISTS "user_collection_progress_user_id_idx" ON "base_schema"."user_collection_progress" ("user_id");

-- Index for user collection progress queries by collection_id
CREATE INDEX IF NOT EXISTS "user_collection_progress_collection_id_idx" ON "base_schema"."user_collection_progress" ("collection_id");

-- Index for badge nominations by nominee_user_id (for checking received nominations)
CREATE INDEX IF NOT EXISTS "badge_nominations_nominee_user_id_idx" ON "base_schema"."badge_nominations" ("nominee_user_id");

-- Index for badge nominations by nominator_user_id (for checking given nominations)
CREATE INDEX IF NOT EXISTS "badge_nominations_nominator_user_id_idx" ON "base_schema"."badge_nominations" ("nominator_user_id");

-- Index for badge nominations by status (for processing pending nominations)
CREATE INDEX IF NOT EXISTS "badge_nominations_status_idx" ON "base_schema"."badge_nominations" ("status");

-- Index for badge nominations by badge_type_id (for badge-specific nomination queries)
CREATE INDEX IF NOT EXISTS "badge_nominations_badge_type_id_idx" ON "base_schema"."badge_nominations" ("badge_type_id");

-- Index for badge collections by type and is_active
CREATE INDEX IF NOT EXISTS "badge_collections_type_active_idx" ON "base_schema"."badge_collections" ("type", "is_active");

-- Index for user badges by assigned_at for chronological queries
CREATE INDEX IF NOT EXISTS "user_badges_assigned_at_idx" ON "base_schema"."user_badges" ("assigned_at");

-- Index for user badges by is_visible for display queries
CREATE INDEX IF NOT EXISTS "user_badges_is_visible_idx" ON "base_schema"."user_badges" ("is_visible");

-- Composite index for user badges by user_id and is_visible (most common display query)
CREATE INDEX IF NOT EXISTS "user_badges_user_id_visible_idx" ON "base_schema"."user_badges" ("user_id", "is_visible");

-- Index for user collection progress by completion status
CREATE INDEX IF NOT EXISTS "user_collection_progress_completed_idx" ON "base_schema"."user_collection_progress" ("is_completed");

-- Index for badge types by display_order for ordering within collections
CREATE INDEX IF NOT EXISTS "badge_types_display_order_idx" ON "base_schema"."badge_types" ("display_order");