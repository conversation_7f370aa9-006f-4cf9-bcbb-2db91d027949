{"id": "d66b4ca9-97c3-4edb-b486-048e20f7ea2c", "prevId": "815546ce-a797-4164-990a-264cdf4eb4db", "version": "7", "dialect": "postgresql", "tables": {"base_schema.badge_collections": {"name": "badge_collections", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "collection_id": {"name": "collection_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "collection_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'progressive'"}, "total_badges": {"name": "total_badges", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "unlocked_by": {"name": "unlocked_by", "type": "text", "primaryKey": false, "notNull": false}, "completion_reward": {"name": "completion_reward", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"badge_collections_collection_id_unique": {"name": "badge_collections_collection_id_unique", "nullsNotDistinct": false, "columns": ["collection_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.badge_nominations": {"name": "badge_nominations", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "badge_type_id": {"name": "badge_type_id", "type": "uuid", "primaryKey": false, "notNull": true}, "nominee_user_id": {"name": "nominee_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "nominator_user_id": {"name": "nominator_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "nomination_reason": {"name": "nomination_reason", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"nomination_idx": {"name": "nomination_idx", "columns": [{"expression": "badge_type_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "nominee_user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "nominator_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"badge_nominations_badge_type_id_badge_types_id_fk": {"name": "badge_nominations_badge_type_id_badge_types_id_fk", "tableFrom": "badge_nominations", "tableTo": "badge_types", "schemaTo": "base_schema", "columnsFrom": ["badge_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "badge_nominations_nominee_user_id_users_id_fk": {"name": "badge_nominations_nominee_user_id_users_id_fk", "tableFrom": "badge_nominations", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["nominee_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "badge_nominations_nominator_user_id_users_id_fk": {"name": "badge_nominations_nominator_user_id_users_id_fk", "tableFrom": "badge_nominations", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["nominator_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.badge_types": {"name": "badge_types", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "collection_id": {"name": "collection_id", "type": "uuid", "primaryKey": false, "notNull": false}, "badge_id": {"name": "badge_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false, "default": "'🏆'"}, "tooltip": {"name": "tooltip", "type": "text", "primaryKey": false, "notNull": false}, "design": {"name": "design", "type": "text", "primaryKey": false, "notNull": true}, "criteria": {"name": "criteria", "type": "text", "primaryKey": false, "notNull": true}, "perks": {"name": "perks", "type": "text", "primaryKey": false, "notNull": false}, "unlock_type": {"name": "unlock_type", "type": "unlock_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'automatic'"}, "visual_description": {"name": "visual_description", "type": "text", "primaryKey": false, "notNull": false}, "animation": {"name": "animation", "type": "text", "primaryKey": false, "notNull": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "category": {"name": "category", "type": "badge_category", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'achievement'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"collection_badge_idx": {"name": "collection_badge_idx", "columns": [{"expression": "collection_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "badge_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "collection_order_idx": {"name": "collection_order_idx", "columns": [{"expression": "collection_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "display_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"badge_types_collection_id_badge_collections_id_fk": {"name": "badge_types_collection_id_badge_collections_id_fk", "tableFrom": "badge_types", "tableTo": "badge_collections", "schemaTo": "base_schema", "columnsFrom": ["collection_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.category_allowed_roles": {"name": "category_allowed_roles", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"category_allowed_roles_category_id_channel_categories_id_fk": {"name": "category_allowed_roles_category_id_channel_categories_id_fk", "tableFrom": "category_allowed_roles", "tableTo": "channel_categories", "schemaTo": "base_schema", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "category_allowed_roles_role_id_server_roles_id_fk": {"name": "category_allowed_roles_role_id_server_roles_id_fk", "tableFrom": "category_allowed_roles", "tableTo": "server_roles", "schemaTo": "base_schema", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.category_allowed_users": {"name": "category_allowed_users", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"category_allowed_users_category_id_channel_categories_id_fk": {"name": "category_allowed_users_category_id_channel_categories_id_fk", "tableFrom": "category_allowed_users", "tableTo": "channel_categories", "schemaTo": "base_schema", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "category_allowed_users_user_id_users_id_fk": {"name": "category_allowed_users_user_id_users_id_fk", "tableFrom": "category_allowed_users", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.category_permission_overrides": {"name": "category_permission_overrides", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "allowed_permissions": {"name": "allowed_permissions", "type": "bigint", "primaryKey": false, "notNull": false, "default": 0}, "denied_permissions": {"name": "denied_permissions", "type": "bigint", "primaryKey": false, "notNull": false, "default": 0}}, "indexes": {}, "foreignKeys": {"category_permission_overrides_category_id_channel_categories_id_fk": {"name": "category_permission_overrides_category_id_channel_categories_id_fk", "tableFrom": "category_permission_overrides", "tableTo": "channel_categories", "schemaTo": "base_schema", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "category_permission_overrides_role_id_server_roles_id_fk": {"name": "category_permission_overrides_role_id_server_roles_id_fk", "tableFrom": "category_permission_overrides", "tableTo": "server_roles", "schemaTo": "base_schema", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "category_permission_overrides_user_id_users_id_fk": {"name": "category_permission_overrides_user_id_users_id_fk", "tableFrom": "category_permission_overrides", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.category_permissions": {"name": "category_permissions", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_private": {"name": "is_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_visible": {"name": "is_visible", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {"category_permissions_category_id_channel_categories_id_fk": {"name": "category_permissions_category_id_channel_categories_id_fk", "tableFrom": "category_permissions", "tableTo": "channel_categories", "schemaTo": "base_schema", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.category_visible_roles": {"name": "category_visible_roles", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"category_visible_roles_category_id_channel_categories_id_fk": {"name": "category_visible_roles_category_id_channel_categories_id_fk", "tableFrom": "category_visible_roles", "tableTo": "channel_categories", "schemaTo": "base_schema", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "category_visible_roles_role_id_server_roles_id_fk": {"name": "category_visible_roles_role_id_server_roles_id_fk", "tableFrom": "category_visible_roles", "tableTo": "server_roles", "schemaTo": "base_schema", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.category_visible_users": {"name": "category_visible_users", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"category_visible_users_category_id_channel_categories_id_fk": {"name": "category_visible_users_category_id_channel_categories_id_fk", "tableFrom": "category_visible_users", "tableTo": "channel_categories", "schemaTo": "base_schema", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "category_visible_users_user_id_users_id_fk": {"name": "category_visible_users_user_id_users_id_fk", "tableFrom": "category_visible_users", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.channel_allowed_roles": {"name": "channel_allowed_roles", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "channel_id": {"name": "channel_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"channel_allowed_roles_channel_id_channels_id_fk": {"name": "channel_allowed_roles_channel_id_channels_id_fk", "tableFrom": "channel_allowed_roles", "tableTo": "channels", "schemaTo": "base_schema", "columnsFrom": ["channel_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "channel_allowed_roles_role_id_server_roles_id_fk": {"name": "channel_allowed_roles_role_id_server_roles_id_fk", "tableFrom": "channel_allowed_roles", "tableTo": "server_roles", "schemaTo": "base_schema", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.channel_allowed_users": {"name": "channel_allowed_users", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "channel_id": {"name": "channel_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"channel_allowed_users_channel_id_channels_id_fk": {"name": "channel_allowed_users_channel_id_channels_id_fk", "tableFrom": "channel_allowed_users", "tableTo": "channels", "schemaTo": "base_schema", "columnsFrom": ["channel_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "channel_allowed_users_user_id_users_id_fk": {"name": "channel_allowed_users_user_id_users_id_fk", "tableFrom": "channel_allowed_users", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.channel_categories": {"name": "channel_categories", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "server_id": {"name": "server_id", "type": "uuid", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"channel_categories_server_id_servers_id_fk": {"name": "channel_categories_server_id_servers_id_fk", "tableFrom": "channel_categories", "tableTo": "servers", "schemaTo": "base_schema", "columnsFrom": ["server_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.channel_permission_overrides": {"name": "channel_permission_overrides", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "channel_id": {"name": "channel_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "allowed_permissions": {"name": "allowed_permissions", "type": "bigint", "primaryKey": false, "notNull": false, "default": 0}, "denied_permissions": {"name": "denied_permissions", "type": "bigint", "primaryKey": false, "notNull": false, "default": 0}}, "indexes": {}, "foreignKeys": {"channel_permission_overrides_channel_id_channels_id_fk": {"name": "channel_permission_overrides_channel_id_channels_id_fk", "tableFrom": "channel_permission_overrides", "tableTo": "channels", "schemaTo": "base_schema", "columnsFrom": ["channel_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "channel_permission_overrides_role_id_server_roles_id_fk": {"name": "channel_permission_overrides_role_id_server_roles_id_fk", "tableFrom": "channel_permission_overrides", "tableTo": "server_roles", "schemaTo": "base_schema", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "channel_permission_overrides_user_id_users_id_fk": {"name": "channel_permission_overrides_user_id_users_id_fk", "tableFrom": "channel_permission_overrides", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.channel_privacy": {"name": "channel_privacy", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "channel_id": {"name": "channel_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_visible": {"name": "is_visible", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {"channel_privacy_channel_id_channels_id_fk": {"name": "channel_privacy_channel_id_channels_id_fk", "tableFrom": "channel_privacy", "tableTo": "channels", "schemaTo": "base_schema", "columnsFrom": ["channel_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.channels": {"name": "channels", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "server_id": {"name": "server_id", "type": "uuid", "primaryKey": false, "notNull": false}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "channel_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'TEXT'"}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"channels_server_id_servers_id_fk": {"name": "channels_server_id_servers_id_fk", "tableFrom": "channels", "tableTo": "servers", "schemaTo": "base_schema", "columnsFrom": ["server_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "channels_category_id_channel_categories_id_fk": {"name": "channels_category_id_channel_categories_id_fk", "tableFrom": "channels", "tableTo": "channel_categories", "schemaTo": "base_schema", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.channel_visible_roles": {"name": "channel_visible_roles", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "channel_id": {"name": "channel_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"channel_visible_roles_channel_id_channels_id_fk": {"name": "channel_visible_roles_channel_id_channels_id_fk", "tableFrom": "channel_visible_roles", "tableTo": "channels", "schemaTo": "base_schema", "columnsFrom": ["channel_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "channel_visible_roles_role_id_server_roles_id_fk": {"name": "channel_visible_roles_role_id_server_roles_id_fk", "tableFrom": "channel_visible_roles", "tableTo": "server_roles", "schemaTo": "base_schema", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.channel_visible_users": {"name": "channel_visible_users", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "channel_id": {"name": "channel_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"channel_visible_users_channel_id_channels_id_fk": {"name": "channel_visible_users_channel_id_channels_id_fk", "tableFrom": "channel_visible_users", "tableTo": "channels", "schemaTo": "base_schema", "columnsFrom": ["channel_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "channel_visible_users_user_id_users_id_fk": {"name": "channel_visible_users_user_id_users_id_fk", "tableFrom": "channel_visible_users", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.direct_messages": {"name": "direct_messages", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "sender_id": {"name": "sender_id", "type": "uuid", "primaryKey": false, "notNull": false}, "receiver_id": {"name": "receiver_id", "type": "uuid", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'sent'"}, "read_at": {"name": "read_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "text", "primaryKey": false, "notNull": false}, "edited_at": {"name": "edited_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"direct_messages_sender_id_users_id_fk": {"name": "direct_messages_sender_id_users_id_fk", "tableFrom": "direct_messages", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["sender_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "direct_messages_receiver_id_users_id_fk": {"name": "direct_messages_receiver_id_users_id_fk", "tableFrom": "direct_messages", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["receiver_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.friendships": {"name": "friendships", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "friend_id": {"name": "friend_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "friend_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "initiated_by": {"name": "initiated_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"user_friend_idx": {"name": "user_friend_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "friend_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"friendships_user_id_users_id_fk": {"name": "friendships_user_id_users_id_fk", "tableFrom": "friendships", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "friendships_friend_id_users_id_fk": {"name": "friendships_friend_id_users_id_fk", "tableFrom": "friendships", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["friend_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "friendships_initiated_by_users_id_fk": {"name": "friendships_initiated_by_users_id_fk", "tableFrom": "friendships", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["initiated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.message_reactions": {"name": "message_reactions", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "message_id": {"name": "message_id", "type": "uuid", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "emoji": {"name": "emoji", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"message_reactions_message_id_messages_id_fk": {"name": "message_reactions_message_id_messages_id_fk", "tableFrom": "message_reactions", "tableTo": "messages", "schemaTo": "base_schema", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "message_reactions_user_id_users_id_fk": {"name": "message_reactions_user_id_users_id_fk", "tableFrom": "message_reactions", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.message_reads": {"name": "message_reads", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "message_id": {"name": "message_id", "type": "uuid", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "read_at": {"name": "read_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"message_reads_message_id_messages_id_fk": {"name": "message_reads_message_id_messages_id_fk", "tableFrom": "message_reads", "tableTo": "messages", "schemaTo": "base_schema", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "message_reads_user_id_users_id_fk": {"name": "message_reads_user_id_users_id_fk", "tableFrom": "message_reads", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.messages": {"name": "messages", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "channel_id": {"name": "channel_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'sent'"}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": false, "default": "'text'"}, "attachments": {"name": "attachments", "type": "text", "primaryKey": false, "notNull": false}, "edited_at": {"name": "edited_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"messages_user_id_users_id_fk": {"name": "messages_user_id_users_id_fk", "tableFrom": "messages", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "messages_channel_id_channels_id_fk": {"name": "messages_channel_id_channels_id_fk", "tableFrom": "messages", "tableTo": "channels", "schemaTo": "base_schema", "columnsFrom": ["channel_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.server_bans": {"name": "server_bans", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "server_id": {"name": "server_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "banned_by_id": {"name": "banned_by_id", "type": "uuid", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "banned_at": {"name": "banned_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"server_user_ban_idx": {"name": "server_user_ban_idx", "columns": [{"expression": "server_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"server_bans_server_id_servers_id_fk": {"name": "server_bans_server_id_servers_id_fk", "tableFrom": "server_bans", "tableTo": "servers", "schemaTo": "base_schema", "columnsFrom": ["server_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "server_bans_user_id_users_id_fk": {"name": "server_bans_user_id_users_id_fk", "tableFrom": "server_bans", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "server_bans_banned_by_id_users_id_fk": {"name": "server_bans_banned_by_id_users_id_fk", "tableFrom": "server_bans", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["banned_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.server_invites": {"name": "server_invites", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "server_id": {"name": "server_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_by_id": {"name": "created_by_id", "type": "uuid", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "max_uses": {"name": "max_uses", "type": "integer", "primaryKey": false, "notNull": true}, "uses": {"name": "uses", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"server_invites_server_id_servers_id_fk": {"name": "server_invites_server_id_servers_id_fk", "tableFrom": "server_invites", "tableTo": "servers", "schemaTo": "base_schema", "columnsFrom": ["server_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "server_invites_created_by_id_users_id_fk": {"name": "server_invites_created_by_id_users_id_fk", "tableFrom": "server_invites", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["created_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"server_invites_code_unique": {"name": "server_invites_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.server_memberships": {"name": "server_memberships", "schema": "base_schema", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "server_id": {"name": "server_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"server_memberships_user_id_users_id_fk": {"name": "server_memberships_user_id_users_id_fk", "tableFrom": "server_memberships", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "server_memberships_server_id_servers_id_fk": {"name": "server_memberships_server_id_servers_id_fk", "tableFrom": "server_memberships", "tableTo": "servers", "schemaTo": "base_schema", "columnsFrom": ["server_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"server_memberships_user_id_server_id_pk": {"name": "server_memberships_user_id_server_id_pk", "columns": ["user_id", "server_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.server_roles": {"name": "server_roles", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "server_id": {"name": "server_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "permissions": {"name": "permissions", "type": "bigint", "primaryKey": false, "notNull": true, "default": 0}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false, "default": "'#000000'"}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"server_roles_server_id_servers_id_fk": {"name": "server_roles_server_id_servers_id_fk", "tableFrom": "server_roles", "tableTo": "servers", "schemaTo": "base_schema", "columnsFrom": ["server_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.servers": {"name": "servers", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "icon_url": {"name": "icon_url", "type": "text", "primaryKey": false, "notNull": false}, "owner_id": {"name": "owner_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"servers_owner_id_users_id_fk": {"name": "servers_owner_id_users_id_fk", "tableFrom": "servers", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.tokens": {"name": "tokens", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_in": {"name": "expires_in", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.user_badges": {"name": "user_badges", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "badge_type_id": {"name": "badge_type_id", "type": "uuid", "primaryKey": false, "notNull": true}, "collection_id": {"name": "collection_id", "type": "uuid", "primaryKey": false, "notNull": false}, "assigned_by": {"name": "assigned_by", "type": "uuid", "primaryKey": false, "notNull": false}, "assigned_at": {"name": "assigned_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "is_visible": {"name": "is_visible", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "progress_data": {"name": "progress_data", "type": "text", "primaryKey": false, "notNull": false}, "perks_granted": {"name": "perks_granted", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"user_badge_idx": {"name": "user_badge_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "badge_type_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_badges_user_id_users_id_fk": {"name": "user_badges_user_id_users_id_fk", "tableFrom": "user_badges", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_badges_badge_type_id_badge_types_id_fk": {"name": "user_badges_badge_type_id_badge_types_id_fk", "tableFrom": "user_badges", "tableTo": "badge_types", "schemaTo": "base_schema", "columnsFrom": ["badge_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_badges_collection_id_badge_collections_id_fk": {"name": "user_badges_collection_id_badge_collections_id_fk", "tableFrom": "user_badges", "tableTo": "badge_collections", "schemaTo": "base_schema", "columnsFrom": ["collection_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "user_badges_assigned_by_users_id_fk": {"name": "user_badges_assigned_by_users_id_fk", "tableFrom": "user_badges", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["assigned_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.user_collection_progress": {"name": "user_collection_progress", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "collection_id": {"name": "collection_id", "type": "uuid", "primaryKey": false, "notNull": true}, "badges_earned": {"name": "badges_earned", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "total_badges": {"name": "total_badges", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_completed": {"name": "is_completed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "completion_date": {"name": "completion_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "completion_reward_granted": {"name": "completion_reward_granted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"user_collection_idx": {"name": "user_collection_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "collection_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_collection_progress_user_id_users_id_fk": {"name": "user_collection_progress_user_id_users_id_fk", "tableFrom": "user_collection_progress", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_collection_progress_collection_id_badge_collections_id_fk": {"name": "user_collection_progress_collection_id_badge_collections_id_fk", "tableFrom": "user_collection_progress", "tableTo": "badge_collections", "schemaTo": "base_schema", "columnsFrom": ["collection_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.user_roles": {"name": "user_roles", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": false}, "server_id": {"name": "server_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_roles_user_id_users_id_fk": {"name": "user_roles_user_id_users_id_fk", "tableFrom": "user_roles", "tableTo": "users", "schemaTo": "base_schema", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_roles_role_id_server_roles_id_fk": {"name": "user_roles_role_id_server_roles_id_fk", "tableFrom": "user_roles", "tableTo": "server_roles", "schemaTo": "base_schema", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_roles_server_id_servers_id_fk": {"name": "user_roles_server_id_servers_id_fk", "tableFrom": "user_roles", "tableTo": "servers", "schemaTo": "base_schema", "columnsFrom": ["server_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "base_schema.users": {"name": "users", "schema": "base_schema", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuidv7()"}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false, "default": "'placeholder'"}, "status": {"name": "status", "type": "user_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'OFFLINE'"}, "preferred_status": {"name": "preferred_status", "type": "user_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'ONLINE'"}, "status_message": {"name": "status_message", "type": "text", "primaryKey": false, "notNull": false}, "last_active": {"name": "last_active", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_email_verified": {"name": "is_email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "email_verification_token": {"name": "email_verification_token", "type": "text", "primaryKey": false, "notNull": false}, "email_verification_expiry": {"name": "email_verification_expiry", "type": "timestamp", "primaryKey": false, "notNull": false}, "reset_token": {"name": "reset_token", "type": "text", "primaryKey": false, "notNull": false}, "reset_token_expiry": {"name": "reset_token_expiry", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token_expiry": {"name": "refresh_token_expiry", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.badge_category": {"name": "badge_category", "schema": "public", "values": ["achievement", "role", "special", "community", "milestone"]}, "public.channel_type": {"name": "channel_type", "schema": "public", "values": ["TEXT", "VOICE", "ANNOUNCEMENT"]}, "public.collection_type": {"name": "collection_type", "schema": "public", "values": ["progressive", "standalone"]}, "public.friend_status": {"name": "friend_status", "schema": "public", "values": ["PENDING", "ACCEPTED", "BLOCKED"]}, "public.unlock_type": {"name": "unlock_type", "schema": "public", "values": ["automatic", "manual", "peer_voted", "manual_invitation"]}, "public.user_status": {"name": "user_status", "schema": "public", "values": ["ONLINE", "AWAY", "BUSY", "INVISIBLE", "OFFLINE"]}}, "schemas": {"base_schema": "base_schema"}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}