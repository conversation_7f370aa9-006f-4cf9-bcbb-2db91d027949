-- Create sequences for auto-incrementing positions
CREATE SEQUENCE IF NOT EXISTS "base_schema"."channel_categories_position_seq";--> statement-breakpoint
CREATE SEQUENCE IF NOT EXISTS "base_schema"."channels_position_seq";--> statement-breakpoint

-- Update channel_categories table
ALTER TABLE "base_schema"."channel_categories" ALTER COLUMN "position" SET DATA TYPE integer;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_categories" ALTER COLUMN "position" DROP DEFAULT;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_categories" ALTER COLUMN "position" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_categories" ALTER COLUMN "position" SET DEFAULT nextval('"base_schema"."channel_categories_position_seq"');--> statement-breakpoint

-- Update channels table
ALTER TABLE "base_schema"."channels" ALTER COLUMN "position" SET DATA TYPE integer;--> statement-breakpoint
ALTER TABLE "base_schema"."channels" ALTER COLUMN "position" DROP DEFAULT;--> statement-breakpoint
ALTER TABLE "base_schema"."channels" ALTER COLUMN "position" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "base_schema"."channels" ALTER COLUMN "position" SET DEFAULT nextval('"base_schema"."channels_position_seq"');--> statement-breakpoint

-- Set the sequences to start from the current maximum position value + 1
SELECT setval('"base_schema"."channel_categories_position_seq"', COALESCE((SELECT MAX("position") FROM "base_schema"."channel_categories"), 0) + 1, false);--> statement-breakpoint
SELECT setval('"base_schema"."channels_position_seq"', COALESCE((SELECT MAX("position") FROM "base_schema"."channels"), 0) + 1, false);