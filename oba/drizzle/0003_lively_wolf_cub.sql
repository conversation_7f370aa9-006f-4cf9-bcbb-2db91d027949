ALTER TABLE "base_schema"."channel_categories" ALTER COLUMN "position" SET DATA TYPE integer;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_categories" ALTER COLUMN "position" SET DEFAULT 0;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_categories" ALTER COLUMN "position" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "base_schema"."channels" ALTER COLUMN "position" SET DATA TYPE integer;--> statement-breakpoint
ALTER TABLE "base_schema"."channels" ALTER COLUMN "position" SET DEFAULT 0;--> statement-breakpoint
ALTER TABLE "base_schema"."channels" ALTER COLUMN "position" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "base_schema"."server_roles" ADD COLUMN "color" text DEFAULT '#000000';--> statement-breakpoint
ALTER TABLE "base_schema"."server_roles" ADD COLUMN "is_default" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "base_schema"."server_roles" ADD COLUMN "created_at" timestamp DEFAULT now();--> statement-breakpoint
ALTER TABLE "base_schema"."server_roles" ADD COLUMN "updated_at" timestamp DEFAULT now();