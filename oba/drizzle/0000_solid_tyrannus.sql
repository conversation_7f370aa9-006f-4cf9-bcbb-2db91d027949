CREATE SCHEMA "base_schema";
--> statement-breakpoint
CREATE TYPE "public"."channel_type" AS ENUM('TEXT', 'VOICE', 'ANNOUNCEMENT');--> statement-breakpoint
CREATE TYPE "public"."friend_status" AS ENUM('PENDING', 'ACCEPTED', 'BLOCKED');--> statement-breakpoint
CREATE TYPE "public"."user_status" AS ENUM('ONLINE', 'AWAY', 'BUSY', 'INVISIBLE', 'OFFLINE');--> statement-breakpoint
CREATE TABLE "base_schema"."channel_allowed_roles" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"channel_id" uuid NOT NULL,
	"role_id" uuid NOT NULL
);
--> statement-breakpoint
CREATE TABLE "base_schema"."channel_categories" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"server_id" uuid NOT NULL,
	"position" integer DEFAULT 0,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "base_schema"."channel_privacy" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"channel_id" uuid NOT NULL,
	"is_public" boolean DEFAULT true NOT NULL
);
--> statement-breakpoint
CREATE TABLE "base_schema"."channels" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"server_id" uuid,
	"category_id" uuid,
	"type" "channel_type" DEFAULT 'TEXT' NOT NULL,
	"position" integer DEFAULT 0,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "base_schema"."direct_messages" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"sender_id" uuid,
	"receiver_id" uuid,
	"content" text NOT NULL,
	"status" text DEFAULT 'sent',
	"read_at" timestamp,
	"attachments" text,
	"edited_at" timestamp,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "base_schema"."friendships" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"user_id" uuid NOT NULL,
	"friend_id" uuid NOT NULL,
	"status" "friend_status" DEFAULT 'PENDING' NOT NULL,
	"initiated_by" uuid NOT NULL,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "base_schema"."message_reactions" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"message_id" uuid,
	"user_id" uuid,
	"emoji" text NOT NULL,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "base_schema"."message_reads" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"message_id" uuid,
	"user_id" uuid,
	"read_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "base_schema"."messages" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"content" text NOT NULL,
	"user_id" uuid,
	"channel_id" uuid,
	"status" text DEFAULT 'sent',
	"type" text DEFAULT 'text',
	"attachments" text,
	"edited_at" timestamp,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "base_schema"."server_bans" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"server_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"banned_by_id" uuid NOT NULL,
	"reason" text,
	"banned_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "base_schema"."server_invites" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"code" text NOT NULL,
	"server_id" uuid,
	"created_by_id" uuid,
	"expires_at" timestamp,
	"max_uses" integer NOT NULL,
	"uses" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp DEFAULT now(),
	CONSTRAINT "server_invites_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "base_schema"."server_memberships" (
	"user_id" uuid NOT NULL,
	"server_id" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "server_memberships_user_id_server_id_pk" PRIMARY KEY("user_id","server_id")
);
--> statement-breakpoint
CREATE TABLE "base_schema"."server_roles" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"server_id" uuid NOT NULL,
	"name" text NOT NULL,
	"permissions" bigint DEFAULT 0 NOT NULL
);
--> statement-breakpoint
CREATE TABLE "base_schema"."servers" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"name" text,
	"description" text,
	"icon_url" text,
	"owner_id" uuid,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "base_schema"."tokens" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"access_token" text,
	"refresh_token" text,
	"expires_in" timestamp
);
--> statement-breakpoint
CREATE TABLE "base_schema"."user_roles" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"user_id" uuid,
	"role_id" uuid,
	"server_id" uuid
);
--> statement-breakpoint
CREATE TABLE "base_schema"."users" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"username" text NOT NULL,
	"email" text NOT NULL,
	"password" text NOT NULL,
	"avatar_url" text DEFAULT 'placeholder',
	"status" "user_status" DEFAULT 'OFFLINE',
	"preferred_status" "user_status" DEFAULT 'ONLINE',
	"status_message" text,
	"last_active" timestamp,
	"is_email_verified" boolean DEFAULT false,
	"email_verification_token" text,
	"email_verification_expiry" timestamp,
	"reset_token" text,
	"reset_token_expiry" timestamp,
	"refresh_token" text,
	"refresh_token_expiry" timestamp,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "users_username_unique" UNIQUE("username"),
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
ALTER TABLE "base_schema"."channel_allowed_roles" ADD CONSTRAINT "channel_allowed_roles_channel_id_channels_id_fk" FOREIGN KEY ("channel_id") REFERENCES "base_schema"."channels"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_allowed_roles" ADD CONSTRAINT "channel_allowed_roles_role_id_server_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "base_schema"."server_roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_categories" ADD CONSTRAINT "channel_categories_server_id_servers_id_fk" FOREIGN KEY ("server_id") REFERENCES "base_schema"."servers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_privacy" ADD CONSTRAINT "channel_privacy_channel_id_channels_id_fk" FOREIGN KEY ("channel_id") REFERENCES "base_schema"."channels"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."channels" ADD CONSTRAINT "channels_server_id_servers_id_fk" FOREIGN KEY ("server_id") REFERENCES "base_schema"."servers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."channels" ADD CONSTRAINT "channels_category_id_channel_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "base_schema"."channel_categories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."direct_messages" ADD CONSTRAINT "direct_messages_sender_id_users_id_fk" FOREIGN KEY ("sender_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."direct_messages" ADD CONSTRAINT "direct_messages_receiver_id_users_id_fk" FOREIGN KEY ("receiver_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."friendships" ADD CONSTRAINT "friendships_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."friendships" ADD CONSTRAINT "friendships_friend_id_users_id_fk" FOREIGN KEY ("friend_id") REFERENCES "base_schema"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."friendships" ADD CONSTRAINT "friendships_initiated_by_users_id_fk" FOREIGN KEY ("initiated_by") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."message_reactions" ADD CONSTRAINT "message_reactions_message_id_messages_id_fk" FOREIGN KEY ("message_id") REFERENCES "base_schema"."messages"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."message_reactions" ADD CONSTRAINT "message_reactions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."message_reads" ADD CONSTRAINT "message_reads_message_id_messages_id_fk" FOREIGN KEY ("message_id") REFERENCES "base_schema"."messages"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."message_reads" ADD CONSTRAINT "message_reads_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."messages" ADD CONSTRAINT "messages_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."messages" ADD CONSTRAINT "messages_channel_id_channels_id_fk" FOREIGN KEY ("channel_id") REFERENCES "base_schema"."channels"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."server_bans" ADD CONSTRAINT "server_bans_server_id_servers_id_fk" FOREIGN KEY ("server_id") REFERENCES "base_schema"."servers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."server_bans" ADD CONSTRAINT "server_bans_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."server_bans" ADD CONSTRAINT "server_bans_banned_by_id_users_id_fk" FOREIGN KEY ("banned_by_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."server_invites" ADD CONSTRAINT "server_invites_server_id_servers_id_fk" FOREIGN KEY ("server_id") REFERENCES "base_schema"."servers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."server_invites" ADD CONSTRAINT "server_invites_created_by_id_users_id_fk" FOREIGN KEY ("created_by_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."server_memberships" ADD CONSTRAINT "server_memberships_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."server_memberships" ADD CONSTRAINT "server_memberships_server_id_servers_id_fk" FOREIGN KEY ("server_id") REFERENCES "base_schema"."servers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."server_roles" ADD CONSTRAINT "server_roles_server_id_servers_id_fk" FOREIGN KEY ("server_id") REFERENCES "base_schema"."servers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."servers" ADD CONSTRAINT "servers_owner_id_users_id_fk" FOREIGN KEY ("owner_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."user_roles" ADD CONSTRAINT "user_roles_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."user_roles" ADD CONSTRAINT "user_roles_role_id_server_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "base_schema"."server_roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."user_roles" ADD CONSTRAINT "user_roles_server_id_servers_id_fk" FOREIGN KEY ("server_id") REFERENCES "base_schema"."servers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE UNIQUE INDEX "user_friend_idx" ON "base_schema"."friendships" USING btree ("user_id","friend_id");--> statement-breakpoint
CREATE UNIQUE INDEX "server_user_ban_idx" ON "base_schema"."server_bans" USING btree ("server_id","user_id");