CREATE TABLE "base_schema"."category_allowed_roles" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"category_id" uuid NOT NULL,
	"role_id" uuid NOT NULL
);
--> statement-breakpoint
CREATE TABLE "base_schema"."category_allowed_users" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"category_id" uuid NOT NULL,
	"user_id" uuid NOT NULL
);
--> statement-breakpoint
CREATE TABLE "base_schema"."category_permission_overrides" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"category_id" uuid NOT NULL,
	"role_id" uuid,
	"user_id" uuid,
	"allowed_permissions" bigint DEFAULT 0,
	"denied_permissions" bigint DEFAULT 0
);
--> statement-breakpoint
CREATE TABLE "base_schema"."category_permissions" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"category_id" uuid NOT NULL,
	"is_private" boolean DEFAULT false NOT NULL,
	"is_visible" boolean DEFAULT true NOT NULL
);
--> statement-breakpoint
CREATE TABLE "base_schema"."category_visible_roles" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"category_id" uuid NOT NULL,
	"role_id" uuid NOT NULL
);
--> statement-breakpoint
CREATE TABLE "base_schema"."category_visible_users" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"category_id" uuid NOT NULL,
	"user_id" uuid NOT NULL
);
--> statement-breakpoint
CREATE TABLE "base_schema"."channel_allowed_users" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"channel_id" uuid NOT NULL,
	"user_id" uuid NOT NULL
);
--> statement-breakpoint
CREATE TABLE "base_schema"."channel_permission_overrides" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"channel_id" uuid NOT NULL,
	"role_id" uuid,
	"user_id" uuid,
	"allowed_permissions" bigint DEFAULT 0,
	"denied_permissions" bigint DEFAULT 0
);
--> statement-breakpoint
CREATE TABLE "base_schema"."channel_visible_roles" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"channel_id" uuid NOT NULL,
	"role_id" uuid NOT NULL
);
--> statement-breakpoint
CREATE TABLE "base_schema"."channel_visible_users" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"channel_id" uuid NOT NULL,
	"user_id" uuid NOT NULL
);
--> statement-breakpoint
ALTER TABLE "base_schema"."channel_privacy" ADD COLUMN "is_visible" boolean DEFAULT true NOT NULL;--> statement-breakpoint
ALTER TABLE "base_schema"."category_allowed_roles" ADD CONSTRAINT "category_allowed_roles_category_id_channel_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "base_schema"."channel_categories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."category_allowed_roles" ADD CONSTRAINT "category_allowed_roles_role_id_server_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "base_schema"."server_roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."category_allowed_users" ADD CONSTRAINT "category_allowed_users_category_id_channel_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "base_schema"."channel_categories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."category_allowed_users" ADD CONSTRAINT "category_allowed_users_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."category_permission_overrides" ADD CONSTRAINT "category_permission_overrides_category_id_channel_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "base_schema"."channel_categories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."category_permission_overrides" ADD CONSTRAINT "category_permission_overrides_role_id_server_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "base_schema"."server_roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."category_permission_overrides" ADD CONSTRAINT "category_permission_overrides_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."category_permissions" ADD CONSTRAINT "category_permissions_category_id_channel_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "base_schema"."channel_categories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."category_visible_roles" ADD CONSTRAINT "category_visible_roles_category_id_channel_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "base_schema"."channel_categories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."category_visible_roles" ADD CONSTRAINT "category_visible_roles_role_id_server_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "base_schema"."server_roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."category_visible_users" ADD CONSTRAINT "category_visible_users_category_id_channel_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "base_schema"."channel_categories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."category_visible_users" ADD CONSTRAINT "category_visible_users_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_allowed_users" ADD CONSTRAINT "channel_allowed_users_channel_id_channels_id_fk" FOREIGN KEY ("channel_id") REFERENCES "base_schema"."channels"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_allowed_users" ADD CONSTRAINT "channel_allowed_users_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_permission_overrides" ADD CONSTRAINT "channel_permission_overrides_channel_id_channels_id_fk" FOREIGN KEY ("channel_id") REFERENCES "base_schema"."channels"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_permission_overrides" ADD CONSTRAINT "channel_permission_overrides_role_id_server_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "base_schema"."server_roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_permission_overrides" ADD CONSTRAINT "channel_permission_overrides_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_visible_roles" ADD CONSTRAINT "channel_visible_roles_channel_id_channels_id_fk" FOREIGN KEY ("channel_id") REFERENCES "base_schema"."channels"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_visible_roles" ADD CONSTRAINT "channel_visible_roles_role_id_server_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "base_schema"."server_roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_visible_users" ADD CONSTRAINT "channel_visible_users_channel_id_channels_id_fk" FOREIGN KEY ("channel_id") REFERENCES "base_schema"."channels"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_visible_users" ADD CONSTRAINT "channel_visible_users_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;