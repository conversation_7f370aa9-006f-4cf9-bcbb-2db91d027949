CREATE TYPE "public"."badge_category" AS ENUM('achievement', 'role', 'special', 'community', 'milestone');--> statement-breakpoint
CREATE TABLE "base_schema"."badge_types" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"name" text NOT NULL,
	"description" text NOT NULL,
	"icon_url" text,
	"color" text DEFAULT '#000000',
	"category" "badge_category" DEFAULT 'achievement',
	"is_active" boolean DEFAULT true,
	"assignment_type" text DEFAULT 'manual',
	"criteria" text,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "badge_types_name_unique" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "base_schema"."user_badges" (
	"id" uuid PRIMARY KEY DEFAULT uuidv7() NOT NULL,
	"user_id" uuid NOT NULL,
	"badge_type_id" uuid NOT NULL,
	"assigned_by" uuid,
	"assigned_at" timestamp DEFAULT now(),
	"is_visible" boolean DEFAULT true
);
--> statement-breakpoint
ALTER TABLE "base_schema"."user_badges" ADD CONSTRAINT "user_badges_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "base_schema"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."user_badges" ADD CONSTRAINT "user_badges_badge_type_id_badge_types_id_fk" FOREIGN KEY ("badge_type_id") REFERENCES "base_schema"."badge_types"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "base_schema"."user_badges" ADD CONSTRAINT "user_badges_assigned_by_users_id_fk" FOREIGN KEY ("assigned_by") REFERENCES "base_schema"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE UNIQUE INDEX "user_badge_idx" ON "base_schema"."user_badges" USING btree ("user_id","badge_type_id");