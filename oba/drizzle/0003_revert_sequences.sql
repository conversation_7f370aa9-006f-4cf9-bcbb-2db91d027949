-- Revert the sequence-based approach and go back to regular integer fields with default values

-- Update channel_categories table
ALTER TABLE "base_schema"."channel_categories" ALTER COLUMN "position" DROP DEFAULT;--> statement-breakpoint
ALTER TABLE "base_schema"."channel_categories" ALTER COLUMN "position" SET DEFAULT 0;--> statement-breakpoint

-- Update channels table
ALTER TABLE "base_schema"."channels" ALTER COLUMN "position" DROP DEFAULT;--> statement-breakpoint
ALTER TABLE "base_schema"."channels" ALTER COLUMN "position" SET DEFAULT 0;--> statement-breakpoint

-- Drop the sequences if they exist
DROP SEQUENCE IF EXISTS "base_schema"."channel_categories_position_seq";--> statement-breakpoint
DROP SEQUENCE IF EXISTS "base_schema"."channels_position_seq";
