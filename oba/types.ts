import type { ServerWebSocket } from "bun";

type Middleware = (
  req: Request,
  next: (req: Request) => Response | Promise<Response>,
) => Response | Promise<Response>;

type RouteHandlerFunction = (
  req: Request,
  params?: Record<string, string>,
) => Response | Promise<Response>;

type WebSocketHandler = {
  open?: (ws: ServerWebSocket<unknown>) => void | Promise<void>;
  message?: (
    ws: ServerWebSocket<unknown>,
    message: string | Uint8Array,
  ) => void | Promise<void>;
  close?: (
    ws: ServerWebSocket<unknown>,
    code: number,
    message: string,
  ) => void | Promise<void>;
};

export const CHANNEL_TYPES = [
  "public",
  "private",
  "news",
  "announcement",
] as const;
export type ChannelType = (typeof CHANNEL_TYPES)[number];

//type WebSocketMessageType = "MESSAGE_TYPING" | "MESSAGE_SEND";

import { EventTypes as WebSocketMessageType } from "@kurultai/oba-types";

interface WebSocketMessage {
  type: WebSocketMessageType;
  sender: string;
  data: any; // Could be a string (for MESSAGE_SEND) or { isTyping: boolean } (for MESSAGE_TYPING)
}

interface MessageSendEvent extends WebSocketMessage {
  type: WebSocketMessageType.MESSAGE_SEND;
  sender: string;
  data: {
    message_content: string;
    sent_at: string; //as datetime
  };
}

interface MessageTypingStartEvent extends WebSocketMessage {
  type: WebSocketMessageType.MESSAGE_TYPING_START;
  sender: string;
  data: {
    isTyping: boolean;
  };
}

interface VoiceChatSendEvent extends WebSocketMessage {
  type: WebSocketMessageType.VOICECHAT_SEND;
  sender: string;
  data: string | ArrayBuffer | null;
}

interface ServerCreateEvent extends WebSocketMessage {
  type: WebSocketMessageType.SERVER_CREATE;
  sender: string;
  data: {
    server_name: string;
    server_avatar: string;
    server_description: string;
  };
}

interface ChannelCreateEvent extends WebSocketMessage {
  type: WebSocketMessageType.CHANNEL_CREATE;
  sender: string;
  data: {
    channel_name: string;
    channel_type: ChannelType;
  };
}

// Badge system types
export const BADGE_CATEGORIES = [
  "achievement",
  "role", 
  "special",
  "community",
  "milestone",
] as const;
export type BadgeCategory = (typeof BADGE_CATEGORIES)[number];

export const ASSIGNMENT_TYPES = ["automatic", "manual"] as const;
export type AssignmentType = (typeof ASSIGNMENT_TYPES)[number];

interface BadgeType {
  id: string;
  name: string;
  description: string;
  iconUrl?: string;
  color: string;
  category: BadgeCategory;
  isActive: boolean;
  assignmentType: AssignmentType;
  criteria?: BadgeCriteria;
  createdAt: Date;
  updatedAt: Date;
}

interface UserBadge {
  id: string;
  userId: string;
  badgeTypeId: string;
  assignedBy?: string;
  assignedAt: Date;
  isVisible: boolean;
  badgeType?: BadgeType;
}

interface BadgeCriteria {
  type: 'message_count' | 'server_count' | 'friend_count' | 'days_active' | 'custom';
  threshold?: number;
  conditions?: Record<string, any>;
}

interface UserStats {
  messageCount: number;
  serverCount: number;
  friendCount: number;
  daysActive: number;
  accountAge: number;
  lastActive: Date;
}

interface BadgeStats {
  totalBadges: number;
  totalAssignments: number;
  categoryBreakdown: Record<BadgeCategory, number>;
  mostPopularBadges: Array<{
    badgeType: BadgeType;
    assignmentCount: number;
  }>;
}

interface BadgeLeaderboard {
  userId: string;
  username: string;
  badgeCount: number;
  badges: UserBadge[];
}

export type {
  Middleware,
  RouteHandlerFunction,
  WebSocketHandler,
  //WebSocketMessageType,

  // WebSocket Events
  WebSocketMessage,
  MessageSendEvent,
  MessageTypingEvent,
  VoiceChatSendEvent,
  ServerCreateEvent,
  ChannelCreateEvent,

  // Badge system types
  BadgeType,
  UserBadge,
  BadgeCriteria,
  UserStats,
  BadgeStats,
  BadgeLeaderboard,
};
